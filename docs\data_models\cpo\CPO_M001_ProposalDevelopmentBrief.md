# CPO_M001: ProposalDevelopmentBrief

## 1. Metadata

*   **Data Model ID:** CPO_M001
*   **Data Model Name:** ProposalDevelopmentBrief
*   **Version:** 1.0
*   **Status:** Definition
*   **Last Updated:** YYYY-MM-DD
*   **Owner Command Office:** CPO
*   **Primary Contact/SME:** CPO_A001_PortfolioAnalystAgent

## 2. Purpose

*   This data model provides a structured format for capturing initial ideas, directives, or preliminary information for a potential ESTRATIX proposal. It serves as an optional precursor or lightweight input to the `CPO_F001_StrategicOpportunityToProposalDevelopment` flow, especially for internally generated ideas or high-level strategic mandates that may not yet have a fully formed `KNO_M006_OpportunitySignal` or `KNO_M007_ThreatAlert`.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid

class BriefStatusEnum(str, Enum):
    DRAFT = "Draft"
    SUBMITTED_FOR_CONSIDERATION = "Submitted for Consideration"
    UNDER_REVIEW = "Under Review (by CPO/CSO)"
    ACCEPTED_FOR_PROPOSAL_DEV = "Accepted for Proposal Development"
    DEFERRED = "Deferred"
    REJECTED = "Rejected"

class ProposalNatureEnum(str, Enum):
    NEW_SERVICE = "New Service"
    NEW_FLOW = "New Flow"
    NEW_PROCESS = "New Process"
    NEW_AGENT = "New Agent/Crew"
    OPERATIONAL_AREA_EXPANSION = "Operational Area Expansion"
    INTERNAL_IMPROVEMENT = "Internal Improvement/Optimization"
    STRATEGIC_INITIATIVE = "Strategic Initiative"
    PARTNERSHIP = "Partnership"
    TECHNOLOGY_EXPLORATION = "Technology Exploration"
    OTHER = "Other"

class ProposalDevelopmentBrief(BaseModel):
    brief_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the proposal development brief.")
    title: str = Field(..., description="Working title or subject of the potential proposal.")
    originator_id: str = Field(..., description="ID of the ESTRATIX agent or Command Officer initiating this brief (e.g., 'CEO', 'CTO_A001').")
    originator_co: str = Field(..., description="Command Office of the originator.")
    submission_date: datetime = Field(default_factory=datetime.utcnow, description="Date the brief was submitted.")
    
    # Core Idea
    problem_statement_or_opportunity: str = Field(..., description="Brief description of the problem to be solved or the opportunity to be seized.")
    proposed_concept_or_solution: str = Field(..., description="High-level concept of the proposed solution or initiative.")
    expected_benefits_or_outcomes: str = Field(..., description="Anticipated benefits, value, or outcomes for ESTRATIX.")
    nature_of_proposal: ProposalNatureEnum = Field(..., description="The primary nature or type of the potential proposal.")
    
    # Context & Justification
    strategic_alignment_notes: Optional[str] = Field(None, description="Brief notes on how this aligns with ESTRATIX strategic objectives.")
    key_stakeholders_initial: Optional[List[str]] = Field(default_factory=list, description="Initial list of key stakeholders or COs that might be involved or impacted.")
    known_dependencies_or_constraints: Optional[List[str]] = Field(default_factory=list, description="Any known dependencies, constraints, or prerequisites.")
    
    # Next Steps & Status
    suggested_next_steps: Optional[str] = Field(None, description="What the originator suggests as immediate next steps (e.g., 'Discuss with CPO', 'Conduct preliminary research').")
    brief_status: BriefStatusEnum = Field(default=BriefStatusEnum.DRAFT, description="Current status of this brief.")
    status_last_updated: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of the last status update.")
    cpo_review_notes: Optional[str] = Field(None, description="Notes from CPO/CSO review if applicable.")
    
    # Links to further development
    linked_opportunity_signal_id: Optional[uuid.UUID] = Field(None, description="If this brief leads to or is linked with a formal KNO_M006.")
    linked_threat_alert_id: Optional[uuid.UUID] = Field(None, description="If this brief leads to or is linked with a formal KNO_M007.")
    linked_proposal_id: Optional[uuid.UUID] = Field(None, description="ID of the full proposal (CPO_F001 output) if this brief proceeds to that stage.")

    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, brief-specific metadata.")

```

## 4. Field Descriptions

| Field Name                           | Type                             | Description                                                                                                   | Required | Example Value(s)                                                                 |
|--------------------------------------|----------------------------------|---------------------------------------------------------------------------------------------------------------|----------|----------------------------------------------------------------------------------|
| `brief_id`                           | `uuid.UUID`                      | Unique identifier for the proposal development brief.                                                         | Yes      | `"brief-123-uuid-456-xyz"`                                                       |
| `title`                              | `str`                            | Working title or subject of the potential proposal.                                                           | Yes      | `"Exploration of Generative AI for Content Creation Services"`                     |
| `originator_id`                      | `str`                            | ID of the ESTRATIX agent or Command Officer initiating this brief.                                            | Yes      | `"CEO"`, `"CTO_A001"`                                                            |
| `originator_co`                      | `str`                            | Command Office of the originator.                                                                             | Yes      | `"CEO_Office"`, `"CTO"`                                                          |
| `submission_date`                    | `datetime`                       | Date the brief was submitted.                                                                                 | Yes      | `"2025-05-28T16:00:00Z"`                                                         |
| `problem_statement_or_opportunity`   | `str`                            | Brief description of the problem to be solved or the opportunity to be seized.                                | Yes      | `"Current content creation is manual and slow. GenAI could automate and scale."`   |
| `proposed_concept_or_solution`       | `str`                            | High-level concept of the proposed solution or initiative.                                                    | Yes      | `"Develop a new ESTRATIX service leveraging GenAI models for automated content generation."` |
| `expected_benefits_or_outcomes`      | `str`                            | Anticipated benefits, value, or outcomes for ESTRATIX.                                                        | Yes      | `"Reduced content creation costs, faster turnaround, new revenue stream."`       |
| `nature_of_proposal`                 | `ProposalNatureEnum`             | The primary nature or type of the potential proposal.                                                         | Yes      | `"New Service"`, `"Internal Improvement"`                                        |
| `strategic_alignment_notes`          | `Optional[str]`                  | Brief notes on how this aligns with ESTRATIX strategic objectives.                                            | No       | `"Aligns with strategic pillar of AI adoption and operational efficiency."`      |
| `key_stakeholders_initial`           | `Optional[List[str]]`            | Initial list of key stakeholders or COs.                                                                      | No       | `["CMO", "CTO", "CPO"]`                                                        |
| `known_dependencies_or_constraints`  | `Optional[List[str]]`            | Any known dependencies, constraints, or prerequisites.                                                        | No       | `["Requires access to LLM APIs", "Budget for model fine-tuning"]`                |
| `suggested_next_steps`               | `Optional[str]`                  | What the originator suggests as immediate next steps.                                                         | No       | `"Form a small working group with CTO and CMO reps to explore feasibility."`     |
| `brief_status`                       | `BriefStatusEnum`                | Current status of this brief.                                                                                 | Yes      | `"Draft"`, `"Submitted for Consideration"`                                       |
| `status_last_updated`                | `datetime`                       | Timestamp of the last status update.                                                                          | Yes      | `"2025-05-28T16:00:00Z"`                                                         |
| `cpo_review_notes`                   | `Optional[str]`                  | Notes from CPO/CSO review if applicable.                                                                      | No       | `"Interesting idea. Request more detail on potential market size before proceeding."` |
| `linked_opportunity_signal_id`       | `Optional[uuid.UUID]`            | If linked with a formal `KNO_M006`.                                                                           | No       | `"abc123ef-..."`                                                                 |
| `linked_threat_alert_id`             | `Optional[uuid.UUID]`            | If linked with a formal `KNO_M007`.                                                                           | No       | `"def456gh-..."`                                                                 |
| `linked_proposal_id`                 | `Optional[uuid.UUID]`            | ID of the full proposal if this brief proceeds.                                                               | No       | `"prop-dev-789-uuid"`                                                            |
| `custom_fields`                      | `Optional[Dict[str, Any]]`       | Flexible dictionary for additional, brief-specific metadata.                                                  | No       | `{"initial_budget_estimate_range": "$50k-$100k"}`                               |

## 5. Relationships to Other Data Models

*   Can be an input to `CPO_F001_StrategicOpportunityToProposalDevelopment`.
*   May lead to the creation of, or be linked to, a `KNO_M006_OpportunitySignal` or `KNO_M007_ThreatAlert` if further research by KNO is warranted based on the brief.
*   If accepted for development, it will eventually link to a full proposal document (output of `CPO_F001`).

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** Ad-hoc creation by any Command Officer or designated agent. Could be part of an `XXX_FYYY_InternalInnovationSubmission` flow.
*   **Primary Consuming Flow(s)/Process(es):** `CPO_F001_StrategicOpportunityToProposalDevelopment` (specifically `CPO_T001: Intake & Prioritize Strategic Inputs`).
*   **Key Agents Interacting:** Any ESTRATIX agent/officer (originator), `CPO_A001_PortfolioAnalystAgent` (reviews, processes), CSO agents (review).

## 7. Notes / Future Considerations

*   This model provides a lightweight entry point into the proposal pipeline.
*   The `brief_status` lifecycle should be managed by the CPO's office.
*   Could be integrated with an internal idea management platform.
*   A more formal scoring or prioritization mechanism for briefs could be developed by the CPO.
```
