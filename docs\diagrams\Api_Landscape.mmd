```mermaid
graph TD
    subgraph "ESTRATIX API Landscape"
        direction LR
        A["Placeholder API Group 1"] --> B["Placeholder API Group 2"];
        C["Placeholder Standalone API"] -- "consumes" --> D["Placeholder Service"];
    end

    classDef default fill:#ececff,stroke:#999,stroke-width:2px,color:#000;
    classDef api fill:#lightgreen,stroke:#333,stroke-width:2px;
    classDef service fill:#lightblue,stroke:#333,stroke-width:2px;

    class A,B,C api;
    class D service;

    %% Note: This is a placeholder diagram.
    %% It will be automatically updated by the wf_update_api_landscape.md workflow
    %% based on the contents of docs/matrices/api_matrix.md.
```
