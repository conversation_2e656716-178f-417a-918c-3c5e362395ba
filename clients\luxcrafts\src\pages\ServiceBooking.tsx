import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  StarIcon,
  MapPinIcon,
  ClockIcon,
  CurrencyDollarIcon,
  UserIcon,
  PhoneIcon,
  ChatBubbleLeftRightIcon,
  CalendarIcon,
  CheckCircleIcon,
  XCircleIcon,
  HeartIcon,
  ShareIcon,
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

// Enhanced service categories inspired by Airbnb model
const experienceCategories = [
  { id: 'tours', name: 'City Tours & Sightseeing', icon: '🏛️', count: 156, duration: '2-8 hours' },
  { id: 'outdoor', name: 'Outdoor Adventures', icon: '🏔️', count: 89, duration: '4-12 hours' },
  { id: 'cultural', name: 'Cultural Experiences', icon: '🎭', count: 134, duration: '1-6 hours' },
  { id: 'food', name: 'Food & Drink', icon: '🍷', count: 203, duration: '2-4 hours' },
  { id: 'wellness', name: 'Wellness & Fitness', icon: '🧘', count: 78, duration: '1-3 hours' },
  { id: 'art', name: 'Art & Design', icon: '🎨', count: 92, duration: '2-5 hours' },
  { id: 'music', name: 'Music & Entertainment', icon: '🎵', count: 67, duration: '2-6 hours' },
  { id: 'sports', name: 'Sports & Recreation', icon: '⚽', count: 45, duration: '1-8 hours' },
];

const serviceCategories = [
  { id: 'photography', name: 'Photography', icon: '📸', count: 145, avgPrice: '$200-500' },
  { id: 'chefs', name: 'Private Chefs', icon: '👨‍🍳', count: 89, avgPrice: '$300-800' },
  { id: 'meals', name: 'Prepared Meals', icon: '🍽️', count: 234, avgPrice: '$50-150' },
  { id: 'training', name: 'Personal Training', icon: '💪', count: 167, avgPrice: '$100-300' },
  { id: 'hair', name: 'Hair Services', icon: '💇', count: 123, avgPrice: '$80-250' },
  { id: 'spa', name: 'Spa Treatments', icon: '🧖', count: 198, avgPrice: '$150-400' },
  { id: 'catering', name: 'Catering', icon: '🍾', count: 76, avgPrice: '$500-2000' },
  { id: 'nails', name: 'Nail Services', icon: '💅', count: 134, avgPrice: '$60-150' },
  { id: 'massage', name: 'Massage Therapy', icon: '💆', count: 189, avgPrice: '$120-300' },
  { id: 'makeup', name: 'Makeup Services', icon: '💄', count: 98, avgPrice: '$100-350' },
  { id: 'cleaning', name: 'Cleaning Services', icon: '🧹', count: 345, avgPrice: '$150-400' },
  { id: 'maintenance', name: 'Maintenance & Repair', icon: '🔧', count: 267, avgPrice: '$200-600' },
  { id: 'landscaping', name: 'Landscaping', icon: '🌿', count: 132, avgPrice: '$300-1000' },
  { id: 'security', name: 'Security Services', icon: '🛡️', count: 89, avgPrice: '$400-1200' },
  { id: 'concierge', name: 'Concierge Services', icon: '🎩', count: 67, avgPrice: '$200-800' },
];

const propertyTypes = [
  { id: 'house', name: 'Entire House', icon: '🏠', count: 1234 },
  { id: 'apartment', name: 'Apartment', icon: '🏢', count: 2156 },
  { id: 'hotel', name: 'Hotel Room', icon: '🏨', count: 567 },
  { id: 'guesthouse', name: 'Guesthouse', icon: '🏡', count: 345 },
  { id: 'villa', name: 'Luxury Villa', icon: '🏰', count: 189 },
  { id: 'penthouse', name: 'Penthouse', icon: '🏙️', count: 78 },
  { id: 'room', name: 'Private Room', icon: '🛏️', count: 890 },
];

// Enhanced provider data for all categories
const featuredExperiences = [
  {
    id: 1,
    name: 'Historic Boston Walking Tour',
    category: 'City Tours & Sightseeing',
    type: 'experience',
    rating: 4.9,
    reviews: 1247,
    price: '$45/person',
    duration: '3 hours',
    location: 'Boston, MA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=historic%20boston%20walking%20tour%20guide%20freedom%20trail&image_size=square',
    verified: true,
    hostName: 'Sarah Mitchell',
    languages: ['English', 'Spanish'],
    groupSize: 'Up to 15 people',
    includes: ['Professional guide', 'Historical insights', 'Photo opportunities'],
    availability: 'Available Today',
    timeSlots: ['9:00 AM', '1:00 PM', '5:00 PM'],
  },
  {
    id: 2,
    name: 'Luxury Wine Tasting Experience',
    category: 'Food & Drink',
    type: 'experience',
    rating: 4.8,
    reviews: 892,
    price: '$120/person',
    duration: '2.5 hours',
    location: 'Napa Valley, CA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20wine%20tasting%20vineyard%20sommelier&image_size=square',
    verified: true,
    hostName: 'Marco Rossi',
    languages: ['English', 'Italian', 'French'],
    groupSize: 'Up to 8 people',
    includes: ['Wine tastings', 'Cheese pairings', 'Vineyard tour'],
    availability: 'Available Tomorrow',
    timeSlots: ['11:00 AM', '3:00 PM'],
  },
  {
    id: 3,
    name: 'Private Art Studio Workshop',
    category: 'Art & Design',
    type: 'experience',
    rating: 4.9,
    reviews: 567,
    price: '$85/person',
    duration: '4 hours',
    location: 'Brooklyn, NY',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=private%20art%20studio%20painting%20workshop%20artist&image_size=square',
    verified: true,
    hostName: 'Elena Rodriguez',
    languages: ['English', 'Spanish'],
    groupSize: 'Up to 6 people',
    includes: ['All art supplies', 'Personal instruction', 'Take home artwork'],
    availability: 'Available This Week',
    timeSlots: ['10:00 AM', '2:00 PM'],
  },
];

const featuredServices = [
  {
    id: 4,
    name: 'Elite Property Care',
    category: 'Cleaning Services',
    type: 'service',
    rating: 4.9,
    reviews: 234,
    price: '$150-300/visit',
    location: 'Boston, MA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20cleaning%20service%20team%20luxury%20property&image_size=square',
    verified: true,
    responseTime: '< 2 hours',
    completedJobs: 1247,
    specialties: ['Deep Cleaning', 'Luxury Properties', 'Eco-Friendly'],
    availability: 'Available Today',
    serviceArea: '25 mile radius',
    insurance: 'Fully insured & bonded',
  },
  {
    id: 5,
    name: 'Chef Marcus Culinary',
    category: 'Private Chefs',
    type: 'service',
    rating: 4.9,
    reviews: 156,
    price: '$400-800/event',
    location: 'San Francisco, CA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20private%20chef%20luxury%20kitchen%20cooking&image_size=square',
    verified: true,
    responseTime: '< 4 hours',
    completedJobs: 892,
    specialties: ['French Cuisine', 'Farm-to-Table', 'Dietary Restrictions'],
    availability: 'Available Tomorrow',
    serviceArea: 'Bay Area',
    insurance: 'Fully insured',
  },
  {
    id: 6,
    name: 'Serenity Spa Services',
    category: 'Spa Treatments',
    type: 'service',
    rating: 4.8,
    reviews: 423,
    price: '$200-450/session',
    location: 'Miami, FL',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20spa%20massage%20therapist%20wellness&image_size=square',
    verified: true,
    responseTime: '< 3 hours',
    completedJobs: 1156,
    specialties: ['Deep Tissue', 'Hot Stone', 'Aromatherapy'],
    availability: 'Available Today',
    serviceArea: 'Miami-Dade County',
    insurance: 'Licensed & insured',
  },
];

const featuredProperties = [
  {
    id: 7,
    name: 'Luxury Penthouse Suite',
    category: 'Penthouse',
    type: 'property',
    rating: 4.9,
    reviews: 89,
    price: '$850/night',
    location: 'Manhattan, NY',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20manhattan%20skyline%20view%20modern&image_size=square',
    verified: true,
    hostName: 'David Chen',
    propertyType: 'Entire place',
    guests: 6,
    bedrooms: 3,
    bathrooms: 2,
    amenities: ['City view', 'Gym', 'Pool', 'Concierge', 'Parking'],
    instantBook: true,
    superhost: true,
    accessibility: ['Step-free access', 'Wide doorways'],
  },
  {
    id: 8,
    name: 'Beachfront Villa Paradise',
    category: 'Villa',
    type: 'property',
    rating: 4.8,
    reviews: 156,
    price: '$1,200/night',
    location: 'Malibu, CA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20beachfront%20villa%20malibu%20ocean%20view&image_size=square',
    verified: true,
    hostName: 'Isabella Martinez',
    propertyType: 'Entire place',
    guests: 10,
    bedrooms: 5,
    bathrooms: 4,
    amenities: ['Ocean view', 'Private beach', 'Hot tub', 'Chef kitchen', 'Parking'],
    instantBook: false,
    superhost: true,
    accessibility: ['Step-free access'],
  },
  {
    id: 9,
    name: 'Historic Brownstone Apartment',
    category: 'Apartment',
    type: 'property',
    rating: 4.7,
    reviews: 234,
    price: '$320/night',
    location: 'Boston, MA',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=historic%20brownstone%20apartment%20boston%20elegant%20interior&image_size=square',
    verified: true,
    hostName: 'James Wilson',
    propertyType: 'Entire place',
    guests: 4,
    bedrooms: 2,
    bathrooms: 1,
    amenities: ['Historic charm', 'Modern kitchen', 'Workspace', 'Parking'],
    instantBook: true,
    superhost: false,
    accessibility: [],
  },
];

// Combined providers based on active tab
const getFeaturedProviders = (activeMainTab) => {
  switch (activeMainTab) {
    case 'experiences':
      return featuredExperiences;
    case 'services':
      return featuredServices;
    case 'properties':
      return featuredProperties;
    default:
      return [...featuredExperiences, ...featuredServices, ...featuredProperties];
  }
};

const recentBookings = [
  {
    id: 1,
    service: 'Deep Cleaning Service',
    provider: 'Elite Property Care',
    date: '2024-01-15',
    time: '10:00 AM',
    status: 'Confirmed',
    price: '$250',
    property: '123 Beacon Hill, Boston',
  },
  {
    id: 2,
    service: 'HVAC Maintenance',
    provider: 'Premium Maintenance Co.',
    date: '2024-01-18',
    time: '2:00 PM',
    status: 'In Progress',
    price: '$400',
    property: '456 Back Bay, Boston',
  },
  {
    id: 3,
    service: 'Garden Redesign',
    provider: 'Luxury Landscapes',
    date: '2024-01-20',
    time: '9:00 AM',
    status: 'Pending',
    price: '$1,200',
    property: '789 Cambridge St, Cambridge',
  },
];

export default function ServiceBooking() {
  const [activeMainTab, setActiveMainTab] = useState('experiences'); // experiences, services, properties
  const [activeTab, setActiveTab] = useState('discover');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  
  // Enhanced filter states
  const [filters, setFilters] = useState({
    location: '',
    checkIn: '',
    checkOut: '',
    guests: 1,
    priceRange: [0, 2000],
    duration: 'any', // any, 1-2h, 2-4h, 4-8h, 8h+
    travelerType: 'any', // any, solo, couple, family, group
    timeOfDay: 'any', // any, morning, afternoon, evening, night
    language: 'any', // any, english, spanish, french, etc.
    accessibility: [],
    instantBook: false,
    selfCheckIn: false,
    allowPets: false,
    propertyType: 'any',
    rooms: 'any',
    beds: 'any',
    baths: 'any',
    amenities: [],
    standoutStays: false,
    luxeProperties: false,
    hostLanguage: 'any'
  });

  const mainTabs = [
    { id: 'experiences', name: 'Experiences', count: experienceCategories.reduce((sum, cat) => sum + cat.count, 0) },
    { id: 'services', name: 'Services', count: serviceCategories.reduce((sum, cat) => sum + cat.count, 0) },
    { id: 'properties', name: 'Properties', count: propertyTypes.reduce((sum, type) => sum + type.count, 0) },
  ];

  const tabs = [
    { id: 'discover', name: 'Discover' },
    { id: 'bookings', name: 'My Bookings' },
    { id: 'favorites', name: 'Favorites' },
    { id: 'quotes', name: 'Quotes' },
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarIconSolid
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Luxcrafts Marketplace
          </h1>
          <p className="text-xl text-gray-600">
            Discover experiences, services, and luxury properties worldwide
          </p>
        </div>

        {/* Main Category Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {mainTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveMainTab(tab.id)}
                className={`flex-1 py-3 px-4 rounded-md font-medium text-sm transition-all ${
                  activeMainTab === tab.id
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.name}
                <span className="ml-2 text-xs bg-gray-200 px-2 py-1 rounded-full">
                  {tab.count.toLocaleString()}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Sub Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Discover Services Tab */}
        {activeTab === 'discover' && (
          <div className="space-y-8">
            {/* Enhanced Search and Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              {/* Main Search Bar */}
              <div className="flex flex-col lg:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder={`Search ${activeMainTab}...`}
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                {/* Quick Filters */}
                <div className="flex flex-wrap gap-3">
                  <div className="flex items-center space-x-2">
                    <MapPinIcon className="w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Location"
                      value={filters.location}
                      onChange={(e) => setFilters({...filters, location: e.target.value})}
                      className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 w-32"
                    />
                  </div>
                  
                  {(activeMainTab === 'experiences' || activeMainTab === 'properties') && (
                    <>
                      <div className="flex items-center space-x-2">
                        <CalendarIcon className="w-4 h-4 text-gray-400" />
                        <input
                          type="date"
                          value={filters.checkIn}
                          onChange={(e) => setFilters({...filters, checkIn: e.target.value})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <UserIcon className="w-4 h-4 text-gray-400" />
                        <select
                          value={filters.guests}
                          onChange={(e) => setFilters({...filters, guests: parseInt(e.target.value)})}
                          className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {[1,2,3,4,5,6,7,8].map(num => (
                            <option key={num} value={num}>{num} guest{num > 1 ? 's' : ''}</option>
                          ))}
                        </select>
                      </div>
                    </>
                  )}
                    
                    <button
                    onClick={() => setShowFilters(!showFilters)}
                    className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center space-x-2"
                  >
                    <span>Filters</span>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {Object.values(filters).filter(v => v && v !== 'any' && v !== 0 && (Array.isArray(v) ? v.length > 0 : true)).length}
                    </span>
                  </button>
                </div>
              </div>
              
              {/* Advanced Filters Panel */}
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="border-t pt-6 mt-6"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    
                    {/* Price Range */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Price Range</label>
                      <div className="space-y-2">
                        <input
                          type="range"
                          min="0"
                          max="2000"
                          value={filters.priceRange[1]}
                          onChange={(e) => setFilters({...filters, priceRange: [0, parseInt(e.target.value)]})}
                          className="w-full"
                        />
                        <div className="flex justify-between text-sm text-gray-500">
                          <span>$0</span>
                          <span>${filters.priceRange[1]}+</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Duration (for experiences) */}
                    {activeMainTab === 'experiences' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                        <select
                          value={filters.duration}
                          onChange={(e) => setFilters({...filters, duration: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="any">Any duration</option>
                          <option value="1-2h">1-2 hours</option>
                          <option value="2-4h">2-4 hours</option>
                          <option value="4-8h">4-8 hours</option>
                          <option value="8h+">8+ hours</option>
                        </select>
                      </div>
                    )}
                    
                    {/* Traveler Type */}
                    {activeMainTab === 'experiences' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Traveler Type</label>
                        <select
                          value={filters.travelerType}
                          onChange={(e) => setFilters({...filters, travelerType: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="any">Any traveler</option>
                          <option value="solo">Solo traveler</option>
                          <option value="couple">Couple</option>
                          <option value="family">Family</option>
                          <option value="group">Group</option>
                        </select>
                      </div>
                    )}
                    
                    {/* Time of Day */}
                    {activeMainTab === 'experiences' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Time of Day</label>
                        <select
                          value={filters.timeOfDay}
                          onChange={(e) => setFilters({...filters, timeOfDay: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="any">Any time</option>
                          <option value="morning">Morning (6AM-12PM)</option>
                          <option value="afternoon">Afternoon (12PM-6PM)</option>
                          <option value="evening">Evening (6PM-10PM)</option>
                          <option value="night">Night (10PM-6AM)</option>
                        </select>
                      </div>
                    )}
                    
                    {/* Language */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                      <select
                        value={filters.language}
                        onChange={(e) => setFilters({...filters, language: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="any">Any language</option>
                        <option value="english">English</option>
                        <option value="spanish">Spanish</option>
                        <option value="french">French</option>
                        <option value="german">German</option>
                        <option value="italian">Italian</option>
                        <option value="portuguese">Portuguese</option>
                        <option value="chinese">Chinese</option>
                        <option value="japanese">Japanese</option>
                      </select>
                    </div>
                    
                    {/* Property Type (for properties) */}
                    {activeMainTab === 'properties' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                        <select
                          value={filters.propertyType}
                          onChange={(e) => setFilters({...filters, propertyType: e.target.value})}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="any">Any type</option>
                          {propertyTypes.map(type => (
                            <option key={type.id} value={type.id}>{type.name}</option>
                          ))}
                        </select>
                      </div>
                    )}
                    
                    {/* Rooms & Beds (for properties) */}
                    {activeMainTab === 'properties' && (
                      <>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Bedrooms</label>
                          <select
                            value={filters.rooms}
                            onChange={(e) => setFilters({...filters, rooms: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="any">Any</option>
                            <option value="1">1+</option>
                            <option value="2">2+</option>
                            <option value="3">3+</option>
                            <option value="4">4+</option>
                            <option value="5">5+</option>
                          </select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Bathrooms</label>
                          <select
                            value={filters.baths}
                            onChange={(e) => setFilters({...filters, baths: e.target.value})}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          >
                            <option value="any">Any</option>
                            <option value="1">1+</option>
                            <option value="2">2+</option>
                            <option value="3">3+</option>
                            <option value="4">4+</option>
                          </select>
                        </div>
                      </>
                    )}
                  </div>
                  
                  {/* Checkbox Filters */}
                  <div className="mt-6 pt-6 border-t">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      
                      {/* Booking Options */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Booking Options</h4>
                        <div className="space-y-2">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.instantBook}
                              onChange={(e) => setFilters({...filters, instantBook: e.target.checked})}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Instant Book</span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.selfCheckIn}
                              onChange={(e) => setFilters({...filters, selfCheckIn: e.target.checked})}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Self check-in</span>
                          </label>
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.allowPets}
                              onChange={(e) => setFilters({...filters, allowPets: e.target.checked})}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">Pets allowed</span>
                          </label>
                        </div>
                      </div>
                      
                      {/* Standout Features */}
                      {activeMainTab === 'properties' && (
                        <div>
                          <h4 className="font-medium text-gray-900 mb-3">Standout Stays</h4>
                          <div className="space-y-2">
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={filters.standoutStays}
                                onChange={(e) => setFilters({...filters, standoutStays: e.target.checked})}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="ml-2 text-sm text-gray-700">Guest favorites</span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={filters.luxeProperties}
                                onChange={(e) => setFilters({...filters, luxeProperties: e.target.checked})}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="ml-2 text-sm text-gray-700">Luxe properties</span>
                            </label>
                          </div>
                        </div>
                      )}
                      
                      {/* Accessibility Features */}
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Accessibility</h4>
                        <div className="space-y-2">
                          {[
                            'Step-free guest entrance',
                            'Guest entrance wider than 32 inches',
                            'Accessible parking spot',
                            'Step-free bedroom access',
                            'Bedroom entrance wider than 32 inches',
                            'Step-free bathroom access',
                            'Bathroom entrance wider than 32 inches',
                            'Toilet grab bar',
                            'Shower grab bar',
                            'Step-free shower'
                          ].map((feature) => (
                            <label key={feature} className="flex items-center">
                              <input
                                type="checkbox"
                                checked={filters.accessibility.includes(feature)}
                                onChange={(e) => {
                                  if (e.target.checked) {
                                    setFilters({...filters, accessibility: [...filters.accessibility, feature]});
                                  } else {
                                    setFilters({...filters, accessibility: filters.accessibility.filter(f => f !== feature)});
                                  }
                                }}
                                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              />
                              <span className="ml-2 text-sm text-gray-700">{feature}</span>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Filter Actions */}
                  <div className="mt-6 pt-6 border-t flex justify-between">
                    <button
                      onClick={() => setFilters({
                        location: '',
                        checkIn: '',
                        checkOut: '',
                        guests: 1,
                        priceRange: [0, 2000],
                        duration: 'any',
                        travelerType: 'any',
                        timeOfDay: 'any',
                        language: 'any',
                        accessibility: [],
                        instantBook: false,
                        selfCheckIn: false,
                        allowPets: false,
                        propertyType: 'any',
                        rooms: 'any',
                        beds: 'any',
                        baths: 'any',
                        amenities: [],
                        standoutStays: false,
                        luxeProperties: false,
                        hostLanguage: 'any'
                      })}
                      className="px-4 py-2 text-gray-600 hover:text-gray-900"
                    >
                      Clear all
                    </button>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                      Show results
                    </button>
                  </div>
                </motion.div>
              )}
            </div>

            {/* Dynamic Categories */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {activeMainTab === 'experiences' ? 'Experience Categories' :
                 activeMainTab === 'services' ? 'Service Categories' :
                 'Property Types'}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                {(activeMainTab === 'experiences' ? experienceCategories :
                  activeMainTab === 'services' ? serviceCategories :
                  propertyTypes).map((category) => (
                  <motion.button
                    key={category.id}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`p-4 rounded-xl border-2 transition-all duration-200 ${
                      selectedCategory === category.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="text-3xl mb-2">{category.icon}</div>
                    <h4 className="font-semibold text-gray-900 text-sm">{category.name}</h4>
                    <p className="text-xs text-gray-500">
                      {category.count} {activeMainTab === 'properties' ? 'listings' : 
                       activeMainTab === 'experiences' ? 'experiences' : 'providers'}
                    </p>
                    {activeMainTab === 'experiences' && 'duration' in category && category.duration && (
                      <p className="text-xs text-blue-600 mt-1">{String(category.duration)}</p>
                    )}
                    {activeMainTab === 'services' && 'avgPrice' in category && category.avgPrice && (
                      <p className="text-xs text-green-600 mt-1">{String(category.avgPrice)}</p>
                    )}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Featured Listings */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {activeMainTab === 'experiences' ? 'Featured Experiences' :
                 activeMainTab === 'services' ? 'Featured Services' :
                 'Featured Properties'}
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {getFeaturedProviders(activeMainTab).map((provider) => (
                  <motion.div
                    key={provider.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => setSelectedProvider(provider)}
                  >
                    <div className="relative mb-4">
                      <img
                        src={provider.image}
                        alt={provider.name}
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute top-3 right-3 flex space-x-2">
                        <button className="p-2 bg-white/80 backdrop-blur-sm rounded-full text-gray-600 hover:text-red-500 transition-colors">
                          <HeartIcon className="w-4 h-4" />
                        </button>
                        <button className="p-2 bg-white/80 backdrop-blur-sm rounded-full text-gray-600 hover:text-blue-500 transition-colors">
                          <ShareIcon className="w-4 h-4" />
                        </button>
                      </div>
                      {'superhost' in provider && provider.superhost && (
                        <div className="absolute top-3 left-3 px-2 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-xs font-medium rounded-full">
                          Superhost
                        </div>
                      )}
                      {'instantBook' in provider && provider.instantBook && (
                        <div className="absolute bottom-3 left-3 px-2 py-1 bg-blue-600 text-white text-xs font-medium rounded-full">
                          Instant Book
                        </div>
                      )}
                    </div>
                    
                    <div className="mb-4">
                      <h4 className="font-bold text-gray-900 text-lg mb-1">{provider.name}</h4>
                      <p className="text-sm text-gray-600">{provider.category}</p>
                      {'hostName' in provider && (
                        <p className="text-sm text-gray-500">Hosted by {provider.hostName}</p>
                      )}
                    </div>

                    <div className="flex items-center space-x-2 mb-3">
                      <div className="flex">{renderStars(provider.rating)}</div>
                      <span className="text-sm font-medium">{provider.rating}</span>
                      <span className="text-sm text-gray-500">({provider.reviews} reviews)</span>
                      {provider.verified && (
                        <CheckCircleIcon className="w-4 h-4 text-green-500" />
                      )}
                    </div>

                    <div className="space-y-3 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Price:</span>
                        <span className="font-semibold text-lg">{provider.price}</span>
                      </div>
                      
                      {/* Experience-specific details */}
                      {provider.type === 'experience' && (
                        <>
                          {'duration' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Duration:</span>
                              <span className="font-medium">{provider.duration}</span>
                            </div>
                          )}
                          {'groupSize' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Group size:</span>
                              <span className="font-medium">{provider.groupSize}</span>
                            </div>
                          )}
                          {'languages' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Languages:</span>
                              <span className="font-medium">{provider.languages?.join(', ')}</span>
                            </div>
                          )}
                        </>
                      )}
                      
                      {/* Service-specific details */}
                      {provider.type === 'service' && (
                        <>
                          {'responseTime' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Response time:</span>
                              <span className="font-medium text-green-600">{provider.responseTime}</span>
                            </div>
                          )}
                          {'serviceArea' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Service area:</span>
                              <span className="font-medium">{provider.serviceArea}</span>
                            </div>
                          )}
                          {'completedJobs' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Completed jobs:</span>
                              <span className="font-medium">{provider.completedJobs}</span>
                            </div>
                          )}
                        </>
                      )}
                      
                      {/* Property-specific details */}
                      {provider.type === 'property' && (
                        <>
                          {'propertyType' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Property type:</span>
                              <span className="font-medium">{provider.propertyType}</span>
                            </div>
                          )}
                          {'guests' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Guests:</span>
                              <span className="font-medium">{provider.guests} guests</span>
                            </div>
                          )}
                          {'bedrooms' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Bedrooms:</span>
                              <span className="font-medium">{provider.bedrooms} bed{provider.bedrooms > 1 ? 's' : ''}</span>
                            </div>
                          )}
                          {'bathrooms' in provider && (
                            <div className="flex items-center justify-between text-sm">
                              <span className="text-gray-500">Bathrooms:</span>
                              <span className="font-medium">{provider.bathrooms} bath{provider.bathrooms > 1 ? 's' : ''}</span>
                            </div>
                          )}
                        </>
                      )}
                      
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Location:</span>
                        <span className="font-medium">{provider.location}</span>
                      </div>
                    </div>

                    {/* Features/Specialties/Amenities */}
                    <div className="mb-4">
                      <p className="text-sm text-gray-500 mb-2">
                        {provider.type === 'experience' ? 'Includes:' :
                         provider.type === 'service' ? 'Specialties:' :
                         'Amenities:'}
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {(
                          ('includes' in provider ? provider.includes : null) ||
                          ('specialties' in provider ? provider.specialties : null) ||
                          ('amenities' in provider ? provider.amenities : null)
                        )?.map((item) => (
                          <span
                            key={item}
                            className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                          >
                            {item}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Availability */}
                    <div className="flex items-center justify-between mb-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        'availability' in provider && provider.availability?.includes('Today') ? 'bg-green-100 text-green-800' :
                        'availability' in provider && provider.availability?.includes('Tomorrow') ? 'bg-yellow-100 text-yellow-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {'availability' in provider ? provider.availability : 'Available'}
                      </span>
                      
                      {/* Time slots for experiences */}
                      {provider.type === 'experience' && 'timeSlots' in provider && provider.timeSlots && (
                        <div className="flex space-x-1">
                          {provider.timeSlots.slice(0, 2).map((time) => (
                            <span key={time} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                              {time}
                            </span>
                          ))}
                          {provider.timeSlots.length > 2 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                              +{provider.timeSlots.length - 2}
                            </span>
                          )}
                        </div>
                      )}
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowBookingModal(true);
                        }}
                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                      >
                        Book Now
                      </button>
                      <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <ChatBubbleLeftRightIcon className="w-4 h-4" />
                      </button>
                      <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                        <PhoneIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* My Bookings Tab */}
        {activeTab === 'bookings' && (
          <div className="space-y-8">
            {/* Booking Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                    <p className="text-3xl font-bold text-gray-900">47</p>
                  </div>
                  <CalendarIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">This Month</p>
                    <p className="text-3xl font-bold text-gray-900">8</p>
                  </div>
                  <ClockIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Spent</p>
                    <p className="text-3xl font-bold text-gray-900">$12.4K</p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                    <p className="text-3xl font-bold text-gray-900">4.8</p>
                  </div>
                  <StarIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>

            {/* Recent Bookings */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Bookings</h3>
              <div className="space-y-4">
                {recentBookings.map((booking) => (
                  <div key={booking.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900">{booking.service}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            booking.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                            booking.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-yellow-100 text-yellow-800'
                          }`}>
                            {booking.status}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mb-1">{booking.provider}</p>
                        <p className="text-sm text-gray-500">{booking.property}</p>
                        <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                          <span>{booking.date}</span>
                          <span>{booking.time}</span>
                          <span className="font-medium text-gray-900">{booking.price}</span>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button className="px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                          View Details
                        </button>
                        <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium">
                          Contact
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Favorites Tab */}
        {activeTab === 'favorites' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Favorite Providers</h3>
            <div className="text-center py-12">
              <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">No favorites yet</h4>
              <p className="text-gray-600 mb-6">
                Start adding providers to your favorites to quickly book them again.
              </p>
              <button
                onClick={() => setActiveTab('discover')}
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Discover Providers
              </button>
            </div>
          </div>
        )}

        {/* Quotes Tab */}
        {activeTab === 'quotes' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Request Quotes</h3>
            <div className="text-center py-12">
              <CurrencyDollarIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">Custom Quote System</h4>
              <p className="text-gray-600 mb-6">
                Get personalized quotes from multiple providers for your specific needs.
              </p>
              <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                Request Quote
              </button>
            </div>
          </div>
        )}

        {/* Booking Modal */}
        {showBookingModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900">Book Service</h3>
                <button
                  onClick={() => setShowBookingModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <XCircleIcon className="w-6 h-6" />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Select Property
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>123 Beacon Hill, Boston</option>
                    <option>456 Back Bay, Boston</option>
                    <option>789 Cambridge St, Cambridge</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date
                  </label>
                  <input
                    type="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>Morning (8AM - 12PM)</option>
                    <option>Afternoon (12PM - 5PM)</option>
                    <option>Evening (5PM - 8PM)</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Special Instructions
                  </label>
                  <textarea
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Any specific requirements or instructions..."
                  />
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowBookingModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Confirm Booking
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}