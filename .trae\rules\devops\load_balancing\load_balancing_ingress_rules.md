# ESTRATIX Agentic Rules: Load Balancing and Ingress Management
# Rule ID: R-DO-004

---

## 1. Core Principles

This document establishes the agentic rules for managing ingress traffic and load balancing for services within ESTRATIX Kubernetes clusters. The primary goal is to ensure secure, reliable, and efficient routing of external traffic to internal services.

- **Principle 1: Centralized Ingress Control**
  - **Rule:** All external HTTP/S traffic MUST be routed through a standardized, managed Ingress controller. Direct exposure of services via `LoadBalancer` or `NodePort` types is prohibited for HTTP/S traffic unless explicitly justified and approved for specific use cases (e.g., TCP/UDP services).
  - **Enforcement:** DevOps agents will deploy and manage a standard Ingress controller (e.g., NGINX Ingress Controller) across all clusters. CI/CD pipelines will reject Service manifests that violate this rule.

- **Principle 2: Secure by Default (TLS Everywhere)**
  - **Rule:** All Ingress resources MUST enforce TLS encryption. Plain-text HTTP traffic SHOULD be automatically redirected to HTTPS. TLS certificates MUST be managed automatically.
  - **Enforcement:** SecOps and DevOps agents will manage `cert-manager` to automatically provision and renew TLS certificates from Let's Encrypt or a trusted internal CA. Ingress manifests without a valid `tls` section will be rejected.

- **Principle 3: Granular Routing and Traffic Shaping**
  - **Rule:** Routing MUST be defined using standard Ingress objects with clear host-based or path-based rules. Advanced traffic management (e.g., canary releases, A/B testing) should leverage specialized CRDs (e.g., Istio VirtualService, Flagger Canary).
  - **Enforcement:** DevOps agents will monitor Ingress configurations for clarity and correctness. GitOps agents will manage the rollout of traffic-shaping policies.

---

## 2. Specific Rules

### 2.1. Ingress Configuration
- **Rule R-DO-004.1 (Standard Annotations):** Ingress resources MUST use a standard set of annotations for configuration (e.g., `nginx.ingress.kubernetes.io/rewrite-target`, `cert-manager.io/cluster-issuer`). Custom or non-standard annotations require documentation and approval.
- **Rule R-DO-004.2 (Host Naming):** Hostnames used in Ingress rules MUST follow ESTRATIX naming conventions and be registered in the appropriate DNS service. Wildcard hosts are restricted and require a security review.
- **Rule R-DO-004.3 (Backend Service):** Each Ingress path MUST route to a valid backend Service and port. The backend Service MUST be of type `ClusterIP`.

### 2.2. Security Policies
- **Rule R-DO-004.4 (Rate Limiting):** Rate limiting MUST be configured on all public-facing endpoints to protect against abuse and DoS attacks. Configuration should be applied via standard Ingress annotations.
- **Rule R-DO-004.5 (WAF Integration):** High-security applications MUST be protected by a Web Application Firewall (WAF). This can be integrated at the Ingress controller level or via a cloud provider service.
- **Rule R-DO-004.6 (IP Whitelisting):** Access to sensitive or internal endpoints exposed via Ingress MUST be restricted using IP whitelisting annotations (`nginx.ingress.kubernetes.io/whitelist-source-range`).

### 2.3. Health and Performance
- **Rule R-DO-004.7 (Load Balancing Algorithm):** The default load balancing algorithm should be `round-robin`. For services requiring session persistence, cookie-based affinity (`nginx.ingress.kubernetes.io/affinity: cookie`) MUST be used.
- **Rule R-DO-004.8 (Timeouts):** Default timeout values MUST be configured at the Ingress controller level. Per-service timeouts can be overridden via annotations but must be justified.

---

## 3. Enforcement by ESTRATIX Agents

- **DevOps Agents:** Manage the lifecycle of Ingress controllers, `cert-manager`, and routing configurations.
- **SecOps Agents:** Enforce TLS policies, WAF integration, and IP whitelisting. Audit Ingress configurations for security vulnerabilities.
- **GitOps Agents:** Ensure the live Ingress configuration in the cluster matches the state defined in Git.
