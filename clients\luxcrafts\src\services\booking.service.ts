import { apiClient, ApiResponse } from './api.client';
import { BaseService } from './base.service';
import { buildSearchParams } from './service.utils';

// Booking types
export interface Booking {
  id: string;
  userId: string;
  providerId: string;
  serviceId: string;
  propertyId?: string;
  type: 'service' | 'experience' | 'property';
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  scheduledDate: Date;
  duration: number; // in minutes
  totalAmount: number;
  currency: string;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface BookingRequest {
  providerId: string;
  serviceId: string;
  propertyId?: string;
  type: 'service' | 'experience' | 'property';
  scheduledDate: Date;
  duration: number;
  notes?: string;
  guestCount?: number;
  specialRequests?: string[];
}

export interface BookingFilters {
  status?: Booking['status'][];
  type?: Booking['type'][];
  dateFrom?: Date;
  dateTo?: Date;
  providerId?: string;
  serviceId?: string;
}

export interface BookingStats {
  total: number;
  pending: number;
  confirmed: number;
  completed: number;
  cancelled: number;
  totalRevenue: number;
  averageRating: number;
}

// Mock data for development
const mockBookings: Booking[] = [
  {
    id: '1',
    userId: 'user-1',
    providerId: 'provider-1',
    serviceId: 'service-1',
    type: 'service',
    status: 'confirmed',
    scheduledDate: new Date('2024-01-15T10:00:00'),
    duration: 180,
    totalAmount: 350,
    currency: 'USD',
    paymentStatus: 'paid',
    notes: 'Deep cleaning for 3-bedroom apartment',
    createdAt: new Date('2024-01-10T09:00:00'),
    updatedAt: new Date('2024-01-10T09:00:00')
  },
  {
    id: '2',
    userId: 'user-2',
    providerId: 'provider-2',
    serviceId: 'service-2',
    type: 'experience',
    status: 'pending',
    scheduledDate: new Date('2024-01-20T14:00:00'),
    duration: 240,
    totalAmount: 150,
    currency: 'USD',
    paymentStatus: 'pending',
    notes: 'Wine tasting for 4 people',
    createdAt: new Date('2024-01-12T15:30:00'),
    updatedAt: new Date('2024-01-12T15:30:00')
  },
  {
    id: '3',
    userId: 'user-3',
    providerId: 'provider-3',
    serviceId: 'service-3',
    propertyId: 'property-1',
    type: 'property',
    status: 'completed',
    scheduledDate: new Date('2024-01-05T16:00:00'),
    duration: 1440, // 24 hours
    totalAmount: 500,
    currency: 'USD',
    paymentStatus: 'paid',
    notes: 'Luxury penthouse rental',
    createdAt: new Date('2024-01-01T12:00:00'),
    updatedAt: new Date('2024-01-06T10:00:00')
  }
];

class BookingService extends BaseService {
  async createBooking(request: BookingRequest): Promise<Booking> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<Booking>('/bookings', request);
        return response.data;
      },
      async () => {
      
      const newBooking: Booking = {
        id: `booking-${Date.now()}`,
        userId: 'current-user', // Would come from auth context
        ...request,
        status: 'pending',
        totalAmount: this.calculateAmount(request),
        currency: 'USD',
        paymentStatus: 'pending',
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
        mockBookings.push(newBooking);
        return newBooking;
      },
      1000
    );
  }

  async getBookings(filters?: BookingFilters): Promise<Booking[]> {
    return this.handleApiCall(
      async () => {
        const searchParams = buildSearchParams({
          status: filters?.status?.join(','),
          type: filters?.type?.join(','),
          dateFrom: filters?.dateFrom?.toISOString(),
          dateTo: filters?.dateTo?.toISOString(),
          providerId: filters?.providerId,
          serviceId: filters?.serviceId
        });
        const response = await apiClient.get<Booking[]>(`/bookings?${searchParams.toString()}`);
        return response.data;
      },
      async () => {
        let filteredBookings = [...mockBookings];
        
        if (filters?.status) {
          filteredBookings = filteredBookings.filter(b => filters.status!.includes(b.status));
        }
        
        if (filters?.type) {
          filteredBookings = filteredBookings.filter(b => filters.type!.includes(b.type));
        }
        
        if (filters?.dateFrom) {
          filteredBookings = filteredBookings.filter(b => b.scheduledDate >= filters.dateFrom!);
        }
        
        if (filters?.dateTo) {
          filteredBookings = filteredBookings.filter(b => b.scheduledDate <= filters.dateTo!);
        }
        
        return filteredBookings.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      },
      500
    );
  }

  async getBookingById(id: string): Promise<Booking> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Booking>(`/bookings/${id}`);
        return response.data;
      },
      async () => {
        const booking = mockBookings.find(b => b.id === id);
        if (!booking) {
          throw new Error('Booking not found');
        }
        return booking;
      },
      300
    );
  }

  async updateBookingStatus(id: string, status: Booking['status'], notes?: string): Promise<Booking> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.patch<Booking>(`/bookings/${id}/status`, {
          status,
          notes
        });
        return response.data;
      },
      async () => {
        const bookingIndex = mockBookings.findIndex(b => b.id === id);
        if (bookingIndex === -1) {
          throw new Error('Booking not found');
        }
        
        mockBookings[bookingIndex] = {
          ...mockBookings[bookingIndex],
          status,
          notes: notes || mockBookings[bookingIndex].notes,
          updatedAt: new Date()
        };
        
        return mockBookings[bookingIndex];
      },
      500
    );
  }

  async cancelBooking(id: string, reason?: string): Promise<Booking> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.patch<Booking>(`/bookings/${id}/cancel`, {
          reason
        });
        return response.data;
      },
      async () => {
        const bookingIndex = mockBookings.findIndex(b => b.id === id);
        if (bookingIndex === -1) {
          throw new Error('Booking not found');
        }
        
        mockBookings[bookingIndex] = {
          ...mockBookings[bookingIndex],
          status: 'cancelled',
          notes: reason ? `Cancelled: ${reason}` : 'Cancelled by user',
          updatedAt: new Date()
        };
        
        return mockBookings[bookingIndex];
      },
      500
    );
  }

  async getBookingStats(filters?: BookingFilters): Promise<BookingStats> {
    if (this.isDevelopment) {
      await new Promise(resolve => setTimeout(resolve, 300));
      
      const bookings = await this.getBookings(filters);
      
      return {
        total: bookings.length,
        pending: bookings.filter(b => b.status === 'pending').length,
        confirmed: bookings.filter(b => b.status === 'confirmed').length,
        completed: bookings.filter(b => b.status === 'completed').length,
        cancelled: bookings.filter(b => b.status === 'cancelled').length,
        totalRevenue: bookings
          .filter(b => b.paymentStatus === 'paid')
          .reduce((sum, b) => sum + b.totalAmount, 0),
        averageRating: 4.7 // Mock average rating
      };
    }

    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status.join(','));
    if (filters?.type) params.append('type', filters.type.join(','));
    if (filters?.dateFrom) params.append('dateFrom', filters.dateFrom.toISOString());
    if (filters?.dateTo) params.append('dateTo', filters.dateTo.toISOString());

    const response = await apiClient.get<BookingStats>(`/bookings/stats?${params.toString()}`);
    return response.data;
  }

  async getAvailableSlots(providerId: string, serviceId: string, date: Date): Promise<string[]> {
    if (this.isDevelopment) {
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock available time slots
      return [
        '09:00', '10:00', '11:00', '14:00', '15:00', '16:00'
      ];
    }

    const response = await apiClient.get<string[]>(
      `/bookings/availability/${providerId}/${serviceId}?date=${date.toISOString().split('T')[0]}`
    );
    return response.data;
  }

  async processPayment(bookingId: string, paymentMethod: string): Promise<{ success: boolean; transactionId?: string }> {
    if (this.isDevelopment) {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mock payment processing
      const success = Math.random() > 0.1; // 90% success rate
      
      if (success) {
        const bookingIndex = mockBookings.findIndex(b => b.id === bookingId);
        if (bookingIndex !== -1) {
          mockBookings[bookingIndex].paymentStatus = 'paid';
          mockBookings[bookingIndex].status = 'confirmed';
        }
        
        return {
          success: true,
          transactionId: `txn_${Date.now()}`
        };
      } else {
        throw new Error('Payment failed. Please try again.');
      }
    }

    const response = await apiClient.post<{ success: boolean; transactionId?: string }>(
      `/bookings/${bookingId}/payment`,
      { paymentMethod }
    );
    return response.data;
  }

  private calculateAmount(request: BookingRequest): number {
    // Mock pricing calculation
    const baseRates = {
      service: 100, // $100/hour
      experience: 50, // $50/hour
      property: 200 // $200/day
    };
    
    const rate = baseRates[request.type];
    const hours = request.duration / 60;
    
    return Math.round(rate * hours);
  }
}

export const bookingService = new BookingService();
export default bookingService;