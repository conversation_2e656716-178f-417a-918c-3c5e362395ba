import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Activity, 
  Server, 
  Wifi, 
  Database,
  RefreshCw,
  Download,
  Eye,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { errorMonitoring, ErrorType, ErrorSeverity, type ErrorReport, type HealthMetrics } from '@/utils/errorMonitoring';
import { toast } from 'sonner';

interface EndpointStatus {
  url: string;
  name: string;
  healthy: boolean;
  responseTime: number;
  lastChecked: Date;
  error?: string;
}

const MonitoringDashboard: React.FC = () => {
  const [healthMetrics, setHealthMetrics] = useState<HealthMetrics | null>(null);
  const [errors, setErrors] = useState<ErrorReport[]>([]);
  const [endpoints, setEndpoints] = useState<EndpointStatus[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedError, setSelectedError] = useState<ErrorReport | null>(null);

  const criticalEndpoints = [
    { url: '/api/health', name: 'API Health' },
    { url: '/api/auth/status', name: 'Authentication' },
    { url: '/api/properties', name: 'Properties API' },
    { url: '/api/blockchain/status', name: 'Blockchain' },
    { url: '/api/monitoring/metrics', name: 'Metrics' }
  ];

  useEffect(() => {
    refreshData();
    const interval = setInterval(refreshData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  const refreshData = async () => {
    setIsRefreshing(true);
    
    try {
      // Get health metrics
      const metrics = errorMonitoring.getHealthReport();
      setHealthMetrics(metrics);
      
      // Get recent errors
      const recentErrors = errorMonitoring.getErrors({ limit: 50 });
      setErrors(recentErrors);
      
      // Check endpoint health
      const endpointChecks = await Promise.all(
        criticalEndpoints.map(async (endpoint) => {
          const result = await errorMonitoring.checkEndpointHealth(endpoint.url);
          return {
            ...endpoint,
            healthy: result.healthy,
            responseTime: result.responseTime,
            lastChecked: new Date(),
            error: result.error
          };
        })
      );
      
      setEndpoints(endpointChecks);
    } catch (error) {
      toast.error('Failed to refresh monitoring data');
      console.error('Monitoring refresh error:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getSeverityColor = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return 'destructive';
      case ErrorSeverity.HIGH: return 'destructive';
      case ErrorSeverity.MEDIUM: return 'secondary';
      case ErrorSeverity.LOW: return 'outline';
      default: return 'outline';
    }
  };

  const getSeverityIcon = (severity: ErrorSeverity) => {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return <XCircle className="h-4 w-4" />;
      case ErrorSeverity.HIGH: return <AlertTriangle className="h-4 w-4" />;
      case ErrorSeverity.MEDIUM: return <Clock className="h-4 w-4" />;
      case ErrorSeverity.LOW: return <CheckCircle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const formatUptime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hours}h ${minutes}m ${secs}s`;
  };

  const exportErrors = () => {
    const data = JSON.stringify(errors, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `errors-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    toast.success('Error log exported successfully');
  };

  const clearAllErrors = () => {
    errorMonitoring.clearErrors();
    setErrors([]);
    toast.success('All errors cleared');
  };

  if (!healthMetrics) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading monitoring data...</span>
      </div>
    );
  }

  const healthScore = Math.max(0, 100 - (healthMetrics.totalErrors * 2));
  const criticalErrors = errors.filter(e => e.severity === ErrorSeverity.CRITICAL).length;
  const highErrors = errors.filter(e => e.severity === ErrorSeverity.HIGH).length;

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">System Monitoring</h1>
          <p className="text-muted-foreground">Real-time application health and error tracking</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={refreshData} disabled={isRefreshing} variant="outline">
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button onClick={exportErrors} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Health Score</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthScore}%</div>
            <Progress value={healthScore} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-2">
              {healthScore >= 90 ? 'Excellent' : healthScore >= 70 ? 'Good' : healthScore >= 50 ? 'Fair' : 'Poor'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{healthMetrics.totalErrors}</div>
            <p className="text-xs text-muted-foreground">
              {criticalErrors} critical, {highErrors} high priority
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Uptime</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatUptime(healthMetrics.uptime)}</div>
            <p className="text-xs text-muted-foreground">
              Since {new Date(Date.now() - healthMetrics.uptime * 1000).toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Endpoints</CardTitle>
            <Server className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {endpoints.filter(e => e.healthy).length}/{endpoints.length}
            </div>
            <p className="text-xs text-muted-foreground">
              {endpoints.filter(e => e.healthy).length} healthy endpoints
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Critical Alerts */}
      {criticalErrors > 0 && (
        <Alert variant="destructive">
          <XCircle className="h-4 w-4" />
          <AlertTitle>Critical Errors Detected</AlertTitle>
          <AlertDescription>
            {criticalErrors} critical error{criticalErrors > 1 ? 's' : ''} require immediate attention.
            <Button variant="link" className="p-0 h-auto ml-2" onClick={() => setSelectedError(errors.find(e => e.severity === ErrorSeverity.CRITICAL) || null)}>
              View Details
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="errors" className="space-y-4">
        <TabsList>
          <TabsTrigger value="errors">Error Log</TabsTrigger>
          <TabsTrigger value="endpoints">Endpoints</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="errors" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Recent Errors</h3>
            <Button onClick={clearAllErrors} variant="outline" size="sm">
              Clear All
            </Button>
          </div>
          
          <div className="space-y-2">
            {errors.length === 0 ? (
              <Card>
                <CardContent className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-2" />
                    <p className="text-lg font-semibold">No Errors</p>
                    <p className="text-muted-foreground">System is running smoothly</p>
                  </div>
                </CardContent>
              </Card>
            ) : (
              errors.map((error) => (
                <Card key={error.id} className="cursor-pointer hover:bg-muted/50" onClick={() => setSelectedError(error)}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {getSeverityIcon(error.severity)}
                        <div>
                          <p className="font-medium">{error.message}</p>
                          <p className="text-sm text-muted-foreground">
                            {error.type} • {error.timestamp.toLocaleString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={getSeverityColor(error.severity)}>
                          {error.severity}
                        </Badge>
                        <Eye className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>

        <TabsContent value="endpoints" className="space-y-4">
          <h3 className="text-lg font-semibold">Endpoint Health</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {endpoints.map((endpoint) => (
              <Card key={endpoint.url}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-base">{endpoint.name}</CardTitle>
                    <div className="flex items-center space-x-2">
                      {endpoint.healthy ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-500" />
                      )}
                      <Badge variant={endpoint.healthy ? 'default' : 'destructive'}>
                        {endpoint.healthy ? 'Healthy' : 'Down'}
                      </Badge>
                    </div>
                  </div>
                  <CardDescription>{endpoint.url}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Response Time:</span>
                      <span className={endpoint.responseTime > 1000 ? 'text-red-500' : endpoint.responseTime > 500 ? 'text-yellow-500' : 'text-green-500'}>
                        {endpoint.responseTime.toFixed(0)}ms
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Last Checked:</span>
                      <span>{endpoint.lastChecked.toLocaleTimeString()}</span>
                    </div>
                    {endpoint.error && (
                      <div className="text-sm text-red-500">
                        Error: {endpoint.error}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <h3 className="text-lg font-semibold">Error Analytics</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Errors by Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(healthMetrics.errorsByType).map(([type, count]) => (
                    <div key={type} className="flex justify-between items-center">
                      <span className="capitalize">{type}</span>
                      <Badge variant="outline">{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Errors by Severity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {Object.entries(healthMetrics.errorsBySeverity).map(([severity, count]) => (
                    <div key={severity} className="flex justify-between items-center">
                      <span className="capitalize">{severity}</span>
                      <Badge variant={getSeverityColor(severity as ErrorSeverity)}>{count}</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Error Detail Modal */}
      {selectedError && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="max-w-2xl w-full max-h-[80vh] overflow-auto">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Error Details</CardTitle>
                <Button variant="ghost" size="sm" onClick={() => setSelectedError(null)}>
                  ×
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">ID</label>
                  <p className="text-sm text-muted-foreground">{selectedError.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Type</label>
                  <p className="text-sm text-muted-foreground capitalize">{selectedError.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Severity</label>
                  <Badge variant={getSeverityColor(selectedError.severity)}>{selectedError.severity}</Badge>
                </div>
                <div>
                  <label className="text-sm font-medium">Timestamp</label>
                  <p className="text-sm text-muted-foreground">{selectedError.timestamp.toLocaleString()}</p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium">Message</label>
                <p className="text-sm text-muted-foreground mt-1">{selectedError.message}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium">URL</label>
                <p className="text-sm text-muted-foreground mt-1 break-all">{selectedError.url}</p>
              </div>
              
              {selectedError.stack && (
                <div>
                  <label className="text-sm font-medium">Stack Trace</label>
                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-40">
                    {selectedError.stack}
                  </pre>
                </div>
              )}
              
              {selectedError.metadata && (
                <div>
                  <label className="text-sm font-medium">Metadata</label>
                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto max-h-40">
                    {JSON.stringify(selectedError.metadata, null, 2)}
                  </pre>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

export default MonitoringDashboard;