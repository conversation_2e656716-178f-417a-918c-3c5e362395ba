---
description: (MASTER WORKFLOW) Orchestrates the complete bootstrapping of a new ESTRATIX client project, from initial onboarding and RFP to a ready-to-develop project structure.
---

# MASTER WORKFLOW: Bootstrap Client Project

**Workflow ID**: `WF-CLIENTBOOT-01`

## 1. Overview

This master workflow orchestrates the end-to-end onboarding of a new client project. It ensures a seamless transition from initial requirements gathering, through formal proposal and approval, to the creation of a fully structured, architecturally compliant project ready for development.

The process is designed to be executed by a high-level `COO_AXXX_ClientOnboardingAgent` and leverages several other workflows to guarantee consistency, traceability, and adherence to ESTRATIX standards.

## 2. Prerequisites

- Initial contact has been made with a potential or existing client.
- The agent executing this workflow has the necessary permissions to create directories and update matrix files.

---

## 3. Phase 1: Initiation & Briefing

**Objective**: To capture all necessary client and project requirements in a single, unified document.

1. **Create Client-Specific Directory (If New)**
   - **Action**: If the project is for a new client, create a dedicated directory for them at `docs/clients/[Client_Name_Snake_Case]/`.
   - **Tool**: `run_command`
   - **Command**:

     ```markdown
     <!-- run_command(
         CommandLine='if (-not (Test-Path docs/clients/[Client_Name_Snake_Case])) { mkdir docs/clients/[Client_Name_Snake_Case] }'
     ) -->
     ```

2. **Complete the Unified Client & Project Brief**
   - **Action**: The `AGENT_Client_Relationship_Manager` and the client collaboratively complete the unified brief.
   - **Template**: `docs/templates/client_management/Unified_Client_And_Project_Brief_Template.md`
   - **Output**: A completed and signed `Unified_Client_And_Project_Brief.md` stored in `docs/clients/[Client_Name_Snake_Case]/`.

---

## 4. Phase 2: Proposal & Approval

**Objective**: To translate the unified brief into a formal, scoped, and costed proposal for approval.

1. **Generate Proposal**
   - **Action**: An `AGENT_Brief_Processor` parses the brief to generate a formal project proposal.
   - **Workflow Trigger**: `/proposal_definition`
   - **Input**: The approved `Unified_Client_And_Project_Brief.md`.
   - **Output**: An approved and signed proposal document, stored in `docs/proposals/`.

---

## 5. Phase 3: Project Definition & Documentation Scaffolding

**Objective**: To formalize the project and scaffold its documentation and management structure.

1. **Define and Register the Client Master Project**
   - **Action**: Execute the client project definition workflow to create the charter, register it in the `project_matrix.md`, and establish the project's documentation home.
   - **Workflow Trigger**: `/client_project_definition`
   - **Inputs**: The approved `[Proposal_ID]` and `[Client_Name_Snake_Case]`.
   - **Output**: A new project directory at `docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName]/` and a new entry in `docs/matrices/project_matrix.md`.

2. **Create Management Directories**
   - **Action**: Create the standard project management phase directories inside the new project's documentation path.
   - **Tool**: `run_command`
   - **Command**:

     ```markdown
     <!-- run_command(
         Cwd='docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName]/',
         CommandLine='mkdir 00_ProjectInitiation 01_ProjectPlanning 02_ProjectExecution 03_ProjectMonitoringControlling 04_ProjectClosure'
     ) -->
     ```

3. **Populate with Key Templates**
   - **Action**: Copy the essential project management templates into the corresponding phase folders.
   - **Tool**: `run_command`
   - **Commands**:

     ```markdown
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Project_Charter_Template.md -Destination docs\clients\[Client_Name_Snake_Case]\projects\[Project_ID]_[ProjectName]\00_ProjectInitiation\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Project_Plan_Template.md -Destination docs\clients\[Client_Name_Snake_Case]\projects\[Project_ID]_[ProjectName]\01_ProjectPlanning\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Requirements_Specification_Template.md -Destination docs\clients\[Client_Name_Snake_Case]\projects\[Project_ID]_[ProjectName]\01_ProjectPlanning\') -->
     ```

---

## 6. Phase 4: Implementation Scaffolding

**Objective**: To generate the baseline, architecturally-compliant source code structure in the correct monorepo location.

1. **Generate the Client Project Code Structure**
   - **Action**: Execute the client project generation workflow to create the standard DDD/Hexagonal folder structure.
   - **Workflow Trigger**: `/client_project_generation`
   - **Inputs**: `[ClientMasterProjectName]` from Phase 3.
   - **Architectural Rule**: The root directory for the client's code **must** be `clients/[ClientMasterProjectName]/`.
   - **Output**: A baseline code repository structure at `clients/[ClientMasterProjectName]/` containing standard folders like `domain/`, `application/`, `infrastructure/`, and `tests/`.

---

## 7. Phase 5: Verification

**Objective**: To confirm the successful and complete bootstrapping of the client project.

1. **Final Review**
   - **Action**: Verify that all artifacts have been created correctly.
   - **Checklist**:
     - Client root documentation directory: `docs/clients/[Client_Name_Snake_Case]`
     - Client's master project folder: `docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName]`
     - Project registration in `docs/matrices/project_matrix.md`.
     - Source code directory: `clients/[ClientMasterProjectName]`
     - All source documents (Brief, Proposal, Definition) are correctly located.