---
description: Guides the definition of a new ESTRATIX Data Model conceptually, producing a detailed markdown document and updating the data model matrix.
---

# Workflow: Define ESTRATIX Data Model (Conceptual)

This workflow outlines the steps to define a new ESTRATIX Data Model conceptually. The output is a markdown document detailing the model's structure, attributes, relationships, and purpose, which will serve as the specification for its Pydantic implementation.

## Responsible Command Office

CTO, CPO, or relevant CO for the component type.

## Prerequisites

* Understanding of the ESTRATIX component (e.g., Service, Flow, Process, Project, Agent) for which the data model is being defined.
* Familiarity with basic data modeling concepts (attributes, types, relationships).
* Access to `docs/templates/estratix_data_model_definition_template.md`.
* Access to `docs/data_models/data_model_matrix.md`.

## Steps

1. **Initiate Data Model Definition**
   * **Action:** Identify the need for a new data model or a new version of an existing one.
   * **Determine:**
     * **Component Type Represented:** (e.g., Service, Flow, Process, Task, Agent, Project, Rule, Standard, etc.)
     * **Proposed Model Name (PascalCase):** This will be the Pydantic class name (e.g., `UserServiceProfile`, `OrderDetails`).
     * **Brief Purpose:** Why is this model needed?

2. **Assign Model ID**
   * **Action:** Generate a unique Model ID.
   * **Format:** `DM_[COMP]_[NAME_PASCALCASE]`
     * `DM`: Prefix for Data Model.
     * `[COMP]`: Short acronym for the component type (e.g., `SVC`, `FLW`, `PRC`, `AGT`, `PRJ`, `USR`).
     * `[NAME_PASCALCASE]`: The PascalCase name of the model (e.g., `UserServiceProfile`, `OrderDetails`).
   * **Example:** `DM_USR_UserServiceProfile`

3. **Create Draft Definition from Template**
   * **Action:** Copy `docs/templates/estratix_data_model_definition_template.md` to a new file.
   * **New File Path:** `docs/data_models/[component_type_lowercase]/[ModelNamePascalCase]_Definition.md`
     * `[component_type_lowercase]`: e.g., `service`, `flow`, `user`
     * `[ModelNamePascalCase]`: e.g., `UserServiceProfile`
   * **Example Path:** `docs/data_models/user/UserServiceProfile_Definition.md`
   * **Tool:** `view_file` (for template), `write_to_file` (for new definition file).

4. **Complete Data Model Definition Document**
   * **Action:** Edit the newly created draft definition file.
   * **Populate all sections** of the template (`[ModelName]`, Overview, Purpose, Attributes, Relationships, Enums, Nested Models, Business Rules, etc.).
   * **Be explicit** about data types (Pydantic/Python types), constraints, and example values.
   * **Clearly define relationships** to other existing or planned data models.
   * **Tool:** `edit_file`.

5. **Review and Refine Definition**
   * **Action:** Review the completed definition for clarity, completeness, and consistency with ESTRATIX standards.
   * **Involve relevant stakeholders** (e.g., developers who will implement it, COs).
   * **Iterate** on the definition as needed.

6. **Update Data Model Matrix**
   * **Action:** Add a new row or update an existing one in `docs/data_models/data_model_matrix.md` for the newly defined data model.
   * **Ensure all columns are accurately filled**, including the Model ID, Model Name, Component Type, Status (set to "Definition"), Version (e.g., 1.0 for new, increment for updates), and the link to the conceptual definition file created in Step 3.
   * **Leave the "Pydantic Implementation Link" blank** for now, or point to a planned path.
   * **Tool:** `edit_file`.

7. **Commit and Document**
   * **Action:** Commit the new/updated definition file and the updated matrix to version control.
   * **Announce** the new data model definition to relevant teams/COs.

## Outputs

* A new markdown file: `docs/data_models/[component_type_lowercase]/[ModelNamePascalCase]_Definition.md`.
* An updated `docs/data_models/data_model_matrix.md`.

## Next Steps (Post-Conceptual Definition)

* Proceed to Pydantic model implementation (potentially guided by another workflow like `/1_component_lifecycle/2_generate/data_model_generation.md`).
* Update the "Pydantic Implementation Link" in the `data_model_matrix.md` once the Python file is created.
* Use the defined data model in relevant ESTRATIX components (services, flows, agents).