# ESTRATIX LLM Provider Strategy

---

## 1. Overview

This document outlines the ESTRATIX strategy for leveraging Large Language Models (LLMs) across the framework. Our approach prioritizes flexibility, cost-effectiveness, and performance by using a unified access layer that can connect to a variety of model providers.

---

## 2. Core Strategy: Unified Access via OpenRouter

To avoid vendor lock-in and to maintain maximum flexibility, ESTRATIX uses [OpenRouter](https://openrouter.ai/) as a centralized API gateway for accessing a wide range of LLMs. This approach is facilitated by the `LiteLLM` library, which provides a consistent interface for interacting with different models.

### Key Advantages

- **Provider Agnostic**: Easily switch between different LLM providers (e.g., OpenAI, Anthropic, Google, Cohere) without changing application code.
- **Access to Diverse Models**: Leverages a vast catalog of models, including open-source, fine-tuned, and proprietary options.
- **Cost Optimization**: Enables the selection of the most cost-effective model for a given task.
- **Simplified Configuration**: Centralizes API key management to a single `OPENROUTER_API_KEY`.

---

## 3. Default LLM: Qwen3 Coder

For general-purpose agentic tasks, the default model for the ESTRATIX framework is **Qwen3 Coder**, accessed via OpenRouter.

- **Model Identifier**: `qwen/qwen3-coder:free`

### Rationale

- **Performance**: Qwen3 Coder offers a strong balance of reasoning, instruction-following, and language generation capabilities suitable for our core tasks.
- **Cost-Effectiveness**: As a free, rate-limited model available through OpenRouter, it provides a powerful, no-cost option for development, testing, and many production workloads.
- **Scalability**: For more demanding tasks, we can easily switch to more powerful paid models through the same OpenRouter endpoint by simply changing the model identifier string in the configuration.

---

## 4. Implementation

All agentic components requiring an LLM should be configured to use the `ChatOpenAI` class (from `langchain_openai`) with the `base_url` pointed to OpenRouter's API endpoint and the `api_key` set to the `OPENROUTER_API_KEY`.

See the `.env.example` file for the correct environment variable setup.
