import Fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import multipart from '@fastify/multipart';
import { config } from './config/environment';
import { logger } from './utils/logger';
import { authenticateToken } from './middleware/auth';
import { web3Service } from './services/web3Service';
import contractRoutes from './routes/contracts';
import stakingRoutes from './routes/staking';
import tokenRoutes from './routes/tokens';
import propertyRoutes from './routes/properties';
import governanceRoutes from './routes/governance';
import defiRoutes from './routes/defi';
import healthRoutes from './routes/health';

const fastify = Fastify({
  logger: logger,
  trustProxy: true,
  bodyLimit: 10485760, // 10MB
});

// Register plugins
async function registerPlugins() {
  // Security
  await fastify.register(helmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  });

  await fastify.register(cors, {
    origin: config.CORS_ORIGINS,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  });

  // JWT
  await fastify.register(jwt, {
    secret: config.JWT_SECRET,
    sign: {
      expiresIn: '24h',
    },
  });

  // Multipart for file uploads
  await fastify.register(multipart, {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
    },
  });

  // Swagger documentation
  await fastify.register(swagger, {
    swagger: {
      info: {
        title: 'ESTRATIX Smart Contracts API',
        description: 'Smart Contracts microservice for ESTRATIX platform - handles Web3 interactions, property tokenization, DeFi protocols, staking, and governance',
        version: '1.0.0',
      },
      host: `localhost:${config.PORT}`,
      schemes: ['http', 'https'],
      consumes: ['application/json'],
      produces: ['application/json'],
      securityDefinitions: {
        Bearer: {
          type: 'apiKey',
          name: 'Authorization',
          in: 'header',
          description: 'Enter JWT token in format: Bearer <token>',
        },
      },
    },
  });

  await fastify.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'full',
      deepLinking: false,
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
  });
}

// Register middleware
async function registerMiddleware() {
  // Auth middleware for protected routes
  fastify.decorate('authenticate', authenticateToken);

  // Request logging
  fastify.addHook('onRequest', async (request, reply) => {
    request.log.info({
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
    }, 'Incoming request');
  });

  // Response logging
  fastify.addHook('onResponse', async (request, reply) => {
    request.log.info({
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      responseTime: reply.getResponseTime(),
    }, 'Request completed');
  });
}

// Register routes
async function registerRoutes() {
  // Health check routes
  await fastify.register(healthRoutes, { prefix: '/health' });

  // API routes
  await fastify.register(contractRoutes, { prefix: '/api/v1/contracts' });
  await fastify.register(stakingRoutes, { prefix: '/api/v1/staking' });
  await fastify.register(tokenRoutes, { prefix: '/api/v1/tokens' });
  await fastify.register(propertyRoutes, { prefix: '/api/v1/properties' });
  await fastify.register(governanceRoutes, { prefix: '/api/v1/governance' });
  await fastify.register(defiRoutes, { prefix: '/api/v1/defi' });
}

// Initialize Web3 service
async function initializeServices() {
  try {
    await web3Service.initialize();
    fastify.log.info('Web3 service initialized successfully');
  } catch (error) {
    fastify.log.error('Failed to initialize Web3 service:', error);
    throw error;
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  fastify.log.info('Received SIGINT, shutting down gracefully');
  try {
    await fastify.close();
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  fastify.log.info('Received SIGTERM, shutting down gracefully');
  try {
    await fastify.close();
    process.exit(0);
  } catch (error) {
    fastify.log.error('Error during shutdown:', error);
    process.exit(1);
  }
});

// Unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  fastify.log.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Uncaught exceptions
process.on('uncaughtException', (error) => {
  fastify.log.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start server
async function start() {
  try {
    // Register all plugins, middleware, and routes
    await registerPlugins();
    await registerMiddleware();
    await registerRoutes();
    
    // Initialize services
    await initializeServices();

    // Start the server
    const address = await fastify.listen({
      port: config.PORT,
      host: config.HOST,
    });

    fastify.log.info(`🚀 Smart Contracts service is running at ${address}`);
    fastify.log.info(`📚 API Documentation available at ${address}/docs`);
    fastify.log.info(`🔗 Environment: ${config.NODE_ENV}`);
    fastify.log.info(`🌐 Network: ${config.BLOCKCHAIN_NETWORK}`);
    
  } catch (error) {
    fastify.log.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Export fastify instance for testing
export { fastify };

// Start the server if this file is run directly
if (require.main === module) {
  start();
}