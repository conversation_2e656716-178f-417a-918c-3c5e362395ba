import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { web3Service } from '../services/web3Service';
import { authenticateToken } from '../middleware/auth';

// Staking schemas
const stakeSchema = {
  body: {
    type: 'object',
    required: ['poolId', 'amount'],
    properties: {
      poolId: { type: 'string' },
      amount: { type: 'string' }
    }
  }
};

const unstakeSchema = {
  body: {
    type: 'object',
    required: ['stakeId'],
    properties: {
      stakeId: { type: 'string' }
    }
  }
};

const claimSchema = {
  body: {
    type: 'object',
    required: ['stakeId'],
    properties: {
      stakeId: { type: 'string' }
    }
  }
};

async function stakingRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Stake tokens
  fastify.post('/stake', {
    schema: stakeSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { poolId, amount } = request.body;
      const txHash = await web3Service.stakeTokens(BigInt(poolId), amount);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to stake tokens' });
    }
  });

  // Unstake tokens
  fastify.post('/unstake', {
    schema: unstakeSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { stakeId } = request.body;
      const txHash = await web3Service.unstakeTokens(BigInt(stakeId));
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to unstake tokens' });
    }
  });

  // Claim rewards
  fastify.post('/claim-rewards', {
    schema: claimSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { stakeId } = request.body;
      const txHash = await web3Service.claimStakingRewards(BigInt(stakeId));
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to claim rewards' });
    }
  });

  // Get staking info
  fastify.get('/info/:stakeId', async (request: any, reply: any) => {
    try {
      const { stakeId } = request.params;
      const info = await web3Service.getStakeInfo(BigInt(stakeId));
      reply.send({ success: true, info });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get staking info' });
    }
  });

  // Get user stakes
  fastify.get('/user/:address', async (request: any, reply: any) => {
    try {
      const { address } = request.params;
      const stakes = await web3Service.getUserStakes(address);
      reply.send({ success: true, stakes });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get user stakes' });
    }
  });

  // Get staking statistics
  fastify.get('/stats', async (request: any, reply: any) => {
    try {
      const stats = await web3Service.getStakingStats();
      reply.send({ success: true, stats });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get staking stats' });
    }
  });
}

export default fp(stakingRoutes);