# f005: Knowledge Base Monitoring & Maintenance Flow

## 1. Metadata

* **Flow ID:** `f005`
* **Flow Name:** Knowledge Base Monitoring & Maintenance Flow
* **Version:** 1.0
* **Status:** Defined
* **Last Updated:** 2025-07-08
* **Owner/SME:** CIO
* **Responsible Command Office:** CIO
* **SOP References:** [To be defined]
* **SLOs/SLAs:** [To be defined]

## 2. Purpose & Goal

* **Purpose:** To ensure the ESTRATIX knowledge base remains accurate, relevant, and up-to-date by systematically monitoring sources for changes and triggering re-ingestion or archival processes as needed.
* **Goal(s):** To detect and flag 95% of significant changes in monitored knowledge sources within 24 hours of their occurrence.

## 3. Scope

* **In Scope:** Regularly scheduled checks of sources listed in `source_matrix.md`, comparing current content against a stored hash or timestamp, identifying content drift, and updating the matrix to trigger re-ingestion or flag for archival.
* **Out of Scope:** The ingestion process itself (handled by `f004`), manual analysis of content drift significance, and the archival mechanism.

## 4. Triggers

* **Scheduled Trigger:** This flow is executed on a predefined schedule (e.g., daily at 02:00 UTC).

## 5. Inputs

* The `source_matrix.md` file, specifically entries with a status of `Completed` and a `monitoring_enabled` flag set to `true`.

## 6. Outputs

* The `source_matrix.md` is updated. If a change is detected, the status of the corresponding entry is changed to `Pending Re-ingestion`.
* A log entry is created in `knowledge_maintenance_log_matrix.md` detailing the check, the outcome, and any actions taken.

## 7. Constituent Work Units & Sequence

This flow orchestrates the `p002` (Knowledge Source Monitoring) process:

1. **Source Identification:** Identify all sources to be monitored from `source_matrix.md`.
2. **Content Hashing:** For each source, fetch the content and compute a hash (`a007`: Content Hashing Agent).
3. **Hash Comparison:** Compare the new hash with the previously stored hash (`a008`: Content Drift Detection Agent).
4. **Matrix Update:** If hashes differ, update the source's status in `source_matrix.md` to `Pending Re-ingestion` (`a009`: Matrix Update Agent).

## 7.1. Implementation Checklist / Acceptance Criteria

* [ ] **Criterion for `p002`:** The `p002` process must execute end-to-end for all monitored sources.
* [ ] **Criterion for Hashing:** The content hashing agent correctly generates a consistent hash for unchanged content.
* [ ] **Criterion for Drift Detection:** The drift detection agent correctly identifies mismatches between old and new hashes.
* [ ] **Overall Flow Criterion 1:** The flow correctly updates the `source_matrix.md` when changes are detected.
* [ ] **Overall Flow Criterion 2:** The flow correctly logs all checks in `knowledge_maintenance_log_matrix.md`.

## 8. Key Roles & Agents

* **Orchestrator:** The Knowledge Base Monitoring Flow itself (implemented as a CrewAI crew).
* **Specialist Agents:**
  * `a007`: Content Hashing Agent
  * `a008`: Content Drift Detection Agent
  * `a009`: Matrix Update Agent

## 9. Tools & MCPs Utilized

* CrewAI Framework
* A scheduling service (e.g., cron, AWS EventBridge)
* Hashing library (e.g., hashlib in Python)

## 10. Success Metrics & KPIs

* **Detection Rate:** Percentage of actual content changes that are successfully detected by the flow.
* **False Positive Rate:** Percentage of times a change is flagged when no significant change has occurred.
* **Processing Time:** Total time taken to check all monitored sources.

## 11. Dependencies

* **Upstream Flows/Processes:** `f004` (Documentation Ingestion Flow), as it provides the initial set of ingested and hashed content.
* **Downstream Flows/Processes:** `f004` is also a downstream process, as it is triggered by the status change set by this flow.

## 12. Exception Handling & Escalation

* **Common Issues:** Source URL is unavailable, content is protected or has changed format, matrix file is locked.
* **Handling:** Errors are logged in `knowledge_maintenance_log_matrix.md`. The flow will retry a failed source check up to 3 times before flagging it as `Monitoring Failed` in the `source_matrix.md` and notifying the CIO.
* **Logging Strategy:** All checks, outcomes, and errors are logged to the central observability platform under `FlowExecutionLogs`, filterable by `FlowID: f005`.

## 13. PDCA (Plan-Do-Check-Act) / Continuous Improvement

* **Review Cadence:** Quarterly
* **Responsible for Review:** CIO
* **Key Metrics for Review:** Detection Rate, False Positive Rate.
* **Process for Incorporating Improvements:** Refinements to the hashing or comparison logic can be proposed and implemented to reduce false positives or improve detection accuracy.

### 13.1. Lessons Learned & Iterations

* *Initial Version 1.0:* To be developed.

## 14. Agentic Framework Mapping

* **Windsurf Workflows:** This definition can be used as input for the `/flow_generation` workflow.
* **CrewAI Conceptual Mapping:** The flow is implemented as a CrewAI `Crew`. The `p002` process maps to a series of `Tasks` assigned to the specialist `Agents`.
