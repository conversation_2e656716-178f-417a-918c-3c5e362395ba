{"common": {"welcome": "<PERSON><PERSON><PERSON>", "home": "Home", "about": "Chi Siamo", "services": "<PERSON><PERSON><PERSON>", "contact": "<PERSON><PERSON><PERSON>", "login": "Accedi", "logout": "<PERSON><PERSON><PERSON>", "register": "Registrati", "submit": "Invia", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "edit": "Modifica", "delete": "Elimina", "view": "Visualizza", "back": "Indietro", "next": "<PERSON><PERSON>", "previous": "Precedente", "close": "<PERSON><PERSON>", "open": "<PERSON>i", "yes": "Sì", "no": "No", "ok": "OK", "confirm": "Conferma", "loading": "Caricamento...", "search": "Cerca", "filter": "Filtra", "sort": "Ordina", "date": "Data", "time": "<PERSON>a", "name": "Nome", "email": "Email", "phone": "Telefono", "address": "<PERSON><PERSON><PERSON><PERSON>", "city": "Città", "country": "<PERSON><PERSON>", "price": "Prezzo", "currency": "Valuta", "language": "<PERSON><PERSON>", "settings": "Impostazioni", "profile": "<PERSON>ilo", "dashboard": "Dashboard", "notifications": "Notifiche", "help": "<PERSON><PERSON>", "support": "Supporto", "faq": "FAQ", "terms": "Termini di Servizio", "privacy": "Privacy Policy", "cookies": "<PERSON><PERSON>"}, "navigation": {"main_menu": "<PERSON><PERSON>", "properties": "Proprietà", "luxury_homes": "Case di Lusso", "commercial": "Commerciale", "investments": "Investimenti", "rentals": "<PERSON><PERSON>itt<PERSON>", "web3": "Web3", "nft_marketplace": "Marketplace NFT", "tokenization": "Tokenizzazione", "defi": "<PERSON><PERSON><PERSON>", "portfolio": "Portfolio", "wallet": "Portafoglio", "transactions": "Transazioni", "analytics": "Analytics", "reports": "Report", "tools": "Strumenti", "calculator": "Calcolatore", "market_analysis": "Analisi di Mercato", "valuation": "Valutazione", "legal": "Legale", "documentation": "Documentazione", "contracts": "<PERSON><PERSON><PERSON>", "compliance": "Conformità"}, "home": {"hero": {"title": "<PERSON><PERSON> Ridefinito nell'Era Digitale", "subtitle": "Scopri proprietà esclusive e investimenti immobiliari tokenizzati con la potenza della blockchain", "cta_primary": "Esplora Proprietà", "cta_secondary": "Scopri Web3"}, "stats": {"properties_sold": "Proprietà Vendute", "total_value": "<PERSON><PERSON>", "satisfied_clients": "<PERSON><PERSON><PERSON>", "countries": "<PERSON><PERSON>"}, "services": {"title": "I Nostri <PERSON>", "subtitle": "Soluzioni complete per il mercato immobiliare di lusso", "luxury_sales": {"title": "<PERSON>end<PERSON> di Lusso", "description": "Proprietà esclusive curate per clienti esigenti"}, "investment_advisory": {"title": "Consulenza Investimenti", "description": "Strategie di investimento personalizzate per massimizzare i rendimenti"}, "property_management": {"title": "Gestione Proprietà", "description": "Servizi completi di gestione per i tuoi asset immobiliari"}, "legal_services": {"title": "Servizi Legali", "description": "Supporto legale esperto per tutte le transazioni immobiliari"}}, "web3_features": {"title": "Innovazione Web3", "subtitle": "Il futuro degli investimenti immobiliari", "tokenization": {"title": "Tokenizzazione Proprietà", "description": "Trasforma le proprietà in asset digitali negoziabili"}, "nft_ownership": {"title": "Proprietà NFT", "description": "Certificati di proprietà digitali sicuri e verificabili"}, "defi_lending": {"title": "Prestiti DeFi", "description": "Accedi a liquidità utilizzando i tuoi asset immobiliari"}, "smart_contracts": {"title": "Smart Contract", "description": "Transazioni automatizzate e trasparenti"}}, "token_dashboard": {"title": "Dashboard Token", "subtitle": "Gestisci i tuoi investimenti immobiliari tokenizzati", "total_tokens": "Token Totali", "portfolio_value": "<PERSON><PERSON>", "monthly_yield": "Rendimento Mensile", "active_properties": "Proprietà Attive"}, "cta": {"ready_to_start": "Pronto per Iniziare?", "join_revolution": "Unisciti alla rivoluzione degli investimenti immobiliari digitali", "get_started": "Inizia Ora", "learn_more": "<PERSON><PERSON><PERSON> di <PERSON>ù"}}, "payment": {"methods": {"title": "Metodi di Pagamento", "web2": {"title": "Pagamenti Tradizionali", "credit_card": "Carta di Credito", "bank_transfer": "Bonifico Bancario", "paypal": "PayPal", "stripe": "Stripe", "wire_transfer": "Trasferimento Bancario"}, "web3": {"title": "Pagamenti Crypto", "bitcoin": "Bitcoin (BTC)", "ethereum": "Ethereum (ETH)", "usdc": "USD Coin (USDC)", "usdt": "Tether (USDT)", "bnb": "Binance Coin (BNB)", "polygon": "Polygon (MATIC)", "avalanche": "Avalanche (AVAX)", "solana": "Solana (SOL)"}}, "processing": {"title": "Elaborazione Pagamento", "please_wait": "Attendere prego...", "confirming": "Confermando transazione", "success": "Pagamento completato con successo", "failed": "Pagamento fallito", "cancelled": "<PERSON><PERSON><PERSON> annullato"}}, "accessibility": {"high_contrast": "Alto Contrasto", "font_size": "Dimensione Font", "increase_font": "Aumenta Font", "decrease_font": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "screen_reader": "<PERSON><PERSON>", "keyboard_navigation": "Navigazione Tastiera", "skip_to_content": "Salta al Contenuto", "skip_to_navigation": "Salta alla Navigazione", "aria_labels": {"main_navigation": "Navigazione principale", "search_form": "Modulo di ricerca", "user_menu": "Menu utente", "breadcrumb": "Breadcrumb", "pagination": "Paginazione", "sort_options": "Opzioni di ordinamento", "filter_options": "Opzioni di filtro"}}, "ui": {"loading": "Caricamento...", "error": "Errore", "success": "Successo", "warning": "Avviso", "info": "Informazione", "retry": "<PERSON><PERSON><PERSON><PERSON>", "refresh": "Aggiorna", "clear": "Can<PERSON><PERSON>", "reset": "Reimposta", "apply": "Applica", "dismiss": "Ignora", "expand": "Espandi", "collapse": "Comprimi", "show_more": "Mostra di Più", "show_less": "<PERSON><PERSON>", "select_all": "<PERSON><PERSON><PERSON><PERSON>", "deselect_all": "Desele<PERSON><PERSON>", "no_results": "<PERSON><PERSON><PERSON> ris<PERSON>ato trovato", "empty_state": "<PERSON><PERSON><PERSON> dato disponibile", "coming_soon": "Prossimamente", "under_construction": "In Costruzione", "maintenance": "Manutenzione", "offline": "Offline", "online": "Online", "connected": "<PERSON><PERSON><PERSON>", "disconnected": "Disconnesso", "syncing": "Sincronizzazione...", "synced": "Sincronizzato", "draft": "<PERSON><PERSON>", "published": "Pubblicato", "archived": "Archiviato", "active": "Attivo", "inactive": "Inattivo", "enabled": "Abilitato", "disabled": "Disabilitato", "public": "Pubblico", "private": "Privato", "featured": "In Evidenza", "new": "Nuovo", "updated": "Aggiornato", "popular": "Popolare", "trending": "Tendenza", "recommended": "<PERSON><PERSON><PERSON><PERSON>", "favorite": "<PERSON><PERSON><PERSON>", "bookmark": "Se<PERSON><PERSON><PERSON>", "share": "Condi<PERSON><PERSON>", "copy": "Copia", "paste": "<PERSON><PERSON><PERSON>", "cut": "Taglia", "undo": "<PERSON><PERSON><PERSON>", "redo": "R<PERSON><PERSON>", "print": "Stampa", "download": "Scarica", "upload": "Carica", "import": "Importa", "export": "Esporta", "sync": "Sincronizza", "backup": "Backup", "restore": "R<PERSON><PERSON><PERSON>", "preview": "Anteprima", "fullscreen": "<PERSON><PERSON>mo <PERSON>", "minimize": "<PERSON><PERSON><PERSON>", "maximize": "Massimizza", "pin": "<PERSON><PERSON>", "unpin": "<PERSON><PERSON><PERSON><PERSON>", "lock": "Blocca", "unlock": "S<PERSON><PERSON>ca", "hide": "Nascondi", "show": "Mostra", "toggle": "Attiva/Disattiva"}, "forms": {"validation": {"required": "Questo campo è obbligatorio", "email_invalid": "Inserisci un indirizzo email valido", "password_too_short": "La password deve essere di almeno 8 caratteri", "passwords_dont_match": "Le password non corrispondono", "phone_invalid": "Inserisci un numero di telefono valido", "url_invalid": "Inserisci un URL valido", "number_invalid": "Inserisci un numero valido", "date_invalid": "Inserisci una data valida", "file_too_large": "Il file è troppo grande", "file_type_invalid": "Tipo di file non supportato", "min_length": "Deve essere di almeno {min} caratteri", "max_length": "Non può superare {max} caratteri", "min_value": "Il valore deve essere almeno {min}", "max_value": "Il valore non può superare {max}"}, "placeholders": {"enter_email": "Inserisci la tua email", "enter_password": "Inserisci la tua password", "confirm_password": "Conferma la password", "enter_name": "Inser<PERSON>ci il tuo nome", "enter_phone": "Inserisci il tuo numero di telefono", "enter_address": "Inserisci il tuo indirizzo", "enter_message": "Inser<PERSON>ci il tuo messaggio", "search_properties": "Cerca proprietà...", "search_location": "Cerca per località...", "select_option": "Seleziona un'opzione", "choose_file": "Scegli file", "enter_amount": "Inserisci importo", "enter_wallet_address": "Inserisci indirizzo wallet"}}, "navigation_ui": {"breadcrumb": {"home": "Home", "separator": "/"}, "pagination": {"previous": "Precedente", "next": "Successivo", "first": "Primo", "last": "Ultimo", "page": "<PERSON><PERSON><PERSON>", "of": "di", "results": "risultati", "showing": "Mostrando", "to": "a", "items_per_page": "Elementi per pagina"}, "search": {"placeholder": "Cerca...", "no_results": "<PERSON><PERSON><PERSON> risultato trovato per '{query}'", "results_count": "{count} risultati trovati", "clear_search": "Cancella ricerca", "search_suggestions": "Suggerimenti di ricerca", "recent_searches": "Ricerche recenti"}, "filters": {"title": "<PERSON><PERSON><PERSON>", "apply_filters": "Applica Filtri", "clear_filters": "<PERSON><PERSON><PERSON>", "active_filters": "<PERSON><PERSON><PERSON>", "no_filters": "<PERSON><PERSON>un filtro applicato", "price_range": "Fascia di Prezzo", "location": "Località", "property_type": "Tipo di Proprietà", "bedrooms": "<PERSON><PERSON>", "bathrooms": "<PERSON><PERSON>", "area": "Area", "year_built": "Anno di Costruzione", "amenities": "<PERSON><PERSON><PERSON>"}, "sorting": {"sort_by": "Ordina per", "price_low_high": "Prezzo: Basso a Alto", "price_high_low": "Prezzo: Alto a Basso", "date_newest": "Data: <PERSON><PERSON>", "date_oldest": "Data: <PERSON><PERSON>", "name_a_z": "Nome: A-Z", "name_z_a": "Nome: Z-A", "relevance": "<PERSON><PERSON><PERSON><PERSON>", "popularity": "Popolarità", "rating": "Valutazione"}}, "media": {"images": {"alt_property": "Imma<PERSON>e della proprietà", "alt_gallery": "Immagine della galleria", "alt_thumbnail": "Miniatura", "alt_avatar": "Avatar utente", "alt_logo": "Logo", "loading": "Caricamento immagine...", "error": "Errore nel caricamento dell'immagine", "no_image": "Nessuna immagine disponibile"}, "videos": {"play": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pause": "Pausa", "stop": "Ferma", "mute": "Silenzia", "unmute": "Riattiva Audio", "fullscreen": "<PERSON><PERSON>mo <PERSON>", "loading": "Caricamento video...", "error": "Errore nel caricamento del video"}, "files": {"download": "Scarica", "view": "Visualizza", "size": "Dimensione", "type": "Tipo", "modified": "Modificato", "uploaded": "Caricato"}}, "interactive": {"buttons": {"click_here": "<PERSON><PERSON><PERSON>", "learn_more": "<PERSON><PERSON><PERSON> di <PERSON>ù", "get_started": "Inizia", "try_now": "<PERSON><PERSON>", "sign_up": "Registrati", "sign_in": "Accedi", "contact_us": "Con<PERSON><PERSON><PERSON>", "request_info": "<PERSON>di Informazioni", "schedule_tour": "Prenota Visita", "make_offer": "Fai un'Offerta", "add_to_favorites": "Aggiungi ai Preferiti", "remove_from_favorites": "Rimuovi dai Preferiti", "share_property": "Condividi Proprietà", "calculate_mortgage": "Calcola <PERSON>", "view_details": "Visualizza Dettagli", "book_viewing": "Prenota Visione"}, "links": {"external_link": "Link esterno", "opens_new_window": "Si apre in una nuova finestra", "download_link": "Link di download", "email_link": "Link email", "phone_link": "Link telefono"}, "modals": {"close": "<PERSON><PERSON>", "confirm_action": "Conferma Azione", "are_you_sure": "Sei sicuro?", "this_action_cannot_be_undone": "Questa azione non può essere annullata", "continue": "Continua", "cancel": "<PERSON><PERSON><PERSON>"}}, "errors": {"generic": {"something_went_wrong": "Qualcosa è andato storto", "unexpected_error": "Errore inaspettato", "please_try_again": "<PERSON><PERSON><PERSON><PERSON>", "contact_support": "Contatta il supporto", "error_code": "<PERSON><PERSON> errore", "error_message": "Messaggio di errore", "error_details": "<PERSON><PERSON><PERSON> errore"}, "network": {"connection_failed": "Connessione fallita", "timeout": "Timeout della richiesta", "offline": "Sei offline", "slow_connection": "Connessione lenta", "server_unreachable": "Server non raggiungibile", "dns_error": "Errore DNS", "ssl_error": "Errore SSL", "proxy_error": "Errore proxy", "network_unavailable": "Rete non disponibile", "connection_reset": "Connessione resettata"}, "authentication": {"login_failed": "Login fallito", "invalid_credentials": "Credenziali non valide", "session_expired": "Sessione scaduta", "unauthorized": "Non autorizzato", "forbidden": "Accesso negato", "account_locked": "Account bloc<PERSON>o", "account_suspended": "Account sospeso", "account_not_verified": "Account non verificato", "password_expired": "Password scaduta", "too_many_attempts": "<PERSON><PERSON><PERSON> tentativi", "invalid_token": "Token non valido", "token_expired": "<PERSON><PERSON> scaduto", "refresh_token_invalid": "Token di aggiornamento non valido", "mfa_required": "Autenticazione a due fattori richiesta", "mfa_failed": "Autenticazione a due fattori fallita"}, "validation": {"required_field": "Campo obbligatorio", "invalid_format": "Formato non valido", "invalid_email": "Email non valida", "invalid_phone": "Numero di telefono non valido", "invalid_url": "URL non valido", "invalid_date": "Data non valida", "invalid_number": "Numero non valido", "value_too_short": "<PERSON>ore troppo corto", "value_too_long": "<PERSON><PERSON> troppo lungo", "value_too_small": "<PERSON>ore troppo piccolo", "value_too_large": "Valore troppo grande", "invalid_characters": "Caratteri non validi", "passwords_dont_match": "Le password non corrispondono", "weak_password": "Password troppo debole", "duplicate_value": "<PERSON><PERSON> dup<PERSON>"}, "file": {"file_not_found": "File non trovato", "file_too_large": "File troppo grande", "invalid_file_type": "Tipo di file non valido", "upload_failed": "Caricamento fallito", "download_failed": "Download fallito", "file_corrupted": "File corrotto", "insufficient_storage": "Spazio di archiviazione insufficiente", "file_exists": "Il file esiste già", "permission_denied": "<PERSON><PERSON><PERSON> negato", "quota_exceeded": "Quota superata"}, "database": {"connection_failed": "Connessione al database fallita", "query_failed": "Query fallita", "transaction_failed": "Transazione fallita", "constraint_violation": "Violazione del vincolo", "duplicate_entry": "Voce duplicata", "foreign_key_violation": "Violazione chiave esterna", "data_integrity_error": "Errore integrità dati", "deadlock_detected": "Deadlock rilevato", "timeout": "Timeout database", "maintenance_mode": "Database in manutenzione"}, "cache": {"cache_miss": "<PERSON><PERSON> miss", "cache_expired": "<PERSON><PERSON> scaduta", "cache_full": "<PERSON><PERSON> piena", "cache_error": "Errore cache", "redis_connection_failed": "Connessione Redis fallita", "memcached_error": "<PERSON>rro<PERSON>", "cache_invalidation_failed": "Invalidazione cache fallita", "cache_write_failed": "Scrittura cache fallita", "cache_read_failed": "Lettura cache fallita", "cache_delete_failed": "Eliminazione cache fallita"}, "api": {"invalid_request": "Richiesta non valida", "missing_parameters": "<PERSON><PERSON><PERSON>", "invalid_parameters": "Parametri non validi", "rate_limit_exceeded": "Limite di velocità superato", "quota_exceeded": "Quota superata", "service_unavailable": "Servizio non disponibile", "bad_gateway": "Bad gateway", "gateway_timeout": "Timeout gateway", "internal_server_error": "Errore interno del server", "not_implemented": "Non implementato", "version_not_supported": "Versione non supportata", "deprecated_endpoint": "Endpoint deprecato", "maintenance_mode": "Modalità manutenzione", "circuit_breaker_open": "Circuit breaker aperto", "upstream_error": "Errore upstream"}, "blockchain": {"transaction_failed": "Transazione fallita", "insufficient_funds": "Fondi insufficienti", "gas_limit_exceeded": "Limite gas superato", "nonce_too_low": "<PERSON><PERSON> troppo basso", "nonce_too_high": "<PERSON><PERSON> troppo alto", "invalid_signature": "Firma non valida", "contract_execution_failed": "Esecuzione contratto fallita", "network_congestion": "Congestione di rete", "node_unavailable": "Nodo non disponibile", "chain_reorganization": "Riorganizzazione catena", "block_not_found": "Blocco non trovato", "transaction_not_found": "Transazione non trovata", "wallet_locked": "Wallet bloccato", "wallet_not_connected": "Wallet non connesso", "unsupported_network": "Rete non supportata"}, "ai": {"model_unavailable": "Modello non disponibile", "inference_failed": "Inferenza fallita", "training_failed": "Addestramento fallito", "data_preprocessing_failed": "Preprocessing dati fallito", "model_loading_failed": "Caricamento modello fallito", "gpu_memory_exceeded": "Memoria GPU superata", "computation_timeout": "Timeout computazione", "invalid_input_format": "Formato input non valido", "model_version_mismatch": "Versione modello non corrispondente", "feature_extraction_failed": "Estrazione feature fallita"}, "payment": {"payment_failed": "Pagamento fallito", "card_declined": "<PERSON>ta rifiutata", "insufficient_funds": "Fondi insufficienti", "expired_card": "Carta scaduta", "invalid_card": "Carta non valida", "payment_timeout": "Timeout pagamento", "gateway_error": "Errore gateway", "fraud_detected": "<PERSON>ode rilevata", "currency_not_supported": "Valuta non supportata", "amount_too_small": "Importo troppo piccolo", "amount_too_large": "Importo troppo grande", "merchant_account_error": "Errore account commerciante", "refund_failed": "<PERSON><PERSON><PERSON><PERSON> fall<PERSON>", "chargeback_received": "Chargeback ricevuto", "subscription_failed": "Abbonamento fallito"}, "email": {"send_failed": "Invio email fallito", "invalid_recipient": "Destinatario non valido", "bounce_detected": "<PERSON><PERSON><PERSON> r<PERSON><PERSON>o", "spam_detected": "<PERSON><PERSON> rilevato", "quota_exceeded": "Quota email superata", "template_not_found": "Template non trovato", "attachment_too_large": "Allegato troppo grande", "smtp_error": "Errore SMTP", "authentication_failed": "Autenticazione fallita", "rate_limit_exceeded": "Limite velocità superato", "mailgun_failed": "Mailgun fallito", "sendgrid_failed": "<PERSON><PERSON><PERSON> fall<PERSON>", "ses_failed": "SES fallito", "postmark_failed": "Postmark fallito", "mandrill_failed": "Mandrill fallito"}, "sms": {"send_failed": "Invio SMS fallito", "invalid_number": "Numero non valido", "carrier_blocked": "Operatore bloccato", "message_too_long": "Messaggio troppo lungo", "quota_exceeded": "Quota SMS superata", "rate_limit_exceeded": "Limite velocità superato", "twilio_failed": "<PERSON><PERSON><PERSON>", "nexmo_failed": "Nexmo fallito", "plivo_failed": "Plivo fallito", "messagebird_failed": "MessageBird fallito"}, "storage": {"upload_failed": "Caricamento fallito", "download_failed": "Download fallito", "delete_failed": "Eliminazione fallita", "access_denied": "Accesso negato", "quota_exceeded": "Quota superata", "file_not_found": "File non trovato", "bucket_not_found": "Bucket non trovato", "invalid_key": "Chiave non valida", "checksum_mismatch": "Checksum non corrispondente", "encryption_failed": "Crittografia fallita", "s3_error": "Errore S3", "gcs_error": "Errore GCS", "azure_blob_error": "Errore Azure Blob", "cloudinary_error": "Errore Cloudinary", "cdn_error": "Errore CDN"}, "search": {"index_not_found": "Indice non trovato", "query_failed": "Query fallita", "indexing_failed": "Indicizzazione fallita", "search_timeout": "Timeout ricerca", "too_many_results": "<PERSON><PERSON><PERSON> r<PERSON>", "invalid_query": "Query non valida", "elasticsearch_error": "Errore Elasticsearch", "solr_error": "Errore Solr", "algolia_error": "Errore Algolia", "sphinx_error": "Errore Sphinx"}, "analytics": {"tracking_failed": "Tracciamento fallito", "event_not_recorded": "Evento non registrato", "invalid_event": "Evento non valido", "quota_exceeded": "Quota superata", "google_analytics_error": "Errore Google Analytics", "mixpanel_error": "Errore Mixpanel", "amplitude_error": "Errore Amplitude", "segment_error": "Errore Segment", "hotjar_error": "Errore <PERSON>", "fullstory_error": "Errore FullStory"}, "monitoring": {"alert_failed": "Avviso fallito", "metric_collection_failed": "Raccolta metrica fallita", "log_ingestion_failed": "Ingestione log fallita", "dashboard_error": "Errore dashboard", "threshold_exceeded": "Soglia superata", "service_degraded": "<PERSON><PERSON><PERSON>", "datadog_error": "Errore Datadog", "newrelic_error": "Errore New Relic", "sentry_error": "Errore <PERSON>", "rollbar_error": "Errore <PERSON>", "bugsnag_error": "Errore <PERSON>", "honeybadger_error": "Errore Honeybadger"}, "integration": {"webhook_failed": "Webhook fallito", "api_key_invalid": "Chiave API non valida", "oauth_failed": "<PERSON><PERSON><PERSON> fall<PERSON>", "sync_failed": "Sincronizzazione fallita", "import_failed": "Importazione fallita", "export_failed": "Esportazione fallita", "transformation_failed": "Trasformazione fallita", "mapping_error": "Errore mappatura", "schema_validation_failed": "Validazione schema fallita", "rate_limit_exceeded": "Limite velocità superato", "salesforce_error": "Errore Salesforce", "hubspot_error": "Errore HubSpot", "zapier_error": "<PERSON><PERSON><PERSON>", "slack_error": "<PERSON><PERSON><PERSON>", "discord_error": "Errore Discord", "teams_error": "Errore Teams"}, "enterprise": {"license_expired": "Licenza scaduta", "license_invalid": "Licenza non valida", "feature_not_available": "Funzionalità non disponibile", "user_limit_exceeded": "Limite utenti superato", "storage_limit_exceeded": "Limite storage superato", "bandwidth_limit_exceeded": "Limite banda superato", "compliance_violation": "Violazione conformità", "audit_failed": "<PERSON>t fallito", "backup_failed": "Backup fallito", "restore_failed": "<PERSON><PERSON><PERSON><PERSON>", "migration_failed": "Migrazione fallita", "provisioning_failed": "Provisioning fallito", "deprovisioning_failed": "Deprovisioning fallito", "sso_failed": "SSO fallito", "ldap_error": "Errore LDAP", "active_directory_error": "Errore Active Directory", "saml_error": "Errore SAML", "okta_error": "<PERSON><PERSON><PERSON>", "auth0_error": "Errore Auth0", "azure_ad_error": "Errore Azure AD"}}}