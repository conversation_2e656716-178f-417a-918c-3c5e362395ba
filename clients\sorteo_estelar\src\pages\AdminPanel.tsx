import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  BarChart3, Users, DollarSign, Trophy, Settings,
  Plus, Edit, Trash2, Eye, Search, Filter,
  TrendingUp, TrendingDown, AlertTriangle, CheckCircle,
  Calendar, Clock, Globe, Shield, Zap, Target,
  Download, Upload, RefreshCw, Bell, Mail,
  User, CreditCard, Coins, Award, Activity
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { useUserStore, useAppStore } from '../store';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalDraws: number;
  activeDraws: number;
  totalPrizes: number;
  pendingPayouts: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  status: 'active' | 'suspended' | 'pending';
  joinDate: string;
  totalSpent: number;
  lastActivity: string;
  country: string;
  verified: boolean;
}

interface Draw {
  id: string;
  name: string;
  type: '2-digit' | '3-digit' | '4-digit' | 'special';
  status: 'active' | 'completed' | 'cancelled' | 'scheduled';
  startDate: string;
  endDate: string;
  ticketPrice: number;
  totalTickets: number;
  soldTickets: number;
  prizePool: number;
  participants: number;
}

interface Transaction {
  id: string;
  userId: string;
  userName: string;
  type: 'deposit' | 'withdrawal' | 'ticket_purchase' | 'prize_payout';
  amount: number;
  currency: string;
  status: 'completed' | 'pending' | 'failed';
  timestamp: string;
  method: string;
}

const AdminPanel: React.FC = () => {
  const { t } = useTranslation();
  const { user, isAuthenticated } = useUserStore();
  const [activeTab, setActiveTab] = useState<'dashboard' | 'users' | 'draws' | 'transactions' | 'settings'>('dashboard');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  // Check if user has admin privileges
  if (!isAuthenticated || !user || user.role !== 'admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card variant="glass">
          <div className="text-center space-y-4 p-8">
            <Shield className="w-16 h-16 text-red-400 mx-auto" />
            <h2 className="text-2xl font-bold text-white">Acceso Denegado</h2>
            <p className="text-white/80">No tienes permisos para acceder al panel de administración.</p>
            <Button variant="primary" onClick={() => window.history.back()}>
              Volver
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  const stats: DashboardStats = {
    totalUsers: 52847,
    activeUsers: 8934,
    totalRevenue: 2847593,
    monthlyRevenue: 284759,
    totalDraws: 1247,
    activeDraws: 23,
    totalPrizes: 1847293,
    pendingPayouts: 45892
  };

  const mockUsers: User[] = [
    {
      id: '1',
      name: 'Carlos Mendoza',
      email: '<EMAIL>',
      status: 'active',
      joinDate: '2024-01-15',
      totalSpent: 2450,
      lastActivity: '2024-12-15 14:30',
      country: 'México',
      verified: true
    },
    {
      id: '2',
      name: 'Ana García',
      email: '<EMAIL>',
      status: 'active',
      joinDate: '2024-02-20',
      totalSpent: 1890,
      lastActivity: '2024-12-15 12:15',
      country: 'España',
      verified: true
    },
    {
      id: '3',
      name: 'Roberto Silva',
      email: '<EMAIL>',
      status: 'suspended',
      joinDate: '2024-03-10',
      totalSpent: 890,
      lastActivity: '2024-12-10 09:45',
      country: 'Argentina',
      verified: false
    }
  ];

  const mockDraws: Draw[] = [
    {
      id: '1',
      name: 'Sorteo Estelar Semanal',
      type: '3-digit',
      status: 'active',
      startDate: '2024-12-10',
      endDate: '2024-12-17',
      ticketPrice: 5,
      totalTickets: 10000,
      soldTickets: 7834,
      prizePool: 39170,
      participants: 3421
    },
    {
      id: '2',
      name: 'Mega Sorteo Navideño',
      type: 'special',
      status: 'scheduled',
      startDate: '2024-12-20',
      endDate: '2024-12-25',
      ticketPrice: 25,
      totalTickets: 5000,
      soldTickets: 0,
      prizePool: 125000,
      participants: 0
    },
    {
      id: '3',
      name: 'Sorteo Diario Express',
      type: '2-digit',
      status: 'completed',
      startDate: '2024-12-14',
      endDate: '2024-12-14',
      ticketPrice: 2,
      totalTickets: 1000,
      soldTickets: 1000,
      prizePool: 2000,
      participants: 456
    }
  ];

  const mockTransactions: Transaction[] = [
    {
      id: '1',
      userId: '1',
      userName: 'Carlos Mendoza',
      type: 'ticket_purchase',
      amount: 25,
      currency: 'USD',
      status: 'completed',
      timestamp: '2024-12-15 14:30:00',
      method: 'Credit Card'
    },
    {
      id: '2',
      userId: '2',
      userName: 'Ana García',
      type: 'deposit',
      amount: 100,
      currency: 'EUR',
      status: 'completed',
      timestamp: '2024-12-15 12:15:00',
      method: 'Bank Transfer'
    },
    {
      id: '3',
      userId: '3',
      userName: 'Roberto Silva',
      type: 'prize_payout',
      amount: 500,
      currency: 'USD',
      status: 'pending',
      timestamp: '2024-12-15 10:45:00',
      method: 'Crypto Wallet'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'text-green-400 bg-green-500/20';
      case 'pending':
      case 'scheduled':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'suspended':
      case 'cancelled':
      case 'failed':
        return 'text-red-400 bg-red-500/20';
      default:
        return 'text-white/70 bg-white/10';
    }
  };

  const StatCard: React.FC<{ title: string; value: string | number; change?: number; icon: React.ReactNode; color: string }> = ({ title, value, change, icon, color }) => (
    <Card variant="glass">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-white/70 text-sm">{title}</p>
          <p className="text-2xl font-bold text-white">{typeof value === 'number' ? value.toLocaleString() : value}</p>
          {change !== undefined && (
            <div className={`flex items-center space-x-1 text-sm ${
              change >= 0 ? 'text-green-400' : 'text-red-400'
            }`}>
              {change >= 0 ? <TrendingUp className="w-4 h-4" /> : <TrendingDown className="w-4 h-4" />}
              <span>{Math.abs(change)}%</span>
            </div>
          )}
        </div>
        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${color}`}>
          {icon}
        </div>
      </div>
    </Card>
  );

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Usuarios"
          value={stats.totalUsers}
          change={12.5}
          icon={<Users className="w-6 h-6" />}
          color="bg-blue-500/20 text-blue-400"
        />
        <StatCard
          title="Ingresos Totales"
          value={`$${stats.totalRevenue.toLocaleString()}`}
          change={8.3}
          icon={<DollarSign className="w-6 h-6" />}
          color="bg-green-500/20 text-green-400"
        />
        <StatCard
          title="Sorteos Activos"
          value={stats.activeDraws}
          change={-2.1}
          icon={<Target className="w-6 h-6" />}
          color="bg-purple-500/20 text-purple-400"
        />
        <StatCard
          title="Premios Pendientes"
          value={`$${stats.pendingPayouts.toLocaleString()}`}
          icon={<Trophy className="w-6 h-6" />}
          color="bg-amber-500/20 text-amber-400"
        />
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card variant="glass">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-xl font-semibold text-white">Ingresos Mensuales</h3>
            <Button variant="outline" size="sm">
              <Download className="w-4 h-4 mr-2" />
              Exportar
            </Button>
          </div>
          <div className="h-64 flex items-center justify-center text-white/50">
            <BarChart3 className="w-16 h-16" />
            <span className="ml-4">Gráfico de ingresos aquí</span>
          </div>
        </Card>

        <Card variant="glass">
          <h3 className="text-xl font-semibold text-white mb-4">Actividad Reciente</h3>
          <div className="space-y-3">
            {mockTransactions.slice(0, 5).map(transaction => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b border-white/10 last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    transaction.type === 'deposit' ? 'bg-green-500/20 text-green-400' :
                    transaction.type === 'withdrawal' ? 'bg-red-500/20 text-red-400' :
                    transaction.type === 'ticket_purchase' ? 'bg-blue-500/20 text-blue-400' :
                    'bg-amber-500/20 text-amber-400'
                  }`}>
                    {transaction.type === 'deposit' ? <TrendingUp className="w-4 h-4" /> :
                     transaction.type === 'withdrawal' ? <TrendingDown className="w-4 h-4" /> :
                     transaction.type === 'ticket_purchase' ? <CreditCard className="w-4 h-4" /> :
                     <Trophy className="w-4 h-4" />}
                  </div>
                  <div>
                    <p className="text-white font-medium">{transaction.userName}</p>
                    <p className="text-white/70 text-sm">{transaction.type.replace('_', ' ')}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-white font-medium">${transaction.amount}</p>
                  <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(transaction.status)}`}>
                    {transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card variant="glass">
        <h3 className="text-xl font-semibold text-white mb-4">Acciones Rápidas</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button variant="primary" className="flex flex-col items-center space-y-2 h-20">
            <Plus className="w-6 h-6" />
            <span>Nuevo Sorteo</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Users className="w-6 h-6" />
            <span>Gestionar Usuarios</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Download className="w-6 h-6" />
            <span>Exportar Datos</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Settings className="w-6 h-6" />
            <span>Configuración</span>
          </Button>
        </div>
      </Card>
    </div>
  );

  const renderUsers = () => (
    <div className="space-y-6">
      {/* Search and Filters */}
      <Card variant="glass">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar usuarios..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            <option value="all">Todos los estados</option>
            <option value="active">Activos</option>
            <option value="suspended">Suspendidos</option>
            <option value="pending">Pendientes</option>
          </select>
          <Button variant="primary">
            <Plus className="w-4 h-4 mr-2" />
            Nuevo Usuario
          </Button>
        </div>
      </Card>

      {/* Users Table */}
      <Card variant="glass">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/20">
                <th className="text-left text-white font-medium py-3">Usuario</th>
                <th className="text-left text-white font-medium py-3">Estado</th>
                <th className="text-left text-white font-medium py-3">Fecha de Registro</th>
                <th className="text-left text-white font-medium py-3">Total Gastado</th>
                <th className="text-left text-white font-medium py-3">Última Actividad</th>
                <th className="text-left text-white font-medium py-3">Acciones</th>
              </tr>
            </thead>
            <tbody>
              {mockUsers.map(user => (
                <tr key={user.id} className="border-b border-white/10 hover:bg-white/5">
                  <td className="py-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <User className="w-5 h-5 text-purple-400" />
                      </div>
                      <div>
                        <p className="text-white font-medium">{user.name}</p>
                        <p className="text-white/70 text-sm">{user.email}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className="text-white/50 text-xs">{user.country}</span>
                          {user.verified && <CheckCircle className="w-3 h-3 text-green-400" />}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(user.status)}`}>
                      {user.status}
                    </span>
                  </td>
                  <td className="py-4 text-white/80">{user.joinDate}</td>
                  <td className="py-4 text-white/80">${user.totalSpent.toLocaleString()}</td>
                  <td className="py-4 text-white/80">{user.lastActivity}</td>
                  <td className="py-4">
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const renderDraws = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Gestión de Sorteos</h2>
        <Button variant="primary">
          <Plus className="w-4 h-4 mr-2" />
          Crear Sorteo
        </Button>
      </div>

      {/* Draws Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {mockDraws.map(draw => (
          <Card key={draw.id} variant="glass">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-white font-semibold">{draw.name}</h3>
                <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(draw.status)}`}>
                  {draw.status}
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Tipo:</span>
                  <span className="text-white">{draw.type}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Precio por ticket:</span>
                  <span className="text-white">${draw.ticketPrice}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Tickets vendidos:</span>
                  <span className="text-white">{draw.soldTickets}/{draw.totalTickets}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Premio acumulado:</span>
                  <span className="text-white">${draw.prizePool.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Participantes:</span>
                  <span className="text-white">{draw.participants}</span>
                </div>
              </div>
              
              <div className="w-full bg-white/10 rounded-full h-2">
                <div 
                  className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(draw.soldTickets / draw.totalTickets) * 100}%` }}
                ></div>
              </div>
              
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Eye className="w-4 h-4 mr-2" />
                  Ver
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Edit className="w-4 h-4 mr-2" />
                  Editar
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderTransactions = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Transacciones</h2>
        <Button variant="primary">
          <Download className="w-4 h-4 mr-2" />
          Exportar
        </Button>
      </div>

      {/* Transactions Table */}
      <Card variant="glass">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/20">
                <th className="text-left text-white font-medium py-3">ID</th>
                <th className="text-left text-white font-medium py-3">Usuario</th>
                <th className="text-left text-white font-medium py-3">Tipo</th>
                <th className="text-left text-white font-medium py-3">Cantidad</th>
                <th className="text-left text-white font-medium py-3">Estado</th>
                <th className="text-left text-white font-medium py-3">Fecha</th>
                <th className="text-left text-white font-medium py-3">Método</th>
              </tr>
            </thead>
            <tbody>
              {mockTransactions.map(transaction => (
                <tr key={transaction.id} className="border-b border-white/10 hover:bg-white/5">
                  <td className="py-4 text-white/80">#{transaction.id}</td>
                  <td className="py-4 text-white">{transaction.userName}</td>
                  <td className="py-4">
                    <span className="text-white/80">{transaction.type.replace('_', ' ')}</span>
                  </td>
                  <td className="py-4 text-white font-medium">
                    ${transaction.amount} {transaction.currency}
                  </td>
                  <td className="py-4">
                    <span className={`px-2 py-1 rounded-full text-xs ${getStatusColor(transaction.status)}`}>
                      {transaction.status}
                    </span>
                  </td>
                  <td className="py-4 text-white/80">{transaction.timestamp}</td>
                  <td className="py-4 text-white/80">{transaction.method}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );

  const renderSettings = () => (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Configuración del Sistema</h2>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card variant="glass">
          <h3 className="text-xl font-semibold text-white mb-4">Configuración General</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-white/80 text-sm mb-2">Nombre de la Plataforma</label>
              <input 
                type="text" 
                defaultValue="Sorteo Estelar"
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-white/80 text-sm mb-2">Comisión por Transacción (%)</label>
              <input 
                type="number" 
                defaultValue="2.5"
                className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <div>
              <label className="block text-white/80 text-sm mb-2">Moneda Base</label>
              <select className="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500">
                <option value="USD">USD</option>
                <option value="EUR">EUR</option>
                <option value="GBP">GBP</option>
              </select>
            </div>
          </div>
        </Card>

        <Card variant="glass">
          <h3 className="text-xl font-semibold text-white mb-4">Configuración de Seguridad</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white/80">Autenticación de dos factores obligatoria</span>
              <button className="w-12 h-6 bg-purple-500 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80">Verificación KYC obligatoria</span>
              <button className="w-12 h-6 bg-purple-500 rounded-full relative">
                <div className="w-5 h-5 bg-white rounded-full absolute right-0.5 top-0.5"></div>
              </button>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-white/80">Límite de retiro diario</span>
              <input 
                type="number" 
                defaultValue="10000"
                className="w-24 px-2 py-1 bg-white/10 border border-white/20 rounded text-white text-sm focus:outline-none focus:ring-1 focus:ring-purple-500"
              />
            </div>
          </div>
        </Card>
      </div>

      <Card variant="glass">
        <h3 className="text-xl font-semibold text-white mb-4">Acciones del Sistema</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <RefreshCw className="w-6 h-6" />
            <span>Reiniciar Sistema</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Download className="w-6 h-6" />
            <span>Backup DB</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Upload className="w-6 h-6" />
            <span>Restaurar DB</span>
          </Button>
          <Button variant="outline" className="flex flex-col items-center space-y-2 h-20">
            <Activity className="w-6 h-6" />
            <span>Logs del Sistema</span>
          </Button>
        </div>
      </Card>
    </div>
  );

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
    { id: 'users', label: 'Usuarios', icon: Users },
    { id: 'draws', label: 'Sorteos', icon: Target },
    { id: 'transactions', label: 'Transacciones', icon: CreditCard },
    { id: 'settings', label: 'Configuración', icon: Settings }
  ];

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Panel de <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Administración</span>
          </h1>
          <p className="text-white/80">Gestiona todos los aspectos de la plataforma Sorteo Estelar</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {tabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium hidden sm:block">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div>
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'users' && renderUsers()}
          {activeTab === 'draws' && renderDraws()}
          {activeTab === 'transactions' && renderTransactions()}
          {activeTab === 'settings' && renderSettings()}
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;