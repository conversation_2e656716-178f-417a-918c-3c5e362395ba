import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { web3Service } from '../services/web3Service';
import { authenticateToken } from '../middleware/auth';

// Property schemas
const tokenizePropertySchema = {
  body: {
    type: 'object',
    required: ['propertyId', 'totalValue', 'fractionSize', 'pricePerFraction', 'metadataURI', 'metadata'],
    properties: {
      propertyId: { type: 'string' },
      totalValue: { type: 'string' },
      fractionSize: { type: 'string' },
      pricePerFraction: { type: 'string' },
      metadataURI: { type: 'string' },
      metadata: {
        type: 'object',
        required: ['name', 'description', 'location', 'area', 'propertyType'],
        properties: {
          name: { type: 'string' },
          description: { type: 'string' },
          location: { type: 'string' },
          area: { type: 'string' },
          propertyType: { type: 'string' },
          images: { type: 'array', items: { type: 'string' } },
          documents: { type: 'array', items: { type: 'string' } }
        }
      }
    }
  }
};

const buySharesSchema = {
  body: {
    type: 'object',
    required: ['propertyId', 'shares'],
    properties: {
      propertyId: { type: 'string' },
      shares: { type: 'number' }
    }
  }
};

async function propertyRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Tokenize property (admin only)
  fastify.post('/tokenize', {
    schema: tokenizePropertySchema,
    preHandler: [authenticateToken, async (request: any, reply: any) => {
      if (request.user?.role !== 'admin') {
        return reply.code(403).send({ error: 'Admin access required' });
      }
    }]
  }, async (request: any, reply: any) => {
    try {
      const { propertyId, totalValue, fractionSize, pricePerFraction, metadataURI, metadata } = request.body;
      const params = {
        propertyId,
        totalValue: BigInt(totalValue),
        fractionSize: BigInt(fractionSize),
        pricePerFraction: BigInt(pricePerFraction),
        metadataURI,
        metadata: {
          ...metadata,
          area: BigInt(metadata.area)
        }
      };
      const txHash = await web3Service.tokenizeProperty(params);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to tokenize property' });
    }
  });

  // Buy property shares
  fastify.post('/buy-shares', {
    schema: buySharesSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { propertyId, shares } = request.body;
      const userAddress = request.user?.walletAddress;
      if (!userAddress) {
        return reply.code(400).send({ error: 'Wallet address required' });
      }
      const txHash = await web3Service.buyPropertyShares(userAddress, propertyId, shares);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to buy property shares' });
    }
  });

  // Get property details
  fastify.get('/:propertyId', async (request: any, reply: any) => {
    try {
      const { propertyId } = request.params;
      const property = await web3Service.getPropertyDetails(propertyId);
      reply.send({ success: true, property });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get property details' });
    }
  });

  // Get user property shares
  fastify.get('/shares/:address', async (request: any, reply: any) => {
    try {
      const { address } = request.params;
      const shares = await web3Service.getUserPropertyShares(address);
      reply.send({ success: true, shares });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get user property shares' });
    }
  });

  // Get all properties
  fastify.get('/', async (request: any, reply: any) => {
    try {
      const properties = await web3Service.getAllProperties();
      reply.send({ success: true, properties });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get properties' });
    }
  });
}

export default fp(propertyRoutes);