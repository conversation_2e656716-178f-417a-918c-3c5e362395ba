---
description: "Enforce a consistent naming convention for all vector database collections to ensure clarity and prevent conflicts."
globs: "[''**/*.py'']"
alwaysApply: true
---

# Vector DB Collection Naming Convention

## Context

- This rule applies to any code that creates or manages vector database collections (e.g., in Milvus, Qdrant).
- A standardized naming convention is crucial for managing multiple data sources, preventing naming collisions, and providing clarity on the purpose of each collection.

## Requirements

- All collection names **MUST** follow the format: `[project_id]_[data_source_type]_[content_descriptor]`.
- All parts of the name must be in `snake_case`.
- `project_id`: The ESTRATIX Project ID (e.g., `es_internal_001`, `client_acme_002`).
- `data_source_type`: The type of data source (e.g., `web`, `pdf`, `docs`, `code`).
- `content_descriptor`: A brief, clear description of the content (e.g., `crewai_docs`, `project_proposals`).

## Examples

<example>
A compliant collection name for ingesting CrewAI documentation for an internal project.

```python
# Correct collection name
collection_name = "es_internal_001_docs_crewai_main"
```
</example>

<example type="invalid">
A non-compliant collection name that lacks structure and clarity.

```python
# Incorrect: Does not follow the required format
collection_name = "test_collection1"
```
</example>

## Critical Rules

- **ALWAYS** use the `[project_id]_[data_source_type]_[content_descriptor]` format for collection names.
- **NEVER** use generic or non-descriptive names for vector collections.
