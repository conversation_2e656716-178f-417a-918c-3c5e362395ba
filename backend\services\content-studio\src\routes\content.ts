import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { logger } from '../utils/logger';

// Validation schemas
const createContentSchema = z.object({
  title: z.string().min(1).max(200),
  content: z.string().min(1),
  type: z.enum(['blog_post', 'social_media', 'email', 'ad_copy', 'video_script', 'podcast_script']),
  status: z.enum(['draft', 'review', 'approved', 'published']).default('draft'),
  tags: z.array(z.string()).optional(),
  metadata: z.object({
    seoTitle: z.string().optional(),
    seoDescription: z.string().optional(),
    keywords: z.array(z.string()).optional(),
    targetAudience: z.string().optional(),
    platform: z.string().optional(),
  }).optional(),
  scheduledAt: z.string().datetime().optional(),
});

const updateContentSchema = createContentSchema.partial();

const contentQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
  tags: z.string().optional(), // comma-separated
  sortBy: z.enum(['createdAt', 'updatedAt', 'title', 'status']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

export const contentRoutes: FastifyPluginAsync = async (fastify) => {
  // Get all content with filtering and pagination
  fastify.get('/', {
    schema: {
      description: 'Get all content with filtering and pagination',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      querystring: contentQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                content: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      content: { type: 'string' },
                      type: { type: 'string' },
                      status: { type: 'string' },
                      tags: { type: 'array', items: { type: 'string' } },
                      metadata: { type: 'object' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                      scheduledAt: { type: 'string' },
                    },
                  },
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    total: { type: 'number' },
                    pages: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const query = contentQuerySchema.parse(request.query);
      const userId = request.user?.id;

      // Mock data for now - replace with actual database queries
      const mockContent = [
        {
          id: '1',
          title: 'Sample Blog Post',
          content: 'This is a sample blog post content...',
          type: 'blog_post',
          status: 'published',
          tags: ['marketing', 'content'],
          metadata: {
            seoTitle: 'Sample Blog Post - SEO Title',
            seoDescription: 'This is a sample blog post for demonstration.',
            keywords: ['sample', 'blog', 'content'],
            targetAudience: 'marketers',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          title: 'Social Media Campaign',
          content: 'Engaging social media content for our latest campaign...',
          type: 'social_media',
          status: 'draft',
          tags: ['social', 'campaign'],
          metadata: {
            platform: 'instagram',
            targetAudience: 'millennials',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      // Apply filters
      let filteredContent = mockContent;
      if (query.type) {
        filteredContent = filteredContent.filter(item => item.type === query.type);
      }
      if (query.status) {
        filteredContent = filteredContent.filter(item => item.status === query.status);
      }
      if (query.search) {
        const searchLower = query.search.toLowerCase();
        filteredContent = filteredContent.filter(item => 
          item.title.toLowerCase().includes(searchLower) ||
          item.content.toLowerCase().includes(searchLower)
        );
      }

      // Pagination
      const total = filteredContent.length;
      const pages = Math.ceil(total / query.limit);
      const offset = (query.page - 1) * query.limit;
      const paginatedContent = filteredContent.slice(offset, offset + query.limit);

      return {
        success: true,
        data: {
          content: paginatedContent,
          pagination: {
            page: query.page,
            limit: query.limit,
            total,
            pages,
          },
        },
      };
    } catch (error) {
      logger.error('Error fetching content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch content',
      };
    }
  });

  // Get content by ID
  fastify.get('/:id', {
    schema: {
      description: 'Get content by ID',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock data - replace with actual database query
      const mockContent = {
        id,
        title: 'Sample Content',
        content: 'This is sample content...',
        type: 'blog_post',
        status: 'published',
        tags: ['sample'],
        metadata: {},
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: mockContent,
      };
    } catch (error) {
      logger.error('Error fetching content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch content',
      };
    }
  });

  // Create new content
  fastify.post('/', {
    schema: {
      description: 'Create new content',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      body: createContentSchema,
    },
  }, async (request, reply) => {
    try {
      const contentData = createContentSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock creation - replace with actual database insertion
      const newContent = {
        id: Date.now().toString(),
        ...contentData,
        userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Content created: ${newContent.id}`);

      reply.code(201);
      return {
        success: true,
        data: newContent,
      };
    } catch (error) {
      logger.error('Error creating content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to create content',
      };
    }
  });

  // Update content
  fastify.put('/:id', {
    schema: {
      description: 'Update content',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: updateContentSchema,
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = updateContentSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock update - replace with actual database update
      const updatedContent = {
        id,
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Content updated: ${id}`);

      return {
        success: true,
        data: updatedContent,
      };
    } catch (error) {
      logger.error('Error updating content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to update content',
      };
    }
  });

  // Delete content
  fastify.delete('/:id', {
    schema: {
      description: 'Delete content',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock deletion - replace with actual database deletion
      logger.info(`Content deleted: ${id}`);

      reply.code(204);
      return;
    } catch (error) {
      logger.error('Error deleting content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to delete content',
      };
    }
  });

  // Publish content
  fastify.post('/:id/publish', {
    schema: {
      description: 'Publish content',
      tags: ['Content Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock publishing - replace with actual publishing logic
      logger.info(`Content published: ${id}`);

      return {
        success: true,
        message: 'Content published successfully',
      };
    } catch (error) {
      logger.error('Error publishing content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to publish content',
      };
    }
  });
};