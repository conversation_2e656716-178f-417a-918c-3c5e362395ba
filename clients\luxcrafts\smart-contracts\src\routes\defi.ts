import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { authenticateToken, requireWallet } from '../middleware/auth';
import { web3Service } from '../services/web3Service';
import { contractAddresses } from '../config/environment';
import { logger } from '../utils/logger';

const lendSchema = {
  body: {
    type: 'object',
    required: ['amount'],
    properties: {
      amount: { type: 'string' },
      token: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
    }
  }
};

const borrowSchema = {
  body: {
    type: 'object',
    required: ['amount', 'collateralAmount'],
    properties: {
      amount: { type: 'string' },
      collateralAmount: { type: 'string' },
      token: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
      collateralToken: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
    }
  }
};

const repaySchema = {
  body: {
    type: 'object',
    required: ['amount'],
    properties: {
      amount: { type: 'string' },
      token: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
    }
  }
};

const liquiditySchema = {
  body: {
    type: 'object',
    required: ['tokenA', 'tokenB', 'amountA', 'amountB'],
    properties: {
      tokenA: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
      tokenB: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
      amountA: { type: 'string' },
      amountB: { type: 'string' },
      slippage: { type: 'number', minimum: 0, maximum: 100 }
    }
  }
};

const removeLiquiditySchema = {
  body: {
    type: 'object',
    required: ['tokenA', 'tokenB', 'liquidity'],
    properties: {
      tokenA: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
      tokenB: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' },
      liquidity: { type: 'string' },
      slippage: { type: 'number', minimum: 0, maximum: 100 }
    }
  }
};

const yieldFarmSchema = {
  body: {
    type: 'object',
    required: ['poolId', 'amount'],
    properties: {
      poolId: { type: 'string' },
      amount: { type: 'string' }
    }
  }
};

const addressParamSchema = {
  params: {
    type: 'object',
    required: ['address'],
    properties: {
      address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
    }
  }
};

async function defiRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Lending operations
  fastify.post('/lending/deposit', {
    schema: lendSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { amount, token } = request.body as { amount: string; token?: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.depositToLendingPool(
        userAddress,
        token || contractAddresses.LUX_TOKEN,
        amount
      );
      
      logger.info(`Lending deposit: ${amount} tokens by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        amount,
        token: token || contractAddresses.LUX_TOKEN
      };
    } catch (error) {
      logger.error('Lending deposit failed:', error);
      return reply.status(500).send({
        error: 'Failed to deposit to lending pool',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  fastify.post('/lending/withdraw', {
    schema: lendSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { amount, token } = request.body as { amount: string; token?: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.withdrawFromLendingPool(
        userAddress,
        token || contractAddresses.LUX_TOKEN,
        amount
      );
      
      logger.info(`Lending withdrawal: ${amount} tokens by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        amount,
        token: token || contractAddresses.LUX_TOKEN
      };
    } catch (error) {
      logger.error('Lending withdrawal failed:', error);
      return reply.status(500).send({
        error: 'Failed to withdraw from lending pool',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Borrowing operations
  fastify.post('/lending/borrow', {
    schema: borrowSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { amount, collateralAmount, token, collateralToken } = request.body as {
        amount: string;
        collateralAmount: string;
        token?: string;
        collateralToken?: string;
      };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.borrowFromLendingPool(
        userAddress,
        token || contractAddresses.LUX_TOKEN,
        amount,
        collateralToken || contractAddresses.LUX_TOKEN,
        collateralAmount
      );
      
      logger.info(`Borrow: ${amount} tokens with ${collateralAmount} collateral by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        amount,
        collateralAmount,
        token: token || contractAddresses.LUX_TOKEN,
        collateralToken: collateralToken || contractAddresses.LUX_TOKEN
      };
    } catch (error) {
      logger.error('Borrowing failed:', error);
      return reply.status(500).send({
        error: 'Failed to borrow from lending pool',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  fastify.post('/lending/repay', {
    schema: repaySchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { amount, token } = request.body as { amount: string; token?: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.repayToLendingPool(
        userAddress,
        token || contractAddresses.LUX_TOKEN,
        amount
      );
      
      logger.info(`Loan repayment: ${amount} tokens by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        amount,
        token: token || contractAddresses.LUX_TOKEN
      };
    } catch (error) {
      logger.error('Loan repayment failed:', error);
      return reply.status(500).send({
        error: 'Failed to repay loan',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Liquidity operations
  fastify.post('/liquidity/add', {
    schema: liquiditySchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { tokenA, tokenB, amountA, amountB, slippage = 1 } = request.body as {
        tokenA: string;
        tokenB: string;
        amountA: string;
        amountB: string;
        slippage?: number;
      };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.addLiquidity(
        userAddress,
        tokenA,
        tokenB,
        amountA,
        amountB,
        slippage
      );
      
      logger.info(`Liquidity added: ${amountA}/${amountB} by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        tokenA,
        tokenB,
        amountA,
        amountB,
        slippage
      };
    } catch (error) {
      logger.error('Add liquidity failed:', error);
      return reply.status(500).send({
        error: 'Failed to add liquidity',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  fastify.post('/liquidity/remove', {
    schema: removeLiquiditySchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { tokenA, tokenB, liquidity, slippage = 1 } = request.body as {
        tokenA: string;
        tokenB: string;
        liquidity: string;
        slippage?: number;
      };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.removeLiquidity(
        userAddress,
        tokenA,
        tokenB,
        liquidity,
        slippage
      );
      
      logger.info(`Liquidity removed: ${liquidity} LP tokens by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        tokenA,
        tokenB,
        liquidity,
        slippage
      };
    } catch (error) {
      logger.error('Remove liquidity failed:', error);
      return reply.status(500).send({
        error: 'Failed to remove liquidity',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Yield farming operations
  fastify.post('/yield-farming/stake', {
    schema: yieldFarmSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { poolId, amount } = request.body as { poolId: string; amount: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.stakeInYieldFarm(
        userAddress,
        poolId,
        amount
      );
      
      logger.info(`Yield farming stake: ${amount} in pool ${poolId} by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        poolId,
        amount
      };
    } catch (error) {
      logger.error('Yield farming stake failed:', error);
      return reply.status(500).send({
        error: 'Failed to stake in yield farm',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  fastify.post('/yield-farming/unstake', {
    schema: yieldFarmSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { poolId, amount } = request.body as { poolId: string; amount: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.unstakeFromYieldFarm(
        userAddress,
        poolId,
        amount
      );
      
      logger.info(`Yield farming unstake: ${amount} from pool ${poolId} by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        poolId,
        amount
      };
    } catch (error) {
      logger.error('Yield farming unstake failed:', error);
      return reply.status(500).send({
        error: 'Failed to unstake from yield farm',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  fastify.post('/yield-farming/harvest', {
    schema: {
      body: {
        type: 'object',
        required: ['poolId'],
        properties: {
          poolId: { type: 'string' }
        }
      }
    },
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { poolId } = request.body as { poolId: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.harvestYieldFarmRewards(
        userAddress,
        poolId
      );
      
      logger.info(`Yield farming harvest from pool ${poolId} by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        poolId
      };
    } catch (error) {
      logger.error('Yield farming harvest failed:', error);
      return reply.status(500).send({
        error: 'Failed to harvest yield farm rewards',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get user positions
  fastify.get('/positions/:address', {
    schema: addressParamSchema
  }, async (request, reply) => {
    try {
      const { address } = request.params as { address: string };
      
      const positions = await web3Service.getUserDeFiPositions(address);
      
      return { positions };
    } catch (error) {
      logger.error('Failed to get DeFi positions:', error);
      return reply.status(500).send({
        error: 'Failed to get DeFi positions',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get pool information
  fastify.get('/pools', async (request, reply) => {
    try {
      const pools = await web3Service.getAllDeFiPools();
      
      return { pools };
    } catch (error) {
      logger.error('Failed to get DeFi pools:', error);
      return reply.status(500).send({
        error: 'Failed to get DeFi pools',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get lending rates
  fastify.get('/lending/rates', async (request, reply) => {
    try {
      const rates = await web3Service.getLendingRates();
      
      return { rates };
    } catch (error) {
      logger.error('Failed to get lending rates:', error);
      return reply.status(500).send({
        error: 'Failed to get lending rates',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

export default fp(defiRoutes);