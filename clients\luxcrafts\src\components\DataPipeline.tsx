import React, { useState, useEffect } from 'react';
import {
  Database,
  Users,
  Phone,
  Mail,
  TrendingUp,
  Download,
  Upload,
  Filter,
  Search,
  Play,
  Pause,
  BarChart3,
  Target,
  DollarSign,
  Calendar,
  MapPin,
  Building,
  Coins
} from 'lucide-react';

// Interfaces
interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  investmentCapacity: number;
  interests: string[];
  source: 'cold_call' | 'referral' | 'website' | 'social_media' | 'event';
  status: 'new' | 'contacted' | 'qualified' | 'negotiating' | 'closed' | 'lost';
  lastContact: Date;
  notes: string;
  aiAgentInteractions: number;
  conversionProbability: number;
}

interface Property {
  id: string;
  address: string;
  type: 'residential' | 'commercial' | 'mixed_use' | 'land';
  status: 'acquisition_target' | 'under_contract' | 'owned' | 'disposition' | 'sold';
  value: number;
  acquisitionDate?: Date;
  dispositionTarget?: Date;
  roi: number;
  tokenized: boolean;
  tokenSupply?: number;
  investorCount?: number;
}

interface Pipeline {
  id: string;
  name: string;
  type: 'acquisition' | 'disposition' | 'investor_outreach';
  status: 'active' | 'paused' | 'completed';
  progress: number;
  totalRecords: number;
  processedRecords: number;
  successRate: number;
  aiAgentAssigned: boolean;
  lastRun: Date;
}

interface DataPipelineProps {
  type?: 'acquisition' | 'disposition' | 'full';
}

const DataPipeline: React.FC<DataPipelineProps> = ({ type = 'full' }) => {
  const [activeTab, setActiveTab] = useState('leads');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock data
  const [leads] = useState<Lead[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-0123',
      location: 'Miami, FL',
      investmentCapacity: 500000,
      interests: ['luxury_condos', 'commercial_real_estate'],
      source: 'cold_call',
      status: 'qualified',
      lastContact: new Date('2024-01-15'),
      notes: 'Interested in luxury properties, prefers Miami area',
      aiAgentInteractions: 3,
      conversionProbability: 75
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '******-0456',
      location: 'New York, NY',
      investmentCapacity: 2000000,
      interests: ['tokenized_assets', 'commercial_properties'],
      source: 'referral',
      status: 'negotiating',
      lastContact: new Date('2024-01-20'),
      notes: 'High-value investor, interested in tokenized real estate',
      aiAgentInteractions: 7,
      conversionProbability: 85
    },
    {
      id: '3',
      name: 'Michael Chen',
      email: '<EMAIL>',
      phone: '******-0789',
      location: 'Los Angeles, CA',
      investmentCapacity: 1500000,
      interests: ['residential_properties', 'fix_and_flip'],
      source: 'website',
      status: 'contacted',
      lastContact: new Date('2024-01-18'),
      notes: 'Experienced in fix and flip, looking for new opportunities',
      aiAgentInteractions: 2,
      conversionProbability: 60
    }
  ]);

  const [properties] = useState<Property[]>([
    {
      id: '1',
      address: '123 Ocean Drive, Miami Beach, FL',
      type: 'residential',
      status: 'owned',
      value: 2500000,
      acquisitionDate: new Date('2023-06-15'),
      roi: 12.5,
      tokenized: true,
      tokenSupply: 2500,
      investorCount: 45
    },
    {
      id: '2',
      address: '456 Broadway, New York, NY',
      type: 'commercial',
      status: 'disposition',
      value: 5000000,
      acquisitionDate: new Date('2022-03-10'),
      dispositionTarget: new Date('2024-06-30'),
      roi: 18.2,
      tokenized: true,
      tokenSupply: 5000,
      investorCount: 120
    },
    {
      id: '3',
      address: '789 Sunset Blvd, Los Angeles, CA',
      type: 'mixed_use',
      status: 'acquisition_target',
      value: 3200000,
      roi: 15.8,
      tokenized: false
    }
  ]);

  const [pipelines] = useState<Pipeline[]>([
    {
      id: '1',
      name: 'Miami Luxury Investor Outreach',
      type: 'investor_outreach',
      status: 'active',
      progress: 65,
      totalRecords: 1000,
      processedRecords: 650,
      successRate: 12.3,
      aiAgentAssigned: true,
      lastRun: new Date('2024-01-20')
    },
    {
      id: '2',
      name: 'Commercial Property Acquisition',
      type: 'acquisition',
      status: 'active',
      progress: 40,
      totalRecords: 500,
      processedRecords: 200,
      successRate: 8.5,
      aiAgentAssigned: true,
      lastRun: new Date('2024-01-19')
    },
    {
      id: '3',
      name: 'Tokenized Asset Disposition',
      type: 'disposition',
      status: 'paused',
      progress: 25,
      totalRecords: 300,
      processedRecords: 75,
      successRate: 15.2,
      aiAgentAssigned: false,
      lastRun: new Date('2024-01-15')
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'negotiating': return 'bg-purple-100 text-purple-800';
      case 'closed': return 'bg-emerald-100 text-emerald-800';
      case 'lost': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPropertyStatusColor = (status: string) => {
    switch (status) {
      case 'acquisition_target': return 'bg-blue-100 text-blue-800';
      case 'under_contract': return 'bg-yellow-100 text-yellow-800';
      case 'owned': return 'bg-green-100 text-green-800';
      case 'disposition': return 'bg-purple-100 text-purple-800';
      case 'sold': return 'bg-emerald-100 text-emerald-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'all' || lead.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const startPipeline = (pipelineId: string) => {
    setIsProcessing(true);
    // Simulate pipeline processing
    setTimeout(() => {
      setIsProcessing(false);
    }, 2000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Data Pipeline Management</h1>
          <p className="text-gray-600 mt-2">
            Manage property acquisition, disposition, and investor outreach pipelines
          </p>
        </div>
        <div className="flex gap-3">
          <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Import Data
          </button>
          <button className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-2">
            <Download className="h-4 w-4" />
            Export Report
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Leads</p>
              <p className="text-2xl font-bold text-gray-900">{leads.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Properties</p>
              <p className="text-2xl font-bold text-gray-900">{properties.length}</p>
            </div>
            <Building className="h-8 w-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Pipelines</p>
              <p className="text-2xl font-bold text-gray-900">
                {pipelines.filter(p => p.status === 'active').length}
              </p>
            </div>
            <Database className="h-8 w-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg Success Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {(pipelines.reduce((acc, p) => acc + p.successRate, 0) / pipelines.length).toFixed(1)}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-600" />
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div>
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'leads', label: 'Investor Leads' },
              { id: 'properties', label: 'Properties' },
              { id: 'pipelines', label: 'Pipelines' },
              { id: 'analytics', label: 'Analytics' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {activeTab === 'leads' && (
          <div className="space-y-6">
            {/* Search and Filter */}
            <div className="flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search leads..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="new">New</option>
              <option value="contacted">Contacted</option>
              <option value="qualified">Qualified</option>
              <option value="negotiating">Negotiating</option>
              <option value="closed">Closed</option>
              <option value="lost">Lost</option>
            </select>
          </div>

            {/* Leads Table */}
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Investor Leads</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {filteredLeads.map((lead) => (
                    <div key={lead.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-gray-900">{lead.name}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(lead.status)}`}>
                              {lead.status.replace('_', ' ')}
                            </span>
                            <span className="px-2 py-1 rounded-full text-xs font-medium border border-gray-300 bg-white text-gray-700">
                              {lead.conversionProbability}% probability
                            </span>
                          </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Mail className="h-4 w-4" />
                            {lead.email}
                          </div>
                          <div className="flex items-center gap-1">
                            <Phone className="h-4 w-4" />
                            {lead.phone}
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            {lead.location}
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-4 w-4" />
                            ${lead.investmentCapacity.toLocaleString()}
                          </div>
                        </div>
                          <div className="mt-2">
                            <p className="text-sm text-gray-600">{lead.notes}</p>
                            <div className="flex gap-2 mt-2">
                              {lead.interests.map((interest) => (
                                <span key={interest} className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-700">
                                  {interest.replace('_', ' ')}
                                </span>
                              ))}
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <button className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-1">
                            <Phone className="h-4 w-4" />
                            Call
                          </button>
                          <button className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50 flex items-center gap-1">
                            <Mail className="h-4 w-4" />
                            Email
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'properties' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Property Portfolio</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {properties.map((property) => (
                    <div key={property.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-semibold text-gray-900">{property.address}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPropertyStatusColor(property.status)}`}>
                              {property.status.replace('_', ' ')}
                            </span>
                            {property.tokenized && (
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-yellow-50 text-yellow-700 border border-yellow-200 flex items-center gap-1">
                                <Coins className="h-3 w-3" />
                                Tokenized
                              </span>
                            )}
                          </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Type:</span> {property.type.replace('_', ' ')}
                          </div>
                          <div>
                            <span className="font-medium">Value:</span> ${property.value.toLocaleString()}
                          </div>
                          <div>
                            <span className="font-medium">ROI:</span> {property.roi}%
                          </div>
                          {property.tokenized && (
                            <div>
                              <span className="font-medium">Investors:</span> {property.investorCount}
                            </div>
                          )}
                        </div>
                        </div>
                        <div className="flex gap-2">
                          <button className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                            View Details
                          </button>
                          {property.status === 'disposition' && (
                            <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700">
                              Market
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'pipelines' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Active Pipelines</h3>
              </div>
              <div className="p-6">
                <div className="space-y-4">
                  {pipelines.map((pipeline) => (
                    <div key={pipeline.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold text-gray-900">{pipeline.name}</h3>
                          <p className="text-sm text-gray-600 capitalize">{pipeline.type.replace('_', ' ')}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <span 
                            className={`px-2 py-1 rounded-full text-xs font-medium ${
                              pipeline.status === 'active' ? 'bg-green-100 text-green-800' : 
                              pipeline.status === 'paused' ? 'bg-yellow-100 text-yellow-800' : 
                              'bg-gray-100 text-gray-800'
                            }`}
                          >
                            {pipeline.status}
                          </span>
                          {pipeline.aiAgentAssigned && (
                            <span className="px-2 py-1 rounded-full text-xs font-medium border border-blue-200 bg-blue-50 text-blue-700">
                              AI Agent
                            </span>
                          )}
                        </div>
                      </div>
                      
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Progress</span>
                            <span>{pipeline.processedRecords}/{pipeline.totalRecords} records</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${pipeline.progress}%` }}
                            ></div>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 text-sm">
                          <div>
                            <span className="text-gray-600">Success Rate:</span>
                            <span className="font-medium ml-1">{pipeline.successRate}%</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Last Run:</span>
                            <span className="font-medium ml-1">
                              {pipeline.lastRun.toLocaleDateString()}
                            </span>
                          </div>
                          <div className="flex gap-2">
                            <button 
                              className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1"
                              onClick={() => startPipeline(pipeline.id)}
                              disabled={isProcessing}
                            >
                              {pipeline.status === 'active' ? (
                                <Pause className="h-4 w-4" />
                              ) : (
                                <Play className="h-4 w-4" />
                              )}
                              {pipeline.status === 'active' ? 'Pause' : 'Start'}
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Pipeline Performance
                  </h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    {pipelines.map((pipeline) => (
                      <div key={pipeline.id} className="flex justify-between items-center">
                        <span className="text-sm font-medium">{pipeline.name}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${pipeline.successRate * 5}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600">{pipeline.successRate}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Conversion Metrics
                  </h3>
                </div>
                <div className="p-6">
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm">Lead to Qualified</span>
                      <span className="font-medium">24.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Qualified to Negotiating</span>
                      <span className="font-medium">45.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Negotiating to Closed</span>
                      <span className="font-medium">67.8%</span>
                    </div>
                    <div className="flex justify-between border-t pt-2">
                      <span className="text-sm font-medium">Overall Conversion</span>
                      <span className="font-bold">7.5%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DataPipeline;