# Server Configuration
PORT=3001
HOST=0.0.0.0
NODE_ENV=development

# Security
JWT_SECRET=your-super-secret-jwt-key-here
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/estratix_smart_contracts

# Blockchain Configuration
NETWORK_NAME=sepolia
RPC_URL=https://sepolia.infura.io/v3/YOUR_INFURA_PROJECT_ID
PRIVATE_KEY=your-private-key-here
CHAIN_ID=11155111

# Alternative RPC URLs
MAINNET_RPC_URL=https://mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
ARBITRUM_RPC_URL=https://arbitrum-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
OPTIMISM_RPC_URL=https://optimism-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID
BASE_RPC_URL=https://base-mainnet.infura.io/v3/YOUR_INFURA_PROJECT_ID

# API Keys
INFURA_API_KEY=your-infura-api-key
ALCHEMY_API_KEY=your-alchemy-api-key
ETHERSCAN_API_KEY=your-etherscan-api-key
POLYGONSCAN_API_KEY=your-polygonscan-api-key
ARBISCAN_API_KEY=your-arbiscan-api-key
OPTIMISM_ETHERSCAN_API_KEY=your-optimism-etherscan-api-key
BASESCAN_API_KEY=your-basescan-api-key

# Contract Addresses (Sepolia Testnet)
LUX_TOKEN_ADDRESS=******************************************
PROPERTY_NFT_ADDRESS=******************************************
STAKING_CONTRACT_ADDRESS=******************************************
MARKETPLACE_ADDRESS=******************************************
GOVERNANCE_ADDRESS=******************************************
LENDING_POOL_ADDRESS=******************************************
LIQUIDITY_POOL_ADDRESS=******************************************
PROPERTY_TOKENIZATION_ADDRESS=******************************************
YIELD_FARMING_ADDRESS=******************************************
INSURANCE_POOL_ADDRESS=******************************************

# IPFS Configuration
IPFS_GATEWAY=https://ipfs.io/ipfs/
IPFS_API_URL=https://api.pinata.cloud
PINATA_API_KEY=your-pinata-api-key
PINATA_SECRET_KEY=your-pinata-secret-key

# External Services
COINGECKO_API_KEY=your-coingecko-api-key
MORTALIS_API_KEY=your-mortalis-api-key

# Monitoring
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_DIR=./uploads

# Queue Configuration
REDIS_URL=redis://localhost:6379
QUEUE_NAME=smart-contracts

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret
WEBHOOK_URL=https://your-domain.com/webhooks

# Feature Flags
ENABLE_GOVERNANCE=true
ENABLE_DEFI=true
ENABLE_YIELD_FARMING=true
ENABLE_PROPERTY_TOKENIZATION=true
ENABLE_STAKING=true