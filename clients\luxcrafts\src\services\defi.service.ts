import { apiClient, ApiResponse } from './api.client';
import { BaseService } from './base.service';
import { buildSearchParams } from './service.utils';

// DeFi types
export interface Token {
  id: string;
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  logoUrl: string;
  price: number;
  priceChange24h: number;
  marketCap: number;
  volume24h: number;
  totalSupply: number;
  circulatingSupply: number;
  blockchain: 'ethereum' | 'polygon' | 'binance' | 'arbitrum';
  isStablecoin: boolean;
  isVerified: boolean;
}

export interface LiquidityPool {
  id: string;
  name: string;
  token0: Token;
  token1: Token;
  reserve0: number;
  reserve1: number;
  totalLiquidity: number;
  apr: number;
  volume24h: number;
  fees24h: number;
  lpTokenPrice: number;
  lpTokenSupply: number;
  blockchain: Token['blockchain'];
  protocol: 'uniswap' | 'sushiswap' | 'pancakeswap' | 'quickswap';
  isActive: boolean;
  createdAt: Date;
}

export interface StakingPool {
  id: string;
  name: string;
  stakingToken: Token;
  rewardToken: Token;
  totalStaked: number;
  totalRewards: number;
  apr: number;
  apy: number;
  lockPeriod: number; // in days
  minStake: number;
  maxStake?: number;
  isActive: boolean;
  startDate: Date;
  endDate?: Date;
  userStaked?: number;
  userRewards?: number;
  lastRewardTime?: Date;
}

export interface YieldFarm {
  id: string;
  name: string;
  lpToken: string;
  rewardToken: Token;
  totalLiquidity: number;
  totalRewards: number;
  apr: number;
  multiplier: number;
  allocPoint: number;
  lastRewardBlock: number;
  accRewardPerShare: number;
  isActive: boolean;
  userInfo?: {
    amount: number;
    rewardDebt: number;
    pendingRewards: number;
  };
}

export interface Portfolio {
  totalValue: number;
  totalPnl: number;
  totalPnlPercentage: number;
  tokens: Array<{
    token: Token;
    balance: number;
    value: number;
    pnl: number;
    pnlPercentage: number;
  }>;
  liquidityPositions: Array<{
    pool: LiquidityPool;
    lpTokens: number;
    value: number;
    share: number;
    fees24h: number;
  }>;
  stakingPositions: Array<{
    pool: StakingPool;
    staked: number;
    rewards: number;
    value: number;
  }>;
  farmingPositions: Array<{
    farm: YieldFarm;
    staked: number;
    rewards: number;
    value: number;
  }>;
}

export interface SwapQuote {
  inputToken: Token;
  outputToken: Token;
  inputAmount: number;
  outputAmount: number;
  priceImpact: number;
  minimumReceived: number;
  route: Array<{
    pool: string;
    tokenIn: string;
    tokenOut: string;
    fee: number;
  }>;
  gasEstimate: number;
  slippage: number;
}

export interface Transaction {
  id: string;
  hash: string;
  type: 'swap' | 'add_liquidity' | 'remove_liquidity' | 'stake' | 'unstake' | 'claim';
  status: 'pending' | 'confirmed' | 'failed';
  from: string;
  to: string;
  value: number;
  gasUsed: number;
  gasPrice: number;
  blockNumber: number;
  timestamp: Date;
  tokens: Array<{
    token: Token;
    amount: number;
    direction: 'in' | 'out';
  }>;
}

// Mock data for development
const mockTokens: Token[] = [
  {
    id: 'ethereum',
    symbol: 'ETH',
    name: 'Ethereum',
    address: '******************************************',
    decimals: 18,
    logoUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=ethereum%20logo%20cryptocurrency&image_size=square',
    price: 2450.75,
    priceChange24h: 3.2,
    marketCap: ************,
    volume24h: 15600000000,
    totalSupply: 120280000,
    circulatingSupply: 120280000,
    blockchain: 'ethereum',
    isStablecoin: false,
    isVerified: true
  },
  {
    id: 'usdc',
    symbol: 'USDC',
    name: 'USD Coin',
    address: '******************************************',
    decimals: 6,
    logoUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=usdc%20coin%20logo%20stablecoin&image_size=square',
    price: 1.0,
    priceChange24h: 0.01,
    marketCap: 32800000000,
    volume24h: 4200000000,
    totalSupply: 32800000000,
    circulatingSupply: 32800000000,
    blockchain: 'ethereum',
    isStablecoin: true,
    isVerified: true
  },
  {
    id: 'luxcraft-token',
    symbol: 'LUX',
    name: 'LuxCraft Token',
    address: '******************************************',
    decimals: 18,
    logoUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20token%20logo%20gold%20elegant&image_size=square',
    price: 12.45,
    priceChange24h: 8.7,
    marketCap: 124500000,
    volume24h: 2300000,
    totalSupply: 10000000,
    circulatingSupply: 8500000,
    blockchain: 'ethereum',
    isStablecoin: false,
    isVerified: true
  }
];

const mockLiquidityPools: LiquidityPool[] = [
  {
    id: 'pool-1',
    name: 'ETH/USDC',
    token0: mockTokens[0],
    token1: mockTokens[1],
    reserve0: 1250.5,
    reserve1: 3062737.5,
    totalLiquidity: 7500000,
    apr: 12.5,
    volume24h: 45000000,
    fees24h: 135000,
    lpTokenPrice: 3.75,
    lpTokenSupply: 2000000,
    blockchain: 'ethereum',
    protocol: 'uniswap',
    isActive: true,
    createdAt: new Date('2023-01-15')
  },
  {
    id: 'pool-2',
    name: 'LUX/ETH',
    token0: mockTokens[2],
    token1: mockTokens[0],
    reserve0: 50000,
    reserve1: 254.2,
    totalLiquidity: 1250000,
    apr: 45.8,
    volume24h: 890000,
    fees24h: 2670,
    lpTokenPrice: 0.625,
    lpTokenSupply: 200000,
    blockchain: 'ethereum',
    protocol: 'uniswap',
    isActive: true,
    createdAt: new Date('2023-12-01')
  }
];

const mockStakingPools: StakingPool[] = [
  {
    id: 'stake-1',
    name: 'LUX Staking Pool',
    stakingToken: mockTokens[2],
    rewardToken: mockTokens[2],
    totalStaked: 2500000,
    totalRewards: 125000,
    apr: 25.0,
    apy: 28.4,
    lockPeriod: 30,
    minStake: 100,
    maxStake: 50000,
    isActive: true,
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    userStaked: 5000,
    userRewards: 312.5,
    lastRewardTime: new Date('2024-01-20')
  },
  {
    id: 'stake-2',
    name: 'ETH Staking Pool',
    stakingToken: mockTokens[0],
    rewardToken: mockTokens[2],
    totalStaked: 850.5,
    totalRewards: 42525,
    apr: 18.5,
    apy: 20.2,
    lockPeriod: 0,
    minStake: 0.1,
    isActive: true,
    startDate: new Date('2023-06-01'),
    userStaked: 2.5,
    userRewards: 115.6
  }
];

const mockYieldFarms: YieldFarm[] = [
  {
    id: 'farm-1',
    name: 'ETH/USDC LP Farm',
    lpToken: 'pool-1',
    rewardToken: mockTokens[2],
    totalLiquidity: 5000000,
    totalRewards: 250000,
    apr: 35.2,
    multiplier: 2,
    allocPoint: 1000,
    lastRewardBlock: 18950000,
    accRewardPerShare: 1250000000000,
    isActive: true,
    userInfo: {
      amount: 1500,
      rewardDebt: 1875000000000000,
      pendingRewards: 125.5
    }
  }
];

class DeFiService extends BaseService {

  async getTokens(): Promise<Token[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Token[]>('/defi/tokens');
        return response.data;
      },
      () => [...mockTokens],
      400
    );
  }

  async getTokenById(id: string): Promise<Token> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Token>(`/defi/tokens/${id}`);
        return response.data;
      },
      async () => {
        const token = mockTokens.find(t => t.id === id);
        if (!token) {
          throw new Error('Token not found');
        }
        return token;
      },
      300
    );
  }

  async getLiquidityPools(): Promise<LiquidityPool[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<LiquidityPool[]>('/defi/pools');
        return response.data;
      },
      () => [...mockLiquidityPools],
      500
    );
  }

  async getStakingPools(): Promise<StakingPool[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<StakingPool[]>('/defi/staking');
        return response.data;
      },
      () => [...mockStakingPools],
      400
    );
  }

  async getYieldFarms(): Promise<YieldFarm[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<YieldFarm[]>('/defi/farms');
        return response.data;
      },
      () => [...mockYieldFarms],
      400
    );
  }

  async getSwapQuote(
    inputTokenId: string,
    outputTokenId: string,
    inputAmount: number,
    slippage: number = 0.5
  ): Promise<SwapQuote> {
    return this.handleApiCall(
      async () => {
        const params = buildSearchParams({
          inputToken: inputTokenId,
          outputToken: outputTokenId,
          inputAmount: inputAmount.toString(),
          slippage: slippage.toString()
        });
        const response = await apiClient.get<SwapQuote>(`/defi/swap/quote?${params.toString()}`);
        return response.data;
      },
      async () => {
        const inputToken = mockTokens.find(t => t.id === inputTokenId);
        const outputToken = mockTokens.find(t => t.id === outputTokenId);
        
        if (!inputToken || !outputToken) {
          throw new Error('Token not found');
        }
        
        // Mock calculation
        const rate = outputToken.price / inputToken.price;
        const outputAmount = inputAmount * rate * 0.997; // 0.3% fee
        const priceImpact = inputAmount > 1000 ? 0.5 : 0.1;
        const minimumReceived = outputAmount * (1 - slippage / 100);
        
        return {
          inputToken,
          outputToken,
          inputAmount,
          outputAmount,
          priceImpact,
          minimumReceived,
          route: [
            {
              pool: 'pool-1',
              tokenIn: inputToken.address,
              tokenOut: outputToken.address,
              fee: 0.3
            }
          ],
          gasEstimate: 150000,
          slippage
        };
      },
      800
    );
  }

  async executeSwap(quote: SwapQuote): Promise<{ transactionHash: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string }>('/defi/swap/execute', quote);
        return response.data;
      },
      () => ({
        transactionHash: '0x' + Math.random().toString(16).substr(2, 64)
      }),
      2000
    );
  }

  async addLiquidity(
    poolId: string,
    token0Amount: number,
    token1Amount: number
  ): Promise<{ transactionHash: string; lpTokens: number }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string; lpTokens: number }>(
          `/defi/pools/${poolId}/add-liquidity`,
          { token0Amount, token1Amount }
        );
        return response.data;
      },
      async () => {
        const pool = mockLiquidityPools.find(p => p.id === poolId);
        if (!pool) {
          throw new Error('Pool not found');
        }
        
        // Mock LP token calculation
        const lpTokens = Math.sqrt(token0Amount * token1Amount) * 0.95;
        
        return {
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
          lpTokens
        };
      },
      2500
    );
  }

  async removeLiquidity(
    poolId: string,
    lpTokenAmount: number
  ): Promise<{ transactionHash: string; token0Amount: number; token1Amount: number }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string; token0Amount: number; token1Amount: number }>(
          `/defi/pools/${poolId}/remove-liquidity`,
          { lpTokenAmount }
        );
        return response.data;
      },
      async () => {
        const pool = mockLiquidityPools.find(p => p.id === poolId);
        if (!pool) {
          throw new Error('Pool not found');
        }
        
        // Mock token amounts calculation
        const share = lpTokenAmount / pool.lpTokenSupply;
        const token0Amount = pool.reserve0 * share;
        const token1Amount = pool.reserve1 * share;
        
        return {
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
          token0Amount,
          token1Amount
        };
      },
      2000
    );
  }

  async stakeTokens(
    poolId: string,
    amount: number
  ): Promise<{ transactionHash: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string }>(
          `/defi/staking/${poolId}/stake`,
          { amount }
        );
        return response.data;
      },
      async () => {
        const pool = mockStakingPools.find(p => p.id === poolId);
        if (!pool) {
          throw new Error('Staking pool not found');
        }
        
        if (amount < pool.minStake) {
          throw new Error(`Minimum stake amount is ${pool.minStake}`);
        }
        
        if (pool.maxStake && amount > pool.maxStake) {
          throw new Error(`Maximum stake amount is ${pool.maxStake}`);
        }
        
        // Update mock data
        pool.totalStaked += amount;
        if (pool.userStaked) {
          pool.userStaked += amount;
        } else {
          pool.userStaked = amount;
        }
        
        return {
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64)
        };
      },
      1800
    );
  }

  async unstakeTokens(
    poolId: string,
    amount: number
  ): Promise<{ transactionHash: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string }>(
          `/defi/staking/${poolId}/unstake`,
          { amount }
        );
        return response.data;
      },
      async () => {
        const pool = mockStakingPools.find(p => p.id === poolId);
        if (!pool) {
          throw new Error('Staking pool not found');
        }
        
        if (!pool.userStaked || amount > pool.userStaked) {
          throw new Error('Insufficient staked amount');
        }
        
        // Update mock data
        pool.totalStaked -= amount;
        pool.userStaked -= amount;
        
        return {
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64)
        };
      },
      1800
    );
  }

  async claimRewards(poolId: string): Promise<{ transactionHash: string; rewardAmount: number }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ transactionHash: string; rewardAmount: number }>(
          `/defi/staking/${poolId}/claim`
        );
        return response.data;
      },
      async () => {
        const pool = mockStakingPools.find(p => p.id === poolId);
        if (!pool) {
          throw new Error('Staking pool not found');
        }
        
        const rewardAmount = pool.userRewards || 0;
        
        // Update mock data
        if (pool.userRewards) {
          pool.userRewards = 0;
          pool.lastRewardTime = new Date();
        }
        
        return {
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64),
          rewardAmount
        };
      },
      1500
    );
  }

  async getPortfolio(address: string): Promise<Portfolio> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Portfolio>(`/defi/portfolio/${address}`);
        return response.data;
      },
      async () => {
        // Mock portfolio data
        const tokenBalances = [
          {
            token: mockTokens[0],
            balance: 2.5,
            value: 2.5 * mockTokens[0].price,
            pnl: 245.5,
            pnlPercentage: 4.2
          },
          {
            token: mockTokens[1],
            balance: 5000,
            value: 5000 * mockTokens[1].price,
            pnl: 0,
            pnlPercentage: 0
          },
          {
            token: mockTokens[2],
            balance: 1500,
            value: 1500 * mockTokens[2].price,
            pnl: 1245.8,
            pnlPercentage: 7.1
          }
        ];
        
        const liquidityPositions = [
          {
            pool: mockLiquidityPools[0],
            lpTokens: 100,
            value: 375,
            share: 0.005,
            fees24h: 6.75
          }
        ];
        
        const stakingPositions = [
          {
            pool: mockStakingPools[0],
            staked: 5000,
            rewards: 312.5,
            value: 5000 * mockTokens[2].price + 312.5 * mockTokens[2].price
          }
        ];
        
        const farmingPositions = [
          {
            farm: mockYieldFarms[0],
            staked: 1500,
            rewards: 125.5,
            value: 1500 * mockLiquidityPools[0].lpTokenPrice + 125.5 * mockTokens[2].price
          }
        ];
        
        const totalValue = [
          ...tokenBalances.map(t => t.value),
          ...liquidityPositions.map(l => l.value),
          ...stakingPositions.map(s => s.value),
          ...farmingPositions.map(f => f.value)
        ].reduce((sum, value) => sum + value, 0);
        
        const totalPnl = tokenBalances.reduce((sum, t) => sum + t.pnl, 0);
        
        return {
          totalValue,
          totalPnl,
          totalPnlPercentage: (totalPnl / (totalValue - totalPnl)) * 100,
          tokens: tokenBalances,
          liquidityPositions,
          stakingPositions,
          farmingPositions
        };
      },
      800
    );
  }

  async getTransactionHistory(address: string): Promise<Transaction[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Transaction[]>(`/defi/transactions/${address}`);
        return response.data;
      },
      () => [
        {
          id: 'tx-1',
          hash: '******************************************90abcdef1234567890abcdef',
          type: 'swap',
          status: 'confirmed',
          from: address,
          to: '0xUniswapRouter',
          value: 1000,
          gasUsed: 150000,
          gasPrice: 20,
          blockNumber: 18950000,
          timestamp: new Date('2024-01-20T10:30:00Z'),
          tokens: [
            {
              token: mockTokens[1],
              amount: 1000,
              direction: 'out'
            },
            {
              token: mockTokens[2],
              amount: 80.32,
              direction: 'in'
            }
          ]
        },
        {
          id: 'tx-2',
          hash: '0x2345678901bcdef12345678901bcdef12345678901bcdef12345678901bcdef1',
          type: 'stake',
          status: 'confirmed',
          from: address,
          to: '0xStakingContract',
          value: 5000,
          gasUsed: 120000,
          gasPrice: 18,
          blockNumber: 18949500,
          timestamp: new Date('2024-01-19T15:45:00Z'),
          tokens: [
            {
              token: mockTokens[2],
              amount: 5000,
              direction: 'out'
            }
          ]
        }
      ],
      600
    );
  }

  async getTokenPrice(tokenId: string): Promise<{ price: number; priceChange24h: number }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<{ price: number; priceChange24h: number }>(
          `/defi/tokens/${tokenId}/price`
        );
        return response.data;
      },
      async () => {
        const token = mockTokens.find(t => t.id === tokenId);
        if (!token) {
          throw new Error('Token not found');
        }
        
        return {
          price: token.price,
          priceChange24h: token.priceChange24h
        };
      },
      200
    );
  }
}

export const defiService = new DeFiService();
export default defiService;