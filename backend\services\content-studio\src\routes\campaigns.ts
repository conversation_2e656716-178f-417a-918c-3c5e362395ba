import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { logger } from '../utils/logger';

// Validation schemas
const createCampaignSchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().optional(),
  type: z.enum(['social_media', 'email_sequence', 'content_series', 'advertising', 'influencer', 'seo', 'ppc']),
  status: z.enum(['draft', 'planning', 'active', 'paused', 'completed', 'cancelled']).default('draft'),
  objectives: z.array(z.enum(['awareness', 'engagement', 'conversion', 'retention', 'education', 'lead_generation'])),
  targetAudience: z.object({
    demographics: z.string(),
    interests: z.array(z.string()),
    painPoints: z.array(z.string()),
    platforms: z.array(z.string()),
  }),
  budget: z.object({
    total: z.number().min(0),
    allocated: z.number().min(0).default(0),
    currency: z.string().default('USD'),
  }),
  timeline: z.object({
    startDate: z.string().datetime(),
    endDate: z.string().datetime(),
    milestones: z.array(z.object({
      name: z.string(),
      date: z.string().datetime(),
      description: z.string().optional(),
    })).optional(),
  }),
  platforms: z.array(z.string()),
  kpis: z.array(z.object({
    name: z.string(),
    target: z.number(),
    unit: z.string(),
    priority: z.enum(['low', 'medium', 'high']),
  })),
  tags: z.array(z.string()).optional(),
  metadata: z.object({
    brandGuidelines: z.object({
      brandName: z.string(),
      brandVoice: z.string(),
      brandValues: z.array(z.string()),
      colorPalette: z.array(z.string()).optional(),
      fonts: z.array(z.string()).optional(),
    }).optional(),
    competitorAnalysis: z.string().optional(),
    marketResearch: z.string().optional(),
  }).optional(),
});

const updateCampaignSchema = createCampaignSchema.partial();

const campaignQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(20),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['createdAt', 'updatedAt', 'name', 'status', 'startDate']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

const campaignContentSchema = z.object({
  contentIds: z.array(z.string()),
  schedule: z.object({
    frequency: z.enum(['daily', 'weekly', 'bi-weekly', 'monthly']),
    timeSlots: z.array(z.string()),
    platforms: z.array(z.string()),
  }),
});

export const campaignRoutes: FastifyPluginAsync = async (fastify) => {
  // Get all campaigns
  fastify.get('/', {
    schema: {
      description: 'Get all campaigns with filtering and pagination',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      querystring: campaignQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                campaigns: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      description: { type: 'string' },
                      type: { type: 'string' },
                      status: { type: 'string' },
                      objectives: { type: 'array', items: { type: 'string' } },
                      budget: { type: 'object' },
                      timeline: { type: 'object' },
                      platforms: { type: 'array', items: { type: 'string' } },
                      performance: { type: 'object' },
                      createdAt: { type: 'string' },
                      updatedAt: { type: 'string' },
                    },
                  },
                },
                pagination: {
                  type: 'object',
                  properties: {
                    page: { type: 'number' },
                    limit: { type: 'number' },
                    total: { type: 'number' },
                    pages: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const query = campaignQuerySchema.parse(request.query);
      const userId = request.user?.id;

      // Mock campaigns data
      const mockCampaigns = [
        {
          id: '1',
          name: 'Q4 Holiday Campaign',
          description: 'Comprehensive holiday marketing campaign across all platforms',
          type: 'social_media',
          status: 'active',
          objectives: ['awareness', 'conversion'],
          budget: {
            total: 50000,
            allocated: 32000,
            currency: 'USD',
          },
          timeline: {
            startDate: '2024-11-01T00:00:00Z',
            endDate: '2024-12-31T23:59:59Z',
          },
          platforms: ['instagram', 'facebook', 'twitter', 'email'],
          performance: {
            reach: 125000,
            engagement: 8500,
            conversions: 450,
            roi: 2.3,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: '2',
          name: 'Brand Awareness Series',
          description: 'Educational content series to build brand authority',
          type: 'content_series',
          status: 'planning',
          objectives: ['awareness', 'education'],
          budget: {
            total: 25000,
            allocated: 5000,
            currency: 'USD',
          },
          timeline: {
            startDate: '2024-12-15T00:00:00Z',
            endDate: '2025-03-15T23:59:59Z',
          },
          platforms: ['blog', 'linkedin', 'youtube'],
          performance: {
            reach: 0,
            engagement: 0,
            conversions: 0,
            roi: 0,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      // Apply filters
      let filteredCampaigns = mockCampaigns;
      if (query.type) {
        filteredCampaigns = filteredCampaigns.filter(campaign => campaign.type === query.type);
      }
      if (query.status) {
        filteredCampaigns = filteredCampaigns.filter(campaign => campaign.status === query.status);
      }
      if (query.search) {
        const searchLower = query.search.toLowerCase();
        filteredCampaigns = filteredCampaigns.filter(campaign => 
          campaign.name.toLowerCase().includes(searchLower) ||
          campaign.description?.toLowerCase().includes(searchLower)
        );
      }

      // Pagination
      const total = filteredCampaigns.length;
      const pages = Math.ceil(total / query.limit);
      const offset = (query.page - 1) * query.limit;
      const paginatedCampaigns = filteredCampaigns.slice(offset, offset + query.limit);

      return {
        success: true,
        data: {
          campaigns: paginatedCampaigns,
          pagination: {
            page: query.page,
            limit: query.limit,
            total,
            pages,
          },
        },
      };
    } catch (error) {
      logger.error('Error fetching campaigns:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch campaigns',
      };
    }
  });

  // Get campaign by ID
  fastify.get('/:id', {
    schema: {
      description: 'Get campaign by ID',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock campaign data
      const mockCampaign = {
        id,
        name: 'Sample Campaign',
        description: 'This is a sample campaign',
        type: 'social_media',
        status: 'active',
        objectives: ['awareness', 'engagement'],
        targetAudience: {
          demographics: 'Adults 25-45, urban professionals',
          interests: ['technology', 'business', 'lifestyle'],
          painPoints: ['time management', 'work-life balance'],
          platforms: ['instagram', 'linkedin'],
        },
        budget: {
          total: 10000,
          allocated: 6500,
          currency: 'USD',
        },
        timeline: {
          startDate: '2024-01-01T00:00:00Z',
          endDate: '2024-03-31T23:59:59Z',
          milestones: [
            {
              name: 'Campaign Launch',
              date: '2024-01-01T00:00:00Z',
              description: 'Official campaign launch',
            },
            {
              name: 'Mid-campaign Review',
              date: '2024-02-15T00:00:00Z',
              description: 'Performance review and optimization',
            },
          ],
        },
        platforms: ['instagram', 'facebook', 'linkedin'],
        kpis: [
          {
            name: 'Reach',
            target: 100000,
            unit: 'people',
            priority: 'high',
          },
          {
            name: 'Engagement Rate',
            target: 5.5,
            unit: 'percentage',
            priority: 'medium',
          },
        ],
        content: {
          total: 24,
          published: 18,
          scheduled: 6,
          draft: 0,
        },
        performance: {
          reach: 85000,
          impressions: 250000,
          engagement: 4200,
          clicks: 1800,
          conversions: 120,
          cost: 6500,
          roi: 1.8,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      return {
        success: true,
        data: mockCampaign,
      };
    } catch (error) {
      logger.error('Error fetching campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch campaign',
      };
    }
  });

  // Create new campaign
  fastify.post('/', {
    schema: {
      description: 'Create a new campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      body: createCampaignSchema,
    },
  }, async (request, reply) => {
    try {
      const campaignData = createCampaignSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock campaign creation
      const newCampaign = {
        id: Date.now().toString(),
        ...campaignData,
        userId,
        performance: {
          reach: 0,
          impressions: 0,
          engagement: 0,
          clicks: 0,
          conversions: 0,
          cost: 0,
          roi: 0,
        },
        content: {
          total: 0,
          published: 0,
          scheduled: 0,
          draft: 0,
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Campaign created: ${newCampaign.id}`);

      reply.code(201);
      return {
        success: true,
        data: newCampaign,
      };
    } catch (error) {
      logger.error('Error creating campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to create campaign',
      };
    }
  });

  // Update campaign
  fastify.put('/:id', {
    schema: {
      description: 'Update a campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: updateCampaignSchema,
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = updateCampaignSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock campaign update
      const updatedCampaign = {
        id,
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Campaign updated: ${id}`);

      return {
        success: true,
        data: updatedCampaign,
      };
    } catch (error) {
      logger.error('Error updating campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to update campaign',
      };
    }
  });

  // Delete campaign
  fastify.delete('/:id', {
    schema: {
      description: 'Delete a campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock campaign deletion
      logger.info(`Campaign deleted: ${id}`);

      reply.code(204);
      return;
    } catch (error) {
      logger.error('Error deleting campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to delete campaign',
      };
    }
  });

  // Get campaign analytics
  fastify.get('/:id/analytics', {
    schema: {
      description: 'Get detailed analytics for a campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      querystring: z.object({
        period: z.enum(['day', 'week', 'month']).default('week'),
        metrics: z.string().optional(), // comma-separated list
      }),
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const query = request.query as any;
      const userId = request.user?.id;

      // Mock analytics data
      const analytics = {
        overview: {
          totalReach: 125000,
          totalImpressions: 450000,
          totalEngagement: 8500,
          totalClicks: 3200,
          totalConversions: 450,
          totalSpent: 32000,
          roi: 2.3,
          cpm: 71.11,
          cpc: 10.00,
          ctr: 0.71,
          conversionRate: 14.06,
        },
        timeline: [
          {
            date: '2024-01-01',
            reach: 5000,
            impressions: 18000,
            engagement: 340,
            clicks: 128,
            conversions: 18,
            spent: 1280,
          },
          {
            date: '2024-01-02',
            reach: 6200,
            impressions: 22000,
            engagement: 420,
            clicks: 156,
            conversions: 22,
            spent: 1560,
          },
        ],
        platforms: {
          instagram: {
            reach: 65000,
            engagement: 4500,
            clicks: 1800,
            conversions: 250,
            spent: 18000,
          },
          facebook: {
            reach: 40000,
            engagement: 2800,
            clicks: 900,
            conversions: 120,
            spent: 9000,
          },
          twitter: {
            reach: 20000,
            engagement: 1200,
            clicks: 500,
            conversions: 80,
            spent: 5000,
          },
        },
        demographics: {
          age: {
            '18-24': 15,
            '25-34': 35,
            '35-44': 30,
            '45-54': 15,
            '55+': 5,
          },
          gender: {
            male: 45,
            female: 52,
            other: 3,
          },
          location: {
            'United States': 60,
            'Canada': 15,
            'United Kingdom': 12,
            'Australia': 8,
            'Other': 5,
          },
        },
        topContent: [
          {
            id: 'content-1',
            title: 'Holiday Special Announcement',
            type: 'social_media',
            platform: 'instagram',
            reach: 25000,
            engagement: 1800,
            clicks: 450,
            conversions: 65,
          },
          {
            id: 'content-2',
            title: 'Behind the Scenes Video',
            type: 'video',
            platform: 'facebook',
            reach: 18000,
            engagement: 1200,
            clicks: 320,
            conversions: 42,
          },
        ],
        insights: [
          'Instagram posts perform 40% better than Facebook posts',
          'Video content has 3x higher engagement rate',
          'Peak engagement time is between 6-8 PM',
          'Mobile users account for 85% of total reach',
        ],
      };

      return {
        success: true,
        data: analytics,
      };
    } catch (error) {
      logger.error('Error fetching campaign analytics:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch campaign analytics',
      };
    }
  });

  // Add content to campaign
  fastify.post('/:id/content', {
    schema: {
      description: 'Add content to a campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: campaignContentSchema,
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const contentData = campaignContentSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock content addition to campaign
      logger.info(`Added ${contentData.contentIds.length} content pieces to campaign ${id}`);

      return {
        success: true,
        message: `Successfully added ${contentData.contentIds.length} content pieces to campaign`,
        data: {
          campaignId: id,
          addedContent: contentData.contentIds.length,
          schedule: contentData.schedule,
        },
      };
    } catch (error) {
      logger.error('Error adding content to campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to add content to campaign',
      };
    }
  });

  // Launch campaign
  fastify.post('/:id/launch', {
    schema: {
      description: 'Launch a campaign',
      tags: ['Campaign Management'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock campaign launch
      logger.info(`Campaign launched: ${id}`);

      return {
        success: true,
        message: 'Campaign launched successfully',
        data: {
          campaignId: id,
          status: 'active',
          launchedAt: new Date().toISOString(),
        },
      };
    } catch (error) {
      logger.error('Error launching campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to launch campaign',
      };
    }
  });
};