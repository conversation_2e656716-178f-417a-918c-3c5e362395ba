# ESTRATIX Kubernetes Cluster Matrix

## 1. Overview

This matrix serves as the central registry for all Kubernetes (K8s) clusters managed by ESTRATIX. It tracks cluster configurations, environments, status, and integration points with other infrastructure components.

## 2. Kubernetes Cluster Registry

| Cluster ID | Cluster Name        | Cloud Provider | Region      | Environment | Status   | K8s Version | Node Count | Definition Link |
| :--------- | :------------------ | :------------- | :---------- | :---------- | :------- | :---------- | :--------- | :-------------- |
| K8S-CL-001 | estratix-prod-us-east-1 | AWS EKS        | us-east-1   | Production  | `Active` | 1.28        | 3          | [K8S-CL-001_Definition.md](./definitions/K8S-CL-001.md) |
| K8S-CL-002 | estratix-staging-us-east-1| AWS EKS        | us-east-1   | Staging     | `Active` | 1.28        | 1          | [K8S-CL-002_Definition.md](./definitions/K8S-CL-002.md) |

## 3. Status Definitions

- **Provisioning**: The cluster is currently being created and configured.
- **Active**: The cluster is operational and serving workloads.
- **Maintenance**: The cluster is undergoing maintenance (e.g., version upgrade).
- **Degraded**: The cluster is operational but experiencing issues.
- **Inactive**: The cluster is provisioned but not currently in use.
- **Decommissioned**: The cluster has been destroyed.

## 4. Integration Points

- **VPC Server Matrix**: Defines the underlying virtual machine instances (nodes) and network infrastructure.
- **Container Matrix**: Tracks the container images and applications deployed to the clusters.
- **Deployment Matrix**: Outlines the CI/CD pipelines that target these clusters for application deployment.

---

**Last Updated**: YYYY-MM-DD  
**Next Review**: YYYY-MM-DD  
**Owner**: CTO (Chief Technology Officer)