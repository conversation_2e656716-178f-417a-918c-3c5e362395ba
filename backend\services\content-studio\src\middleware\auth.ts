import { FastifyPluginAsync, FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '../utils/logger';

// Extend FastifyRequest to include user property
declare module 'fastify' {
  interface FastifyRequest {
    user?: {
      id: string;
      email: string;
      role: string;
      permissions: string[];
    };
  }
}

export const authMiddleware: FastifyPluginAsync = async (fastify) => {
  // JWT verification hook
  fastify.addHook('preHandler', async (request: FastifyRequest, reply: FastifyReply) => {
    // Skip auth for health check and docs
    const publicRoutes = ['/health', '/docs', '/docs/static'];
    if (publicRoutes.some(route => request.url.startsWith(route))) {
      return;
    }

    try {
      // Extract token from Authorization header
      const authHeader = request.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        reply.code(401);
        throw new Error('Missing or invalid authorization header');
      }

      const token = authHeader.substring(7); // Remove 'Bearer ' prefix

      // Verify JWT token
      const decoded = await request.jwtVerify();
      
      // Attach user info to request
      request.user = {
        id: decoded.sub as string,
        email: decoded.email as string,
        role: decoded.role as string,
        permissions: decoded.permissions as string[] || [],
      };

      logger.debug(`Authenticated user: ${request.user.email}`);
    } catch (error) {
      logger.warn('Authentication failed:', error);
      reply.code(401);
      throw new Error('Invalid or expired token');
    }
  });

  // Role-based access control decorator
  fastify.decorate('requireRole', (requiredRole: string) => {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      if (!request.user) {
        reply.code(401);
        throw new Error('Authentication required');
      }

      if (request.user.role !== requiredRole && request.user.role !== 'admin') {
        reply.code(403);
        throw new Error(`Access denied. Required role: ${requiredRole}`);
      }
    };
  });

  // Permission-based access control decorator
  fastify.decorate('requirePermission', (requiredPermission: string) => {
    return async (request: FastifyRequest, reply: FastifyReply) => {
      if (!request.user) {
        reply.code(401);
        throw new Error('Authentication required');
      }

      if (!request.user.permissions.includes(requiredPermission) && request.user.role !== 'admin') {
        reply.code(403);
        throw new Error(`Access denied. Required permission: ${requiredPermission}`);
      }
    };
  });
};

// Helper function to generate JWT tokens (for testing/development)
export const generateTestToken = async (fastify: any, user: any) => {
  return fastify.jwt.sign(
    {
      sub: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    },
    { expiresIn: '24h' }
  );
};

// Middleware to extract user from token without throwing errors
export const optionalAuth = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const decoded = await request.jwtVerify();
      request.user = {
        id: decoded.sub as string,
        email: decoded.email as string,
        role: decoded.role as string,
        permissions: decoded.permissions as string[] || [],
      };
    }
  } catch (error) {
    // Silently ignore auth errors for optional auth
    logger.debug('Optional auth failed:', error);
  }
};