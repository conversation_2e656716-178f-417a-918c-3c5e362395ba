import { FastifyInstance as OriginalFastifyInstance, FastifyRequest } from 'fastify';

interface User {
  userId: string;
  email: string;
  role: string;
  walletAddress?: string;
  iat: number;
  exp: number;
}

declare module 'fastify' {
  interface FastifyInstance {
    get: OriginalFastifyInstance['get'];
    post: OriginalFastifyInstance['post'];
    put: OriginalFastifyInstance['put'];
    delete: OriginalFastifyInstance['delete'];
    patch: OriginalFastifyInstance['patch'];
    head: OriginalFastifyInstance['head'];
    options: OriginalFastifyInstance['options'];
  }
  
  interface FastifyRequest {
    user?: User;
  }
}