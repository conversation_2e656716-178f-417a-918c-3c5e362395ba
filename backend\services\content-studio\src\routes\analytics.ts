import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { logger } from '../utils/logger';

// Validation schemas
const analyticsQuerySchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  granularity: z.enum(['hour', 'day', 'week', 'month']).default('day'),
  metrics: z.string().optional(), // comma-separated list
  platforms: z.string().optional(), // comma-separated list
  contentTypes: z.string().optional(), // comma-separated list
  campaigns: z.string().optional(), // comma-separated list
});

const performanceQuerySchema = z.object({
  period: z.enum(['today', 'yesterday', 'last_7_days', 'last_30_days', 'last_90_days', 'custom']).default('last_7_days'),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  compareWith: z.enum(['previous_period', 'same_period_last_year']).optional(),
});

const reportConfigSchema = z.object({
  name: z.string().min(1).max(200),
  description: z.string().optional(),
  type: z.enum(['performance', 'engagement', 'roi', 'audience', 'content', 'campaign']),
  schedule: z.enum(['daily', 'weekly', 'monthly', 'quarterly']).optional(),
  recipients: z.array(z.string().email()).optional(),
  filters: z.object({
    platforms: z.array(z.string()).optional(),
    contentTypes: z.array(z.string()).optional(),
    campaigns: z.array(z.string()).optional(),
    dateRange: z.object({
      start: z.string().datetime(),
      end: z.string().datetime(),
    }).optional(),
  }).optional(),
  metrics: z.array(z.string()),
  visualizations: z.array(z.enum(['line_chart', 'bar_chart', 'pie_chart', 'table', 'heatmap'])),
});

export const analyticsRoutes: FastifyPluginAsync = async (fastify) => {
  // Get overview analytics
  fastify.get('/overview', {
    schema: {
      description: 'Get overview analytics dashboard data',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
      querystring: performanceQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                summary: {
                  type: 'object',
                  properties: {
                    totalReach: { type: 'number' },
                    totalImpressions: { type: 'number' },
                    totalEngagement: { type: 'number' },
                    totalClicks: { type: 'number' },
                    totalConversions: { type: 'number' },
                    totalRevenue: { type: 'number' },
                    averageEngagementRate: { type: 'number' },
                    averageCTR: { type: 'number' },
                    averageConversionRate: { type: 'number' },
                    roi: { type: 'number' },
                  },
                },
                trends: {
                  type: 'object',
                  properties: {
                    reach: { type: 'array' },
                    engagement: { type: 'array' },
                    conversions: { type: 'array' },
                  },
                },
                topPerformers: {
                  type: 'object',
                  properties: {
                    content: { type: 'array' },
                    campaigns: { type: 'array' },
                    platforms: { type: 'array' },
                  },
                },
                insights: { type: 'array', items: { type: 'string' } },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const query = performanceQuerySchema.parse(request.query);
      const userId = request.user?.id;

      // Mock analytics overview data
      const overview = {
        summary: {
          totalReach: 2450000,
          totalImpressions: 8900000,
          totalEngagement: 185000,
          totalClicks: 45000,
          totalConversions: 3200,
          totalRevenue: 128000,
          averageEngagementRate: 2.08,
          averageCTR: 0.51,
          averageConversionRate: 7.11,
          roi: 3.2,
        },
        trends: {
          reach: [
            { date: '2024-01-01', value: 45000 },
            { date: '2024-01-02', value: 52000 },
            { date: '2024-01-03', value: 48000 },
            { date: '2024-01-04', value: 61000 },
            { date: '2024-01-05', value: 58000 },
            { date: '2024-01-06', value: 67000 },
            { date: '2024-01-07', value: 72000 },
          ],
          engagement: [
            { date: '2024-01-01', value: 1200 },
            { date: '2024-01-02', value: 1450 },
            { date: '2024-01-03', value: 1180 },
            { date: '2024-01-04', value: 1680 },
            { date: '2024-01-05', value: 1520 },
            { date: '2024-01-06', value: 1890 },
            { date: '2024-01-07', value: 2100 },
          ],
          conversions: [
            { date: '2024-01-01', value: 28 },
            { date: '2024-01-02', value: 35 },
            { date: '2024-01-03', value: 22 },
            { date: '2024-01-04', value: 48 },
            { date: '2024-01-05', value: 41 },
            { date: '2024-01-06', value: 52 },
            { date: '2024-01-07', value: 58 },
          ],
        },
        topPerformers: {
          content: [
            {
              id: 'content-1',
              title: 'AI Marketing Trends 2024',
              type: 'blog_post',
              platform: 'blog',
              reach: 125000,
              engagement: 8500,
              conversions: 450,
            },
            {
              id: 'content-2',
              title: 'Behind the Scenes Video',
              type: 'video',
              platform: 'instagram',
              reach: 98000,
              engagement: 12000,
              conversions: 320,
            },
          ],
          campaigns: [
            {
              id: 'campaign-1',
              name: 'Q4 Holiday Campaign',
              type: 'social_media',
              reach: 450000,
              engagement: 28000,
              conversions: 1200,
              roi: 4.2,
            },
            {
              id: 'campaign-2',
              name: 'Product Launch Series',
              type: 'content_series',
              reach: 320000,
              engagement: 18500,
              conversions: 850,
              roi: 3.8,
            },
          ],
          platforms: [
            {
              name: 'Instagram',
              reach: 890000,
              engagement: 65000,
              conversions: 1200,
              engagementRate: 7.3,
            },
            {
              name: 'Facebook',
              reach: 650000,
              engagement: 32000,
              conversions: 850,
              engagementRate: 4.9,
            },
            {
              name: 'LinkedIn',
              reach: 420000,
              engagement: 28000,
              conversions: 680,
              engagementRate: 6.7,
            },
          ],
        },
        insights: [
          'Instagram engagement increased by 23% this week',
          'Video content performs 3.2x better than static images',
          'Peak engagement time is between 6-8 PM on weekdays',
          'Mobile users account for 87% of total reach',
          'Blog posts drive 45% of total conversions',
        ],
      };

      return {
        success: true,
        data: overview,
      };
    } catch (error) {
      logger.error('Error fetching analytics overview:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch analytics overview',
      };
    }
  });

  // Get detailed analytics
  fastify.get('/detailed', {
    schema: {
      description: 'Get detailed analytics with custom filters',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
      querystring: analyticsQuerySchema,
    },
  }, async (request, reply) => {
    try {
      const query = analyticsQuerySchema.parse(request.query);
      const userId = request.user?.id;

      // Parse filters
      const metrics = query.metrics?.split(',') || ['reach', 'engagement', 'clicks', 'conversions'];
      const platforms = query.platforms?.split(',') || [];
      const contentTypes = query.contentTypes?.split(',') || [];
      const campaigns = query.campaigns?.split(',') || [];

      // Mock detailed analytics data
      const detailedAnalytics = {
        metadata: {
          dateRange: {
            start: query.startDate,
            end: query.endDate,
          },
          granularity: query.granularity,
          metrics,
          filters: {
            platforms,
            contentTypes,
            campaigns,
          },
        },
        timeSeries: [
          {
            date: '2024-01-01T00:00:00Z',
            reach: 45000,
            impressions: 180000,
            engagement: 1200,
            clicks: 450,
            conversions: 28,
            revenue: 1120,
          },
          {
            date: '2024-01-02T00:00:00Z',
            reach: 52000,
            impressions: 208000,
            engagement: 1450,
            clicks: 520,
            conversions: 35,
            revenue: 1400,
          },
        ],
        breakdown: {
          byPlatform: {
            instagram: {
              reach: 125000,
              engagement: 8500,
              clicks: 2100,
              conversions: 180,
              revenue: 7200,
            },
            facebook: {
              reach: 98000,
              engagement: 4200,
              clicks: 1200,
              conversions: 95,
              revenue: 3800,
            },
            linkedin: {
              reach: 67000,
              engagement: 3800,
              clicks: 890,
              conversions: 78,
              revenue: 3120,
            },
          },
          byContentType: {
            blog_post: {
              reach: 180000,
              engagement: 12000,
              clicks: 3200,
              conversions: 280,
              revenue: 11200,
            },
            social_media: {
              reach: 95000,
              engagement: 8500,
              clicks: 1800,
              conversions: 120,
              revenue: 4800,
            },
            video: {
              reach: 78000,
              engagement: 15000,
              clicks: 2400,
              conversions: 180,
              revenue: 7200,
            },
          },
          byCampaign: {
            'Q4 Holiday Campaign': {
              reach: 220000,
              engagement: 18000,
              clicks: 4200,
              conversions: 350,
              revenue: 14000,
            },
            'Brand Awareness Series': {
              reach: 145000,
              engagement: 9500,
              clicks: 2100,
              conversions: 180,
              revenue: 7200,
            },
          },
        },
        demographics: {
          age: {
            '18-24': { percentage: 18, reach: 54000, engagement: 4200 },
            '25-34': { percentage: 32, reach: 96000, engagement: 6800 },
            '35-44': { percentage: 28, reach: 84000, engagement: 5200 },
            '45-54': { percentage: 15, reach: 45000, engagement: 2100 },
            '55+': { percentage: 7, reach: 21000, engagement: 900 },
          },
          gender: {
            male: { percentage: 48, reach: 144000, engagement: 8500 },
            female: { percentage: 50, reach: 150000, engagement: 9800 },
            other: { percentage: 2, reach: 6000, engagement: 200 },
          },
          location: {
            'United States': { percentage: 55, reach: 165000, engagement: 12000 },
            'Canada': { percentage: 15, reach: 45000, engagement: 2800 },
            'United Kingdom': { percentage: 12, reach: 36000, engagement: 2200 },
            'Australia': { percentage: 8, reach: 24000, engagement: 1500 },
            'Other': { percentage: 10, reach: 30000, engagement: 1500 },
          },
        },
        correlations: {
          engagementVsReach: 0.78,
          clicksVsEngagement: 0.85,
          conversionsVsClicks: 0.72,
          revenueVsConversions: 0.95,
        },
        predictions: {
          nextWeekReach: 385000,
          nextWeekEngagement: 22000,
          nextWeekConversions: 420,
          confidence: 0.82,
        },
      };

      return {
        success: true,
        data: detailedAnalytics,
      };
    } catch (error) {
      logger.error('Error fetching detailed analytics:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch detailed analytics',
      };
    }
  });

  // Get content performance analytics
  fastify.get('/content/:id', {
    schema: {
      description: 'Get analytics for specific content',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      querystring: z.object({
        period: z.enum(['hour', 'day', 'week', 'month']).default('day'),
        includeComparisons: z.boolean().default(false),
      }),
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const query = request.query as any;
      const userId = request.user?.id;

      // Mock content analytics
      const contentAnalytics = {
        content: {
          id,
          title: 'AI Marketing Trends 2024',
          type: 'blog_post',
          platform: 'blog',
          publishedAt: '2024-01-01T10:00:00Z',
          status: 'published',
        },
        performance: {
          reach: 125000,
          impressions: 450000,
          engagement: 8500,
          clicks: 2100,
          conversions: 180,
          revenue: 7200,
          shares: 320,
          saves: 150,
          comments: 85,
          likes: 7940,
        },
        metrics: {
          engagementRate: 6.8,
          clickThroughRate: 0.47,
          conversionRate: 8.57,
          shareRate: 0.26,
          saveRate: 0.12,
          commentRate: 0.07,
        },
        timeline: [
          { hour: 0, views: 120, engagement: 8 },
          { hour: 1, views: 95, engagement: 6 },
          { hour: 2, views: 78, engagement: 4 },
          { hour: 3, views: 65, engagement: 3 },
          { hour: 4, views: 89, engagement: 5 },
          { hour: 5, views: 145, engagement: 12 },
          { hour: 6, views: 280, engagement: 28 },
          { hour: 7, views: 420, engagement: 45 },
          { hour: 8, views: 680, engagement: 78 },
          { hour: 9, views: 950, engagement: 125 },
          { hour: 10, views: 1200, engagement: 180 },
          { hour: 11, views: 1100, engagement: 165 },
          { hour: 12, views: 1350, engagement: 220 },
        ],
        audience: {
          demographics: {
            age: {
              '18-24': 12,
              '25-34': 38,
              '35-44': 32,
              '45-54': 15,
              '55+': 3,
            },
            gender: {
              male: 52,
              female: 46,
              other: 2,
            },
          },
          interests: [
            { name: 'Marketing', percentage: 85 },
            { name: 'Technology', percentage: 72 },
            { name: 'Business', percentage: 68 },
            { name: 'AI/ML', percentage: 58 },
            { name: 'Digital Strategy', percentage: 45 },
          ],
          devices: {
            mobile: 68,
            desktop: 28,
            tablet: 4,
          },
        },
        referrers: [
          { source: 'Direct', visits: 45000, percentage: 36 },
          { source: 'Google Search', visits: 38000, percentage: 30.4 },
          { source: 'Social Media', visits: 25000, percentage: 20 },
          { source: 'Email', visits: 12000, percentage: 9.6 },
          { source: 'Other', visits: 5000, percentage: 4 },
        ],
        insights: [
          'Peak engagement occurred at 12 PM',
          'Mobile users had 15% higher engagement rate',
          'Content performed 23% better than average',
          'Generated 40% more conversions than similar content',
        ],
      };

      return {
        success: true,
        data: contentAnalytics,
      };
    } catch (error) {
      logger.error('Error fetching content analytics:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch content analytics',
      };
    }
  });

  // Create custom report
  fastify.post('/reports', {
    schema: {
      description: 'Create a custom analytics report',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
      body: reportConfigSchema,
    },
  }, async (request, reply) => {
    try {
      const reportConfig = reportConfigSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock report creation
      const newReport = {
        id: Date.now().toString(),
        ...reportConfig,
        userId,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastGenerated: null,
        nextGeneration: reportConfig.schedule ? new Date(Date.now() + 86400000).toISOString() : null,
      };

      logger.info(`Custom report created: ${newReport.id}`);

      reply.code(201);
      return {
        success: true,
        data: newReport,
      };
    } catch (error) {
      logger.error('Error creating custom report:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to create custom report',
      };
    }
  });

  // Get available metrics
  fastify.get('/metrics', {
    schema: {
      description: 'Get available analytics metrics',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
    },
  }, async (request, reply) => {
    try {
      const metrics = {
        engagement: {
          name: 'Engagement',
          description: 'Total engagement (likes, comments, shares, saves)',
          category: 'engagement',
          unit: 'count',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        reach: {
          name: 'Reach',
          description: 'Unique users who saw the content',
          category: 'reach',
          unit: 'count',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        impressions: {
          name: 'Impressions',
          description: 'Total number of times content was displayed',
          category: 'reach',
          unit: 'count',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        clicks: {
          name: 'Clicks',
          description: 'Number of clicks on content or links',
          category: 'engagement',
          unit: 'count',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        conversions: {
          name: 'Conversions',
          description: 'Number of desired actions completed',
          category: 'conversion',
          unit: 'count',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        revenue: {
          name: 'Revenue',
          description: 'Revenue generated from content',
          category: 'conversion',
          unit: 'currency',
          aggregations: ['sum', 'average', 'max', 'min'],
        },
        engagementRate: {
          name: 'Engagement Rate',
          description: 'Engagement divided by reach',
          category: 'rate',
          unit: 'percentage',
          aggregations: ['average', 'max', 'min'],
        },
        clickThroughRate: {
          name: 'Click-Through Rate',
          description: 'Clicks divided by impressions',
          category: 'rate',
          unit: 'percentage',
          aggregations: ['average', 'max', 'min'],
        },
        conversionRate: {
          name: 'Conversion Rate',
          description: 'Conversions divided by clicks',
          category: 'rate',
          unit: 'percentage',
          aggregations: ['average', 'max', 'min'],
        },
        roi: {
          name: 'Return on Investment',
          description: 'Revenue divided by cost',
          category: 'efficiency',
          unit: 'ratio',
          aggregations: ['average', 'max', 'min'],
        },
      };

      return {
        success: true,
        data: {
          metrics,
          categories: ['engagement', 'reach', 'conversion', 'rate', 'efficiency'],
          units: ['count', 'currency', 'percentage', 'ratio'],
          aggregations: ['sum', 'average', 'max', 'min'],
        },
      };
    } catch (error) {
      logger.error('Error fetching metrics:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch metrics',
      };
    }
  });

  // Export analytics data
  fastify.post('/export', {
    schema: {
      description: 'Export analytics data',
      tags: ['Analytics'],
      security: [{ Bearer: [] }],
      body: z.object({
        format: z.enum(['csv', 'xlsx', 'json', 'pdf']),
        data: z.enum(['overview', 'detailed', 'content', 'campaigns']),
        filters: analyticsQuerySchema.omit({ granularity: true }).optional(),
        includeCharts: z.boolean().default(false),
      }),
    },
  }, async (request, reply) => {
    try {
      const exportConfig = request.body as any;
      const userId = request.user?.id;

      // Mock export process
      const exportJob = {
        id: Date.now().toString(),
        status: 'processing',
        format: exportConfig.format,
        data: exportConfig.data,
        createdAt: new Date().toISOString(),
        estimatedCompletion: new Date(Date.now() + 30000).toISOString(), // 30 seconds
        downloadUrl: null,
      };

      logger.info(`Analytics export started: ${exportJob.id}`);

      // Simulate processing time
      setTimeout(() => {
        exportJob.status = 'completed';
        exportJob.downloadUrl = `https://api.example.com/downloads/${exportJob.id}.${exportConfig.format}`;
      }, 5000);

      return {
        success: true,
        data: exportJob,
      };
    } catch (error) {
      logger.error('Error starting analytics export:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to start analytics export',
      };
    }
  });
};