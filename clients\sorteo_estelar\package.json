{"name": "sorteo_estelar", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "check": "tsc -b --noEmit", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "docker:build": "docker build -t sorteo-estelar .", "docker:run": "docker run -p 3000:80 sorteo-estelar", "docker:compose:up": "docker-compose up -d", "docker:compose:down": "docker-compose down", "docker:compose:logs": "docker-compose logs -f", "deploy": "./deploy.sh deploy", "deploy:staging": "./deploy.sh deploy staging", "deploy:health": "./deploy.sh health", "deploy:logs": "./deploy.sh logs", "deploy:restart": "./deploy.sh restart", "prepare": "husky install", "postinstall": "npm run prepare"}, "dependencies": {"clsx": "^2.1.1", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.3.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-plugin-react-dev-locator": "^1.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vite-tsconfig-paths": "^5.1.4"}}