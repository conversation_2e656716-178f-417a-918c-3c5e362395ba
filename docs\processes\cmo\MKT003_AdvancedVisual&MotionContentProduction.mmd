```mermaid
graph TD
    A[Start: Brief/Script from MKT001/MKT002] --> B{Brief Intake & Concept Refinement};
    B -- Validated by TechnicalBriefValidatorAgent --> Ba(Technical Specs Confirmed);
    Ba --> C(Pre-Production Planning);
    C --> D{Asset Production / Filming};
    D -- Graphic Design --> D1(Human Graphic Designers);
    D -- Motion Graphics --> D2(Human Motion Designers);
    D -- Video/Photo Shoot --> D3(Human Videographers/Photographers);
    
    D1 --> E(Post-Production: Graphics Finalization);
    D2 --> E_mg(Post-Production: Motion Graphics Assembly/Render);
    D2 -- Render via RenderTaskSubmitterAgent --> D2_render[Render Farm];
    D2_render --> E_mg;
    D3 --> E_vid(Post-Production: Video Editing, Color, Sound, VFX);

    E --> F{Review & Iteration Cycle};
    E_mg --> F;
    E_vid --> F;
    F -- Review links via ReviewLinkDistributorAgent --> F_stakeholders[Stakeholders];
    F_stakeholders -- Feedback --> F;
    F -- Approved --> G(Finalization & Mastering);

    G --> H(AdvancedAssetManagerAgent: Archive & Metadata);
    H --> I[End: Advanced Asset Ready for MKT004/Distribution];

    %% Inputs/Outputs
    X1(MKT001 Brand Guidelines) --> B;
    X1 --> D1; X1 --> D2; X1 --> D3;
    I --> X2(MKT004: Ad Creative Production);
    I --> X3(Other Distribution Channels);
```
