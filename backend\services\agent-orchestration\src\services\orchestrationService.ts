import { v4 as uuidv4 } from 'uuid';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';
import { AgentService, Agent } from './agentService';
import { WorkflowService, Workflow, WorkflowExecution } from './workflowService';

export interface OrchestrationTask {
  id: string;
  type: 'workflow' | 'agent_task' | 'batch_process';
  name: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'queued' | 'running' | 'completed' | 'failed' | 'cancelled';
  agentId?: string;
  workflowId?: string;
  payload: any;
  result?: any;
  error?: string;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  startedAt?: Date;
  completedAt?: Date;
  retryCount: number;
  maxRetries: number;
}

export interface OrchestrationMetrics {
  totalTasks: number;
  queuedTasks: number;
  runningTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageExecutionTime: number;
  activeAgents: number;
  runningWorkflows: number;
}

export class OrchestrationService {
  private tasks: Map<string, OrchestrationTask> = new Map();
  private taskQueue: string[] = [];
  private runningTasks: Set<string> = new Set();
  private isInitialized = false;
  private processingInterval?: NodeJS.Timeout;

  constructor(
    private agentService: AgentService,
    private workflowService: WorkflowService
  ) {}

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Orchestration Service...');
      
      // Start task processing
      this.startTaskProcessing();
      
      this.isInitialized = true;
      logger.info('Orchestration Service initialized successfully');
    } catch (error) {
      logger.error(error, 'Failed to initialize Orchestration Service');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up Orchestration Service...');
      
      // Stop task processing
      if (this.processingInterval) {
        clearInterval(this.processingInterval);
        this.processingInterval = undefined;
      }
      
      // Cancel all running tasks
      for (const taskId of this.runningTasks) {
        await this.cancelTask(taskId);
      }
      
      this.tasks.clear();
      this.taskQueue.length = 0;
      this.runningTasks.clear();
      this.isInitialized = false;
      
      logger.info('Orchestration Service cleanup completed');
    } catch (error) {
      logger.error(error, 'Error during Orchestration Service cleanup');
      throw error;
    }
  }

  async getStatus(): Promise<string> {
    if (!this.isInitialized) {
      return 'initializing';
    }
    
    const queuedTasks = this.taskQueue.length;
    const runningTasks = this.runningTasks.size;
    
    if (queuedTasks > environment.orchestration.maxConcurrentAgents * 2) {
      return 'overloaded';
    }
    
    if (runningTasks > environment.orchestration.maxConcurrentAgents * 0.8) {
      return 'busy';
    }
    
    return 'healthy';
  }

  async getMetrics(): Promise<OrchestrationMetrics> {
    const tasks = Array.from(this.tasks.values());
    
    const totalTasks = tasks.length;
    const queuedTasks = this.taskQueue.length;
    const runningTasks = this.runningTasks.size;
    const completedTasks = tasks.filter(t => t.status === 'completed').length;
    const failedTasks = tasks.filter(t => t.status === 'failed').length;
    
    // Calculate average execution time
    const completedTasksWithTime = tasks.filter(t => t.status === 'completed' && t.startedAt && t.completedAt);
    const averageExecutionTime = completedTasksWithTime.length > 0
      ? completedTasksWithTime.reduce((sum, t) => {
          const duration = t.completedAt!.getTime() - t.startedAt!.getTime();
          return sum + duration;
        }, 0) / completedTasksWithTime.length
      : 0;
    
    // Get agent and workflow metrics
    const agentMetrics = await this.agentService.getMetrics();
    const workflowMetrics = await this.workflowService.getMetrics();
    
    return {
      totalTasks,
      queuedTasks,
      runningTasks,
      completedTasks,
      failedTasks,
      averageExecutionTime,
      activeAgents: agentMetrics.active,
      runningWorkflows: workflowMetrics.running
    };
  }

  async createTask(data: Partial<OrchestrationTask>): Promise<OrchestrationTask> {
    const task: OrchestrationTask = {
      id: uuidv4(),
      type: data.type || 'agent_task',
      name: data.name || 'Untitled Task',
      description: data.description || '',
      priority: data.priority || 'medium',
      status: 'queued',
      agentId: data.agentId,
      workflowId: data.workflowId,
      payload: data.payload || {},
      createdBy: data.createdBy || 'system',
      organizationId: data.organizationId,
      createdAt: new Date(),
      retryCount: 0,
      maxRetries: data.maxRetries || environment.orchestration.maxRetries
    };

    this.tasks.set(task.id, task);
    this.enqueueTask(task.id);
    
    logger.info(`Task created and queued: ${task.id} (${task.name})`);
    return task;
  }

  async getTasks(organizationId?: string): Promise<OrchestrationTask[]> {
    let tasks = Array.from(this.tasks.values());
    
    if (organizationId) {
      tasks = tasks.filter(t => t.organizationId === organizationId);
    }
    
    return tasks.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getTask(id: string, organizationId?: string): Promise<OrchestrationTask | null> {
    const task = this.tasks.get(id);
    
    if (!task) {
      return null;
    }
    
    if (organizationId && task.organizationId !== organizationId) {
      return null;
    }
    
    return task;
  }

  async cancelTask(id: string): Promise<void> {
    const task = this.tasks.get(id);
    
    if (!task) {
      throw new Error('Task not found');
    }
    
    if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
      throw new Error('Task cannot be cancelled');
    }
    
    // Remove from queue if queued
    if (task.status === 'queued') {
      const queueIndex = this.taskQueue.indexOf(id);
      if (queueIndex > -1) {
        this.taskQueue.splice(queueIndex, 1);
      }
    }
    
    // Remove from running tasks if running
    if (task.status === 'running') {
      this.runningTasks.delete(id);
    }
    
    task.status = 'cancelled';
    task.completedAt = new Date();
    this.tasks.set(id, task);
    
    logger.info(`Task cancelled: ${id}`);
  }

  async retryTask(id: string): Promise<void> {
    const task = this.tasks.get(id);
    
    if (!task) {
      throw new Error('Task not found');
    }
    
    if (task.status !== 'failed') {
      throw new Error('Only failed tasks can be retried');
    }
    
    if (task.retryCount >= task.maxRetries) {
      throw new Error('Maximum retry attempts exceeded');
    }
    
    task.status = 'queued';
    task.retryCount++;
    task.error = undefined;
    task.startedAt = undefined;
    task.completedAt = undefined;
    
    this.tasks.set(id, task);
    this.enqueueTask(id);
    
    logger.info(`Task queued for retry: ${id} (attempt ${task.retryCount}/${task.maxRetries})`);
  }

  private enqueueTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) return;
    
    // Insert task based on priority
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    const taskPriority = priorityOrder[task.priority];
    
    let insertIndex = this.taskQueue.length;
    for (let i = 0; i < this.taskQueue.length; i++) {
      const queuedTask = this.tasks.get(this.taskQueue[i]);
      if (queuedTask && priorityOrder[queuedTask.priority] > taskPriority) {
        insertIndex = i;
        break;
      }
    }
    
    this.taskQueue.splice(insertIndex, 0, taskId);
  }

  private startTaskProcessing(): void {
    this.processingInterval = setInterval(async () => {
      await this.processTaskQueue();
    }, 1000); // Process every second
  }

  private async processTaskQueue(): Promise<void> {
    try {
      // Check if we can process more tasks
      if (this.runningTasks.size >= environment.orchestration.maxConcurrentAgents) {
        return;
      }
      
      // Get next task from queue
      const taskId = this.taskQueue.shift();
      if (!taskId) {
        return;
      }
      
      const task = this.tasks.get(taskId);
      if (!task || task.status !== 'queued') {
        return;
      }
      
      // Start task execution
      await this.executeTask(task);
      
    } catch (error) {
      logger.error(error, 'Error processing task queue');
    }
  }

  private async executeTask(task: OrchestrationTask): Promise<void> {
    try {
      task.status = 'running';
      task.startedAt = new Date();
      this.runningTasks.add(task.id);
      this.tasks.set(task.id, task);
      
      logger.info(`Executing task: ${task.id} (${task.type})`);
      
      let result: any;
      
      switch (task.type) {
        case 'workflow':
          result = await this.executeWorkflowTask(task);
          break;
        case 'agent_task':
          result = await this.executeAgentTask(task);
          break;
        case 'batch_process':
          result = await this.executeBatchProcess(task);
          break;
        default:
          throw new Error(`Unknown task type: ${task.type}`);
      }
      
      task.status = 'completed';
      task.result = result;
      task.completedAt = new Date();
      
      logger.info(`Task completed: ${task.id}`);
      
    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : 'Unknown error';
      task.completedAt = new Date();
      
      logger.error(error, `Task failed: ${task.id}`);
      
      // Auto-retry if within limits
      if (task.retryCount < task.maxRetries) {
        setTimeout(() => {
          this.retryTask(task.id).catch(retryError => {
            logger.error(retryError, `Failed to retry task: ${task.id}`);
          });
        }, 5000); // Retry after 5 seconds
      }
    } finally {
      this.runningTasks.delete(task.id);
      this.tasks.set(task.id, task);
    }
  }

  private async executeWorkflowTask(task: OrchestrationTask): Promise<any> {
    if (!task.workflowId) {
      throw new Error('Workflow ID is required for workflow tasks');
    }
    
    const execution = await this.workflowService.executeWorkflow(task.workflowId, task.payload.variables);
    return { executionId: execution.id };
  }

  private async executeAgentTask(task: OrchestrationTask): Promise<any> {
    if (!task.agentId) {
      throw new Error('Agent ID is required for agent tasks');
    }
    
    // Simulate agent task execution
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    return {
      agentId: task.agentId,
      result: 'Task completed successfully',
      executedAt: new Date()
    };
  }

  private async executeBatchProcess(task: OrchestrationTask): Promise<any> {
    // Simulate batch processing
    const items = task.payload.items || [];
    const results = [];
    
    for (const item of items) {
      // Process each item
      await new Promise(resolve => setTimeout(resolve, 500));
      results.push({ item, processed: true });
    }
    
    return {
      totalItems: items.length,
      processedItems: results.length,
      results
    };
  }
}