name: Monitoring & Performance Analytics

on:
  workflow_dispatch:
    inputs:
      monitoring_action:
        description: 'Monitoring Action'
        required: true
        type: choice
        options:
        - health-check
        - performance-test
        - load-test
        - security-scan
        - dependency-audit
        - lighthouse-audit
        - api-testing
        - database-health
        - setup-monitoring
      test_duration:
        description: 'Test duration in minutes (for load testing)'
        required: false
        default: '5'
        type: string
      target_environment:
        description: 'Target environment'
        required: false
        default: 'production'
        type: choice
        options:
        - production
        - staging
  schedule:
    # Health check every 30 minutes
    - cron: '*/30 * * * *'
    # Performance test daily at 6 AM UTC
    - cron: '0 6 * * *'
    # Security scan weekly on Mondays at 7 AM UTC
    - cron: '0 7 * * 1'

env:
  VPS_HOST: '**************'
  VPS_PORT: '2222'
  VPS_USER: 'admin'
  PRODUCTION_URL: 'https://www.sorteoestelar.com'
  STAGING_URL: 'https://staging.sorteoestelar.com'
  API_URL: 'https://api.sorteoestelar.com'
  MONITORING_URL: 'https://monitoring.sorteoestelar.com'

jobs:
  determine-environment:
    runs-on: ubuntu-latest
    outputs:
      target-url: ${{ steps.env.outputs.url }}
      api-url: ${{ steps.env.outputs.api }}
      environment: ${{ steps.env.outputs.environment }}
    steps:
      - name: Set environment URLs
        id: env
        run: |
          ENV="${{ github.event.inputs.target_environment || 'production' }}"
          echo "environment=$ENV" >> $GITHUB_OUTPUT
          
          if [ "$ENV" == "staging" ]; then
            echo "url=${{ env.STAGING_URL }}" >> $GITHUB_OUTPUT
            echo "api=https://api-staging.sorteoestelar.com" >> $GITHUB_OUTPUT
          else
            echo "url=${{ env.PRODUCTION_URL }}" >> $GITHUB_OUTPUT
            echo "api=${{ env.API_URL }}" >> $GITHUB_OUTPUT
          fi

  health-check:
    runs-on: ubuntu-latest
    needs: determine-environment
    if: github.event.inputs.monitoring_action == 'health-check' || github.event_name == 'schedule'
    steps:
      - name: Frontend health check
        run: |
          echo "=== Frontend Health Check ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          
          # Basic connectivity test
          if curl -f -s -o /dev/null -w "%{http_code}" "$URL" | grep -q "200"; then
            echo "✅ Frontend is responding (HTTP 200)"
          else
            echo "❌ Frontend is not responding"
            exit 1
          fi
          
          # Response time test
          RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" "$URL")
          echo "⏱️  Response time: ${RESPONSE_TIME}s"
          
          if (( $(echo "$RESPONSE_TIME > 5.0" | bc -l) )); then
            echo "⚠️  Slow response time detected (>${RESPONSE_TIME}s)"
          fi
          
          # SSL certificate check
          SSL_EXPIRY=$(echo | openssl s_client -servername $(echo $URL | sed 's|https://||' | sed 's|/.*||') -connect $(echo $URL | sed 's|https://||' | sed 's|/.*||'):443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
          echo "🔒 SSL certificate expires: $SSL_EXPIRY"
          
          # Check SSL expiry (warn if less than 30 days)
          SSL_EXPIRY_EPOCH=$(date -d "$SSL_EXPIRY" +%s)
          CURRENT_EPOCH=$(date +%s)
          DAYS_UNTIL_EXPIRY=$(( (SSL_EXPIRY_EPOCH - CURRENT_EPOCH) / 86400 ))
          
          if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
            echo "⚠️  SSL certificate expires in $DAYS_UNTIL_EXPIRY days"
          else
            echo "✅ SSL certificate is valid for $DAYS_UNTIL_EXPIRY days"
          fi

      - name: API health check
        run: |
          echo "=== API Health Check ==="
          API_URL="${{ needs.determine-environment.outputs.api-url }}"
          
          # API health endpoint
          if curl -f -s "$API_URL/health" | jq -e '.status == "ok"' >/dev/null; then
            echo "✅ API health endpoint is responding"
          else
            echo "❌ API health endpoint is not responding"
            exit 1
          fi
          
          # Database connectivity
          if curl -f -s "$API_URL/health/database" | jq -e '.database == "connected"' >/dev/null; then
            echo "✅ Database connectivity is healthy"
          else
            echo "❌ Database connectivity issues detected"
          fi
          
          # Redis connectivity
          if curl -f -s "$API_URL/health/redis" | jq -e '.redis == "connected"' >/dev/null; then
            echo "✅ Redis connectivity is healthy"
          else
            echo "❌ Redis connectivity issues detected"
          fi

      - name: VPS system health
        run: |
          echo "=== VPS System Health ==="
          
          # Setup SSH
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts
          
          # Check system resources
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "📊 System Resources:"
            
            # CPU usage
            CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk "{print \$2}" | cut -d"%" -f1)
            echo "   CPU: ${CPU_USAGE}%"
            
            # Memory usage
            MEM_USAGE=$(free | grep Mem | awk "{printf \"%.1f\", \$3/\$2 * 100.0}")
            echo "   Memory: ${MEM_USAGE}%"
            
            # Disk usage
            DISK_USAGE=$(df / | tail -1 | awk "{print \$5}" | cut -d"%" -f1)
            echo "   Disk: ${DISK_USAGE}%"
            
            # Load average
            LOAD_AVG=$(uptime | awk -F"load average:" "{print \$2}" | awk "{print \$1}" | cut -d"," -f1)
            echo "   Load: ${LOAD_AVG}"
            
            # Docker containers
            if command -v docker >/dev/null 2>&1; then
              RUNNING_CONTAINERS=$(docker ps -q | wc -l)
              TOTAL_CONTAINERS=$(docker ps -a -q | wc -l)
              echo "   Containers: $RUNNING_CONTAINERS/$TOTAL_CONTAINERS running"
              
              # Check specific containers
              if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "sorteo-estelar"; then
                echo "   ✅ Sorteo Estelar containers are running"
              else
                echo "   ❌ Sorteo Estelar containers are not running"
              fi
            fi
            
            # Service status
            echo "🔧 Services:"
            systemctl is-active docker && echo "   ✅ Docker: Active" || echo "   ❌ Docker: Inactive"
            systemctl is-active nginx && echo "   ✅ Nginx: Active" || echo "   ❌ Nginx: Inactive"
            
            # Network connectivity
            echo "🌐 Network:"
            if ping -c 1 8.8.8.8 >/dev/null 2>&1; then
              echo "   ✅ Internet connectivity: OK"
            else
              echo "   ❌ Internet connectivity: Failed"
            fi
          '

  performance-test:
    runs-on: ubuntu-latest
    needs: determine-environment
    if: github.event.inputs.monitoring_action == 'performance-test' || (github.event_name == 'schedule' && github.event.schedule == '0 6 * * *')
    steps:
      - name: Install performance testing tools
        run: |
          sudo apt-get update
          sudo apt-get install -y apache2-utils curl jq
          npm install -g lighthouse

      - name: Response time analysis
        run: |
          echo "=== Response Time Analysis ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          
          echo "Testing URL: $URL"
          
          # Multiple response time tests
          TOTAL_TIME=0
          TESTS=10
          
          for i in $(seq 1 $TESTS); do
            RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" "$URL")
            echo "Test $i: ${RESPONSE_TIME}s"
            TOTAL_TIME=$(echo "$TOTAL_TIME + $RESPONSE_TIME" | bc -l)
          done
          
          AVG_TIME=$(echo "scale=3; $TOTAL_TIME / $TESTS" | bc -l)
          echo "📊 Average response time: ${AVG_TIME}s"
          
          # Performance thresholds
          if (( $(echo "$AVG_TIME > 2.0" | bc -l) )); then
            echo "⚠️  Performance warning: Average response time > 2s"
          elif (( $(echo "$AVG_TIME > 5.0" | bc -l) )); then
            echo "❌ Performance critical: Average response time > 5s"
            exit 1
          else
            echo "✅ Performance good: Average response time < 2s"
          fi

      - name: Lighthouse audit
        run: |
          echo "=== Lighthouse Performance Audit ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          
          # Run Lighthouse audit
          lighthouse "$URL" --output=json --output-path=./lighthouse-report.json --chrome-flags="--headless --no-sandbox"
          
          # Extract scores
          PERFORMANCE=$(jq -r '.categories.performance.score * 100' lighthouse-report.json)
          ACCESSIBILITY=$(jq -r '.categories.accessibility.score * 100' lighthouse-report.json)
          BEST_PRACTICES=$(jq -r '.categories["best-practices"].score * 100' lighthouse-report.json)
          SEO=$(jq -r '.categories.seo.score * 100' lighthouse-report.json)
          
          echo "📊 Lighthouse Scores:"
          echo "   Performance: ${PERFORMANCE}/100"
          echo "   Accessibility: ${ACCESSIBILITY}/100"
          echo "   Best Practices: ${BEST_PRACTICES}/100"
          echo "   SEO: ${SEO}/100"
          
          # Performance thresholds
          if (( $(echo "$PERFORMANCE < 70" | bc -l) )); then
            echo "⚠️  Performance score below 70"
          fi
          
          if (( $(echo "$ACCESSIBILITY < 90" | bc -l) )); then
            echo "⚠️  Accessibility score below 90"
          fi
          
          # Upload report as artifact
          echo "📄 Lighthouse report generated"

      - name: API performance test
        run: |
          echo "=== API Performance Test ==="
          API_URL="${{ needs.determine-environment.outputs.api-url }}"
          
          # Test various API endpoints
          ENDPOINTS=(
            "/health"
            "/api/v1/games"
            "/api/v1/user/profile"
            "/api/v1/nft/marketplace"
          )
          
          for endpoint in "${ENDPOINTS[@]}"; do
            echo "Testing: $API_URL$endpoint"
            
            # Response time test
            RESPONSE_TIME=$(curl -o /dev/null -s -w "%{time_total}" "$API_URL$endpoint" || echo "failed")
            
            if [ "$RESPONSE_TIME" != "failed" ]; then
              echo "   Response time: ${RESPONSE_TIME}s"
              
              if (( $(echo "$RESPONSE_TIME > 1.0" | bc -l) )); then
                echo "   ⚠️  Slow API response (>1s)"
              else
                echo "   ✅ Good API response (<1s)"
              fi
            else
              echo "   ❌ API endpoint failed"
            fi
          done

  load-test:
    runs-on: ubuntu-latest
    needs: determine-environment
    if: github.event.inputs.monitoring_action == 'load-test'
    steps:
      - name: Install load testing tools
        run: |
          sudo apt-get update
          sudo apt-get install -y apache2-utils

      - name: Frontend load test
        env:
          DURATION: ${{ github.event.inputs.test_duration || '5' }}
        run: |
          echo "=== Frontend Load Test ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          
          echo "Running load test for $DURATION minutes"
          echo "Target: $URL"
          
          # Apache Bench load test
          ab -t $(($DURATION * 60)) -c 10 -g loadtest.tsv "$URL/" > loadtest-results.txt
          
          # Parse results
          echo "📊 Load Test Results:"
          grep "Requests per second" loadtest-results.txt
          grep "Time per request" loadtest-results.txt
          grep "Failed requests" loadtest-results.txt
          
          # Check for failures
          FAILED_REQUESTS=$(grep "Failed requests" loadtest-results.txt | awk '{print $3}')
          if [ "$FAILED_REQUESTS" -gt 0 ]; then
            echo "❌ Load test detected $FAILED_REQUESTS failed requests"
            exit 1
          else
            echo "✅ Load test completed successfully with no failures"
          fi

      - name: API load test
        env:
          DURATION: ${{ github.event.inputs.test_duration || '5' }}
        run: |
          echo "=== API Load Test ==="
          API_URL="${{ needs.determine-environment.outputs.api-url }}"
          
          # Test API health endpoint
          ab -t $(($DURATION * 60)) -c 5 -g api-loadtest.tsv "$API_URL/health" > api-loadtest-results.txt
          
          echo "📊 API Load Test Results:"
          grep "Requests per second" api-loadtest-results.txt
          grep "Time per request" api-loadtest-results.txt
          grep "Failed requests" api-loadtest-results.txt

  security-scan:
    runs-on: ubuntu-latest
    needs: determine-environment
    if: github.event.inputs.monitoring_action == 'security-scan' || (github.event_name == 'schedule' && github.event.schedule == '0 7 * * 1')
    steps:
      - name: SSL/TLS security scan
        run: |
          echo "=== SSL/TLS Security Scan ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          DOMAIN=$(echo $URL | sed 's|https://||' | sed 's|/.*||')
          
          echo "Scanning domain: $DOMAIN"
          
          # Check SSL configuration
          echo "🔒 SSL Configuration:"
          echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -text | grep -E "Signature Algorithm|Public Key|Not Before|Not After"
          
          # Check for weak ciphers
          echo "🔐 Cipher Analysis:"
          nmap --script ssl-enum-ciphers -p 443 $DOMAIN | grep -E "TLS|SSL|cipher"

      - name: HTTP security headers
        run: |
          echo "=== HTTP Security Headers ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          
          echo "Checking security headers for: $URL"
          
          # Get headers
          HEADERS=$(curl -I -s "$URL")
          
          # Check for security headers
          echo "📋 Security Headers Analysis:"
          
          if echo "$HEADERS" | grep -qi "strict-transport-security"; then
            echo "   ✅ HSTS (Strict-Transport-Security) present"
          else
            echo "   ❌ HSTS (Strict-Transport-Security) missing"
          fi
          
          if echo "$HEADERS" | grep -qi "x-frame-options"; then
            echo "   ✅ X-Frame-Options present"
          else
            echo "   ❌ X-Frame-Options missing"
          fi
          
          if echo "$HEADERS" | grep -qi "x-content-type-options"; then
            echo "   ✅ X-Content-Type-Options present"
          else
            echo "   ❌ X-Content-Type-Options missing"
          fi
          
          if echo "$HEADERS" | grep -qi "content-security-policy"; then
            echo "   ✅ Content-Security-Policy present"
          else
            echo "   ❌ Content-Security-Policy missing"
          fi
          
          if echo "$HEADERS" | grep -qi "x-xss-protection"; then
            echo "   ✅ X-XSS-Protection present"
          else
            echo "   ❌ X-XSS-Protection missing"
          fi

      - name: Vulnerability scan
        run: |
          echo "=== Vulnerability Scan ==="
          URL="${{ needs.determine-environment.outputs.target-url }}"
          DOMAIN=$(echo $URL | sed 's|https://||' | sed 's|/.*||')
          
          # Basic port scan
          echo "🔍 Port Scan:"
          nmap -sS -O $DOMAIN | grep -E "PORT|open"
          
          # Check for common vulnerabilities
          echo "🛡️  Common Vulnerability Check:"
          
          # Check for directory traversal
          if curl -s "$URL/../../../etc/passwd" | grep -q "root:"; then
            echo "   ❌ Directory traversal vulnerability detected"
          else
            echo "   ✅ No directory traversal vulnerability"
          fi
          
          # Check for SQL injection (basic)
          if curl -s "$URL/?id=1'" | grep -qi "sql\|mysql\|error"; then
            echo "   ⚠️  Possible SQL injection vulnerability"
          else
            echo "   ✅ No obvious SQL injection vulnerability"
          fi

  dependency-audit:
    runs-on: ubuntu-latest
    if: github.event.inputs.monitoring_action == 'dependency-audit'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: |
          echo "=== Dependency Security Audit ==="
          
          # NPM audit
          echo "📦 NPM Security Audit:"
          npm audit --audit-level=moderate
          
          # Generate audit report
          npm audit --json > audit-report.json
          
          # Parse audit results
          CRITICAL=$(jq '.metadata.vulnerabilities.critical // 0' audit-report.json)
          HIGH=$(jq '.metadata.vulnerabilities.high // 0' audit-report.json)
          MODERATE=$(jq '.metadata.vulnerabilities.moderate // 0' audit-report.json)
          LOW=$(jq '.metadata.vulnerabilities.low // 0' audit-report.json)
          
          echo "📊 Vulnerability Summary:"
          echo "   Critical: $CRITICAL"
          echo "   High: $HIGH"
          echo "   Moderate: $MODERATE"
          echo "   Low: $LOW"
          
          # Fail if critical or high vulnerabilities
          if [ "$CRITICAL" -gt 0 ] || [ "$HIGH" -gt 0 ]; then
            echo "❌ Critical or high severity vulnerabilities detected"
            exit 1
          else
            echo "✅ No critical or high severity vulnerabilities"
          fi

      - name: License compliance check
        run: |
          echo "=== License Compliance Check ==="
          
          # Install license checker
          npm install -g license-checker
          
          # Generate license report
          license-checker --json > licenses.json
          
          # Check for problematic licenses
          PROBLEMATIC_LICENSES=("GPL-2.0" "GPL-3.0" "AGPL-1.0" "AGPL-3.0")
          
          echo "📄 License Analysis:"
          for license in "${PROBLEMATIC_LICENSES[@]}"; do
            if jq -r '.[] | .licenses' licenses.json | grep -q "$license"; then
              echo "   ⚠️  Found potentially problematic license: $license"
            fi
          done
          
          echo "✅ License compliance check completed"

  database-health:
    runs-on: ubuntu-latest
    if: github.event.inputs.monitoring_action == 'database-health'
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Database health check
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Database Health Check ==="
            
            # PostgreSQL health
            if docker ps | grep -q postgres; then
              echo "🐘 PostgreSQL Status:"
              
              # Connection test
              if docker exec sorteo-estelar-postgres pg_isready -U postgres; then
                echo "   ✅ PostgreSQL is accepting connections"
              else
                echo "   ❌ PostgreSQL is not accepting connections"
              fi
              
              # Database size
              DB_SIZE=$(docker exec sorteo-estelar-postgres psql -U postgres -d sorteo_estelar -t -c "SELECT pg_size_pretty(pg_database_size(current_database()));" | xargs)
              echo "   📊 Database size: $DB_SIZE"
              
              # Active connections
              ACTIVE_CONN=$(docker exec sorteo-estelar-postgres psql -U postgres -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active';" | xargs)
              echo "   🔗 Active connections: $ACTIVE_CONN"
              
              # Slow queries
              SLOW_QUERIES=$(docker exec sorteo-estelar-postgres psql -U postgres -t -c "SELECT count(*) FROM pg_stat_activity WHERE state = 'active' AND query_start < now() - interval '1 minute';" | xargs)
              echo "   🐌 Slow queries (>1min): $SLOW_QUERIES"
              
              # Table sizes
              echo "   📋 Largest tables:"
              docker exec sorteo-estelar-postgres psql -U postgres -d sorteo_estelar -c "SELECT schemaname,tablename,pg_size_pretty(size) as size FROM (SELECT schemaname,tablename,pg_relation_size(schemaname||'.'||tablename) as size FROM pg_tables WHERE schemaname NOT IN ('information_schema','pg_catalog')) x ORDER BY size DESC LIMIT 5;"
            else
              echo "❌ PostgreSQL container not found"
            fi
            
            echo ""
            
            # Redis health
            if docker ps | grep -q redis; then
              echo "🔴 Redis Status:"
              
              # Connection test
              if docker exec sorteo-estelar-redis redis-cli ping | grep -q PONG; then
                echo "   ✅ Redis is responding to ping"
              else
                echo "   ❌ Redis is not responding"
              fi
              
              # Memory usage
              REDIS_MEMORY=$(docker exec sorteo-estelar-redis redis-cli info memory | grep used_memory_human | cut -d: -f2 | tr -d '\r')
              echo "   💾 Memory usage: $REDIS_MEMORY"
              
              # Connected clients
              REDIS_CLIENTS=$(docker exec sorteo-estelar-redis redis-cli info clients | grep connected_clients | cut -d: -f2 | tr -d '\r')
              echo "   👥 Connected clients: $REDIS_CLIENTS"
              
              # Key count
              KEY_COUNT=$(docker exec sorteo-estelar-redis redis-cli dbsize)
              echo "   🔑 Total keys: $KEY_COUNT"
            else
              echo "❌ Redis container not found"
            fi
          '

  setup-monitoring:
    runs-on: ubuntu-latest
    if: github.event.inputs.monitoring_action == 'setup-monitoring'
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Install monitoring stack
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Setting up Monitoring Stack ==="
            
            # Create monitoring directory
            sudo mkdir -p /opt/monitoring/{prometheus,grafana,alertmanager}
            cd /opt/monitoring
            
            # Create docker-compose for monitoring
            cat > docker-compose.monitoring.yml <<EOF
            version: "3.8"
            services:
              prometheus:
                image: prom/prometheus:latest
                container_name: prometheus
                restart: unless-stopped
                ports:
                  - "9090:9090"
                volumes:
                  - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
                  - prometheus_data:/prometheus
                command:
                  - "--config.file=/etc/prometheus/prometheus.yml"
                  - "--storage.tsdb.path=/prometheus"
                  - "--web.console.libraries=/etc/prometheus/console_libraries"
                  - "--web.console.templates=/etc/prometheus/consoles"
                  - "--storage.tsdb.retention.time=200h"
                  - "--web.enable-lifecycle"
                networks:
                  - monitoring
            
              grafana:
                image: grafana/grafana:latest
                container_name: grafana
                restart: unless-stopped
                ports:
                  - "3001:3000"
                environment:
                  - GF_SECURITY_ADMIN_PASSWORD=${{ secrets.GRAFANA_ADMIN_PASSWORD }}
                  - GF_SERVER_DOMAIN=${{ env.MONITORING_URL }}
                volumes:
                  - grafana_data:/var/lib/grafana
                  - ./grafana/provisioning:/etc/grafana/provisioning
                networks:
                  - monitoring
            
              node-exporter:
                image: prom/node-exporter:latest
                container_name: node-exporter
                restart: unless-stopped
                ports:
                  - "9100:9100"
                volumes:
                  - /proc:/host/proc:ro
                  - /sys:/host/sys:ro
                  - /:/rootfs:ro
                command:
                  - "--path.procfs=/host/proc"
                  - "--path.rootfs=/rootfs"
                  - "--path.sysfs=/host/sys"
                  - "--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)"
                networks:
                  - monitoring
            
              cadvisor:
                image: gcr.io/cadvisor/cadvisor:latest
                container_name: cadvisor
                restart: unless-stopped
                ports:
                  - "8080:8080"
                volumes:
                  - /:/rootfs:ro
                  - /var/run:/var/run:ro
                  - /sys:/sys:ro
                  - /var/lib/docker/:/var/lib/docker:ro
                  - /dev/disk/:/dev/disk:ro
                networks:
                  - monitoring
            
            volumes:
              prometheus_data:
              grafana_data:
            
            networks:
              monitoring:
                driver: bridge
            EOF
            
            # Create Prometheus configuration
            mkdir -p prometheus
            cat > prometheus/prometheus.yml <<EOF
            global:
              scrape_interval: 15s
              evaluation_interval: 15s
            
            scrape_configs:
              - job_name: "prometheus"
                static_configs:
                  - targets: ["localhost:9090"]
            
              - job_name: "node-exporter"
                static_configs:
                  - targets: ["node-exporter:9100"]
            
              - job_name: "cadvisor"
                static_configs:
                  - targets: ["cadvisor:8080"]
            
              - job_name: "sorteo-estelar-frontend"
                static_configs:
                  - targets: ["${{ env.VPS_HOST }}:80"]
                metrics_path: "/metrics"
            
              - job_name: "sorteo-estelar-api"
                static_configs:
                  - targets: ["${{ env.VPS_HOST }}:3001"]
                metrics_path: "/api/metrics"
            EOF
            
            # Start monitoring stack
            docker-compose -f docker-compose.monitoring.yml up -d
            
            # Configure firewall
            sudo ufw allow 9090/tcp comment "Prometheus"
            sudo ufw allow 3001/tcp comment "Grafana"
            
            echo "✅ Monitoring stack deployed successfully"
            echo "🔗 Prometheus: http://${{ env.VPS_HOST }}:9090"
            echo "📊 Grafana: http://${{ env.VPS_HOST }}:3001"
          '

  generate-report:
    runs-on: ubuntu-latest
    needs: [determine-environment, health-check, performance-test, load-test, security-scan, dependency-audit, database-health]
    if: always()
    steps:
      - name: Generate monitoring report
        run: |
          echo "=== Monitoring & Performance Report ==="
          echo "Timestamp: $(date -Iseconds)"
          echo "Triggered by: ${{ github.actor }}"
          echo "Action: ${{ github.event.inputs.monitoring_action || github.event_name }}"
          echo "Environment: ${{ needs.determine-environment.outputs.environment }}"
          echo "Target URL: ${{ needs.determine-environment.outputs.target-url }}"
          echo ""
          
          echo "📊 Job Results:"
          echo "   Health Check: ${{ needs.health-check.result || 'skipped' }}"
          echo "   Performance Test: ${{ needs.performance-test.result || 'skipped' }}"
          echo "   Load Test: ${{ needs.load-test.result || 'skipped' }}"
          echo "   Security Scan: ${{ needs.security-scan.result || 'skipped' }}"
          echo "   Dependency Audit: ${{ needs.dependency-audit.result || 'skipped' }}"
          echo "   Database Health: ${{ needs.database-health.result || 'skipped' }}"
          echo ""
          
          # Determine overall status
          FAILED_JOBS=0
          
          [ "${{ needs.health-check.result }}" == "failure" ] && ((FAILED_JOBS++))
          [ "${{ needs.performance-test.result }}" == "failure" ] && ((FAILED_JOBS++))
          [ "${{ needs.load-test.result }}" == "failure" ] && ((FAILED_JOBS++))
          [ "${{ needs.security-scan.result }}" == "failure" ] && ((FAILED_JOBS++))
          [ "${{ needs.dependency-audit.result }}" == "failure" ] && ((FAILED_JOBS++))
          [ "${{ needs.database-health.result }}" == "failure" ] && ((FAILED_JOBS++))
          
          if [ $FAILED_JOBS -eq 0 ]; then
            echo "✅ All monitoring checks passed successfully"
          else
            echo "❌ $FAILED_JOBS monitoring check(s) failed"
            exit 1
          fi
          
          echo ""
          echo "📈 Next Steps:"
          echo "   - Review detailed logs for any warnings"
          echo "   - Check monitoring dashboards for trends"
          echo "   - Schedule regular performance optimizations"
          echo "   - Update security configurations as needed"