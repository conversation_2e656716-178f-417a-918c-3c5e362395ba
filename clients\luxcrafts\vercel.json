{"version": 2, "buildCommand": "node --max-old-space-size=32768 --max-semi-space-size=1024 node_modules/vite/bin/vite.js build", "installCommand": "npm install", "outputDirectory": "dist", "framework": "vite", "env": {"VITE_APP_ENVIRONMENT": "staging", "VITE_WALLETCONNECT_PROJECT_ID": "demo", "VITE_ALCHEMY_API_KEY": "demo", "VITE_INFURA_API_KEY": "demo", "VITE_API_BASE_URL": "https://api.luxcrafts.co", "VITE_CHAIN_ID": "1", "VITE_CONTRACT_ADDRESS": "******************************************", "VITE_LUX_TOKEN_ADDRESS": "******************************************"}, "build": {"env": {"VITE_APP_ENVIRONMENT": "staging", "VITE_WALLETCONNECT_PROJECT_ID": "demo", "VITE_ALCHEMY_API_KEY": "demo", "VITE_INFURA_API_KEY": "demo", "VITE_API_BASE_URL": "https://api.luxcrafts.co", "VITE_CHAIN_ID": "1", "VITE_CONTRACT_ADDRESS": "******************************************", "VITE_LUX_TOKEN_ADDRESS": "******************************************"}}, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}