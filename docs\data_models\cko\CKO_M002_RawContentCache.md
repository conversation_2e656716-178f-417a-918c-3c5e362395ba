# CKO_M002: RawContentCache

## 1. Metadata

*   **Data Model ID:** CKO_M002
*   **Data Model Name:** RawContentCache
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A002_DataIngestionAgent

## 2. Purpose

*   This data model represents raw, unprocessed content fetched from an external knowledge source (as defined in `CKO_M001_KnowledgeSourceRegistry`). It serves as a temporary cache before the content is cleaned, structured, and transformed into a `CKO_M003_CuratedKnowledgeAsset`.

## 3. Pydantic Model Definition

```python
from typing import Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
import uuid

class ContentFormatEnum(str, Enum):
    HTML = "HTML"
    JSON = "JSON"
    XML = "XML"
    TEXT = "TEXT"
    PDF = "PDF"
    BINARY = "BINARY"
    OTHER = "OTHER"

class RawContentCache(BaseModel):
    cache_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for this cached content item.")
    source_registry_id: uuid.UUID = Field(..., description="Identifier of the knowledge source from CKO_M001_KnowledgeSourceRegistry.")
    source_url_snapshot: Union[HttpUrl, str] = Field(..., description="The specific URL from which this content was fetched (could be more granular than source_url in CKO_M001, e.g., a specific article URL).")
    
    fetch_timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp when the content was fetched.")
    content_hash: str = Field(..., description="Hash (e.g., SHA256) of the raw content to detect duplicates or changes.")
    raw_content: Union[str, bytes] = Field(..., description="The actual fetched content. Can be string (for text-based formats) or bytes (for binary formats).")
    content_format_detected: ContentFormatEnum = Field(..., description="Detected format of the raw content.")
    
    http_headers: Optional[Dict[str, str]] = Field(None, description="HTTP headers received with the content (e.g., Content-Type, ETag).")
    http_status_code: Optional[int] = Field(None, description="HTTP status code from the fetch operation.")
    
    # Processing Status
    is_processed: bool = Field(default=False, description="Flag indicating if this raw content has been processed into a CuratedKnowledgeAsset.")
    processing_attempts: int = Field(default=0, description="Number of times processing has been attempted on this content.")
    last_processing_attempt_timestamp: Optional[datetime] = Field(None, description="Timestamp of the last attempt to process this content.")
    
    # Retention
    cache_expiry_timestamp: Optional[datetime] = Field(None, description="Timestamp when this cached item should be considered for deletion if not processed.")
    
    metadata_extracted: Optional[Dict[str, Any]] = Field(None, description="Any preliminary metadata extracted during fetching (e.g., page title from HTML, basic API response info).")

```

## 4. Field Descriptions

| Field Name                        | Type                             | Description                                                                                    | Required | Example Value(s)                                      |
|-----------------------------------|----------------------------------|------------------------------------------------------------------------------------------------|----------|-------------------------------------------------------|
| `cache_id`                        | `uuid.UUID`                      | Unique identifier for this cached content item.                                                | Yes      | `"a1b2c3d4-e5f6-7890-1234-567890abcdef"`              |
| `source_registry_id`              | `uuid.UUID`                      | Identifier of the knowledge source from `CKO_M001`.                                            | Yes      | `"123e4567-e89b-12d3-a456-************"`              |
| `source_url_snapshot`             | `Union[HttpUrl, str]`            | The specific URL from which this content was fetched.                                          | Yes      | `"https://example.com/article/123"`                   |
| `fetch_timestamp`                 | `datetime`                       | Timestamp when the content was fetched.                                                        | Yes      | `"2025-05-26T19:00:00Z"`                              |
| `content_hash`                    | `str`                            | Hash (e.g., SHA256) of the raw content.                                                        | Yes      | `"e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855"` |
| `raw_content`                     | `Union[str, bytes]`              | The actual fetched content.                                                                    | Yes      | `"<html>...</html>"`, `b'\x89PNG...'`                  |
| `content_format_detected`         | `ContentFormatEnum`              | Detected format of the raw content.                                                            | Yes      | `"HTML"`, `"JSON"`                                    |
| `http_headers`                    | `Optional[Dict[str, str]]`       | HTTP headers received with the content.                                                        | No       | `{"Content-Type": "text/html", "ETag": "xyz"}`        |
| `http_status_code`                | `Optional[int]`                  | HTTP status code from the fetch operation.                                                     | No       | `200`, `404`                                          |
| `is_processed`                    | `bool`                           | Flag indicating if this raw content has been processed.                                        | Yes      | `False`, `True`                                       |
| `processing_attempts`             | `int`                            | Number of times processing has been attempted.                                                 | Yes      | `0`, `1`                                              |
| `last_processing_attempt_timestamp` | `Optional[datetime]`             | Timestamp of the last attempt to process this content.                                         | No       | `"2025-05-26T19:05:00Z"`                              |
| `cache_expiry_timestamp`          | `Optional[datetime]`             | Timestamp when this cached item should be considered for deletion.                             | No       | `"2025-05-27T19:00:00Z"`                              |
| `metadata_extracted`              | `Optional[Dict[str, Any]]`       | Preliminary metadata extracted during fetching.                                                | No       | `{"title": "Example Article"}`                        |

## 5. Relationships to Other Data Models

*   **`source_registry_id` (links to `CKO_M001_KnowledgeSourceRegistry.source_id`):** Foreign key linking to the specific knowledge source.
*   This model is a precursor to `CKO_M003_CuratedKnowledgeAsset`. One `RawContentCache` instance may lead to one or more `CuratedKnowledgeAsset` instances if the raw content contains multiple distinct pieces of information, or zero if the content is irrelevant or unusable.

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F001_ExternalKnowledgeIngestionAndCuration` (specifically `CKO_T002_ExecuteKnowledgeAcquisition`). Created by `CKO_A002_DataIngestionAgent`.
*   **Primary Consuming Flow(s)/Process(es):** `CKO_F001_ExternalKnowledgeIngestionAndCuration` (specifically `CKO_P004_ProcessAndStructureKnowledge` and `CKO_P005_KnowledgeEnrichmentAndContextualization`). Consumed by `CKO_A003_ContentCurationAgent`.
*   **Key Agents Interacting:** `CKO_A002_DataIngestionAgent` (creates), `CKO_A003_ContentCurationAgent` (reads, updates `is_processed` status).

## 7. Notes / Future Considerations

*   The `raw_content` field could potentially store large amounts of data. Consideration should be given to storage mechanisms (e.g., database BLOB, file system, object storage like S3) depending on the expected size and volume of content.
*   A strategy for managing `cache_expiry_timestamp` and purging old/unprocessed raw content is important to prevent unbounded growth of the cache.
*   Error handling for fetch failures should be robust, potentially logging errors separately and updating `CKO_M001_KnowledgeSourceRegistry.error_count`.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                               |
| :------ | :--------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------ |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M002 to CKO_M002. Updated ID, version, owner, SME, and all internal KNO references to CKO. Updated Last Updated date. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the RawContentCache data model.                                                                              |
