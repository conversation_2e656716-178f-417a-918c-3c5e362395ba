---
description: "Enforce all AWS resources managed by Terraform include a standard set of ESTRATIX tags for cost tracking and ownership."
globs: "[''**/*.tf'']"
alwaysApply: true
---

# Terraform Required Tags Enforcement

## Context

- This rule applies to all Terraform resource definitions (`.tf` files) within the project.
- A consistent tagging strategy is essential for cloud governance, including cost allocation, automation, access control, and resource lifecycle management.

## Requirements

- Every `resource` block in Terraform **MUST** include a `tags` argument.
- The `tags` map **MUST** contain the following keys: `estratix:project-id`, `estratix:environment`, and `estratix:owner`.
- The values for these tags must not be empty strings.

## Examples

<example>
A compliant AWS S3 bucket resource with the required ESTRATIX tags.

```terraform
resource "aws_s3_bucket" "data" {
  bucket = "my-estratix-data-bucket"

  tags = {
    "estratix:project-id" = "ES-INTERNAL-001"
    "estratix:environment"  = "development"
    "estratix:owner"        = "CTOOffice"
  }
}
```
</example>

<example type="invalid">
A non-compliant AWS instance resource that is missing the required tags.

```terraform
resource "aws_instance" "web_server" {
  ami           = "ami-0c55b159cbfafe1f0"
  instance_type = "t2.micro"

  # Missing the entire tags block.
}
```
</example>

## Critical Rules

- **ALWAYS** apply the standard ESTRATIX tags to every resource you define.
- **NEVER** deploy untagged resources, as they are untraceable and violate governance policies.
