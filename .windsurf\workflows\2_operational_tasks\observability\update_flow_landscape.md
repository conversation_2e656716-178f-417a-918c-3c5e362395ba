---
description: Generates the Flow_Landscape.mmd diagram based on the flow_matrix.md and process matrices, showing how ESTRATIX Flows orchestrate ESTRATIX Processes.
---

# Flow: Update Flow Landscape Diagram

This flow reads `docs/flows/flow_matrix.md` as the primary source for defined ESTRATIX Flows and their constituent processes. It then cross-references `docs/processes/primary_activities_process_matrix.md` and `docs/processes/support_activities_process_matrix.md` to fetch details for these processes. The output is the `docs/flows/diagrams/Flow_Landscape.mmd` diagram.

## Steps:

1.  **Read Flow Matrix (`flow_matrix.md`):**
    *   Action: Read the entire content of `docs/flows/flow_matrix.md`.
    *   Tool: `mcp9_read_file`.
    *   Parse it to get a list of all defined ESTRATIX Flows. For each Flow, store:
        *   `Flow ID` (e.g., `CPO_F001`)
        *   `Flow Name`
        *   `Responsible Command Office Acronym`
        *   `Status`
        *   `Link to Definition` (e.g., `docs/flows/definitions/cpo/CPO_F001_ClientOnboardingFlow.md` - this path should be relative from the matrix file or absolute, the workflow will calculate relative path for diagram.)
        *   `Constituent Process IDs` (comma-separated string, e.g., `CPO_P001,CTO_P002`)

2.  **Read Process Matrices:**
    *   Action: Read the entire content of `docs/processes/primary_activities_process_matrix.md` and `docs/processes/support_activities_process_matrix.md`.
    *   Tool: `mcp9_read_multiple_files` or two separate `mcp9_read_file` calls.
    *   Parse these matrices to create a lookup dictionary for process details, keyed by `Process ID`. For each process, store:
        *   `Process Name`
        *   `Status` (process status)
        *   `Link to Definition` (process definition link, e.g., `docs/processes/definitions/cpo/CPO_P001_DefineProcessItself.md` - relative from matrix or absolute)

3.  **Generate Mermaid Diagram Syntax (`Flow_Landscape.mmd`):**
    *   Action: Construct the Mermaid `graph TD;` syntax string.
    *   Start with `graph TD;`.
    *   Define standard CSS classes for statuses (e.g., `planning`, `draft`, `active`, `deprecated`, and potentially classes for each Command Office Acronym if styling by office is desired).
        *   Example: `classDef planning fill:#f9f,stroke:#333,stroke-width:2px;`

    *   **Flow Subgraphs:**
        *   For each Flow parsed from `flow_matrix.md`:
            *   Sanitize `Flow ID` for use in Mermaid node/subgraph IDs (e.g., replace special characters with underscores if any, though standard IDs shouldn't have them).
            *   Determine the correct relative path for the Flow definition link from `docs/flows/diagrams/` to `docs/flows/definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md`. (e.g., `../definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md`).
            *   Create a top-level subgraph: `subgraph SG_F_[SanitizedFlowID] ["([ResponsibleCommandOfficeAcronym]) [Flow_ID]: [Flow Name]"]`
            *   Add a click event for the subgraph: `click SG_F_[SanitizedFlowID] "[RelativePathToFlowDefinition]" "View [Flow_ID] Definition" _blank;`
            *   Apply a class to the subgraph based on its `Status` (e.g., `:::flowStatus`).

            *   **Process Nodes within Flows:**
                *   Split the `Constituent Process IDs` string into a list of individual Process IDs.
                *   For each `Process ID` in the list:
                    *   Look up its details (Name, Status, Definition Link) from the process data (Step 2).
                    *   If the process is found:
                        *   Sanitize `Process ID` for Mermaid node IDs.
                        *   Determine the correct relative path for the process definition link from `docs/flows/diagrams/` to `docs/processes/definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md` (e.g., `../../processes/definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md`).
                        *   Create a process node inside the current Flow's subgraph: `    P_[SanitizedProcessID]["[Process_ID]: [Process_Name]"]:::processStatus;` (Note: `processStatus` should be the actual status class, e.g. `planning`, `active`)
                        *   Add a click event for the process node: `    click P_[SanitizedProcessID] "[RelativePathToProcessDefinition]" "View [Process_ID] Definition" _blank;`
                    *   Else (process not found in matrices):
                        *   Create a placeholder node indicating it's missing or undefined: `    P_[SanitizedProcessID]["[Process_ID]: (Undefined Process)"]:::missing;`
                *   *(Future Enhancement): If sequence information for processes within a Flow is available (e.g., from the Flow definition file itself, which would require parsing that too), add arrows between process nodes: `P_PROCESS_A --> P_PROCESS_B;`.*
            *   End the subgraph: `end`

4.  **Write/Update Flow Landscape Diagram File:**
    *   Action: Write the generated Mermaid syntax to `docs/flows/diagrams/Flow_Landscape.mmd`. If the file exists, overwrite it.
    *   Tool: `mcp9_write_file`.

## Outputs:

*   An updated `docs/flows/diagrams/Flow_Landscape.mmd` file.

## Dependencies:

*   `docs/flows/flow_matrix.md`
*   `docs/processes/primary_activities_process_matrix.md`
*   `docs/processes/support_activities_process_matrix.md`