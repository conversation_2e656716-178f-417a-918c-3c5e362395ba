# CKO_M008: KnowledgeGraphNode

## 1. Metadata

*   **Data Model ID:** CKO_M008
*   **Data Model Name:** KnowledgeGraphNode
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A004_KnowledgeAnalystAgent

## 2. Purpose

*   This data model represents a node in the ESTRATIX Knowledge Graph. Nodes typically represent entities (e.g., organizations, people, technologies, concepts) extracted or inferred from `CKO_M003_CuratedKnowledgeAsset`s. Edges (relationships) between these nodes are defined separately or as part of the node's properties.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
import uuid

class NodeTypeEnum(str, Enum):
    ORGANIZATION = "Organization"
    PERSON = "Person"
    TECHNOLOGY = "Technology"
    PRODUCT = "Product"
    CONCEPT = "Concept"
    EVENT = "Event"
    LOCATION = "Location"
    RESEARCH_AREA = "Research Area"
    INDUSTRY = "Industry"
    OTHER = "Other"

class KnowledgeGraphNode(BaseModel):
    node_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the knowledge graph node.")
    node_label: str = Field(..., description="Primary human-readable label or name for the node (e.g., 'Acme Corp', 'Artificial Intelligence').")
    node_type: NodeTypeEnum = Field(..., description="The type of entity this node represents.")
    
    description: Optional[str] = Field(None, description="A brief description of the node/entity.")
    aliases: List[str] = Field(default_factory=list, description="Alternative names or synonyms for this node.")
    
    # Properties and Attributes
    properties: Dict[str, Any] = Field(default_factory=dict, description="Key-value pairs representing attributes of the node (e.g., {'founding_year': 1990, 'ceo': 'Jane Doe'}).")
    
    # Relationships (can be represented as properties or separate edge models)
    # Example: Storing direct relationships as a list of target node_ids with relationship type
    relationships: Optional[List[Dict[str, Union[uuid.UUID, str]]]] = Field(
        default_factory=list, 
        description="List of relationships. Each dict: {'target_node_id': uuid, 'relationship_type': str, 'properties': {Optional[Dict]}}"
    )
    # Example relationship dict: {'target_node_id': '...', 'relationship_type': 'EMPLOYS', 'properties': {'role': 'CEO'}}

    # Provenance
    source_asset_ids: List[uuid.UUID] = Field(default_factory=list, description="List of CKO_M003_CuratedKnowledgeAsset IDs from which this node's information was derived or confirmed.")
    confidence_score: float = Field(default=1.0, description="Confidence in the existence and accuracy of this node and its properties (0.0-1.0).")
    creation_date: datetime = Field(default_factory=datetime.utcnow, description="Date this node was created in the graph.")
    last_updated_date: datetime = Field(default_factory=datetime.utcnow, description="Date this node was last updated.")
    
    # External Identifiers
    external_uris: Optional[Dict[str, Union[HttpUrl, str]]] = Field(None, description="Links to external representations of this entity (e.g., {'wikidata': 'Q12345', 'crunchbase': '...'}).")

    # Graph-specific metadata
    degree: Optional[int] = Field(None, description="Number of relationships connected to this node (calculated dynamically or stored).")
    centrality_score: Optional[float] = Field(None, description="A measure of the node's importance in the graph (e.g., PageRank, betweenness centrality).")

    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, node-type-specific metadata.")

```

## 4. Field Descriptions

| Field Name             | Type                                                 | Description                                                                                             | Required | Example Value(s)                                                              |
|------------------------|------------------------------------------------------|---------------------------------------------------------------------------------------------------------|----------|-------------------------------------------------------------------------------|
| `node_id`              | `uuid.UUID`                                          | Unique identifier for the knowledge graph node.                                                         | Yes      | `"e6f7g8h9-..."`                                                             |
| `node_label`           | `str`                                                | Primary human-readable label or name for the node.                                                      | Yes      | `"Acme Corp"`, `"Artificial Intelligence"`                                      |
| `node_type`            | `NodeTypeEnum`                                       | The type of entity this node represents.                                                                | Yes      | `"Organization"`, `"Technology"`                                                |
| `description`          | `Optional[str]`                                      | A brief description of the node/entity.                                                                 | No       | `"A leading provider of AI solutions."`                                       |
| `aliases`              | `List[str]`                                          | Alternative names or synonyms for this node.                                                            | Yes      | `["Acme Inc.", "Acme International"]`                                           |
| `properties`           | `Dict[str, Any]`                                     | Key-value pairs representing attributes of the node.                                                    | Yes      | `{"founding_year": 1990, "ceo_node_id": "person_node_xyz"}`                   |
| `relationships`        | `Optional[List[Dict[str, Union[uuid.UUID, str]]]]` | List of relationships to other nodes.                                                                   | No       | `[{"target_node_id": "node_abc", "relationship_type": "SUBSIDIARY_OF"}]` |
| `source_asset_ids`     | `List[uuid.UUID]`                                    | List of `CKO_M003` asset IDs from which this node's info was derived.                                   | Yes      | `["c1d2e3f4-..."]`                                                             |
| `confidence_score`     | `float`                                              | Confidence in the accuracy of this node (0.0-1.0).                                                      | Yes      | `0.9`                                                                         |
| `creation_date`        | `datetime`                                           | Date this node was created in the graph.                                                                | Yes      | `"2025-05-26T22:00:00Z"`                                                      |
| `last_updated_date`    | `datetime`                                           | Date this node was last updated.                                                                        | Yes      | `"2025-05-26T22:05:00Z"`                                                      |
| `external_uris`        | `Optional[Dict[str, Union[HttpUrl, str]]]`           | Links to external representations of this entity.                                                       | No       | `{"wikidata": "https://www.wikidata.org/wiki/Q12345"}`                      |
| `degree`               | `Optional[int]`                                      | Number of relationships connected to this node.                                                         | No       | `15`                                                                          |
| `centrality_score`     | `Optional[float]`                                    | A measure of the node's importance in the graph.                                                        | No       | `0.0025`                                                                      |
| `custom_fields`        | `Optional[Dict[str, Any]]`                           | Flexible dictionary for additional metadata.                                                            | No       | `{"stock_symbol": "ACME"}`                                                    |

## 5. Relationships to Other Data Models

*   **`source_asset_ids` (references `CKO_M003_CuratedKnowledgeAsset.asset_id`):** Indicates the curated assets that provide evidence or information for this node.
*   The `relationships` field directly links to other `CKO_M008_KnowledgeGraphNode.node_id` instances.
*   Knowledge Graph Edges/Relationships might be defined as a separate model (`CKO_M009_KnowledgeGraphEdge`) for more complex relationship attributes, especially if using a graph database that models edges as first-class citizens.

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (specifically `CKO_P007_BuildAndUpdateKnowledgeGraph`). Created/updated by `CKO_A004_KnowledgeAnalystAgent` or specialized graph construction agents.
*   **Primary Consuming Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (for graph traversal, pattern detection, insight generation), various analytical and reporting flows across ESTRATIX.
*   **Key Agents Interacting:** `CKO_A004_KnowledgeAnalystAgent` (creates, updates, queries), other analytical agents.

## 7. Notes / Future Considerations

*   The representation of `relationships` within this model is one approach. For complex graphs, a dedicated `KnowledgeGraphEdge` model might be preferred, allowing edges to have their own properties, IDs, and provenance.
*   Graph metrics like `degree` and `centrality_score` are often calculated by graph database systems or algorithms and might be updated periodically rather than on every node update.
*   Schema enforcement for `properties` based on `node_type` could be considered for more structured graph data.
*   This model is intended for use with graph databases (e.g., Neo4j, Amazon Neptune) or graph processing libraries.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                                                            |
| :------ | :--------- | :--------- | :----------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M004 to CKO_M008. Updated ID, version, owner, SME, and all internal KNO references to CKO. Updated Last Updated date. Renumbered from M004 to M008. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the KnowledgeGraphNode data model.                                                                                                             |
