#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# Check if required environment variables are set
if [ -z "$PROMETHEUS_PUSHGATEWAY" ] || [ -z "$GITHUB_REF_NAME" ] || [ -z "$DEPLOYMENT_STATUS" ]; then
  echo "Error: One or more required environment variables are not set."
  echo "Skipping metrics push."
  exit 0
fi

# Construct the metrics payload
METRICS_PAYLOAD=$(printf '# HELP luxcrafts_deployment_total Total number of deployments\n# TYPE luxcrafts_deployment_total counter\nluxcrafts_deployment_total{branch="%s",status="%s"} 1\n' "$GITHUB_REF_NAME" "$DEPLOYMENT_STATUS")

# Push metrics to Prometheus Pushgateway
echo "Pushing deployment metrics to $PROMETHEUS_PUSHGATEWAY..."
echo "$METRICS_PAYLOAD" | curl --data-binary @- "$PROMETHEUS_PUSHGATEWAY/metrics/job/luxcrafts_deployment/branch/$GITHUB_REF_NAME"

echo "Metrics pushed successfully."
