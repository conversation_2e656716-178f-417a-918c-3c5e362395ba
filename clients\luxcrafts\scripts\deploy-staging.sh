#!/bin/bash

# Luxcrafts Staging Deployment Script
# High-momentum CI/CD deployment to Vercel staging

set -e

echo "🚀 Starting Luxcrafts Staging Deployment with High Momentum"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the luxcrafts client directory."
    exit 1
fi

print_status "Verifying project structure..."
if [ ! -f "vercel.json" ]; then
    print_error "vercel.json not found. Vercel configuration is required."
    exit 1
fi

print_success "Project structure verified"

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm ci
    print_success "Dependencies installed"
else
    print_status "Dependencies already installed"
fi

# Run quality checks
print_status "Running quality checks..."

print_status "Type checking..."
npm run check
print_success "Type checking passed"

print_status "Linting..."
npm run lint
print_success "Linting passed"

# Build the project
print_status "Building project for staging..."
export VITE_APP_ENVIRONMENT=staging
export NODE_OPTIONS="--max-old-space-size=32768 --max-semi-space-size=1024"

npm run build
print_success "Build completed successfully"

# Check build output
if [ ! -d "dist" ]; then
    print_error "Build failed - dist directory not found"
    exit 1
fi

print_status "Build output size:"
du -sh dist/

# Verify critical files exist
if [ ! -f "dist/index.html" ]; then
    print_error "Build failed - index.html not found in dist"
    exit 1
fi

print_success "Build verification passed"

# Create deployment info
print_status "Creating deployment metadata..."
cat > dist/deployment-info.json << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "environment": "staging",
  "version": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
  "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)"
}
EOF

print_success "Deployment metadata created"

# Create health check endpoint
print_status "Creating health check endpoint..."
cat > dist/health.json << EOF
{
  "status": "healthy",
  "environment": "staging",
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "version": "$(git rev-parse --short HEAD 2>/dev/null || echo 'unknown')",
  "services": {
    "frontend": "operational",
    "build": "successful"
  }
}
EOF

print_success "Health check endpoint created"

# Optimize for deployment
print_status "Optimizing deployment package..."

# Create optimized vercel.json for staging
cat > vercel-staging.json << EOF
{
  "version": 2,
  "buildCommand": "echo 'Using pre-built artifacts'",
  "outputDirectory": "dist",
  "framework": null,
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/assets/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        }
      ]
    },
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Referrer-Policy",
          "value": "strict-origin-when-cross-origin"
        }
      ]
    }
  ]
}
EOF

print_success "Staging configuration optimized"

# Display deployment summary
echo ""
echo "📊 DEPLOYMENT SUMMARY"
echo "====================="
echo "Environment: Staging"
echo "Build Status: ✅ Success"
echo "Build Size: $(du -sh dist/ | cut -f1)"
echo "Files: $(find dist -type f | wc -l) files"
echo "Timestamp: $(date)"
echo ""

print_success "🎉 Staging deployment preparation completed!"
print_status "Ready for Vercel deployment"

echo ""
echo "🔗 NEXT STEPS:"
echo "1. Configure Vercel project settings"
echo "2. Set up environment variables"
echo "3. Deploy to staging environment"
echo "4. Run health checks"
echo "5. Notify team of deployment"
echo ""

print_success "High-momentum staging deployment script completed successfully! 🚀"