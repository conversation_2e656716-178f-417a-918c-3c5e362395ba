const Fastify = require('fastify');

const fastify = Fastify({
  logger: true
});

// Simple health check route
fastify.get('/health', async (request, reply) => {
  return { status: 'ok', timestamp: new Date().toISOString() };
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ port: 3003, host: 'localhost' });
    console.log('Test server listening on localhost:3003');
  } catch (err) {
    console.error('Failed to start test server:', err);
    process.exit(1);
  }
};

start();