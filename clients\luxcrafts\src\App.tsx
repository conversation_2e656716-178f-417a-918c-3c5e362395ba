import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { WagmiProvider } from "wagmi";
import { RainbowKitProvider } from "@rainbow-me/rainbowkit";
import { config } from "@/config/wagmi";
import { Toaster } from "sonner";
import { useAuthStore } from "@/stores/authStore";
import { UserRole, Permission } from "@/types/auth";
import { ErrorBoundary } from "react-error-boundary";
import { errorMonitoring, ErrorType, ErrorSeverity } from "@/utils/errorMonitoring";
import "@/i18n"; // Initialize i18n
import AccessibilityToolbar from '@/components/AccessibilityToolbar';

// Error Fallback Component
const ErrorFallback: React.FC<{ error: Error; resetErrorBoundary: () => void }> = ({ error, resetErrorBoundary }) => {
  // Capture the error in our monitoring system
  React.useEffect(() => {
    errorMonitoring.captureError({
      type: ErrorType.RUNTIME,
      severity: ErrorSeverity.CRITICAL,
      message: error.message,
      stack: error.stack,
      metadata: {
        component: 'ErrorBoundary',
        errorBoundary: true
      }
    });
  }, [error]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
        <h2 className="text-xl font-semibold text-red-600 mb-4">Something went wrong</h2>
        <p className="text-gray-600 mb-4">{error.message}</p>
        <div className="space-y-2 mb-4">
          <button 
            onClick={() => {
              const healthReport = errorMonitoring.getHealthReport();
              console.log('Error Health Report:', healthReport);
            }}
            className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
          >
            View Error Report
          </button>
          <button 
            onClick={resetErrorBoundary}
            className="w-full px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
          >
            Try again
          </button>
        </div>
      </div>
    </div>
  );
};

// Pages
import Home from "@/pages/Home";
import PropertyMarketplace from "@/pages/PropertyMarketplace";
import ServiceBooking from "@/pages/ServiceBooking";
import Dashboard from "@/pages/Dashboard";
import ProviderPortal from "@/pages/ProviderPortal";
import AdminConsole from "@/pages/AdminConsole";
import PropertyAcquisition from "@/pages/PropertyAcquisition";
import ContentStudio from "@/pages/ContentStudio";
import TokenDashboard from "@/pages/TokenDashboard";
import NFTMarketplace from "@/pages/NFTMarketplace";

// Admin Components
import AdminDashboard from "@/components/admin/AdminDashboard";
import AIVoiceAgent from "@/components/ai/AIVoiceAgent";
import LiquidityPools from "@/components/defi/LiquidityPools";
import MonitoringDashboard from "@/components/MonitoringDashboard";

// Components
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const queryClient = new QueryClient();

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermission?: Permission;
  requiredRole?: UserRole;
  fallback?: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  requiredPermission, 
  requiredRole, 
  fallback = <Navigate to="/" replace /> 
}) => {
  const { user, hasPermission, hasRole } = useAuthStore();
  
  if (!user) {
    return <>{fallback}</>;
  }
  
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return <>{fallback}</>;
  }
  
  if (requiredRole && !hasRole(requiredRole)) {
    return <>{fallback}</>;
  }
  
  return <>{children}</>;
};

// Subdomain Router Component
const SubdomainRouter: React.FC = () => {
  const subdomain = typeof window !== 'undefined' 
    ? window.location.hostname.split('.')[0] 
    : '';
  
  // Route based on subdomain
  switch (subdomain) {
    case 'admin':
      return (
        <ProtectedRoute requiredPermission={Permission.MANAGE_CLIENT_ACCOUNTS}>
          <AdminDashboard />
        </ProtectedRoute>
      );
    case 'dev':
      return (
        <ProtectedRoute requiredPermission={Permission.ACCESS_DEV_TOOLS}>
          <AdminDashboard />
        </ProtectedRoute>
      );
    case 'ai':
      return (
        <ProtectedRoute requiredPermission={Permission.MANAGE_AI_AGENTS}>
          <AIVoiceAgent agentType="acquisition" />
        </ProtectedRoute>
      );
    case 'properties':
      return (
        <ProtectedRoute requiredPermission={Permission.MANAGE_PROPERTIES}>
          <PropertyMarketplace />
        </ProtectedRoute>
      );
    case 'invest':
      return (
        <ProtectedRoute requiredPermission={Permission.VIEW_INVESTMENT_OPPORTUNITIES}>
          <LiquidityPools />
        </ProtectedRoute>
      );
    default:
      return null;
  }
};

export default function App() {
  const subdomain = typeof window !== 'undefined' 
    ? window.location.hostname.split('.')[0] 
    : '';
  
  // If on a subdomain, render subdomain-specific content
  if (subdomain && !['localhost', 'luxcrafts', 'www'].includes(subdomain)) {
    return (
      <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => window.location.reload()}>
        <WagmiProvider config={config}>
          <QueryClientProvider client={queryClient}>
            <RainbowKitProvider>
              <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
                <AccessibilityToolbar />
                <Navbar />
                <main className="pt-16">
                  <SubdomainRouter />
                </main>
                <Footer />
              </div>
              <Toaster position="top-right" richColors />
            </RainbowKitProvider>
          </QueryClientProvider>
        </WagmiProvider>
      </ErrorBoundary>
    );
  }
  
  // Main application routing
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback} onReset={() => window.location.reload()}>
      <WagmiProvider config={config}>
        <QueryClientProvider client={queryClient}>
          <RainbowKitProvider>
            <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
              <AccessibilityToolbar />
              <Navbar />
              <main className="pt-16">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/marketplace" element={<PropertyMarketplace />} />
                  <Route path="/services" element={<ServiceBooking />} />
                  <Route path="/dashboard" element={<Dashboard />} />
                  <Route path="/provider" element={<ProviderPortal />} />
                  <Route path="/admin" element={<AdminConsole />} />
                  <Route path="/acquisition" element={<PropertyAcquisition />} />
                  <Route path="/content" element={<ContentStudio />} />
                  <Route path="/tokens" element={<TokenDashboard />} />
                  <Route path="/nft" element={<NFTMarketplace />} />
                  <Route path="/monitoring" element={<MonitoringDashboard />} />
                  
                  {/* Admin Routes with Role-Based Access */}
                  <Route 
                    path="/admin/dashboard" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.MANAGE_CLIENT_ACCOUNTS}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* AI Agent Routes */}
                  <Route 
                    path="/admin/ai-agents" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.MANAGE_AI_AGENTS}>
                        <AIVoiceAgent agentType="acquisition" />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/admin/ai-agents/acquisition" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.MANAGE_AI_AGENTS}>
                        <AIVoiceAgent agentType="acquisition" />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/admin/ai-agents/disposition" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.MANAGE_AI_AGENTS}>
                        <AIVoiceAgent agentType="disposition" />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* DeFi Routes */}
                  <Route 
                    path="/defi" 
                    element={<LiquidityPools />} 
                  />
                  <Route 
                    path="/defi/pools" 
                    element={<LiquidityPools />} 
                  />
                  <Route 
                    path="/admin/liquidity-pools" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.ACCESS_FINANCIAL_DATA}>
                        <LiquidityPools />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* Developer Routes */}
                  <Route 
                    path="/admin/dev-tools" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.ACCESS_DEV_TOOLS}>
                        <AdminDashboard />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* Property Management Routes */}
                  <Route 
                    path="/admin/properties" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.MANAGE_PROPERTIES}>
                        <PropertyMarketplace />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* Investment Routes */}
                  <Route 
                    path="/admin/investments" 
                    element={
                      <ProtectedRoute requiredPermission={Permission.VIEW_INVESTMENT_OPPORTUNITIES}>
                        <LiquidityPools />
                      </ProtectedRoute>
                    } 
                  />
                  
                  {/* Catch-all route */}
                  <Route path="*" element={<Navigate to="/" replace />} />
                </Routes>
              </main>
              <Footer />
            </div>
            <Toaster position="top-right" richColors />
        </RainbowKitProvider>
      </QueryClientProvider>
    </WagmiProvider>
    </ErrorBoundary>
  );
}
