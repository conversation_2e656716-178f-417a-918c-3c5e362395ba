import React, { useState, useEffect } from 'react';
import { Search, Phone, FileText, TrendingUp, MapPin, DollarSign, Calendar, <PERSON><PERSON>, Mi<PERSON>, <PERSON>c<PERSON><PERSON> } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface Property {
  id: string;
  address: string;
  price: number;
  estimatedValue: number;
  profitMargin: number;
  status: 'research' | 'contacted' | 'negotiating' | 'under_contract' | 'closed';
  ownerInfo: {
    name: string;
    phone: string;
    email: string;
    motivation: 'high' | 'medium' | 'low';
  };
  marketData: {
    comparables: number[];
    daysOnMarket: number;
    priceHistory: { date: string; price: number }[];
  };
  aiAnalysis: {
    repairEstimate: number;
    arvEstimate: number;
    rentEstimate: number;
    investmentScore: number;
  };
  voiceAgent: {
    lastCall: string;
    callCount: number;
    sentiment: 'positive' | 'neutral' | 'negative';
    nextAction: string;
  };
}

interface VoiceAgentConfig {
  isActive: boolean;
  currentCall: {
    propertyId: string;
    duration: number;
    status: 'dialing' | 'connected' | 'completed';
    transcript: string[];
  } | null;
}

const PropertyDataPipeline: React.FC = () => {
  const [properties, setProperties] = useState<Property[]>([]);
  const [searchCriteria, setSearchCriteria] = useState({
    location: '',
    maxPrice: 500000,
    minProfitMargin: 20,
    propertyType: 'all'
  });
  const [voiceAgent, setVoiceAgent] = useState<VoiceAgentConfig>({
    isActive: false,
    currentCall: null
  });
  const [isScanning, setIsScanning] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    const mockProperties: Property[] = [
      {
        id: '1',
        address: '123 Oak Street, Austin, TX',
        price: 285000,
        estimatedValue: 380000,
        profitMargin: 33.3,
        status: 'research',
        ownerInfo: {
          name: 'John Smith',
          phone: '******-555-0123',
          email: '<EMAIL>',
          motivation: 'high'
        },
        marketData: {
          comparables: [295000, 310000, 275000, 320000],
          daysOnMarket: 45,
          priceHistory: [
            { date: '2024-01-15', price: 320000 },
            { date: '2024-02-01', price: 305000 },
            { date: '2024-02-15', price: 285000 }
          ]
        },
        aiAnalysis: {
          repairEstimate: 25000,
          arvEstimate: 380000,
          rentEstimate: 2800,
          investmentScore: 8.5
        },
        voiceAgent: {
          lastCall: '2024-02-20',
          callCount: 2,
          sentiment: 'positive',
          nextAction: 'Schedule property viewing'
        }
      },
      {
        id: '2',
        address: '456 Pine Avenue, Dallas, TX',
        price: 195000,
        estimatedValue: 275000,
        profitMargin: 41.0,
        status: 'negotiating',
        ownerInfo: {
          name: 'Sarah Johnson',
          phone: '******-555-0456',
          email: '<EMAIL>',
          motivation: 'medium'
        },
        marketData: {
          comparables: [185000, 205000, 190000, 210000],
          daysOnMarket: 78,
          priceHistory: [
            { date: '2024-01-01', price: 225000 },
            { date: '2024-01-20', price: 210000 },
            { date: '2024-02-10', price: 195000 }
          ]
        },
        aiAnalysis: {
          repairEstimate: 18000,
          arvEstimate: 275000,
          rentEstimate: 2200,
          investmentScore: 9.2
        },
        voiceAgent: {
          lastCall: '2024-02-22',
          callCount: 4,
          sentiment: 'neutral',
          nextAction: 'Present final offer'
        }
      }
    ];
    setProperties(mockProperties);
  }, []);

  const startPropertyScan = async () => {
    setIsScanning(true);
    // Simulate API call to property data sources
    setTimeout(() => {
      setIsScanning(false);
      // Add new properties to the list
    }, 3000);
  };

  const initiateVoiceCall = (propertyId: string) => {
    const property = properties.find(p => p.id === propertyId);
    if (!property) return;

    setVoiceAgent({
      isActive: true,
      currentCall: {
        propertyId,
        duration: 0,
        status: 'dialing',
        transcript: [`Calling ${property.ownerInfo.name} at ${property.ownerInfo.phone}...`]
      }
    });

    // Simulate call progression
    setTimeout(() => {
      setVoiceAgent(prev => ({
        ...prev,
        currentCall: prev.currentCall ? {
          ...prev.currentCall,
          status: 'connected',
          transcript: [
            ...prev.currentCall.transcript,
            'AI Agent: Hello, this is Alex from Luxcrafts Property Services. I\'m calling about your property at ' + property.address + '. Do you have a moment to discuss?',
            'Owner: Yes, I can talk for a few minutes.',
            'AI Agent: Great! I understand you might be interested in selling. We specialize in quick, fair cash offers. What\'s your timeline like?'
          ]
        } : null
      }));
    }, 2000);
  };

  const endVoiceCall = () => {
    setVoiceAgent({
      isActive: false,
      currentCall: null
    });
  };

  const getStatusColor = (status: Property['status']) => {
    switch (status) {
      case 'research': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'negotiating': return 'bg-orange-100 text-orange-800';
      case 'under_contract': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getMotivationColor = (motivation: string) => {
    switch (motivation) {
      case 'high': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'low': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Property Acquisition Pipeline</h1>
          <p className="text-gray-600">AI-powered property research, owner outreach, and deal management</p>
        </div>

        {/* Search Controls */}
        <motion.div 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-lg p-6 mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
              <input
                type="text"
                value={searchCriteria.location}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, location: e.target.value }))}
                placeholder="City, State or ZIP"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
              <input
                type="number"
                value={searchCriteria.maxPrice}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, maxPrice: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Min Profit Margin (%)</label>
              <input
                type="number"
                value={searchCriteria.minProfitMargin}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, minProfitMargin: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
              <select
                value={searchCriteria.propertyType}
                onChange={(e) => setSearchCriteria(prev => ({ ...prev, propertyType: e.target.value }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Types</option>
                <option value="single-family">Single Family</option>
                <option value="multi-family">Multi Family</option>
                <option value="condo">Condo</option>
                <option value="commercial">Commercial</option>
              </select>
            </div>
          </div>
          <button
            onClick={startPropertyScan}
            disabled={isScanning}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-lg font-medium transition-colors flex items-center gap-2"
          >
            <Search className="w-4 h-4" />
            {isScanning ? 'Scanning Properties...' : 'Start Property Scan'}
          </button>
        </motion.div>

        {/* Voice Agent Status */}
        <AnimatePresence>
          {voiceAgent.isActive && voiceAgent.currentCall && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="bg-green-50 border border-green-200 rounded-xl p-6 mb-8"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="bg-green-500 rounded-full p-2">
                    <Bot className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-green-900">AI Voice Agent Active</h3>
                    <p className="text-green-700 text-sm">Status: {voiceAgent.currentCall.status}</p>
                  </div>
                </div>
                <button
                  onClick={endVoiceCall}
                  className="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
                >
                  <MicOff className="w-4 h-4" />
                  End Call
                </button>
              </div>
              <div className="bg-white rounded-lg p-4 max-h-40 overflow-y-auto">
                {voiceAgent.currentCall.transcript.map((message, index) => (
                  <p key={index} className="text-sm mb-2 last:mb-0">{message}</p>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Properties Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {properties.map((property) => (
            <motion.div
              key={property.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              <div className="p-6">
                {/* Property Header */}
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{property.address}</h3>
                    <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(property.status)}`}>
                      {property.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-gray-900">${property.price.toLocaleString()}</p>
                    <p className="text-sm text-gray-500">List Price</p>
                  </div>
                </div>

                {/* Key Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-green-50 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <TrendingUp className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-green-900">Profit Margin</span>
                    </div>
                    <p className="text-xl font-bold text-green-600">{property.profitMargin.toFixed(1)}%</p>
                  </div>
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="flex items-center gap-2 mb-1">
                      <DollarSign className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900">ARV Estimate</span>
                    </div>
                    <p className="text-xl font-bold text-blue-600">${property.aiAnalysis.arvEstimate.toLocaleString()}</p>
                  </div>
                </div>

                {/* Owner Information */}
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-gray-900 mb-2">Owner Information</h4>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Name:</span> {property.ownerInfo.name}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Phone:</span> {property.ownerInfo.phone}
                    </p>
                    <p className="text-sm text-gray-600">
                      <span className="font-medium">Motivation:</span> 
                      <span className={`ml-1 font-medium ${getMotivationColor(property.ownerInfo.motivation)}`}>
                        {property.ownerInfo.motivation.toUpperCase()}
                      </span>
                    </p>
                  </div>
                </div>

                {/* Voice Agent Status */}
                <div className="bg-purple-50 rounded-lg p-4 mb-4">
                  <h4 className="font-medium text-purple-900 mb-2">AI Voice Agent</h4>
                  <div className="space-y-1">
                    <p className="text-sm text-purple-700">
                      <span className="font-medium">Last Call:</span> {property.voiceAgent.lastCall}
                    </p>
                    <p className="text-sm text-purple-700">
                      <span className="font-medium">Call Count:</span> {property.voiceAgent.callCount}
                    </p>
                    <p className="text-sm text-purple-700">
                      <span className="font-medium">Sentiment:</span> {property.voiceAgent.sentiment}
                    </p>
                    <p className="text-sm text-purple-700">
                      <span className="font-medium">Next Action:</span> {property.voiceAgent.nextAction}
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2">
                  <button
                    onClick={() => initiateVoiceCall(property.id)}
                    disabled={voiceAgent.isActive}
                    className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                  >
                    <Phone className="w-4 h-4" />
                    Call Owner
                  </button>
                  <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors flex items-center justify-center gap-2">
                    <FileText className="w-4 h-4" />
                    View Details
                  </button>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PropertyDataPipeline;