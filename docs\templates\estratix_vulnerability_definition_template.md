# ESTRATIX Vulnerability Definition: [Vulnerability Name] ([ID])

## 1. Metadata

* **ID:** [VULN_ID] (e.g., VULN001)
* **Vulnerability Name:** [e.g., CVE-2023-XXXXX, SQL Injection in Login Form]
* **Version:** 1.0
* **Status:** (Identified | Remediating | Fixed | Risk Accepted)
* **Owner (Command Office):** CSecO
* **Reporter:** [Link to User Definition or source]
* **Date Created:** YYYY-MM-DD
* **Last Updated:** YYYY-MM-DD

## 2. Overview

* **Description:** [A detailed description of the vulnerability.]

## 3. Impact Assessment

* **Severity:** (Low | Medium | High | Critical)
* **CVSS Score:** [e.g., 9.8]
* **Affected Component:** [Link to the specific service, library, or application definition.]

## 4. Remediation

* **Recommended Action:** [e.g., Upgrade library, Patch code, Update configuration.]
* **Assigned To:** [Link to User Definition]
* **Due Date:** YYYY-MM-DD

## 5. Revision History

| Version | Date       | Author        | Changes                                     |
| :------ | :--------- | :------------ | :------------------------------------------ |
| 1.0     | YYYY-MM-DD | [Author Name] | Initial Definition                          |
