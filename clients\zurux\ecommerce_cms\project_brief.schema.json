{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Ecommerce Project Brief", "type": "object", "properties": {"clientName": {"type": "string"}, "brand": {"type": "object", "properties": {"brandName": {"type": "string"}, "logoUrl": {"type": "string", "format": "uri"}, "primaryColor": {"type": "string"}, "secondaryColor": {"type": "string"}}, "required": ["brandName"]}, "projectObjective": {"type": "string"}, "requiredFeatures": {"type": "array", "items": {"type": "string"}}, "preferredBackend": {"type": "string", "enum": ["medusajs", "payload", "strapi", "sanity"]}, "preferredFrontendFrameworks": {"type": "array", "items": {"type": "string", "enum": ["react", "vue", "svelte", "alpine"]}}, "paymentMethods": {"type": "array", "items": {"type": "string", "enum": ["qr_blockchain", "manual_confirmation", "credit_card", "paypal"]}}, "contentGeneration": {"type": "boolean"}, "seoBoost": {"type": "boolean"}, "customizationVariables": {"type": "object", "additionalProperties": true}, "notes": {"type": "string"}}, "required": ["clientName", "brand", "projectObjective", "requiredFeatures", "preferredBackend", "preferredFrontendFrameworks", "paymentMethods"]}