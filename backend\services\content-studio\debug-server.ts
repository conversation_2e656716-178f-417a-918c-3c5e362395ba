import Fastify from 'fastify';
import { config } from './src/config/environment';
import { logger } from './src/utils/logger';

const fastify = Fastify({
  logger: logger,
  trustProxy: true,
});

// Simple health check endpoint
fastify.get('/health', async (request, reply) => {
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  };
});

// Start server
const start = async () => {
  try {
    await fastify.listen({ 
      port: config.port, 
      host: config.host 
    });
    
    logger.info(`Debug server listening on ${config.host}:${config.port}`);
  } catch (err) {
    logger.error('Failed to start debug server:', err);
    process.exit(1);
  }
};

start();

export { fastify };