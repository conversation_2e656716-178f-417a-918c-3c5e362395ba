# ESTRATIX IP Matrix

## 1. Overview

This matrix tracks the providers and pools of IP addresses used for various ESTRATIX operations, including traffic generation, web scraping, and security testing. It ensures proper management and allocation of IP resources.

## 2. IP Provider Matrix

| Group ID   | Provider Name | IP Type     | Status   | Region(s)      | API/Credential Source      | Definition                                |
| :--------- | :------------ | :---------- | :------- | :------------- | :------------------------- | :---------------------------------------- |
| IP-GRP-001 | Geonode       | Residential | `Active`   | Global         | Vault: `geonode-api-key`   | [IP-GRP-001_Definition.md](./definitions/IP-GRP-001.md) |
| IP-GRP-002 | DataImpulse   | Residential | `Active`   | North America  | Vault: `dataimpulse-api-key` | [IP-GRP-002_Definition.md](./definitions/IP-GRP-002.md) |
| IP-GRP-003 | AWS IP Pool   | Datacenter  | `Active`   | us-east-1      | IAM Role                   | [IP-GRP-003_Definition.md](./definitions/IP-GRP-003.md) |

## 3. IP Types

- **Residential**: IPs from real user devices, ideal for mimicking organic user traffic and avoiding blocks.
- **Datacenter**: IPs from cloud providers (e.g., AWS, GCP). Good for high-volume, low-sensitivity tasks.
- **Mobile**: IPs from mobile carrier networks, used for testing mobile user experiences.

## 4. Integration Points

- **Traffic Matrix**: Campaigns specify an IP Group ID from this matrix to define their traffic source.
- **VPC Server Matrix**: Servers used for running traffic scripts are configured to use IP pools from specific providers.
- **Tool Definitions**: Scraping and testing tools are configured to use appropriate IP groups to perform their tasks.

---

**Last Updated**: YYYY-MM-DD  
**Next Review**: YYYY-MM-DD  
**Owner**: CTO (Chief Technology Officer)
