#!/bin/bash

# ============================================================================
# ESTRATIX Luxcrafts Deployment from GitHub Container Registry
# ============================================================================
# Purpose: Deploys the luxcrafts application by pulling a pre-built
#          Docker image from ghcr.io.
# Prerequisites: Docker installed, VPS user logged into ghcr.io.
# Author: ESTRATIX Infrastructure Team (Generated by Cascade)
# Version: 1.0
# ============================================================================

set -euo pipefail

# --- Configuration ---
# These variables should be set in the environment where the script is run (e.g., GitHub Actions runner)

readonly APP_NAME="luxcrafts-platform"
readonly DOCKER_COMPOSE_FILE="docker-compose.prod.yml"

# Check for required environment variables
if [ -z "${DOCKER_IMAGE_TAG}" ] || [ -z "${DOMAIN_NAME}" ] || [ -z "${REGISTRY_USER}" ] || [ -z "${REGISTRY_TOKEN}" ]; then
  echo "[ERROR] Missing required environment variables: DOCKER_IMAGE_TAG, DOMAIN_NAME, REGISTRY_USER, REGISTRY_TOKEN" >&2
  exit 1
fi

# --- Logging ---
log_info() { echo -e "\033[0;34m[INFO]\033[0m $1"; }
log_success() { echo -e "\033[0;32m[SUCCESS]\033[0m $1"; }
log_error() { echo -e "\033[0;31m[ERROR]\033[0m $1"; }

# --- Main Script ---

log_info "Starting deployment for ${APP_NAME} from image ${DOCKER_IMAGE_TAG}"

# 1. Login to GitHub Container Registry
log_info "Logging into GitHub Container Registry..."
echo "${REGISTRY_TOKEN}" | docker login ghcr.io -u "${REGISTRY_USER}" --password-stdin
if [ $? -ne 0 ]; then
    log_error "Docker login failed. Please check REGISTRY_USER and REGISTRY_TOKEN."
    exit 1
fi
log_success "Logged into ghcr.io successfully."

# 2. Pull the new Docker image
log_info "Pulling Docker image: ${DOCKER_IMAGE_TAG}"
docker pull "${DOCKER_IMAGE_TAG}"
if [ $? -ne 0 ]; then
    log_error "Failed to pull Docker image. Please check the image tag and registry permissions."
    exit 1
fi
log_success "Image pulled successfully."

# 3. Deploy using Docker Compose
log_info "Deploying application using Docker Compose..."

# The docker-compose.prod.yml file is expected to be in the same directory
# It will use DOCKER_IMAGE_TAG and DOMAIN_NAME from the environment
docker-compose -f "${DOCKER_COMPOSE_FILE}" up -d --force-recreate

if [ $? -ne 0 ]; then
    log_error "Docker Compose deployment failed."
    exit 1
fi

log_success "Docker Compose stack has been updated."

# 4. Health Check
log_info "Performing health check on https://${DOMAIN_NAME}"
sleep 15 # Give the container a moment to start up

if curl -fsS --head "https://${DOMAIN_NAME}" > /dev/null; then
    log_success "Health check passed. Application is live at https://${DOMAIN_NAME}"
else
    log_error "Health check failed. Please check the container logs."
    docker logs "${APP_NAME}"
    exit 1
fi

# 5. Clean up old images
log_info "Cleaning up dangling Docker images..."
docker image prune -f

log_success "🚀 Deployment of ${APP_NAME} completed successfully!"
