---
Description: Guides the definition of a new ESTRATIX API, from proposal to a complete definition document, including matrix registration and landscape updates.
---

# Workflow: Define New ESTRATIX API

## Objective

To systematically define a new API, ensuring all necessary information is captured, documented, and integrated into the ESTRATIX framework.

## Trigger

Identification of a need for a new API (e.g., from service definition, flow requirements, integration opportunity).

## Responsible Command Office (Lead)

Typically CTO, but can be initiated by any office requiring API development.

## Key ESTRATIX Components Involved

* `docs/matrices/api_matrix.md`
* `docs/api/[OFFICER_CODE]/[API_ID]_[API_Name_PascalCase].md` (API Definition Document)
* `docs/api/[OFFICER_CODE]/[API_ID]_[API_Name_PascalCase].yaml` (OpenAPI/Swagger Specification - Optional, can be generated later)
* `docs/templates/estratix_api_definition_template.md`
* `docs/diagrams/api_landscape.mmd`
* Potentially: `docs/data_models/api/[API_ID]/...`

## Steps

1.  **Proposal & Initial Scoping**
    -   **Action:** Document the initial idea for the API, its purpose, consumers, and core functionality. Use a formal proposal if needed.
    -   **Tool:** `write_to_file` (for a new proposal)
    -   **Example:**

        ```markdown
        <!-- write_to_file('docs/proposals/PROP_API_NewService.md', '# Proposal: New Service API\n\n...') -->
        ```

2.  **Assign API ID & Responsible Office**
    -   **Action:** Determine the responsible Command Office and assign a unique, sequential API ID.
    -   **Guidance:** Follow `docs/standards/STD001_NamingConventions.md`.
    -   **Output:** API ID (e.g., `CTO_API002`), API Name, Responsible Office.

3.  **Draft API Definition Document**
    -   **Action:** Create the API definition file by copying the standard template.
    -   **Tool:** `run_command`
    -   **Example:**

        ```markdown
        <!-- run_command('cp docs/templates/estratix_api_definition_template.md docs/api/cto/CTO_API002_NewServiceAPI.md') -->
        ```

4.  **Define Endpoints & Data Models**
    -   **Action:** Detail each endpoint and define necessary data models (e.g., Pydantic schemas) in the definition document. Create separate files for complex data models.
    -   **Tool:** `write_to_file`
    -   **Example:**

        ```markdown
        <!-- write_to_file('docs/data_models/api/CTO_API002/UserInputModel.md', '# Data Model: UserInput\n\n...') -->
        ```

5.  **Create Formal Specification (Optional)**
    -   **Action:** Create a formal specification file, such as an OpenAPI/Swagger YAML.
    -   **Tool:** `write_to_file`
    -   **Example:**

        ```markdown
        <!-- write_to_file('docs/api/cto/CTO_API002_NewServiceAPI.yaml', 'openapi: 3.0.0\ninfo:\n  title: New Service API\n  version: 0.1.0\n...') -->
        ```

6.  **Register API in Matrix**
    -   **Action:** Add a new entry for the API in the `api_matrix.md`.
    -   **Tool:** `replace_file_content`
    -   **Example (assuming the table has a header row):**

        ```markdown
        <!-- replace_file_content(
            'docs/matrices/api_matrix.md',
            ReplacementChunks=[{
                'AllowMultiple': false,
                'TargetContent': '| --- | --- | --- | --- | --- | --- | --- |',
                'ReplacementContent': '| --- | --- | --- | --- | --- | --- | --- |\n| `CTO_API002` | New Service API | `CTO` | `0.1.0` | `Design` | [Definition](./../api/cto/CTO_API002_NewServiceAPI.md) | [Spec](./../api/cto/CTO_API002_NewServiceAPI.yaml) |'
            }]
        ) -->
        ```

7.  **Review & Approval**
    -   **Action:** Submit the API definition and matrix update for review.
    -   **Process:** Follow the standard ESTRATIX approval workflow.
    -   **Output:** Status in `api_matrix.md` is updated to `Approved`.

8.  **Update API Landscape Diagram**
    -   **Action:** Trigger the `update_api_landscape` workflow to reflect the new API in the master diagram.
    -   **Tool:** (Agent invokes workflow)
    -   **Example:**

        ```markdown
        <!-- /update_api_landscape -->
        ```

9.  **Proceed to Implementation**
    -   **Action:** With the definition approved, the API is ready for the generation/implementation phase.
    -   **Next Workflow:** `api_generation.md`

## Post-Definition Activities (Handled by other workflows)

* API Implementation (e.g., `wf_agentic_component_implementation.md`)
* API Testing
* API Deployment
* API Monitoring & Maintenance
