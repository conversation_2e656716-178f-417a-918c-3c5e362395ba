import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { web3Service } from '../services/web3Service';
import { authenticateToken } from '../middleware/auth';

// Contract schemas

async function contractRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Get contract addresses
  fastify.get('/addresses', async (request: any, reply: any) => {
    try {
      const addresses = {
        luxToken: process.env.LUX_TOKEN_ADDRESS,
        propertyNFT: process.env.PROPERTY_NFT_ADDRESS,
        staking: process.env.STAKING_CONTRACT_ADDRESS,
        marketplace: process.env.MARKETPLACE_ADDRESS,
        governance: process.env.GOVERNANCE_ADDRESS,
        lendingPool: process.env.LENDING_POOL_ADDRESS,
        liquidityPool: process.env.LIQUIDITY_POOL_ADDRESS
      };
      reply.send({ success: true, addresses });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get contract addresses' });
    }
  });

  // Get contract status
  fastify.get('/status', async (request: any, reply: any) => {
    try {
      const status = await web3Service.getContractStatus();
      reply.send({ success: true, status });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get contract status' });
    }
  });

  // Deploy contract (admin only)
  fastify.post('/deploy', {
    preHandler: [authenticateToken, async (request: any, reply: any) => {
      if (request.user?.role !== 'admin') {
        return reply.code(403).send({ error: 'Admin access required' });
      }
    }]
  }, async (request: any, reply: any) => {
    try {
      const { contractType, constructorArgs } = request.body;
      const result = await web3Service.deployContract(contractType, constructorArgs);
      reply.send({ success: true, result });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to deploy contract' });
    }
  });
}

export default fp(contractRoutes);