import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PhotoIcon,
  HomeIcon,
  CurrencyDollarIcon,
  HeartIcon,
  EyeIcon,
  ShareIcon,
  TagIcon,
  ClockIcon,
  TrophyIcon,
  SparklesIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  ChartBarIcon,
  FireIcon,
  GlobeAltIcon,
  ShieldCheckIcon,
  DocumentTextIcon,
  CubeIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

const categories = [
  { id: 'all', name: 'All NFTs', count: 1247 },
  { id: 'property', name: 'Property Certificates', count: 856 },
  { id: 'fractional', name: 'Fractional Ownership', count: 234 },
  { id: 'rental', name: 'Rental Rights', count: 89 },
  { id: 'development', name: 'Development Rights', count: 45 },
  { id: 'art', name: 'Property Art', count: 23 },
];

const sortOptions = [
  { id: 'recent', name: 'Recently Listed' },
  { id: 'price_low', name: 'Price: Low to High' },
  { id: 'price_high', name: 'Price: High to Low' },
  { id: 'popular', name: 'Most Popular' },
  { id: 'ending', name: 'Ending Soon' },
];

const nftListings = [
  {
    id: 1,
    title: 'Beacon Hill Penthouse Certificate',
    description: 'Luxury penthouse ownership certificate with full property rights',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20beacon%20hill%20boston%20certificate%20NFT&image_size=square',
    price: 125.5,
    currency: 'LUX',
    usdPrice: 310.24,
    category: 'property',
    propertyType: 'Penthouse',
    location: 'Beacon Hill, Boston',
    bedrooms: 3,
    bathrooms: 2,
    sqft: 2400,
    yearBuilt: 1920,
    seller: {
      name: 'PropertyDAO',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=property%20dao%20avatar%20logo&image_size=square',
      verified: true,
    },
    stats: {
      views: 1250,
      likes: 89,
      offers: 12,
    },
    auction: {
      isAuction: true,
      endTime: '2024-01-20T18:00:00Z',
      highestBid: 120.0,
      bidders: 8,
    },
    rarity: 'Legendary',
    traits: [
      { name: 'Location', value: 'Prime' },
      { name: 'View', value: 'Harbor' },
      { name: 'Parking', value: 'Private' },
      { name: 'Amenities', value: 'Luxury' },
    ],
  },
  {
    id: 2,
    title: 'Back Bay Condo Fractional Share',
    description: '25% ownership share in premium Back Bay condominium',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=back%20bay%20condo%20fractional%20ownership%20NFT&image_size=square',
    price: 45.8,
    currency: 'LUX',
    usdPrice: 113.18,
    category: 'fractional',
    propertyType: 'Condo',
    location: 'Back Bay, Boston',
    bedrooms: 2,
    bathrooms: 1,
    sqft: 1200,
    yearBuilt: 2015,
    seller: {
      name: 'FractionalRE',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=fractional%20real%20estate%20avatar&image_size=square',
      verified: true,
    },
    stats: {
      views: 890,
      likes: 67,
      offers: 5,
    },
    auction: {
      isAuction: false,
    },
    rarity: 'Rare',
    traits: [
      { name: 'Share', value: '25%' },
      { name: 'Yield', value: '8.5% APY' },
      { name: 'Liquidity', value: 'High' },
      { name: 'Management', value: 'Professional' },
    ],
  },
  {
    id: 3,
    title: 'Cambridge Waterfront Rental Rights',
    description: '12-month rental rights for luxury waterfront property',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=cambridge%20waterfront%20rental%20rights%20NFT&image_size=square',
    price: 18.2,
    currency: 'LUX',
    usdPrice: 44.95,
    category: 'rental',
    propertyType: 'Townhouse',
    location: 'Cambridge, MA',
    bedrooms: 4,
    bathrooms: 3,
    sqft: 2800,
    yearBuilt: 2018,
    seller: {
      name: 'RentalDAO',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=rental%20dao%20avatar&image_size=square',
      verified: false,
    },
    stats: {
      views: 456,
      likes: 34,
      offers: 3,
    },
    auction: {
      isAuction: true,
      endTime: '2024-01-18T12:00:00Z',
      highestBid: 16.5,
      bidders: 4,
    },
    rarity: 'Common',
    traits: [
      { name: 'Duration', value: '12 months' },
      { name: 'Transferable', value: 'Yes' },
      { name: 'Utilities', value: 'Included' },
      { name: 'Furnished', value: 'Fully' },
    ],
  },
  {
    id: 4,
    title: 'Newton Development Rights',
    description: 'Development rights for 5-acre plot in Newton',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=newton%20development%20rights%20land%20NFT&image_size=square',
    price: 250.0,
    currency: 'LUX',
    usdPrice: 617.50,
    category: 'development',
    propertyType: 'Land',
    location: 'Newton, MA',
    bedrooms: 0,
    bathrooms: 0,
    sqft: 217800, // 5 acres
    yearBuilt: null,
    seller: {
      name: 'DevCorp',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=development%20corporation%20avatar&image_size=square',
      verified: true,
    },
    stats: {
      views: 2100,
      likes: 156,
      offers: 23,
    },
    auction: {
      isAuction: false,
    },
    rarity: 'Epic',
    traits: [
      { name: 'Zoning', value: 'Residential' },
      { name: 'Utilities', value: 'Available' },
      { name: 'Access', value: 'Road' },
      { name: 'Permits', value: 'Pre-approved' },
    ],
  },
  {
    id: 5,
    title: 'Boston Harbor View Art',
    description: 'Digital art piece featuring Boston Harbor luxury properties',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=boston%20harbor%20luxury%20property%20digital%20art%20NFT&image_size=square',
    price: 8.5,
    currency: 'LUX',
    usdPrice: 20.99,
    category: 'art',
    propertyType: 'Digital Art',
    location: 'Boston Harbor',
    bedrooms: null,
    bathrooms: null,
    sqft: null,
    yearBuilt: 2024,
    seller: {
      name: 'ArtistDAO',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=artist%20dao%20avatar&image_size=square',
      verified: true,
    },
    stats: {
      views: 678,
      likes: 45,
      offers: 7,
    },
    auction: {
      isAuction: false,
    },
    rarity: 'Uncommon',
    traits: [
      { name: 'Style', value: 'Photorealistic' },
      { name: 'Resolution', value: '4K' },
      { name: 'Edition', value: '1/1' },
      { name: 'Rights', value: 'Commercial' },
    ],
  },
  {
    id: 6,
    title: 'Somerville Loft Certificate',
    description: 'Industrial loft conversion ownership certificate',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=somerville%20industrial%20loft%20certificate%20NFT&image_size=square',
    price: 78.9,
    currency: 'LUX',
    usdPrice: 194.89,
    category: 'property',
    propertyType: 'Loft',
    location: 'Somerville, MA',
    bedrooms: 1,
    bathrooms: 1,
    sqft: 1800,
    yearBuilt: 1995,
    seller: {
      name: 'LoftDAO',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=loft%20dao%20avatar&image_size=square',
      verified: true,
    },
    stats: {
      views: 1100,
      likes: 78,
      offers: 9,
    },
    auction: {
      isAuction: true,
      endTime: '2024-01-19T15:30:00Z',
      highestBid: 75.0,
      bidders: 6,
    },
    rarity: 'Rare',
    traits: [
      { name: 'Style', value: 'Industrial' },
      { name: 'Ceiling', value: 'High' },
      { name: 'Windows', value: 'Floor-to-ceiling' },
      { name: 'Parking', value: 'Street' },
    ],
  },
];

const featuredCollections = [
  {
    id: 1,
    name: 'Boston Luxury Properties',
    description: 'Premium property certificates in Boston\'s most exclusive neighborhoods',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=boston%20luxury%20properties%20collection%20banner&image_size=landscape_16_9',
    items: 45,
    floorPrice: 25.5,
    volume: 1250.8,
  },
  {
    id: 2,
    name: 'Fractional Ownership',
    description: 'Shared ownership opportunities in high-value properties',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=fractional%20ownership%20collection%20banner&image_size=landscape_16_9',
    items: 23,
    floorPrice: 12.0,
    volume: 456.2,
  },
  {
    id: 3,
    name: 'Development Rights',
    description: 'Future development opportunities and land rights',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=development%20rights%20collection%20banner&image_size=landscape_16_9',
    items: 12,
    floorPrice: 150.0,
    volume: 2100.5,
  },
];

export default function NFTMarketplace() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('recent');
  const [searchQuery, setSearchQuery] = useState('');
  const [likedItems, setLikedItems] = useState(new Set());
  const [selectedNFT, setSelectedNFT] = useState(null);
  const [showBidModal, setShowBidModal] = useState(false);
  const [bidAmount, setBidAmount] = useState('');

  const filteredNFTs = nftListings.filter(nft => {
    const matchesCategory = selectedCategory === 'all' || nft.category === selectedCategory;
    const matchesSearch = nft.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         nft.location.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const toggleLike = (nftId) => {
    const newLikedItems = new Set(likedItems);
    if (newLikedItems.has(nftId)) {
      newLikedItems.delete(nftId);
    } else {
      newLikedItems.add(nftId);
    }
    setLikedItems(newLikedItems);
  };

  const handleBid = () => {
    console.log('Placing bid:', bidAmount, 'LUX on NFT:', selectedNFT.id);
    setShowBidModal(false);
    setBidAmount('');
    setSelectedNFT(null);
  };

  const getRarityColor = (rarity) => {
    switch (rarity) {
      case 'Common': return 'text-gray-600 bg-gray-100';
      case 'Uncommon': return 'text-green-600 bg-green-100';
      case 'Rare': return 'text-blue-600 bg-blue-100';
      case 'Epic': return 'text-purple-600 bg-purple-100';
      case 'Legendary': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const formatTimeRemaining = (endTime) => {
    const now = new Date();
    const end = new Date(endTime);
    const diff = Number(end) - Number(now);
    
    if (diff <= 0) return 'Ended';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    }
    
    return `${hours}h ${minutes}m`;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            NFT Marketplace
          </h1>
          <p className="text-xl text-gray-600">
            Discover, collect, and trade property NFTs and digital assets
          </p>
        </div>

        {/* Featured Collections */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Collections</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {featuredCollections.map((collection) => (
              <motion.div
                key={collection.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
              >
                <div className="relative h-48">
                  <img
                    src={collection.image}
                    alt={collection.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-bold">{collection.name}</h3>
                    <p className="text-sm opacity-90">{collection.items} items</p>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-gray-600 mb-4">{collection.description}</p>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">Floor Price</p>
                      <p className="font-bold">{collection.floorPrice} LUX</p>
                    </div>
                    <div>
                      <p className="text-gray-500">Volume</p>
                      <p className="font-bold">{collection.volume} LUX</p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Filters and Search */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Search */}
            <div className="flex-1">
              <input
                type="text"
                placeholder="Search NFTs, properties, or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            
            {/* Sort */}
            <div className="lg:w-48">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {sortOptions.map((option) => (
                  <option key={option.id} value={option.id}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          {/* Categories */}
          <div className="mt-6">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category.name} ({category.count})
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* NFT Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredNFTs.map((nft) => (
            <motion.div
              key={nft.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
            >
              {/* NFT Image */}
              <div className="relative">
                <img
                  src={nft.image}
                  alt={nft.title}
                  className="w-full h-64 object-cover"
                />
                
                {/* Rarity Badge */}
                <div className="absolute top-3 left-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRarityColor(nft.rarity)}`}>
                    {nft.rarity}
                  </span>
                </div>
                
                {/* Like Button */}
                <button
                  onClick={() => toggleLike(nft.id)}
                  className="absolute top-3 right-3 p-2 bg-white/80 backdrop-blur-sm rounded-full hover:bg-white transition-colors"
                >
                  {likedItems.has(nft.id) ? (
                    <HeartSolidIcon className="w-5 h-5 text-red-500" />
                  ) : (
                    <HeartIcon className="w-5 h-5 text-gray-600" />
                  )}
                </button>
                
                {/* Auction Timer */}
                {nft.auction.isAuction && (
                  <div className="absolute bottom-3 left-3 bg-black/80 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs">
                    <div className="flex items-center space-x-1">
                      <ClockIcon className="w-3 h-3" />
                      <span>{formatTimeRemaining(nft.auction.endTime)}</span>
                    </div>
                  </div>
                )}
              </div>
              
              {/* NFT Details */}
              <div className="p-6">
                <div className="flex items-center space-x-2 mb-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    nft.category === 'property' ? 'bg-blue-100 text-blue-800' :
                    nft.category === 'fractional' ? 'bg-green-100 text-green-800' :
                    nft.category === 'rental' ? 'bg-purple-100 text-purple-800' :
                    nft.category === 'development' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-pink-100 text-pink-800'
                  }`}>
                    {nft.category}
                  </span>
                  {nft.seller.verified && (
                    <ShieldCheckIcon className="w-4 h-4 text-blue-500" />
                  )}
                </div>
                
                <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">{nft.title}</h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{nft.description}</p>
                
                {/* Property Details */}
                {nft.propertyType !== 'Digital Art' && (
                  <div className="grid grid-cols-2 gap-2 mb-4 text-xs text-gray-500">
                    <div className="flex items-center space-x-1">
                      <MapPinIcon className="w-3 h-3" />
                      <span>{nft.location}</span>
                    </div>
                    {nft.bedrooms !== null && (
                      <div className="flex items-center space-x-1">
                        <HomeIcon className="w-3 h-3" />
                        <span>{nft.bedrooms}bd {nft.bathrooms}ba</span>
                      </div>
                    )}
                    {nft.sqft && (
                      <div className="flex items-center space-x-1">
                        <CubeIcon className="w-3 h-3" />
                        <span>{nft.sqft.toLocaleString()} sqft</span>
                      </div>
                    )}
                    {nft.yearBuilt && (
                      <div className="flex items-center space-x-1">
                        <CalendarIcon className="w-3 h-3" />
                        <span>Built {nft.yearBuilt}</span>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Seller */}
                <div className="flex items-center space-x-2 mb-4">
                  <img
                    src={nft.seller.avatar}
                    alt={nft.seller.name}
                    className="w-6 h-6 rounded-full"
                  />
                  <span className="text-sm text-gray-600">{nft.seller.name}</span>
                </div>
                
                {/* Stats */}
                <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                  <div className="flex items-center space-x-1">
                    <EyeIcon className="w-3 h-3" />
                    <span>{nft.stats.views}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <HeartIcon className="w-3 h-3" />
                    <span>{nft.stats.likes}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <TagIcon className="w-3 h-3" />
                    <span>{nft.stats.offers} offers</span>
                  </div>
                </div>
                
                {/* Price and Action */}
                <div className="border-t pt-4">
                  {nft.auction.isAuction ? (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Current Bid</span>
                        <span className="text-sm text-gray-500">{nft.auction.bidders} bidders</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-lg font-bold text-gray-900">
                            {nft.auction.highestBid} {nft.currency}
                          </p>
                          <p className="text-sm text-gray-500">
                            ${(nft.auction.highestBid * 2.47).toFixed(2)}
                          </p>
                        </div>
                        <button
                          onClick={() => {
                            setSelectedNFT(nft);
                            setShowBidModal(true);
                          }}
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                        >
                          Place Bid
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-lg font-bold text-gray-900">
                          {nft.price} {nft.currency}
                        </p>
                        <p className="text-sm text-gray-500">${nft.usdPrice}</p>
                      </div>
                      <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Buy Now
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Bid Modal */}
        {showBidModal && selectedNFT && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Place a Bid</h3>
              
              <div className="mb-6">
                <div className="flex items-center space-x-4 mb-4">
                  <img
                    src={selectedNFT.image}
                    alt={selectedNFT.title}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900">{selectedNFT.title}</h4>
                    <p className="text-sm text-gray-600">{selectedNFT.location}</p>
                  </div>
                </div>
                
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm text-gray-600">Current Highest Bid</span>
                    <span className="font-bold">{selectedNFT.auction.highestBid} LUX</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Minimum Bid</span>
                    <span className="font-bold">{(selectedNFT.auction.highestBid + 1).toFixed(1)} LUX</span>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Your Bid Amount (LUX)
                  </label>
                  <input
                    type="number"
                    value={bidAmount}
                    onChange={(e) => setBidAmount(e.target.value)}
                    placeholder={`Minimum ${(selectedNFT.auction.highestBid + 1).toFixed(1)} LUX`}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    setShowBidModal(false);
                    setSelectedNFT(null);
                    setBidAmount('');
                  }}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBid}
                  disabled={!bidAmount || parseFloat(bidAmount) <= selectedNFT.auction.highestBid}
                  className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Place Bid
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}