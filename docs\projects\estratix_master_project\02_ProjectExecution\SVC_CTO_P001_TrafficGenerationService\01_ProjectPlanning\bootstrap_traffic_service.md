---
description: Guides the generation of a new ESTRATIX Traffic Generation microservice.
---

# ESTRATIX Workflow: Bootstrap Traffic Generation Service

**Workflow ID:** `WF_CMO_BOOTSTRAP_TRAFFIC_SERVICE_V1`

**Agent Persona:** `AGENT_Traffic_Service_Builder_TSB001`

## 1. Objective

To automate the creation of a new Python FastAPI microservice designed to execute traffic generation campaigns. This service will read configurations from the `traffic_matrix.md`, utilize resources from the `ip_matrix.md` and `domain_matrix.md`, and run on infrastructure defined in the `vpc_server_matrix.md`.

## 2. Prerequisites

- The name of the new service must be `traffic-generation-service`.
- The target parent directory will be `src/domain/services`.
- The core infrastructure matrices (`traffic_matrix.md`, `ip_matrix.md`, `domain_matrix.md`, `vpc_server_matrix.md`) must be initialized and accessible.

## 3. Workflow Steps

### Step 1: Define Project Name and Path

1.  **Action:** Define `service_name` as `traffic-generation-service`.
2.  **Action:** Define `parent_directory` as `src/domain/services`.
3.  **Action:** Define `project_root` as `{{parent_directory}}/{{service_name}}`.

### Step 2: Create Root Directory and Scaffold

-   **Action:** Create the main project directory and standard hexagonal structure.
-   **Tool:** `run_command`
-   **Commands:**
    ```markdown
    <!-- run_command('mkdir -p {{project_root}}') -->
    <!-- run_command('mkdir -p .github/workflows docs scripts src tests', Cwd='{{project_root}}') -->
    <!-- run_command('mkdir -p src/application src/domain src/infrastructure', Cwd='{{project_root}}') -->
    <!-- run_command('mkdir -p src/application/services src/application/ports', Cwd='{{project_root}}') -->
    <!-- run_command('mkdir -p src/domain/models src/domain/services', Cwd='{{project_root}}') -->
    <!-- run_command('mkdir -p src/infrastructure/adapters src/infrastructure/repositories', Cwd='{{project_root}}') -->
    ```

### Step 3: Create Core Service Files

-   **Action:** Create placeholder Python files for the core service logic.
-   **Tool:** `write_to_file`
-   **Files to Create:**
    -   `{{project_root}}/src/main.py`
    -   `{{project_root}}/src/domain/services/campaign_executor.py`
    -   `{{project_root}}/src/infrastructure/repositories/matrix_reader.py`
    -   `{{project_root}}/src/application/services/traffic_orchestrator.py`
    -   `{{project_root}}/pyproject.toml`
    -   `{{project_root}}/README.md`

### Step 4: Populate README.md

-   **Action:** Add initial content to the service's README file.
-   **Tool:** `write_to_file` (or `replace_file_content`)
-   **Content:** Provide a brief overview of the service, its purpose, and instructions on how to install dependencies and run it using `uv`.

### Step 5: Define Dependencies in pyproject.toml

-   **Action:** Define `fastapi` and `uvicorn` as initial dependencies.
-   **Tool:** `write_to_file` (or `replace_file_content`)
-   **Content:**
    ```toml
    [project]
    name = "traffic-generation-service"
    version = "0.1.0"
    dependencies = [
        "fastapi",
        "uvicorn[standard]",
    ]
    ```

## 4. Next Steps

- Implement the logic within the created Python files to read from matrices and execute traffic scripts.
- Develop agentic capabilities to manage the lifecycle of traffic campaigns through this service.
