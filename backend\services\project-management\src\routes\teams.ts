import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fp from 'fastify-plugin';
import { authenticateToken, requirePermission } from '../middleware/auth';
import { teamService } from '../services/teamService';
import { logger } from '../utils/logger';

// Request/Response schemas
const createTeamSchema = {
  body: {
    type: 'object',
    required: ['name'],
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 100 },
      description: { type: 'string', maxLength: 500 },
      projectIds: { type: 'array', items: { type: 'string' } },
      memberIds: { type: 'array', items: { type: 'string' } },
      leaderId: { type: 'string' },
      metadata: { type: 'object' }
    }
  }
};

const updateTeamSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 100 },
      description: { type: 'string', maxLength: 500 },
      projectIds: { type: 'array', items: { type: 'string' } },
      leaderId: { type: 'string' },
      metadata: { type: 'object' }
    }
  }
};

const getTeamsSchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'integer', minimum: 1, default: 1 },
      limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
      search: { type: 'string' },
      projectId: { type: 'string' },
      leaderId: { type: 'string' },
      memberId: { type: 'string' },
      sortBy: { type: 'string', enum: ['name', 'createdAt', 'updatedAt', 'memberCount'] },
      sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
    }
  }
};

const teamParamsSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  }
};

const manageMembersSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    required: ['memberIds'],
    properties: {
      memberIds: { type: 'array', items: { type: 'string' } }
    }
  }
};

const assignProjectSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    required: ['projectId'],
    properties: {
      projectId: { type: 'string' }
    }
  }
};

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    email: string;
    role: string;
    permissions: string[];
  };
}

async function teamRoutes(fastify: FastifyInstance) {
  // Create team
  fastify.post('/teams', {
    schema: createTeamSchema,
    preHandler: [authenticateToken, requirePermission('teams:create')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const teamData = request.body as any;
      const userId = request.user!.id;
      
      const team = await teamService.createTeam({
        ...teamData,
        createdBy: userId
      });
      
      logger.info(`Team created: ${team.id}`, {
        teamId: team.id,
        teamName: team.name,
        userId
      });
      
      reply.code(201).send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error creating team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to create team'
      });
    }
  });
  
  // Get teams with filtering and pagination
  fastify.get('/teams', {
    schema: getTeamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const query = request.query as any;
      const userId = request.user!.id;
      const userRole = request.user!.role;
      
      // Apply user-based filtering for non-admin users
      if (userRole !== 'admin' && userRole !== 'manager') {
        query.memberId = userId;
      }
      
      const result = await teamService.getTeams(query);
      
      reply.send({
        success: true,
        data: result.teams,
        pagination: {
          page: query.page || 1,
          limit: query.limit || 20,
          total: result.total,
          totalPages: Math.ceil(result.total / (query.limit || 20))
        }
      });
    } catch (error) {
      logger.error('Error fetching teams:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch teams'
      });
    }
  });
  
  // Get team by ID
  fastify.get('/teams/:id', {
    schema: teamParamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user!.id;
      const userRole = request.user!.role;
      
      const team = await teamService.getTeamById(id);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      // Check if user has access to this team
      if (userRole !== 'admin' && userRole !== 'manager' && 
          !team.memberIds.includes(userId) && team.leaderId !== userId) {
        return reply.code(403).send({
          success: false,
          error: 'Access denied'
        });
      }
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error fetching team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch team'
      });
    }
  });
  
  // Update team
  fastify.put('/teams/:id', {
    schema: updateTeamSchema,
    preHandler: [authenticateToken, requirePermission('teams:update')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = request.body as any;
      const userId = request.user!.id;
      
      const team = await teamService.updateTeam(id, updateData, userId);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Team updated: ${id}`, {
        teamId: id,
        userId
      });
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error updating team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to update team'
      });
    }
  });
  
  // Delete team
  fastify.delete('/teams/:id', {
    schema: teamParamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:delete')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user!.id;
      
      const success = await teamService.deleteTeam(id, userId);
      
      if (!success) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Team deleted: ${id}`, {
        teamId: id,
        userId
      });
      
      reply.send({
        success: true,
        message: 'Team deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to delete team'
      });
    }
  });
  
  // Add members to team
  fastify.post('/teams/:id/members', {
    schema: manageMembersSchema,
    preHandler: [authenticateToken, requirePermission('teams:manage_members')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const { memberIds } = request.body as any;
      const userId = request.user!.id;
      
      const team = await teamService.addMembers(id, memberIds, userId);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Members added to team: ${id}`, {
        teamId: id,
        memberIds,
        userId
      });
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error adding team members:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to add team members'
      });
    }
  });
  
  // Remove members from team
  fastify.delete('/teams/:id/members', {
    schema: manageMembersSchema,
    preHandler: [authenticateToken, requirePermission('teams:manage_members')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const { memberIds } = request.body as any;
      const userId = request.user!.id;
      
      const team = await teamService.removeMembers(id, memberIds, userId);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Members removed from team: ${id}`, {
        teamId: id,
        memberIds,
        userId
      });
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error removing team members:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to remove team members'
      });
    }
  });
  
  // Assign project to team
  fastify.post('/teams/:id/projects', {
    schema: assignProjectSchema,
    preHandler: [authenticateToken, requirePermission('teams:assign_projects')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const { projectId } = request.body as any;
      const userId = request.user!.id;
      
      const team = await teamService.assignProject(id, projectId, userId);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Project assigned to team: ${id}`, {
        teamId: id,
        projectId,
        userId
      });
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error assigning project to team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to assign project to team'
      });
    }
  });
  
  // Remove project from team
  fastify.delete('/teams/:id/projects/:projectId', {
    params: {
      type: 'object',
      required: ['id', 'projectId'],
      properties: {
        id: { type: 'string' },
        projectId: { type: 'string' }
      }
    },
    preHandler: [authenticateToken, requirePermission('teams:assign_projects')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id, projectId } = request.params as { id: string; projectId: string };
      const userId = request.user!.id;
      
      const team = await teamService.removeProject(id, projectId, userId);
      
      if (!team) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      logger.info(`Project removed from team: ${id}`, {
        teamId: id,
        projectId,
        userId
      });
      
      reply.send({
        success: true,
        data: team
      });
    } catch (error) {
      logger.error('Error removing project from team:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to remove project from team'
      });
    }
  });
  
  // Get team analytics
  fastify.get('/teams/:id/analytics', {
    schema: teamParamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:analytics')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      
      const analytics = await teamService.getTeamAnalytics(id);
      
      if (!analytics) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      reply.send({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching team analytics:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch team analytics'
      });
    }
  });
  
  // Get team workload
  fastify.get('/teams/:id/workload', {
    schema: teamParamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      
      const workload = await teamService.getTeamWorkload(id);
      
      if (!workload) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      reply.send({
        success: true,
        data: workload
      });
    } catch (error) {
      logger.error('Error fetching team workload:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch team workload'
      });
    }
  });
  
  // Get team performance metrics
  fastify.get('/teams/:id/performance', {
    schema: teamParamsSchema,
    preHandler: [authenticateToken, requirePermission('teams:analytics')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      
      const performance = await teamService.getTeamPerformance(id);
      
      if (!performance) {
        return reply.code(404).send({
          success: false,
          error: 'Team not found'
        });
      }
      
      reply.send({
        success: true,
        data: performance
      });
    } catch (error) {
      logger.error('Error fetching team performance:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch team performance'
      });
    }
  });
}

export default fp(teamRoutes, {
  name: 'team-routes'
});