{"name": "@estratix/smart-contracts-service", "version": "1.0.0", "description": "Smart Contracts microservice for ESTRATIX platform - handles Web3 interactions, property tokenization, DeFi protocols, staking, and governance", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "deploy:contracts": "hardhat run scripts/deploy.ts", "compile:contracts": "hardhat compile", "verify:contracts": "hardhat verify", "generate:types": "typechain --target ethers-v6 --out-dir src/types/contracts 'contracts/**/*.sol'"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/multipart": "^8.3.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.1.0", "@nomicfoundation/hardhat-toolbox": "^5.0.0", "@openzeppelin/contracts": "^5.1.0", "@openzeppelin/contracts-upgradeable": "^5.1.0", "@prisma/client": "^5.22.0", "@typechain/ethers-v6": "^0.5.1", "@typechain/hardhat": "^9.1.0", "@types/jsonwebtoken": "^9.0.10", "axios": "^1.7.7", "bullmq": "^5.15.0", "dotenv": "^16.4.5", "ethers": "^6.13.4", "fastify": "^4.28.1", "fastify-plugin": "^5.0.1", "hardhat": "^2.22.15", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "pino": "^9.4.0", "typechain": "^8.3.2", "viem": "^2.21.19", "wagmi": "^2.12.17", "zod": "^3.23.8"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.16.5", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.25.0", "jest": "^29.7.0", "prisma": "^5.22.0", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "keywords": ["blockchain", "smart-contracts", "web3", "defi", "property-tokenization", "staking", "governance", "ethereum", "polygon", "real-estate"], "author": "ESTRATIX Development Team", "license": "MIT"}