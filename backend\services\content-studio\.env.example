# Server Configuration
NODE_ENV=development
SERVER_HOST=localhost
SERVER_PORT=3001

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:5173
CORS_CREDENTIALS=true
CORS_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS

# AI Service APIs
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
STABILITY_API_KEY=your-stability-ai-api-key

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/estratix_content_studio
MONGODB_DB_NAME=estratix_content_studio

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Vector Database (Milvus)
MILVUS_HOST=localhost:19530
MILVUS_USERNAME=
MILVUS_PASSWORD=
MILVUS_SSL=false

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=
QUEUE_REDIS_DB=1

# File Storage
FILE_STORAGE_TYPE=local
FILE_STORAGE_PATH=./uploads
FILE_STORAGE_MAX_SIZE=10485760

# AWS S3 (if using S3 storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
AWS_S3_BUCKET=

# Analytics
ANALYTICS_ENABLED=true
ANALYTICS_PROVIDER=internal

# External APIs
UNSPLASH_ACCESS_KEY=your-unsplash-access-key
PEXELS_API_KEY=your-pexels-api-key

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Development
ENABLE_SWAGGER=true
ENABLE_CORS=true
ENABLE_HELMET=true