import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  FileText, Shield, CreditCard, Users, Globe,
  AlertTriangle, CheckCircle, Clock, Scale,
  Download, Printer, ExternalLink, ChevronRight
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface Section {
  id: string;
  title: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Terms: React.FC = () => {
  const { t } = useTranslation();
  const [activeSection, setActiveSection] = useState<string>('general');

  const lastUpdated = '15 de Diciembre, 2024';
  const effectiveDate = '1 de Enero, 2025';

  const sections: Section[] = [
    {
      id: 'general',
      title: 'Términos Generales',
      icon: <FileText className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">1. Aceptación de Términos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Al acceder y utilizar Sorteo Estelar ("la Plataforma"), usted acepta estar sujeto a estos Términos y Condiciones de Uso ("Términos"). Si no está de acuerdo con alguna parte de estos términos, no debe utilizar nuestros servicios.
            </p>
            <p className="text-white/80 leading-relaxed">
              Estos términos constituyen un acuerdo legal vinculante entre usted y Sorteo Estelar Inc. ("nosotros", "nuestro", "la Compañía"). Nos reservamos el derecho de modificar estos términos en cualquier momento, y dichas modificaciones entrarán en vigor inmediatamente después de su publicación en la Plataforma.
            </p>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">2. Definiciones</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <ChevronRight className="w-4 h-4 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <span className="text-purple-400 font-medium">"Usuario":</span>
                  <span className="text-white/80 ml-2">Cualquier persona que acceda o utilice la Plataforma.</span>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <ChevronRight className="w-4 h-4 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <span className="text-purple-400 font-medium">"Sorteo":</span>
                  <span className="text-white/80 ml-2">Cualquier juego de lotería o sorteo disponible en la Plataforma.</span>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <ChevronRight className="w-4 h-4 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <span className="text-purple-400 font-medium">"Smart Contract":</span>
                  <span className="text-white/80 ml-2">Contratos autoejecutables en blockchain que gestionan los sorteos.</span>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <ChevronRight className="w-4 h-4 text-purple-400 mt-1 flex-shrink-0" />
                <div>
                  <span className="text-purple-400 font-medium">"Criptomoneda":</span>
                  <span className="text-white/80 ml-2">Monedas digitales aceptadas como método de pago en la Plataforma.</span>
                </div>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">3. Elegibilidad</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Para utilizar nuestros servicios, debe:
            </p>
            <ul className="space-y-2">
              <li className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-white/80">Tener al menos 18 años de edad (o la mayoría de edad en su jurisdicción)</span>
              </li>
              <li className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-white/80">Residir en una jurisdicción donde nuestros servicios sean legales</span>
              </li>
              <li className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-white/80">Proporcionar información precisa y completa durante el registro</span>
              </li>
              <li className="flex items-center space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                <span className="text-white/80">Cumplir con todas las leyes y regulaciones aplicables</span>
              </li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'privacy',
      title: 'Privacidad y Datos',
      icon: <Shield className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">4. Recopilación de Datos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Recopilamos información necesaria para proporcionar nuestros servicios de manera segura y eficiente:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Información Personal</h4>
                <ul className="text-white/70 text-sm space-y-1">
                  <li>• Nombre completo</li>
                  <li>• Dirección de email</li>
                  <li>• Fecha de nacimiento</li>
                  <li>• Dirección física</li>
                  <li>• Número de teléfono</li>
                </ul>
              </div>
              <div className="bg-white/5 rounded-lg p-4">
                <h4 className="text-white font-medium mb-2">Información Técnica</h4>
                <ul className="text-white/70 text-sm space-y-1">
                  <li>• Dirección IP</li>
                  <li>• Tipo de dispositivo</li>
                  <li>• Navegador utilizado</li>
                  <li>• Actividad en la plataforma</li>
                  <li>• Preferencias de usuario</li>
                </ul>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">5. Uso de Datos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Utilizamos sus datos para:
            </p>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Verificar su identidad y cumplir con regulaciones KYC/AML</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Procesar transacciones y gestionar su cuenta</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Mejorar nuestros servicios y experiencia de usuario</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Comunicar actualizaciones importantes y promociones</span>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Detectar y prevenir actividades fraudulentas</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">6. Protección de Datos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Implementamos medidas de seguridad robustas para proteger su información:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                <Shield className="w-8 h-8 text-green-400 mb-2" />
                <h4 className="text-white font-medium mb-2">Encriptación</h4>
                <p className="text-white/70 text-sm">AES-256 para datos en reposo y TLS 1.3 para datos en tránsito</p>
              </div>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                <Users className="w-8 h-8 text-blue-400 mb-2" />
                <h4 className="text-white font-medium mb-2">Acceso Limitado</h4>
                <p className="text-white/70 text-sm">Solo personal autorizado con necesidad legítima puede acceder</p>
              </div>
              <div className="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
                <Clock className="w-8 h-8 text-purple-400 mb-2" />
                <h4 className="text-white font-medium mb-2">Monitoreo 24/7</h4>
                <p className="text-white/70 text-sm">Supervisión continua para detectar accesos no autorizados</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'payments',
      title: 'Pagos y Transacciones',
      icon: <CreditCard className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">7. Métodos de Pago Aceptados</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Aceptamos múltiples métodos de pago para su conveniencia:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-white font-medium mb-3 flex items-center">
                  <CreditCard className="w-5 h-5 text-blue-400 mr-2" />
                  Métodos Tradicionales
                </h4>
                <ul className="space-y-2">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Tarjetas de crédito/débito (Visa, Mastercard, Amex)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Transferencias bancarias</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">PayPal</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Wise (TransferWise)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Payoneer</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-white font-medium mb-3 flex items-center">
                  <Globe className="w-5 h-5 text-amber-400 mr-2" />
                  Criptomonedas
                </h4>
                <ul className="space-y-2">
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Bitcoin (BTC)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Ethereum (ETH)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">USD Coin (USDC)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Tether (USDT)</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <CheckCircle className="w-4 h-4 text-green-400" />
                    <span className="text-white/80">Polygon (MATIC)</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">8. Comisiones y Tarifas</h3>
            <div className="bg-amber-500/10 border border-amber-500/20 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-amber-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-amber-400 font-medium mb-1">Transparencia en Tarifas</h4>
                  <p className="text-white/80 text-sm">
                    Todas las comisiones se muestran claramente antes de confirmar cualquier transacción. 
                    No hay tarifas ocultas.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b border-white/20">
                    <th className="text-left text-white font-medium py-3">Método de Pago</th>
                    <th className="text-left text-white font-medium py-3">Comisión de Depósito</th>
                    <th className="text-left text-white font-medium py-3">Tiempo de Procesamiento</th>
                  </tr>
                </thead>
                <tbody className="text-white/80">
                  <tr className="border-b border-white/10">
                    <td className="py-3">Tarjetas de Crédito/Débito</td>
                    <td className="py-3">2.9% + $0.30</td>
                    <td className="py-3">Instantáneo</td>
                  </tr>
                  <tr className="border-b border-white/10">
                    <td className="py-3">Transferencia Bancaria</td>
                    <td className="py-3">$5.00 fijo</td>
                    <td className="py-3">1-3 días hábiles</td>
                  </tr>
                  <tr className="border-b border-white/10">
                    <td className="py-3">PayPal</td>
                    <td className="py-3">3.4% + $0.30</td>
                    <td className="py-3">Instantáneo</td>
                  </tr>
                  <tr className="border-b border-white/10">
                    <td className="py-3">Wise/Payoneer</td>
                    <td className="py-3">1.5% + $2.00</td>
                    <td className="py-3">1-2 días hábiles</td>
                  </tr>
                  <tr>
                    <td className="py-3">Criptomonedas</td>
                    <td className="py-3">Solo gas fees de red</td>
                    <td className="py-3">1-30 minutos</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">9. Política de Reembolsos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Debido a la naturaleza de los sorteos y loterías, generalmente no ofrecemos reembolsos una vez que se ha confirmado la participación. Sin embargo, consideramos reembolsos en las siguientes circunstancias:
            </p>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 mt-1 flex-shrink-0" />
                <span className="text-white/80">Errores técnicos de la plataforma que impidan la participación</span>
              </li>
              <li className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 mt-1 flex-shrink-0" />
                <span className="text-white/80">Cancelación de sorteos por causas de fuerza mayor</span>
              </li>
              <li className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 mt-1 flex-shrink-0" />
                <span className="text-white/80">Transacciones duplicadas por error del sistema</span>
              </li>
              <li className="flex items-start space-x-2">
                <CheckCircle className="w-4 h-4 text-green-400 mt-1 flex-shrink-0" />
                <span className="text-white/80">Actividad fraudulenta verificada en la cuenta</span>
              </li>
            </ul>
          </div>
        </div>
      )
    },
    {
      id: 'conduct',
      title: 'Conducta del Usuario',
      icon: <Users className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">10. Uso Aceptable</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Al utilizar nuestra plataforma, usted se compromete a:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="text-green-400 font-medium mb-3 flex items-center">
                  <CheckCircle className="w-5 h-5 mr-2" />
                  Comportamientos Permitidos
                </h4>
                <ul className="space-y-2">
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Usar la plataforma de manera responsable</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Proporcionar información veraz y actualizada</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Respetar los derechos de otros usuarios</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Cumplir con todas las leyes aplicables</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Reportar actividades sospechosas</span>
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="text-red-400 font-medium mb-3 flex items-center">
                  <AlertTriangle className="w-5 h-5 mr-2" />
                  Comportamientos Prohibidos
                </h4>
                <ul className="space-y-2">
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Crear múltiples cuentas</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Usar bots o scripts automatizados</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Intentar manipular sorteos</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Realizar actividades fraudulentas</span>
                  </li>
                  <li className="flex items-start space-x-2">
                    <div className="w-2 h-2 bg-red-400 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-white/80">Violar derechos de propiedad intelectual</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">11. Consecuencias por Violaciones</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Las violaciones a estos términos pueden resultar en:
            </p>
            <div className="space-y-3">
              <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                <h4 className="text-yellow-400 font-medium mb-2">Advertencias</h4>
                <p className="text-white/80 text-sm">Para violaciones menores, emitiremos advertencias y oportunidades de corrección.</p>
              </div>
              <div className="bg-orange-500/10 border border-orange-500/20 rounded-lg p-4">
                <h4 className="text-orange-400 font-medium mb-2">Suspensión Temporal</h4>
                <p className="text-white/80 text-sm">Restricción temporal del acceso a la cuenta por violaciones moderadas.</p>
              </div>
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <h4 className="text-red-400 font-medium mb-2">Suspensión Permanente</h4>
                <p className="text-white/80 text-sm">Cierre definitivo de la cuenta por violaciones graves o reincidencia.</p>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">12. Proceso de Apelación</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Si considera que su cuenta ha sido suspendida injustamente, puede apelar siguiendo estos pasos:
            </p>
            <ol className="space-y-3">
              <li className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-400 text-sm font-medium">1</span>
                </div>
                <span className="text-white/80">Envíe un <NAME_EMAIL> con detalles de su caso</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-400 text-sm font-medium">2</span>
                </div>
                <span className="text-white/80">Proporcione evidencia que respalde su apelación</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-400 text-sm font-medium">3</span>
                </div>
                <span className="text-white/80">Nuestro equipo revisará su caso en 5-7 días hábiles</span>
              </li>
              <li className="flex items-start space-x-3">
                <div className="w-6 h-6 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-purple-400 text-sm font-medium">4</span>
                </div>
                <span className="text-white/80">Recibirá una respuesta detallada con la decisión final</span>
              </li>
            </ol>
          </div>
        </div>
      )
    },
    {
      id: 'legal',
      title: 'Aspectos Legales',
      icon: <Scale className="w-5 h-5" />,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">13. Limitación de Responsabilidad</h3>
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="w-5 h-5 text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <h4 className="text-red-400 font-medium mb-1">Aviso Importante</h4>
                  <p className="text-white/80 text-sm">
                    Los sorteos y loterías implican riesgo. Nunca invierta más de lo que puede permitirse perder.
                  </p>
                </div>
              </div>
            </div>
            
            <p className="text-white/80 leading-relaxed mb-4">
              En la máxima medida permitida por la ley, Sorteo Estelar no será responsable por:
            </p>
            <ul className="space-y-2">
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-white/50 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Pérdidas financieras derivadas de la participación en sorteos</span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-white/50 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Interrupciones del servicio por mantenimiento o causas técnicas</span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-white/50 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Fluctuaciones en el valor de criptomonedas</span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-white/50 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Acciones de terceros, incluyendo hackers o gobiernos</span>
              </li>
              <li className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-white/50 rounded-full mt-2 flex-shrink-0"></div>
                <span className="text-white/80">Cambios en regulaciones que afecten nuestros servicios</span>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">14. Ley Aplicable y Jurisdicción</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Estos términos se rigen por las leyes del Estado de Delaware, Estados Unidos. 
              Cualquier disputa será resuelta en los tribunales competentes de Delaware.
            </p>
            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
              <h4 className="text-blue-400 font-medium mb-2">Resolución de Disputas</h4>
              <p className="text-white/80 text-sm mb-3">
                Preferimos resolver disputas de manera amigable. Antes de iniciar acciones legales, 
                le pedimos que contacte nuestro equipo de soporte para buscar una solución.
              </p>
              <p className="text-white/80 text-sm">
                Para disputas que no puedan resolverse directamente, ofrecemos mediación 
                a través de servicios profesionales de resolución de conflictos.
              </p>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">15. Modificaciones a los Términos</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Nos reservamos el derecho de modificar estos términos en cualquier momento. 
              Los cambios importantes serán notificados con al menos 30 días de anticipación.
            </p>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-white/80">Notificación por email a todos los usuarios registrados</span>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-white/80">Aviso prominente en la plataforma</span>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-white/80">Período de gracia para revisar los cambios</span>
              </div>
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-5 h-5 text-green-400 mt-0.5 flex-shrink-0" />
                <span className="text-white/80">Opción de cerrar cuenta si no acepta los nuevos términos</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-xl font-semibold text-white mb-3">16. Contacto Legal</h3>
            <p className="text-white/80 leading-relaxed mb-4">
              Para asuntos legales, puede contactarnos en:
            </p>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="space-y-2">
                <div className="flex items-center space-x-3">
                  <Scale className="w-5 h-5 text-purple-400" />
                  <span className="text-white font-medium">Departamento Legal</span>
                </div>
                <div className="text-white/80 text-sm space-y-1">
                  <p>Sorteo Estelar Inc.</p>
                  <p>123 Blockchain Avenue, Suite 456</p>
                  <p>San Francisco, CA 94105</p>
                  <p>Email: <EMAIL></p>
                  <p>Teléfono: +****************</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  const navigationItems = [
    { id: 'general', label: 'General' },
    { id: 'privacy', label: 'Privacidad' },
    { id: 'payments', label: 'Pagos' },
    { id: 'conduct', label: 'Conducta' },
    { id: 'legal', label: 'Legal' }
  ];

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Términos y <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Condiciones</span>
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-6">
            Lea cuidadosamente estos términos y condiciones antes de utilizar nuestros servicios. 
            Su uso de la plataforma constituye la aceptación de estos términos.
          </p>
          
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-white/70">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4" />
              <span className="text-sm">Última actualización: {lastUpdated}</span>
            </div>
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-4 h-4" />
              <span className="text-sm">Efectivo desde: {effectiveDate}</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <Card variant="glass" className="mb-8">
          <div className="flex flex-col sm:flex-row items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <FileText className="w-6 h-6 text-purple-400" />
              <div>
                <h3 className="text-white font-semibold">Documento Completo</h3>
                <p className="text-white/70 text-sm">Acceda a versiones descargables de nuestros términos</p>
              </div>
            </div>
            
            <div className="flex space-x-3">
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                Descargar PDF
              </Button>
              <Button variant="outline" size="sm">
                <Printer className="w-4 h-4 mr-2" />
                Imprimir
              </Button>
            </div>
          </div>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Navigation Sidebar */}
          <div className="lg:col-span-1">
            <Card variant="glass" className="sticky top-8">
              <h3 className="text-white font-semibold mb-4 flex items-center">
                <FileText className="w-5 h-5 mr-2 text-purple-400" />
                Secciones
              </h3>
              <nav className="space-y-2">
                {navigationItems.map(item => {
                  const section = sections.find(s => s.id === item.id);
                  const isActive = activeSection === item.id;
                  return (
                    <button
                      key={item.id}
                      onClick={() => setActiveSection(item.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors duration-200 ${
                        isActive
                          ? 'bg-purple-500/20 text-purple-400 border border-purple-500/30'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      {section?.icon}
                      <span className="font-medium">{item.label}</span>
                    </button>
                  );
                })}
              </nav>
            </Card>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            <Card variant="glass">
              {sections.map(section => (
                <div
                  key={section.id}
                  className={activeSection === section.id ? 'block' : 'hidden'}
                >
                  <div className="flex items-center space-x-3 mb-6 pb-4 border-b border-white/10">
                    <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                      <div className="text-purple-400">
                        {section.icon}
                      </div>
                    </div>
                    <h2 className="text-2xl font-bold text-white">{section.title}</h2>
                  </div>
                  
                  <div className="prose prose-invert max-w-none">
                    {section.content}
                  </div>
                </div>
              ))}
            </Card>
          </div>
        </div>

        {/* Footer Notice */}
        <Card variant="glass" className="mt-12">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 bg-blue-500/20 rounded-full mx-auto flex items-center justify-center">
              <Scale className="w-8 h-8 text-blue-400" />
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-2">¿Preguntas sobre estos términos?</h3>
              <p className="text-white/80 max-w-2xl mx-auto">
                Si tiene alguna pregunta sobre estos términos y condiciones, 
                no dude en contactar a nuestro equipo legal.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary">
                <ExternalLink className="w-4 h-4 mr-2" />
                Contactar Equipo Legal
              </Button>
              <Button variant="outline">
                <HelpCircle className="w-4 h-4 mr-2" />
                Preguntas Frecuentes
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Terms;