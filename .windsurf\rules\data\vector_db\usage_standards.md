# Vector Database Usage Standards

This document provides the standards for using vector databases (e.g., Milvus, Qdrant, Weaviate) within the ESTRATIX framework. These rules ensure our knowledge bases are efficient, scalable, and effective.

## 1. Core Principles

- **Purpose-Driven Design:** Collections and schemas must be designed for specific use cases (e.g., document retrieval, image similarity, recommendation).
- **Metadata is Key:** Store rich, filterable metadata alongside vectors to enable powerful hybrid search capabilities.
- **Performance and Cost:** Choose indexing strategies and vector dimensions that balance search performance with computational and storage costs.

## 2. Schema and Collection Design

- **Consistency:** Use a consistent naming convention for collections, fields, and indexes (e.g., `snake_case`).
- **Data Modeling:** Define clear Pydantic models for the data to be vectorized and stored. These models are the source of truth for the collection schema.
- **Vectorization Strategy:** Document the embedding model used for each collection to ensure consistency during indexing and querying.

## 3. Indexing

- **Appropriate Indexes:** Select the correct index type (e.g., HNSW, IVF_FLAT) based on the data size, query latency requirements, and desired accuracy.
- **Batching:** Ingest data in batches to optimize network traffic and database performance.
- **Regular Optimization:** For databases that require it, schedule regular tasks to build, optimize, or compact indexes.

## 4. Querying

- **Hybrid Search:** Whenever possible, combine vector similarity search with metadata filtering to achieve more precise results.
- **Parameterization:** Tune search parameters (e.g., 
probe, f_search) for the specific use case to balance speed and recall.
- **API Abstraction:** Interact with vector databases through a dedicated infrastructure adapter (port-and-adapter pattern) to isolate the application from specific client libraries.
