# ESTRATIX User Story Template

## Document Control (for this Template)
- **Template Version:** ESTRATIX-TEMPL-SER-US-1.0
- **Status:** Approved
- **Author(s):** `AGENT_Template_Creator` (ID: AGENT_TC001)
- **Reviewer(s):** `AGENT_Agile_Coach` (ID: AGENT_CPO_AC001)
- **Approver(s):** `AGENT_CPO_Lead` (ID: AGENT_CPO_L001)
- **Date Created:** `{{YYYY-MM-DD}}`
- **Last Updated Date:** `{{YYYY-MM-DD}}`
- **Security Classification:** ESTRATIX Internal

## Guidance for Use (ESTRATIX)

This User Story template is a fundamental artifact in ESTRATIX agile development, designed to capture requirements from the end-user's perspective. It facilitates clear communication between the product owner, development team, and stakeholders, ensuring that developed features deliver tangible value.

- **Purpose:** To define a small, valuable piece of functionality in a concise, understandable format.
- **INVEST Criteria:** Strive for user stories that are Independent, Negotiable, Valuable, Estimable, Small, and Testable. `AGENT_User_Story_Validator` (ID: AGENT_CPO_USV001) can assist in assessing stories against these criteria.
- **Collaborative Creation & Refinement:** User stories are typically written by the Product Owner (`AGENT_CPO_PO001`) or Business Analyst (`AGENT_CPO_BA001`) in collaboration with the development team and stakeholders. `AGENT_User_Story_Workshop_Facilitator` (ID: AGENT_CPO_USWF001) can guide backlog refinement sessions.
- **Acceptance Criteria are Key:** Well-defined acceptance criteria are crucial for development and testing. They form the basis for test case creation by `AGENT_QA_Test_Designer` (ID: AGENT_CTO_QATD001) and automated test generation by `AGENT_Automated_Test_Generator` (ID: AGENT_CTO_ATG001).
- **Task Breakdown:** Once a story is ready for a sprint, `AGENT_Task_Breakdown_Assistant` (ID: AGENT_CTO_TBA001) can help the development team decompose it into actionable technical tasks.
- **Living Document:** User stories evolve. They are refined throughout the project lifecycle, especially during backlog grooming and sprint planning. All versions should be tracked in the ESTRATIX project management tool.
- **Linkage:** User stories should be linked to higher-level requirements (e.g., BRD/SRS IDs, Epics, Features) for traceability, managed by `AGENT_Traceability_Manager` (ID: AGENT_CPO_TMM001).

---

## User Story

**Story ID:** `{{ProjectID_SprintID_StoryNumber, e.g., ESTRATIX_PROJ_XYZ_S01_US001}}`

**Title:** `{{Short, descriptive title of the user story}}`

**User Role/Persona:**
As a `{{Type of user, e.g., Registered Customer, System Administrator, Marketing Manager}}`

**Goal/Action:**
I want to `{{Perform an action or achieve a goal, e.g., view my order history, generate a sales report}}`

**Benefit/Value:**
So that `{{I can realize a benefit or value, e.g., I can track my past purchases, I can analyze sales performance}}`

---

### Acceptance Criteria
`[List specific, testable criteria that must be met for the story to be considered complete. Use GIVEN/WHEN/THEN format where appropriate. AGENT_Acceptance_Criteria_Generator (ID: AGENT_CPO_ACG001) can help formulate these.]`

1.  **AC1:** `{{GIVEN [context] WHEN [action] THEN [outcome]}}`
2.  **AC2:** `{{GIVEN [context] WHEN [action] THEN [outcome]}}`
3.  **AC3:** `{{Criteria description}}`
    *   `{{Sub-criteria 3.1}}`
    *   `{{Sub-criteria 3.2}}`

---

### Details

*   **Priority:** `{{e.g., Critical, High, Medium, Low / MoSCoW: Must Have, Should Have, Could Have, Won't Have This Time}}`
*   **Story Points:** `{{Estimate of effort, e.g., 1, 2, 3, 5, 8, 13 - using Fibonacci sequence}}`
*   **Status:** `{{e.g., New, Ready for Dev, In Development, Ready for QA, In QA, Done, Blocked}}`
*   **Sprint Assigned:** `{{Sprint ID or Name, e.g., Sprint 2024.03}}`
*   **Epic/Feature Link:** `{{Link to parent Epic or Feature ID, e.g., EPIC_005, FEAT_012}}`
*   **Related BRD/SRS ID(s):** `{{Link to specific Business Requirement or Software Requirement ID(s)}}`
*   **Dependencies:** `{{List any other user stories, technical tasks, or external factors this story depends on, or that depend on this story. AGENT_Dependency_Analyzer (ID: AGENT_CTO_DA001) can help identify these.}}`
    *   `{{e.g., US_00X must be completed first.}}`
    *   `{{e.g., Requires API from Team Y.}}`
*   **Technical Notes/Assumptions:** `{{Any technical considerations, implementation suggestions, or assumptions relevant to this story. Input from AGENT_Technical_Advisor (ID: AGENT_CTO_TA001).}}`
    *   `{{e.g., Assumes use of existing authentication service.}}`
    *   `{{e.g., Consider using X library for this feature.}}`
*   **UI/UX Mockup/Wireframe Links:** `{{Link to relevant design artifacts, e.g., Figma link, screenshot path}}`
*   **Attachments:** `{{List or link to any other supporting documents or files.}}`

---

### ESTRATIX Agent Support

*   **`AGENT_User_Story_Refiner` (ID: AGENT_CPO_USR001):** Reviews for clarity, INVEST criteria, and testability.
*   **`AGENT_Acceptance_Criteria_Validator` (ID: AGENT_CPO_ACV001):** Validates acceptance criteria for completeness and testability.
*   **`AGENT_Task_Breakdown_Assistant` (ID: AGENT_CTO_TBA001):** Assists development team in creating sub-tasks.
*   **`AGENT_Effort_Estimator_Advisor` (ID: AGENT_CPO_EEA001):** Provides historical data or patterns to aid in story point estimation.

---
*This User Story is a component of the ESTRATIX project backlog. It is subject to refinement, prioritization, and scheduling as per ESTRATIX agile project management practices (Ref: `CPO_P00Y_AgileMethodologyGuide`). All changes should be tracked in the designated ESTRATIX project management tool.*
