# ESTRATIX Project Change Log

## Document Control
*   **Document Title:** Project Change Log
*   **Log Template Version:** `[e.g., 1.0 - Version of this log template]`
*   **Security Classification (of this Log):** `[e.g., ESTRATIX Internal, Client Confidential]`

## Register Information
*   **Project Name:** `[Full Official Project Name]`
*   **Project ID:** `[Unique Project Identifier]`
*   **Change Log Version (Live Data):** `[e.g., 1.0, incremented with significant updates to the log data]`
*   **Last Updated:** `[YYYY-MM-DD HH:MM Z]` (Timestamp of the last modification to this log)
*   **Maintained by:** `[Project Manager Name / Role / ESTRATIX Agent ID, e.g., CPO_AXXX_ChangeCoordinatorAgent]`

---

| CR ID | CR Version | Change Title | Requester | Date Submitted | Category | Priority | Status | Impact Summary (Scope, Schedule, Cost, Quality, Risk, Resources, Agents) | CCB Decision | Decision Date | Implementation Status | Target Completion Date | Actual Completion Date | Implementation Lead (Agent/Team/Role) | CRF Security Classification | Notes / Links to CRF |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|
| `[ProjectID_CR_XXX]` | `[e.g., 1.0]` | `[Brief, descriptive title from CRF]` | `[Requester Name]` | `[YYYY-MM-DD]` | `[e.g., Scope, Schedule, Cost, Technical, Process]` | `[e.g., Critical, High, Medium, Low]` | `[e.g., Submitted, Under Review, Pending Impact Assessment, Pending CCB Approval, Approved, Rejected, Deferred, In Implementation, Implemented, Verified, Closed]` | `[Brief summary of key impacts, including agent impacts]` | `[e.g., Approved, Rejected, Deferred, Approved with Conditions]` | `[YYYY-MM-DD]` | `[e.g., Not Started, In Progress, On Hold, Completed, Cancelled]` | `[YYYY-MM-DD]` | `[YYYY-MM-DD]` | `[Agent ID / Team Name / Role]` | `[Security Class. of linked CRF]` | `[Link to specific CRF document, e.g., ./Change_Requests/CR_XXX.md]` |
| `[ProjectID_CR_YYY]` | `[e.g., 1.0]` | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... |
| `[ProjectID_CR_ZZZ]` | `[e.g., 1.1]` | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... | ... |

---

**Instructions for Use:**

*   **CR ID:** Unique identifier for the Change Request, typically `[ProjectID_CR_SequentialNumber]`.
*   **CR Version:** Version of the Change Request Form being logged (e.g., if a CR is revised and resubmitted).
*   **Change Title:** The concise title of the change from the CRF.
*   **Requester:** Name of the individual or entity requesting the change.
*   **Date Submitted:** Date the CRF was formally submitted.
*   **Category:** Classification of the change (e.g., Scope, Schedule, Cost, Resource, Technical, Process, Policy, External). Aligns with CRF.
*   **Priority:** Urgency/importance of the change (e.g., Critical, High, Medium, Low). Aligns with CRF.
*   **Status:** Current lifecycle status of the CR. Examples:
    *   `Submitted`: CRF received and logged.
    *   `Under Review`: Initial review by PM/Coordinator.
    *   `Pending Impact Assessment`: Awaiting detailed impact analysis.
    *   `Pending CCB Approval`: Awaiting CCB decision.
    *   `Approved`: Change request approved by CCB/Authority.
    *   `Rejected`: Change request rejected.
    *   `Deferred`: Decision on change postponed.
    *   `Approved with Conditions`: Approved, but with specific conditions to be met.
    *   `In Implementation`: Approved change is being implemented.
    *   `On Hold`: Implementation is paused.
    *   `Implemented`: Change has been put in place.
    *   `Verification Pending`: Awaiting verification of implementation.
    *   `Verified`: Implemented change has been confirmed as correct.
    *   `Closed`: Change process complete, including verification and documentation updates.
    *   `Cancelled`: Change request withdrawn or cancelled.
*   **Impact Summary:** A brief overview of the assessed impacts on key project dimensions.
*   **CCB Decision:** The formal decision made by the Change Control Board or approving authority.
*   **Decision Date:** Date the CCB decision was made.
*   **Implementation Status:** Current status of the work to implement the approved change.
*   **Target Completion Date:** Planned date for completing the implementation of the change.
*   **Actual Completion Date:** Actual date the change implementation was completed and verified.
*   **Implementation Lead (Agent/Team/Role):** The ESTRATIX Agent ID, team, or role responsible for leading the implementation of the change.
*   **CRF Security Classification:** The security classification of the linked Change Request Form (e.g., ESTRATIX Internal, Client Confidential).
*   **Notes / Links to CRF:** Any relevant notes, comments, or direct links to the detailed Change Request Form document.

**Guidance for Use (Continued from Instructions):**

*   This Change Log is a living document and should be updated promptly as the status of any Change Request evolves.
*   It serves as the central register for all changes, providing an auditable trail of decisions and actions.
*   Ensure that links to individual Change Request Forms (CRFs) are accurate and maintained.
*   When a change impacts ESTRATIX agents (as detailed in the CRF's impact assessment), ensure these impacts are actively managed during implementation and verified post-implementation.
*   Regular reviews of the Change Log should be conducted by the Project Manager and, if applicable, the Change Control Board (CCB) or `CPO_AXXX_ChangeReviewAgent`.

---
*This Change Log is a controlled ESTRATIX document. It is maintained as per the Change Management Procedures defined in `../03_ProjectMonitoringControlling/Change_Control_Procedures_Template.md` (or the project-specific approved version) and supports the overall project governance framework. All entries must be accurate and kept up-to-date. Refer to individual Change Request Forms for complete details on each change. This log is fundamental for maintaining control and visibility over project evolution.*
