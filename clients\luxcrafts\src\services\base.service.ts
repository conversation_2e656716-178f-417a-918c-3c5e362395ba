export abstract class BaseService {
  protected readonly isDevelopment = import.meta.env.DEV;

  /**
   * Handles API calls with automatic fallback to mock data in development mode
   * @param apiCall - Function that makes the actual API call
   * @param mockDataProvider - Function that returns mock data
   * @param delay - Delay in milliseconds for mock data (default: 500)
   */
  protected async handleApiCall<T>(
    apiCall: () => Promise<T>,
    mockDataProvider: () => T | Promise<T>,
    delay: number = 500
  ): Promise<T> {
    if (this.isDevelopment) {
      // Simulate network delay in development
      await new Promise(resolve => setTimeout(resolve, delay));
      return await mockDataProvider();
    }

    return await apiCall();
  }
}