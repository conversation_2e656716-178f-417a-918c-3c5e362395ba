import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { environment } from '@/config/environment';

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: string;
  permissions: string[];
  organizationId?: string;
}

declare module 'fastify' {
  interface FastifyRequest {
    user?: AuthenticatedUser;
  }
}

export async function authenticateToken(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  try {
    const authHeader = request.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Missing or invalid authorization header'
      });
    }
    
    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    const decoded = jwt.verify(token, environment.jwt.secret) as any;
    
    request.user = {
      id: decoded.id || decoded.sub,
      email: decoded.email,
      role: decoded.role || 'user',
      permissions: decoded.permissions || [],
      organizationId: decoded.organizationId
    };
    
  } catch (error) {
    return reply.status(401).send({
      error: 'Unauthorized',
      message: 'Invalid or expired token'
    });
  }
}

export function requireRole(requiredRole: string) {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    if (!request.user) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }
    
    if (request.user.role !== requiredRole && request.user.role !== 'admin') {
      return reply.status(403).send({
        error: 'Forbidden',
        message: `Required role: ${requiredRole}`
      });
    }
  };
}

export function requirePermission(requiredPermission: string) {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    if (!request.user) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }
    
    if (!request.user.permissions.includes(requiredPermission) && request.user.role !== 'admin') {
      return reply.status(403).send({
        error: 'Forbidden',
        message: `Required permission: ${requiredPermission}`
      });
    }
  };
}

export function requireAnyPermission(requiredPermissions: string[]) {
  return async (request: FastifyRequest, reply: FastifyReply): Promise<void> => {
    if (!request.user) {
      return reply.status(401).send({
        error: 'Unauthorized',
        message: 'Authentication required'
      });
    }
    
    const hasPermission = requiredPermissions.some(permission => 
      request.user!.permissions.includes(permission)
    );
    
    if (!hasPermission && request.user.role !== 'admin') {
      return reply.status(403).send({
        error: 'Forbidden',
        message: `Required one of permissions: ${requiredPermissions.join(', ')}`
      });
    }
  };
}