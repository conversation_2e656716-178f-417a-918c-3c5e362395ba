---
description: "Guides the agentic generation of a new ESTRATIX API from its conceptual definition, creating framework-specific implementations in the `src/frameworks` directory."
---

# Workflow: Generate ESTRATIX API

**Objective:** To agentically generate the complete, runnable source code for a new ESTRATIX API based on its approved conceptual definition. The agent will create a framework-specific implementation, including endpoint logic, data models, tests, and documentation.

**Trigger:** An API definition has been approved and its status in `docs/matrices/api_matrix.md` is set to `Approved for Development`.

**Responsible Command Office (Lead):** CTO

**Key ESTRATIX Components Involved:**

* `docs/api/[API_ID]_[APIName]_Definition.md` (Input: Approved API Definition)
* `src/domain/models/` (Input: Shared Pydantic models)
* `src/frameworks/[framework]/apis/[API_ID]_[APIName]/` (Output: Generated API source code)
* `docs/matrices/api_matrix.md` (Output: Updated status and link to implementation)

## Steps

1. **Select Target API**
   * **Action**: From `docs/matrices/api_matrix.md`, identify an API with `Status: Approved for Development`.
   * **Tool**: `view_file_outline`
   * **Example**: `<!-- view_file_outline('docs/matrices/api_matrix.md') -->`
   * **Output**: `API_ID`, `APIName`, and the full line from the matrix for the target API.

2. **Analyze API Definition**
   * **Action**: Parse the approved API Definition file to extract core requirements.
   * **Tool**: `view_file_outline`
   * **Example**: `<!-- view_file_outline('docs/api/[API_ID]_[APIName]_Definition.md') -->`
   * **Focus**: Endpoints, methods, parameters, request/response payloads, associated data models, and security requirements.

3. **Select Framework & Scaffold Directory**
   * **Action**: Based on the definition or user prompt, select the target framework (e.g., `fastapi`) and create the standardized directory structure.
   * **Tool**: `run_command`
   * **Example**: `<!-- run_command('mkdir -p src/frameworks/fastapi/apis/[API_ID]_[APIName]/routers src/frameworks/fastapi/apis/[API_ID]_[APIName]/tests') -->`

4. **Generate API Implementation Code**
   * **Action**: Generate the framework-specific code. This includes the main application setup (`main.py`), endpoint logic (`routers/`), and any framework-specific configuration.
   * **Guidance**: This step requires a specialized "Builder" agent. The agent must reference shared domain logic and models from `src/domain/` where applicable.
   * **Tool**: `write_to_file`

5. **Generate Tests**
   * **Action**: Generate a corresponding test suite using `pytest` for each endpoint.
   * **Guidance**: Cover success cases, validation errors, and authentication checks.
   * **Tool**: `write_to_file`

6. **Generate Documentation**
   * **Action**: Create a `README.md` for the generated API and a MermaidJS implementation diagram.
   * **Tool**: `write_to_file`
   * **Example**:

     ```markdown
     <!-- write_to_file('src/frameworks/fastapi/apis/[API_ID]_[APIName]/README.md', '# [API_ID] [APIName]\n\n## Overview\nThis directory contains the FastAPI implementation for the [APIName] API.\n\n## Setup\n`pip install -r requirements.txt`\n\n## Running Tests\n`pytest`') -->
     ```

7. **Update API Matrix**
   * **Action**: Update the API's entry in `docs/matrices/api_matrix.md` to `Implemented` and add a link to the source code.
   * **Tool**: `replace_file_content`
   * **Guidance**: Use the full line content gathered in Step 1 as the `TargetContent` to ensure an exact match.
   * **Example**:

     ```markdown
     <!-- replace_file_content(
         'docs/matrices/api_matrix.md',
         ReplacementChunks=[{
             'AllowMultiple': false,
             'TargetContent': '...the full, exact line from the matrix for the API with status Approved for Development...',
             'ReplacementContent': '...the new full line for the API with status Implemented and a link to the src directory...'
         }]
     ) -->
     ```

## Guidance for Use

* This workflow is designed to be executed by a specialized "Builder" agent with expertise in the target framework (e.g., FastAPI).
* **Hexagonal Architecture**: The agent must distinguish between pure business logic and framework-specific implementation. Reusable domain logic and data models should be placed in `src/domain/` and imported by the framework-specific code in `src/frameworks/[framework]/apis/`.
* Ensure the generated code adheres to all ESTRATIX coding standards, including SOLID principles and proper error handling.
