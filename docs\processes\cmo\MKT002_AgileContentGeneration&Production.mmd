graph TD
    A[Start: Brief from MKT001] --> B{Brief Ingestion & Assign};
    B -- Text Task --> C1(TextContentWriterAgent);
    B -- Script Task --> C2(ScriptwriterAgent);
    B -- Basic Visual Task --> C3(BasicVisualCreatorAgent);

    C1 --> D{Draft Text Content};
    C2 --> D_script(Draft Script);
    C3 --> D_visual(Draft Basic Visual);

    D --> E(ContentEditorProofreaderAgent Review);
    D_script --> E;
    D_visual --> E;
    E -- Revisions Needed --> C1;
    E -- Revisions Needed --> C2;
    E -- Revisions Needed --> C3;
    E -- Approved --> F{Optional Stakeholder Review};
    
    F -- Feedback --> C1;
    F -- Feedback --> C2;
    F -- Feedback --> C3;
    F -- Approved --> G(Final QA by ContentEditorProofreaderAgent);

    G --> H(ContentAssetManagerAgent: Store & Version);
    H --> I[End: Content Ready for Distribution/MKT003];

    %% Inputs/Outputs
    X1(MKT001 Content Calendar) --> A;
    X2(MKT001 Brand Guidelines) --> C1;
    X2 --> C2;
    X2 --> C3;
    I --> X3(Distribution Processes / MKT003);
