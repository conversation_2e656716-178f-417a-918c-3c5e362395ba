{"domain": {"ssl": {"email": "<EMAIL>", "provider": "letsencrypt", "enabled": true}, "primary": "luxcrafts.co", "aliases": ["www.luxcrafts.co"]}, "resources": {"cpu": "1000m", "memory": "2Gi", "storage": "10Gi"}, "environment": {"NODE_ENV": "production", "VITE_APP_ENVIRONMENT": "production"}, "security": {"headers": {"X-Frame-Options": "DENY", "X-Content-Type-Options": "nosniff", "Strict-Transport-Security": "max-age=31536000; includeSubDomains", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';", "X-XSS-Protection": "1; mode=block"}}, "version": "1.0", "name": "luxcrafts-production", "backup": {"schedule": "0 2 * * *", "retention": "7d", "enabled": true}, "scaling": {"targetCPU": 70, "maxReplicas": 10, "minReplicas": 2}, "deployment": {"outputDirectory": "dist", "buildCommand": "npm run build", "source": "dist", "type": "static"}, "healthCheck": {"path": "/health.json", "interval": "30s", "timeout": "10s", "retries": 3}}