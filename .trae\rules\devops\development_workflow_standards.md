---
trigger: always_on
---

# ESTRATIX Development Workflow Standards - High Momentum Operations

## 1. Core Development Philosophy

### 1.1. High Impulse & Momentum Principles

**ESTRATIX operates on HIGH MOMENTUM, RESULTS-DRIVEN development cycles:**

- **Velocity First**: Every development action must contribute to measurable progress
- **UV-Powered Execution**: All Python operations use `uv run` for maximum efficiency
- **Continuous Delivery**: Ship working code daily, iterate rapidly
- **Autonomous Operations**: Minimize manual intervention, maximize automation
- **Strategic Focus**: Every task aligns with quarterly objectives

### 1.2. Development Velocity Metrics

```bash
# Daily velocity tracking
uv run python scripts/track_velocity.py --date $(date +%Y-%m-%d)

# Sprint momentum analysis
uv run python scripts/sprint_analysis.py --sprint current

# Productivity dashboard
uv run python scripts/productivity_dashboard.py --realtime
```

## 2. UV-Powered Development Workflow

### 2.1. Project Initialization (High Speed)

```bash
# Rapid project bootstrap
uv init estratix-[component-name]
cd estratix-[component-name]

# Install core dependencies (fast)
uv add fastapi pydantic pydantic-ai crewai
uv add --dev pytest ruff mypy black

# Generate project structure
uv run python -m cookiecutter gh:estratix/project-template

# Initialize git and commit
git init && git add . && git commit -m "feat: bootstrap [component-name]"
```

### 2.2. Daily Development Cycle

```bash
# Morning startup (< 30 seconds)
uv sync --dev                           # Sync dependencies
uv run pytest -m "unit" --maxfail=1    # Quick health check
uv run ruff check src/                  # Code quality check

# Development loop
uv run --dev src/main.py               # Start dev server
uv run pytest --lf                     # Test last failed
uv run mypy src/                       # Type checking

# End of day (< 60 seconds)
uv run pytest --cov=src               # Full test suite
uv run ruff format src/                # Format code
git add . && git commit -m "feat: [description]"
git push origin main
```

### 2.3. Continuous Integration Workflow

```bash
# Pre-commit validation (< 10 seconds)
uv run pytest -m "unit" --maxfail=3
uv run ruff check --fix src/
uv run mypy src/

# CI pipeline execution
uv run pytest --cov=src --cov-fail-under=80
uv run ruff check src/ tests/
uv run mypy src/ tests/
uv run bandit -r src/
```

## 3. High-Performance Development Patterns

### 3.1. Rapid Prototyping

```bash
# Quick prototype creation
uv run python -c "
import sys
sys.path.append('src')
from prototype import QuickTest
QuickTest().run()
"

# Interactive development
uv run ipython --profile=estratix

# Jupyter notebook development
uv run jupyter lab --port=8888
```

### 3.2. Agent Development Acceleration

```bash
# Generate agent scaffold
uv run python scripts/generate_agent.py --name CTO --type command_office

# Test agent immediately
uv run pytest tests/agents/test_cto.py -v

# Deploy agent to development
uv run python scripts/deploy_agent.py --agent CTO --env dev
```

### 3.3. Tool Development Speed

```bash
# Create tool from template
uv run python scripts/create_tool.py --name DocumentProcessor --category processing

# Validate tool interface
uv run python scripts/validate_tool.py --tool DocumentProcessor

# Integration test
uv run pytest tests/tools/test_document_processor.py --integration
```

## 4. Automated Quality Assurance

### 4.1. Real-Time Code Quality

```bash
# Watch mode for continuous quality
uv run watchdog --patterns="*.py" --command="uv run ruff check {watch_src_path}"

# Auto-format on save
uv run black --watch src/

# Continuous type checking
uv run mypy --follow-imports=silent --watch src/
```

### 4.2. Performance Monitoring

```bash
# Profile application performance
uv run python -m cProfile -o profile.stats src/main.py

# Memory usage tracking
uv run python -m memory_profiler src/main.py

# Benchmark critical paths
uv run pytest tests/benchmarks/ --benchmark-only
```

## 5. Strategic Task Execution

### 5.1. Task Prioritization Matrix

```python
# High Priority (Execute Immediately)
HIGH_PRIORITY_TASKS = [
    "uv run pytest tests/critical/",
    "uv run python scripts/deploy_production.py",
    "uv run python scripts/security_scan.py",
]

# Medium Priority (Execute Daily)
MEDIUM_PRIORITY_TASKS = [
    "uv run pytest --cov=src",
    "uv run ruff check src/",
    "uv run mypy src/",
]

# Low Priority (Execute Weekly)
LOW_PRIORITY_TASKS = [
    "uv run python scripts/dependency_audit.py",
    "uv run python scripts/documentation_update.py",
    "uv run python scripts/performance_analysis.py",
]
```

### 5.2. Automated Task Execution

```bash
# Execute high-priority tasks
uv run python scripts/execute_priority_tasks.py --level high

# Daily automation
uv run python scripts/daily_automation.py

# Weekly maintenance
uv run python scripts/weekly_maintenance.py
```

## 6. Results-Driven Metrics

### 6.1. Development Velocity Tracking

```bash
# Lines of code per day
uv run python scripts/metrics/loc_velocity.py

# Features completed per sprint
uv run python scripts/metrics/feature_velocity.py

# Bug resolution time
uv run python scripts/metrics/bug_resolution.py

# Test coverage improvement
uv run python scripts/metrics/coverage_trend.py
```

### 6.2. Quality Metrics Dashboard

```bash
# Generate quality dashboard
uv run python scripts/quality_dashboard.py --output html

# Code complexity analysis
uv run python scripts/complexity_analysis.py

# Technical debt assessment
uv run python scripts/tech_debt_analysis.py
```

## 7. Deployment & Operations

### 7.1. Rapid Deployment Pipeline

```bash
# Build and test (< 2 minutes)
uv run python scripts/build.py --fast
uv run pytest tests/deployment/ --maxfail=1

# Deploy to staging
uv run python scripts/deploy.py --env staging --fast

# Smoke tests
uv run pytest tests/smoke/ --env staging

# Production deployment
uv run python scripts/deploy.py --env production
```

### 7.2. Monitoring & Observability

```bash
# Real-time application monitoring
uv run python scripts/monitor.py --realtime

# Performance metrics collection
uv run python scripts/collect_metrics.py --interval 60

# Alert system
uv run python scripts/alert_system.py --threshold critical
```

## 8. Collaboration & Coordination

### 8.1. Multi-Assistant Coordination

```bash
# Sync with Windsurf Assistant
uv run python scripts/sync_windsurf.py --status

# Update coordination worksheet
uv run python scripts/update_coordination.py --date $(date +%Y-%m-%d)

# Generate status report
uv run python scripts/status_report.py --format markdown
```

### 8.2. Documentation Automation

```bash
# Auto-generate API documentation
uv run python scripts/generate_docs.py --api

# Update architecture diagrams
uv run python scripts/update_diagrams.py --auto

# Sync documentation
uv run python scripts/sync_docs.py --target github
```

## 9. Security & Compliance

### 9.1. Automated Security Scanning

```bash
# Security vulnerability scan
uv run bandit -r src/ --format json

# Dependency security audit
uv audit

# Secrets detection
uv run python scripts/detect_secrets.py --scan src/
```

### 9.2. Compliance Validation

```bash
# Code style compliance
uv run ruff check src/ --config pyproject.toml

# Type safety compliance
uv run mypy src/ --strict

# Test coverage compliance
uv run pytest --cov=src --cov-fail-under=80
```

## 10. Innovation & Experimentation

### 10.1. Rapid Prototyping Environment

```bash
# Create experimental branch
git checkout -b experiment/$(date +%Y%m%d)-[feature-name]

# Set up experiment environment
uv run python scripts/setup_experiment.py --name [feature-name]

# Run experiments
uv run python experiments/[feature-name]/run.py
```

### 10.2. A/B Testing Framework

```bash
# Deploy A/B test
uv run python scripts/deploy_ab_test.py --test [test-name]

# Collect A/B metrics
uv run python scripts/collect_ab_metrics.py --test [test-name]

# Analyze A/B results
uv run python scripts/analyze_ab_results.py --test [test-name]
```

## 11. Emergency Response Procedures

### 11.1. Incident Response

```bash
# Emergency deployment rollback
uv run python scripts/emergency_rollback.py --version [safe-version]

# System health check
uv run python scripts/health_check.py --comprehensive

# Generate incident report
uv run python scripts/incident_report.py --severity [level]
```

### 11.2. Recovery Procedures

```bash
# Database recovery
uv run python scripts/db_recovery.py --backup [timestamp]

# Service restoration
uv run python scripts/restore_services.py --priority critical

# Post-incident analysis
uv run python scripts/post_incident_analysis.py --incident [id]
```

## 12. Continuous Improvement

### 12.1. Performance Optimization

```bash
# Identify bottlenecks
uv run python scripts/identify_bottlenecks.py

# Optimize critical paths
uv run python scripts/optimize_critical_paths.py

# Measure improvement
uv run python scripts/measure_improvement.py --baseline [timestamp]
```

### 12.2. Process Enhancement

```bash
# Analyze workflow efficiency
uv run python scripts/workflow_analysis.py

# Suggest improvements
uv run python scripts/suggest_improvements.py

# Implement optimizations
uv run python scripts/implement_optimizations.py --auto
```

## 13. Success Metrics & KPIs

### 13.1. Development KPIs

- **Deployment Frequency**: Multiple times per day
- **Lead Time**: < 24 hours from commit to production
- **Mean Time to Recovery**: < 1 hour
- **Change Failure Rate**: < 5%
- **Test Coverage**: > 80%
- **Code Quality Score**: > 90%

### 13.2. Velocity Metrics

```bash
# Track velocity metrics
uv run python scripts/track_kpis.py --daily

# Generate velocity report
uv run python scripts/velocity_report.py --period weekly

# Benchmark against targets
uv run python scripts/benchmark_targets.py
```

## 14. Automation Scripts

### 14.1. Daily Automation

```bash
#!/bin/bash
# daily_automation.sh

# Sync dependencies
uv sync --dev

# Run health checks
uv run pytest tests/health/ --maxfail=1

# Update documentation
uv run python scripts/update_docs.py --auto

# Generate status report
uv run python scripts/daily_status.py

# Commit daily progress
git add . && git commit -m "chore: daily automation $(date +%Y-%m-%d)"
```

### 14.2. Weekly Maintenance

```bash
#!/bin/bash
# weekly_maintenance.sh

# Update dependencies
uv lock --upgrade

# Security audit
uv audit

# Performance analysis
uv run python scripts/performance_analysis.py

# Generate weekly report
uv run python scripts/weekly_report.py
```

## 15. Compliance Checklist

### 15.1. Daily Compliance

- [ ] All Python execution uses `uv run`
- [ ] Tests pass with > 80% coverage
- [ ] Code quality checks pass
- [ ] Security scans complete
- [ ] Documentation updated
- [ ] Metrics collected
- [ ] Status reported

### 15.2. Weekly Compliance

- [ ] Dependencies updated
- [ ] Performance benchmarks run
- [ ] Security audit completed
- [ ] Architecture review conducted
- [ ] Process improvements identified
- [ ] Strategic alignment validated

---

**ESTRATIX High-Velocity Development - Powered by UV**
**Results-Driven • High-Momentum • Autonomous Operations**
© 2025 ESTRATIX - Engineering Excellence Framework