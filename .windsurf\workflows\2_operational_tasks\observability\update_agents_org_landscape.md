---
description: "Guides the creation and refinement of the ESTRATIX Agents Organizational Landscape diagram, illustrating the hierarchy and relationships of agents within their respective Command Offices."
---

# Workflow: Update ESTRATIX Agents Organizational Landscape Diagram

This workflow details the process for generating or updating the ESTRATIX Agents Organizational Landscape diagram. The diagram visually represents the structure of agent teams and their reporting lines within various Command Offices.

**Objective:** To produce an up-to-date Mermaid diagram (`.mmd`) in `docs/diagrams/Agents_Organizational_Landscape.mmd` that accurately reflects the agent organizational structure defined in `docs/matrices/agents_matrix.md`, grouped under their respective Command Offices from `docs/matrices/organization_matrix.md`.

**Relies on:**
-   `docs/matrices/organization_matrix.md` (for Command Officer list and hierarchy)
-   `docs/matrices/agents_matrix.md` (for agent details, reporting lines, and office affiliation)
-   (Potentially) Individual Officer Headquarters definitions for more granular detail in future iterations.
-   `docs/diagrams/Command_Officers_Organizational_Chart.mmd` (Related higher-level Command Officer chart)

**Key Output:**
-   An updated `docs/diagrams/Agents_Organizational_Landscape.mmd` file.

---

## Steps:

1.  **Data Ingestion & Preparation:**
    *   **Action (Automated/Agent-Assisted):**
        1.  Read `docs/matrices/organization_matrix.md` into a structured format (e.g., list of dictionaries, DataFrame). Extract Officer ID, Acronym, Officer Name, and Type.
        2.  Read `docs/matrices/agents_matrix.md` into a structured format. Extract Agent ID, Agent Name/Role, Reports To, and Command Office.
    *   **Tooling:** Scripting (Python with CSV/Markdown parsing libraries), data manipulation libraries.

2.  **Diagram Generation Logic (Mermaid Syntax):**
    *   **Action (Automated/Agent-Assisted):** Based on the ingested data, construct the Mermaid graph definition.
    *   **Guidance for Mermaid Structure:**
        *   Start with a `graph TD` (Top-Down) or `graph LR` (Left-Right) definition.
        *   Iterate through `organization_matrix.md` to create top-level subgraphs for each Command Office (e.g., `subgraph "CEO Office (ORG_O001)"`).
            *   Inside each Command Office subgraph, define the Officer's node (e.g., `ORG_O001["CEO (Chief Executive Officer)"]`).
        *   Iterate through `agents_matrix.md`:
            *   For each agent, create a node (e.g., `AGENT_ID_Example["Example Agent Name/Role"]`).
            *   Place the agent's node within the subgraph of their designated "Command Office" (Officer ID).
            *   Establish a link from their "Reports To" (which could be the Officer ID of their Command Office, or another Agent ID within the same office) to the agent's node.
            *   Example:
                ```mermaid
                graph TD
                    subgraph "CEO Office (ORG_O001)"
                        direction LR
                        ORG_O001["CEO (Chief Executive Officer)"]
                        AGENT_CEO_Staff_1["CEO Staff Agent 1 (AGENT_S001)"]
                        ORG_O001 --> AGENT_CEO_Staff_1
                    end

                    subgraph "CPO Office (ORG_O002)"
                        direction LR
                        ORG_O002["CPO (Chief Process Officer)"]
                        AGENT_CPO_Analyst_1["Process Analyst Agent (AGENT_P001)"]
                        ORG_O002 --> AGENT_CPO_Analyst_1
                    end
                ```
        *   Style nodes or links as needed for clarity (e.g., different shapes for officers vs. agents). Use `classDef` for consistent styling.
    *   **Tooling:** Scripting to generate Mermaid text.

3.  **Write/Update Diagram File:**
    *   **Action (Automated/Agent-Assisted):**
        1.  Ensure the directory `docs/diagrams/` exists.
        2.  Write the generated Mermaid syntax to `docs/diagrams/Agents_Organizational_Landscape.mmd`. Overwrite the existing file if it exists.
    *   **Tooling:** File system operations.

4.  **Review and Validation (Manual/Visual):**
    *   **Action (User):**
        1.  Open `Agents_Organizational_Landscape.mmd` using a Mermaid-compatible viewer (e.g., VS Code extension, online editor).
        2.  Visually inspect the diagram for accuracy, completeness, and clarity.
        3.  Compare against the source matrices to ensure all key relationships are represented.
    *   **Input:** Visual feedback.
    *   **Output:** Confirmation of diagram correctness or identification of issues to refine in Step 2.

5.  **Iteration (If Necessary):**
    *   **Action:** If the diagram is not satisfactory, revisit Step 2 (Diagram Generation Logic) to adjust the Mermaid syntax generation. Repeat Step 3 and 4.

6.  **Finalization & Communication:**
    *   **Action:** Once the diagram is approved, announce its update to relevant stakeholders.
    *   **Consideration:** This workflow should be triggered whenever significant changes occur in `organization_matrix.md` or `agents_matrix.md`.

---
**Next Steps:**
-   Implement the initial version of the script/tooling for Step 1, 2, and 3.
-   Execute this workflow to generate the first version of the `Agents_Organizational_Landscape.mmd`.