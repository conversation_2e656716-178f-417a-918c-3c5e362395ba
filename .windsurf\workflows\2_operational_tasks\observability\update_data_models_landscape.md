---
Description: Scans implemented Pydantic data models in `src/domain/models/`, cross-references with `data_model_matrix.md`, and updates the `docs/diagrams/data_models_landscape.mmd` to reflect their structure and relationships.
---

# Workflow: Update Data Models Landscape Diagram

**Objective:** To provide an accurate and up-to-date visual representation of the ESTRATIX domain data models, reflecting their actual implementation as Pydantic models in `src/domain/models/` and their conceptual definitions in `docs/matrices/data_model_matrix.md`.

**Trigger:** 
*   Manual execution.
*   After significant changes to Pydantic models in `src/domain/models/`.
*   After updates to `docs/matrices/data_model_matrix.md`.
*   As part of a CI/CD pipeline or regular documentation update process.

**Responsible Command Office (Lead):** CIO (for information architecture) or CTO (for technical implementation standards).

**Key ESTRATIX Components Involved:**

*   `src/domain/models/` (Input: Directory containing Pydantic model Python files)
*   `docs/matrices/data_model_matrix.md` (Input: Conceptual data model definitions)
*   `docs/diagrams/data_models_landscape.mmd` (Output: Mermaid diagram)

## Pre-requisites:

*   Pydantic models in `src/domain/models/` follow consistent coding standards for parsing (e.g., clear class definitions, type hints).
*   `data_model_matrix.md` is reasonably up-to-date with conceptual model information.

## Steps:

1.  **Read Data Model Matrix:**
    *   **Action:** Parse `docs/matrices/data_model_matrix.md` to extract conceptual information about each data model (e.g., Model ID, Name, Description, Responsible Office, Related Models as defined conceptually).
    *   **Tooling:** Python script with Markdown parsing capabilities.
    *   **Output:** Structured data (e.g., list of dictionaries) representing the conceptual models.

2.  **Scan and Parse Implemented Pydantic Models:**
    *   **Action:** Traverse the `src/domain/models/` directory to find all Python files (`.py`).
    *   **Action:** For each Python file, parse its content to identify Pydantic model definitions (classes inheriting from `pydantic.BaseModel`).
    *   **Action:** For each identified Pydantic model, extract:
        *   Class name (Model Name).
        *   Fields (attributes): name, type (including handling of `Optional`, `List`, and custom Pydantic types or other models).
        *   Inheritance relationships (base classes).
        *   Docstrings for the model and its fields.
    *   **Tooling:** Python script using Abstract Syntax Tree (AST) parsing (`ast` module) or static analysis tools (e.g., `jedi`, `parso`). Regular expressions might be too fragile.
    *   **Output:** Structured data representing the implemented Pydantic models, their fields, types, and relationships.

3.  **Correlate Implemented Models with Matrix Definitions:**
    *   **Action:** Match the parsed Pydantic models (from Step 2) with the conceptual models from the matrix (Step 1), likely using the model name as a key.
    *   **Action:** For each model, combine the implementation details (fields, actual types) with conceptual details (description, responsible office from the matrix).
    *   **Output:** A consolidated list of data models with both conceptual and implementation details.

4.  **Generate Mermaid Diagram Syntax (Class Diagram Style):**
    *   **Action:** Construct the Mermaid `classDiagram` syntax.
    *   **Logic:**
        *   For each Pydantic model, define a class in Mermaid.
        *   List its fields with their types (e.g., `+fieldName: fieldType`).
        *   Represent relationships:
            *   **Inheritance:** Use `ParentModel <|-- ChildModel`.
            *   **Composition/Aggregation (Nested Models/Lists of Models):** Use association lines like `ModelA --> "1..*" ModelB` or `ModelA o-- ModelB` if a field in `ModelA` is of type `ModelB` or `List[ModelB]`. The exact notation might need to be chosen for clarity.
            *   Consider using notes or labels from the `data_model_matrix.md` (e.g., description, responsible office) as annotations if Mermaid syntax allows or by adding them to the class block.
        *   Group related models or models by sub-domain/module using Mermaid's `namespace` or `package` features if applicable and if the directory structure in `src/domain/models/` suggests such groupings.
    *   **Example Snippet (Conceptual):
        ```mermaid
        classDiagram
            namespace EstratixDomainModels {
                class BaseModel {
                    <<Abstract>>
                }

                class User {
                    +user_id: str
                    +username: str
                    +email: EmailStr
                    +profile: UserProfile
                    +roles: List~Role~
                }
                BaseModel <|-- User

                class UserProfile {
                    +full_name: Optional~str~
                    +bio: Optional~str~
                }
                User o-- UserProfile : has a

                class Role {
                    +role_id: int
                    +role_name: str
                }
                User --> "*" Role : has many
            }
            %% Add notes from matrix if possible
            %% note for User "Manages user authentication and basic info. Office: CIO"
        ```
    *   **Output:** A string containing the complete Mermaid syntax for the class diagram.

5.  **Write to Data Models Landscape Diagram File:**
    *   **Action:** Overwrite the content of `docs/diagrams/data_models_landscape.mmd` with the newly generated Mermaid syntax.
    *   **Tooling:** File system operations.
    *   **Input:** Generated Mermaid syntax string.
    *   **Output:** Updated `docs/diagrams/data_models_landscape.mmd`.

6.  **Commit Changes (If in a Git Repository):**
    *   **Action (Optional):** If this workflow is part of an automated CI/CD pipeline or script, commit the changes to `data_models_landscape.mmd`.
    *   **Tooling:** Git commands.
    *   **Output:** Committed changes to version control.

## Considerations & Enhancements:

*   **Parsing Complexity:** Accurately parsing Python code, especially complex type hints and relationships, can be challenging. The chosen parsing method needs to be robust.
*   **External Types:** Decide how to represent types imported from other libraries or standard Python types.
*   **Diagram Clarity:** For a large number of models, the diagram can become cluttered. Consider:
    *   Generating multiple diagrams for different sub-domains if `src/domain/models/` has a clear modular structure.
    *   Allowing options to show/hide field details or focus on specific relationships.
*   **Matrix as Source of Truth for Relationships:** The matrix might define conceptual relationships that are not yet (or cannot be directly) implemented as Pydantic field types. The diagram generation logic might need to incorporate these conceptual links as well, perhaps styled differently.
*   **Error Handling:** Implement robust error handling for parsing Python files and the matrix file.
*   **Styling:** Use Mermaid `classDef` for styling nodes based on categories, status (if available in matrix), or responsible office.
