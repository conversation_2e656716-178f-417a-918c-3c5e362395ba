import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PhotoIcon,
  VideoCameraIcon,
  SpeakerWaveIcon,
  DocumentTextIcon,
  ShareIcon,
  CalendarIcon,
  ChartBarIcon,
  EyeIcon,
  HeartIcon,
  ChatBubbleLeftRightIcon,
  ArrowPathIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  MicrophoneIcon,
  SparklesIcon,
  PaintBrushIcon,
  CubeIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

const contentTypes = [
  {
    id: 'image',
    name: 'AI Images',
    icon: PhotoIcon,
    description: 'Generate stunning property photos and marketing visuals',
    color: 'blue',
    count: 247,
  },
  {
    id: 'video',
    name: 'Video Content',
    icon: VideoCameraIcon,
    description: 'Create property tours and promotional videos',
    color: 'purple',
    count: 89,
  },
  {
    id: 'audio',
    name: 'Audio Content',
    icon: SpeakerWaveIcon,
    description: 'Generate voiceovers and podcast content',
    color: 'green',
    count: 156,
  },
  {
    id: 'text',
    name: 'Written Content',
    icon: DocumentTextIcon,
    description: 'Create property descriptions and marketing copy',
    color: 'yellow',
    count: 342,
  },
  {
    id: '3d',
    name: '3D Models',
    icon: CubeIcon,
    description: 'Generate 3D property models and virtual staging',
    color: 'red',
    count: 67,
  },
  {
    id: 'social',
    name: 'Social Media',
    icon: ShareIcon,
    description: 'Automated social media posts and campaigns',
    color: 'indigo',
    count: 523,
  },
];

const recentContent = [
  {
    id: 1,
    title: 'Luxury Penthouse Virtual Tour',
    type: 'video',
    property: 'Beacon Hill Penthouse',
    status: 'Published',
    views: 12500,
    engagement: 8.5,
    created: '2024-01-15',
    thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20virtual%20tour%20thumbnail&image_size=landscape_4_3',
  },
  {
    id: 2,
    title: 'Property Investment Guide',
    type: 'text',
    property: 'General',
    status: 'Draft',
    views: 0,
    engagement: 0,
    created: '2024-01-16',
    thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=property%20investment%20guide%20document&image_size=landscape_4_3',
  },
  {
    id: 3,
    title: 'Boston Real Estate Market Update',
    type: 'image',
    property: 'Market Analysis',
    status: 'Published',
    views: 8900,
    engagement: 12.3,
    created: '2024-01-14',
    thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=boston%20real%20estate%20market%20infographic&image_size=landscape_4_3',
  },
];

const contentCalendar = [
  {
    date: '2024-01-18',
    posts: [
      { platform: 'Instagram', type: 'image', title: 'Property Spotlight: Back Bay Condo' },
      { platform: 'LinkedIn', type: 'text', title: 'Market Insights: Q1 2024 Trends' },
    ],
  },
  {
    date: '2024-01-19',
    posts: [
      { platform: 'YouTube', type: 'video', title: 'Virtual Tour: Cambridge Waterfront' },
      { platform: 'Twitter', type: 'text', title: 'Investment Tip of the Day' },
    ],
  },
  {
    date: '2024-01-20',
    posts: [
      { platform: 'Facebook', type: 'image', title: 'Client Success Story' },
      { platform: 'Instagram', type: 'video', title: 'Behind the Scenes: Property Staging' },
    ],
  },
];

const performanceData = [
  { month: 'Jan', views: 45000, engagement: 8.2, shares: 1200 },
  { month: 'Feb', views: 52000, engagement: 9.1, shares: 1450 },
  { month: 'Mar', views: 48000, engagement: 7.8, shares: 1100 },
  { month: 'Apr', views: 61000, engagement: 10.5, shares: 1800 },
  { month: 'May', views: 58000, engagement: 9.7, shares: 1650 },
  { month: 'Jun', views: 67000, engagement: 11.2, shares: 2100 },
];

export default function ContentStudio() {
  const [activeTab, setActiveTab] = useState('create');
  const [selectedContentType, setSelectedContentType] = useState('image');
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  const tabs = [
    { id: 'create', name: 'Create Content' },
    { id: 'library', name: 'Content Library' },
    { id: 'calendar', name: 'Content Calendar' },
    { id: 'analytics', name: 'Performance Analytics' },
  ];

  const handleGenerate = () => {
    setIsGenerating(true);
    setGenerationProgress(0);
    
    const interval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGenerating(false);
          return 100;
        }
        return prev + 10;
      });
    }, 500);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            AI Content Studio
          </h1>
          <p className="text-xl text-gray-600">
            Create compelling marketing content with advanced AI tools
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Create Content Tab */}
        {activeTab === 'create' && (
          <div className="space-y-8">
            {/* Content Type Selection */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Choose Content Type</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {contentTypes.map((type) => (
                  <motion.button
                    key={type.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedContentType(type.id)}
                    className={`p-6 rounded-xl border-2 transition-all duration-200 text-left ${
                      selectedContentType === type.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center space-x-4 mb-3">
                      <div className={`p-3 rounded-lg ${
                        type.color === 'blue' ? 'bg-blue-100' :
                        type.color === 'purple' ? 'bg-purple-100' :
                        type.color === 'green' ? 'bg-green-100' :
                        type.color === 'yellow' ? 'bg-yellow-100' :
                        type.color === 'red' ? 'bg-red-100' :
                        'bg-indigo-100'
                      }`}>
                        <type.icon className={`w-6 h-6 ${
                          type.color === 'blue' ? 'text-blue-600' :
                          type.color === 'purple' ? 'text-purple-600' :
                          type.color === 'green' ? 'text-green-600' :
                          type.color === 'yellow' ? 'text-yellow-600' :
                          type.color === 'red' ? 'text-red-600' :
                          'text-indigo-600'
                        }`} />
                      </div>
                      <div>
                        <h4 className="font-bold text-gray-900">{type.name}</h4>
                        <p className="text-sm text-gray-500">{type.count} created</p>
                      </div>
                    </div>
                    <p className="text-sm text-gray-600">{type.description}</p>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Content Generation Form */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                Generate {contentTypes.find(t => t.id === selectedContentType)?.name}
              </h3>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Input Form */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Property/Topic
                    </label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option>Beacon Hill Penthouse</option>
                      <option>Back Bay Condo</option>
                      <option>Cambridge Waterfront</option>
                      <option>Newton Townhouse</option>
                      <option>General Market Content</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Content Description
                    </label>
                    <textarea
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Describe what you want to create..."
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Style/Tone
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>Professional</option>
                        <option>Luxury</option>
                        <option>Modern</option>
                        <option>Elegant</option>
                        <option>Minimalist</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Target Audience
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>Investors</option>
                        <option>Homebuyers</option>
                        <option>Property Owners</option>
                        <option>Real Estate Agents</option>
                        <option>General Public</option>
                      </select>
                    </div>
                  </div>

                  {selectedContentType === 'image' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Image Dimensions
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                          Square
                        </button>
                        <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                          Landscape
                        </button>
                        <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 text-sm">
                          Portrait
                        </button>
                      </div>
                    </div>
                  )}

                  {selectedContentType === 'video' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Video Duration
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option>30 seconds</option>
                        <option>1 minute</option>
                        <option>2 minutes</option>
                        <option>5 minutes</option>
                      </select>
                    </div>
                  )}

                  <div className="flex space-x-4">
                    <button
                      onClick={handleGenerate}
                      disabled={isGenerating}
                      className="flex-1 px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
                    >
                      {isGenerating ? (
                        <>
                          <ArrowPathIcon className="w-5 h-5 animate-spin" />
                          <span>Generating...</span>
                        </>
                      ) : (
                        <>
                          <SparklesIcon className="w-5 h-5" />
                          <span>Generate Content</span>
                        </>
                      )}
                    </button>
                    <button className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                      Save Draft
                    </button>
                  </div>

                  {isGenerating && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Generation Progress</span>
                        <span>{generationProgress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-500"
                          style={{ width: `${generationProgress}%` }}
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Preview Area */}
                <div className="bg-gray-50 rounded-xl p-6">
                  <h4 className="font-semibold text-gray-900 mb-4">Preview</h4>
                  <div className="aspect-video bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                    {isGenerating ? (
                      <div className="text-center">
                        <ArrowPathIcon className="w-12 h-12 text-blue-500 animate-spin mx-auto mb-2" />
                        <p className="text-gray-600">Generating content...</p>
                      </div>
                    ) : (
                      <div className="text-center">
                        <PaintBrushIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-600">Content preview will appear here</p>
                      </div>
                    )}
                  </div>
                  
                  <div className="mt-4 space-y-3">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Estimated Generation Time:</span>
                      <span className="font-medium">2-5 minutes</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Quality Level:</span>
                      <span className="font-medium text-green-600">High Definition</span>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">AI Model:</span>
                      <span className="font-medium">SDXL Turbo</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Library Tab */}
        {activeTab === 'library' && (
          <div className="space-y-8">
            {/* Search and Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search content library..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex space-x-4">
                  <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>All Types</option>
                    <option>Images</option>
                    <option>Videos</option>
                    <option>Audio</option>
                    <option>Text</option>
                  </select>
                  <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>All Status</option>
                    <option>Published</option>
                    <option>Draft</option>
                    <option>Scheduled</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Content Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recentContent.map((content) => (
                <motion.div
                  key={content.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
                >
                  <div className="relative">
                    <img
                      src={content.thumbnail}
                      alt={content.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        content.type === 'video' ? 'bg-purple-100 text-purple-800' :
                        content.type === 'image' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {content.type}
                      </span>
                    </div>
                    <div className="absolute top-4 right-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        content.status === 'Published' ? 'bg-green-100 text-green-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {content.status}
                      </span>
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h4 className="font-bold text-gray-900 mb-2">{content.title}</h4>
                    <p className="text-sm text-gray-600 mb-4">{content.property}</p>
                    
                    {content.status === 'Published' && (
                      <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <EyeIcon className="w-4 h-4 text-gray-400" />
                          <span>{content.views.toLocaleString()} views</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <ChartBarIcon className="w-4 h-4 text-gray-400" />
                          <span>{content.engagement}% engagement</span>
                        </div>
                      </div>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">{content.created}</span>
                      <div className="flex space-x-2">
                        <button className="p-2 text-gray-400 hover:text-blue-500 transition-colors">
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        <button className="p-2 text-gray-400 hover:text-green-500 transition-colors">
                          <ShareIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Content Calendar Tab */}
        {activeTab === 'calendar' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Content Calendar</h3>
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  Schedule New Post
                </button>
              </div>
              
              <div className="space-y-6">
                {contentCalendar.map((day, index) => (
                  <div key={day.date} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-4">
                      <CalendarIcon className="w-5 h-5 text-blue-500" />
                      <h4 className="font-semibold text-gray-900">{day.date}</h4>
                      <span className="text-sm text-gray-500">({day.posts.length} posts scheduled)</span>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {day.posts.map((post, postIndex) => (
                        <div key={postIndex} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                            post.platform === 'Instagram' ? 'bg-pink-100' :
                            post.platform === 'LinkedIn' ? 'bg-blue-100' :
                            post.platform === 'YouTube' ? 'bg-red-100' :
                            post.platform === 'Twitter' ? 'bg-sky-100' :
                            'bg-indigo-100'
                          }`}>
                            <ShareIcon className={`w-4 h-4 ${
                              post.platform === 'Instagram' ? 'text-pink-600' :
                              post.platform === 'LinkedIn' ? 'text-blue-600' :
                              post.platform === 'YouTube' ? 'text-red-600' :
                              post.platform === 'Twitter' ? 'text-sky-600' :
                              'text-indigo-600'
                            }`} />
                          </div>
                          <div className="flex-1">
                            <p className="font-medium text-gray-900">{post.title}</p>
                            <p className="text-sm text-gray-600">{post.platform} • {post.type}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Performance Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            {/* Performance Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Views</p>
                    <p className="text-3xl font-bold text-gray-900">2.1M</p>
                    <p className="text-sm text-green-600">+15.3% this month</p>
                  </div>
                  <EyeIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Engagement Rate</p>
                    <p className="text-3xl font-bold text-gray-900">9.7%</p>
                    <p className="text-sm text-green-600">+2.1% this month</p>
                  </div>
                  <HeartIcon className="w-8 h-8 text-red-500" />
                </div>
              </div>
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Content Pieces</p>
                    <p className="text-3xl font-bold text-gray-900">1,424</p>
                    <p className="text-sm text-green-600">+89 this month</p>
                  </div>
                  <DocumentTextIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* Performance Chart */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Content Performance Trends</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={performanceData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Line yAxisId="left" type="monotone" dataKey="views" stroke="#3b82f6" strokeWidth={3} />
                    <Line yAxisId="right" type="monotone" dataKey="engagement" stroke="#10b981" strokeWidth={3} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Content Type Performance */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Performance by Content Type</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={[
                    { type: 'Images', views: 45000, engagement: 8.2 },
                    { type: 'Videos', views: 67000, engagement: 12.5 },
                    { type: 'Audio', views: 23000, engagement: 6.8 },
                    { type: 'Text', views: 34000, engagement: 9.1 },
                  ]}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="type" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="views" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}