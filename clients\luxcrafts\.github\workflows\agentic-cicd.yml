name: Agentic CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      deployment_target:
        description: 'Deployment target'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      agent_framework:
        description: 'Agentic framework to use'
        required: true
        default: 'autogen'
        type: choice
        options:
        - autogen
        - crewai
        - langchain
        - llamaindex
      parallel_execution:
        description: 'Enable parallel agent execution'
        required: false
        default: true
        type: boolean

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}
  DEPLOYMENT_TIMEOUT: 600
  AGENT_TIMEOUT: 300

jobs:
  # Code Quality and Security Analysis
  code-analysis:
    runs-on: ubuntu-latest
    outputs:
      security-score: ${{ steps.security-scan.outputs.score }}
      quality-gate: ${{ steps.quality-check.outputs.passed }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        npm install -g @typescript-eslint/eslint-plugin eslint-plugin-security

    - name: TypeScript compilation check
      run: npx tsc --noEmit

    - name: ESLint analysis
      run: |
        npx eslint . --ext .ts,.tsx,.js,.jsx --format json --output-file eslint-report.json || true
        npx eslint . --ext .ts,.tsx,.js,.jsx

    - name: Security vulnerability scan
      id: security-scan
      run: |
        npm audit --audit-level moderate --json > audit-report.json || true
        CRITICAL=$(cat audit-report.json | jq '.metadata.vulnerabilities.critical // 0')
        HIGH=$(cat audit-report.json | jq '.metadata.vulnerabilities.high // 0')
        SCORE=$((100 - CRITICAL * 20 - HIGH * 10))
        echo "score=$SCORE" >> $GITHUB_OUTPUT
        echo "Security Score: $SCORE"

    - name: Code quality gate
      id: quality-check
      run: |
        SECURITY_SCORE=${{ steps.security-scan.outputs.score }}
        if [ $SECURITY_SCORE -ge 80 ]; then
          echo "passed=true" >> $GITHUB_OUTPUT
          echo "✅ Quality gate passed"
        else
          echo "passed=false" >> $GITHUB_OUTPUT
          echo "❌ Quality gate failed"
        fi

    - name: Upload analysis artifacts
      uses: actions/upload-artifact@v4
      with:
        name: code-analysis-reports
        path: |
          eslint-report.json
          audit-report.json

  # Agentic Framework Testing
  agentic-testing:
    runs-on: ubuntu-latest
    needs: code-analysis
    if: needs.code-analysis.outputs.quality-gate == 'true'
    strategy:
      matrix:
        framework: [autogen, crewai, langchain]
        test-type: [unit, integration, e2e]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python for agentic frameworks
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install dependencies
      run: |
        npm ci
        pip install -r requirements-agents.txt || echo "No agent requirements found"

    - name: Setup agentic framework - ${{ matrix.framework }}
      run: |
        case "${{ matrix.framework }}" in
          "autogen")
            pip install pyautogen openai
            ;;
          "crewai")
            pip install crewai langchain-openai
            ;;
          "langchain")
            pip install langchain langchain-openai langchain-community
            ;;
        esac

    - name: Run ${{ matrix.test-type }} tests with ${{ matrix.framework }}
      env:
        OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
        ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        AGENT_FRAMEWORK: ${{ matrix.framework }}
        TEST_TYPE: ${{ matrix.test-type }}
      run: |
        case "${{ matrix.test-type }}" in
          "unit")
            npm run test:unit
            python -m pytest tests/agents/unit/ -v
            ;;
          "integration")
            npm run test:integration
            python -m pytest tests/agents/integration/ -v
            ;;
          "e2e")
            npm run test:e2e
            python -m pytest tests/agents/e2e/ -v
            ;;
        esac

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.framework }}-${{ matrix.test-type }}
        path: |
          coverage/
          test-results/
          pytest-report.xml

  # Build and Container Registry
  build-and-push:
    runs-on: ubuntu-latest
    needs: [code-analysis, agentic-testing]
    if: needs.code-analysis.outputs.quality-gate == 'true'
    outputs:
      image-digest: ${{ steps.build.outputs.digest }}
      image-tag: ${{ steps.meta.outputs.tags }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Build and push Docker image
      id: build
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          VITE_WALLETCONNECT_PROJECT_ID=${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY=${{ secrets.VITE_ALCHEMY_API_KEY }}
          VITE_INFURA_API_KEY=${{ secrets.VITE_INFURA_API_KEY }}

    - name: Generate SBOM
      uses: anchore/sbom-action@v0
      with:
        image: ${{ steps.meta.outputs.tags }}
        format: spdx-json
        output-file: sbom.spdx.json

    - name: Upload SBOM
      uses: actions/upload-artifact@v4
      with:
        name: sbom
        path: sbom.spdx.json

  # Agentic Deployment Orchestration
  agentic-deployment:
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'
    environment: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Python for deployment agents
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'

    - name: Install deployment agent dependencies
      run: |
        pip install -r requirements-deployment.txt || echo "Installing default deployment tools"
        pip install fabric paramiko ansible docker-compose

    - name: Configure deployment environment
      env:
        DEPLOYMENT_TARGET: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
        VPS_HOST: ${{ secrets.VPS_HOST }}
        VPS_USER: ${{ secrets.VPS_USER }}
        VPS_SSH_KEY: ${{ secrets.VPS_SSH_KEY }}
        DOCKER_IMAGE: ${{ needs.build-and-push.outputs.image-tag }}
      run: |
        echo "Configuring deployment for $DEPLOYMENT_TARGET"
        mkdir -p ~/.ssh
        echo "$VPS_SSH_KEY" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H $VPS_HOST >> ~/.ssh/known_hosts

    - name: Deploy to VPS from Registry
      env:
        VPS_HOST: ${{ secrets.VPS_HOST }}
        VPS_USER: ${{ secrets.VPS_USER }}
        DOMAIN_NAME: ${{ secrets.DOMAIN_NAME }}
        IMAGE_TAG: ${{ needs.build-and-push.outputs.image-digest }}
        REGISTRY_USER: ${{ github.actor }}
        REGISTRY_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        # Define the remote directory
        REMOTE_DEPLOY_DIR="~/luxcrafts-deployment"

        # Create the deployment directory on the VPS
        ssh $VPS_USER@$VPS_HOST "mkdir -p $REMOTE_DEPLOY_DIR"

        # Copy the necessary deployment files to the VPS
        scp clients/luxcrafts/scripts/deploy_from_registry.sh $VPS_USER@$VPS_HOST:$REMOTE_DEPLOY_DIR
        scp clients/luxcrafts/docker-compose.prod.yml $VPS_USER@$VPS_HOST:$REMOTE_DEPLOY_DIR

        # Execute the deployment script on the VPS
        ssh $VPS_USER@$VPS_HOST "cd $REMOTE_DEPLOY_DIR && \
          export DOCKER_IMAGE_TAG='${IMAGE_TAG}' && \
          export DOMAIN_NAME='${DOMAIN_NAME}' && \
          export REGISTRY_USER='${REGISTRY_USER}' && \
          export REGISTRY_TOKEN='${REGISTRY_TOKEN}' && \
          bash deploy_from_registry.sh"

  # Post-Deployment Validation
  post-deployment-validation:
    runs-on: ubuntu-latest
    needs: agentic-deployment
    if: always() && needs.agentic-deployment.result == 'success'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup validation tools
      run: |
        npm install -g lighthouse artillery newman
        pip install requests pytest-html

    - name: Health check validation
      env:
        APP_URL: ${{ github.ref == 'refs/heads/main' && secrets.PROD_APP_URL || secrets.STAGING_APP_URL }}
      run: |
        echo "Validating deployment at $APP_URL"
        curl -f $APP_URL/health || exit 1
        curl -f $APP_URL/api/health || exit 1

    - name: Performance testing with Artillery
      env:
        APP_URL: ${{ github.ref == 'refs/heads/main' && secrets.PROD_APP_URL || secrets.STAGING_APP_URL }}
      run: |
        printf '%s\n' \
          'config:' \
          '  target: "${{ env.APP_URL }}"' \
          '  phases:' \
          '    - duration: 60' \
          '      arrivalRate: 10' \
          '    - duration: 120' \
          '      arrivalRate: 20' \
          'scenarios:' \
          '  - name: "Homepage load test"' \
          '    flow:' \
          '      - get:' \
          '          url: "/"' \
          '      - get:' \
          '          url: "/api/health"' > artillery-config.yml
        artillery run artillery-config.yml --output artillery-report.json

    - name: Lighthouse performance audit
      env:
        APP_URL: ${{ github.ref == 'refs/heads/main' && secrets.PROD_APP_URL || secrets.STAGING_APP_URL }}
      run: |
        lighthouse ${{ env.APP_URL }} \
          --chrome-flags="--headless --no-sandbox" \
          --output=json \
          --output-path=lighthouse-report.json \
          --preset=desktop

    - name: Security scan with OWASP ZAP
      env:
        APP_URL: ${{ github.ref == 'refs/heads/main' && secrets.PROD_APP_URL || secrets.STAGING_APP_URL }}
      run: |
        docker run -v $(pwd):/zap/wrk/:rw \
          -t owasp/zap2docker-stable zap-baseline.py \
          -t ${{ env.APP_URL }} \
          -J zap-report.json || true

    - name: Upload validation reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: validation-reports
        path: |
          artillery-report.json
          lighthouse-report.json
          zap-report.json

  # Notification and Reporting
  notification:
    runs-on: ubuntu-latest
    needs: [code-analysis, agentic-testing, build-and-push, agentic-deployment, post-deployment-validation]
    if: always()
    steps:
    - name: Prepare notification data
      id: notification-data
      run: |
        DEPLOYMENT_STATUS="${{ needs.agentic-deployment.result }}"
        VALIDATION_STATUS="${{ needs.post-deployment-validation.result }}"
        SECURITY_SCORE="${{ needs.code-analysis.outputs.security-score }}"

        if [ "$DEPLOYMENT_STATUS" = "success" ] && [ "$VALIDATION_STATUS" = "success" ]; then
          STATUS=" SUCCESS"
          COLOR="good"
        elif [ "$DEPLOYMENT_STATUS" = "failure" ] || [ "$VALIDATION_STATUS" = "failure" ]; then
          STATUS=" FAILED"
          COLOR="danger"
        else
          STATUS=" PARTIAL"
          COLOR="warning"
        fi

        echo "status=$STATUS" >> $GITHUB_OUTPUT
        echo "color=$COLOR" >> $GITHUB_OUTPUT
        echo "security_score=$SECURITY_SCORE" >> $GITHUB_OUTPUT

    - name: Slack notification
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            "text": "Luxcrafts Deployment ${{ steps.notification-data.outputs.status }}",
            "attachments": [{
              "color": "${{ steps.notification-data.outputs.color }}",
              "fields": [
                {
                  "title": "Repository",
                  "value": "${{ github.repository }}",
                  "short": true
                },
                {
                  "title": "Branch",
                  "value": "${{ github.ref_name }}",
                  "short": true
                },
                {
                  "title": "Commit",
                  "value": "${{ github.sha }}",
                  "short": true
                },
                {
                  "title": "Security Score",
                  "value": "${{ steps.notification-data.outputs.security_score }}/100",
                  "short": true
                },
                {
                  "title": "Deployment",
                  "value": "${{ needs.agentic-deployment.result }}",
                  "short": true
                },
                {
                  "title": "Validation",
                  "value": "${{ needs.post-deployment-validation.result }}",
                  "short": true
                }
              ]
            }]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Discord notification
      if: always()
      uses: Ilshidur/action-discord@master
      with:
        args: |
          **Luxcrafts Agentic Deployment** ${{ steps.notification-data.outputs.status }}
          
          **Repository:** ${{ github.repository }}
          **Branch:** ${{ github.ref_name }}
          **Commit:** `${{ github.sha }}`
          **Security Score:** ${{ steps.notification-data.outputs.security_score }}/100
          **Deployment:** ${{ needs.agentic-deployment.result }}
          **Validation:** ${{ needs.post-deployment-validation.result }}
          
          [View Workflow](${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }})
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}

  # Cleanup and Maintenance
  cleanup:
    runs-on: ubuntu-latest
    needs: [notification]
    if: always()
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Cleanup old artifacts
      run: |
        echo "Cleaning up old artifacts and temporary resources"

    - name: Update deployment metrics
      env:
        PROMETHEUS_PUSHGATEWAY: ${{ secrets.PROMETHEUS_PUSHGATEWAY }}
        GITHUB_REF_NAME: ${{ github.ref_name }}
        DEPLOYMENT_STATUS: ${{ needs.agentic-deployment.result }}
      run: |
        chmod +x clients/luxcrafts/scripts/push_metrics.sh
        clients/luxcrafts/scripts/push_metrics.sh