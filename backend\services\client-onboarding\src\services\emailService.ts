import nodemailer from 'nodemailer';
import { logger } from '@/utils/logger';
import { environment } from '@/config/environment';
import { Client, RFP, OnboardingFlow } from '@/types';

export interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export interface SendEmailRequest {
  to: string | string[];
  subject: string;
  html?: string;
  text?: string;
  template?: string;
  variables?: Record<string, any>;
  attachments?: Array<{
    filename: string;
    content: Buffer;
    contentType: string;
  }>;
}

export class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private templates: Map<string, EmailTemplate> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Email Service...');
      
      // Initialize email transporter
      await this.initializeTransporter();
      
      // Load email templates
      await this.loadTemplates();
      
      this.isInitialized = true;
      logger.info('Email Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Email Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Email Service...');
    if (this.transporter) {
      this.transporter.close();
      this.transporter = null;
    }
    this.templates.clear();
    this.isInitialized = false;
  }

  async sendEmail(request: SendEmailRequest): Promise<void> {
    try {
      if (!this.transporter) {
        throw new Error('Email transporter not initialized');
      }

      let { subject, html, text } = request;

      // Use template if specified
      if (request.template) {
        const template = this.templates.get(request.template);
        if (!template) {
          throw new Error(`Email template '${request.template}' not found`);
        }

        subject = this.replaceVariables(template.subject, request.variables || {});
        html = this.replaceVariables(template.html, request.variables || {});
        text = this.replaceVariables(template.text, request.variables || {});
      }

      const mailOptions = {
        from: environment.email.from,
        to: Array.isArray(request.to) ? request.to.join(', ') : request.to,
        subject,
        html,
        text,
        attachments: request.attachments
      };

      const result = await this.transporter.sendMail(mailOptions);

      logger.info('Email sent successfully', {
        to: request.to,
        subject,
        messageId: result.messageId,
        template: request.template
      });
    } catch (error) {
      logger.error('Failed to send email', { error, request });
      throw error;
    }
  }

  async sendWelcomeEmail(client: Client): Promise<void> {
    try {
      await this.sendEmail({
        to: client.email,
        template: 'welcome',
        variables: {
          clientName: client.name,
          companyName: client.company || 'your organization'
        }
      });

      logger.info('Welcome email sent', {
        clientId: client.id,
        email: client.email
      });
    } catch (error) {
      logger.error('Failed to send welcome email', { error, clientId: client.id });
      throw error;
    }
  }

  async sendRFPSubmissionConfirmation(rfp: RFP, clientEmail: string): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'rfp_submission',
        variables: {
          rfpTitle: rfp.title,
          rfpId: rfp.id,
          submittedAt: rfp.submittedAt?.toLocaleDateString() || 'N/A'
        }
      });

      logger.info('RFP submission confirmation sent', {
        rfpId: rfp.id,
        email: clientEmail
      });
    } catch (error) {
      logger.error('Failed to send RFP submission confirmation', { error, rfpId: rfp.id });
      throw error;
    }
  }

  async sendRFPStatusUpdate(rfp: RFP, clientEmail: string): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'rfp_status_update',
        variables: {
          rfpTitle: rfp.title,
          rfpId: rfp.id,
          status: rfp.status,
          reviewedAt: rfp.reviewedAt?.toLocaleDateString() || 'N/A'
        }
      });

      logger.info('RFP status update sent', {
        rfpId: rfp.id,
        status: rfp.status,
        email: clientEmail
      });
    } catch (error) {
      logger.error('Failed to send RFP status update', { error, rfpId: rfp.id });
      throw error;
    }
  }

  async sendOnboardingStarted(flow: OnboardingFlow, clientEmail: string): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'onboarding_started',
        variables: {
          flowType: flow.type,
          totalSteps: flow.totalSteps,
          startedAt: flow.startedAt?.toLocaleDateString() || 'N/A'
        }
      });

      logger.info('Onboarding started notification sent', {
        flowId: flow.id,
        email: clientEmail
      });
    } catch (error) {
      logger.error('Failed to send onboarding started notification', { error, flowId: flow.id });
      throw error;
    }
  }

  async sendOnboardingCompleted(flow: OnboardingFlow, clientEmail: string): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'onboarding_completed',
        variables: {
          flowType: flow.type,
          completedAt: flow.completedAt?.toLocaleDateString() || 'N/A',
          totalSteps: flow.totalSteps
        }
      });

      logger.info('Onboarding completed notification sent', {
        flowId: flow.id,
        email: clientEmail
      });
    } catch (error) {
      logger.error('Failed to send onboarding completed notification', { error, flowId: flow.id });
      throw error;
    }
  }

  async sendDocumentApprovalNotification(
    documentName: string,
    clientEmail: string,
    approvedBy: string
  ): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'document_approved',
        variables: {
          documentName,
          approvedBy,
          approvedAt: new Date().toLocaleDateString()
        }
      });

      logger.info('Document approval notification sent', {
        documentName,
        email: clientEmail,
        approvedBy
      });
    } catch (error) {
      logger.error('Failed to send document approval notification', { error, documentName });
      throw error;
    }
  }

  async sendDocumentRejectionNotification(
    documentName: string,
    clientEmail: string,
    reason: string
  ): Promise<void> {
    try {
      await this.sendEmail({
        to: clientEmail,
        template: 'document_rejected',
        variables: {
          documentName,
          reason,
          rejectedAt: new Date().toLocaleDateString()
        }
      });

      logger.info('Document rejection notification sent', {
        documentName,
        email: clientEmail,
        reason
      });
    } catch (error) {
      logger.error('Failed to send document rejection notification', { error, documentName });
      throw error;
    }
  }

  async sendInternalNotification(
    to: string | string[],
    subject: string,
    message: string,
    data?: Record<string, any>
  ): Promise<void> {
    try {
      await this.sendEmail({
        to,
        template: 'internal_notification',
        variables: {
          subject,
          message,
          data: data ? JSON.stringify(data, null, 2) : '',
          timestamp: new Date().toLocaleString()
        }
      });

      logger.info('Internal notification sent', {
        to,
        subject
      });
    } catch (error) {
      logger.error('Failed to send internal notification', { error, subject });
      throw error;
    }
  }

  private async initializeTransporter(): Promise<void> {
    if (environment.nodeEnv === 'development') {
      // Use Ethereal Email for development
      const testAccount = await nodemailer.createTestAccount();
      
      this.transporter = nodemailer.createTransporter({
        host: 'smtp.ethereal.email',
        port: 587,
        secure: false,
        auth: {
          user: testAccount.user,
          pass: testAccount.pass
        }
      });

      logger.info('Email transporter initialized (development mode)', {
        host: 'smtp.ethereal.email',
        user: testAccount.user
      });
    } else {
      // Use configured SMTP for production
      this.transporter = nodemailer.createTransporter({
        host: environment.email.smtp.host,
        port: environment.email.smtp.port,
        secure: environment.email.smtp.secure,
        auth: {
          user: environment.email.smtp.user,
          pass: environment.email.smtp.pass
        }
      });

      logger.info('Email transporter initialized (production mode)', {
        host: environment.email.smtp.host
      });
    }

    // Verify connection
    await this.transporter.verify();
    logger.info('Email transporter verified successfully');
  }

  private async loadTemplates(): Promise<void> {
    const templates: Record<string, EmailTemplate> = {
      welcome: {
        subject: 'Welcome to ESTRATIX - {{clientName}}',
        html: `
          <h1>Welcome to ESTRATIX, {{clientName}}!</h1>
          <p>Thank you for choosing ESTRATIX for {{companyName}}. We're excited to work with you!</p>
          <p>Our team will be in touch shortly to begin the onboarding process.</p>
          <p>If you have any questions, please don't hesitate to reach out.</p>
          <p>Best regards,<br>The ESTRATIX Team</p>
        `,
        text: `Welcome to ESTRATIX, {{clientName}}! Thank you for choosing ESTRATIX for {{companyName}}. We're excited to work with you! Our team will be in touch shortly to begin the onboarding process. If you have any questions, please don't hesitate to reach out. Best regards, The ESTRATIX Team`
      },
      rfp_submission: {
        subject: 'RFP Submission Confirmation - {{rfpTitle}}',
        html: `
          <h1>RFP Submission Confirmed</h1>
          <p>Your RFP "{{rfpTitle}}" (ID: {{rfpId}}) has been successfully submitted on {{submittedAt}}.</p>
          <p>Our team will review your requirements and get back to you within 2-3 business days.</p>
          <p>Thank you for your interest in ESTRATIX!</p>
        `,
        text: `RFP Submission Confirmed. Your RFP "{{rfpTitle}}" (ID: {{rfpId}}) has been successfully submitted on {{submittedAt}}. Our team will review your requirements and get back to you within 2-3 business days. Thank you for your interest in ESTRATIX!`
      },
      rfp_status_update: {
        subject: 'RFP Status Update - {{rfpTitle}}',
        html: `
          <h1>RFP Status Update</h1>
          <p>Your RFP "{{rfpTitle}}" (ID: {{rfpId}}) status has been updated to: <strong>{{status}}</strong></p>
          <p>Review completed on: {{reviewedAt}}</p>
          <p>We'll be in touch with next steps shortly.</p>
        `,
        text: `RFP Status Update. Your RFP "{{rfpTitle}}" (ID: {{rfpId}}) status has been updated to: {{status}}. Review completed on: {{reviewedAt}}. We'll be in touch with next steps shortly.`
      },
      onboarding_started: {
        subject: 'Onboarding Process Started - Welcome to ESTRATIX',
        html: `
          <h1>Your Onboarding Journey Begins!</h1>
          <p>We've started your {{flowType}} onboarding process on {{startedAt}}.</p>
          <p>This process includes {{totalSteps}} steps designed to get you up and running quickly.</p>
          <p>Our team will guide you through each step. Let's get started!</p>
        `,
        text: `Your Onboarding Journey Begins! We've started your {{flowType}} onboarding process on {{startedAt}}. This process includes {{totalSteps}} steps designed to get you up and running quickly. Our team will guide you through each step. Let's get started!`
      },
      onboarding_completed: {
        subject: 'Onboarding Complete - Welcome to the ESTRATIX Family!',
        html: `
          <h1>Congratulations! Onboarding Complete</h1>
          <p>You've successfully completed your {{flowType}} onboarding process on {{completedAt}}!</p>
          <p>All {{totalSteps}} steps have been completed. You're now ready to start working with ESTRATIX.</p>
          <p>Welcome to the ESTRATIX family! We're excited to help you achieve your goals.</p>
        `,
        text: `Congratulations! Onboarding Complete. You've successfully completed your {{flowType}} onboarding process on {{completedAt}}! All {{totalSteps}} steps have been completed. You're now ready to start working with ESTRATIX. Welcome to the ESTRATIX family! We're excited to help you achieve your goals.`
      },
      document_approved: {
        subject: 'Document Approved - {{documentName}}',
        html: `
          <h1>Document Approved</h1>
          <p>Your document "{{documentName}}" has been approved by {{approvedBy}} on {{approvedAt}}.</p>
          <p>You can now proceed to the next step in your process.</p>
        `,
        text: `Document Approved. Your document "{{documentName}}" has been approved by {{approvedBy}} on {{approvedAt}}. You can now proceed to the next step in your process.`
      },
      document_rejected: {
        subject: 'Document Requires Revision - {{documentName}}',
        html: `
          <h1>Document Requires Revision</h1>
          <p>Your document "{{documentName}}" requires revision.</p>
          <p><strong>Reason:</strong> {{reason}}</p>
          <p>Please make the necessary changes and resubmit the document.</p>
          <p>Rejected on: {{rejectedAt}}</p>
        `,
        text: `Document Requires Revision. Your document "{{documentName}}" requires revision. Reason: {{reason}}. Please make the necessary changes and resubmit the document. Rejected on: {{rejectedAt}}`
      },
      internal_notification: {
        subject: 'ESTRATIX Internal Notification - {{subject}}',
        html: `
          <h1>Internal Notification</h1>
          <p><strong>Subject:</strong> {{subject}}</p>
          <p><strong>Message:</strong> {{message}}</p>
          {{#if data}}
          <p><strong>Data:</strong></p>
          <pre>{{data}}</pre>
          {{/if}}
          <p><strong>Timestamp:</strong> {{timestamp}}</p>
        `,
        text: `Internal Notification. Subject: {{subject}}. Message: {{message}}. {{#if data}}Data: {{data}}{{/if}} Timestamp: {{timestamp}}`
      }
    };

    for (const [name, template] of Object.entries(templates)) {
      this.templates.set(name, template);
    }

    logger.debug(`Loaded ${Object.keys(templates).length} email templates`);
  }

  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;
    
    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    }

    // Handle conditional blocks (simple implementation)
    result = result.replace(/{{#if (\w+)}}([\s\S]*?){{/if}}/g, (match, condition, content) => {
      return variables[condition] ? content : '';
    });

    return result;
  }
}