# f004: Documentation Ingestion Flow

## 1. Metadata

* **Flow ID:** `f004`
* **Flow Name:** Documentation Ingestion Flow
* **Version:** 1.0
* **Status:** Implemented
* **Last Updated:** 2025-07-08
* **Owner/SME:** CIO
* **Responsible Command Office:** CIO
* **SOP References:** [To be defined]
* **SLOs/SLAs:** [To be defined]

## 2. Purpose & Goal

* **Purpose:** To provide a standardized, automated, and reliable mechanism for ingesting external knowledge from various sources (websites, documents) into the central vector database, making it available for semantic search and retrieval by all agents.
* **Goal(s):** To ensure 99.9% of ingestion requests are processed successfully within 1 hour of being registered in the `source_matrix.md`.

## 3. Scope

* **In Scope:** Orchestration of the entire ingestion pipeline, from content acquisition and processing to embedding and database loading. Monitoring the `source_matrix.md` for new tasks. Updating the status of ingestion jobs.
* **Out of Scope:** The manual approval process for adding new sources to the matrix. The internal implementation details of the specialist agents (scraping, embedding generation). Long-term knowledge maintenance and decay.

## 4. Triggers

* A new entry with the status `Pending Ingestion` is added to the `source_matrix.md`.

## 5. Inputs

* A single row from `source_matrix.md` containing the source URL/path and metadata.
* The output of the `p001` (Knowledge Ingestion) process.

## 6. Outputs

* Vector embeddings loaded into the QdrantDB vector database.
* An updated status (`Completed` or `Failed`) for the corresponding entry in `source_matrix.md`.
* A detailed log entry in `ingestion_log_matrix.md`.

## 7. Constituent Work Units & Sequence

This flow orchestrates the `p001` (Knowledge Ingestion) process, which is composed of the following tasks executed in sequence:

1. **Content Acquisition:** `a004` (Web Scraping Specialist) or `a005` (PDF Processing Specialist) acquires the raw content.
2. **Content Processing:** `a006` (Content Processing Specialist) cleans and chunks the raw content.
3. **Embedding Generation:** `a021` (Embedding Agent) generates vector embeddings for the content chunks.
4. **Database Loading:** `a022` (Vector DB Loader Agent) loads the embeddings and metadata into the database.

## 7.1. Implementation Checklist / Acceptance Criteria

* [x] **Criterion for `p001`:** The `p001` process must execute end-to-end.
* [x] **Criterion for Content Acquisition:** The correct agent (`a004` or `a005`) is invoked based on the source type.
* [x] **Criterion for Database Loading:** Embeddings are successfully written to the vector database with correct metadata.
* [x] **Overall Flow Criterion 1:** The flow correctly reads from and updates the `source_matrix.md`.
* [x] **Overall Flow Criterion 2:** End-to-end execution completes and logs are generated in `ingestion_log_matrix.md`.

## 8. Key Roles & Agents

* **Orchestrator:** The Documentation Ingestion Flow itself (implemented as a CrewAI crew).
* **Specialist Agents:**
  * `a004`: Web Scraping Specialist
  * `a005`: PDF Processing Specialist
  * `a006`: Content Processing Specialist
  * `a021`: Embedding Agent
  * `a022`: Vector DB Loader Agent

## 9. Tools & MCPs Utilized

* CrewAI Framework
* QdrantDB Vector Database
* Firecrawl (via Tool)

## 10. Success Metrics & KPIs

* **Cycle Time:** Time from `Pending Ingestion` to `Completed` status in `source_matrix.md`.
* **Success Rate:** Percentage of ingestion jobs that complete without errors.
* **Data Quality:** [To be defined - e.g., a metric for the relevance of retrieved chunks after ingestion].

## 11. Dependencies

* **Upstream Flows/Processes:** Manual or semi-automated processes for identifying and approving new knowledge sources.
* **Downstream Flows/Processes:** Any flow or process that relies on the knowledge base, such as `f007` (Autonomous Web Research Flow).

## 12. Exception Handling & Escalation

* **Common Issues:** Failed scrapes, invalid source URLs, embedding model errors, database connection failures.
* **Handling:** Failed steps are logged in `ingestion_log_matrix.md`. The flow attempts a retry up to 3 times. If it still fails, the status in `source_matrix.md` is set to `Failed`, and a notification is sent to the CIO Command Office for manual review.
* **Logging Strategy:** All execution steps, inputs, outputs, and errors are logged to the central observability platform under the `FlowExecutionLogs` table, filterable by `FlowID: f004`.

## 13. PDCA (Plan-Do-Check-Act) / Continuous Improvement

* **Review Cadence:** Quarterly
* **Responsible for Review:** CIO
* **Key Metrics for Review:** Cycle Time, Success Rate.
* **Process for Incorporating Improvements:** Proposals for improvement are submitted as `Proposal` components. Approved changes are implemented in the next release cycle.

### 13.1. Lessons Learned & Iterations

* *Initial Version 1.0:* The flow was designed based on the initial `CIO_P001` process definition.

## 14. Agentic Framework Mapping

* **Windsurf Workflows:** This flow definition can be used as input for the `/flow_generation` workflow to create or update the CrewAI implementation.
* **CrewAI Conceptual Mapping:** The flow is implemented as a single CrewAI `Crew`. The constituent work units from Section 7 are mapped to `Tasks` within the crew, assigned to the specialist `Agents` listed in Section 8.
