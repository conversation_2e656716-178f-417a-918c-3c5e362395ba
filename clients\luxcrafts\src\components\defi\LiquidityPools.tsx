import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Percent, 
  Clock, 
  Zap, 
  Shield, 
  Award,
  Plus,
  Minus,
  RefreshCw,
  Info,
  ExternalLink,
  Coins,
  BarChart3,
  Target,
  Calendar
} from 'lucide-react';

interface LiquidityPool {
  id: string;
  name: string;
  tokenA: Token;
  tokenB: Token;
  totalLiquidity: number;
  apr: number;
  volume24h: number;
  fees24h: number;
  userLiquidity?: number;
  userRewards?: number;
  lockPeriod?: number; // in days
  multiplier: number;
  isStable: boolean;
}

interface Token {
  symbol: string;
  name: string;
  address: string;
  decimals: number;
  price: number;
  logo: string;
  balance?: number;
}

interface StakingPosition {
  id: string;
  poolId: string;
  amount: number;
  startDate: Date;
  lockPeriod: number;
  apr: number;
  rewards: number;
  status: 'active' | 'unlocking' | 'completed';
}

interface TokenomicsData {
  totalSupply: number;
  circulatingSupply: number;
  marketCap: number;
  stakingRatio: number;
  burnRate: number;
  inflationRate: number;
  treasuryBalance: number;
}

const LiquidityPools: React.FC = () => {
  const [selectedPool, setSelectedPool] = useState<string>('');
  const [stakeAmount, setStakeAmount] = useState('');
  const [unstakeAmount, setUnstakeAmount] = useState('');
  const [selectedLockPeriod, setSelectedLockPeriod] = useState(30);
  const [activeTab, setActiveTab] = useState<'pools' | 'staking' | 'rewards' | 'tokenomics'>('pools');
  const [userPositions, setUserPositions] = useState<StakingPosition[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Mock data
  const luxToken: Token = {
    symbol: 'LUX',
    name: 'Luxcrafts Token',
    address: '0x1234...5678',
    decimals: 18,
    price: 2.45,
    logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20token%20logo%20gold%20diamond&image_size=square',
    balance: 1000
  };

  const liquidityPools: LiquidityPool[] = [
    {
      id: 'lux-usdc',
      name: 'LUX/USDC',
      tokenA: luxToken,
      tokenB: {
        symbol: 'USDC',
        name: 'USD Coin',
        address: '0xA0b8...6789',
        decimals: 6,
        price: 1.00,
        logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=USDC%20coin%20logo%20blue%20circle&image_size=square',
        balance: 5000
      },
      totalLiquidity: 2500000,
      apr: 45.2,
      volume24h: 125000,
      fees24h: 375,
      userLiquidity: 15000,
      userRewards: 125.50,
      lockPeriod: 30,
      multiplier: 1.5,
      isStable: false
    },
    {
      id: 'lux-eth',
      name: 'LUX/ETH',
      tokenA: luxToken,
      tokenB: {
        symbol: 'ETH',
        name: 'Ethereum',
        address: '0xC02a...4567',
        decimals: 18,
        price: 2340.50,
        logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=ethereum%20logo%20purple%20diamond&image_size=square',
        balance: 2.5
      },
      totalLiquidity: 1800000,
      apr: 62.8,
      volume24h: 89000,
      fees24h: 267,
      userLiquidity: 8500,
      userRewards: 89.25,
      lockPeriod: 60,
      multiplier: 2.0,
      isStable: false
    },
    {
      id: 'lux-stable',
      name: 'LUX/STABLE',
      tokenA: luxToken,
      tokenB: {
        symbol: 'LUSD',
        name: 'Luxcrafts Stable',
        address: '0xB1c2...3456',
        decimals: 18,
        price: 1.00,
        logo: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=stable%20coin%20logo%20green%20shield&image_size=square',
        balance: 10000
      },
      totalLiquidity: 3200000,
      apr: 28.5,
      volume24h: 156000,
      fees24h: 468,
      userLiquidity: 25000,
      userRewards: 156.75,
      lockPeriod: 14,
      multiplier: 1.2,
      isStable: true
    }
  ];

  const tokenomics: TokenomicsData = {
    totalSupply: 100000000,
    circulatingSupply: 65000000,
    marketCap: 159250000,
    stakingRatio: 0.35,
    burnRate: 0.02,
    inflationRate: 0.05,
    treasuryBalance: 5000000
  };

  const mockPositions: StakingPosition[] = [
    {
      id: '1',
      poolId: 'lux-usdc',
      amount: 15000,
      startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
      lockPeriod: 30,
      apr: 45.2,
      rewards: 125.50,
      status: 'active'
    },
    {
      id: '2',
      poolId: 'lux-eth',
      amount: 8500,
      startDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      lockPeriod: 60,
      apr: 62.8,
      rewards: 289.25,
      status: 'unlocking'
    }
  ];

  useEffect(() => {
    setUserPositions(mockPositions);
  }, []);

  const handleStake = async () => {
    if (!selectedPool || !stakeAmount) return;
    
    setIsLoading(true);
    // Simulate staking transaction
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const newPosition: StakingPosition = {
      id: Date.now().toString(),
      poolId: selectedPool,
      amount: parseFloat(stakeAmount),
      startDate: new Date(),
      lockPeriod: selectedLockPeriod,
      apr: liquidityPools.find(p => p.id === selectedPool)?.apr || 0,
      rewards: 0,
      status: 'active'
    };
    
    setUserPositions(prev => [...prev, newPosition]);
    setStakeAmount('');
    setIsLoading(false);
  };

  const handleUnstake = async (positionId: string) => {
    setIsLoading(true);
    // Simulate unstaking transaction
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setUserPositions(prev => 
      prev.map(pos => 
        pos.id === positionId 
          ? { ...pos, status: 'unlocking' as const }
          : pos
      )
    );
    setIsLoading(false);
  };

  const calculateRewards = (position: StakingPosition): number => {
    const daysStaked = Math.floor((Date.now() - position.startDate.getTime()) / (1000 * 60 * 60 * 24));
    const dailyRate = position.apr / 365 / 100;
    return position.amount * dailyRate * daysStaked;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const formatPercentage = (num: number): string => {
    return `${num.toFixed(2)}%`;
  };

  return (
    <div className="max-w-7xl mx-auto p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">DeFi Liquidity Pools</h1>
        <p className="text-gray-600">
          Stake tokens, provide liquidity, and earn rewards through our decentralized finance protocol
        </p>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'pools', label: 'Liquidity Pools', icon: <Coins className="h-4 w-4" /> },
          { id: 'staking', label: 'Staking', icon: <Shield className="h-4 w-4" /> },
          { id: 'rewards', label: 'Rewards', icon: <Award className="h-4 w-4" /> },
          { id: 'tokenomics', label: 'Tokenomics', icon: <BarChart3 className="h-4 w-4" /> }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-white text-blue-600 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Liquidity Pools Tab */}
      {activeTab === 'pools' && (
        <div className="space-y-6">
          {/* Pool Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Value Locked</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(liquidityPools.reduce((sum, pool) => sum + pool.totalLiquidity, 0))}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">24h Volume</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(liquidityPools.reduce((sum, pool) => sum + pool.volume24h, 0))}
                  </p>
                </div>
                <BarChart3 className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">24h Fees</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(liquidityPools.reduce((sum, pool) => sum + pool.fees24h, 0))}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Average APR</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(liquidityPools.reduce((sum, pool) => sum + pool.apr, 0) / liquidityPools.length)}
                  </p>
                </div>
                <Percent className="h-8 w-8 text-purple-500" />
              </div>
            </div>
          </div>

          {/* Pool List */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Available Pools</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pool
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      TVL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      APR
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      24h Volume
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Your Liquidity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {liquidityPools.map((pool) => (
                    <tr key={pool.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex -space-x-2">
                            <img className="h-8 w-8 rounded-full border-2 border-white" src={pool.tokenA.logo} alt={pool.tokenA.symbol} />
                            <img className="h-8 w-8 rounded-full border-2 border-white" src={pool.tokenB.logo} alt={pool.tokenB.symbol} />
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">{pool.name}</div>
                            <div className="flex items-center space-x-2">
                              {pool.isStable && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                  <Shield className="h-3 w-3 mr-1" />
                                  Stable
                                </span>
                              )}
                              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                {pool.multiplier}x Multiplier
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(pool.totalLiquidity)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="text-sm font-medium text-green-600">
                          {formatPercentage(pool.apr)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatCurrency(pool.volume24h)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {pool.userLiquidity ? (
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {formatCurrency(pool.userLiquidity)}
                            </div>
                            <div className="text-sm text-green-600">
                              +{formatCurrency(pool.userRewards || 0)} rewards
                            </div>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-500">-</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-900">
                            Add Liquidity
                          </button>
                          {pool.userLiquidity && (
                            <button className="text-red-600 hover:text-red-900">
                              Remove
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}

      {/* Staking Tab */}
      {activeTab === 'staking' && (
        <div className="space-y-6">
          {/* Staking Interface */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Stake Tokens</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Select Pool
                </label>
                <select
                  value={selectedPool}
                  onChange={(e) => setSelectedPool(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Choose a pool...</option>
                  {liquidityPools.map(pool => (
                    <option key={pool.id} value={pool.id}>
                      {pool.name} - {formatPercentage(pool.apr)} APR
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lock Period
                </label>
                <select
                  value={selectedLockPeriod}
                  onChange={(e) => setSelectedLockPeriod(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={7}>7 days - 1.0x multiplier</option>
                  <option value={30}>30 days - 1.5x multiplier</option>
                  <option value={60}>60 days - 2.0x multiplier</option>
                  <option value={90}>90 days - 2.5x multiplier</option>
                  <option value={180}>180 days - 3.0x multiplier</option>
                </select>
              </div>
            </div>
            
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Amount to Stake
              </label>
              <div className="relative">
                <input
                  type="number"
                  value={stakeAmount}
                  onChange={(e) => setStakeAmount(e.target.value)}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                  <span className="text-gray-500 text-sm">LUX</span>
                </div>
              </div>
              <p className="text-sm text-gray-500 mt-1">
                Balance: {formatNumber(luxToken.balance || 0)} LUX
              </p>
            </div>
            
            <button
              onClick={handleStake}
              disabled={!selectedPool || !stakeAmount || isLoading}
              className="w-full mt-4 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white py-2 px-4 rounded-md font-medium transition-colors"
            >
              {isLoading ? 'Staking...' : 'Stake Tokens'}
            </button>
          </div>

          {/* Active Positions */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Your Staking Positions</h2>
            </div>
            <div className="p-6">
              {userPositions.length === 0 ? (
                <div className="text-center py-8">
                  <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">No staking positions yet</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {userPositions.map((position) => {
                    const pool = liquidityPools.find(p => p.id === position.poolId);
                    const rewards = calculateRewards(position);
                    const daysRemaining = Math.max(0, position.lockPeriod - Math.floor((Date.now() - position.startDate.getTime()) / (1000 * 60 * 60 * 24)));
                    
                    return (
                      <div key={position.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="flex -space-x-2">
                              <img className="h-8 w-8 rounded-full border-2 border-white" src={pool?.tokenA.logo} alt={pool?.tokenA.symbol} />
                              <img className="h-8 w-8 rounded-full border-2 border-white" src={pool?.tokenB.logo} alt={pool?.tokenB.symbol} />
                            </div>
                            <div>
                              <h3 className="font-medium text-gray-900">{pool?.name}</h3>
                              <p className="text-sm text-gray-500">
                                {formatCurrency(position.amount)} staked
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-semibold text-green-600">
                              +{formatCurrency(rewards)}
                            </p>
                            <p className="text-sm text-gray-500">Rewards earned</p>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-3 gap-4 mb-4">
                          <div>
                            <p className="text-sm text-gray-500">APR</p>
                            <p className="font-medium">{formatPercentage(position.apr)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Lock Period</p>
                            <p className="font-medium">{position.lockPeriod} days</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-500">Days Remaining</p>
                            <p className="font-medium">{daysRemaining} days</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            position.status === 'active' ? 'bg-green-100 text-green-800' :
                            position.status === 'unlocking' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {position.status.charAt(0).toUpperCase() + position.status.slice(1)}
                          </span>
                          
                          {position.status === 'active' && daysRemaining === 0 && (
                            <button
                              onClick={() => handleUnstake(position.id)}
                              disabled={isLoading}
                              className="bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
                            >
                              {isLoading ? 'Unstaking...' : 'Unstake'}
                            </button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Tokenomics Tab */}
      {activeTab === 'tokenomics' && (
        <div className="space-y-6">
          {/* Token Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Market Cap</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(tokenomics.marketCap)}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Circulating Supply</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatNumber(tokenomics.circulatingSupply)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatPercentage((tokenomics.circulatingSupply / tokenomics.totalSupply) * 100)} of total
                  </p>
                </div>
                <Coins className="h-8 w-8 text-blue-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Staking Ratio</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatPercentage(tokenomics.stakingRatio * 100)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatNumber(tokenomics.circulatingSupply * tokenomics.stakingRatio)} LUX staked
                  </p>
                </div>
                <Shield className="h-8 w-8 text-purple-500" />
              </div>
            </div>
            
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Treasury Balance</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(tokenomics.treasuryBalance * luxToken.price)}
                  </p>
                  <p className="text-sm text-gray-500">
                    {formatNumber(tokenomics.treasuryBalance)} LUX
                  </p>
                </div>
                <Award className="h-8 w-8 text-yellow-500" />
              </div>
            </div>
          </div>

          {/* Tokenomics Details */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Tokenomics Overview</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-4">Supply Mechanics</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Supply:</span>
                    <span className="font-medium">{formatNumber(tokenomics.totalSupply)} LUX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Circulating Supply:</span>
                    <span className="font-medium">{formatNumber(tokenomics.circulatingSupply)} LUX</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Inflation Rate:</span>
                    <span className="font-medium text-red-600">+{formatPercentage(tokenomics.inflationRate * 100)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Burn Rate:</span>
                    <span className="font-medium text-green-600">-{formatPercentage(tokenomics.burnRate * 100)}</span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-md font-medium text-gray-900 mb-4">Staking Incentives</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base APR:</span>
                    <span className="font-medium">25%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Max Multiplier:</span>
                    <span className="font-medium">3.0x (180 days)</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Early Unstake Penalty:</span>
                    <span className="font-medium text-red-600">5%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Compound Frequency:</span>
                    <span className="font-medium">Daily</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mt-8 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-start space-x-3">
                <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-blue-900">Seigniorage & Arbitrage Protocol</h4>
                  <p className="text-blue-800 text-sm mt-1">
                    Our protocol automatically adjusts token supply based on market conditions, 
                    capturing arbitrage opportunities and distributing profits to long-term stakers. 
                    The treasury balance is used to maintain price stability and fund ecosystem development.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default LiquidityPools;