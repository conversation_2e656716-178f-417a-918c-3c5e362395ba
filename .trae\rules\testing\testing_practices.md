# ESTRATIX Testing Practices

This document defines the standards for testing all software components within the ESTRATIX framework. Adherence to these rules is mandatory to ensure quality, reliability, and maintainability.

## 1. Core Philosophy

- **Test-Driven Development (TDD):** For all new features, tests should be written before the implementation code. Write a failing test, write the code to make it pass, and then refactor.
- **Comprehensive Coverage:** Aim for high test coverage, but prioritize testing critical paths, business logic, and complex algorithms over simple getters and setters.
- **Automation:** All tests must be fully automated and integrated into the CI/CD pipeline.

## 2. The Testing Pyramid

Follow the testing pyramid model to balance the test suite:

- **Unit Tests (Most Numerous):**
  - Each unit of code (function, class method) must have corresponding unit tests.
  - Mock all external dependencies (e.g., databases, APIs, file system) to ensure tests are fast and isolated.
  - Use frameworks like pytest for Python and Jest or Vitest for JavaScript.

- **Integration Tests:**
  - Test the interaction between multiple components or services (e.g., application service and database adapter).
  - Use real dependencies where feasible (e.g., a test database container) or high-fidelity fakes.
  - Focus on data flow and contract enforcement between components.

- **End-to-End (E2E) Tests (Least Numerous):**
  - Test entire user flows from the UI to the backend and database.
  - Use tools like Cypress, Playwright, or Selenium for browser automation.
  - These tests are slow and brittle; reserve them for the most critical user journeys.

## 3. Test Organization and Naming

- **Structure:** Test files should mirror the source code structure (e.g., src/domain/services/service.py is tested by 	ests/domain/services/test_service.py).
- **Naming:** Test functions should be descriptive, following a 	est_when_then pattern (e.g., 	est_calculate_total_when_items_present_then_returns_correct_sum).

## 4. Assertions and Validation

- **Be Specific:** Assert specific outcomes rather than generic truths (e.g., assert 
esult == 5, not 
esult is not None).
- **Single Assertion per Test:** Ideally, each test case should assert only one logical condition. If multiple assertions are needed, ensure they are all testing the same unit of behavior.
