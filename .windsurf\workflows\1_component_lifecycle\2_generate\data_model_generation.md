---
description: Guides the generation of Pydantic models from conceptual ESTRATIX data model definitions.
---

# Workflow: Generate Pydantic Model from Conceptual Definition

This workflow outlines the steps to translate a conceptual ESTRATIX Data Model definition (Markdown) into a Pydantic Python class implementation.

**Responsible Agent/Role:** `AGENT_Pydantic_Model_Generator`

## Prerequisites:

*   A completed conceptual data model definition: `docs/data_models/[component_type_lowercase]/[ModelNamePascalCase]_Definition.md`.
*   The `docs/data_models/data_model_matrix.md` entry for the model should exist with "Status" as "Definition".
*   Familiarity with Pydantic and Python type hinting.
*   Target directory for implementation: `src/domain/models/[component_type_lowercase]/`

## Steps:

### Step 1: Identify Model and Define Variables

1.  **Select Model:**
    -   **Action:** Choose a data model from `docs/data_models/data_model_matrix.md` with status "Definition".
    -   **Tool:** `view_file_outline`
    -   **Example:** `<!-- view_file_outline('docs/data_models/data_model_matrix.md') -->`

2.  **Define Variables:**
    -   **Action:** From the matrix and the conceptual definition, define all necessary string variables for the subsequent steps.
    -   **Example Variables:**
        -   `model_id`: "DM-USR-001"
        -   `model_name_pascal_case`: "UserServiceProfile"
        -   `model_name_snake_case`: "user_service_profile"
        -   `component_type_lowercase`: "user"
        -   `conceptual_def_path`: "docs/data_models/user/UserServiceProfile_Definition.md"
        -   `implementation_dir`: "src/domain/models/user"
        -   `implementation_path`: "src/domain/models/user/user_service_profile.py"
        -   `test_dir`: "tests/unit/domain/models/user"
        -   `test_path`: "tests/unit/domain/models/user/test_user_service_profile.py"

### Step 2: Review Conceptual Definition

-   **Action:** Read the conceptual definition markdown file to understand all attributes, types, relationships, and constraints.
-   **Tool:** `view_file_outline`
-   **Example:** `<!-- view_file_outline('{{conceptual_def_path}}') -->`

### Step 3: Create Directories

-   **Action:** Ensure the target directories for the implementation and test files exist.
-   **Tool:** `run_command`
-   **Example:**
    ```markdown
    <!-- run_command('mkdir -p {{implementation_dir}}') -->
    <!-- run_command('mkdir -p {{test_dir}}') -->
    ```

### Step 4: Implement Pydantic Model

1.  **Create Python File:**
    -   **Action:** Create the Python file and populate it with the Pydantic model code, translating the conceptual definition into a concrete implementation.
    -   **Tool:** `write_to_file`
    -   **Example:**
        ```markdown
        <!-- write_to_file('{{implementation_path}}', 'from pydantic import BaseModel, Field\nfrom typing import Optional\nimport uuid\nfrom datetime import datetime\n\nclass {{model_name_pascal_case}}(BaseModel):\n    """Docstring for {{model_name_pascal_case}}."""\n    id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the model.")\n    # ... other fields based on conceptual doc ...\n\n    class Config:\n        orm_mode = True') -->
        ```

2.  **Make Model Importable:**
    -   **Action:** Create or update the `__init__.py` file in the implementation directory to ensure the new model is easily importable.
    -   **Tool:** `write_to_file` or `replace_file_content`
    -   **Example:** `<!-- write_to_file('{{implementation_dir}}/__init__.py', 'from .{{model_name_snake_case}} import {{model_name_pascal_case}}\n') -->`

### Step 5: Implement Unit Tests

-   **Action:** Create a test file with basic tests for model instantiation and validation.
-   **Tool:** `write_to_file`
-   **Example:**
    ```markdown
    <!-- write_to_file('{{test_path}}', 'import pytest\nfrom {{implementation_dir|replace("/", ".")}} import {{model_name_pascal_case}}\n\ndef test_{{model_name_snake_case}}_creation():\n    """Tests basic instantiation and data validation."""\n    data = { "name": "Test Model", "description": "A test description." } # Add required fields\n    model = {{model_name_pascal_case}}(**data)\n    assert model.name == "Test Model"\n    assert model.id is not None') -->
    ```

### Step 6: Update Data Model Matrix

-   **Action:** Update the model's row in `docs/data_models/data_model_matrix.md` to change the status to "Implemented" and add a link to the new file.
-   **Tool:** `replace_file_content`
-   **Guidance:** First, read the file to find the exact line to target. Then, construct the replacement content.
-   **Example:**
    ```markdown
    <!-- replace_file_content('docs/data_models/data_model_matrix.md', replacement_chunks=[{ 'target_content': '| {{model_id}} | ... | Definition | ... | TBD |', 'replacement_content': '| {{model_id}} | ... | Implemented | ... | [Link](../../../{{implementation_path}}) |' }]) -->
    ```

### Step 7: Commit Changes

-   **Action:** Commit the new model, test, and updated matrix to version control.
-   **Tool:** `run_command`
-   **Example:**
    ```markdown
    <!-- run_command('git add .') -->
    <!-- run_command('git commit -m "FEAT(Domain): Implement Pydantic model for {{model_name_pascal_case}}"') -->
    ```

## Outputs:

*   A new Python file: `src/domain/models/[component_type_lowercase]/[model_name_snake_case].py`.
*   (Optional) A new Python test file: `tests/domain/models/[component_type_lowercase]/test_[model_name_snake_case].py`.
*   An updated `docs/data_models/data_model_matrix.md`.

## Example Pydantic Snippet:
```python
from pydantic import BaseModel, Field
from typing import Optional
import uuid
from datetime import datetime

class SimpleModel(BaseModel):
    id: uuid.UUID = Field(default_factory=uuid.uuid4)
    name: str
    description: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

    class Config:
        allow_population_by_field_name = True
```
