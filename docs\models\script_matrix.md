# ESTRATIX Implemented Scripts Registry

---

## 1. Overview

This matrix serves as a central registry for all operational scripts implemented within the ESTRATIX project. It provides a version-controlled, auditable catalog of reusable automation assets, linking them to the specific services, flows, or tasks they support. This ensures discoverability and prevents redundant effort.

---

## 2. Script Inventory

| Script ID | Description | Source Location | Associated Service/Flow | Language/Tool | Version | Status |
|-----------|-------------|-----------------|-------------------------|---------------|---------|--------|
| S-GEN-001 | Boilerplate Generator for FastAPI Services | `/scripts/generation/gen_fastapi_service.py` | `service_generation` workflow | Python | 1.0 | Active |
| S-TEST-001 | Run All Pytest Unit Tests | `/scripts/testing/run_all_tests.sh` | CI/CD Pipeline | Bash | 1.1 | Active |
| S-DEPLOY-001 | Deploy to Staging Environment | `/scripts/deployment/deploy_staging.ps1` | `CTO_F001_DeployApplication` | PowerShell | 1.0 | Active |
| S-GEN-002 | Boilerplate Generator for React+Vite Frontend | `/scripts/generation/gen_react_vite_app.js` | `productized_service_generation` workflow | Node.js (zx) | 1.0 | Proposed |
| S-INFRA-001 | VPS Bootstrap & Hardening Script | `/src/infrastructure/deployment/scripts/vps_bootstrap.sh` | `CTO_P001_InfrastructureProvisioning` | Bash | 1.0 | Active |
| S-INFRA-002 | Dokploy Multi-Server Deployment | `/src/infrastructure/deployment/scripts/dokploy_setup.sh` | `CTO_P002_ContainerOrchestration` | Bash | 1.0 | Active |
| S-INFRA-003 | Multi-Node Kubernetes Cluster Setup | `/src/infrastructure/deployment/scripts/multi_node_setup.sh` | `CTO_P003_KubernetesClusterManagement` | Bash | 1.0 | Active |
| S-INFRA-004 | CI/CD Pipeline Configuration | `/src/infrastructure/deployment/scripts/setup_cicd_pipeline.sh` | `CTO_P004_CICDPipelineManagement` | Bash | 1.0 | Active |
| S-INFRA-005 | Secure Tunneling & Remote Access Setup | `/src/infrastructure/deployment/scripts/setup_secure_tunneling.sh` | `CTO_P005_NetworkSecurityManagement` | Bash | 1.0 | Active |
| S-MAINT-001 | ESTRATIX Naming Convention Fix Tool | `/src/infrastructure/deployment/scripts/fix_naming_conventions.py` | `CIO_P001_ComponentAlignmentManagement` | Python | 1.0 | Active |
| S-DOC-001 | Comprehensive VPS Deployment Guide | `/src/infrastructure/deployment/ESTRATIX_VPS_DEPLOYMENT_GUIDE.md` | `CTO_P006_InfrastructureDocumentation` | Markdown | 1.0 | Active |

---

## 3. Maintenance

This matrix must be updated whenever a new reusable script is created or an existing one is significantly modified. All scripts must adhere to the standards defined in a forthcoming `scripting_standards.md` document.
