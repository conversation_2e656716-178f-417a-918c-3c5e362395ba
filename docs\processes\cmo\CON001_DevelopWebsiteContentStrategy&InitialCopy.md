# ESTRATIX Process Definition: Develop Website Content Strategy & Initial Copy

## 1. Process Metadata

*   **Process ID:** `CON001`
*   **Process Name:** `Develop Website Content Strategy & Initial Copy`
*   **Version:** `1.0`
*   **Creation Date:** `2025-05-11`
*   **Last Updated:** `2025-05-11`
*   **Owner/Maintainer:** `Content Team / Lead Content Strategist`
*   **Status:** `Draft`
*   **Related ESTRATIX Global Rule(s):** `Content Quality Standards, SEO Best Practices, Brand Voice Guidelines (ref: MEMORY[user_global])`

## 2. Purpose & Goal

*   **Purpose:** To develop a comprehensive website content strategy and create initial, high-quality, SEO-informed copy for key website pages. This process ensures that the website's messaging aligns with the brand voice, target audience (defined in `PLN001`), and design layouts (from `DES001`), ultimately supporting user engagement, conversion goals, and overall ESTRATIX quality standards.
*   **Goal(s):**
    *   **Strategic Foundation:** Define and document a clear `Content Strategy` including website voice, tone, style guidelines, core content pillars/themes, and an initial SEO keyword strategy.
    *   **Compelling Copy:** Produce initial, engaging, persuasive, and SEO-optimized copy for all primary pages defined in the sitemap (`PLN001`) and appropriately structured for the approved UI designs (`DES001`).
    *   **Efficient Handoff:** Establish a clear workflow for content review, client approval, and seamless handoff of the final copy deck to the development team (`DEV00X`) for website population.
    *   **Future-Proofing:** Outline a preliminary plan for ongoing content creation, maintenance, updates, and governance post-launch to ensure long-term content relevance and effectiveness.
    *   **Conversion Focus:** Ensure all content, including calls-to-action (CTAs), is crafted to effectively communicate the value proposition and guide users towards desired outcomes (e.g., inquiries, purchases, sign-ups).

## 3. Scope

*   **In Scope:**
    *   Thorough interpretation of `PLN001` (Website Strategy, Sitemap, Target Audience, UX Pillars) and `DES001` (Wireframes, Mockups, Component Layouts) outputs to inform content needs.
    *   Defining and documenting the website's voice, tone, and style guide specific to written content.
    *   Developing core content pillars or themes that align with brand messaging and user interests.
    *   Conducting foundational keyword research and developing an initial SEO content plan (identifying target keywords for key pages).
    *   Writing, editing, and proofreading initial website copy for key pages (e.g., Homepage, About Us, Service/Product Pages, Key Landing Pages, Contact Us).
    *   Creating a `Content Strategy Document` that encapsulates all strategic content decisions.
    *   Developing a `Content Matrix` or `Content Plan` document (e.g., spreadsheet) mapping specific content elements (headlines, body copy, CTAs) to sitemap pages and corresponding design modules/components.
    *   Strategic planning and drafting of effective calls-to-action (CTAs) for each key page.
    *   Considering content presentation and readability needs for different device viewports as per responsive designs from `DES001`.
    *   Defining a process for obtaining necessary internal, client, and potentially legal/compliance reviews for all drafted content.
    *   Structuring content logically for user experience and search engine visibility.
*   **Out of Scope:**
    *   Initial website strategy development, sitemap creation, or detailed market research (these are outputs of `PLN001`).
    *   UI/UX design, wireframing, mockup creation, or visual asset design (these are outputs of `DES001`).
    *   Final graphic design, video production, photography, or sourcing of all final visual assets (though the content strategy may identify the *need* for such assets).
    *   Full technical SEO implementation (e.g., implementing schema markup, site speed optimization, advanced link building - though content will be created with SEO best practices in mind).
    *   Translation and localization of content into multiple languages (this would typically be a separate, subsequent `CON00X` process or a specialized sub-process).
    *   Ongoing creation of extensive blog posts, articles, social media content, or other secondary/supporting content beyond the initial key website pages (unless specifically identified as critical for launch within `PLN001`).
    *   Management of content within a CMS post-development.

## 4. Triggers

*   **Approved `PLN001` Deliverables:** Receipt and confirmation of the finalized and client-approved `Website Strategy & Plan Document` from the `PLN001` process.
*   **Approved `DES001` Deliverables:** Receipt and confirmation of the finalized and client-approved `UI Design Specification Document` (especially wireframes and high-fidelity mockups) from the `DES001` process, which provide the visual context for content placement.
*   **Official Project Phase Transition:** Formal hand-off to the Content Team Lead / Lead Content Strategist and allocation of content creation resources within the ESTRATIX project management system.

## 5. Inputs

*   **Input Name:** `Approved Website Strategy & Plan Document (from PLN001)`
    *   **Type:** `Digital Document (Markdown, PDF)`
    *   **Source:** `PLN001 Process Output, ESTRATIX Project Management System`
    *   **Format/Constraints:** Must include defined target audience personas, website objectives, UX pillars, core value proposition, and any initial content-related directives from the design brief section.
*   **Input Name:** `Finalized Sitemap (Visual & Textual) (from PLN001)`
    *   **Type:** `Image File (PNG, SVG), Structured Text/XML, Figma Link`
    *   **Source:** `PLN001 Process Output`
    *   **Format/Constraints:** Provides the list of pages requiring content.
*   **Input Name:** `Approved UI Design Specification Document (from DES001)`
    *   **Type:** `Figma File Link, Exported PDFs/PNGs, Comprehensive Digital Document`
    *   **Source:** `DES001 Process Output`
    *   **Format/Constraints:** Includes wireframes, mockups, and component layouts, showing content areas, character count estimates, and visual hierarchy.
*   **Input Name:** `Client Onboarding Package / Brand Brief / Existing Marketing Materials`
    *   **Type:** `Digital Documents, URLs, PDFs`
    *   **Source:** `Client, PLN001 Inputs, Sales/Account Management`
    *   **Format/Constraints:** Provides insights into existing brand messaging, voice, tone, style guides, product/service details, and competitor information.
*   **Input Name:** `SEO Keyword Research Data (Initial)`
    *   **Type:** `Spreadsheet, Document, Output from SEO tools`
    *   **Source:** `Marketing Team, SEO Specialist, or to be generated within this process`
    *   **Format/Constraints:** List of potential target keywords and competitor keyword analysis.
*   **Input Name:** `ESTRATIX Content Quality & SEO Standards / Checklists`
    *   **Type:** `Links to ESTRATIX Documentation`
    *   **Source:** `ESTRATIX Internal Knowledge Base`
    *   **Format/Constraints:** Provides guidelines for quality, readability, and on-page SEO.

## 6. Outputs / Deliverables

*   **Output Name:** `Content Strategy Document`
    *   **Type:** `Comprehensive Digital Document (Markdown, PDF, Google Doc, Notion Page)`
    *   **Destination/Storage:** `ESTRATIX Project Management System, Client Communication Portal`
    *   **Format/Quality Criteria:** Clearly outlines website voice, tone, style guidelines, content pillars/themes, primary target keywords per page, content governance plan (for post-launch).
*   **Output Name:** `Finalized Website Copy Deck`
    *   **Type:** `Digital Document (Google Doc, Word Doc, Markdown files per page)`
    *   **Destination/Storage:** `ESTRATIX Project Management System, DEV00X Inputs`
    *   **Format/Quality Criteria:** Contains all approved, proofread, and SEO-optimized copy for specified key website pages, structured logically (e.g., by page and section, matching wireframes), ready for development integration.
*   **Output Name:** `Content Matrix / Content Plan`
    *   **Type:** `Spreadsheet (Google Sheets, Excel)`
    *   **Destination/Storage:** `ESTRATIX Project Management System, Internal Team Reference`
    *   **Format/Quality Criteria:** Detailed mapping of content elements (headlines, body, CTAs, image alt-text ideas) to specific sitemap pages, corresponding design modules/components, target keywords, and responsible parties/status if applicable.
*   **Output Name:** `(Optional) Content Briefs for Specialized Content`
    *   **Type:** `Digital Document(s)`
    *   **Destination/Storage:** `ESTRATIX Project Management System, External Contributor Portal`
    *   **Format/Quality Criteria:** Clear instructions for any content to be sourced externally (e.g., technical articles, detailed case studies, specific legal disclaimers), including objectives, audience, key messaging, keywords, and desired format.
*   **Output Name:** `Client/Stakeholder Approval Record for Content Strategy & Initial Copy`
    *   **Type:** `Email Confirmation, Signed Document, Project Management System Update`
    *   **Destination/Storage:** `ESTRATIX Project Management System`
    *   **Format/Quality Criteria:** Explicit agreement on the final content strategy and copy deck.

## 7. High-Level Steps / Phases

    1.  **Initiation, Input Analysis & Briefing:** Receive and thoroughly analyze inputs from `PLN001` & `DES001`. Conduct internal briefing with the content team. Clarify scope and expectations.
    2.  **Content Strategy Development:** Define and document voice, tone, and style. Develop content pillars. Conduct/refine keyword research and map keywords to sitemap pages.
    3.  **Content Planning & Detailed Outlining (Content Matrix):** Create the Content Matrix. Develop detailed outlines for each key page, aligning with wireframes/mockups and allocating keywords. Plan CTAs.
    4.  **Initial Copywriting (Drafting):** Draft compelling and persuasive copy for all specified pages based on outlines and strategy.
    5.  **SEO Optimization, Editing & Proofreading:** Review and refine drafted copy for SEO effectiveness (keyword integration, readability), clarity, grammar, style consistency, and alignment with brand voice and design constraints.
    6.  **Internal Review & Iteration:** Conduct internal peer reviews within the content team. Iterate on copy based on feedback to ensure quality and consistency.
    7.  **Client/Stakeholder Presentation & Feedback Cycle:** Present the Content Strategy Document and initial Copy Deck to the client/stakeholders. Collect feedback and manage revisions.
    8.  **Final Approval & Handoff Preparation:** Obtain formal client sign-off on the strategy and all copy. Prepare the final Copy Deck and Content Matrix for handoff to `DEV00X`.

## 8. Key Tools, Technologies, & MCPs Involved

*   **Content Creation & Editing:** Google Docs, Microsoft Word, Notion, **Grammarly**, Hemingway Editor, Markdown Editors.
*   **SEO & Keyword Research Tools:** SEMrush, Ahrefs, Google Keyword Planner, Google Trends, AnswerThePublic. (Conceptual use for agents: tools that can analyze SERPs and suggest keywords).
*   **Project Management & Collaboration:** ESTRATIX PM System, Linear, Asana, Trello.
*   **Communication:** Slack (via `slack` MCP), Email, Google Meet/Zoom.
*   **File Storage & Sharing:** Google Drive, Dropbox, SharePoint.
*   **Windsurf MCPs/Tools (Conceptual for Agentic Execution):** `read_file_content` (for inputs from PLN001/DES001), `write_to_file` (for strategy docs, copy deck), `mcp3_brave_web_search` (for topic/competitor research), `create_memory` (for key strategic decisions, style guides). (Future: AI writing assistant MCPs, SEO analysis MCPs).

## 9. Roles & Responsibilities

*   **Role:** `Lead Content Strategist (Agent or Human)`
    *   **Responsibilities:** Overseeing the CON001 process, developing the core content strategy, ensuring quality and consistency, team coordination (if applicable), primary client contact for content.
*   **Role:** `Content Writer(s) / Copywriter(s) (Agent or Human)`
    *   **Responsibilities:** Executing copywriting tasks, conducting keyword research, adhering to style guides, collaborating with strategist and designers.
*   **Role:** `SEO Specialist (Consulted or part of Content Team)`
    *   **Responsibilities:** Providing keyword research, on-page SEO guidance, reviewing copy for SEO effectiveness.
*   **Role:** `Editor / Proofreader (Agent or Human)`
    *   **Responsibilities:** Ensuring grammatical accuracy, style consistency, clarity, and adherence to brand voice.
*   **Role:** `Client / Stakeholder`
    *   **Responsibilities:** Providing necessary inputs (e.g., product info, existing materials), participating in content reviews, providing timely feedback, approving final content strategy and copy.
*   **Role:** `(Consulted) UI/UX Designer (from DES001)`
    *   **Responsibilities:** Clarifying how content fits into design layouts, providing character count constraints or layout implications for content.
*   **Role:** `(Consulted) Legal/Compliance Reviewer (if applicable)`
    *   **Responsibilities:** Reviewing content for legal accuracy and compliance with industry regulations.

## 10. Metrics & KPIs

*   **Content Approval Rate:** (Number of content deliverables approved on first/second review / Total deliverables submitted).
*   **Time to Content Completion:** (From process initiation to final client approval of copy deck).
*   **SEO Keyword Integration Score:** (Percentage of target pages effectively incorporating primary keywords – can be an internal audit score).
*   **Readability Score:** (Average score from tools like Hemingway or Flesch-Kincaid for key pages).
*   **Client Satisfaction with Content:** (Feedback scores or qualitative comments).

## 11. Dependencies

*   **Upstream Processes:** `PLN001_DefineWebsitePlan&Sitemap` (critical for strategy, audience, sitemap), `DES001_CreateWebsiteWireframes&UIDesignSpecification` (critical for layout context).
*   **Downstream Processes:** `DEV00X_ApplicationDevelopment` (consumes final copy deck), `DEP001_DeployWebsiteWithCoolify` (deploys website with this content).
*   **External Systems/Services:** Client's existing brand/marketing materials, SEO tools, ESTRATIX PM & documentation tools.

## 12. Agentic Framework Mapping

    ### A. Windsurf Workflow Mapping:
    *   **Associated Windsurf Workflow File:** `docs/processes/workflows/CON001_DevelopContent_WF.md` (To be created)
    *   **Key Parameters / User Inputs:** Approved PLN001 & DES001 documentation, target page list.
    *   **Primary Windsurf Tools Used:** `read_file_content`, `write_to_file`, `mcp3_brave_web_search`, `create_memory`, (Future AI writing/SEO MCPs).

    ### B. CrewAI Mapping (Conceptual):
    *   **Potential Crew(s):** `ContentCreationCrew`, `SEOOpsCrew`
    *   **Agent Role(s) involved:** `ContentStrategistAgent`, `SEOKeywordResearcherAgent`, `CopywriterAgent` (per page/section), `ProofreaderAgent`, `ClientFeedbackIntegratorAgent`.
    *   **Agent Tools:** Document analysis tools, SEO analysis tools (wrapping existing APIs or via web search), AI writing assistant tools, grammar/style checkers.

    ### C. Pydantic-AI Mapping (Conceptual):
    *   **Relevant Pydantic Models:** `ContentInputBrief`, `PageContentModel`, `SEOKeywordSet`, `ContentStrategyOutput`.
    *   **Core Logic Function Signature:** `async def generate_website_content(brief: ContentInputBrief, design_spec_url: str) -> Tuple[ContentStrategyOutput, PageContentModel]:`

    ### D. PocketFlow Mapping (Conceptual):
    *   **Flow Definition:** States like `StrategyDev`, `KeywordResearch`, `CopyDrafting_[PageName]`, `SEOReview`, `ClientReviewLoop`, `Finalization`.
    *   **Transitions:** Based on task completion, review outcomes, external triggers (client feedback).

    ### E. Aider Integration Points:
    *   `/edit Draft an 'About Us' page copy based on these points: [history, mission, team values] targeting [audience type].`
    *   `/ask Suggest 5 SEO keywords for a page about 'sustainable home energy solutions'.`
    *   `/edit Rewrite this paragraph to be more persuasive: [paragraph text].`
    *   `/ask What are common CTAs for a product landing page?`

    ### F. Multi-Framework Agent Training Strategy (ESTRATIX Guiding Principle):
    *   "Content Agents" trained on brand voice guidelines, SEO best practices, persuasive writing techniques, and analysis of high-performing content. Training data to include successful copy decks, style guides, and keyword research reports.

## 13. Exception Handling & Error Conditions

*   **Insufficient Input from `PLN001` or `DES001`:** Halt process, request clarification/completion from respective process owners.
*   **Difficulty Defining Voice/Tone:** Conduct dedicated branding workshops or refer back to client for more specific guidance.
*   **Poor Keyword Performance/High Competition:** Re-evaluate keyword strategy, explore long-tail keywords, adjust content angles.
*   **Client Disagreement on Copy/Strategy:** Facilitate detailed feedback sessions, provide rationale for choices, iterate. Manage scope if revisions are extensive.
*   **Writer's Block / Lack of Inspiration:** Utilize brainstorming techniques, refer to competitor content (for ideas, not copying), use AI writing assistants for ideation.

## 14. PDCA (Plan-Do-Check-Act) & Continuous Improvement

*   **Review Cycle:** After every major content project, or quarterly.
*   **Improvement Log:**
    *   **Date:** `2025-05-11` | **Issue/Observation:** `Initial version.` | **Proposed Change:** `N/A` | **Action Taken:** `N/A` | **Result:** `N/A`
    *   Track content performance post-launch (engagement metrics, conversion rates, SEO rankings) via `MON001` and feed insights back into this process.
    *   Evaluate the effectiveness of different content formats, CTAs, and SEO strategies.
    *   Refine content templates, checklists, and style guides based on lessons learned.

## 15. Diagram Reference

*   **Mermaid Diagram:** `[docs/processes/diagrams/CON001_DevelopWebsiteContentStrategy&InitialCopy.mmd](./../diagrams/CON001_DevelopWebsiteContentStrategy&InitialCopy.mmd)` (To be created)

## 16. Notes & Assumptions

*   Assumes client is responsive and provides timely feedback on content drafts.
*   The scope of "initial copy" (number of pages) should be clearly defined in `PLN001` or at the start of this process.
*   Effective content often requires close collaboration between content creators, designers, and SEO specialists.
