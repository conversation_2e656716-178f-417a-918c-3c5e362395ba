import React, { useState, useEffect } from 'react';
import { TrendingUp, TrendingDown, Coins, Zap, Lock, Unlock, Plus, Minus, BarChart3, <PERSON><PERSON><PERSON>, DollarSign, Percent, Clock, Users, Target, Award } from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON> as RechartsPieChart, Cell, BarChart, Bar } from 'recharts';

interface StakingPool {
  id: string;
  name: string;
  token: string;
  apy: number;
  totalStaked: number;
  userStaked: number;
  lockPeriod: number;
  minStake: number;
  rewards: number;
  status: 'active' | 'ended' | 'upcoming';
}

interface LiquidityPool {
  id: string;
  pair: string;
  token1: string;
  token2: string;
  apy: number;
  tvl: number;
  userLiquidity: number;
  fees24h: number;
  volume24h: number;
}

interface YieldFarm {
  id: string;
  name: string;
  pair: string;
  multiplier: number;
  apy: number;
  totalDeposited: number;
  userDeposited: number;
  pendingRewards: number;
  endBlock: number;
}

const DeFiHub: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'overview' | 'staking' | 'liquidity' | 'farming'>('overview');
  const [selectedPool, setSelectedPool] = useState<string | null>(null);
  const [stakeAmount, setStakeAmount] = useState('');
  const [unstakeAmount, setUnstakeAmount] = useState('');

  // Mock data
  const stakingPools: StakingPool[] = [
    {
      id: '1',
      name: 'ESTELAR Staking',
      token: 'ESTELAR',
      apy: 24.5,
      totalStaked: 1250000,
      userStaked: 5000,
      lockPeriod: 30,
      minStake: 100,
      rewards: 125.50,
      status: 'active'
    },
    {
      id: '2',
      name: 'LOTTERY Token Pool',
      token: 'LOTTERY',
      apy: 18.2,
      totalStaked: 850000,
      userStaked: 2500,
      lockPeriod: 14,
      minStake: 50,
      rewards: 89.25,
      status: 'active'
    },
    {
      id: '3',
      name: 'Premium Staking',
      token: 'ESTELAR',
      apy: 35.8,
      totalStaked: 500000,
      userStaked: 0,
      lockPeriod: 90,
      minStake: 1000,
      rewards: 0,
      status: 'active'
    }
  ];

  const liquidityPools: LiquidityPool[] = [
    {
      id: '1',
      pair: 'ESTELAR/USDC',
      token1: 'ESTELAR',
      token2: 'USDC',
      apy: 42.3,
      tvl: 2500000,
      userLiquidity: 1250,
      fees24h: 1250,
      volume24h: 125000
    },
    {
      id: '2',
      pair: 'LOTTERY/ETH',
      token1: 'LOTTERY',
      token2: 'ETH',
      apy: 28.7,
      tvl: 1800000,
      userLiquidity: 850,
      fees24h: 890,
      volume24h: 89000
    }
  ];

  const yieldFarms: YieldFarm[] = [
    {
      id: '1',
      name: 'ESTELAR-USDC LP',
      pair: 'ESTELAR/USDC',
      multiplier: 2.5,
      apy: 65.4,
      totalDeposited: 750000,
      userDeposited: 1500,
      pendingRewards: 45.25,
      endBlock: 15000000
    },
    {
      id: '2',
      name: 'LOTTERY-ETH LP',
      pair: 'LOTTERY/ETH',
      multiplier: 1.8,
      apy: 48.9,
      totalDeposited: 450000,
      userDeposited: 800,
      pendingRewards: 28.75,
      endBlock: 14500000
    }
  ];

  // Chart data
  const stakingChartData = [
    { name: 'Ene', staked: 800000, rewards: 15000 },
    { name: 'Feb', staked: 950000, rewards: 18500 },
    { name: 'Mar', staked: 1100000, rewards: 22000 },
    { name: 'Abr', staked: 1250000, rewards: 25500 },
    { name: 'May', staked: 1400000, rewards: 29000 },
    { name: 'Jun', staked: 1600000, rewards: 33500 }
  ];

  const portfolioData = [
    { name: 'Staking', value: 7500, color: '#8B5CF6' },
    { name: 'Liquidity', value: 2100, color: '#F59E0B' },
    { name: 'Farming', value: 2300, color: '#10B981' },
    { name: 'Rewards', value: 1200, color: '#EF4444' }
  ];

  const totalPortfolioValue = portfolioData.reduce((sum, item) => sum + item.value, 0);

  const OverviewTab = () => (
    <div className="space-y-8">
      {/* Portfolio Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card variant="glass" className="lg:col-span-2">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-white">Portfolio Overview</h3>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-green-400 text-sm">Live</span>
            </div>
          </div>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div className="text-center">
              <p className="text-white/60 text-sm">Total Value</p>
              <p className="text-2xl font-bold text-white">${totalPortfolioValue.toLocaleString()}</p>
              <p className="text-green-400 text-sm flex items-center justify-center">
                <TrendingUp className="w-3 h-3 mr-1" />
                +12.5%
              </p>
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm">Staked</p>
              <p className="text-xl font-bold text-purple-400">${portfolioData[0].value.toLocaleString()}</p>
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm">Liquidity</p>
              <p className="text-xl font-bold text-amber-400">${portfolioData[1].value.toLocaleString()}</p>
            </div>
            <div className="text-center">
              <p className="text-white/60 text-sm">Rewards</p>
              <p className="text-xl font-bold text-green-400">${portfolioData[3].value.toLocaleString()}</p>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={200}>
            <AreaChart data={stakingChartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
              <XAxis dataKey="name" stroke="rgba(255,255,255,0.6)" />
              <YAxis stroke="rgba(255,255,255,0.6)" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(0,0,0,0.8)', 
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px'
                }}
              />
              <Area 
                type="monotone" 
                dataKey="staked" 
                stroke="#8B5CF6" 
                fill="url(#stakingGradient)" 
                strokeWidth={2}
              />
              <defs>
                <linearGradient id="stakingGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0}/>
                </linearGradient>
              </defs>
            </AreaChart>
          </ResponsiveContainer>
        </Card>
        
        <Card variant="glass">
          <h3 className="text-xl font-bold text-white mb-6">Asset Distribution</h3>
          <ResponsiveContainer width="100%" height={200}>
            <RechartsPieChart>
              <Pie
                data={portfolioData}
                cx="50%"
                cy="50%"
                innerRadius={40}
                outerRadius={80}
                paddingAngle={5}
                dataKey="value"
              >
                {portfolioData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </RechartsPieChart>
          </ResponsiveContainer>
          <div className="space-y-2 mt-4">
            {portfolioData.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full mr-2" style={{ backgroundColor: item.color }}></div>
                  <span className="text-white/80 text-sm">{item.name}</span>
                </div>
                <span className="text-white font-medium">${item.value.toLocaleString()}</span>
              </div>
            ))}
          </div>
        </Card>
      </div>
      
      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card variant="glass" className="text-center">
          <div className="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-purple-400" />
          </div>
          <h3 className="text-lg font-bold text-white mb-2">Stake Tokens</h3>
          <p className="text-white/70 text-sm mb-4">Earn rewards by staking your ESTELAR tokens</p>
          <Button onClick={() => setActiveTab('staking')} className="w-full">
            Start Staking
          </Button>
        </Card>
        
        <Card variant="glass" className="text-center">
          <div className="w-16 h-16 bg-amber-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <Coins className="w-8 h-8 text-amber-400" />
          </div>
          <h3 className="text-lg font-bold text-white mb-2">Provide Liquidity</h3>
          <p className="text-white/70 text-sm mb-4">Earn fees by providing liquidity to pools</p>
          <Button onClick={() => setActiveTab('liquidity')} className="w-full">
            Add Liquidity
          </Button>
        </Card>
        
        <Card variant="glass" className="text-center">
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <TrendingUp className="w-8 h-8 text-green-400" />
          </div>
          <h3 className="text-lg font-bold text-white mb-2">Yield Farming</h3>
          <p className="text-white/70 text-sm mb-4">Maximize returns with yield farming</p>
          <Button onClick={() => setActiveTab('farming')} className="w-full">
            Start Farming
          </Button>
        </Card>
      </div>
    </div>
  );

  const StakingTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {stakingPools.map((pool) => (
          <Card key={pool.id} variant="glass" className="hover:scale-105 transition-transform">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-amber-500 rounded-full flex items-center justify-center mr-3">
                  <Coins className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">{pool.name}</h3>
                  <p className="text-white/60 text-sm">{pool.token}</p>
                </div>
              </div>
              <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                pool.status === 'active' ? 'bg-green-500/20 text-green-400' :
                pool.status === 'ended' ? 'bg-red-500/20 text-red-400' :
                'bg-amber-500/20 text-amber-400'
              }`}>
                {pool.status === 'active' ? 'Activo' : pool.status === 'ended' ? 'Finalizado' : 'Próximamente'}
              </div>
            </div>
            
            <div className="space-y-3 mb-6">
              <div className="flex justify-between">
                <span className="text-white/70">APY</span>
                <span className="text-green-400 font-bold">{pool.apy}%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Total Staked</span>
                <span className="text-white">{pool.totalStaked.toLocaleString()} {pool.token}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Your Stake</span>
                <span className="text-white">{pool.userStaked.toLocaleString()} {pool.token}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Pending Rewards</span>
                <span className="text-amber-400 font-bold">{pool.rewards} {pool.token}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/70">Lock Period</span>
                <span className="text-white">{pool.lockPeriod} días</span>
              </div>
            </div>
            
            <div className="space-y-2">
              {pool.userStaked > 0 && (
                <Button variant="outline" size="sm" className="w-full border-amber-500 text-amber-400 hover:bg-amber-500/10">
                  <Award className="w-4 h-4 mr-2" />
                  Claim Rewards
                </Button>
              )}
              <div className="flex space-x-2">
                <Button size="sm" className="flex-1">
                  <Plus className="w-4 h-4 mr-1" />
                  Stake
                </Button>
                {pool.userStaked > 0 && (
                  <Button variant="outline" size="sm" className="flex-1 border-white/30 text-white hover:bg-white/10">
                    <Minus className="w-4 h-4 mr-1" />
                    Unstake
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const LiquidityTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {liquidityPools.map((pool) => (
          <Card key={pool.id} variant="glass">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="flex -space-x-2 mr-3">
                  <div className="w-8 h-8 bg-purple-500 rounded-full border-2 border-white flex items-center justify-center">
                    <span className="text-white text-xs font-bold">{pool.token1[0]}</span>
                  </div>
                  <div className="w-8 h-8 bg-amber-500 rounded-full border-2 border-white flex items-center justify-center">
                    <span className="text-white text-xs font-bold">{pool.token2[0]}</span>
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">{pool.pair}</h3>
                  <p className="text-white/60 text-sm">Liquidity Pool</p>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-white/70 text-sm">APY</p>
                <p className="text-green-400 font-bold text-xl">{pool.apy}%</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">TVL</p>
                <p className="text-white font-bold text-xl">${pool.tvl.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">24h Volume</p>
                <p className="text-white">${pool.volume24h.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">24h Fees</p>
                <p className="text-amber-400">${pool.fees24h.toLocaleString()}</p>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-white/70">Your Liquidity</span>
                <span className="text-white">${pool.userLiquidity.toLocaleString()}</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-amber-500 h-2 rounded-full"
                  style={{ width: `${(pool.userLiquidity / pool.tvl) * 100}%` }}
                ></div>
              </div>
            </div>
            
            <div className="flex space-x-2">
              <Button size="sm" className="flex-1">
                <Plus className="w-4 h-4 mr-1" />
                Add Liquidity
              </Button>
              {pool.userLiquidity > 0 && (
                <Button variant="outline" size="sm" className="flex-1 border-white/30 text-white hover:bg-white/10">
                  <Minus className="w-4 h-4 mr-1" />
                  Remove
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  const FarmingTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {yieldFarms.map((farm) => (
          <Card key={farm.id} variant="glass">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-bold text-white">{farm.name}</h3>
                <p className="text-white/60 text-sm">{farm.pair}</p>
              </div>
              <div className="text-right">
                <p className="text-amber-400 font-bold">{farm.multiplier}x</p>
                <p className="text-white/60 text-sm">Multiplier</p>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div>
                <p className="text-white/70 text-sm">APY</p>
                <p className="text-green-400 font-bold text-xl">{farm.apy}%</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">Total Deposited</p>
                <p className="text-white font-bold">${farm.totalDeposited.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">Your Deposit</p>
                <p className="text-white">${farm.userDeposited.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-white/70 text-sm">Pending Rewards</p>
                <p className="text-amber-400 font-bold">{farm.pendingRewards} ESTELAR</p>
              </div>
            </div>
            
            <div className="mb-6">
              <div className="flex justify-between mb-2">
                <span className="text-white/70">Farm Progress</span>
                <span className="text-white">Block {farm.endBlock.toLocaleString()}</span>
              </div>
              <div className="w-full bg-white/20 rounded-full h-2">
                <div className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full" style={{ width: '65%' }}></div>
              </div>
            </div>
            
            <div className="space-y-2">
              {farm.pendingRewards > 0 && (
                <Button variant="outline" size="sm" className="w-full border-amber-500 text-amber-400 hover:bg-amber-500/10">
                  <Award className="w-4 h-4 mr-2" />
                  Harvest ({farm.pendingRewards} ESTELAR)
                </Button>
              )}
              <div className="flex space-x-2">
                <Button size="sm" className="flex-1">
                  <Plus className="w-4 h-4 mr-1" />
                  Deposit
                </Button>
                {farm.userDeposited > 0 && (
                  <Button variant="outline" size="sm" className="flex-1 border-white/30 text-white hover:bg-white/10">
                    <Minus className="w-4 h-4 mr-1" />
                    Withdraw
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen pt-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            DeFi <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Hub</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Maximiza tus ganancias con staking, liquidity pools y yield farming en el ecosistema Sorteo Estelar.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap justify-center mb-8">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-1 flex">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'staking', label: 'Staking', icon: Lock },
              { id: 'liquidity', label: 'Liquidity', icon: Coins },
              { id: 'farming', label: 'Farming', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-6 py-3 rounded-lg font-medium transition-all flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-purple-500 to-amber-500 text-white'
                      : 'text-white/70 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="mb-12">
          {activeTab === 'overview' && <OverviewTab />}
          {activeTab === 'staking' && <StakingTab />}
          {activeTab === 'liquidity' && <LiquidityTab />}
          {activeTab === 'farming' && <FarmingTab />}
        </div>
      </div>
    </div>
  );
};

export default DeFiHub;