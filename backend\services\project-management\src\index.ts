import Fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import multipart from '@fastify/multipart';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { config } from './config/environment';
import { logger } from './utils/logger';
import { authenticateToken } from './middleware/auth';
import { requestLogger, responseLogger } from './middleware/logging';

// Route imports
import healthRoutes from './routes/health';
import projectRoutes from './routes/projects';
import taskRoutes from './routes/tasks';
import teamRoutes from './routes/teams';
import resourceRoutes from './routes/resources';
import reportRoutes from './routes/reports';

// Service imports
import { projectService } from './services/projectService';
import { notificationService } from './services/notificationService';

const fastify = Fastify({
  logger: false // We use our custom logger
});

async function start() {
  try {
    // Register plugins
    await fastify.register(cors, {
      origin: config.CORS_ORIGINS,
      credentials: true
    });
    
    await fastify.register(helmet, {
      contentSecurityPolicy: false
    });
    
    await fastify.register(jwt, {
      secret: config.JWT_SECRET
    });
    
    await fastify.register(multipart, {
      limits: {
        fileSize: config.MAX_FILE_SIZE
      }
    });
    
    // Swagger documentation
    await fastify.register(swagger, {
      openapi: {
        openapi: '3.0.0',
        info: {
          title: 'ESTRATIX Project Management Service API',
          description: 'Project Management microservice for ESTRATIX platform',
          version: '1.0.0'
        },
        servers: [
          {
            url: `http://${config.HOST}:${config.PORT}`,
            description: 'Development server'
          }
        ],
        components: {
          securitySchemes: {
            bearerAuth: {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            }
          }
        }
      }
    });
    
    await fastify.register(swaggerUi, {
      routePrefix: '/docs',
      uiConfig: {
        docExpansion: 'full',
        deepLinking: false
      }
    });
    
    // Register middleware
    await fastify.register(requestLogger);
    await fastify.register(responseLogger);
    
    // Register routes
    await fastify.register(healthRoutes, { prefix: '/api/v1/health' });
    await fastify.register(projectRoutes, { prefix: '/api/v1/projects' });
    await fastify.register(taskRoutes, { prefix: '/api/v1/tasks' });
    await fastify.register(teamRoutes, { prefix: '/api/v1/teams' });
    await fastify.register(resourceRoutes, { prefix: '/api/v1/resources' });
    await fastify.register(reportRoutes, { prefix: '/api/v1/reports' });
    
    // Initialize services
    logger.info('Initializing Project Management services...');
    await projectService.initialize();
    await notificationService.initialize();
    logger.info('Project Management services initialized successfully');
    
    // Start server
    await fastify.listen({
      port: config.PORT,
      host: config.HOST
    });
    
    logger.info(`🚀 Project Management service started on ${config.HOST}:${config.PORT}`);
    logger.info(`📚 API Documentation available at http://${config.HOST}:${config.PORT}/docs`);
    
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  logger.info('Received SIGINT, shutting down gracefully...');
  try {
    await projectService.cleanup();
    await notificationService.cleanup();
    await fastify.close();
    logger.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  logger.info('Received SIGTERM, shutting down gracefully...');
  try {
    await projectService.cleanup();
    await notificationService.cleanup();
    await fastify.close();
    logger.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
});

start();