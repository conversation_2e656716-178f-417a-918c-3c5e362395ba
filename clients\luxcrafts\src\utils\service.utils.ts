/**
 * Service utility functions for common patterns across services
 */

/**
 * Mock delay utility for development mode
 * @param ms - Delay in milliseconds
 * @returns Promise that resolves after the specified delay
 */
export const mockDelay = (ms: number = 500): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Build URLSearchParams from an object, handling arrays and undefined values
 * @param params - Object with parameters
 * @returns URLSearchParams instance
 */
export const buildSearchParams = (params: Record<string, any>): URLSearchParams => {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      if (Array.isArray(value)) {
        searchParams.append(key, value.join(','));
      } else if (value instanceof Date) {
        searchParams.append(key, value.toISOString());
      } else {
        searchParams.append(key, value.toString());
      }
    }
  });
  
  return searchParams;
};

/**
 * Generic filter function for arrays based on multiple criteria
 * @param items - Array to filter
 * @param filters - Filter criteria
 * @returns Filtered array
 */
export const applyFilters = <T>(items: T[], filters: Record<string, any>): T[] => {
  return items.filter(item => {
    return Object.entries(filters).every(([key, value]) => {
      if (value === undefined || value === null) return true;
      
      const itemValue = (item as any)[key];
      
      if (Array.isArray(value)) {
        return Array.isArray(itemValue) 
          ? value.some(v => itemValue.includes(v))
          : value.includes(itemValue);
      }
      
      if (typeof value === 'string' && typeof itemValue === 'string') {
        return itemValue.toLowerCase().includes(value.toLowerCase());
      }
      
      return itemValue === value;
    });
  });
};

/**
 * Paginate an array
 * @param items - Array to paginate
 * @param page - Page number (1-based)
 * @param limit - Items per page
 * @returns Paginated result with items and total
 */
export const paginate = <T>(items: T[], page: number = 1, limit: number = 10): { items: T[]; total: number } => {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  
  return {
    items: items.slice(startIndex, endIndex),
    total: items.length
  };
};

/**
 * Sort array by multiple criteria
 * @param items - Array to sort
 * @param sortBy - Field to sort by
 * @param sortOrder - Sort order (asc/desc)
 * @returns Sorted array
 */
export const sortItems = <T>(items: T[], sortBy?: string, sortOrder: 'asc' | 'desc' = 'asc'): T[] => {
  if (!sortBy) return items;
  
  return [...items].sort((a, b) => {
    const aValue = (a as any)[sortBy];
    const bValue = (b as any)[sortBy];
    
    let comparison = 0;
    
    if (aValue instanceof Date && bValue instanceof Date) {
      comparison = aValue.getTime() - bValue.getTime();
    } else if (typeof aValue === 'number' && typeof bValue === 'number') {
      comparison = aValue - bValue;
    } else {
      comparison = String(aValue).localeCompare(String(bValue));
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
};

/**
 * Generate random number within range
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns Random number
 */
export const randomBetween = (min: number, max: number): number => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * Generate mock ID
 * @param prefix - ID prefix
 * @returns Generated ID
 */
export const generateMockId = (prefix: string = 'id'): string => {
  return `${prefix}-${Date.now()}-${randomBetween(1000, 9999)}`;
};

/**
 * Base service class with common functionality
 */
export abstract class BaseService {
  protected readonly isDevelopment = import.meta.env.DEV;
  
  /**
   * Execute with mock delay in development mode
   * @param fn - Function to execute
   * @param delay - Mock delay in milliseconds
   * @returns Promise with result
   */
  protected async withMockDelay<T>(fn: () => T | Promise<T>, delay: number = 500): Promise<T> {
    if (this.isDevelopment) {
      await mockDelay(delay);
    }
    return await fn();
  }
  
  /**
   * Handle API call with fallback to mock data
   * @param apiCall - API call function
   * @param mockData - Mock data function
   * @param delay - Mock delay in milliseconds
   * @returns Promise with result
   */
  protected async handleApiCall<T>(
    apiCall: () => Promise<T>,
    mockData: () => T | Promise<T>,
    delay: number = 500
  ): Promise<T> {
    if (this.isDevelopment) {
      return this.withMockDelay(mockData, delay);
    }
    return await apiCall();
  }
}

/**
 * Error handling utilities
 */
export class ServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500
  ) {
    super(message);
    this.name = 'ServiceError';
  }
}

export const handleServiceError = (error: any): never => {
  if (error instanceof ServiceError) {
    throw error;
  }
  
  if (error.response) {
    throw new ServiceError(
      error.response.data?.message || 'API Error',
      error.response.data?.code || 'API_ERROR',
      error.response.status
    );
  }
  
  throw new ServiceError(
    error.message || 'Unknown error',
    'UNKNOWN_ERROR'
  );
};