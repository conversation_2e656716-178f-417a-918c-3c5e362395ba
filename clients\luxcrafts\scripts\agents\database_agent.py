#!/usr/bin/env python3
"""
Database Management Agent

This agent handles database setup, migrations, data seeding, backup/restore,
and database optimization for the Luxcrafts platform.
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import psycopg2
import pymongo
import redis
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import boto3
from fabric import Connection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    postgres_url: str
    mongodb_url: str
    redis_url: str
    environment: str
    backup_enabled: bool = True
    backup_retention_days: int = 30
    s3_backup_bucket: Optional[str] = None
    migration_path: str = "./migrations"
    seed_data_path: str = "./seeds"

class DatabaseAgent:
    """AI-powered database management agent"""
    
    def __init__(self, config: DatabaseConfig):
        self.config = config
        self.postgres_engine = None
        self.mongodb_client = None
        self.redis_client = None
        self.operation_log: List[Dict[str, Any]] = []
        
    def log_operation(self, operation: str, status: str, details: str = "", duration: float = 0):
        """Log database operations for audit and debugging"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "operation": operation,
            "status": status,
            "details": details,
            "duration": duration
        }
        self.operation_log.append(log_entry)
        logger.info(f"{operation}: {status} - {details}")
    
    def connect_databases(self) -> bool:
        """Establish connections to all databases"""
        try:
            start_time = time.time()
            
            # Connect to PostgreSQL
            self.postgres_engine = create_engine(self.config.postgres_url)
            with self.postgres_engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("PostgreSQL connection established")
            
            # Connect to MongoDB
            self.mongodb_client = pymongo.MongoClient(self.config.mongodb_url)
            self.mongodb_client.admin.command('ping')
            logger.info("MongoDB connection established")
            
            # Connect to Redis
            self.redis_client = redis.from_url(self.config.redis_url)
            self.redis_client.ping()
            logger.info("Redis connection established")
            
            duration = time.time() - start_time
            self.log_operation(
                "Database Connections", 
                "SUCCESS", 
                "All database connections established",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "Database Connections", 
                "FAILED", 
                f"Failed to connect to databases: {str(e)}",
                duration
            )
            return False
    
    def setup_postgres_schema(self) -> bool:
        """Setup PostgreSQL schema and tables"""
        try:
            start_time = time.time()
            
            # Create database schema
            schema_sql = """
            -- Users and Authentication
            CREATE TABLE IF NOT EXISTS users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) UNIQUE NOT NULL,
                password_hash VARCHAR(255),
                wallet_address VARCHAR(42),
                role VARCHAR(50) DEFAULT 'user',
                profile JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Properties
            CREATE TABLE IF NOT EXISTS properties (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                owner_id UUID REFERENCES users(id),
                address TEXT NOT NULL,
                property_type VARCHAR(100),
                bedrooms INTEGER,
                bathrooms INTEGER,
                square_feet INTEGER,
                lot_size DECIMAL,
                year_built INTEGER,
                listing_price DECIMAL(15,2),
                market_value DECIMAL(15,2),
                rental_price DECIMAL(10,2),
                property_data JSONB,
                location POINT,
                status VARCHAR(50) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Property Services
            CREATE TABLE IF NOT EXISTS property_services (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                property_id UUID REFERENCES properties(id),
                service_type VARCHAR(100) NOT NULL,
                provider_id UUID REFERENCES users(id),
                service_date TIMESTAMP,
                cost DECIMAL(10,2),
                status VARCHAR(50) DEFAULT 'pending',
                service_details JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Transactions
            CREATE TABLE IF NOT EXISTS transactions (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id),
                property_id UUID REFERENCES properties(id),
                transaction_type VARCHAR(50),
                amount DECIMAL(15,2),
                currency VARCHAR(10),
                blockchain_tx_hash VARCHAR(66),
                status VARCHAR(50) DEFAULT 'pending',
                transaction_data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- LUX Token Operations
            CREATE TABLE IF NOT EXISTS token_operations (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id),
                operation_type VARCHAR(50),
                amount DECIMAL(18,8),
                token_price DECIMAL(15,8),
                blockchain_tx_hash VARCHAR(66),
                status VARCHAR(50) DEFAULT 'pending',
                operation_data JSONB,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Staking Pools
            CREATE TABLE IF NOT EXISTS staking_pools (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id),
                pool_type VARCHAR(50),
                staked_amount DECIMAL(18,8),
                lock_period INTEGER,
                apy DECIMAL(5,2),
                start_date TIMESTAMP,
                end_date TIMESTAMP,
                rewards_earned DECIMAL(18,8),
                status VARCHAR(50) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- NFT Collections
            CREATE TABLE IF NOT EXISTS nft_collections (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                property_id UUID REFERENCES properties(id),
                owner_id UUID REFERENCES users(id),
                token_id VARCHAR(100),
                contract_address VARCHAR(42),
                metadata_uri TEXT,
                collection_type VARCHAR(50),
                mint_price DECIMAL(15,8),
                current_price DECIMAL(15,8),
                status VARCHAR(50) DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Content Generation
            CREATE TABLE IF NOT EXISTS content_generations (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                user_id UUID REFERENCES users(id),
                content_type VARCHAR(50),
                prompt TEXT,
                generated_content JSONB,
                generation_params JSONB,
                cost DECIMAL(10,4),
                status VARCHAR(50) DEFAULT 'completed',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Property Acquisition Pipeline
            CREATE TABLE IF NOT EXISTS acquisition_pipeline (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                property_address TEXT NOT NULL,
                owner_info JSONB,
                market_analysis JSONB,
                acquisition_strategy JSONB,
                negotiation_status VARCHAR(50) DEFAULT 'identified',
                ai_agent_interactions JSONB,
                profit_projection DECIMAL(15,2),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
            
            -- Create indexes for performance
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
            CREATE INDEX IF NOT EXISTS idx_users_wallet ON users(wallet_address);
            CREATE INDEX IF NOT EXISTS idx_properties_owner ON properties(owner_id);
            CREATE INDEX IF NOT EXISTS idx_properties_location ON properties USING GIST(location);
            CREATE INDEX IF NOT EXISTS idx_transactions_user ON transactions(user_id);
            CREATE INDEX IF NOT EXISTS idx_transactions_property ON transactions(property_id);
            CREATE INDEX IF NOT EXISTS idx_token_operations_user ON token_operations(user_id);
            CREATE INDEX IF NOT EXISTS idx_staking_pools_user ON staking_pools(user_id);
            CREATE INDEX IF NOT EXISTS idx_nft_collections_owner ON nft_collections(owner_id);
            CREATE INDEX IF NOT EXISTS idx_content_generations_user ON content_generations(user_id);
            
            -- Create triggers for updated_at
            CREATE OR REPLACE FUNCTION update_updated_at_column()
            RETURNS TRIGGER AS $$
            BEGIN
                NEW.updated_at = CURRENT_TIMESTAMP;
                RETURN NEW;
            END;
            $$ language 'plpgsql';
            
            CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            CREATE TRIGGER update_properties_updated_at BEFORE UPDATE ON properties
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            CREATE TRIGGER update_acquisition_pipeline_updated_at BEFORE UPDATE ON acquisition_pipeline
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """
            
            with self.postgres_engine.connect() as conn:
                conn.execute(text(schema_sql))
                conn.commit()
            
            duration = time.time() - start_time
            self.log_operation(
                "PostgreSQL Schema Setup", 
                "SUCCESS", 
                "Database schema created successfully",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "PostgreSQL Schema Setup", 
                "FAILED", 
                f"Failed to setup schema: {str(e)}",
                duration
            )
            return False
    
    def setup_mongodb_collections(self) -> bool:
        """Setup MongoDB collections and indexes"""
        try:
            start_time = time.time()
            
            db = self.mongodb_client.luxcrafts
            
            # Create collections with validation schemas
            collections_config = {
                "property_analytics": {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["property_id", "analytics_type", "data", "timestamp"],
                            "properties": {
                                "property_id": {"bsonType": "string"},
                                "analytics_type": {"bsonType": "string"},
                                "data": {"bsonType": "object"},
                                "timestamp": {"bsonType": "date"}
                            }
                        }
                    }
                },
                "market_data": {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["location", "market_metrics", "timestamp"],
                            "properties": {
                                "location": {"bsonType": "object"},
                                "market_metrics": {"bsonType": "object"},
                                "timestamp": {"bsonType": "date"}
                            }
                        }
                    }
                },
                "ai_interactions": {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["user_id", "interaction_type", "data", "timestamp"],
                            "properties": {
                                "user_id": {"bsonType": "string"},
                                "interaction_type": {"bsonType": "string"},
                                "data": {"bsonType": "object"},
                                "timestamp": {"bsonType": "date"}
                            }
                        }
                    }
                },
                "content_library": {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["content_id", "content_type", "metadata", "created_at"],
                            "properties": {
                                "content_id": {"bsonType": "string"},
                                "content_type": {"bsonType": "string"},
                                "metadata": {"bsonType": "object"},
                                "created_at": {"bsonType": "date"}
                            }
                        }
                    }
                },
                "blockchain_events": {
                    "validator": {
                        "$jsonSchema": {
                            "bsonType": "object",
                            "required": ["event_type", "blockchain", "transaction_hash", "timestamp"],
                            "properties": {
                                "event_type": {"bsonType": "string"},
                                "blockchain": {"bsonType": "string"},
                                "transaction_hash": {"bsonType": "string"},
                                "timestamp": {"bsonType": "date"}
                            }
                        }
                    }
                }
            }
            
            # Create collections
            for collection_name, config in collections_config.items():
                if collection_name not in db.list_collection_names():
                    db.create_collection(collection_name, **config)
                    logger.info(f"Created collection: {collection_name}")
            
            # Create indexes
            indexes_config = {
                "property_analytics": [
                    ("property_id", 1),
                    ("analytics_type", 1),
                    ("timestamp", -1),
                    ([("property_id", 1), ("analytics_type", 1), ("timestamp", -1)], {})
                ],
                "market_data": [
                    ("location.coordinates", "2dsphere"),
                    ("timestamp", -1),
                    ("market_metrics.price_per_sqft", 1)
                ],
                "ai_interactions": [
                    ("user_id", 1),
                    ("interaction_type", 1),
                    ("timestamp", -1)
                ],
                "content_library": [
                    ("content_type", 1),
                    ("created_at", -1),
                    ("metadata.tags", 1)
                ],
                "blockchain_events": [
                    ("transaction_hash", 1),
                    ("event_type", 1),
                    ("timestamp", -1)
                ]
            }
            
            for collection_name, indexes in indexes_config.items():
                collection = db[collection_name]
                for index in indexes:
                    if isinstance(index, tuple) and len(index) == 2:
                        field, options = index
                        if isinstance(field, list):
                            collection.create_index(field, **options)
                        else:
                            collection.create_index(field)
                    else:
                        collection.create_index(index)
                logger.info(f"Created indexes for collection: {collection_name}")
            
            duration = time.time() - start_time
            self.log_operation(
                "MongoDB Collections Setup", 
                "SUCCESS", 
                "MongoDB collections and indexes created",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "MongoDB Collections Setup", 
                "FAILED", 
                f"Failed to setup MongoDB collections: {str(e)}",
                duration
            )
            return False
    
    def setup_redis_cache(self) -> bool:
        """Setup Redis cache structure and configurations"""
        try:
            start_time = time.time()
            
            # Set up cache configurations
            cache_configs = {
                "user_sessions": {"ttl": 86400},  # 24 hours
                "property_cache": {"ttl": 3600},   # 1 hour
                "market_data": {"ttl": 1800},      # 30 minutes
                "token_prices": {"ttl": 300},      # 5 minutes
                "api_rate_limits": {"ttl": 3600},  # 1 hour
                "content_generation": {"ttl": 7200} # 2 hours
            }
            
            # Set cache configurations in Redis
            for cache_type, config in cache_configs.items():
                self.redis_client.hset(f"cache_config:{cache_type}", mapping=config)
            
            # Initialize rate limiting structures
            rate_limits = {
                "api_calls": {"limit": 1000, "window": 3600},
                "content_generation": {"limit": 100, "window": 3600},
                "ai_interactions": {"limit": 500, "window": 3600}
            }
            
            for limit_type, config in rate_limits.items():
                self.redis_client.hset(f"rate_limit:{limit_type}", mapping=config)
            
            # Set up pub/sub channels for real-time updates
            pubsub_channels = [
                "property_updates",
                "token_price_updates",
                "transaction_notifications",
                "ai_agent_updates",
                "content_generation_status"
            ]
            
            for channel in pubsub_channels:
                self.redis_client.set(f"channel:{channel}:active", "true")
            
            # Initialize system metrics
            system_metrics = {
                "total_users": 0,
                "total_properties": 0,
                "total_transactions": 0,
                "total_token_operations": 0,
                "active_staking_pools": 0
            }
            
            self.redis_client.hset("system_metrics", mapping=system_metrics)
            
            duration = time.time() - start_time
            self.log_operation(
                "Redis Cache Setup", 
                "SUCCESS", 
                "Redis cache structure initialized",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "Redis Cache Setup", 
                "FAILED", 
                f"Failed to setup Redis cache: {str(e)}",
                duration
            )
            return False
    
    def seed_initial_data(self) -> bool:
        """Seed initial data for development and testing"""
        try:
            start_time = time.time()
            
            # Seed PostgreSQL data
            seed_sql = """
            -- Insert admin user
            INSERT INTO users (email, role, profile) VALUES 
            ('<EMAIL>', 'admin', '{"name": "Admin User", "phone": "+1234567890"}') 
            ON CONFLICT (email) DO NOTHING;
            
            -- Insert sample properties
            INSERT INTO properties (address, property_type, bedrooms, bathrooms, square_feet, listing_price, market_value, rental_price, property_data) VALUES 
            ('123 Luxury Ave, Beverly Hills, CA 90210', 'luxury_home', 5, 4, 3500, 2500000.00, 2400000.00, 8500.00, '{"amenities": ["pool", "garage", "garden"], "year_built": 2018}'),
            ('456 Elite St, Manhattan, NY 10001', 'penthouse', 3, 3, 2200, 1800000.00, 1750000.00, 6500.00, '{"amenities": ["balcony", "gym", "concierge"], "year_built": 2020}'),
            ('789 Premium Blvd, Miami, FL 33101', 'condo', 2, 2, 1500, 850000.00, 820000.00, 4200.00, '{"amenities": ["beach_access", "pool", "spa"], "year_built": 2019}')
            ON CONFLICT DO NOTHING;
            """
            
            with self.postgres_engine.connect() as conn:
                conn.execute(text(seed_sql))
                conn.commit()
            
            # Seed MongoDB data
            db = self.mongodb_client.luxcrafts
            
            # Sample market data
            market_data_samples = [
                {
                    "location": {
                        "city": "Beverly Hills",
                        "state": "CA",
                        "coordinates": [-118.4004, 34.0736]
                    },
                    "market_metrics": {
                        "avg_price_per_sqft": 850,
                        "market_trend": "increasing",
                        "inventory_level": "low",
                        "days_on_market": 45
                    },
                    "timestamp": datetime.now()
                },
                {
                    "location": {
                        "city": "Manhattan",
                        "state": "NY",
                        "coordinates": [-73.9857, 40.7484]
                    },
                    "market_metrics": {
                        "avg_price_per_sqft": 1200,
                        "market_trend": "stable",
                        "inventory_level": "medium",
                        "days_on_market": 60
                    },
                    "timestamp": datetime.now()
                }
            ]
            
            db.market_data.insert_many(market_data_samples)
            
            # Sample content library
            content_samples = [
                {
                    "content_id": "sample_property_video_1",
                    "content_type": "video",
                    "metadata": {
                        "title": "Luxury Property Showcase",
                        "duration": 120,
                        "tags": ["luxury", "property", "showcase"],
                        "resolution": "4K"
                    },
                    "created_at": datetime.now()
                },
                {
                    "content_id": "sample_market_report_1",
                    "content_type": "document",
                    "metadata": {
                        "title": "Q4 Market Analysis Report",
                        "pages": 25,
                        "tags": ["market", "analysis", "report"],
                        "format": "PDF"
                    },
                    "created_at": datetime.now()
                }
            ]
            
            db.content_library.insert_many(content_samples)
            
            # Initialize Redis cache with sample data
            sample_token_price = {
                "LUX": {"price": 1.25, "change_24h": 5.2, "volume": 1250000},
                "ETH": {"price": 2400.50, "change_24h": -2.1, "volume": 15000000},
                "BTC": {"price": 45000.00, "change_24h": 1.8, "volume": 25000000}
            }
            
            self.redis_client.hset("token_prices", mapping={
                k: json.dumps(v) for k, v in sample_token_price.items()
            })
            
            duration = time.time() - start_time
            self.log_operation(
                "Data Seeding", 
                "SUCCESS", 
                "Initial data seeded successfully",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "Data Seeding", 
                "FAILED", 
                f"Failed to seed data: {str(e)}",
                duration
            )
            return False
    
    def create_backup(self) -> bool:
        """Create database backups"""
        if not self.config.backup_enabled:
            self.log_operation("Database Backup", "SKIPPED", "Backup not enabled")
            return True
            
        try:
            start_time = time.time()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # PostgreSQL backup
            pg_backup_file = f"luxcrafts_postgres_{timestamp}.sql"
            pg_backup_cmd = f"pg_dump {self.config.postgres_url} > {pg_backup_file}"
            subprocess.run(pg_backup_cmd, shell=True, check=True)
            
            # MongoDB backup
            mongo_backup_dir = f"luxcrafts_mongodb_{timestamp}"
            mongo_backup_cmd = f"mongodump --uri='{self.config.mongodb_url}' --out={mongo_backup_dir}"
            subprocess.run(mongo_backup_cmd, shell=True, check=True)
            
            # Redis backup
            redis_backup_file = f"luxcrafts_redis_{timestamp}.rdb"
            self.redis_client.bgsave()
            
            # Upload to S3 if configured
            if self.config.s3_backup_bucket:
                s3_client = boto3.client('s3')
                
                # Upload PostgreSQL backup
                s3_client.upload_file(
                    pg_backup_file, 
                    self.config.s3_backup_bucket, 
                    f"postgres/{pg_backup_file}"
                )
                
                # Upload MongoDB backup (compressed)
                subprocess.run(f"tar -czf {mongo_backup_dir}.tar.gz {mongo_backup_dir}", shell=True)
                s3_client.upload_file(
                    f"{mongo_backup_dir}.tar.gz", 
                    self.config.s3_backup_bucket, 
                    f"mongodb/{mongo_backup_dir}.tar.gz"
                )
                
                # Clean up local files
                subprocess.run(f"rm -f {pg_backup_file} {mongo_backup_dir}.tar.gz", shell=True)
                subprocess.run(f"rm -rf {mongo_backup_dir}", shell=True)
            
            duration = time.time() - start_time
            self.log_operation(
                "Database Backup", 
                "SUCCESS", 
                f"Backup created: {timestamp}",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "Database Backup", 
                "FAILED", 
                f"Failed to create backup: {str(e)}",
                duration
            )
            return False
    
    def optimize_databases(self) -> bool:
        """Optimize database performance"""
        try:
            start_time = time.time()
            
            # PostgreSQL optimization
            optimization_sql = """
            -- Update table statistics
            ANALYZE;
            
            -- Reindex tables
            REINDEX DATABASE luxcrafts;
            
            -- Vacuum tables
            VACUUM ANALYZE;
            """
            
            with self.postgres_engine.connect() as conn:
                for statement in optimization_sql.split(';'):
                    if statement.strip():
                        conn.execute(text(statement))
            
            # MongoDB optimization
            db = self.mongodb_client.luxcrafts
            for collection_name in db.list_collection_names():
                collection = db[collection_name]
                # Rebuild indexes
                collection.reindex()
                # Compact collection
                db.command("compact", collection_name)
            
            # Redis optimization
            # Clean expired keys
            self.redis_client.flushdb()
            
            # Optimize memory usage
            info = self.redis_client.info('memory')
            logger.info(f"Redis memory usage: {info.get('used_memory_human', 'unknown')}")
            
            duration = time.time() - start_time
            self.log_operation(
                "Database Optimization", 
                "SUCCESS", 
                "Database optimization completed",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation(
                "Database Optimization", 
                "FAILED", 
                f"Failed to optimize databases: {str(e)}",
                duration
            )
            return False
    
    def generate_database_report(self) -> Dict[str, Any]:
        """Generate comprehensive database status report"""
        try:
            # PostgreSQL statistics
            with self.postgres_engine.connect() as conn:
                pg_stats = conn.execute(text("""
                    SELECT 
                        schemaname,
                        tablename,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes,
                        n_live_tup as live_tuples
                    FROM pg_stat_user_tables
                    ORDER BY n_live_tup DESC;
                """)).fetchall()
            
            # MongoDB statistics
            db = self.mongodb_client.luxcrafts
            mongo_stats = {}
            for collection_name in db.list_collection_names():
                stats = db.command("collStats", collection_name)
                mongo_stats[collection_name] = {
                    "count": stats.get("count", 0),
                    "size": stats.get("size", 0),
                    "avgObjSize": stats.get("avgObjSize", 0)
                }
            
            # Redis statistics
            redis_info = self.redis_client.info()
            
            return {
                "timestamp": datetime.now().isoformat(),
                "postgresql": {
                    "table_statistics": [dict(row._mapping) for row in pg_stats],
                    "connection_status": "connected"
                },
                "mongodb": {
                    "collection_statistics": mongo_stats,
                    "connection_status": "connected"
                },
                "redis": {
                    "memory_usage": redis_info.get("used_memory_human"),
                    "connected_clients": redis_info.get("connected_clients"),
                    "total_commands_processed": redis_info.get("total_commands_processed"),
                    "connection_status": "connected"
                },
                "operations_log": self.operation_log
            }
            
        except Exception as e:
            logger.error(f"Failed to generate database report: {str(e)}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e),
                "operations_log": self.operation_log
            }
    
    def setup_databases(self) -> Dict[str, Any]:
        """Execute complete database setup process"""
        logger.info("Starting database setup process")
        
        setup_steps = [
            ("connect_databases", "Connecting to databases"),
            ("setup_postgres_schema", "Setting up PostgreSQL schema"),
            ("setup_mongodb_collections", "Setting up MongoDB collections"),
            ("setup_redis_cache", "Setting up Redis cache"),
            ("seed_initial_data", "Seeding initial data"),
            ("create_backup", "Creating initial backup"),
            ("optimize_databases", "Optimizing databases")
        ]
        
        for step_method, step_description in setup_steps:
            logger.info(f"Executing: {step_description}")
            method = getattr(self, step_method)
            success = method()
            
            if not success and step_method in ['connect_databases', 'setup_postgres_schema']:
                logger.error(f"Critical step failed: {step_description}")
                break
        
        # Generate and return database report
        report = self.generate_database_report()
        
        # Determine overall status
        successful_operations = len([op for op in self.operation_log if op['status'] == 'SUCCESS'])
        total_operations = len(self.operation_log)
        
        report['setup_summary'] = {
            "total_operations": total_operations,
            "successful_operations": successful_operations,
            "success_rate": (successful_operations / total_operations * 100) if total_operations > 0 else 0,
            "status": "SUCCESS" if successful_operations == total_operations else "PARTIAL" if successful_operations > 0 else "FAILED"
        }
        
        logger.info(f"Database setup completed with status: {report['setup_summary']['status']}")
        return report

def main():
    parser = argparse.ArgumentParser(description="Database Management Agent")
    parser.add_argument("--postgres-url", required=True, help="PostgreSQL connection URL")
    parser.add_argument("--mongodb-url", required=True, help="MongoDB connection URL")
    parser.add_argument("--redis-url", required=True, help="Redis connection URL")
    parser.add_argument("--environment", required=True, choices=["development", "staging", "production"],
                       help="Environment name")
    parser.add_argument("--backup-enabled", action="store_true", help="Enable database backups")
    parser.add_argument("--s3-backup-bucket", help="S3 bucket for backups")
    parser.add_argument("--output", default="database-report.json",
                       help="Output file for database report")
    
    args = parser.parse_args()
    
    # Create database configuration
    config = DatabaseConfig(
        postgres_url=args.postgres_url,
        mongodb_url=args.mongodb_url,
        redis_url=args.redis_url,
        environment=args.environment,
        backup_enabled=args.backup_enabled,
        s3_backup_bucket=args.s3_backup_bucket
    )
    
    # Create and run database agent
    agent = DatabaseAgent(config)
    report = agent.setup_databases()
    
    # Save database report
    with open(args.output, 'w') as f:
        json.dump(report, f, indent=2, default=str)
    
    logger.info(f"Database report saved to {args.output}")
    
    # Exit with appropriate code
    status = report.get('setup_summary', {}).get('status', 'FAILED')
    if status == 'SUCCESS':
        sys.exit(0)
    elif status == 'PARTIAL':
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()