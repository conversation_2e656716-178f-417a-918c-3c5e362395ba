import { logger } from '../utils/logger';
import { notificationService } from './notificationService';

interface Task {
  id: string;
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  createdBy: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'todo' | 'in_progress' | 'review' | 'done' | 'blocked';
  dueDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  tags: string[];
  dependencies: string[];
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

interface CreateTaskData {
  title: string;
  description?: string;
  projectId: string;
  assigneeId?: string;
  createdBy: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  status?: 'todo' | 'in_progress' | 'review' | 'done' | 'blocked';
  dueDate?: Date;
  estimatedHours?: number;
  tags?: string[];
  dependencies?: string[];
  metadata?: Record<string, any>;
}

interface TaskQuery {
  page?: number;
  limit?: number;
  projectId?: string;
  assigneeId?: string;
  status?: string;
  priority?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  tags?: string;
  dueDateFrom?: Date;
  dueDateTo?: Date;
}

interface TaskComment {
  id: string;
  taskId: string;
  authorId: string;
  content: string;
  createdAt: Date;
  updatedAt: Date;
  metadata?: Record<string, any>;
}

interface TaskTimeLog {
  id: string;
  taskId: string;
  userId: string;
  hours: number;
  description: string;
  date: Date;
  createdAt: Date;
  metadata?: Record<string, any>;
}

interface TaskAnalytics {
  totalTasks: number;
  tasksByStatus: Record<string, number>;
  tasksByPriority: Record<string, number>;
  averageCompletionTime: number;
  overdueTasks: number;
  completionRate: number;
  totalEstimatedHours: number;
  totalActualHours: number;
  productivityMetrics: {
    tasksCompletedThisWeek: number;
    tasksCompletedThisMonth: number;
    averageTasksPerDay: number;
  };
}

class TaskService {
  private tasks: Map<string, Task> = new Map();
  private comments: Map<string, TaskComment[]> = new Map();
  private timeLogs: Map<string, TaskTimeLog[]> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Task Service...');
      
      // Generate mock data for development
      this.generateMockData();
      
      this.initialized = true;
      logger.info('Task Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Task Service:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Task Service...');
    this.initialized = false;
  }

  async isHealthy(): Promise<boolean> {
    return this.initialized;
  }

  async getMetrics(): Promise<Record<string, any>> {
    return {
      totalTasks: this.tasks.size,
      totalComments: Array.from(this.comments.values()).reduce((sum, comments) => sum + comments.length, 0),
      totalTimeLogs: Array.from(this.timeLogs.values()).reduce((sum, logs) => sum + logs.length, 0),
      memoryUsage: process.memoryUsage()
    };
  }

  async createTask(data: CreateTaskData): Promise<Task> {
    const id = this.generateId();
    const now = new Date();
    
    const task: Task = {
      id,
      title: data.title,
      description: data.description,
      projectId: data.projectId,
      assigneeId: data.assigneeId,
      createdBy: data.createdBy,
      priority: data.priority || 'medium',
      status: data.status || 'todo',
      dueDate: data.dueDate,
      estimatedHours: data.estimatedHours,
      actualHours: 0,
      tags: data.tags || [],
      dependencies: data.dependencies || [],
      createdAt: now,
      updatedAt: now,
      metadata: data.metadata
    };
    
    this.tasks.set(id, task);
    
    // Initialize comments and time logs arrays
    this.comments.set(id, []);
    this.timeLogs.set(id, []);
    
    // Send notification if task is assigned
    if (task.assigneeId) {
      await notificationService.notifyTaskAssigned(
        task.id,
        task.title,
        task.assigneeId,
        task.projectId
      );
    }
    
    logger.info(`Task created: ${id}`, {
      taskId: id,
      projectId: task.projectId,
      assigneeId: task.assigneeId
    });
    
    return task;
  }

  async getTasks(query: TaskQuery): Promise<{ tasks: Task[]; total: number }> {
    let filteredTasks = Array.from(this.tasks.values());
    
    // Apply filters
    if (query.projectId) {
      filteredTasks = filteredTasks.filter(task => task.projectId === query.projectId);
    }
    
    if (query.assigneeId) {
      filteredTasks = filteredTasks.filter(task => task.assigneeId === query.assigneeId);
    }
    
    if (query.status) {
      filteredTasks = filteredTasks.filter(task => task.status === query.status);
    }
    
    if (query.priority) {
      filteredTasks = filteredTasks.filter(task => task.priority === query.priority);
    }
    
    if (query.search) {
      const searchLower = query.search.toLowerCase();
      filteredTasks = filteredTasks.filter(task => 
        task.title.toLowerCase().includes(searchLower) ||
        (task.description && task.description.toLowerCase().includes(searchLower))
      );
    }
    
    if (query.tags) {
      const searchTags = query.tags.split(',').map(tag => tag.trim().toLowerCase());
      filteredTasks = filteredTasks.filter(task => 
        searchTags.some(searchTag => 
          task.tags.some(tag => tag.toLowerCase().includes(searchTag))
        )
      );
    }
    
    if (query.dueDateFrom) {
      filteredTasks = filteredTasks.filter(task => 
        task.dueDate && task.dueDate >= query.dueDateFrom!
      );
    }
    
    if (query.dueDateTo) {
      filteredTasks = filteredTasks.filter(task => 
        task.dueDate && task.dueDate <= query.dueDateTo!
      );
    }
    
    // Apply sorting
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';
    
    filteredTasks.sort((a, b) => {
      let aValue: any = a[sortBy as keyof Task];
      let bValue: any = b[sortBy as keyof Task];
      
      if (aValue instanceof Date) aValue = aValue.getTime();
      if (bValue instanceof Date) bValue = bValue.getTime();
      
      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });
    
    const total = filteredTasks.length;
    
    // Apply pagination
    const page = query.page || 1;
    const limit = query.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const tasks = filteredTasks.slice(startIndex, endIndex);
    
    return { tasks, total };
  }

  async getTaskById(id: string): Promise<Task | null> {
    return this.tasks.get(id) || null;
  }

  async updateTask(id: string, updateData: Partial<Task>, userId: string): Promise<Task | null> {
    const task = this.tasks.get(id);
    
    if (!task) {
      return null;
    }
    
    // Check permissions
    if (!this.canUserModifyTask(task, userId)) {
      throw new Error('Insufficient permissions to modify this task');
    }
    
    const previousAssigneeId = task.assigneeId;
    const updatedTask: Task = {
      ...task,
      ...updateData,
      id, // Ensure ID cannot be changed
      updatedAt: new Date()
    };
    
    // Handle status change to 'done'
    if (updateData.status === 'done' && task.status !== 'done') {
      updatedTask.completedAt = new Date();
    }
    
    this.tasks.set(id, updatedTask);
    
    // Send notification if assignee changed
    if (updateData.assigneeId && updateData.assigneeId !== previousAssigneeId) {
      await notificationService.notifyTaskAssigned(
        updatedTask.id,
        updatedTask.title,
        updateData.assigneeId,
        updatedTask.projectId
      );
    }
    
    logger.info(`Task updated: ${id}`, {
      taskId: id,
      userId,
      changes: Object.keys(updateData)
    });
    
    return updatedTask;
  }

  async deleteTask(id: string, userId: string): Promise<boolean> {
    const task = this.tasks.get(id);
    
    if (!task) {
      return false;
    }
    
    // Check permissions
    if (!this.canUserModifyTask(task, userId)) {
      throw new Error('Insufficient permissions to delete this task');
    }
    
    this.tasks.delete(id);
    this.comments.delete(id);
    this.timeLogs.delete(id);
    
    logger.info(`Task deleted: ${id}`, {
      taskId: id,
      userId
    });
    
    return true;
  }

  async addComment(taskId: string, commentData: {
    content: string;
    authorId: string;
    metadata?: Record<string, any>;
  }): Promise<TaskComment | null> {
    const task = this.tasks.get(taskId);
    
    if (!task) {
      return null;
    }
    
    const comment: TaskComment = {
      id: this.generateId(),
      taskId,
      authorId: commentData.authorId,
      content: commentData.content,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: commentData.metadata
    };
    
    const comments = this.comments.get(taskId) || [];
    comments.push(comment);
    this.comments.set(taskId, comments);
    
    logger.info(`Comment added to task: ${taskId}`, {
      taskId,
      commentId: comment.id,
      authorId: commentData.authorId
    });
    
    return comment;
  }

  async logTime(taskId: string, timeLogData: {
    userId: string;
    hours: number;
    description: string;
    date?: Date;
    metadata?: Record<string, any>;
  }): Promise<TaskTimeLog | null> {
    const task = this.tasks.get(taskId);
    
    if (!task) {
      return null;
    }
    
    const timeLog: TaskTimeLog = {
      id: this.generateId(),
      taskId,
      userId: timeLogData.userId,
      hours: timeLogData.hours,
      description: timeLogData.description,
      date: timeLogData.date || new Date(),
      createdAt: new Date(),
      metadata: timeLogData.metadata
    };
    
    const timeLogs = this.timeLogs.get(taskId) || [];
    timeLogs.push(timeLog);
    this.timeLogs.set(taskId, timeLogs);
    
    // Update task's actual hours
    const updatedTask = { ...task };
    updatedTask.actualHours = (updatedTask.actualHours || 0) + timeLogData.hours;
    updatedTask.updatedAt = new Date();
    this.tasks.set(taskId, updatedTask);
    
    logger.info(`Time logged for task: ${taskId}`, {
      taskId,
      timeLogId: timeLog.id,
      hours: timeLogData.hours,
      userId: timeLogData.userId
    });
    
    return timeLog;
  }

  async getTaskAnalytics(query: TaskQuery): Promise<TaskAnalytics> {
    const { tasks } = await this.getTasks({ ...query, limit: 10000 }); // Get all matching tasks
    
    const totalTasks = tasks.length;
    const tasksByStatus: Record<string, number> = {};
    const tasksByPriority: Record<string, number> = {};
    
    let totalEstimatedHours = 0;
    let totalActualHours = 0;
    let completedTasks = 0;
    let overdueTasks = 0;
    let totalCompletionTime = 0;
    
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    let tasksCompletedThisWeek = 0;
    let tasksCompletedThisMonth = 0;
    
    for (const task of tasks) {
      // Count by status
      tasksByStatus[task.status] = (tasksByStatus[task.status] || 0) + 1;
      
      // Count by priority
      tasksByPriority[task.priority] = (tasksByPriority[task.priority] || 0) + 1;
      
      // Calculate hours
      if (task.estimatedHours) {
        totalEstimatedHours += task.estimatedHours;
      }
      if (task.actualHours) {
        totalActualHours += task.actualHours;
      }
      
      // Check if completed
      if (task.status === 'done') {
        completedTasks++;
        
        if (task.completedAt) {
          const completionTime = task.completedAt.getTime() - task.createdAt.getTime();
          totalCompletionTime += completionTime;
          
          // Check completion dates
          if (task.completedAt >= oneWeekAgo) {
            tasksCompletedThisWeek++;
          }
          if (task.completedAt >= oneMonthAgo) {
            tasksCompletedThisMonth++;
          }
        }
      }
      
      // Check if overdue
      if (task.dueDate && task.dueDate < now && task.status !== 'done') {
        overdueTasks++;
      }
    }
    
    const averageCompletionTime = completedTasks > 0 ? totalCompletionTime / completedTasks : 0;
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
    const averageTasksPerDay = tasksCompletedThisMonth > 0 ? tasksCompletedThisMonth / 30 : 0;
    
    return {
      totalTasks,
      tasksByStatus,
      tasksByPriority,
      averageCompletionTime: Math.round(averageCompletionTime / (1000 * 60 * 60 * 24)), // Convert to days
      overdueTasks,
      completionRate: Math.round(completionRate * 100) / 100,
      totalEstimatedHours,
      totalActualHours,
      productivityMetrics: {
        tasksCompletedThisWeek,
        tasksCompletedThisMonth,
        averageTasksPerDay: Math.round(averageTasksPerDay * 100) / 100
      }
    };
  }

  async getTaskDependencies(taskId: string): Promise<{
    dependencies: Task[];
    dependents: Task[];
  }> {
    const task = this.tasks.get(taskId);
    
    if (!task) {
      return { dependencies: [], dependents: [] };
    }
    
    // Get tasks that this task depends on
    const dependencies = task.dependencies
      .map(depId => this.tasks.get(depId))
      .filter(Boolean) as Task[];
    
    // Get tasks that depend on this task
    const dependents = Array.from(this.tasks.values())
      .filter(t => t.dependencies.includes(taskId));
    
    return { dependencies, dependents };
  }

  async updateTaskStatus(
    taskId: string,
    status: Task['status'],
    userId: string,
    comment?: string
  ): Promise<Task | null> {
    const task = await this.updateTask(taskId, { status }, userId);
    
    if (task && comment) {
      await this.addComment(taskId, {
        content: `Status changed to ${status}: ${comment}`,
        authorId: userId
      });
    }
    
    return task;
  }

  private canUserModifyTask(task: Task, userId: string): boolean {
    // Users can modify tasks they created or are assigned to
    // In a real implementation, this would check user roles and permissions
    return task.createdBy === userId || task.assigneeId === userId;
  }

  private generateId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateMockData(): void {
    const mockTasks: CreateTaskData[] = [
      {
        title: 'Setup project infrastructure',
        description: 'Initialize the project with necessary tools and configurations',
        projectId: 'proj_1',
        createdBy: 'user_1',
        priority: 'high',
        status: 'in_progress',
        estimatedHours: 8,
        tags: ['setup', 'infrastructure'],
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      },
      {
        title: 'Design user interface mockups',
        description: 'Create wireframes and mockups for the main user interface',
        projectId: 'proj_1',
        assigneeId: 'user_2',
        createdBy: 'user_1',
        priority: 'medium',
        status: 'todo',
        estimatedHours: 12,
        tags: ['design', 'ui', 'mockups'],
        dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000)
      },
      {
        title: 'Implement authentication system',
        description: 'Build secure user authentication with JWT tokens',
        projectId: 'proj_2',
        assigneeId: 'user_3',
        createdBy: 'user_1',
        priority: 'urgent',
        status: 'review',
        estimatedHours: 16,
        tags: ['backend', 'auth', 'security'],
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000)
      }
    ];
    
    for (const taskData of mockTasks) {
      this.createTask(taskData);
    }
    
    logger.info(`Generated ${mockTasks.length} mock tasks`);
  }
}

export const taskService = new TaskService();