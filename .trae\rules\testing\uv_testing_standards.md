---
trigger: always_on
---

# UV-Based Testing Standards for ESTRATIX

## 1. Testing Execution Protocol

### 1.1. Mandatory UV Run Usage

**CRITICAL REQUIREMENT**: All testing activities in ESTRATIX MUST use `uv run` for consistent environment management and dependency resolution.

```bash
# Standard test execution
uv run pytest

# Test with coverage
uv run pytest --cov=src --cov-report=html

# Specific test files
uv run pytest tests/test_document_processor.py

# Test markers
uv run pytest -m "unit"
uv run pytest -m "integration"
uv run pytest -m "agent"
```

### 1.2. Test Environment Management

- **Isolation**: Each test run uses isolated environment via uv
- **Dependencies**: Test dependencies managed in pyproject.toml
- **Reproducibility**: uv.lock ensures consistent test environments
- **Performance**: UV's fast dependency resolution optimizes test startup

## 2. Test Categories & Execution Patterns

### 2.1. Unit Tests

```bash
# Run all unit tests
uv run pytest -m "unit" --tb=short

# Unit tests with coverage
uv run pytest -m "unit" --cov=src/domain --cov-report=term-missing

# Fast unit test execution
uv run pytest -m "unit" --maxfail=1 -x

# Parallel unit tests
uv run pytest -m "unit" -n auto
```

### 2.2. Integration Tests

```bash
# Integration tests (slower)
uv run pytest -m "integration" --tb=long

# Integration with external services
uv run pytest -m "integration" --external-services

# Database integration tests
uv run pytest -m "integration and database"

# API integration tests
uv run pytest -m "integration and api"
```

### 2.3. Agent-Specific Tests

```bash
# Test all agents
uv run pytest -m "agent" --agent-timeout=30

# Test specific agent
uv run pytest tests/agents/test_cto_command_office.py

# Agent performance tests
uv run pytest -m "agent and performance" --benchmark-only

# Agent integration tests
uv run pytest -m "agent and integration"
```

### 2.4. Tool Tests

```bash
# Test all tools
uv run pytest -m "tool"

# Test document processing tools
uv run pytest tests/tools/test_document_processor.py

# Tool performance benchmarks
uv run pytest -m "tool and benchmark"

# Tool integration with agents
uv run pytest -m "tool and agent"
```

## 3. Test Configuration Standards

### 3.1. PyProject.toml Test Configuration

```toml
[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-benchmark>=4.0.0",
    "pytest-xdist>=3.3.0",
    "httpx>=0.25.0",
    "factory-boy>=3.3.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80",
    "--maxfail=3",
]
markers = [
    "unit: Unit tests (fast, isolated)",
    "integration: Integration tests (slower, external deps)",
    "agent: Agent-specific tests",
    "tool: Tool-specific tests",
    "slow: Slow running tests",
    "benchmark: Performance benchmark tests",
    "database: Tests requiring database",
    "api: API endpoint tests",
    "external: Tests requiring external services",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
asyncio_mode = "auto"
```

### 3.2. Test Directory Structure

```
tests/
├── conftest.py                 # Shared fixtures
├── unit/                       # Unit tests
│   ├── agents/
│   ├── tools/
│   ├── domain/
│   └── infrastructure/
├── integration/                # Integration tests
│   ├── agents/
│   ├── workflows/
│   ├── database/
│   └── api/
├── performance/                # Performance tests
│   ├── benchmarks/
│   └── load_tests/
├── fixtures/                   # Test data
│   ├── documents/
│   ├── responses/
│   └── configs/
└── utils/                      # Test utilities
    ├── factories.py
    ├── helpers.py
    └── mocks.py
```

## 4. Advanced Testing Patterns

### 4.1. Parallel Test Execution

```bash
# Auto-detect CPU cores
uv run pytest -n auto

# Specific number of workers
uv run pytest -n 4

# Distribute by test file
uv run pytest --dist=loadfile

# Distribute by test function
uv run pytest --dist=loadscope
```

### 4.2. Test Coverage Analysis

```bash
# Generate HTML coverage report
uv run pytest --cov=src --cov-report=html --cov-report=term

# Coverage with branch analysis
uv run pytest --cov=src --cov-branch --cov-report=term-missing

# Fail if coverage below threshold
uv run pytest --cov=src --cov-fail-under=85

# Coverage for specific modules
uv run pytest --cov=src.agents --cov=src.tools
```

### 4.3. Performance Testing

```bash
# Benchmark tests
uv run pytest --benchmark-only

# Benchmark with comparison
uv run pytest --benchmark-compare

# Save benchmark results
uv run pytest --benchmark-save=baseline

# Compare against baseline
uv run pytest --benchmark-compare=baseline
```

## 5. Continuous Integration Testing

### 5.1. GitHub Actions Integration

```yaml
name: ESTRATIX Test Suite

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"
      
      - name: Set up Python ${{ matrix.python-version }}
        run: uv python install ${{ matrix.python-version }}
      
      - name: Install dependencies
        run: uv sync --all-extras
      
      - name: Run unit tests
        run: uv run pytest -m "unit" --cov=src --cov-report=xml
      
      - name: Run integration tests
        run: uv run pytest -m "integration" --tb=short
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
```

### 5.2. Pre-commit Testing Hooks

```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: pytest-unit
        name: pytest-unit
        entry: uv run pytest -m "unit" --maxfail=1
        language: system
        types: [python]
        pass_filenames: false
      
      - id: pytest-changed
        name: pytest-changed
        entry: uv run pytest --lf --tb=short
        language: system
        types: [python]
        pass_filenames: false
```

## 6. Test Data Management

### 6.1. Fixture Management

```python
# conftest.py
import pytest
from pathlib import Path

@pytest.fixture(scope="session")
def test_data_dir():
    return Path(__file__).parent / "fixtures"

@pytest.fixture
def sample_document(test_data_dir):
    return test_data_dir / "documents" / "sample.pdf"

@pytest.fixture
def mock_agent_response(test_data_dir):
    with open(test_data_dir / "responses" / "agent_response.json") as f:
        return json.load(f)
```

### 6.2. Factory Pattern for Test Data

```python
# tests/utils/factories.py
import factory
from src.domain.models import Document, Agent

class DocumentFactory(factory.Factory):
    class Meta:
        model = Document
    
    title = factory.Faker("sentence", nb_words=4)
    content = factory.Faker("text", max_nb_chars=1000)
    file_type = "pdf"
    metadata = factory.Dict({
        "author": factory.Faker("name"),
        "created_at": factory.Faker("date_time"),
    })

class AgentFactory(factory.Factory):
    class Meta:
        model = Agent
    
    name = factory.Faker("word")
    role = factory.Faker("job")
    capabilities = factory.List([
        factory.Faker("word") for _ in range(3)
    ])
```

## 7. Debugging & Troubleshooting

### 7.1. Debug Mode Testing

```bash
# Run tests with debug output
uv run pytest --tb=long --capture=no

# Drop into debugger on failure
uv run pytest --pdb

# Debug specific test
uv run pytest tests/test_specific.py::test_function --pdb

# Verbose output
uv run pytest -vv --tb=line
```

### 7.2. Test Environment Debugging

```bash
# Check test environment
uv run python -c "import sys; print(sys.path)"

# Verify test dependencies
uv run pip list | grep pytest

# Check UV environment
uv run python -c "import os; print(os.environ.get('VIRTUAL_ENV'))"

# Test configuration validation
uv run pytest --collect-only
```

## 8. Performance Optimization

### 8.1. Test Execution Speed

```bash
# Profile test execution
uv run pytest --durations=10

# Skip slow tests in development
uv run pytest -m "not slow"

# Fast feedback loop
uv run pytest --maxfail=1 -x --tb=no

# Cache test results
uv run pytest --cache-clear  # Clear cache
uv run pytest --lf           # Run last failed
uv run pytest --ff           # Run failed first
```

### 8.2. Resource Management

```python
# Efficient fixture scoping
@pytest.fixture(scope="session")
def expensive_setup():
    # Setup once per test session
    pass

@pytest.fixture(scope="module")
def module_setup():
    # Setup once per test module
    pass

@pytest.fixture(scope="function")
def function_setup():
    # Setup for each test function
    pass
```

## 9. Quality Assurance Integration

### 9.1. Code Quality in Tests

```bash
# Lint test code
uv run ruff check tests/

# Format test code
uv run black tests/

# Type check tests
uv run mypy tests/

# Security scan tests
uv run bandit -r tests/
```

### 9.2. Test Quality Metrics

```bash
# Test coverage report
uv run pytest --cov=src --cov-report=html

# Mutation testing
uv run mutmut run

# Test complexity analysis
uv run pytest --complexity

# Dead code detection in tests
uv run vulture tests/
```

## 10. Documentation & Reporting

### 10.1. Test Documentation

```python
def test_document_processor_handles_pdf():
    """
    Test that DocumentProcessor correctly processes PDF files.
    
    Given: A valid PDF file
    When: Processing through DocumentProcessor
    Then: Should extract text content and metadata
    
    Execution: uv run pytest tests/test_document_processor.py::test_document_processor_handles_pdf
    """
    pass
```

### 10.2. Test Reporting

```bash
# Generate JUnit XML report
uv run pytest --junitxml=test-results.xml

# Generate HTML report
uv run pytest --html=test-report.html --self-contained-html

# Generate JSON report
uv run pytest --json-report --json-report-file=test-report.json
```

## 11. Compliance & Standards

### 11.1. Mandatory Requirements

- ✅ All tests MUST use `uv run pytest`
- ✅ Test dependencies MUST be in pyproject.toml
- ✅ Coverage MUST be >= 80%
- ✅ Tests MUST be categorized with markers
- ✅ CI/CD MUST use UV for test execution
- ✅ Pre-commit hooks MUST include test validation

### 11.2. Best Practices Enforcement

- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Mock external dependencies
- Use factories for test data
- Maintain test independence
- Document complex test scenarios

---

**ESTRATIX Testing Excellence - Powered by UV**
© 2025 ESTRATIX - Autonomous Operations Framework