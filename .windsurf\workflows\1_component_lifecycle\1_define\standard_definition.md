---
description: Guides the definition of a new ESTRATIX Standard, from proposal/identification to a documented definition and registration in the standards matrix.
---

# Workflow: Define ESTRATIX Standard

**Objective:** To systematically define, document, and register new standards (e.g., coding, security, project management, compliance) within the ESTRATIX framework, ensuring clarity, consistency, and proper governance.

**Trigger:**

* Identification of a need for a new standard (e.g., from project retrospectives, risk assessments, new technology adoption, compliance requirements, quality improvement initiatives).
* A proposal for a new standard is submitted.

**Responsible Command Office (Lead):** Typically the office most relevant to the standard's domain (e.g., CTO for technical standards, CPO for process standards, CPrO for project management standards, CIO for information/security standards). Can be initiated by any office.

**Key ESTRATIX Components Involved:**

* `docs/templates/estratix_standard_definition_template.md` (Input Template)
* `docs/standards/[OFFICER_CODE]/[STD_ID]_[StandardName].md` (Output: New Standard Definition Document)
* `docs/matrices/standards_matrix.md` (Output: Updated Matrix)
* Relevant Command Offices (for review and approval)
* Potentially: Research & Scouting processes for identifying best practices or external requirements.

## Steps

1. **Identify Need & Propose Standard**

   * **Action:** Clearly articulate the need for the new standard, the problem it solves, or the opportunity it addresses.
   * **Input:** Business need, gap analysis, external requirement, best practice research.
   * **Output:** A brief proposal or justification for the new standard, including its intended scope and benefits.

2. **Assign Responsibility & Preliminary Research**

   * **Action:** The relevant Command Office (or a designated working group) takes ownership.
   * **Action:** Conduct preliminary research to gather information on existing best practices, internal needs, and potential impacts.
   * **Output:** Initial research findings and a designated owner/team for drafting the standard.

3. **Draft Standard Definition Document**

   * **Action:** Create a new standard definition document using the `docs/templates/estratix_standard_definition_template.md`.
   * **Path:** `docs/standards/[RESPONSIBLE_OFFICER_CODE]/[STD_ID]_[StandardName_PascalCase].md` (Determine a consistent `STD_ID` format, e.g., `STD_[CATEGORY]_[SPECIFIC_IDENTIFIER]_[VERSION_IF_NEEDED]`).
   * **Content:** Fill in all sections of the template: Purpose & Scope, Category, Details & Specifications, Rationale & Benefits, Applicability & Enforcement, Related Components, Training & Resources, etc.
   * **Guidance:** Be clear, concise, and actionable. Use examples where appropriate.
   * **Output:** Draft standard definition document.

4. **Internal Review & Feedback**

   * **Action:** Circulate the draft standard definition among relevant stakeholders and Command Offices for review and feedback.
   * **Considerations:** Technical accuracy, practicality, clarity, completeness, impact on existing processes/systems.
   * **Output:** Collated feedback and comments on the draft standard.

5. **Revise and Refine Standard Definition**

   * **Action:** Incorporate feedback and revise the standard definition document as necessary.
   * **Output:** Updated draft standard definition document.

6. **Approval & Formalization**

   * **Action:** Submit the revised standard definition for formal approval by the designated authority (e.g., relevant Command Officer, CTO, Standards Committee if one exists).
   * **Output:** Approved standard definition document. Status changed to `Active` (or `Proposed` if further validation is needed before full activation).

7. **Register in Standards Matrix**

   * **Action:** Add a new entry for the approved standard in `docs/matrices/standards_matrix.md`.
   * **Details:** Ensure all columns (Standard ID, Name, Version, Status, Responsible Office, Category, Link to Definition, etc.) are accurately filled.
   * **Output:** Updated `standards_matrix.md`.

8. **Update Standards Landscape Diagram (Trigger Workflow)**

   * **Action:** Manually trigger the `wf_update_standards_landscape.md` workflow, or ensure it is run, to reflect the new standard in `docs/diagrams/standards_landscape.mmd`.
   * **Output:** Updated `standards_landscape.mmd`.

9. **Communicate & Disseminate**

   * **Action:** Announce the new/updated standard to all relevant personnel and teams.
   * **Methods:** Internal memos, team meetings, updates to relevant documentation portals.
   * **Output:** Awareness of the new standard across applicable parts of the organization.

10. **Develop Training & Implementation Plan (If Needed)**

    * **Action:** If the standard requires significant changes in practice, develop a plan for training and implementation support.
    * **Output:** Training materials, implementation guidelines, support plan.

## Post-Definition Activities

* Implementation of the standard (guided by `wf_standard_generation.md` if it involves code/tooling).
* Monitoring adherence and effectiveness.
* Periodic review and updates to the standard as needed (PDCA cycle).
