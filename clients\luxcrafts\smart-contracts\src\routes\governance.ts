import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { authenticateToken, requireRole, requireWallet } from '../middleware/auth';
import { web3Service } from '../services/web3Service';
import { logger } from '../utils/logger';

const createProposalSchema = {
  body: {
    type: 'object',
    required: ['title', 'description', 'targets', 'values', 'calldatas'],
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', minLength: 1, maxLength: 2000 },
      targets: {
        type: 'array',
        items: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
      },
      values: {
        type: 'array',
        items: { type: 'string' }
      },
      calldatas: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  }
};

const voteSchema = {
  body: {
    type: 'object',
    required: ['proposalId', 'support'],
    properties: {
      proposalId: { type: 'string' },
      support: { type: 'number', enum: [0, 1, 2] }, // 0: Against, 1: For, 2: Abstain
      reason: { type: 'string', maxLength: 500 }
    }
  }
};

const proposalIdSchema = {
  params: {
    type: 'object',
    required: ['proposalId'],
    properties: {
      proposalId: { type: 'string' }
    }
  }
};

const delegateSchema = {
  body: {
    type: 'object',
    required: ['delegatee'],
    properties: {
      delegatee: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
    }
  }
};

async function governanceRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Create proposal
  fastify.post('/proposals', {
    schema: createProposalSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { title, description, targets, values, calldatas } = request.body as {
        title: string;
        description: string;
        targets: string[];
        values: string[];
        calldatas: string[];
      };
      
      const userAddress = (request.user as any)?.walletAddress!;
      
      // Check if user has enough voting power to create proposal
      const votingPower = await web3Service.getVotingPower(userAddress);
      const proposalThreshold = await web3Service.getProposalThreshold();
      
      if (BigInt(votingPower) < BigInt(proposalThreshold)) {
        return reply.status(400).send({
          error: 'Insufficient voting power to create proposal',
          required: proposalThreshold,
          current: votingPower
        });
      }
      
      const txHash = await web3Service.createProposal(
        userAddress,
        targets,
        values,
        calldatas,
        `${title}\n\n${description}`
      );
      
      logger.info(`Proposal created by ${userAddress}`, { txHash, title });
      
      return {
        success: true,
        txHash,
        title,
        description
      };
    } catch (error) {
      logger.error('Proposal creation failed:', error);
      return reply.status(500).send({
        error: 'Failed to create proposal',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Vote on proposal
  fastify.post('/proposals/:proposalId/vote', {
    schema: {
      ...voteSchema,
      params: proposalIdSchema.params
    },
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { proposalId } = request.params as { proposalId: string };
      const { support, reason } = request.body as { support: number; reason?: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      // Check if proposal exists and is in voting state
      const proposalState = await web3Service.getProposalState(proposalId);
      if (proposalState !== 1) { // 1 = Active
        return reply.status(400).send({
          error: 'Proposal is not in voting state',
          state: proposalState
        });
      }
      
      const txHash = await web3Service.castVote(
        userAddress,
        proposalId,
        support,
        reason
      );
      
      logger.info(`Vote cast by ${userAddress} on proposal ${proposalId}`, {
        txHash,
        support,
        reason
      });
      
      return {
        success: true,
        txHash,
        proposalId,
        support,
        reason
      };
    } catch (error) {
      logger.error('Voting failed:', error);
      return reply.status(500).send({
        error: 'Failed to cast vote',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Execute proposal
  fastify.post('/proposals/:proposalId/execute', {
    schema: { params: proposalIdSchema.params },
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { proposalId } = request.params as { proposalId: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      // Check if proposal is ready for execution
      const proposalState = await web3Service.getProposalState(proposalId);
      if (proposalState !== 4) { // 4 = Succeeded
        return reply.status(400).send({
          error: 'Proposal is not ready for execution',
          state: proposalState
        });
      }
      
      const txHash = await web3Service.executeProposal(userAddress, proposalId);
      
      logger.info(`Proposal ${proposalId} executed by ${userAddress}`, { txHash });
      
      return {
        success: true,
        txHash,
        proposalId
      };
    } catch (error) {
      logger.error('Proposal execution failed:', error);
      return reply.status(500).send({
        error: 'Failed to execute proposal',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get proposal details
  fastify.get('/proposals/:proposalId', {
    schema: { params: proposalIdSchema.params }
  }, async (request, reply) => {
    try {
      const { proposalId } = request.params as { proposalId: string };
      
      const proposal = await web3Service.getProposal(proposalId);
      
      return proposal;
    } catch (error) {
      logger.error('Failed to get proposal:', error);
      return reply.status(500).send({
        error: 'Failed to get proposal',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get all proposals
  fastify.get('/proposals', async (request, reply) => {
    try {
      const proposals = await web3Service.getAllProposals();
      
      return { proposals };
    } catch (error) {
      logger.error('Failed to get proposals:', error);
      return reply.status(500).send({
        error: 'Failed to get proposals',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Delegate voting power
  fastify.post('/delegate', {
    schema: delegateSchema,
    preHandler: [authenticateToken, requireWallet()]
  }, async (request, reply) => {
    try {
      const { delegatee } = request.body as { delegatee: string };
      const userAddress = (request.user as any)?.walletAddress!;
      
      const txHash = await web3Service.delegateVotes(userAddress, delegatee);
      
      logger.info(`Voting power delegated from ${userAddress} to ${delegatee}`, { txHash });
      
      return {
        success: true,
        txHash,
        delegator: userAddress,
        delegatee
      };
    } catch (error) {
      logger.error('Delegation failed:', error);
      return reply.status(500).send({
        error: 'Failed to delegate votes',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get voting power
  fastify.get('/voting-power/:address', {
    schema: {
      params: {
        type: 'object',
        required: ['address'],
        properties: {
          address: { type: 'string', pattern: '^0x[a-fA-F0-9]{40}$' }
        }
      }
    }
  }, async (request, reply) => {
    try {
      const { address } = request.params as { address: string };
      
      const votingPower = await web3Service.getVotingPower(address);
      
      return {
        address,
        votingPower,
        formatted: web3Service.formatTokenAmount(votingPower)
      };
    } catch (error) {
      logger.error('Failed to get voting power:', error);
      return reply.status(500).send({
        error: 'Failed to get voting power',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get governance settings
  fastify.get('/settings', async (request, reply) => {
    try {
      const settings = await web3Service.getGovernanceSettings();
      
      return settings;
    } catch (error) {
      logger.error('Failed to get governance settings:', error);
      return reply.status(500).send({
        error: 'Failed to get governance settings',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

export default fp(governanceRoutes);