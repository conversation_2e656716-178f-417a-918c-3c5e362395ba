import { v4 as uuidv4 } from 'uuid';
import { Client, CreateClientRequest, UpdateClientRequest } from '@/types';
import { logger } from '@/utils/logger';
import { sanitizeString, sanitizeEmail, sanitizePhone } from '@/utils/validation';

export class ClientService {
  private clients: Map<string, Client> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Client Service...');
      
      // Generate mock clients for development
      await this.generateMockClients();
      
      this.isInitialized = true;
      logger.info('Client Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Client Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Client Service...');
    this.clients.clear();
    this.isInitialized = false;
  }

  async createClient(organizationId: string, data: CreateClientRequest): Promise<Client> {
    try {
      const clientId = uuidv4();
      const now = new Date();

      const client: Client = {
        id: clientId,
        organizationId,
        name: sanitizeString(data.name, 100),
        email: sanitizeEmail(data.email),
        phone: data.phone ? sanitizePhone(data.phone) : undefined,
        company: data.company ? sanitizeString(data.company, 100) : undefined,
        industry: data.industry ? sanitizeString(data.industry, 50) : undefined,
        status: 'lead',
        source: data.source as Client['source'],
        tags: data.tags || [],
        customFields: data.customFields || {},
        createdAt: now,
        updatedAt: now
      };

      this.clients.set(clientId, client);

      logger.info('Client created successfully', {
        clientId,
        organizationId,
        email: client.email
      });

      return client;
    } catch (error) {
      logger.error('Failed to create client', { error, data });
      throw error;
    }
  }

  async getClient(clientId: string, organizationId: string): Promise<Client | null> {
    const client = this.clients.get(clientId);
    
    if (!client || client.organizationId !== organizationId) {
      return null;
    }

    return client;
  }

  async updateClient(
    clientId: string,
    organizationId: string,
    data: UpdateClientRequest
  ): Promise<Client | null> {
    try {
      const client = await this.getClient(clientId, organizationId);
      
      if (!client) {
        return null;
      }

      const updatedClient: Client = {
        ...client,
        name: data.name ? sanitizeString(data.name, 100) : client.name,
        email: data.email ? sanitizeEmail(data.email) : client.email,
        phone: data.phone !== undefined ? (data.phone ? sanitizePhone(data.phone) : undefined) : client.phone,
        company: data.company !== undefined ? (data.company ? sanitizeString(data.company, 100) : undefined) : client.company,
        industry: data.industry !== undefined ? (data.industry ? sanitizeString(data.industry, 50) : undefined) : client.industry,
        status: data.status || client.status,
        assignedTo: data.assignedTo !== undefined ? data.assignedTo : client.assignedTo,
        tags: data.tags || client.tags,
        customFields: { ...client.customFields, ...(data.customFields || {}) },
        updatedAt: new Date()
      };

      this.clients.set(clientId, updatedClient);

      logger.info('Client updated successfully', {
        clientId,
        organizationId,
        changes: Object.keys(data)
      });

      return updatedClient;
    } catch (error) {
      logger.error('Failed to update client', { error, clientId, data });
      throw error;
    }
  }

  async deleteClient(clientId: string, organizationId: string): Promise<boolean> {
    try {
      const client = await this.getClient(clientId, organizationId);
      
      if (!client) {
        return false;
      }

      this.clients.delete(clientId);

      logger.info('Client deleted successfully', {
        clientId,
        organizationId
      });

      return true;
    } catch (error) {
      logger.error('Failed to delete client', { error, clientId });
      throw error;
    }
  }

  async getClients(
    organizationId: string,
    filters: {
      status?: Client['status'];
      source?: Client['source'];
      industry?: string;
      assignedTo?: string;
      tags?: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ clients: Client[]; total: number; page: number; limit: number }> {
    try {
      let filteredClients = Array.from(this.clients.values())
        .filter(client => client.organizationId === organizationId);

      // Apply filters
      if (filters.status) {
        filteredClients = filteredClients.filter(client => client.status === filters.status);
      }

      if (filters.source) {
        filteredClients = filteredClients.filter(client => client.source === filters.source);
      }

      if (filters.industry) {
        filteredClients = filteredClients.filter(client => 
          client.industry?.toLowerCase().includes(filters.industry!.toLowerCase())
        );
      }

      if (filters.assignedTo) {
        filteredClients = filteredClients.filter(client => client.assignedTo === filters.assignedTo);
      }

      if (filters.tags) {
        const searchTags = filters.tags.split(',').map(tag => tag.trim().toLowerCase());
        filteredClients = filteredClients.filter(client => 
          searchTags.some(tag => 
            client.tags.some(clientTag => clientTag.toLowerCase().includes(tag))
          )
        );
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredClients = filteredClients.filter(client => 
          client.name.toLowerCase().includes(searchTerm) ||
          client.email.toLowerCase().includes(searchTerm) ||
          client.company?.toLowerCase().includes(searchTerm)
        );
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'desc';
      
      filteredClients.sort((a, b) => {
        let aValue: any = (a as any)[sortBy];
        let bValue: any = (b as any)[sortBy];

        if (aValue instanceof Date) aValue = aValue.getTime();
        if (bValue instanceof Date) bValue = bValue.getTime();

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedClients = filteredClients.slice(startIndex, endIndex);

      return {
        clients: paginatedClients,
        total: filteredClients.length,
        page,
        limit
      };
    } catch (error) {
      logger.error('Failed to get clients', { error, filters });
      throw error;
    }
  }

  async updateLastContact(clientId: string, organizationId: string): Promise<void> {
    try {
      const client = await this.getClient(clientId, organizationId);
      
      if (client) {
        client.lastContactAt = new Date();
        client.updatedAt = new Date();
        this.clients.set(clientId, client);

        logger.debug('Client last contact updated', { clientId });
      }
    } catch (error) {
      logger.error('Failed to update last contact', { error, clientId });
    }
  }

  async getClientStats(organizationId: string): Promise<{
    total: number;
    byStatus: Record<Client['status'], number>;
    bySource: Record<Client['source'], number>;
    recentlyAdded: number;
  }> {
    try {
      const clients = Array.from(this.clients.values())
        .filter(client => client.organizationId === organizationId);

      const byStatus: Record<Client['status'], number> = {
        lead: 0,
        prospect: 0,
        active: 0,
        inactive: 0,
        churned: 0
      };

      const bySource: Record<Client['source'], number> = {
        website: 0,
        referral: 0,
        marketing: 0,
        sales: 0,
        other: 0
      };

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      let recentlyAdded = 0;

      clients.forEach(client => {
        byStatus[client.status]++;
        bySource[client.source]++;
        
        if (client.createdAt > thirtyDaysAgo) {
          recentlyAdded++;
        }
      });

      return {
        total: clients.length,
        byStatus,
        bySource,
        recentlyAdded
      };
    } catch (error) {
      logger.error('Failed to get client stats', { error, organizationId });
      throw error;
    }
  }

  private async generateMockClients(): Promise<void> {
    const mockClients: Omit<Client, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        organizationId: 'org-1',
        name: 'John Smith',
        email: '<EMAIL>',
        phone: '******-0123',
        company: 'TechCorp Solutions',
        industry: 'Technology',
        status: 'prospect',
        source: 'website',
        tags: ['enterprise', 'high-priority'],
        customFields: {
          budget: 50000,
          timeline: '3 months'
        }
      },
      {
        organizationId: 'org-1',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0456',
        company: 'RetailPlus Inc',
        industry: 'Retail',
        status: 'lead',
        source: 'referral',
        tags: ['small-business'],
        customFields: {
          referredBy: 'Mike Wilson'
        }
      }
    ];

    for (const mockClient of mockClients) {
      const now = new Date();
      const client: Client = {
        ...mockClient,
        id: uuidv4(),
        createdAt: now,
        updatedAt: now
      };
      
      this.clients.set(client.id, client);
    }

    logger.debug(`Generated ${mockClients.length} mock clients`);
  }
}