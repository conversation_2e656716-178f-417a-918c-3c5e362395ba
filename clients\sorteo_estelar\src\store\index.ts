import { create } from 'zustand';
import { persist } from 'zustand/middleware';

// User Store
interface User {
  id: string;
  email: string;
  role: 'default' | 'colombian' | 'international' | 'staker' | 'governance' | 'liquidity_provider' | 'nft_holder';
  walletAddress?: string;
  preferences: {
    language: string;
    currency: string;
    notifications: boolean;
  };
  stats: {
    totalSpent: number;
    totalWon: number;
    ticketsPurchased: number;
    stakingRewards: number;
  };
}

interface UserState {
  user: User | null;
  isAuthenticated: boolean;
  setUser: (user: User) => void;
  logout: () => void;
  updatePreferences: (preferences: Partial<User['preferences']>) => void;
}

export const useUserStore = create<UserState>()(persist(
  (set) => ({
    user: null,
    isAuthenticated: false,
    setUser: (user) => set({ user, isAuthenticated: true }),
    logout: () => set({ user: null, isAuthenticated: false }),
    updatePreferences: (preferences) => set((state) => ({
      user: state.user ? {
        ...state.user,
        preferences: { ...state.user.preferences, ...preferences }
      } : null
    }))
  }),
  {
    name: 'user-storage'
  }
));

// Cart Store
interface CartItem {
  productId: string;
  productName: string;
  price: number;
  quantity: number;
  numbers?: string[];
  image: string;
}

interface CartState {
  items: CartItem[];
  total: number;
  addItem: (item: CartItem) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  getItemCount: () => number;
}

export const useCartStore = create<CartState>()(persist(
  (set, get) => ({
    items: [],
    total: 0,
    addItem: (item) => set((state) => {
      const existingItem = state.items.find(i => i.productId === item.productId);
      let newItems;
      
      if (existingItem) {
        newItems = state.items.map(i => 
          i.productId === item.productId 
            ? { ...i, quantity: i.quantity + item.quantity }
            : i
        );
      } else {
        newItems = [...state.items, item];
      }
      
      const newTotal = newItems.reduce((sum, i) => sum + (i.price * i.quantity), 0);
      return { items: newItems, total: newTotal };
    }),
    removeItem: (productId) => set((state) => {
      const newItems = state.items.filter(i => i.productId !== productId);
      const newTotal = newItems.reduce((sum, i) => sum + (i.price * i.quantity), 0);
      return { items: newItems, total: newTotal };
    }),
    updateQuantity: (productId, quantity) => set((state) => {
      const newItems = state.items.map(i => 
        i.productId === productId ? { ...i, quantity } : i
      );
      const newTotal = newItems.reduce((sum, i) => sum + (i.price * i.quantity), 0);
      return { items: newItems, total: newTotal };
    }),
    clearCart: () => set({ items: [], total: 0 }),
    getItemCount: () => get().items.reduce((sum, item) => sum + item.quantity, 0)
  }),
  {
    name: 'cart-storage'
  }
));

// DeFi Store
interface DeFiState {
  walletConnected: boolean;
  walletAddress: string | null;
  balance: {
    estelar: number;
    usdc: number;
    eth: number;
  };
  staking: {
    totalStaked: number;
    rewards: number;
    apy: number;
  };
  liquidity: {
    totalProvided: number;
    rewards: number;
    pools: any[];
  };
  connectWallet: (address: string) => void;
  disconnectWallet: () => void;
  updateBalance: (token: string, amount: number) => void;
  updateStaking: (data: Partial<DeFiState['staking']>) => void;
}

export const useDeFiStore = create<DeFiState>()(
  (set) => ({
    walletConnected: false,
    walletAddress: null,
    balance: {
      estelar: 0,
      usdc: 0,
      eth: 0
    },
    staking: {
      totalStaked: 0,
      rewards: 0,
      apy: 0
    },
    liquidity: {
      totalProvided: 0,
      rewards: 0,
      pools: []
    },
    connectWallet: (address) => set({ walletConnected: true, walletAddress: address }),
    disconnectWallet: () => set({ walletConnected: false, walletAddress: null }),
    updateBalance: (token, amount) => set((state) => ({
      balance: { ...state.balance, [token]: amount }
    })),
    updateStaking: (data) => set((state) => ({
      staking: { ...state.staking, ...data }
    }))
  })
);

// App Store
interface AppState {
  theme: 'light' | 'dark';
  language: string;
  currency: string;
  notifications: boolean;
  loading: boolean;
  error: string | null;
  setTheme: (theme: 'light' | 'dark') => void;
  setLanguage: (language: string) => void;
  setCurrency: (currency: string) => void;
  setNotifications: (enabled: boolean) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useAppStore = create<AppState>()(persist(
  (set) => ({
    theme: 'dark',
    language: 'es',
    currency: 'COP',
    notifications: true,
    loading: false,
    error: null,
    setTheme: (theme) => set({ theme }),
    setLanguage: (language) => set({ language }),
    setCurrency: (currency) => set({ currency }),
    setNotifications: (notifications) => set({ notifications }),
    setLoading: (loading) => set({ loading }),
    setError: (error) => set({ error })
  }),
  {
    name: 'app-storage'
  }
));