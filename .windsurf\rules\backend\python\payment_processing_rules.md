# ESTRATIX Agentic Rules: Backend Payment & Invoicing

---

## 1. Overview

These rules govern the handling of payments, subscriptions, and invoicing within the ESTRATIX backend. Security, reliability, and auditability are the primary concerns. These rules apply to all interactions with payment gateways like Stripe, PayPal, or BTCPay Server.

## 2. Core Principles

- **Zero Trust for Sensitive Data**: The system must never store raw credit card numbers, CVC codes, or other unencrypted payment credentials.
- **Idempotency is Mandatory**: All operations that create charges or modify subscriptions must be idempotent to prevent duplicate transactions.
- **Auditability**: Every financial transaction must be logged in a structured, immutable format for auditing and dispute resolution.

## 3. Rules

### 3.1. Security
- **Rule R-BE-003.1**: All interactions with payment provider APIs must use the official, up-to-date SDKs (e.g., `stripe-python`).
- **Rule R-BE-003.2**: API keys for payment providers must be stored securely (e.g., HashiCorp Vault, AWS Secrets Manager) and loaded via environment variables. They must never be committed to version control.
- **Rule R-BE-003.3**: Webhook endpoints must be secured. Verify the signature of every incoming webhook request using the provider's secret key to ensure it originated from the payment gateway.

### 3.2. Reliability
- **Rule R-BE-003.4**: All API calls that create or modify financial state (e.g., `stripe.Charge.create`) must use an idempotency key. The key should be unique and generated deterministically.
- **Rule R-BE-003.5**: Webhook handlers must be designed to be asynchronous and resilient. Acknowledge receipt of the webhook immediately (e.g., return a `200 OK` response) and then process the payload in a background job (e.g., using Celery, RQ). This prevents timeouts from the payment provider.

### 3.3. Data Handling & Invoicing
- **Rule R-BE-003.6**: Store only the non-sensitive identifiers provided by the payment gateway (e.g., Customer ID `cus_...`, Charge ID `ch_...`). Use these IDs to retrieve information from the provider when needed.
- **Rule R-BE-003.7**: For invoicing, generate immutable records that include the user ID, product/service ID, amount, currency, transaction ID, and current status.
- **Rule R-BE-003.8**: All monetary values must be handled using the `Decimal` type or as integers representing the smallest currency unit (e.g., cents) to avoid floating-point precision errors.

## 4. Enforcement

- **Agentic Enforcement**: Agents generating payment processing logic will be primed to use idempotency keys, webhook verification, and secure data handling patterns.
- **Code Review**: All payment-related code is subject to a stringent security review.
- **Logging & Monitoring**: Implement structured logging for all payment events. Set up alerts for failed transactions, webhook verification failures, and other anomalies.
