# Enhanced SSH Deployment Guide for Agency VPS

## 🚀 Overview

This guide provides a comprehensive SSH-based deployment solution for agency VPS deployments, addressing the gaps identified in the current deployment infrastructure and providing enterprise-grade features for client deployments.

## 📋 Key Improvements Implemented

### 1. **Enhanced SSH Automation**
- ✅ Advanced SSH tunneling support with bastion host capability
- ✅ Retry logic with exponential backoff for failed connections
- ✅ SSH key validation and permission checking
- ✅ Multi-environment SSH configuration support
- ✅ Secure file transfer with checksum verification

### 2. **Advanced Domain Management**
- ✅ Comprehensive DNS validation from multiple resolvers
- ✅ Multi-domain SSL certificate support
- ✅ CDN detection and configuration
- ✅ Automatic SSL renewal with monitoring
- ✅ Domain propagation checking

### 3. **Enterprise Security Hardening**
- ✅ Advanced firewall configuration with custom rules
- ✅ Enhanced Fail2Ban with multiple jails and recidive protection
- ✅ Intrusion detection system (AIDE) integration
- ✅ Security headers and CSP implementation
- ✅ Rate limiting and DDoS protection
- ✅ Vulnerability scanning integration
- ✅ WAF (Web Application Firewall) support

### 4. **Comprehensive Monitoring & Alerting**
- ✅ Multi-channel alerting (Slack, Discord, Email, SMS)
- ✅ System health monitoring with configurable thresholds
- ✅ SSL certificate expiry monitoring
- ✅ Performance metrics collection
- ✅ Log aggregation and rotation
- ✅ Failed login attempt monitoring
- ✅ Real-time health checks with retry logic

### 5. **Advanced Deployment Automation**
- ✅ Blue-green deployment strategy
- ✅ Automatic rollback on failure
- ✅ Pre-deployment backup creation
- ✅ Health check validation before switching
- ✅ Multi-environment support (production, staging, development)
- ✅ Git deploy key support for private repositories
- ✅ Build optimization with npm ci and caching

### 6. **Enhanced Backup & Recovery**
- ✅ Automated backup system with retention policies
- ✅ Multiple backup destinations (local, S3, DigitalOcean Spaces)
- ✅ Backup integrity verification
- ✅ One-click rollback functionality
- ✅ Backup manifest generation
- ✅ Offsite backup synchronization

## 🛠 Prerequisites

### Local Environment
```bash
# Required tools
sudo apt-get update
sudo apt-get install -y openssh-client curl jq git bc dig

# Optional tools for enhanced features
sudo apt-get install -y awscli
```

### VPS Requirements

#### Minimum Specifications
- **CPU**: 2 vCPUs
- **RAM**: 4GB
- **Storage**: 40GB SSD
- **OS**: Ubuntu 20.04+ or Debian 11+
- **Network**: 1Gbps connection

#### Recommended Specifications
- **CPU**: 4 vCPUs
- **RAM**: 8GB
- **Storage**: 80GB SSD
- **OS**: Ubuntu 22.04 LTS
- **Network**: 1Gbps connection with DDoS protection

### Access Requirements
- SSH access to VPS (root or sudo user)
- Domain DNS management access
- Valid email for SSL certificates
- Optional: AWS/DigitalOcean credentials for backup storage

## 🚀 Quick Start

### 1. Configuration Setup

```bash
# Clone the repository
git clone https://github.com/estratix/luxcrafts.git
cd luxcrafts/scripts

# Copy and customize the configuration
cp deploy-agency-enhanced.env luxcrafts-production.env

# Edit configuration for your client
nano luxcrafts-production.env
```

### 2. Essential Configuration Variables

```bash
# Client Information
export CLIENT_NAME="luxcrafts"
export CLIENT_DOMAIN="www.luxcrafts.co"
export CLIENT_EMAIL="<EMAIL>"

# VPS Connection
export VPS_HOST="your-vps-ip"
export VPS_USER="root"
export SSH_KEY_PATH="~/.ssh/id_rsa"

# Deployment Settings
export ENVIRONMENT="production"
export DEPLOY_STRATEGY="blue-green"
export GIT_REPO="https://github.com/estratix/luxcrafts.git"
export GIT_BRANCH="main"
```

### 3. Deploy Application

```bash
# Make script executable
chmod +x deploy-agency-enhanced-ssh.sh

# Run deployment with configuration file
./deploy-agency-enhanced-ssh.sh --config luxcrafts-production.env

# Or with command line options
./deploy-agency-enhanced-ssh.sh \
  --env production \
  --domain www.luxcrafts.co \
  --strategy blue-green
```

## 📖 Detailed Usage

### Command Line Options

```bash
# Show help
./deploy-agency-enhanced-ssh.sh --help

# Dry run (validation only)
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env --dry-run

# Deploy with specific strategy
./deploy-agency-enhanced-ssh.sh \
  --config luxcrafts.env \
  --strategy blue-green

# Rollback to previous version
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env --rollback

# Deploy to staging environment
./deploy-agency-enhanced-ssh.sh \
  --config luxcrafts.env \
  --env staging \
  --domain staging.luxcrafts.co
```

### Deployment Strategies

#### 1. Blue-Green Deployment (Recommended)
```bash
# Zero-downtime deployment with automatic rollback
export DEPLOY_STRATEGY="blue-green"
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env
```

**Features:**
- Zero downtime deployment
- Automatic health check validation
- Instant rollback capability
- Environment isolation

#### 2. Standard Deployment
```bash
# Traditional deployment with backup
export DEPLOY_STRATEGY="standard"
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env
```

**Features:**
- Pre-deployment backup
- Service restart
- Health check validation
- Manual rollback available

### SSH Tunnel Configuration

```bash
# Enable SSH tunnel for secure deployment
export SSH_TUNNEL_ENABLED="true"
export SSH_TUNNEL_PORT="8080"

# Optional: Use bastion host
export SSH_BASTION_HOST="bastion.example.com"
export SSH_BASTION_USER="deploy"
```

### Multi-Environment Support

```bash
# Production deployment
export ENVIRONMENT="production"
export CLIENT_DOMAIN="www.luxcrafts.co"

# Staging deployment
export ENVIRONMENT="staging"
export CLIENT_DOMAIN="staging.luxcrafts.co"

# Development deployment
export ENVIRONMENT="development"
export CLIENT_DOMAIN="dev.luxcrafts.co"
```

## 🔒 Security Features

### Enhanced Firewall Configuration

```bash
# Basic firewall (enabled by default)
export FIREWALL_ENABLED="true"

# Custom firewall rules
export CUSTOM_FIREWALL_RULES="allow 8080/tcp comment 'Custom App';deny from ***********/24"
```

### Advanced Fail2Ban Protection

```bash
# Enhanced Fail2Ban configuration
export FAIL2BAN_ENABLED="true"
export FAIL2BAN_BANTIME="3600"    # 1 hour
export FAIL2BAN_MAXRETRY="5"

# Includes protection for:
# - SSH brute force attacks
# - Nginx HTTP auth failures
# - Rate limiting violations
# - Bot search attempts
# - Recidive offenders
```

### Intrusion Detection

```bash
# Enable AIDE intrusion detection
export INTRUSION_DETECTION="true"

# Daily file integrity checks
# Email alerts for unauthorized changes
# Automated baseline updates
```

### Security Headers

```bash
# Comprehensive security headers
export SECURITY_HEADERS_ENABLED="true"
export CSP_ENABLED="true"

# Includes:
# - HSTS with preload
# - X-Frame-Options: DENY
# - X-Content-Type-Options: nosniff
# - Content Security Policy
# - Referrer Policy
# - Permissions Policy
```

## 📊 Monitoring & Alerting

### Health Check Configuration

```bash
# Health check settings
export HEALTH_CHECK_ENABLED="true"
export HEALTH_CHECK_INTERVAL="30"     # seconds
export HEALTH_CHECK_RETRIES="5"
export UPTIME_CHECK_INTERVAL="300"    # 5 minutes
```

### Alert Thresholds

```bash
# System monitoring thresholds
export CPU_ALERT_THRESHOLD="80"       # percentage
export MEMORY_ALERT_THRESHOLD="85"    # percentage
export DISK_ALERT_THRESHOLD="85"      # percentage
export RESPONSE_TIME_THRESHOLD="2000" # milliseconds
```

### Multi-Channel Alerting

```bash
# Slack integration
export SLACK_WEBHOOK="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

# Discord integration
export DISCORD_WEBHOOK="https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK"

# Email alerts
export EMAIL_ALERT_ENDPOINT="https://api.emailservice.com/send"

# SMS alerts (optional)
export SMS_ENABLED="true"
export SMS_PROVIDER="twilio"
export SMS_PHONE_NUMBER="+**********"
export TWILIO_ACCOUNT_SID="your_account_sid"
export TWILIO_AUTH_TOKEN="your_auth_token"
```

## 💾 Backup & Recovery

### Automated Backup System

```bash
# Backup configuration
export BACKUP_ENABLED="true"
export BACKUP_SCHEDULE="0 2 * * *"    # Daily at 2 AM
export BACKUP_RETENTION_DAYS="30"

# Local backup storage
export LOCAL_BACKUP_ENABLED="true"
export LOCAL_BACKUP_PATH="/opt/backups"
```

### Offsite Backup Storage

#### AWS S3 Configuration
```bash
export OFFSITE_BACKUP_ENABLED="true"
export BACKUP_PROVIDER="s3"
export S3_BACKUP_BUCKET="estratix-luxcrafts-backups"
export AWS_ACCESS_KEY_ID="your_access_key"
export AWS_SECRET_ACCESS_KEY="your_secret_key"
export AWS_DEFAULT_REGION="us-east-1"
```

#### DigitalOcean Spaces Configuration
```bash
export BACKUP_PROVIDER="digitalocean"
export DO_SPACES_BUCKET="estratix-luxcrafts-backups"
export DO_SPACES_REGION="nyc3"
export DO_SPACES_ACCESS_KEY="your_access_key"
export DO_SPACES_SECRET_KEY="your_secret_key"
```

### Manual Backup & Restore

```bash
# Create manual backup
ssh root@your-vps "/usr/local/bin/luxcrafts-backup.sh"

# List available backups
ssh root@your-vps "ls -la /opt/backups/luxcrafts/"

# Perform rollback
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env --rollback
```

## 🔧 Advanced Configuration

### Performance Optimization

```bash
# CDN integration
export CDN_INTEGRATION="cloudflare"
export CDN_ENABLED="true"

# Caching strategy
export CACHE_STRATEGY="redis"
export REDIS_HOST="localhost"
export REDIS_PORT="6379"

# Compression
export GZIP_ENABLED="true"
export BROTLI_ENABLED="true"
export HTTP2_ENABLED="true"
```

### Database Integration (Optional)

```bash
# Database configuration
export DATABASE_ENABLED="true"
export DATABASE_TYPE="postgresql"
export DATABASE_HOST="localhost"
export DATABASE_NAME="luxcrafts_prod"
export DATABASE_USER="luxcrafts"

# Database backup
export DB_BACKUP_ENABLED="true"
export DB_BACKUP_SCHEDULE="0 3 * * *"
export DB_BACKUP_RETENTION="14"
```

### CI/CD Integration

```bash
# GitHub Actions integration
export CICD_INTEGRATION="github"
export GITHUB_TOKEN="your_github_token"
export GITHUB_REPO="estratix/luxcrafts"
export AUTO_DEPLOY_ENABLED="true"

# Webhook for automatic deployments
export WEBHOOK_SECRET="your_webhook_secret"
export DEPLOY_ON_PUSH="true"
```

## 🚨 Troubleshooting

### Common Issues

#### 1. SSH Connection Failed
```bash
# Check SSH key permissions
chmod 600 ~/.ssh/id_rsa

# Test SSH connection manually
ssh -i ~/.ssh/id_rsa root@your-vps

# Enable debug mode
export DEBUG="true"
./deploy-agency-enhanced-ssh.sh --config luxcrafts.env --dry-run
```

#### 2. DNS Validation Failed
```bash
# Check DNS propagation
dig www.luxcrafts.co
nslookup www.luxcrafts.co *******

# Force deployment despite DNS mismatch
# (Answer 'y' when prompted during deployment)
```

#### 3. SSL Certificate Issues
```bash
# Check certificate status
ssh root@your-vps "certbot certificates"

# Manually renew certificate
ssh root@your-vps "certbot renew --force-renewal"

# Check certificate expiry
echo | openssl s_client -servername www.luxcrafts.co -connect www.luxcrafts.co:443 2>/dev/null | openssl x509 -noout -dates
```

#### 4. Health Check Failures
```bash
# Check application logs
ssh root@your-vps "tail -f /var/log/nginx/luxcrafts.co.error.log"

# Check system resources
ssh root@your-vps "htop"
ssh root@your-vps "df -h"
ssh root@your-vps "free -h"

# Manual health check
curl -v https://www.luxcrafts.co/health
```

### Log Files

```bash
# Deployment logs
tail -f deployment-$(date +%Y%m%d).log

# Alert logs
tail -f alerts-$(date +%Y%m%d).log

# System logs on VPS
ssh root@your-vps "tail -f /var/log/health-check.log"
ssh root@your-vps "tail -f /var/log/backup.log"
ssh root@your-vps "journalctl -u nginx -f"
```

## 📈 Monitoring Dashboard

### Health Check Endpoints

- **Application Health**: `https://www.luxcrafts.co/health`
- **System Status**: SSH to VPS and run `/opt/monitoring/health-check.sh`
- **Backup Status**: Check `/var/log/backup.log`
- **Security Status**: `fail2ban-client status`

### Key Metrics to Monitor

1. **Uptime**: Application availability
2. **Response Time**: Page load performance
3. **SSL Certificate**: Expiry dates
4. **Disk Space**: Storage utilization
5. **Memory Usage**: RAM consumption
6. **CPU Load**: Processing utilization
7. **Security Events**: Failed login attempts
8. **Backup Status**: Successful backup completion

## 🔄 Maintenance Tasks

### Daily
- Monitor health check alerts
- Review backup completion status
- Check system resource usage

### Weekly
- Review security logs
- Update system packages
- Test SSL certificate renewal

### Monthly
- Review and rotate logs
- Update deployment scripts
- Test rollback procedures
- Security audit and vulnerability scan

## 📞 Support

For issues or questions regarding the enhanced SSH deployment system:

1. **Check Logs**: Review deployment and system logs
2. **Run Diagnostics**: Use `--dry-run` flag for validation
3. **Documentation**: Refer to this guide and inline script comments
4. **Emergency Rollback**: Use `--rollback` flag for immediate recovery

## 🎯 Next Steps

After successful deployment:

1. **Configure Monitoring**: Set up alert channels
2. **Test Rollback**: Verify rollback procedures work
3. **Security Review**: Run security scans
4. **Performance Optimization**: Configure CDN and caching
5. **Documentation**: Update client-specific procedures
6. **Training**: Train client team on monitoring and basic maintenance

---

**Enhanced SSH Deployment System v3.0**  
*Developed by ESTRATIX Infrastructure Team*  
*Enterprise-grade deployment automation for agency VPS deployments*