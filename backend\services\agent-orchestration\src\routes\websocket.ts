import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { authenticateToken, requirePermission } from '@/middleware/auth';
import { WebSocketService } from '@/services/webSocketService';

// Request/Response Schemas
const broadcastMessageSchema = z.object({
  topic: z.string().min(1).max(255),
  message: z.any(),
  targetClientId: z.string().optional()
});

const subscriptionQuerySchema = z.object({
  topic: z.string().optional(),
  clientId: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0)
});

interface WebSocketRouteContext {
  webSocketService: WebSocketService;
}

export async function websocketRoutes(fastify: FastifyInstance) {
  const { webSocketService } = fastify as any as WebSocketRouteContext;

  // Broadcast message to topic subscribers
  fastify.post('/broadcast', {
    preHandler: [authenticateToken, requirePermission('websocket:broadcast')],
    schema: {
      body: broadcastMessageSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { topic, message, targetClientId } = broadcastMessageSchema.parse(request.body);
      
      if (targetClientId) {
        // Send to specific client
        webSocketService.sendToClient(targetClientId, message);
      } else {
        // Broadcast to all subscribers of the topic
        webSocketService.broadcast(topic, message);
      }
      
      return reply.send({
        success: true,
        message: targetClientId 
          ? `Message sent to client ${targetClientId}`
          : `Message broadcasted to topic: ${topic}`
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to broadcast message'
      });
    }
  });

  // Broadcast agent status update
  fastify.post('/broadcast/agent-status', {
    preHandler: [authenticateToken, requirePermission('websocket:broadcast')],
    schema: {
      body: {
        type: 'object',
        properties: {
          agentId: { type: 'string' },
          status: { type: 'string' },
          data: { type: 'object' }
        },
        required: ['agentId', 'status']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { agentId, status, data } = request.body as {
        agentId: string;
        status: string;
        data?: any;
      };
      
      webSocketService.broadcastAgentStatus(agentId, status, data);
      
      return reply.send({
        success: true,
        message: `Agent status broadcasted for agent: ${agentId}`
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to broadcast agent status'
      });
    }
  });

  // Broadcast workflow status update
  fastify.post('/broadcast/workflow-status', {
    preHandler: [authenticateToken, requirePermission('websocket:broadcast')],
    schema: {
      body: {
        type: 'object',
        properties: {
          workflowId: { type: 'string' },
          status: { type: 'string' },
          data: { type: 'object' }
        },
        required: ['workflowId', 'status']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { workflowId, status, data } = request.body as {
        workflowId: string;
        status: string;
        data?: any;
      };
      
      webSocketService.broadcastWorkflowStatus(workflowId, status, data);
      
      return reply.send({
        success: true,
        message: `Workflow status broadcasted for workflow: ${workflowId}`
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to broadcast workflow status'
      });
    }
  });

  // Broadcast task update
  fastify.post('/broadcast/task-update', {
    preHandler: [authenticateToken, requirePermission('websocket:broadcast')],
    schema: {
      body: {
        type: 'object',
        properties: {
          taskId: { type: 'string' },
          status: { type: 'string' },
          data: { type: 'object' }
        },
        required: ['taskId', 'status']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { taskId, status, data } = request.body as {
        taskId: string;
        status: string;
        data?: any;
      };
      
      webSocketService.broadcastTaskUpdate(taskId, status, data);
      
      return reply.send({
        success: true,
        message: `Task update broadcasted for task: ${taskId}`
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to broadcast task update'
      });
    }
  });

  // Get WebSocket service status and metrics
  fastify.get('/status', {
    preHandler: [authenticateToken, requirePermission('websocket:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                connectedClients: { type: 'number' },
                totalSubscriptions: { type: 'number' },
                activeTopics: { type: 'number' },
                timestamp: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const status = await webSocketService.getStatus();
      const metrics = await webSocketService.getMetrics();
      
      return reply.send({
        success: true,
        data: {
          status,
          ...metrics,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve WebSocket status'
      });
    }
  });

  // Get WebSocket metrics
  fastify.get('/metrics', {
    preHandler: [authenticateToken, requirePermission('websocket:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                connectedClients: { type: 'number' },
                totalSubscriptions: { type: 'number' },
                activeTopics: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metrics = await webSocketService.getMetrics();
      
      return reply.send({
        success: true,
        data: metrics
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve WebSocket metrics'
      });
    }
  });

  // Send notification to specific user or organization
  fastify.post('/notify', {
    preHandler: [authenticateToken, requirePermission('websocket:broadcast')],
    schema: {
      body: {
        type: 'object',
        properties: {
          type: {
            type: 'string',
            enum: ['info', 'success', 'warning', 'error']
          },
          title: { type: 'string' },
          message: { type: 'string' },
          data: { type: 'object' },
          targetUserId: { type: 'string' },
          targetOrganizationId: { type: 'string' },
          persistent: { type: 'boolean', default: false }
        },
        required: ['type', 'title', 'message']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const {
        type,
        title,
        message,
        data,
        targetUserId,
        targetOrganizationId,
        persistent
      } = request.body as {
        type: string;
        title: string;
        message: string;
        data?: any;
        targetUserId?: string;
        targetOrganizationId?: string;
        persistent?: boolean;
      };
      
      const notification = {
        type: 'notification',
        notification: {
          id: `notif_${Date.now()}`,
          type,
          title,
          message,
          data,
          persistent,
          timestamp: new Date().toISOString()
        }
      };
      
      if (targetUserId) {
        // Send to specific user (would need user-to-client mapping)
        webSocketService.broadcast(`user:${targetUserId}`, notification);
      } else if (targetOrganizationId) {
        // Send to all users in organization
        webSocketService.broadcast(`org:${targetOrganizationId}`, notification);
      } else {
        // Send to all connected clients
        webSocketService.broadcast('notifications', notification);
      }
      
      return reply.send({
        success: true,
        message: 'Notification sent successfully'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to send notification'
      });
    }
  });

  // Get connection info (for debugging)
  fastify.get('/connections', {
    preHandler: [authenticateToken, requirePermission('websocket:admin')],
    schema: {
      querystring: subscriptionQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalConnections: { type: 'number' },
                connections: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      clientId: { type: 'string' },
                      userId: { type: 'string' },
                      organizationId: { type: 'string' },
                      subscriptions: {
                        type: 'array',
                        items: { type: 'string' }
                      },
                      lastPing: { type: 'string' },
                      isAlive: { type: 'boolean' }
                    }
                  }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = subscriptionQuerySchema.parse(request.query);
      
      // This would require exposing client information from WebSocketService
      // For now, return basic metrics
      const metrics = await webSocketService.getMetrics();
      
      return reply.send({
        success: true,
        data: {
          totalConnections: metrics.connectedClients,
          connections: [], // Would need to implement client listing in WebSocketService
        },
        pagination: {
          total: metrics.connectedClients,
          limit: query.limit,
          offset: query.offset
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve connection information'
      });
    }
  });
}