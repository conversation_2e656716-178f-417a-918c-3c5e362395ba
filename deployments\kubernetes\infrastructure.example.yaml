# ESTRATIX Infrastructure Configuration Example
# Copy this file to infrastructure.yaml and customize for your environment

# Project Information
project:
  name: "estratix-production"
  environment: "production"
  region: "us-east-1"
  domain: "estratix.yourdomain.com"
  contact_email: "<EMAIL>"

# VPS Node Configuration
vps_nodes:
  # Control Plane Node (Kubernetes Master + Coolify)
  - name: "control-plane"
    provider: "digitalocean"  # digitalocean, linode, vultr, aws, gcp, azure
    size: "s-4vcpu-8gb"       # Provider-specific instance size
    region: "nyc3"            # Provider-specific region
    role: "master"
    ip_address: ""            # Leave empty for auto-assignment
    ssh_key_path: "~/.ssh/estratix_key"
    tags:
      - "estratix"
      - "control-plane"
      - "kubernetes-master"
    services:
      - "kubernetes-master"
      - "coolify"
      - "prometheus"
      - "vault"
    
  # Worker Node 1
  - name: "worker-1"
    provider: "digitalocean"
    size: "s-2vcpu-4gb"
    region: "nyc3"
    role: "worker"
    ip_address: ""
    ssh_key_path: "~/.ssh/estratix_key"
    tags:
      - "estratix"
      - "worker"
      - "kubernetes-worker"
    services:
      - "kubernetes-worker"
      - "grafana"
      - "applications"
    
  # Worker Node 2
  - name: "worker-2"
    provider: "digitalocean"
    size: "s-2vcpu-4gb"
    region: "nyc3"
    role: "worker"
    ip_address: ""
    ssh_key_path: "~/.ssh/estratix_key"
    tags:
      - "estratix"
      - "worker"
      - "kubernetes-worker"
    services:
      - "kubernetes-worker"
      - "monitoring"
      - "applications"
    
  # Load Balancer Node (Optional)
  - name: "load-balancer"
    provider: "digitalocean"
    size: "s-1vcpu-2gb"
    region: "nyc3"
    role: "loadbalancer"
    ip_address: ""
    ssh_key_path: "~/.ssh/estratix_key"
    tags:
      - "estratix"
      - "load-balancer"
    services:
      - "nginx"
      - "certbot"

# Kubernetes Configuration
kubernetes:
  version: "1.28.0"
  cni: "calico"              # calico, flannel, weave
  ingress: "nginx"           # nginx, traefik, istio
  storage_class: "local-path" # local-path, nfs, ceph
  
  # Cluster Configuration
  cluster:
    name: "estratix-cluster"
    pod_subnet: "**********/16"
    service_subnet: "*********/12"
    dns_domain: "cluster.local"
    
  # Add-ons
  addons:
    dashboard: true
    metrics_server: true
    cert_manager: true
    argocd: true
    istio: false
    
  # Resource Limits
  resources:
    default_cpu_request: "100m"
    default_memory_request: "128Mi"
    default_cpu_limit: "500m"
    default_memory_limit: "512Mi"

# Coolify Configuration
coolify:
  domain: "coolify.yourdomain.com"
  email: "<EMAIL>"
  version: "latest"
  
  # SSL Configuration
  ssl:
    enabled: true
    email: "<EMAIL>"
    
  # Database Configuration
  database:
    type: "postgresql"  # postgresql, mysql
    backup_enabled: true
    backup_schedule: "0 2 * * *"  # Daily at 2 AM
    
  # Projects Configuration
  projects:
    - name: "estratix-frontend"
      repository: "https://github.com/your-org/estratix-frontend.git"
      branch: "main"
      domain: "app.yourdomain.com"
      build_command: "npm run build"
      start_command: "npm start"
      
    - name: "estratix-api"
      repository: "https://github.com/your-org/estratix-api.git"
      branch: "main"
      domain: "api.yourdomain.com"
      build_command: "pip install -r requirements.txt"
      start_command: "python app.py"
      
    - name: "estratix-workers"
      repository: "https://github.com/your-org/estratix-workers.git"
      branch: "main"
      domain: "workers.yourdomain.com"
      build_command: "pip install -r requirements.txt"
      start_command: "celery worker -A app.celery"

# Monitoring Configuration
monitoring:
  prometheus:
    retention: "30d"
    storage_size: "50Gi"
    scrape_interval: "15s"
    
  grafana:
    admin_user: "admin"
    admin_password: "secure-password-change-me"
    storage_size: "10Gi"
    
  alertmanager:
    storage_size: "5Gi"
    
  # Alert Channels
  alerts:
    slack:
      enabled: false
      webhook_url: ""
      channel: "#alerts"
      
    email:
      enabled: true
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "app-password"
      
    discord:
      enabled: false
      webhook_url: ""

# Security Configuration
security:
  # SSH Configuration
  ssh:
    port: 22
    disable_root_login: true
    disable_password_auth: true
    
  # Firewall Configuration
  firewall:
    enabled: true
    default_policy: "deny"
    allowed_ports:
      - 22    # SSH
      - 80    # HTTP
      - 443   # HTTPS
      - 6443  # Kubernetes API
      - 2379  # etcd
      - 2380  # etcd
      - 10250 # kubelet
      - 10251 # kube-scheduler
      - 10252 # kube-controller-manager
      
  # SSL/TLS Configuration
  ssl:
    provider: "letsencrypt"  # letsencrypt, cloudflare, custom
    email: "<EMAIL>"
    
  # Vault Configuration
  vault:
    enabled: true
    storage_backend: "file"  # file, consul, etcd
    
  # Network Policies
  network_policies:
    enabled: true
    default_deny: true

# Backup Configuration
backup:
  enabled: true
  schedule: "0 3 * * *"  # Daily at 3 AM
  retention_days: 30
  
  # Storage Configuration
  storage:
    type: "s3"  # s3, gcs, azure, local
    bucket: "estratix-backups"
    region: "us-east-1"
    
  # What to backup
  targets:
    - "kubernetes-etcd"
    - "postgresql-databases"
    - "application-data"
    - "configuration-files"

# Logging Configuration
logging:
  # ELK Stack Configuration
  elasticsearch:
    enabled: true
    storage_size: "100Gi"
    replicas: 1
    
  logstash:
    enabled: true
    
  kibana:
    enabled: true
    
  # Log Retention
  retention:
    application_logs: "7d"
    system_logs: "30d"
    audit_logs: "90d"

# CI/CD Configuration
cicd:
  # ArgoCD Configuration
  argocd:
    enabled: true
    admin_password: "secure-password-change-me"
    
  # GitOps Repositories
  repositories:
    - url: "https://github.com/your-org/estratix-gitops.git"
      type: "git"
      
  # Webhook Configuration
  webhooks:
    github:
      enabled: true
      secret: "webhook-secret-change-me"
      
    gitlab:
      enabled: false
      secret: ""

# Scaling Configuration
scaling:
  # Horizontal Pod Autoscaler
  hpa:
    enabled: true
    min_replicas: 1
    max_replicas: 10
    target_cpu_utilization: 70
    
  # Vertical Pod Autoscaler
  vpa:
    enabled: false
    
  # Cluster Autoscaler
  cluster_autoscaler:
    enabled: false
    min_nodes: 3
    max_nodes: 10

# Cost Optimization
cost_optimization:
  # Spot Instances (if supported by provider)
  spot_instances:
    enabled: false
    max_price: "0.05"
    
  # Resource Optimization
  resource_optimization:
    enabled: true
    cpu_threshold: 80
    memory_threshold: 80
    
  # Scheduled Scaling
  scheduled_scaling:
    enabled: false
    business_hours:
      start: "09:00"
      end: "18:00"
      timezone: "UTC"
      scale_factor: 2

# Disaster Recovery
disaster_recovery:
  enabled: true
  
  # Multi-Region Setup
  multi_region:
    enabled: false
    regions:
      - "us-east-1"
      - "us-west-2"
      
  # Backup Strategy
  backup_strategy:
    frequency: "daily"
    cross_region_replication: false
    
  # Recovery Objectives
  rto: "4h"  # Recovery Time Objective
  rpo: "1h"  # Recovery Point Objective

# Compliance Configuration
compliance:
  # Standards
  standards:
    - "SOC2"
    - "GDPR"
    - "HIPAA"
    
  # Audit Configuration
  audit:
    enabled: true
    retention: "2y"
    
  # Data Protection
  data_protection:
    encryption_at_rest: true
    encryption_in_transit: true
    
# Notification Configuration
notifications:
  # Deployment Notifications
  deployment:
    enabled: true
    channels:
      - "slack"
      - "email"
      
  # Alert Notifications
  alerts:
    enabled: true
    severity_levels:
      - "critical"
      - "warning"
      
  # Maintenance Notifications
  maintenance:
    enabled: true
    advance_notice: "24h"

# Development Configuration
development:
  # Local Development
  local:
    enabled: true
    docker_compose: true
    
  # Staging Environment
  staging:
    enabled: true
    auto_deploy: true
    
  # Testing
  testing:
    unit_tests: true
    integration_tests: true
    e2e_tests: true
    performance_tests: false

# Custom Configuration
custom:
  # Add any custom configuration here
  # This section will be passed to custom scripts
  example_setting: "value"
  another_setting: 123