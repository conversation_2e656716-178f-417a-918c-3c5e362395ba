# ESTRATIX Agentic Rules: Observability and Monitoring
# Rule ID: R-DO-005

---

## 1. Core Principles

This document defines the agentic rules for implementing comprehensive observability across all ESTRATIX applications and infrastructure. The goal is to provide deep visibility into system health, performance, and behavior to enable proactive monitoring, automated diagnostics, and rapid incident response.

- **Principle 1: The Three Pillars of Observability**
  - **Rule:** All services and applications MUST expose standardized Logs, Metrics, and Traces. These three pillars are non-negotiable for any component deployed in the ESTRATIX ecosystem.
  - **Enforcement:** DevOps and MLOps agents will enforce this through CI/CD pipeline checks. Applications lacking proper instrumentation will fail the deployment pipeline. A central observability platform (e.g., Grafana, Prometheus, OpenTelemetry Collector) will be used for aggregation.

- **Principle 2: Structured, Actionable Logging**
  - **Rule:** Logs MUST be structured (e.g., JSON format) and contain sufficient context (e.g., trace ID, user ID, service name) to be machine-parseable and useful for debugging without requiring access to the source code.
  - **Enforcement:** Agentic code generation will use standardized logging libraries. CI/CD linting will check for unstructured log statements. A central log aggregation service (e.g., Loki, OpenSearch) will be used.

- **Principle 3: Standardized Metrics**
  - **Rule:** Services MUST expose key performance indicators (KPIs) as metrics in a standardized format (e.g., Prometheus). This includes the RED method (Rate, Errors, Duration) for services and USE method (Utilization, Saturation, Errors) for resources.
  - **Enforcement:** Service templates will include a default metrics endpoint. DevOps agents will configure Prometheus to scrape these endpoints and set up standard alerting rules in Alertmanager.

- **Principle 4: End-to-End Distributed Tracing**
  - **Rule:** All inter-service communication MUST propagate trace context to enable end-to-end distributed tracing. This is critical for understanding request flows and identifying performance bottlenecks in a microservices architecture.
  - **Enforcement:** Service meshes (e.g., Istio) or OpenTelemetry SDKs will be used to automate trace context propagation. MLOps agents will use tools like Langfuse or Arize AI for tracing LLM application chains.

---

## 2. Specific Rules

### 2.1. Logging
- **Rule R-DO-005.1 (Log Levels):** Use standard log levels (DEBUG, INFO, WARN, ERROR, FATAL). Production environments SHOULD be configured to log at INFO level or higher.
- **Rule R-DO-005.2 (No Sensitive Data):** Personally Identifiable Information (PII) and other sensitive data MUST NOT be written to logs. Use masking or redaction techniques enforced by security agents.

### 2.2. Metrics
- **Rule R-DO-005.3 (Dashboarding):** Every service MUST have a corresponding Grafana dashboard visualizing its key RED metrics. These dashboards will be provisioned as code.
- **Rule R-DO-005.4 (Alerting):** Critical alerts MUST be defined for key metrics (e.g., high error rate, high latency). Alerts must be actionable and include links to relevant dashboards or runbooks.

### 2.3. Tracing
- **Rule R-DO-005.5 (Trace Sampling):** In high-traffic environments, trace sampling may be used. The sampling strategy (e.g., probabilistic, rate-limiting) MUST be documented and configured to capture representative data, especially for errors.

---

## 3. Enforcement by ESTRATIX Agents

- **DevOps Agents:** Manage the observability stack (Prometheus, Grafana, Loki), CI/CD enforcement, and automated dashboard/alert provisioning.
- **MLOps/LLMOps Agents:** Ensure specialized observability for AI/ML models and LLM chains, integrating tools like Langfuse and Arize AI.
- **SecOps Agents:** Scan for sensitive data in logs and ensure secure configuration of the observability stack.
