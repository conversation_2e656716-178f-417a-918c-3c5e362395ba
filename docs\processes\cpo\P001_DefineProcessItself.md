# ESTRATIX Process Definition: Define ESTRATIX Process Itself

## 1. Process Metadata

*   **Process ID:** `P001`
*   **Process Name:** `Define ESTRATIX Process Itself`
*   **Responsible Command Office:** `[e.g., Chief Process Officer (CPO), Chief Project Officer (CPrO)]`
*   **Responsible Command Office Acronym:** `[e.g., cpo, cpro]`
*   **Version:** `1.1`
*   **Creation Date:** `2025-05-09`
*   **Last Updated:** `YYYY-MM-DD`
*   **Owner/Maintainer:** `Process Engineering Team / ESTRATIX Core Devs`
*   **Status:** `Active`
*   **Related ESTRATIX Global Rule(s):** `Section on Documentation Standards in estratix-dev-rules.md`
*   **Related Documents:** `docs/templates/estratix_process_definition_template.md`, `docs/processes/process_matrix.md`

## 2. Purpose

*   **Purpose:** To establish a standardized, consistent, and maintainable method for defining, documenting, and managing all operational and developmental processes within the ESTRATIX framework.

## 3. Goal

*   **Goal(s):**
    *   Ensure all ESTRATIX processes are clearly documented using the `estratix_process_definition_template.md`.
    *   Maintain an up-to-date `process_matrix.md` as a central registry for all processes.
    *   Facilitate understanding, execution, and improvement of processes across different teams and agentic frameworks.
    *   Enable clear mapping of abstract processes to concrete implementations in Windsurf, CrewAI, Pydantic-AI, Aider, etc.

## 4. Scope

*   **In Scope:**
    *   Creation of new process definitions using the standard template.
    *   Updating existing process definitions.
    *   Registering processes in the `process_matrix.md`.
    *   Creating stub Mermaid diagrams for process visualization.
    *   Versioning and review of process definitions.
*   **Out of Scope:**
    *   The actual execution of the business/technical processes being defined (this process only defines *how to define* them).
    *   Detailed implementation of agentic framework specifics (this process ensures the definition *considers* them via mapping).

## 5. Triggers

*   Identification of a new operational or developmental need requiring a standardized process.
*   Requirement to update an existing process due to changes in technology, strategy, or lessons learned.
*   Onboarding of a new agentic framework requiring mapping of existing processes.

## 6. Inputs

*   **Input Name:** `Process Requirement Specification`
    *   Description: User-provided text, Markdown document, or verbal description detailing the need for a new process.
    *   Source/Format: `ESTRATIX Team Member, Project Need, Strategic Initiative` / `Text, Markdown`
    *   Data Format & Structure (e.g., JSON, Parquet; Pydantic models; Pandas DataFrame requirements): `Typically unstructured text, but could be a Pydantic model if generated by an upstream agent.`
*   **Input Name:** `estratix_process_definition_template.md`
    *   Description: The official template to be used for defining processes.
    *   Source/Format: `docs/templates/estratix_process_definition_template.md` / `Markdown File Path`
    *   Data Format & Structure: `N/A (File Content)`
*   **Input Name:** `Agentic Framework Documentation (Optional but Recommended)`
    *   Description: Documentation for relevant agentic frameworks to inform mapping.
    *   Source/Format: `Official docs for CrewAI, Pydantic-AI, PocketFlow, Aider, Windsurf, etc.` / `URLs, File Paths, Ingested Knowledge from VectorDB`
    *   Data Format & Structure: `N/A (Documentation Content)`

## 7. Outputs / Deliverables

*   **Output Name:** `Completed Process Definition Document`
    *   Description: A fully populated Markdown file defining the new or updated process.
    *   Destination/Format: `docs/processes/definitions/[PXXX]_[ProcessName].md` / `Markdown File`
    *   Data Format & Structure (e.g., JSON, Parquet; Pydantic models; Reporting formats): `Markdown content following the standard template. Specific data outputs of the *defined process* would be detailed within *that process's* definition.`
*   **Output Name:** `Updated Process Matrix Entry`
    *   Description: A row in the central process matrix accurately reflecting the new/updated process.
    *   Destination/Format: `docs/processes/process_matrix.md` / `Row in Markdown Table`
    *   Data Format & Structure: `Text entries in Markdown table.`
*   **Output Name:** `Stub Mermaid Diagram File`
    *   Description: A basic MMD file containing the initial structure for the process diagram.
    *   Destination/Format: `docs/processes/diagrams/[PXXX]_[ProcessName].mmd` / `MMD File`
    *   Data Format & Structure: `Mermaid syntax text.`

## 8. Roles & Responsibilities

| Role                               | Responsibility                                                                                                |
| :--------------------------------- | :------------------------------------------------------------------------------------------------------------ |
| `Process Definer (Human or Agent)` | Gathers requirements, drafts the process definition using the template, coordinates review, and finalizes.        |
| `Process Owner/Stakeholder(s)`     | Provide input, review drafts, approve the final process definition.                                           |
| `Process Engineering Team (PET)`   | Maintains the `estratix_process_definition_template.md` and `process_matrix.md`, provides guidance.         |
| `Agentic Framework Specialist(s)`  | Provide input and review for the 'Agentic Framework Mapping' section.                                       |

## 9. High-Level Steps

1.  **Initiation & Scoping:** Receive process requirement, assign preliminary Process ID, discuss initial scope and goals with stakeholders.
    *   Key Activities: Initial discussion, problem/opportunity clarification, high-level objective setting.
    *   Inputs: `Process Requirement Specification`.
    *   Outputs: `Draft Scope, Preliminary Process ID`.
    *   Primary Role(s): `Process Definer, Process Owner/Stakeholder(s)`.
2.  **Information Gathering:** Collect detailed information about the process, including existing practices, desired state, inputs, outputs, roles, tools, and potential agentic framework mappings by consulting relevant documentation or experts.
    *   Key Activities: Research, interviews, documentation review (including agentic framework docs).
    *   Inputs: `Draft Scope`, `Agentic Framework Documentation`.
    *   Outputs: `Collected Process Details`.
    *   Primary Role(s): `Process Definer, Agentic Framework Specialist(s)`.
3.  **Draft Definition:** Populate the `estratix_process_definition_template.md` with the collected information. Create a stub Mermaid diagram.
    *   Key Activities: Writing, structuring content, initial diagramming.
    *   Inputs: `estratix_process_definition_template.md`, `Collected Process Details`.
    *   Outputs: `Draft Process Definition Document`, `Stub Mermaid Diagram File`.
    *   Primary Role(s): `Process Definer`.
4.  **Review & Refinement:** Circulate the draft definition for review by stakeholders and relevant experts (e.g., agentic framework specialists). Incorporate feedback.
    *   Key Activities: Review meetings, feedback collection, iterative updates to the draft.
    *   Inputs: `Draft Process Definition Document`.
    *   Outputs: `Refined Process Definition Document`.
    *   Primary Role(s): `Process Definer, Process Owner/Stakeholder(s), Agentic Framework Specialist(s)`.
5.  **Finalization & Registration:** Obtain final approval. Update the `process_matrix.md` with the new or updated process details.
    *   Key Activities: Approval, matrix update, versioning.
    *   Inputs: `Refined Process Definition Document`, `Approval from Process Owner`.
    *   Outputs: `Finalized Process Definition Document`, `Updated Process Matrix Entry`.
    *   Primary Role(s): `Process Definer, Process Owner, Process Engineering Team`.

## 10. Tools, Technologies & MCPs

*   `Markdown Editor` (e.g., VS Code, Obsidian)
*   `Windsurf IDE` (for file creation/editing via `write_to_file`, `edit_file` tools, and workflow execution)
*   `Git` (for version control of process documents)
*   `LLMs / AI Assistants` (e.g., Cascade, Aider, for assistance in drafting, refining, or mapping process descriptions)
*   `Mermaid` (for diagramming - actual detailed diagramming might be a follow-up task)
*   `Vector Database / Knowledge Base` (e.g., Milvus/Qdrant, for ingesting and querying agentic framework documentation)

## 11. Key Performance Indicators (KPIs) / Metrics

*   **Clarity & Completeness Score:** (e.g., Peer review score on a 1-5 scale for how well the definition meets template standards)
*   **Time to Define Process:** (e.g., Average time from initiation to finalization for a new process definition)
*   **Template Adherence Rate:** (e.g., Percentage of active processes correctly using the latest template version)
*   **Stakeholder Satisfaction with Definition:** (e.g., Survey score from process owners on the usefulness of the definition)

## 12. Dependencies & Interrelationships

*   **Upstream Processes:** `N/A (This is a foundational process, though it can be triggered by needs from any other area)`
*   **Downstream Processes:** `All other ESTRATIX processes rely on this for their definition and standardization.`
*   **Parallel Processes:** `PXXX_IngestFrameworkDocumentation` (as effective agentic mapping relies on up-to-date knowledge)

### Project Management Integration Points
*   **Primary Project Management Phase(s) this process belongs to:** `Initiation` (when defining PM processes), `Planning` (when defining processes for a specific project's execution), `Organizational Process Definition Efforts`.
*   **Key Project Management Templates (from `docs/templates/project_management/`) used as Inputs:** `N/A directly, but the need for a process often arises from project activities.`
*   **Key Project Management Templates (from `docs/templates/project_management/`) generated or updated as Outputs:** `N/A directly, but defined processes inform how PM templates are used.`
*   **Contribution to Project Progress Tracking Mechanisms (e.g., updates dashboard, logs to master project):** `Ensures standardized processes are available, which is a prerequisite for effective project tracking and execution.`

## 13. Exception Handling & Escalation Paths

*   `Template file not found or outdated: Escalate to Process Engineering Team to update/provide the correct template path.`
*   `Process ID collision during assignment: Implement a central check or manual assignment protocol by PET to ensure uniqueness before finalizing.`
*   `Incomplete information provided for definition: Process Definer must seek clarification or mark sections as 'TBD' before proceeding with formal review. Escalate to Process Owner if blocked.`
*   `Disagreements during review: Facilitated discussion led by Process Owner or PET. If unresolved, escalate to relevant Command Officer.`

## 14. PDCA (Plan-Do-Check-Act) & Continuous Improvement

*   **Plan:** Regularly review the effectiveness of this 'Define ESTRATIX Process Itself' process and the underlying `estratix_process_definition_template.md`.
*   **Do:** Implement updates to the template or this process definition based on feedback, new ESTRATIX capabilities, or changes in best practices for process definition.
*   **Check:** Monitor KPIs (see Section 11). Solicit feedback from users of the template (Process Definers).
*   **Act:** Standardize successful improvements. Update documentation and communicate changes to relevant ESTRATIX teams.
*   **Review Cycle:** `Annually, or when major changes to ESTRATIX, its supported agentic frameworks, or core tooling occur.`
*   **Improvement Log:** `(To be maintained within this document or a linked file)`
    *   **Date:** `YYYY-MM-DD` | **Issue/Observation:** `Aligned with enhanced base template, added detailed prompts and new sections (KPIs, PM Integration, Observability).` | **Proposed Change:** `N/A` | **Action Taken:** `N/A` | **Result:** `N/A`

## 15. Agentic Framework Mapping (Critical)

*(This section details how this P001 process - defining other processes - can itself be assisted or executed by agentic frameworks.)*

### 15.1 Windsurf Workflows
*   **Primary Workflow(s):**
    *   `/wf_define_new_estratix_process.md`: (Orchestrates the entire lifecycle of defining a new process, from ID assignment to final registration, leveraging other tools/agents.)
    *   `/wf_ingest_framework_documentation.md`: (Relevant for ensuring the 'AgenticFrameworkMapping' section of any new process can be accurately populated.)
*   **Key Workflow Steps Mapping to Process Steps:**
    *   Process Step 9.1 (Initiation) -> `wf_define_new_estratix_process.md` Step 1 (Identify Need), Step 2 (Assign ID).
    *   Process Step 9.3 (Draft Definition) -> `wf_define_new_estratix_process.md` Step 4 (Copy Template), invokes file write tools.
*   **Agents Involved via Workflows:**
    *   `AGENT_DocumentationSpecialist` (Conceptual, for drafting/populating template sections)
    *   `AGENT_KnowledgeManager` (Conceptual, for retrieving framework details for mapping)
*   **Structured Data Handling:** `Process requirements might be passed as structured JSON to a workflow. Workflow outputs (paths to created files) are strings.`
*   **Task Breakdown & Agent Assignment (if applicable):** `The /wf_define_new_estratix_process.md itself is the task breakdown. Agents are invoked for specific steps like 'copy template', 'edit file for matrix update'.`
*   **Leveraging External AI/Specialized Libraries:** `The workflow can call LLMs (via Cascade) for drafting text or suggesting mappings.`

### 15.2 CrewAI Implementation (Conceptual / Actual)
*   **Primary Crew(s):** `ProcessDefinitionSupportCrew`
*   **Key Agents in Crew(s) & Role Mapping:**
    *   CrewAI Agent: `ProcessRequirementsCollectorAgent` -> Process Role: `Process Definer (initial phase)` / Supports Process Step(s): `9.1, 9.2`
    *   CrewAI Agent: `ProcessTemplateDrafterAgent` (uses `estratix_process_definition_template.md`) -> Process Role: `Process Definer (drafting phase)` / Supports Process Step(s): `9.3`
    *   CrewAI Agent: `FrameworkMappingAdvisorAgent` (knowledge of CrewAI, Pydantic-AI, etc.) -> Process Role: `Agentic Framework Specialist` / Supports Process Step(s): `9.2, 9.3 (for mapping section)`
*   **Key Tasks in Crew(s):**
    *   CrewAI Task: `GatherAndStructureProcessNeed` -> Corresponds to Process Activity: `Interviewing stakeholders, structuring initial requirements.`
    *   CrewAI Task: `DraftProcessDocumentFromTemplate` -> Corresponds to Process Activity: `Populating the .md template.`
    *   CrewAI Task: `SuggestAgenticMappings` -> Corresponds to Process Activity: `Populating section 15 of the new process definition.`
*   **Tools Utilized by CrewAI Agents:** `File Read/Write tools, Template Access tool, LLM for drafting, Knowledge Base Search Tool (for agentic framework docs).`
*   **Structured Data Handling:** `Pydantic models for ProcessRequirement, ProcessDefinitionDraft. Agents exchange these models.`
*   **Task Breakdown & Agent Assignment (if applicable):** `Tasks are assigned based on agent specialization (collection, drafting, advising).`
*   **Leveraging External AI/Specialized Libraries:** `FrameworkMappingAdvisorAgent might query external LLMs or vector stores for latest framework practices.`

### 15.3 Pydantic-AI Implementation (Conceptual / Actual)
*   **Primary Pydantic-AI Model(s)/Agent(s):** `ProcessDefinitionGeneratorAgent` (takes structured requirements and outputs a populated ProcessDefinitionModel instance)
*   **Functionality Mapping to Process Steps:**
    *   `ProcessDefinitionGeneratorAgent.generate_definition(req: ProcessRequirementModel)` -> Supports Process Step(s): `9.3 (Draft Definition)`
*   **Data Structures (Pydantic Models):**
    ```python
    # from pydantic import BaseModel, Field, List, Dict, Optional
    # class AgenticFrameworkMappingDetails(BaseModel):
    #     windsurf_workflow_file: Optional[str] = None
    #     crewai_crews: List[str] = []
    #     # ... other framework specific mapping fields
    # class ProcessRequirementModel(BaseModel):
    #     process_need_description: str
    #     initial_goals: List[str]
    #     scope_notes: str
    #     # ... other fields to capture detailed requirements for a process
    # class ProcessDefinitionModel(BaseModel):
    #     process_id: str
    #     process_name: str
    #     version: str = "0.1"
    #     # ... all other fields from the template ...
    #     agentic_framework_mapping: AgenticFrameworkMappingDetails
    # class ProcessArtifactsOutput(BaseModel):
    #     definition_file_content: str # Markdown content
    #     matrix_entry_line: str
    #     diagram_stub_content: str # MMD content
    ```
*   **Structured Data Handling:** `Relies heavily on Pydantic models for all inputs, internal states, and outputs.`
*   **Task Breakdown & Agent Assignment (if applicable):** `A single Pydantic-AI agent might orchestrate the generation, or it could be a graph of them.`
*   **Leveraging External AI/Specialized Libraries:** `LLM calls for text generation within the Pydantic-AI agent would be common.`

### 15.4 Aider Integration (Conceptual / Actual)
*   **Aider Task Specification Template(s) Used:** `Potentially one for bootstrapping a new .md file from the template, or for generating code snippets for Pydantic models in section 15.3.`
*   **Process Steps Supported by Aider Commands:**
    *   Process Step 9.3 (Draft Definition) -> Aider command: `aider --message "Create a new file [PXXX]_[ProcessName].md based on docs/templates/estratix_process_definition_template.md and populate metadata section with ID=[PXXX], Name='[ProcessName]'" docs/processes/definitions/`
*   **Structured Data Handling:** `Aider primarily works with file content; structured data would be serialized into prompts or file inclusions.`
*   **Task Breakdown & Agent Assignment (if applicable):** `Aider tasks would be specific file manipulation or code generation actions.`
*   **Leveraging External AI/Specialized Libraries:** `Aider is the external AI in this context.`

### 15.5 Other Frameworks (e.g., PocketFlow - Conceptual)
*   **PocketFlow Workflow Name:** `"DefineNewEstratixProcessWorkflow"`
*   **Key Data Structures / Objects:** `"ProcessRequirementNode", "ProcessDefinitionNode", "FrameworkMappingHintsNode"`
*   **Structured Data Handling:** `PocketFlow nodes would exchange JSON-compatible data objects.`
*   **Task Breakdown & Agent Assignment (if applicable):** `Graph nodes would represent stages like 'GatherRequirements', 'DraftFromTemplate', 'AddFrameworkMappings'.`
*   **Leveraging External AI/Specialized Libraries:** `Nodes could encapsulate calls to LLMs or other specialized Python libraries.`

## 16. Notes & Considerations

*   Assumes the `estratix_process_definition_template.md` is the single source of truth for process structure. Any deviations for specific processes should be justified here.
*   Effective `AgenticFrameworkMapping` relies on up-to-date, ingested knowledge of the target frameworks. Ingestion of framework documentation (CrewAI, Pydantic-AI, etc.) via `wf_ingest_framework_documentation.md` is a parallel, critical activity.
*   The creation of detailed Mermaid diagrams is a subsequent task once the textual definition is stable.
*   Consideration for multi-project/master-subproject scenarios and scalability: `This P001 process itself is singular, but the processes it defines may need to consider such scenarios.`
*   Specialized processes (or variations of this process) may be required for activities like reverse engineering or onboarding existing complex projects: `This P001 process can define a 'PXXX_ReverseEngineerExistingProjectProcess' if needed.`

## 17. Revision History

| Version | Date       | Author        | Changes                                     |
| :------ | :--------- | :------------ | :------------------------------------------ |
| 1.0     | 2025-05-09 | ESTRATIX Core | Initial comprehensive version.              |
| 1.1     | YYYY-MM-DD | [Your Name/AI] | Aligned with enhanced base template, added detailed prompts and new sections (KPIs, PM Integration, Observability). |

## 18. Observability & Traceability Considerations

*   **Key Data Points/Events to Log:**
    *   `Event: New Process Definition Initiated (Process ID, Requester)`
    *   `Event: Process Definition Draft Completed (Process ID, Version)`
    *   `Event: Process Definition Review Started/Completed (Process ID, Reviewer, FeedbackSummary)`
    *   `Event: Process Definition Finalized/Approved (Process ID, Approver)`
    *   `Event: Process Registered in Matrix (Process ID)`
*   **Relevant Metrics for Dashboards:**
    *   `Count of processes defined per period.`
    *   `Average cycle time for process definition (from initiation to finalization).`
    *   `Number of processes by status (Planning, Active, Deprecated).`
*   **Linkages to Observability Agents/Frameworks:** `An AGENT_Observability_Framework_Specialist could monitor these events and update a 'Process Landscape Dashboard'.`
*   **Visibility of Process Progress/Outputs:** `Updates to process_matrix.md. Notifications upon new process finalization. Central repository of process definitions.`
