import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { web3Service } from '../services/web3Service';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

async function healthRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Basic health check
  fastify.get('/health', async (request, reply) => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'smart-contracts-service',
      version: process.env.npm_package_version || '1.0.0'
    };
  });

  // Detailed health check with blockchain connectivity
  fastify.get('/health/detailed', async (request, reply) => {
    const healthCheck = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'smart-contracts-service',
      version: process.env.npm_package_version || '1.0.0',
      checks: {
        blockchain: { status: 'unknown', details: {} },
        contracts: { status: 'unknown', details: {} },
        environment: { status: 'unknown', details: {} }
      }
    };

    try {
      // Check blockchain connectivity
      const blockNumber = await web3Service.getBlockNumber();
      const chainId = await web3Service.getChainId();
      
      healthCheck.checks.blockchain = {
        status: 'healthy',
        details: {
          connected: true,
          blockNumber: blockNumber.toString(),
          chainId: chainId.toString(),
          network: config.BLOCKCHAIN_NETWORK
        }
      };
    } catch (error) {
      healthCheck.status = 'unhealthy';
      healthCheck.checks.blockchain = {
        status: 'unhealthy',
        details: {
          connected: false,
          error: error instanceof Error ? error.message : 'Unknown blockchain error'
        }
      };
    }

    try {
      // Check contract deployments
      const contractChecks = await Promise.allSettled([
        web3Service.getTokenInfo(),
        web3Service.getStakingStats(),
        // Add more contract checks as needed
      ]);

      const contractResults = {
        luxToken: contractChecks[0].status === 'fulfilled',
        stakingContract: contractChecks[1].status === 'fulfilled'
      };

      const allContractsHealthy = Object.values(contractResults).every(Boolean);
      
      healthCheck.checks.contracts = {
        status: allContractsHealthy ? 'healthy' : 'degraded',
        details: contractResults
      };

      if (!allContractsHealthy && healthCheck.status === 'healthy') {
        healthCheck.status = 'degraded';
      }
    } catch (error) {
      healthCheck.status = 'unhealthy';
      healthCheck.checks.contracts = {
        status: 'unhealthy',
        details: {
          error: error instanceof Error ? error.message : 'Unknown contract error'
        }
      };
    }

    // Check environment configuration
    const envChecks = {
      rpcUrl: !!config.SEPOLIA_RPC_URL,
      privateKey: !!config.PRIVATE_KEY,
      contractAddresses: !!config.LUX_TOKEN_ADDRESS && !!config.STAKING_CONTRACT_ADDRESS,
      jwtSecret: !!config.JWT_SECRET
    };

    const allEnvHealthy = Object.values(envChecks).every(Boolean);
    
    healthCheck.checks.environment = {
      status: allEnvHealthy ? 'healthy' : 'unhealthy',
      details: envChecks
    };

    if (!allEnvHealthy) {
      healthCheck.status = 'unhealthy';
    }

    const statusCode = healthCheck.status === 'healthy' ? 200 : 
                      healthCheck.status === 'degraded' ? 200 : 503;

    return reply.status(statusCode).send(healthCheck);
  });

  // Readiness check
  fastify.get('/ready', async (request, reply) => {
    try {
      // Check if service is ready to handle requests
      await web3Service.getBlockNumber();
      
      return {
        status: 'ready',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Readiness check failed:', error);
      return reply.status(503).send({
        status: 'not ready',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Liveness check
  fastify.get('/live', async (request, reply) => {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      pid: process.pid
    };
  });

  // Metrics endpoint
  fastify.get('/metrics', async (request, reply) => {
    try {
      const metrics = {
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        cpu: process.cpuUsage(),
        blockchain: {
          blockNumber: '0',
          chainId: '0',
          gasPrice: '0'
        },
        contracts: {
          totalSupply: '0',
          totalStaked: '0',
          activeStakers: 0
        }
      };

      // Get blockchain metrics
      try {
        const [blockNumber, chainId, gasPrice] = await Promise.all([
          web3Service.getBlockNumber(),
          web3Service.getChainId(),
          web3Service.getGasPrice()
        ]);

        metrics.blockchain = {
          blockNumber: blockNumber.toString(),
          chainId: chainId.toString(),
          gasPrice: gasPrice.toString()
        };
      } catch (error) {
        logger.warn('Failed to get blockchain metrics:', error);
      }

      // Get contract metrics
      try {
        const [totalSupply, stakingStats] = await Promise.all([
          web3Service.getTotalSupply(),
          web3Service.getStakingStats()
        ]);

        metrics.contracts = {
          totalSupply: totalSupply.toString(),
          totalStaked: stakingStats.totalStaked.toString(),
          activeStakers: stakingStats.activeStakers
        };
      } catch (error) {
        logger.warn('Failed to get contract metrics:', error);
      }

      return metrics;
    } catch (error) {
      logger.error('Metrics collection failed:', error);
      return reply.status(500).send({
        error: 'Failed to collect metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

export default fp(healthRoutes);