import * as dotenv from 'dotenv';
import { z } from 'zod';

// Load environment variables
dotenv.config();

// Environment validation schema
const envSchema = z.object({
  // Server configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3003'),
  HOST: z.string().default('0.0.0.0'),
  
  // Security
  JWT_SECRET: z.string().min(32),
  CORS_ORIGINS: z.string().transform(str => str.split(',').map(s => s.trim())),
  
  // Database
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  
  // Blockchain configuration
  BLOCKCHAIN_NETWORK: z.enum(['mainnet', 'sepolia', 'polygon', 'polygonMumbai', 'arbitrum', 'optimism', 'base', 'localhost']).default('sepolia'),
  
  // RPC URLs
  MAINNET_RPC_URL: z.string().url().optional(),
  SEPOLIA_RPC_URL: z.string().url().optional(),
  POLYGON_RPC_URL: z.string().url().optional(),
  POLYGON_MUMBAI_RPC_URL: z.string().url().optional(),
  ARBITRUM_RPC_URL: z.string().url().optional(),
  OPTIMISM_RPC_URL: z.string().url().optional(),
  BASE_RPC_URL: z.string().url().optional(),
  
  // API Keys
  ALCHEMY_API_KEY: z.string().optional(),
  INFURA_API_KEY: z.string().optional(),
  ETHERSCAN_API_KEY: z.string().optional(),
  POLYGONSCAN_API_KEY: z.string().optional(),
  ARBISCAN_API_KEY: z.string().optional(),
  OPTIMISTIC_ETHERSCAN_API_KEY: z.string().optional(),
  BASESCAN_API_KEY: z.string().optional(),
  
  // Wallet configuration
  PRIVATE_KEY: z.string().optional(),
  MNEMONIC: z.string().optional(),
  
  // Contract addresses
  LUX_TOKEN_ADDRESS: z.string().optional(),
  PROPERTY_NFT_ADDRESS: z.string().optional(),
  STAKING_CONTRACT_ADDRESS: z.string().optional(),
  MARKETPLACE_ADDRESS: z.string().optional(),
  GOVERNANCE_ADDRESS: z.string().optional(),
  LENDING_POOL_ADDRESS: z.string().optional(),
  LIQUIDITY_POOL_ADDRESS: z.string().optional(),
  PROPERTY_TOKENIZATION_ADDRESS: z.string().optional(),
  YIELD_FARMING_ADDRESS: z.string().optional(),
  INSURANCE_POOL_ADDRESS: z.string().optional(),
  
  // IPFS configuration
  IPFS_GATEWAY_URL: z.string().url().default('https://ipfs.io/ipfs/'),
  PINATA_API_KEY: z.string().optional(),
  PINATA_SECRET_KEY: z.string().optional(),
  
  // External services
  PROPERTY_DATA_API_URL: z.string().url().optional(),
  PROPERTY_DATA_API_KEY: z.string().optional(),
  
  // Monitoring
  SENTRY_DSN: z.string().optional(),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Rate limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'), // 15 minutes
  
  // File upload
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  UPLOAD_DIR: z.string().default('./uploads'),
  
  // Queue configuration
  QUEUE_REDIS_URL: z.string().url().optional(),
  
  // Webhook configuration
  WEBHOOK_SECRET: z.string().optional(),
  
  // Feature flags
  ENABLE_GOVERNANCE: z.string().transform(str => str === 'true').default('true'),
  ENABLE_STAKING: z.string().transform(str => str === 'true').default('true'),
  ENABLE_DEFI: z.string().transform(str => str === 'true').default('true'),
  ENABLE_PROPERTY_TOKENIZATION: z.string().transform(str => str === 'true').default('true'),
});

// Validate environment variables
const envValidation = envSchema.safeParse(process.env);

if (!envValidation.success) {
  console.error('❌ Invalid environment variables:');
  console.error(envValidation.error.format());
  process.exit(1);
}

export const config = envValidation.data;

// Network configuration
export const networkConfig = {
  mainnet: {
    chainId: 1,
    name: 'Ethereum Mainnet',
    rpcUrl: config.MAINNET_RPC_URL || `https://eth-mainnet.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://etherscan.io',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  },
  sepolia: {
    chainId: 11155111,
    name: 'Sepolia Testnet',
    rpcUrl: config.SEPOLIA_RPC_URL || `https://eth-sepolia.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://sepolia.etherscan.io',
    nativeCurrency: { name: 'Sepolia Ether', symbol: 'SEP', decimals: 18 },
  },
  polygon: {
    chainId: 137,
    name: 'Polygon Mainnet',
    rpcUrl: config.POLYGON_RPC_URL || `https://polygon-mainnet.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://polygonscan.com',
    nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
  },
  polygonMumbai: {
    chainId: 80001,
    name: 'Polygon Mumbai Testnet',
    rpcUrl: config.POLYGON_MUMBAI_RPC_URL || `https://polygon-mumbai.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://mumbai.polygonscan.com',
    nativeCurrency: { name: 'MATIC', symbol: 'MATIC', decimals: 18 },
  },
  arbitrum: {
    chainId: 42161,
    name: 'Arbitrum One',
    rpcUrl: config.ARBITRUM_RPC_URL || `https://arb-mainnet.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://arbiscan.io',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  },
  optimism: {
    chainId: 10,
    name: 'Optimism',
    rpcUrl: config.OPTIMISM_RPC_URL || `https://opt-mainnet.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://optimistic.etherscan.io',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  },
  base: {
    chainId: 8453,
    name: 'Base',
    rpcUrl: config.BASE_RPC_URL || `https://base-mainnet.g.alchemy.com/v2/${config.ALCHEMY_API_KEY}`,
    blockExplorer: 'https://basescan.org',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  },
  localhost: {
    chainId: 31337,
    name: 'Localhost',
    rpcUrl: 'http://127.0.0.1:8545',
    blockExplorer: '',
    nativeCurrency: { name: 'Ether', symbol: 'ETH', decimals: 18 },
  },
};

// Get current network configuration
export const currentNetwork = networkConfig[config.BLOCKCHAIN_NETWORK];

// Contract addresses for current network
export const contractAddresses = {
  LUX_TOKEN: config.LUX_TOKEN_ADDRESS || '',
  PROPERTY_NFT: config.PROPERTY_NFT_ADDRESS || '',
  STAKING_CONTRACT: config.STAKING_CONTRACT_ADDRESS || '',
  MARKETPLACE: config.MARKETPLACE_ADDRESS || '',
  GOVERNANCE: config.GOVERNANCE_ADDRESS || '',
  LENDING_POOL: config.LENDING_POOL_ADDRESS || '',
  LIQUIDITY_POOL: config.LIQUIDITY_POOL_ADDRESS || '',
  PROPERTY_TOKENIZATION: config.PROPERTY_TOKENIZATION_ADDRESS || '',
  YIELD_FARMING: config.YIELD_FARMING_ADDRESS || '',
  INSURANCE_POOL: config.INSURANCE_POOL_ADDRESS || '',
};

// Logging configuration
export const logConfig = {
  level: config.LOG_LEVEL,
  transport: config.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'HH:MM:ss Z',
      ignore: 'pid,hostname',
    },
  } : undefined,
};

// Rate limiting configuration
export const rateLimitConfig = {
  max: config.RATE_LIMIT_MAX,
  timeWindow: config.RATE_LIMIT_WINDOW,
};

// CORS configuration
export const corsConfig = {
  origin: config.CORS_ORIGINS,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
};

// Validate required configuration based on enabled features
if (config.ENABLE_PROPERTY_TOKENIZATION && !config.PROPERTY_TOKENIZATION_ADDRESS) {
  console.warn('⚠️  Property tokenization is enabled but no contract address is configured');
}

if (config.ENABLE_STAKING && !config.STAKING_CONTRACT_ADDRESS) {
  console.warn('⚠️  Staking is enabled but no contract address is configured');
}

if (config.ENABLE_GOVERNANCE && !config.GOVERNANCE_ADDRESS) {
  console.warn('⚠️  Governance is enabled but no contract address is configured');
}

if (config.ENABLE_DEFI && (!config.LENDING_POOL_ADDRESS || !config.LIQUIDITY_POOL_ADDRESS)) {
  console.warn('⚠️  DeFi is enabled but some contract addresses are not configured');
}

console.log(`🔧 Smart Contracts service configuration loaded for ${config.NODE_ENV} environment`);
console.log(`🌐 Network: ${currentNetwork.name} (Chain ID: ${currentNetwork.chainId})`);
console.log(`🚀 Server will start on ${config.HOST}:${config.PORT}`);