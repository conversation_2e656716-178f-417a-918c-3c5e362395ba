import { apiClient, ApiResponse } from './api.client';
import { BaseService } from './base.service';
import { buildSearchParams, applyFilters, paginate, sortItems } from './service.utils';

// Content types
export interface ContentItem {
  id: string;
  title: string;
  description: string;
  type: 'article' | 'video' | 'image' | 'audio' | 'document' | 'social_post';
  category: string;
  tags: string[];
  content: string;
  mediaUrl?: string;
  thumbnailUrl?: string;
  status: 'draft' | 'published' | 'archived' | 'pending_review';
  authorId: string;
  authorName: string;
  publishedAt?: Date;
  viewCount: number;
  likeCount: number;
  shareCount: number;
  isAiGenerated: boolean;
  aiModel?: string;
  prompt?: string;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  type: ContentItem['type'];
  category: string;
  template: string;
  variables: string[];
  isPublic: boolean;
  usageCount: number;
  createdBy: string;
  createdAt: Date;
}

export interface ContentGenerationRequest {
  type: ContentItem['type'];
  category: string;
  prompt: string;
  style?: string;
  tone?: 'professional' | 'casual' | 'friendly' | 'formal' | 'creative';
  length?: 'short' | 'medium' | 'long';
  targetAudience?: string;
  keywords?: string[];
  templateId?: string;
  variables?: Record<string, string>;
}

export interface ContentFilters {
  type?: ContentItem['type'][];
  category?: string;
  status?: ContentItem['status'][];
  authorId?: string;
  isAiGenerated?: boolean;
  tags?: string[];
  dateFrom?: Date;
  dateTo?: Date;
}

export interface ContentSearchParams extends ContentFilters {
  query?: string;
  sortBy?: 'created' | 'updated' | 'published' | 'views' | 'likes';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface ContentStats {
  total: number;
  published: number;
  draft: number;
  totalViews: number;
  totalLikes: number;
  totalShares: number;
  aiGenerated: number;
  topCategories: Array<{ category: string; count: number }>;
}

// Mock data for development
const mockContent: ContentItem[] = [
  {
    id: 'content-1',
    title: 'Luxury Real Estate Investment Guide 2024',
    description: 'Comprehensive guide to investing in luxury real estate markets.',
    type: 'article',
    category: 'Investment',
    tags: ['real-estate', 'investment', 'luxury', 'guide'],
    content: 'The luxury real estate market continues to evolve with new opportunities...',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20real%20estate%20investment%20guide%20modern%20buildings&image_size=landscape_16_9',
    status: 'published',
    authorId: 'author-1',
    authorName: 'Sarah Johnson',
    publishedAt: new Date('2024-01-15'),
    viewCount: 1250,
    likeCount: 89,
    shareCount: 34,
    isAiGenerated: true,
    aiModel: 'GPT-4',
    prompt: 'Write a comprehensive guide about luxury real estate investment in 2024',
    metadata: { readTime: '8 min', difficulty: 'intermediate' },
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'content-2',
    title: 'Virtual Property Tour: Boston Penthouse',
    description: 'Immersive virtual tour of a luxury penthouse in Boston.',
    type: 'video',
    category: 'Property Tours',
    tags: ['virtual-tour', 'penthouse', 'boston', 'luxury'],
    content: 'Experience this stunning penthouse through our virtual reality tour...',
    mediaUrl: 'https://example.com/videos/boston-penthouse-tour.mp4',
    thumbnailUrl: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=boston%20luxury%20penthouse%20virtual%20tour%20modern%20interior&image_size=landscape_16_9',
    status: 'published',
    authorId: 'author-2',
    authorName: 'Michael Chen',
    publishedAt: new Date('2024-01-20'),
    viewCount: 2100,
    likeCount: 156,
    shareCount: 67,
    isAiGenerated: false,
    metadata: { duration: '12:30', quality: '4K' },
    createdAt: new Date('2024-01-18'),
    updatedAt: new Date('2024-01-20')
  },
  {
    id: 'content-3',
    title: 'Market Trends Analysis Q1 2024',
    description: 'AI-generated analysis of current market trends and predictions.',
    type: 'document',
    category: 'Market Analysis',
    tags: ['market-trends', 'analysis', 'q1-2024', 'predictions'],
    content: 'Market analysis shows significant growth in luxury segments...',
    status: 'draft',
    authorId: 'author-3',
    authorName: 'AI Assistant',
    viewCount: 0,
    likeCount: 0,
    shareCount: 0,
    isAiGenerated: true,
    aiModel: 'Claude-3',
    prompt: 'Analyze real estate market trends for Q1 2024 with predictions',
    metadata: { pages: 15, charts: 8 },
    createdAt: new Date('2024-01-25'),
    updatedAt: new Date('2024-01-25')
  }
];

const mockTemplates: ContentTemplate[] = [
  {
    id: 'template-1',
    name: 'Property Listing Description',
    description: 'Template for creating engaging property descriptions',
    type: 'article',
    category: 'Property Marketing',
    template: 'Discover this {{propertyType}} in {{location}}. Featuring {{bedrooms}} bedrooms and {{bathrooms}} bathrooms, this {{adjective}} property offers {{amenities}}.',
    variables: ['propertyType', 'location', 'bedrooms', 'bathrooms', 'adjective', 'amenities'],
    isPublic: true,
    usageCount: 45,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01')
  },
  {
    id: 'template-2',
    name: 'Social Media Post',
    description: 'Template for social media property posts',
    type: 'social_post',
    category: 'Social Media',
    template: '🏠 New listing alert! {{propertyType}} in {{neighborhood}} - {{price}}. {{highlight}}. Contact us for viewing! #RealEstate #{{city}}',
    variables: ['propertyType', 'neighborhood', 'price', 'highlight', 'city'],
    isPublic: true,
    usageCount: 78,
    createdBy: 'admin',
    createdAt: new Date('2024-01-05')
  }
];

class ContentService extends BaseService {
  async getContent(params?: ContentSearchParams): Promise<{ content: ContentItem[]; total: number }> {
    return this.handleApiCall(
      async () => {
        const searchParams = buildSearchParams({
          query: params?.query,
          type: params?.type?.join(','),
          category: params?.category,
          status: params?.status?.join(','),
          authorId: params?.authorId,
          isAiGenerated: params?.isAiGenerated?.toString(),
          tags: params?.tags?.join(','),
          sortBy: params?.sortBy,
          sortOrder: params?.sortOrder,
          page: params?.page?.toString(),
          limit: params?.limit?.toString()
        });
        const response = await apiClient.get<{ content: ContentItem[]; total: number }>(
          `/content?${searchParams}`
        );
        return response.data;
      },
      async () => {
        let filteredContent = [...mockContent];
        
        // Apply filters
        if (params?.type) {
          filteredContent = filteredContent.filter(c => params.type!.includes(c.type));
        }
        if (params?.category) {
          filteredContent = filteredContent.filter(c => 
            c.category.toLowerCase().includes(params.category!.toLowerCase())
          );
        }
        if (params?.status) {
          filteredContent = filteredContent.filter(c => params.status!.includes(c.status));
        }
        if (params?.authorId) {
          filteredContent = filteredContent.filter(c => c.authorId === params.authorId);
        }
        if (params?.isAiGenerated !== undefined) {
          filteredContent = filteredContent.filter(c => c.isAiGenerated === params.isAiGenerated);
        }
        if (params?.tags) {
          filteredContent = filteredContent.filter(c => 
            params.tags!.some(tag => c.tags.includes(tag))
          );
        }
        if (params?.query) {
          const query = params.query.toLowerCase();
          filteredContent = filteredContent.filter(c => 
            c.title.toLowerCase().includes(query) ||
            c.description.toLowerCase().includes(query) ||
            c.content.toLowerCase().includes(query)
          );
        }
        
        // Apply sorting
        if (params?.sortBy) {
          filteredContent = sortItems(filteredContent, params.sortBy, params.sortOrder || 'asc', {
            created: (item: ContentItem) => item.createdAt,
            updated: (item: ContentItem) => item.updatedAt,
            published: (item: ContentItem) => item.publishedAt || new Date(0),
            views: (item: ContentItem) => item.viewCount,
            likes: (item: ContentItem) => item.likeCount
          });
        }
        
        // Apply pagination
        const paginatedResult = paginate(filteredContent, params?.page, params?.limit);
        
        return {
          content: paginatedResult.items,
          total: filteredContent.length
        };
      },
      500
    );
  }

  async getContentById(id: string): Promise<ContentItem> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<ContentItem>(`/content/${id}`);
        return response.data;
      },
      async () => {
        const content = mockContent.find(c => c.id === id);
        if (!content) {
          throw new Error('Content not found');
        }
        return content;
      },
      300
    );
  }

  async generateContent(request: ContentGenerationRequest): Promise<ContentItem> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<ContentItem>('/content/generate', request);
        return response.data;
      },
      async () => {
        const newContent: ContentItem = {
          id: `content-${Date.now()}`,
          title: `AI Generated ${request.type} - ${request.category}`,
          description: `Generated content based on: ${request.prompt}`,
          type: request.type,
          category: request.category,
          tags: request.keywords || ['ai-generated'],
          content: `This is AI-generated content based on the prompt: "${request.prompt}". The content would be generated using advanced AI models to create engaging and relevant material.`,
          status: 'draft',
          authorId: 'ai-assistant',
          authorName: 'AI Assistant',
          viewCount: 0,
          likeCount: 0,
          shareCount: 0,
          isAiGenerated: true,
          aiModel: 'GPT-4',
          prompt: request.prompt,
          metadata: {
            style: request.style,
            tone: request.tone,
            length: request.length,
            targetAudience: request.targetAudience
          },
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        // Add thumbnail for visual content
        if (request.type === 'image' || request.type === 'video') {
          newContent.thumbnailUrl = `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=${encodeURIComponent(request.prompt)}&image_size=landscape_16_9`;
        }
        
        mockContent.push(newContent);
        return newContent;
      },
      3000
    );
  }

  async createContent(data: Partial<ContentItem>): Promise<ContentItem> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<ContentItem>('/content', data);
        return response.data;
      },
      async () => {
        const newContent: ContentItem = {
          id: `content-${Date.now()}`,
          title: data.title || 'Untitled',
          description: data.description || '',
          type: data.type || 'article',
          category: data.category || 'General',
          tags: data.tags || [],
          content: data.content || '',
          status: data.status || 'draft',
          authorId: data.authorId || 'current-user',
          authorName: data.authorName || 'Current User',
          viewCount: 0,
          likeCount: 0,
          shareCount: 0,
          isAiGenerated: data.isAiGenerated || false,
          metadata: data.metadata || {},
          createdAt: new Date(),
          updatedAt: new Date(),
          ...data
        };
        
        mockContent.push(newContent);
        return newContent;
      },
      800
    );
  }

  async updateContent(id: string, updates: Partial<ContentItem>): Promise<ContentItem> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.patch<ContentItem>(`/content/${id}`, updates);
        return response.data;
      },
      async () => {
        const contentIndex = mockContent.findIndex(c => c.id === id);
        if (contentIndex === -1) {
          throw new Error('Content not found');
        }
        
        mockContent[contentIndex] = {
          ...mockContent[contentIndex],
          ...updates,
          updatedAt: new Date()
        };
        
        return mockContent[contentIndex];
      },
      600
    );
  }

  async deleteContent(id: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.delete(`/content/${id}`);
      },
      async () => {
        const contentIndex = mockContent.findIndex(c => c.id === id);
        if (contentIndex === -1) {
          throw new Error('Content not found');
        }
        
        mockContent.splice(contentIndex, 1);
      },
      400
    );
  }

  async publishContent(id: string): Promise<ContentItem> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<ContentItem>(`/content/${id}/publish`);
        return response.data;
      },
      async () => {
        const contentIndex = mockContent.findIndex(c => c.id === id);
        if (contentIndex === -1) {
          throw new Error('Content not found');
        }
        
        mockContent[contentIndex] = {
          ...mockContent[contentIndex],
          status: 'published',
          publishedAt: new Date(),
          updatedAt: new Date()
        };
        
        return mockContent[contentIndex];
      },
      500
    );
  }

  async getContentStats(filters?: ContentFilters): Promise<ContentStats> {
    return this.handleApiCall(
      async () => {
        const params = buildSearchParams({
          type: filters?.type?.join(','),
          category: filters?.category,
          status: filters?.status?.join(','),
          authorId: filters?.authorId,
          isAiGenerated: filters?.isAiGenerated?.toString()
        });
        const response = await apiClient.get<ContentStats>(`/content/stats?${params}`);
        return response.data;
      },
      async () => {
        const { content } = await this.getContent(filters);
        
        const categoryCount = content.reduce((acc, c) => {
          acc[c.category] = (acc[c.category] || 0) + 1;
          return acc;
        }, {} as Record<string, number>);
        
        const topCategories = Object.entries(categoryCount)
          .map(([category, count]) => ({ category, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5);
        
        return {
          total: content.length,
          published: content.filter(c => c.status === 'published').length,
          draft: content.filter(c => c.status === 'draft').length,
          totalViews: content.reduce((sum, c) => sum + c.viewCount, 0),
          totalLikes: content.reduce((sum, c) => sum + c.likeCount, 0),
          totalShares: content.reduce((sum, c) => sum + c.shareCount, 0),
          aiGenerated: content.filter(c => c.isAiGenerated).length,
          topCategories
        };
      },
      400
    );
  }

  async getTemplates(): Promise<ContentTemplate[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<ContentTemplate[]>('/content/templates');
        return response.data;
      },
      async () => {
        return [...mockTemplates];
      },
      300
    );
  }

  async createTemplate(template: Omit<ContentTemplate, 'id' | 'usageCount' | 'createdAt'>): Promise<ContentTemplate> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<ContentTemplate>('/content/templates', template);
        return response.data;
      },
      async () => {
        const newTemplate: ContentTemplate = {
          id: `template-${Date.now()}`,
          usageCount: 0,
          createdAt: new Date(),
          ...template
        };
        
        mockTemplates.push(newTemplate);
        return newTemplate;
      },
      600
    );
  }

  async uploadMedia(file: File, type: 'image' | 'video' | 'audio' | 'document'): Promise<{ url: string; thumbnailUrl?: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.uploadFile<{ url: string; thumbnailUrl?: string }>(
          `/content/upload/${type}`, 
          file
        );
        return response.data;
      },
      async () => {
        // Mock media upload
        const url = `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=uploaded%20${type}%20content&image_size=landscape_16_9`;
        const thumbnailUrl = type === 'video' ? url : undefined;
        
        return { url, thumbnailUrl };
      },
      2000
    );
  }
}

export const contentService = new ContentService();
export default contentService;