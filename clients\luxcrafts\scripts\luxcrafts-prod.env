# ============================================================================
# ESTRATIX Agency VPS Deployment Configuration
# ============================================================================
# This file contains environment variables for SSH-based VPS deployment
# Copy this file and customize for each client deployment
# Usage: source ssh-deploy-config.env && ./deploy-agency-vps.sh deploy
# ============================================================================

# ============================================================================
# CLIENT CONFIGURATION
# ============================================================================

# Client Information
export CLIENT_NAME="luxcrafts"
export CLIENT_EMAIL="<EMAIL>"
export CLIENT_CONTACT="John Doe <<EMAIL>>"

# Domain Configuration
export CLIENT_DOMAIN="www.luxcrafts.co"
export STAGING_DOMAIN="staging.luxcrafts.co"
export DEV_DOMAIN="dev.luxcrafts.co"

# Additional domains (comma-separated)
export ADDITIONAL_DOMAINS="luxcrafts.co,luxcrafts.com"

# ============================================================================
# VPS CONFIGURATION
# ============================================================================

# VPS Connection Details
export VPS_HOST="**************"  # Set to your VPS IP or hostname
export VPS_USER="admin"
export VPS_PORT="2222"
export SSH_KEY_PATH="~/.ssh/id_rsa"

# VPS Provider (for provider-specific optimizations)
export VPS_PROVIDER="NetCup"  # digitalocean, linode, vultr, aws, custom

# Server Specifications (for optimization)
export VPS_RAM_GB="16"
export VPS_CPU_CORES="8"
export VPS_DISK_GB="1024"

# ============================================================================
# APPLICATION CONFIGURATION
# ============================================================================

# Repository Configuration
export GIT_REPO="https://github.com/jdgg777/luxcrafts.git"
export GIT_BRANCH="main"
export GIT_DEPLOY_KEY_PATH=""  # Optional: path to deploy key

# Build Configuration
export NODE_VERSION="20"
export NPM_REGISTRY="https://registry.npmjs.org/"
export BUILD_COMMAND="npm run build"
export BUILD_OUTPUT_DIR="dist"

# Application Settings
export APP_PORT="3000"
export APP_ENV="production"
export APP_DEBUG="false"

# ============================================================================
# ENVIRONMENT CONFIGURATION
# ============================================================================

# Deployment Environment
export ENVIRONMENT="production"  # production, staging, development
export DEPLOY_MODE="blue-green"   # blue-green, rolling, recreate

# Environment-specific settings
if [[ "$ENVIRONMENT" == "staging" ]]; then
    export CLIENT_DOMAIN="$STAGING_DOMAIN"
    export APP_DEBUG="true"
    export SSL_ENABLED="true"  # Still use SSL for staging
elif [[ "$ENVIRONMENT" == "development" ]]; then
    export CLIENT_DOMAIN="$DEV_DOMAIN"
    export APP_DEBUG="true"
    export SSL_ENABLED="false"
fi

# ============================================================================
# SECURITY CONFIGURATION
# ============================================================================

# Firewall Settings
export FIREWALL_ENABLED="true"
export CUSTOM_FIREWALL_RULES=""  # Additional UFW rules

# Fail2Ban Configuration
export FAIL2BAN_ENABLED="true"
export FAIL2BAN_BANTIME="3600"     # 1 hour
export FAIL2BAN_FINDTIME="600"     # 10 minutes
export FAIL2BAN_MAXRETRY="5"

# SSL/TLS Configuration
export SSL_ENABLED="true"
export SSL_PROVIDER="letsencrypt"  # letsencrypt, custom
export HSTS_ENABLED="true"
export HSTS_MAX_AGE="31536000"     # 1 year

# Security Headers
export SECURITY_HEADERS_ENABLED="true"
export CSP_ENABLED="true"
export CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;"

# Rate Limiting
export RATE_LIMITING_ENABLED="true"
export RATE_LIMIT_REQUESTS="100"   # requests per minute
export RATE_LIMIT_BURST="20"

# ============================================================================
# MONITORING AND ALERTING
# ============================================================================

# Monitoring Configuration
export MONITORING_ENABLED="true"
export UPTIME_CHECK_INTERVAL="300"  # 5 minutes
export HEALTH_CHECK_ENDPOINT="/health"

# Alerting Configuration
export ALERTING_WEBHOOK=""  # Slack, Discord, or custom webhook URL
export ALERT_EMAIL="<EMAIL>"
export ALERT_PHONE=""  # For SMS alerts (if supported)

# Monitoring Thresholds
export CPU_ALERT_THRESHOLD="80"     # CPU usage percentage
export MEMORY_ALERT_THRESHOLD="85"  # Memory usage percentage
export DISK_ALERT_THRESHOLD="85"    # Disk usage percentage
export RESPONSE_TIME_THRESHOLD="2"  # Response time in seconds

# External Monitoring Services
export UPTIMEROBOT_API_KEY=""       # UptimeRobot integration
export PINGDOM_API_KEY=""           # Pingdom integration
export NEWRELIC_LICENSE_KEY=""      # New Relic integration

# ============================================================================
# BACKUP CONFIGURATION
# ============================================================================

# Backup Settings
export BACKUP_ENABLED="true"
export BACKUP_RETENTION_DAYS="30"
export BACKUP_SCHEDULE="0 2 * * *"  # Daily at 2 AM

# Backup Storage
export BACKUP_LOCAL_PATH="/opt/backups"
export S3_BACKUP_BUCKET=""          # AWS S3 bucket for offsite backups
export S3_REGION="us-east-1"
export AWS_ACCESS_KEY_ID=""         # AWS credentials for S3
export AWS_SECRET_ACCESS_KEY=""

# Alternative backup providers
export DIGITALOCEAN_SPACES_BUCKET=""  # DigitalOcean Spaces
export DIGITALOCEAN_SPACES_REGION=""
export DIGITALOCEAN_SPACES_KEY=""
export DIGITALOCEAN_SPACES_SECRET=""

# ============================================================================
# DATABASE CONFIGURATION (if applicable)
# ============================================================================

# Database Settings
export DATABASE_ENABLED="false"     # Set to true if using database
export DATABASE_TYPE="postgresql"   # postgresql, mysql, mongodb
export DATABASE_VERSION="13"

# Database Connection
export DATABASE_HOST="localhost"
export DATABASE_PORT="5432"
export DATABASE_NAME="${CLIENT_NAME}_${ENVIRONMENT}"
export DATABASE_USER="${CLIENT_NAME}_user"
export DATABASE_PASSWORD=""         # Generate secure password

# Database Backup
export DATABASE_BACKUP_ENABLED="true"
export DATABASE_BACKUP_RETENTION="7"  # days

# ============================================================================
# CDN AND PERFORMANCE
# ============================================================================

# CDN Configuration
export CDN_ENABLED="false"           # Enable for production
export CDN_PROVIDER="cloudflare"     # cloudflare, aws, custom
export CDN_API_KEY=""
export CDN_ZONE_ID=""

# Performance Optimization
export GZIP_ENABLED="true"
export BROTLI_ENABLED="true"
export CACHE_STATIC_FILES="true"
export CACHE_DURATION="31536000"     # 1 year for static assets

# Image Optimization
export IMAGE_OPTIMIZATION="true"
export WEBP_ENABLED="true"
export AVIF_ENABLED="false"          # Experimental

# ============================================================================
# LOGGING CONFIGURATION
# ============================================================================

# Log Settings
export LOG_LEVEL="info"              # debug, info, warn, error
export LOG_RETENTION_DAYS="30"
export LOG_MAX_SIZE="100M"

# Log Aggregation
export CENTRALIZED_LOGGING="false"   # Enable for enterprise
export ELASTICSEARCH_URL=""
export LOGSTASH_HOST=""
export KIBANA_URL=""

# Application Logs
export APP_LOG_PATH="/var/log/${CLIENT_NAME}"
export ACCESS_LOG_ENABLED="true"
export ERROR_LOG_ENABLED="true"

# ============================================================================
# DEVELOPMENT AND TESTING
# ============================================================================

# Development Tools
export DEV_TOOLS_ENABLED="false"     # Only for staging/dev
export HOT_RELOAD_ENABLED="false"
export SOURCE_MAPS_ENABLED="false"

# Testing Configuration
export RUN_TESTS="true"
export TEST_COMMAND="npm test"
export E2E_TESTS_ENABLED="false"
export LIGHTHOUSE_AUDIT="true"       # Performance audit

# ============================================================================
# INTEGRATION CONFIGURATION
# ============================================================================

# CI/CD Integration
export GITHUB_WEBHOOK_SECRET=""      # For GitHub webhooks
export GITLAB_WEBHOOK_TOKEN=""       # For GitLab webhooks
export JENKINS_URL=""                # Jenkins integration

# Third-party Services
export SENTRY_DSN=""                 # Error tracking
export GOOGLE_ANALYTICS_ID=""        # Analytics
export HOTJAR_ID=""                  # User behavior analytics

# API Keys and Secrets
export STRIPE_PUBLIC_KEY=""          # Payment processing
export STRIPE_SECRET_KEY=""
export SENDGRID_API_KEY=""           # Email service
export TWILIO_ACCOUNT_SID=""         # SMS service
export TWILIO_AUTH_TOKEN=""

# ============================================================================
# CUSTOM CONFIGURATION
# ============================================================================

# Custom Environment Variables
# Add any client-specific environment variables here

# Example custom variables:
# export CUSTOM_API_ENDPOINT="https://api.example.com"
# export CUSTOM_SECRET_KEY="your-secret-key"
# export CUSTOM_FEATURE_FLAG="true"

# ============================================================================
# VALIDATION AND HELPERS
# ============================================================================

# Validate required variables
validate_config() {
    local required_vars=(
        "CLIENT_NAME"
        "CLIENT_DOMAIN"
        "VPS_HOST"
        "GIT_REPO"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            echo "Error: Required variable $var is not set"
            return 1
        fi
    done
    
    echo "Configuration validation passed"
    return 0
}

# Display configuration summary
show_config() {
    echo "============================================================================"
    echo "DEPLOYMENT CONFIGURATION SUMMARY"
    echo "============================================================================"
    echo "Client: $CLIENT_NAME"
    echo "Domain: $CLIENT_DOMAIN"
    echo "Environment: $ENVIRONMENT"
    echo "VPS Host: $VPS_HOST"
    echo "Repository: $GIT_REPO"
    echo "Branch: $GIT_BRANCH"
    echo "SSL Enabled: $SSL_ENABLED"
    echo "Monitoring Enabled: $MONITORING_ENABLED"
    echo "Backup Enabled: $BACKUP_ENABLED"
    echo "============================================================================"
}

# Auto-validate configuration when sourced
if [[ "${BASH_SOURCE[0]}" != "${0}" ]]; then
    # File is being sourced
    if ! validate_config; then
        echo "Configuration validation failed. Please check your settings."
        return 1
    fi
fi