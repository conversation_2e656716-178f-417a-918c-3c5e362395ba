import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { logger } from '@/utils/logger';

export interface GetAnalyticsQuery {
  startDate?: string;
  endDate?: string;
  clientId?: string;
  granularity?: 'day' | 'week' | 'month';
}

export interface GetReportQuery {
  type: 'clients' | 'rfps' | 'onboarding' | 'documents' | 'overview';
  startDate?: string;
  endDate?: string;
  format?: 'json' | 'csv';
}

export async function analyticsRoutes(fastify: FastifyInstance) {
  // Get overall analytics metrics
  fastify.get('/analytics', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Get overall analytics metrics',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' },
          clientId: { type: 'string', format: 'uuid' },
          granularity: { type: 'string', enum: ['day', 'week', 'month'], default: 'day' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            overview: {
              type: 'object',
              properties: {
                totalClients: { type: 'integer' },
                activeClients: { type: 'integer' },
                totalRfps: { type: 'integer' },
                pendingRfps: { type: 'integer' },
                totalOnboardingFlows: { type: 'integer' },
                activeOnboardingFlows: { type: 'integer' },
                totalDocuments: { type: 'integer' },
                pendingDocuments: { type: 'integer' },
                averageOnboardingTime: { type: 'number' },
                clientSatisfactionScore: { type: 'number' },
                conversionRate: { type: 'number' },
                churnRate: { type: 'number' }
              }
            },
            trends: {
              type: 'object',
              properties: {
                clientGrowth: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      value: { type: 'number' }
                    }
                  }
                },
                rfpSubmissions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      value: { type: 'number' }
                    }
                  }
                },
                onboardingCompletions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      value: { type: 'number' }
                    }
                  }
                },
                documentApprovals: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      value: { type: 'number' }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetAnalyticsQuery }>, reply: FastifyReply) => {
    try {
      const { startDate, endDate, clientId, granularity = 'day' } = request.query;
      
      const analytics = await fastify.analyticsService.getOverallMetrics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        clientId,
        granularity
      });

      logger.info('Analytics retrieved', {
        userId: (request as any).user?.id,
        startDate,
        endDate,
        clientId,
        granularity
      });

      return reply.status(200).send(analytics);
    } catch (error) {
      logger.error('Failed to get analytics', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve analytics' });
    }
  });

  // Get client-specific analytics
  fastify.get('/analytics/clients/:clientId', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get client-specific analytics',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          clientId: { type: 'string', format: 'uuid' }
        },
        required: ['clientId']
      },
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            clientId: { type: 'string' },
            totalRfps: { type: 'integer' },
            approvedRfps: { type: 'integer' },
            rejectedRfps: { type: 'integer' },
            pendingRfps: { type: 'integer' },
            totalOnboardingFlows: { type: 'integer' },
            completedOnboardingFlows: { type: 'integer' },
            averageOnboardingTime: { type: 'number' },
            totalDocuments: { type: 'integer' },
            approvedDocuments: { type: 'integer' },
            rejectedDocuments: { type: 'integer' },
            pendingDocuments: { type: 'integer' },
            engagementScore: { type: 'number' },
            lastActivity: { type: 'string' },
            activityTimeline: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string' },
                  activity: { type: 'string' },
                  count: { type: 'integer' }
                }
              }
            }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: { clientId: string }; Querystring: { startDate?: string; endDate?: string } }>, reply: FastifyReply) => {
    try {
      const { clientId } = request.params;
      const { startDate, endDate } = request.query;
      
      // Verify client exists
      const client = await fastify.clientService.getClientById(clientId);
      if (!client) {
        return reply.status(404).send({ error: 'Client not found' });
      }
      
      const analytics = await fastify.analyticsService.getClientAnalytics(clientId, {
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      logger.info('Client analytics retrieved', {
        userId: (request as any).user?.id,
        clientId,
        startDate,
        endDate
      });

      return reply.status(200).send(analytics);
    } catch (error) {
      logger.error('Failed to get client analytics', { error, clientId: request.params.clientId });
      return reply.status(500).send({ error: 'Failed to retrieve client analytics' });
    }
  });

  // Get RFP analytics
  fastify.get('/analytics/rfps', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Get RFP analytics',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            pending: { type: 'integer' },
            approved: { type: 'integer' },
            rejected: { type: 'integer' },
            averageProcessingTime: { type: 'number' },
            approvalRate: { type: 'number' },
            submissionTrend: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string' },
                  submissions: { type: 'integer' },
                  approvals: { type: 'integer' },
                  rejections: { type: 'integer' }
                }
              }
            },
            topClients: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  clientId: { type: 'string' },
                  clientName: { type: 'string' },
                  rfpCount: { type: 'integer' },
                  approvalRate: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { startDate?: string; endDate?: string } }>, reply: FastifyReply) => {
    try {
      const { startDate, endDate } = request.query;
      
      const analytics = await fastify.analyticsService.getRfpAnalytics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      logger.info('RFP analytics retrieved', {
        userId: (request as any).user?.id,
        startDate,
        endDate
      });

      return reply.status(200).send(analytics);
    } catch (error) {
      logger.error('Failed to get RFP analytics', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve RFP analytics' });
    }
  });

  // Get onboarding analytics
  fastify.get('/analytics/onboarding', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Get onboarding analytics',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            totalFlows: { type: 'integer' },
            activeFlows: { type: 'integer' },
            completedFlows: { type: 'integer' },
            averageCompletionTime: { type: 'number' },
            completionRate: { type: 'number' },
            dropoffRate: { type: 'number' },
            stepAnalysis: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  stepName: { type: 'string' },
                  completionRate: { type: 'number' },
                  averageTime: { type: 'number' },
                  dropoffRate: { type: 'number' }
                }
              }
            },
            templatePerformance: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  template: { type: 'string' },
                  usage: { type: 'integer' },
                  completionRate: { type: 'number' },
                  averageTime: { type: 'number' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { startDate?: string; endDate?: string } }>, reply: FastifyReply) => {
    try {
      const { startDate, endDate } = request.query;
      
      const analytics = await fastify.analyticsService.getOnboardingAnalytics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      logger.info('Onboarding analytics retrieved', {
        userId: (request as any).user?.id,
        startDate,
        endDate
      });

      return reply.status(200).send(analytics);
    } catch (error) {
      logger.error('Failed to get onboarding analytics', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve onboarding analytics' });
    }
  });

  // Get document analytics
  fastify.get('/analytics/documents', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Get document analytics',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            pending: { type: 'integer' },
            approved: { type: 'integer' },
            rejected: { type: 'integer' },
            averageProcessingTime: { type: 'number' },
            approvalRate: { type: 'number' },
            totalSize: { type: 'integer' },
            typeDistribution: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            },
            uploadTrend: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  date: { type: 'string' },
                  uploads: { type: 'integer' },
                  approvals: { type: 'integer' },
                  rejections: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { startDate?: string; endDate?: string } }>, reply: FastifyReply) => {
    try {
      const { startDate, endDate } = request.query;
      
      const analytics = await fastify.analyticsService.getDocumentAnalytics({
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      logger.info('Document analytics retrieved', {
        userId: (request as any).user?.id,
        startDate,
        endDate
      });

      return reply.status(200).send(analytics);
    } catch (error) {
      logger.error('Failed to get document analytics', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve document analytics' });
    }
  });

  // Generate analytics report
  fastify.get('/analytics/reports', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Generate analytics report',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          type: { type: 'string', enum: ['clients', 'rfps', 'onboarding', 'documents', 'overview'] },
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' },
          format: { type: 'string', enum: ['json', 'csv'], default: 'json' }
        },
        required: ['type']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            reportId: { type: 'string' },
            type: { type: 'string' },
            generatedAt: { type: 'string' },
            period: {
              type: 'object',
              properties: {
                startDate: { type: 'string' },
                endDate: { type: 'string' }
              }
            },
            summary: {
              type: 'object',
              additionalProperties: true
            },
            data: {
              type: 'array',
              items: {
                type: 'object',
                additionalProperties: true
              }
            },
            insights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string' },
                  message: { type: 'string' },
                  severity: { type: 'string' },
                  recommendation: { type: 'string' }
                }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetReportQuery }>, reply: FastifyReply) => {
    try {
      const { type, startDate, endDate, format = 'json' } = request.query;
      
      const report = await fastify.analyticsService.generateReport({
        type,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined,
        format
      });

      logger.info('Analytics report generated', {
        userId: (request as any).user?.id,
        reportType: type,
        format,
        startDate,
        endDate
      });

      if (format === 'csv') {
        return reply
          .header('Content-Type', 'text/csv')
          .header('Content-Disposition', `attachment; filename="${type}_report_${new Date().toISOString().split('T')[0]}.csv"`)
          .send(report.csvData);
      }

      return reply.status(200).send(report);
    } catch (error) {
      logger.error('Failed to generate analytics report', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to generate analytics report' });
    }
  });

  // Get insights and recommendations
  fastify.get('/analytics/insights', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Get AI-powered insights and recommendations',
      tags: ['Analytics'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          category: { type: 'string', enum: ['performance', 'efficiency', 'quality', 'growth'] },
          startDate: { type: 'string', format: 'date' },
          endDate: { type: 'string', format: 'date' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            insights: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  category: { type: 'string' },
                  type: { type: 'string' },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  severity: { type: 'string' },
                  impact: { type: 'string' },
                  recommendation: { type: 'string' },
                  actionItems: {
                    type: 'array',
                    items: { type: 'string' }
                  },
                  metrics: {
                    type: 'object',
                    additionalProperties: true
                  },
                  generatedAt: { type: 'string' }
                }
              }
            },
            summary: {
              type: 'object',
              properties: {
                totalInsights: { type: 'integer' },
                criticalIssues: { type: 'integer' },
                opportunities: { type: 'integer' },
                overallScore: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: { category?: string; startDate?: string; endDate?: string } }>, reply: FastifyReply) => {
    try {
      const { category, startDate, endDate } = request.query;
      
      const insights = await fastify.analyticsService.getInsights({
        category,
        startDate: startDate ? new Date(startDate) : undefined,
        endDate: endDate ? new Date(endDate) : undefined
      });

      logger.info('Analytics insights retrieved', {
        userId: (request as any).user?.id,
        category,
        insightCount: insights.insights.length
      });

      return reply.status(200).send(insights);
    } catch (error) {
      logger.error('Failed to get analytics insights', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve analytics insights' });
    }
  });
}