# ESTRATIX Naming Conventions

**Version:** 3.0
**Status:** Active
**Last Updated:** 2025-07-02

## 1. Introduction

This document outlines the standardized naming conventions for all key components within the ESTRATIX framework. Adherence to these conventions is crucial for maintaining clarity, consistency, traceability, and facilitating automation across the framework. This version introduces a process-centric naming scheme to enhance component relationship tracking.

## 2. Core Principles

- **Traceability**: Component names must clearly link to their parent Command Office and, where applicable, their parent Process.
- **Hierarchy**: The naming reflects the architectural hierarchy: Flows orchestrate Crews, and Crews (implementing a Process) orchestrate Agents and Tasks.
- **Clarity**: Descriptive names should be concise yet unambiguous.

## 3. Component ID Format

To ensure simplicity and stability, all components use a standardized ID format that is independent of the organizational structure. This change removes the Command Office prefix from the ID itself, relying on globally unique identifiers for each component type.

**Format**: `[type_code][numeric_id]`

- **`[type_code]`**: A short, lowercase code representing the component type.
  - `p`: Process
  - `f`: Flow
  - `c`: Crew
  - `a`: Agent
  - `t`: Task
  - `k`: Tool (from "Capability")
  - `s`: Service
  - `pt`: Pattern
  - `m`: Data Model

- **`[numeric_id]`**: A 3-digit, zero-padded, **globally unique** identifier for that component type. The uniqueness is maintained across the entire ESTRATIX system, regardless of which Command Office owns the component. The next available ID for each type is tracked in the `model_matrix.md`.

**Examples**:

- Process: `p004`
- Flow: `f001`
- Agent: `a001`
- Task: `t001`
- Tool: `k001`

### 3.1. Parent-Child Relationships in Naming

To enhance traceability and clearly define the operational hierarchy, component implementation filenames will encode their direct parent's ID. This creates a clear, self-documenting link between orchestrated components.

The core hierarchy is: **Flow -> Crew -> Agent/Task -> Tool**.

- **Flows** are the top-level orchestrators in this chain.
- **Crews** are orchestrated by a single Flow.
- **Agents and Tasks** belong to a single Crew.
- **Tools** are designed for reusability and are not tied to a specific parent in their name. Their relationships are managed via agent definitions.

This convention applies primarily to the filenames of implementation files (e.g., `.py`, `.yaml`), not the definition documents (`.md`). The globally unique ID (`[type_code][numeric_id]`) remains the primary identifier within matrices and definitions.

## 4. Architectural Note: Process vs. Crew

Based on CrewAI framework specifics, the architectural implementation has been refined:

- **Process (`P_###`)**: Remains a high-level, framework-agnostic definition of a business or technical workflow. It is a documentation and design artifact.
- **Crew (`C_###`)**: Is the framework-specific (e.g., CrewAI) implementation that executes a defined Process. A Crew consists of Agents and Tasks.
- **Flow (`F_###`)**: Orchestrates one or more Crews to achieve a broader business objective.

The hierarchy is: **Flow -> Crew(s) -> Agent(s) -> Task(s) -> Tool(s)**.

## 5. Definition Document Conventions

### 5.1. Filename Convention

Definition documents are named using the full component ID and a descriptive name.

**Format**: `[id]_[DescriptiveName_PascalCase].md`

- **Example (Process)**: `p004_TechnologyScoutingProcess.md`
- **Example (Agent)**: `a001_TechnologyScoutAgent.md`

Diagram files (e.g., `.mmd`) follow the same pattern.

### 5.2. Directory Structure

Definition documents are stored in a structure that reflects their Command Office ownership.

**Format**: `docs/[component_plural]/[officer_acronym_lowercase]/[id]_[DescriptiveName_PascalCase].md`

- **Example (Process)**: `docs/processes/cto/p004_TechnologyScoutingProcess.md`
- **Example (Agent)**: `docs/agents/cto/a001_TechnologyScoutAgent.md`
- **Example (Crew)**: `docs/crews/cto/c001_ScoutingCrew.md`

## 6. Implementation Naming

Source code files are named to reflect their position in the hierarchy and are stored in a flattened directory structure within their component type folder. The Command Office acronym is no longer used in the implementation path to simplify the structure.

### 6.1. Directory Structure

**Format**: `src/frameworks/[framework]/[component_plural]/`

- **Example**: `src/frameworks/crewai/crews/`
- **Example**: `src/frameworks/crewai/agents/`

### 6.2. Filename Convention

**Format**: `[parent_id]_[component_id]_[descriptive_name_snake_case].[ext]`

- **Flow**: `[id]_[name].py`
  - *Example*: `f002_knowledge_monitoring_flow.py`
- **Crew**: `[flow_id]_[crew_id]_[name].py`
  - *Example*: `f002_c005_knowledge_monitoring_crew.py`
- **Agent (YAML)**: `[crew_id]_[agent_id]_[name].yaml`
  - *Example*: `c005_a021_knowledge_monitor_agent.yaml`
- **Task (YAML)**: `[crew_id]_[task_id]_[name].yaml`
  - *Example*: `c005_t001_knowledge_monitor_tasks.yaml`
- **Tool (Domain Layer)**: `[tool_id]_[descriptive_name_snake_case].py`
  - *Example*: `k001_web_content_fetcher.py`
  - *Note*: These are framework-agnostic tools in `src/domain/tools/`.

- **Tool (Framework Layer)**: `[parent_agent_id]_[tool_id]_[descriptive_name_snake_case].py`
  - *Example*: `a025_k001_web_content_fetcher.py`
  - *Note*: These are framework-specific tool implementations in `src/frameworks/[framework]/tools/` and are directly tied to an agent.

## 7. Matrix and Registry Entries

When listing components in matrices (e.g., `process_matrix.md`), the primary identifier used must be the full component ID (e.g., `p001`).

## 8. Revision History

| Version | Date       | Author        | Changes                                                                                             |
| :------ | :--------- | :------------ | :-------------------------------------------------------------------------------------------------- |
| 4.0     | 2025-07-05 | Cascade (AI)  | Changed all type codes to lowercase and removed underscore separator (e.g., `p001`, `f001`, `pt001`). |
| 2.0     | 2025-06-25 | Cascade (AI)  | Overhauled to a process-centric model. Introduced Crew component. Added hierarchical ID format.     |
| 1.0     | 2025-05-24 | Cascade (AI)  | Initial definition of naming conventions.                                                           |
