# ESTRATIX System Prompt Matrix

---

## 1. Overview

This matrix serves as the central registry for all core system prompts used by ESTRATIX agents. It provides a version-controlled, auditable catalog of the foundational instructions that define agent personas, capabilities, and operational guardrails. This registry is critical for ensuring consistency, security, and performance across the agentic framework, as mandated by rule `R-AE-001`.

---

## 2. Prompt Inventory

| Prompt ID | Agent/Persona Name | AgentID | Purpose & Description | Source Location | Version | Status | Owner/Maintainer | Notes |
|-----------|--------------------|---------|-----------------------|-----------------|---------|--------|------------------|-------|
| SP-GEN-001 | Cascade (Default) | `N/A` | The core persona for the primary AI coding assistant, defining its role, capabilities, and communication style. | `/docs/components/system_prompts/cascade_default.md` | 1.0 | Active | CTO | The foundational prompt for general development tasks. |
| SP-SEC-001 | SecOps Sentinel | `N/A` | An agent persona focused on security analysis, vulnerability scanning, and policy enforcement. | `/docs/components/system_prompts/secops_sentinel.md` | 1.0 | Active | CSO | Specialized for security-related workflows. |
| SP-DEV-001 | DevOps Orchestrator | `CTO_A001` | An agent persona specialized in CI/CD, Infrastructure as Code, and Kubernetes management. | `/docs/components/system_prompts/devops_orchestrator.md` | 1.0 | Active | CTO | Governs infrastructure and deployment automation. |
| SP-LLM-001 | MLOps/LLMOps Analyst | `N/A` | An agent persona for monitoring, evaluating, and optimizing AI/ML models and prompt performance. | `/docs/components/system_prompts/mlops_analyst.md` | 1.0 | Active | CTO | Key for managing the AI/ML lifecycle. |

---

## 3. Maintenance

This matrix must be updated whenever a new core system prompt is created or an existing one is versioned. All prompts listed here must undergo a peer review process, including a security audit for potential injection vulnerabilities.
