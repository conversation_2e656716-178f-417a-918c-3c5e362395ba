# ESTRATIX Process Definition: Define Analytical Scope & Retrieve Data (CKO_P007)

## 1. Metadata

*   **ID:** CKO_P007
*   **Process Name:** Define Analytical Scope & Retrieve Data
*   **Version:** 1.2
*   **Status:** Definition
*   **Owner(s):** `CKO_A006_AnalyticalScopingAgent`, Lead Knowledge Analyst
*   **Related Flow(ID):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration`
*   **Date Created:** 2025-05-27
*   **Last Updated:** 2025-05-27
*   **SOP References:** CKO_SOP_008: Analytical Scoping and Data Retrieval Protocol

## 2. Purpose

*   To interpret incoming `CKO_M004_AnalyticalQueryOrRequest`s, clarify and refine the analytical scope, identify the precise data requirements, and retrieve the necessary `CKO_M003_CuratedKnowledgeAsset`s and any other relevant data from ESTRATIX data stores (primarily the Vector Database) to serve as the foundation for subsequent analysis.

## 3. Goals

*   Ensure 100% of accepted `CKO_M004_AnalyticalQueryOrRequest`s have a clearly defined and documented analytical scope within 1 business day of acceptance.
*   Retrieve relevant data sets for 95% of scoped analyses with high precision (minimizing irrelevant data) and recall (capturing most relevant data).
*   Reduce ambiguity in analytical requests by an average of 70% through clarification and refinement steps.

## 4. Scope

*   **In Scope:**
    *   Receiving and parsing `CKO_M004_AnalyticalQueryOrRequest`.
    *   Interacting with the requester (human or agent) for clarification if the request is ambiguous or incomplete.
    *   Translating the request's objectives and key questions into specific data retrieval queries (e.g., semantic search queries for the Vector DB, metadata filters).
    *   Identifying optimal data sources (`CKO_M003_CuratedKnowledgeAsset` assets, potentially other ESTRATIX data models).
    *   Executing data retrieval operations against the ESTRATIX Vector Database and other relevant data stores.
    *   Compiling the retrieved data into a structured dataset ready for analysis.
    *   Documenting the final analytical scope and the data retrieval strategy.
*   **Out of Scope:**
    *   Performing the actual data analysis (handled by subsequent tasks/processes in `CKO_F002_KnowledgeAnalysisAndInsightGeneration`).
    *   Generating new knowledge assets (this process consumes existing ones).
    *   Approving or rejecting the initial `CKO_M004_AnalyticalQueryOrRequest` (assuming it's already been accepted into the flow).

## 5. Triggers

*   An accepted `CKO_M004_AnalyticalQueryOrRequest` is ready for processing within the `CKO_F002_KnowledgeAnalysisAndInsightGeneration` flow.
*   A request for re-scoping or additional data retrieval for an ongoing analysis.

## 6. Inputs

*   **Input 1: `CKO_M004_AnalyticalQueryOrRequest`**
    *   Description: The detailed request for analysis.
    *   Source/Format: ESTRATIX system (Pydantic model instance or JSON object).
    *   Data Format & Structure: As defined in `CKO_M004_AnalyticalQueryOrRequest.md`.
*   **Input 2: Access to ESTRATIX Data Stores**
    *   Description: Read access to `CKO_M003_CuratedKnowledgeAsset` store (Vector DB) and potentially other relevant ESTRATIX data models.
    *   Source/Format: System credentials/APIs configured for the responsible agent.
*   **Input 3: (Optional) Interaction with Requester**
    *   Description: Communication channel for clarifying ambiguities in the request.
    *   Source/Format: ESTRATIX messaging system or direct API call if agent-to-agent.

## 7. Outputs

*   **Output 1: Defined Analytical Scope Document**
    *   Description: A clear, documented statement of the finalized analytical scope, including objectives, key questions, boundaries, and assumptions. This might be an update to the original `CKO_M004_AnalyticalQueryOrRequest` or a separate linked document.
    *   Destination/Format: ESTRATIX document store, linked to the `CKO_M004_AnalyticalQueryOrRequest` request.
    *   Data Format & Structure: Markdown, JSON, or structured data within a database.
*   **Output 2: Compiled Dataset for Analysis**
    *   Description: A collection of `CKO_M003_CuratedKnowledgeAsset`s and any other retrieved data, structured and ready for use by analytical agents/tools.
    *   Destination/Format: ESTRATIX temporary storage or passed directly to the next analytical task/process.
    *   Data Format & Structure: List of asset objects, JSON, pandas DataFrame, etc., depending on the nature of the analysis.
*   **Output 3: Data Retrieval Log**
    *   Description: A log detailing the queries executed, sources accessed, and a summary of data retrieved.
    *   Destination/Format: ESTRATIX Logging System.
    *   Data Format & Structure: Structured log entries.

## 8. Process Steps / Activities

1.  **Activity 1: Receive and Parse Analytical Request:** The `CKO_A006_AnalyticalScopingAgent` receives the `CKO_M004_AnalyticalQueryOrRequest`.
2.  **Activity 2: Clarify and Refine Scope (if needed):**
    *   Analyze the request for clarity, completeness, and feasibility.
    *   If ambiguities exist, formulate clarification questions.
    *   Interact with the requesting officer/agent to resolve ambiguities and refine the scope.
3.  **Activity 3: Finalize and Document Analytical Scope:** Based on the request and clarifications, formally document the precise analytical scope, objectives, and key questions.
4.  **Activity 4: Formulate Data Retrieval Strategy:**
    *   Translate the scoped analytical questions into specific search criteria (keywords, semantic concepts, metadata filters, date ranges) for `CKO_M003_CuratedKnowledgeAsset` assets.
    *   Identify if other ESTRATIX data models are needed.
5.  **Activity 5: Execute Data Retrieval Queries:**
    *   Perform semantic searches on the Vector DB using generated embeddings of query terms/concepts.
    *   Apply metadata filters to narrow down results from the Vector DB or other structured data stores.
6.  **Activity 6: Aggregate and Prepare Retrieved Data:**
    *   Collect all retrieved `CKO_M003_CuratedKnowledgeAsset` assets and other data.
    *   Structure the data into a coherent dataset suitable for the planned analysis (e.g., list of asset objects, combined text corpus).
7.  **Activity 7: Log Retrieval Process:** Record details of the data retrieval, including queries used and results obtained.

## 9. Roles / Responsible Agent(s)

*   **Primary Agent(s):** `CKO_A006_AnalyticalScopingAgent` (responsible for interpreting requests, clarifying scope, formulating queries, and retrieving data).
*   **Supporting Agent(s)/Human Roles:**
    *   `Requesting Officer/Agent`: Provides clarifications on their `CKO_M004_AnalyticalQueryOrRequest` request.
    *   `Lead Knowledge Analyst` (Human): Assists with complex scoping decisions or novel data retrieval strategies.
    *   `CKO_A005_VectorizationAgent` (indirectly, by ensuring assets are embedded for semantic search).

## 10. Tools & Systems Used

*   ESTRATIX Vector Database (QdrantDB) client/SDK for semantic search and filtered retrieval.
*   Access to `CKO_M003_CuratedKnowledgeAsset` data store.
*   (Potentially) Other ESTRATIX data model stores and their query interfaces.
*   ESTRATIX Communication/Messaging System (for clarifications).
*   ESTRATIX Logging System.
*   Text processing libraries (for query formulation if needed).

## 11. Key Performance Indicators (KPIs)

*   **KPI 1:** Time to Finalize Scope: Average time from request acceptance to documented final scope.
*   **KPI 2:** Data Retrieval Relevance: Percentage of retrieved assets deemed relevant by downstream analysts (measured via feedback or automated checks).
*   **KPI 3:** Data Retrieval Completeness: Percentage of truly relevant assets successfully retrieved (harder to measure, may require sampling or expert review).
*   **KPI 4:** Number of Clarification Cycles per Request: Aim to minimize through better initial requests or more intelligent scoping by the agent.

## 12. Risk Management / Contingency Planning

*   **Risk 1:** Ambiguous or overly broad analytical requests leading to scope creep or irrelevant data retrieval.
    *   Mitigation: Robust clarification loop with requester; `CKO_A006_AnalyticalScopingAgent` trained to ask targeted questions; use of predefined analytical templates.
*   **Risk 2:** Poorly formulated retrieval queries leading to missed data or excessive irrelevant data.
    *   Mitigation: Iterative query refinement capabilities for `CKO_A006_AnalyticalScopingAgent`; use of query expansion techniques; allow human review for critical queries.
*   **Risk 3:** Required data does not exist or is insufficient in the knowledge base.
    *   Mitigation: Report data gap to requester and `CKO_F001_ExternalKnowledgeIngestionAndCuration` (for potential new sourcing). Adjust scope if possible.
*   **Risk 4:** Performance issues with Vector DB or other data stores during large retrievals.
    *   Mitigation: Optimize queries; implement pagination/batching for retrieval; ensure data stores are adequately provisioned.

## 13. Revision History

| Version | Date       | Author        | Changes                                                                                                |
| :------ | :--------- | :------------ | :----------------------------------------------------------------------------------------------------- |
| 1.0     | 2025-05-27 | Cascade AI (Refactored) | Initial Definition. Refactored from KNO_P003. |
| 1.1     | 2025-05-27 | Cascade AI    | Renumbered from CKO_P005 to CKO_P006 as part of CKO process list refactoring. Internal ID updated. |
| 1.2     | 2025-05-27 | Cascade AI | Renumbered from CKO_P006 to CKO_P007 to accommodate new CKO_P001. Process content version 1.0. |
