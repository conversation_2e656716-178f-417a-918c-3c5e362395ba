# ESTRATIX WBS Dictionary Template

---

## Document Control

- **Template Version:** ESTRATIX-TEMPL-PM-WBSD-1.0
- **Document Version:** `{{Document Version, e.g., 1.0}}`
- **Status:** `{{Draft | Under Review | Approved | Baseline}}`
- **Author(s):** `AGENT_WBS_Generator` (ID: AGENT_CPO_WBS001), `{{Project Manager Name}}`
- **Reviewer(s):** `{{Project Team}}`, `AGENT_Governance_Auditor` (ID: AGENT_CPO_GA001)
- **Approver(s):** `{{Project Sponsor}}`, `{{Project Manager}}`
- **Date Created:** `{{YYYY-MM-DD}}`
- **Last Updated Date:** `{{YYYY-MM-DD}}`
- **Security Classification:** `{{ESTRATIX Confidential}}`
- **ESTRATIX Project ID:** `{{Project ID}}`
- **Source Document(s):** `{{Link to Detailed Scope Statement}}`

---

## Guidance for Use (ESTRATIX)

### 1. Purpose

The Work Breakdown Structure (WBS) Dictionary is a companion document to the WBS. It provides detailed descriptions of the work, deliverables, activities, and other planning information for each component in the WBS. Its purpose is to eliminate ambiguity and ensure a clear, common understanding of the work to be performed.

### 2. Process

1. **Creation:** The WBS Dictionary is created concurrently with the WBS, immediately after the `Detailed Scope Statement` is approved.
2. **Input:** The primary input is the list of deliverables from the `Detailed Scope Statement`.
3. **Decomposition:** The `AGENT_WBS_Generator` decomposes major deliverables into smaller, manageable work packages.
4. **Detailing:** For each work package, the agent or project manager fills in the details in the dictionary table below.
5. **Baseline:** Once approved, the WBS and its dictionary form the scope baseline for the project.

### 3. Agent Integration

- **`AGENT_WBS_Generator` (ID: AGENT_CPO_WBS001):** Can be tasked to generate the initial WBS and a draft of this dictionary from an approved `Detailed Scope Statement`.
- **`AGENT_Task_Scheduler` (ID: AGENT_CPO_TS001):** Uses the work packages defined in this dictionary as the primary input for creating the detailed project schedule.
- **`AGENT_Validation_Auditor` (ID: AGENT_CPO_VA001):** Can be tasked to audit the WBS Dictionary against the scope statement to ensure 100% of the scope is covered.

---

## WBS Dictionary

| WBS ID | Element Name (Work Package) | Description of Work | Key Deliverables | Owner (Agent/Role) | Effort (Hours) |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **1.0** | **Project Management** | `{{Overall management, coordination, and control of the project.}}` | `{{Project Plan, Status Reports, Change Log, Closure Report}}` | `{{CPO_AXXX_PM}}` | `{{XXX}}` |
| 1.1 | Planning | `{{Activities to define scope, schedule, budget, and plans.}}` | `{{This WBS Dictionary, Schedule, Budget}}` | `{{CPO_AXXX_PM}}` | `{{XXX}}` |
| **2.0** | **`{{Major Deliverable 1 Name}}`** | `{{High-level description of the deliverable from the Scope Statement.}}` | `{{The major deliverable itself.}}` | `{{Technical Lead}}` | `{{XXX}}` |
| 2.1 | `{{Work Package 2.1 Name}}` | `{{Detailed description of the work required for this package.}}` | `{{Specific, tangible output of this work package.}}` | `{{Developer_A}}` | `{{XXX}}` |
| 2.2 | `{{Work Package 2.2 Name}}` | `{{Detailed description of the work required for this package.}}` | `{{Specific, tangible output of this work package.}}` | `{{Developer_B}}` | `{{XXX}}` |
| 2.2.1 | `{{Sub-Package/Activity}}` | `{{Even more granular description of work.}}` | `{{Component or sub-deliverable.}}` | `{{Developer_B}}` | `{{XXX}}` |
| **3.0** | **`{{Major Deliverable 2 Name}}`** | `{{High-level description of the deliverable from the Scope Statement.}}` | `{{The major deliverable itself.}}` | `{{QA Lead}}` | `{{XXX}}` |
| 3.1 | `{{Work Package 3.1 Name}}` | `{{Detailed description of the work required for this package.}}` | `{{Test Plans, Test Cases}}` | `{{QA_Agent_A}}` | `{{XXX}}` |
| `{{...}}` | `{{...}}` | `{{...}}` | `{{...}}` | `{{...}}` | `{{...}}` |

---

> **Agent Prompt (`AGENT_WBS_Generator`):** "Generate the WBS Dictionary for `{{Project ID}}` based on the `Detailed_Scope_Statement_ID`. Decompose each deliverable into work packages. Assign owners based on the project's resource plan."
>
> **Agent Prompt (`AGENT_Validation_Auditor`):** "Validate this WBS Dictionary against the `Detailed_Scope_Statement_ID`. Ensure 100% of the project scope is covered (100% Rule) and that there is no overlap between elements."

---
*This is a controlled ESTRATIX document. The information contained herein is proprietary and confidential. Unauthorized distribution is prohibited.*
