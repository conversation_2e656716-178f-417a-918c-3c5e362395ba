import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { projectService } from '../services/projectService';
import { logger } from '../utils/logger';

async function healthRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Basic health check
  fastify.get('/', async (request, reply) => {
    return {
      status: 'ok',
      service: 'project-management',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  });

  // Detailed health check
  fastify.get('/detailed', async (request, reply) => {
    try {
      const dbStatus = await projectService.checkDatabaseConnection();
      const serviceStatus = await projectService.getServiceStatus();
      
      const health = {
        status: 'ok',
        service: 'project-management',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: {
          database: dbStatus,
          services: serviceStatus
        }
      };
      
      const allHealthy = Object.values(health.checks).every(
        check => (check as any).status === 'ok'
      );
      
      if (!allHealthy) {
        reply.status(503);
        health.status = 'degraded';
      }
      
      return health;
    } catch (error) {
      logger.error('Health check failed:', error);
      return reply.status(503).send({
        status: 'error',
        service: 'project-management',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Readiness probe
  fastify.get('/ready', async (request, reply) => {
    try {
      const isReady = await projectService.isReady();
      
      if (isReady) {
        return {
          status: 'ready',
          service: 'project-management',
          timestamp: new Date().toISOString()
        };
      } else {
        return reply.status(503).send({
          status: 'not-ready',
          service: 'project-management',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      logger.error('Readiness check failed:', error);
      return reply.status(503).send({
        status: 'not-ready',
        service: 'project-management',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Liveness probe
  fastify.get('/live', async (request, reply) => {
    return {
      status: 'alive',
      service: 'project-management',
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };
  });

  // Metrics endpoint
  fastify.get('/metrics', async (request, reply) => {
    try {
      const metrics = await projectService.getMetrics();
      
      return {
        service: 'project-management',
        timestamp: new Date().toISOString(),
        metrics
      };
    } catch (error) {
      logger.error('Metrics collection failed:', error);
      return reply.status(500).send({
        error: 'Failed to collect metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

export default fp(healthRoutes);