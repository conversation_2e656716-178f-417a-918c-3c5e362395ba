# ESTRATIX FastAPI Microservice Boilerplate Definition

---
**Document Control**
- **Version:** 1.0
- **Status:** Baseline
- **Author(s):** ESTRATIX CTO Office, `AGENT_Architecture_Framework_Expert` (ID: AGENT_AFE001)
- **Reviewer(s):** ESTRATIX Lead Architects, `AGENT_Hexagonal_Architect_HA001`
- **Approver(s):** ESTRATIX CTO
- **Date Created:** {{YYYY-MM-DD}}
- **Last Updated Date:** {{YYYY-MM-DD}}
- **Security Classification:** ESTRATIX Internal
- **ESTRATIX Document ID:** ESTRATIX-DEF-SED-BP-FASTAPI-001
- **Distribution List:** All ESTRATIX Development Teams, All ESTRATIX Agents involved in Code Generation or Project Setup
---

## 0. Introduction

### 0.1. Purpose
This document defines the official ESTRATIX boilerplate for creating Python-based RESTful microservices using the FastAPI framework. It ensures all new services start with a standardized, robust, and compliant foundation adhering to **Hexagonal Architecture** and **Domain-Driven Design (DDD)**.

### 0.2. Scope
This boilerplate includes a pre-configured directory structure, core components for a RESTful API, testing setup, containerization, and CI/CD integration stubs. It is intended for building new microservices from scratch.

### 0.3. Core ESTRATIX Principles Embodied
- **SOLID Principles**
- **DRY (Don't Repeat Yourself)**
- **Test-Driven Development (TDD)** readiness
- **Clear Separation of Concerns** (Hexagonal Architecture)
- **Security by Design**
- **Observability Hooks** (logging, metrics, tracing)
- **Configuration Management Best Practices**

---

## 1. Boilerplate Definition

### 1.1. Boilerplate ID
`BP_PY_FASTAPI_HEX_V1`

### 1.2. Target Technology Stack
- **Language:** Python 3.10+
- **Core Framework:** FastAPI
- **Dependency Management:** Poetry
- **Key Libraries:**
  - Pydantic (for data validation and settings)
  - SQLAlchemy (for data persistence)
  - Alembic (for database migrations)
  - Uvicorn (for ASGI server)
  - Pytest (for testing)
  - `python-jose` (for JWT handling)

### 1.3. Key Architectural Patterns
- **Primary:** Hexagonal Architecture, Domain-Driven Design
- **Supporting:** Repository Pattern, Unit of Work (optional stub), Dependency Injection (via FastAPI)

---

## 2. Standardized Directory Structure

```
[project_root]/
├── .github/workflows/         # GitHub Actions workflows (lint, test, build)
├── docs/                      # Project-specific documentation (e.g., ADRs)
├── scripts/                   # Utility scripts (e.g., run-migrations.sh)
├── src/                       # Source code
│   ├── domain/                # Core business logic, entities, value objects, domain services
│   │   ├── __init__.py
│   │   ├── models/            # Pydantic models for domain entities/value objects
│   │   ├── repositories/      # Abstract repository interfaces (ports)
│   │   └── services/          # Domain services
│   ├── application/           # Application-specific logic, use cases
│   │   ├── __init__.py
│   │   ├── services/          # Application services / use cases orchestrating domain logic
│   │   ├── ports/             # Interfaces for driving adapters (e.g., API input DTOs) and driven adapters
│   │   └── dto/               # Data Transfer Objects for application layer
│   ├── infrastructure/        # Adapters, configuration, external concerns
│   │   ├── __init__.py
│   │   ├── adapters/          # Concrete implementations of ports
│   │   │   ├── api/           # Driving/Primary Adapter: FastAPI routers and dependencies
│   │   │   ├── db/            # Driven/Secondary Adapter: SQLAlchemy models, repository implementations
│   │   │   └── external/      # Driven/Secondary Adapter: External service integrations (e.g., email, payments)
│   │   ├── config/            # Configuration loading (Pydantic Settings)
│   │   ├── logging/           # Logging setup
│   │   └── security/          # Security-related components (e.g., auth middleware, password hashing)
│   ├── main.py                # Application entry point (FastAPI app instantiation)
│   └── __init__.py
├── tests/                     # Automated tests
│   ├── __init__.py
│   ├── unit/                  # Unit tests (isolated component tests)
│   ├── integration/           # Integration tests (component interactions)
│   └── e2e/                   # End-to-end tests (full system tests via API)
├── .dockerignore
├── .env.example               # Example environment variables
├── .gitignore
├── Dockerfile                 # Containerization definition
├── pyproject.toml             # Dependencies (Poetry)
├── README.md                  # Project-specific README
└── alembic.ini                # Alembic configuration
```

---

## 3. Core Components & Implementation Guidance

- **Application Entry Point (`src/main.py`):** Instantiates the FastAPI application, includes API routers from `src/infrastructure/adapters/api/`, and registers global middleware and exception handlers.
- **Configuration (`src/infrastructure/config/`):** Uses Pydantic's `BaseSettings` to load configuration from environment variables, providing type-safe access to settings.
- **Logging (`src/infrastructure/logging/`):** Implements structured JSON logging using the standard `logging` module, configured to be easily parsable by observability platforms.
- **Error Handling:** Uses FastAPI's exception handlers to catch specific business exceptions and generic server errors, returning standardized JSON error responses.
- **API Design (`src/infrastructure/adapters/api/`):** Routers are organized by feature. Pydantic is used for request/response model validation. FastAPI's dependency injection system is used to provide application services to endpoints.
- **Data Persistence (`src/infrastructure/adapters/db/`):** SQLAlchemy is used for ORM. Repository implementations handle session management and data translation between domain models and DB models. Alembic is configured for schema migrations.
- **Security (`src/infrastructure/security/`):** Includes stubs for OAuth2 password bearer flow, password hashing utilities, and middleware for handling JWTs.

---

## 4. Testing Strategy (`tests/`)

- **Framework:** Pytest is the standard testing framework.
- **Tools:** `pytest-mock` for mocking, `coverage.py` for code coverage analysis, and FastAPI's `TestClient` for API testing.
- **Unit Tests:** Focus on testing domain logic and application services in isolation, mocking all external dependencies (like repositories).
- **Integration Tests:** Test the integration between application services and their infrastructure adapters (e.g., testing a service with a real test database).

---

## 5. Dockerization & CI/CD

- **`Dockerfile`:** A production-ready, multi-stage Dockerfile is provided to create a minimal and secure container image.
- **CI/CD (`.github/workflows/`):** Example GitHub Actions workflows are included for:
  - Linting with `flake8` and formatting with `black`.
  - Running all tests with `pytest`.
  - Building and pushing the Docker image to a container registry.

---

## 6. How to Use This Boilerplate

**Agent Prompt:** `AGENT_Boilerplate_Generator_BG001` - Generate a new project instance named `[New Project Name]` using the `BP_PY_FASTAPI_HEX_V1` boilerplate, and initialize a Git repository.

**Manual Steps:**
1. Clone the boilerplate repository.
2. Run `poetry install` to install dependencies.
3. Copy `.env.example` to `.env` and configure environment variables.
4. Run `alembic upgrade head` to apply database migrations.
5. Start the server with `poetry run uvicorn src.main:app --reload`.

---

**ESTRATIX Controlled Deliverable**
*This document defines the standard for ESTRATIX FastAPI microservices. It is subject to ESTRATIX document management and version control policies.*
