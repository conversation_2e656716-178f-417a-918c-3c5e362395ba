import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { logger } from '@/utils/logger';

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  services: {
    database: 'connected' | 'disconnected' | 'unknown';
    email: 'available' | 'unavailable' | 'unknown';
    storage: 'available' | 'unavailable' | 'unknown';
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  system: {
    platform: string;
    nodeVersion: string;
    pid: number;
  };
}

export interface ReadinessResponse {
  ready: boolean;
  timestamp: string;
  checks: {
    services: boolean;
    dependencies: boolean;
    configuration: boolean;
  };
  details?: string;
}

export interface LivenessResponse {
  alive: boolean;
  timestamp: string;
  uptime: number;
}

export async function healthRoutes(fastify: FastifyInstance) {
  // Health check endpoint
  fastify.get('/health', {
    schema: {
      description: 'Health check endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string', enum: ['healthy', 'unhealthy'] },
            timestamp: { type: 'string' },
            uptime: { type: 'number' },
            version: { type: 'string' },
            environment: { type: 'string' },
            services: {
              type: 'object',
              properties: {
                database: { type: 'string', enum: ['connected', 'disconnected', 'unknown'] },
                email: { type: 'string', enum: ['available', 'unavailable', 'unknown'] },
                storage: { type: 'string', enum: ['available', 'unavailable', 'unknown'] }
              }
            },
            memory: {
              type: 'object',
              properties: {
                used: { type: 'number' },
                total: { type: 'number' },
                percentage: { type: 'number' }
              }
            },
            system: {
              type: 'object',
              properties: {
                platform: { type: 'string' },
                nodeVersion: { type: 'string' },
                pid: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const memoryPercentage = (usedMemory / totalMemory) * 100;

      // Check service health
      const serviceChecks = await performServiceChecks(fastify);
      
      const isHealthy = Object.values(serviceChecks).every(
        status => status === 'connected' || status === 'available'
      );

      const healthResponse: HealthCheckResponse = {
        status: isHealthy ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: serviceChecks,
        memory: {
          used: Math.round(usedMemory / 1024 / 1024), // MB
          total: Math.round(totalMemory / 1024 / 1024), // MB
          percentage: Math.round(memoryPercentage * 100) / 100
        },
        system: {
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        }
      };

      const statusCode = isHealthy ? 200 : 503;
      
      logger.debug('Health check performed', {
        status: healthResponse.status,
        uptime: healthResponse.uptime,
        memoryUsage: healthResponse.memory.percentage
      });

      return reply.status(statusCode).send(healthResponse);
    } catch (error) {
      logger.error('Health check failed', { error });
      
      const errorResponse: HealthCheckResponse = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        services: {
          database: 'unknown',
          email: 'unknown',
          storage: 'unknown'
        },
        memory: {
          used: 0,
          total: 0,
          percentage: 0
        },
        system: {
          platform: process.platform,
          nodeVersion: process.version,
          pid: process.pid
        }
      };

      return reply.status(503).send(errorResponse);
    }
  });

  // Readiness probe endpoint
  fastify.get('/ready', {
    schema: {
      description: 'Readiness probe endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string' },
            checks: {
              type: 'object',
              properties: {
                services: { type: 'boolean' },
                dependencies: { type: 'boolean' },
                configuration: { type: 'boolean' }
              }
            },
            details: { type: 'string' }
          }
        },
        503: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string' },
            checks: {
              type: 'object',
              properties: {
                services: { type: 'boolean' },
                dependencies: { type: 'boolean' },
                configuration: { type: 'boolean' }
              }
            },
            details: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const checks = await performReadinessChecks(fastify);
      const isReady = Object.values(checks).every(check => check === true);
      
      const readinessResponse: ReadinessResponse = {
        ready: isReady,
        timestamp: new Date().toISOString(),
        checks
      };

      if (!isReady) {
        const failedChecks = Object.entries(checks)
          .filter(([_, passed]) => !passed)
          .map(([check, _]) => check);
        
        readinessResponse.details = `Failed checks: ${failedChecks.join(', ')}`;
      }

      const statusCode = isReady ? 200 : 503;
      
      logger.debug('Readiness check performed', {
        ready: isReady,
        checks
      });

      return reply.status(statusCode).send(readinessResponse);
    } catch (error) {
      logger.error('Readiness check failed', { error });
      
      const errorResponse: ReadinessResponse = {
        ready: false,
        timestamp: new Date().toISOString(),
        checks: {
          services: false,
          dependencies: false,
          configuration: false
        },
        details: 'Readiness check encountered an error'
      };

      return reply.status(503).send(errorResponse);
    }
  });

  // Liveness probe endpoint
  fastify.get('/live', {
    schema: {
      description: 'Liveness probe endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            alive: { type: 'boolean' },
            timestamp: { type: 'string' },
            uptime: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    const livenessResponse: LivenessResponse = {
      alive: true,
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    };

    logger.debug('Liveness check performed', {
      uptime: livenessResponse.uptime
    });

    return reply.status(200).send(livenessResponse);
  });

  // Detailed health metrics endpoint
  fastify.get('/health/metrics', {
    schema: {
      description: 'Detailed health metrics endpoint',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            timestamp: { type: 'string' },
            process: {
              type: 'object',
              properties: {
                uptime: { type: 'number' },
                pid: { type: 'number' },
                ppid: { type: 'number' },
                platform: { type: 'string' },
                version: { type: 'string' }
              }
            },
            memory: {
              type: 'object',
              properties: {
                rss: { type: 'number' },
                heapTotal: { type: 'number' },
                heapUsed: { type: 'number' },
                external: { type: 'number' },
                arrayBuffers: { type: 'number' }
              }
            },
            cpu: {
              type: 'object',
              properties: {
                user: { type: 'number' },
                system: { type: 'number' }
              }
            },
            eventLoop: {
              type: 'object',
              properties: {
                delay: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      // Measure event loop delay
      const start = process.hrtime.bigint();
      await new Promise(resolve => setImmediate(resolve));
      const end = process.hrtime.bigint();
      const eventLoopDelay = Number(end - start) / 1000000; // Convert to milliseconds

      const metrics = {
        timestamp: new Date().toISOString(),
        process: {
          uptime: process.uptime(),
          pid: process.pid,
          ppid: process.ppid,
          platform: process.platform,
          version: process.version
        },
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
          arrayBuffers: Math.round(memoryUsage.arrayBuffers / 1024 / 1024) // MB
        },
        cpu: {
          user: cpuUsage.user / 1000, // Convert to milliseconds
          system: cpuUsage.system / 1000 // Convert to milliseconds
        },
        eventLoop: {
          delay: Math.round(eventLoopDelay * 100) / 100 // Round to 2 decimal places
        }
      };

      logger.debug('Health metrics collected', {
        memoryUsed: metrics.memory.heapUsed,
        eventLoopDelay: metrics.eventLoop.delay
      });

      return reply.status(200).send(metrics);
    } catch (error) {
      logger.error('Failed to collect health metrics', { error });
      return reply.status(500).send({ error: 'Failed to collect metrics' });
    }
  });
}

async function performServiceChecks(fastify: FastifyInstance): Promise<{
  database: 'connected' | 'disconnected' | 'unknown';
  email: 'available' | 'unavailable' | 'unknown';
  storage: 'available' | 'unavailable' | 'unknown';
}> {
  const checks = {
    database: 'unknown' as 'connected' | 'disconnected' | 'unknown',
    email: 'unknown' as 'available' | 'unavailable' | 'unknown',
    storage: 'unknown' as 'available' | 'unavailable' | 'unknown'
  };

  try {
    // Check database connection (mock for now)
    // In a real implementation, this would ping the database
    checks.database = 'connected';

    // Check email service
    if ((fastify as any).emailService) {
      // In a real implementation, this would test email connectivity
      checks.email = 'available';
    } else {
      checks.email = 'unavailable';
    }

    // Check storage service (mock for now)
    // In a real implementation, this would test file storage connectivity
    checks.storage = 'available';
  } catch (error) {
    logger.error('Service check failed', { error });
  }

  return checks;
}

async function performReadinessChecks(fastify: FastifyInstance): Promise<{
  services: boolean;
  dependencies: boolean;
  configuration: boolean;
}> {
  const checks = {
    services: false,
    dependencies: false,
    configuration: false
  };

  try {
    // Check if all services are initialized
    const fastifyAny = fastify as any;
    checks.services = !!(fastifyAny.clientService &&
                        fastifyAny.rfpService &&
                        fastifyAny.onboardingService &&
                        fastifyAny.documentService &&
                        fastifyAny.emailService &&
                        fastifyAny.analyticsService);

    // Check if all dependencies are available
    checks.dependencies = true; // Mock check - in real implementation, check external dependencies

    // Check if configuration is valid
    checks.configuration = !!(process.env.NODE_ENV && 
                             process.env.PORT);
  } catch (error) {
    logger.error('Readiness check failed', { error });
  }

  return checks;
}