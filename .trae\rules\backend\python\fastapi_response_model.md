---
description: "Enforce FastAPI routes have a ''response_model'' to ensure consistent API contracts."
globs: "[''**/routers/**/*.py'', ''**/api/**/*.py'']"
alwaysApply: false
---

# FastAPI Response Model Enforcement

## Context

- This rule applies to any Python file containing FastAPI route definitions (e.g., using `@router.get`, `@router.post`).
- It is critical for maintaining stable, predictable, and well-documented API endpoints.

## Requirements

- Every FastAPI route decorator (`@router.get`, `@router.post`, etc.) **MUST** include the `response_model` argument.
- The value of `response_model` must be a Pydantic model.

## Examples

<example>
A compliant FastAPI route that correctly specifies the response_model.

```python
from fastapi import APIRouter
from pydantic import BaseModel

router = APIRouter()

class Item(BaseModel):
    name: str
    price: float

@router.get("/items/{item_id}", response_model=Item)
def read_item(item_id: int):
    return {"name": "Thing", "price": 42.0}
```
</example>

<example type="invalid">
A non-compliant FastAPI route that is missing the response_model argument.

```python
from fastapi import APIRouter

router = APIRouter()

@router.get("/items/{item_id}") # Missing response_model
def read_item(item_id: int):
    return {"name": "Thing", "price": 42.0}
```
</example>

## Critical Rules

- **ALWAYS** define a `response_model` for every API route.
- **NEVER** omit the `response_model`, as it leads to inconsistent API contracts and poor documentation.
