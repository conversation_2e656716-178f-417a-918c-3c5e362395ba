{"python.languageServer": "None", "azureFunctions.projectSubpath": "src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions", "azureFunctions.deploySubpath": "src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions", "azureFunctions.postDeployTask": "npm install (functions)", "azureFunctions.projectLanguage": "TypeScript", "azureFunctions.projectRuntime": "~4", "debug.internalConsoleOptions": "neverOpen", "azureFunctions.projectLanguageModel": 4, "azureFunctions.preDeployTask": "npm prune (functions)", "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, ".yoyo": true}, "kiroAgent.configureMCP": "Disabled"}