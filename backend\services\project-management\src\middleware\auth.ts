import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { config } from '../config/environment';
import { logger } from '../utils/logger';

interface JWTPayload {
  userId: string;
  email: string;
  role: string;
  permissions?: string[];
  iat: number;
  exp: number;
}

export async function authenticateToken(
  request: FastifyRequest,
  reply: FastifyReply
) {
  try {
    const authHeader = request.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return reply.status(401).send({
        error: 'Access token required',
        code: 'MISSING_TOKEN'
      });
    }

    const decoded = jwt.verify(token, config.JWT_SECRET) as JWTPayload;
    request.user = decoded;
  } catch (error) {
    logger.error('Token verification failed:', error);
    return reply.status(403).send({
      error: 'Invalid or expired token',
      code: 'INVALID_TOKEN'
    });
  }
}

export function requireRole(roles: string[]) {
  return async function(request: FastifyRequest, reply: FastifyReply) {
    if (!request.user) {
      return reply.status(401).send({
        error: 'Authentication required',
        code: 'UNAUTHENTICATED'
      });
    }

    if (!roles.includes((request.user as any)?.role)) {
      return reply.status(403).send({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: roles,
        current: (request.user as any)?.role
      });
    }
  };
}

export function requirePermission(permissions: string[]) {
  return async function(request: FastifyRequest, reply: FastifyReply) {
    if (!request.user) {
      return reply.status(401).send({
        error: 'Authentication required',
        code: 'UNAUTHENTICATED'
      });
    }

    const userPermissions = (request.user as any)?.permissions || [];
    const hasPermission = permissions.some(permission => 
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      return reply.status(403).send({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: permissions,
        current: userPermissions
      });
    }
  };
}