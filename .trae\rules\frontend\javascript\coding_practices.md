# JavaScript Frontend Coding Practices

This document outlines the core coding standards for JavaScript frontend development within the ESTRATIX framework. These rules ensure our user interfaces are robust, maintainable, and performant.

## 1. Core Principles

- **Component-Based Architecture:** Build UIs from small, reusable, and independent components.
- **Declarative UI:** Describe what the UI should look like for a given state, and let the framework manage the DOM updates.
- **Unidirectional Data Flow:** Data should flow in a single direction to make application state more predictable and easier to debug.

## 2. Naming Conventions

- **Files and Directories:** Use `kebab-case` for component files (e.g., `user-profile.js`) and `PascalCase` for component names themselves (e.g., `UserProfile`).
- **Variables and Functions:** Use `camelCase` (e.g., `userName`, `fetchUserData()`).
- **Constants:** Use `UPPER_CASE_SNAKE_CASE` (e.g., `API_URL`).

## 3. Component Structure

- **Single Responsibility:** Each component should have one primary responsibility.
- **Separation of Concerns:** Separate logic (JavaScript), structure (HTML/JSX), and styling (CSS/CSS-in-JS) as much as possible.
- **Props for Configuration:** Use props to pass data and configuration down to child components. Keep props immutable.

## 4. State Management

- **Local State:** Keep state as local as possible. Lift state up only when it needs to be shared between multiple components.
- **Global State:** For complex applications, use a dedicated state management library (e.g., Redux, Vuex, Pinia) to manage global application state.

## 5. Styling

- **Scoped Styles:** Use CSS Modules, CSS-in-JS, or scoped styles in single-file components to prevent style conflicts.
- **Design Tokens:** Use variables (CSS Custom Properties) for colors, fonts, and spacing to ensure consistency.
