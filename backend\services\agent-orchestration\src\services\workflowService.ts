import { v4 as uuidv4 } from 'uuid';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';

export interface Workflow {
  id: string;
  name: string;
  description: string;
  type: 'sequential' | 'parallel' | 'conditional' | 'loop';
  status: 'draft' | 'active' | 'running' | 'completed' | 'failed' | 'paused';
  steps: WorkflowStep[];
  variables: Record<string, any>;
  metadata?: Record<string, any>;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
  startedAt?: Date;
  completedAt?: Date;
}

export interface WorkflowStep {
  id: string;
  name: string;
  type: 'agent_task' | 'condition' | 'loop' | 'parallel' | 'webhook' | 'delay';
  agentId?: string;
  taskDefinition?: any;
  condition?: string;
  dependencies: string[];
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  result?: any;
  error?: string;
  startedAt?: Date;
  completedAt?: Date;
}

export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'paused';
  currentStep?: string;
  variables: Record<string, any>;
  results: Record<string, any>;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
}

export class WorkflowService {
  private workflows: Map<string, Workflow> = new Map();
  private executions: Map<string, WorkflowExecution> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Workflow Service...');
      
      // Generate mock workflows for development
      if (environment.nodeEnv === 'development') {
        await this.generateMockWorkflows();
      }
      
      this.isInitialized = true;
      logger.info('Workflow Service initialized successfully');
    } catch (error) {
      logger.error(error, 'Failed to initialize Workflow Service');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up Workflow Service...');
      
      // Stop all running workflows
      for (const execution of this.executions.values()) {
        if (execution.status === 'running') {
          await this.pauseWorkflow(execution.id);
        }
      }
      
      this.workflows.clear();
      this.executions.clear();
      this.isInitialized = false;
      
      logger.info('Workflow Service cleanup completed');
    } catch (error) {
      logger.error(error, 'Error during Workflow Service cleanup');
      throw error;
    }
  }

  async getStatus(): Promise<string> {
    if (!this.isInitialized) {
      return 'initializing';
    }
    
    const runningWorkflows = Array.from(this.executions.values()).filter(e => e.status === 'running').length;
    const totalWorkflows = this.workflows.size;
    
    if (totalWorkflows === 0) {
      return 'healthy';
    }
    
    return 'healthy';
  }

  async getMetrics(): Promise<{ running: number; completed: number; failed: number; averageExecutionTime: number }> {
    const executions = Array.from(this.executions.values());
    
    const running = executions.filter(e => e.status === 'running').length;
    const completed = executions.filter(e => e.status === 'completed').length;
    const failed = executions.filter(e => e.status === 'failed').length;
    
    // Calculate average execution time for completed workflows
    const completedExecutions = executions.filter(e => e.status === 'completed' && e.completedAt);
    const averageExecutionTime = completedExecutions.length > 0 
      ? completedExecutions.reduce((sum, e) => {
          const duration = e.completedAt!.getTime() - e.startedAt.getTime();
          return sum + duration;
        }, 0) / completedExecutions.length
      : 0;
    
    return {
      running,
      completed,
      failed,
      averageExecutionTime
    };
  }

  async createWorkflow(data: Partial<Workflow>): Promise<Workflow> {
    const workflow: Workflow = {
      id: uuidv4(),
      name: data.name || 'Untitled Workflow',
      description: data.description || '',
      type: data.type || 'sequential',
      status: 'draft',
      steps: data.steps || [],
      variables: data.variables || {},
      metadata: data.metadata,
      createdBy: data.createdBy || 'system',
      organizationId: data.organizationId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.workflows.set(workflow.id, workflow);
    
    logger.info(`Workflow created: ${workflow.id} (${workflow.name})`);
    return workflow;
  }

  async getWorkflows(organizationId?: string): Promise<Workflow[]> {
    let workflows = Array.from(this.workflows.values());
    
    if (organizationId) {
      workflows = workflows.filter(w => w.organizationId === organizationId);
    }
    
    return workflows;
  }

  async getWorkflow(id: string, organizationId?: string): Promise<Workflow | null> {
    const workflow = this.workflows.get(id);
    
    if (!workflow) {
      return null;
    }
    
    if (organizationId && workflow.organizationId !== organizationId) {
      return null;
    }
    
    return workflow;
  }

  async executeWorkflow(workflowId: string, variables?: Record<string, any>): Promise<WorkflowExecution> {
    const workflow = this.workflows.get(workflowId);
    
    if (!workflow) {
      throw new Error('Workflow not found');
    }
    
    if (workflow.status !== 'active') {
      throw new Error('Workflow is not active');
    }
    
    const execution: WorkflowExecution = {
      id: uuidv4(),
      workflowId,
      status: 'running',
      variables: { ...workflow.variables, ...variables },
      results: {},
      startedAt: new Date()
    };
    
    this.executions.set(execution.id, execution);
    
    // Start workflow execution (simplified)
    this.processWorkflowExecution(execution.id);
    
    logger.info(`Workflow execution started: ${execution.id} for workflow ${workflowId}`);
    return execution;
  }

  async pauseWorkflow(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    
    if (!execution) {
      throw new Error('Workflow execution not found');
    }
    
    if (execution.status !== 'running') {
      throw new Error('Workflow is not running');
    }
    
    execution.status = 'paused';
    this.executions.set(executionId, execution);
    
    logger.info(`Workflow execution paused: ${executionId}`);
  }

  async resumeWorkflow(executionId: string): Promise<void> {
    const execution = this.executions.get(executionId);
    
    if (!execution) {
      throw new Error('Workflow execution not found');
    }
    
    if (execution.status !== 'paused') {
      throw new Error('Workflow is not paused');
    }
    
    execution.status = 'running';
    this.executions.set(executionId, execution);
    
    // Resume workflow execution
    this.processWorkflowExecution(executionId);
    
    logger.info(`Workflow execution resumed: ${executionId}`);
  }

  private async processWorkflowExecution(executionId: string): Promise<void> {
    // Simplified workflow execution logic
    // In a real implementation, this would handle step execution, dependencies, etc.
    
    setTimeout(async () => {
      const execution = this.executions.get(executionId);
      if (execution && execution.status === 'running') {
        execution.status = 'completed';
        execution.completedAt = new Date();
        this.executions.set(executionId, execution);
        
        logger.info(`Workflow execution completed: ${executionId}`);
      }
    }, 5000); // Simulate 5 second execution
  }

  private async generateMockWorkflows(): Promise<void> {
    const mockWorkflows = [
      {
        name: 'Client Onboarding Workflow',
        description: 'Automated workflow for onboarding new clients',
        type: 'sequential' as const,
        status: 'active' as const,
        steps: [
          {
            id: uuidv4(),
            name: 'Validate Client Data',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          },
          {
            id: uuidv4(),
            name: 'Create Project',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          },
          {
            id: uuidv4(),
            name: 'Send Welcome Email',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          }
        ],
        variables: {
          clientId: '',
          projectType: 'standard'
        },
        createdBy: 'system'
      },
      {
        name: 'RFP Analysis Workflow',
        description: 'Automated RFP analysis and response generation',
        type: 'parallel' as const,
        status: 'active' as const,
        steps: [
          {
            id: uuidv4(),
            name: 'Extract Requirements',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          },
          {
            id: uuidv4(),
            name: 'Analyze Feasibility',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          },
          {
            id: uuidv4(),
            name: 'Generate Response',
            type: 'agent_task' as const,
            dependencies: [],
            status: 'pending' as const
          }
        ],
        variables: {
          rfpId: '',
          deadline: ''
        },
        createdBy: 'system'
      }
    ];

    for (const workflowData of mockWorkflows) {
      await this.createWorkflow(workflowData);
    }

    logger.info(`Generated ${mockWorkflows.length} mock workflows`);
  }
}