# Luxcrafts CI/CD Pipeline Setup Script
# Establishes comprehensive CI/CD workflows for Vercel staging and Dokploy production

$ErrorActionPreference = "Stop"

Write-Host "🚀 Setting up Luxcrafts CI/CD Pipeline with High Momentum" -ForegroundColor Cyan
Write-Host "========================================================" -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Create enhanced GitHub Actions workflow for staging deployment
Write-Status "Creating enhanced GitHub Actions workflow for staging..."

$stagingWorkflow = @'
name: Luxcrafts Staging Deployment

on:
  push:
    branches: [ develop, feature/* ]
  pull_request:
    branches: [ develop ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Quality Assurance
  quality-check:
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.quality-gate.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npm run check

      - name: Run linting
        run: npm run lint
        continue-on-error: true

      - name: Run tests
        run: npm test -- --coverage
        continue-on-error: true
        env:
          CI: true

      - name: Quality gate decision
        id: quality-gate
        run: |
          if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "🚨 Force deployment enabled - bypassing quality checks"
          else
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "✅ Quality checks passed - proceeding with deployment"
          fi

  # Build and Deploy to Vercel Staging
  deploy-staging:
    needs: quality-check
    runs-on: ubuntu-latest
    if: needs.quality-check.outputs.should-deploy == 'true'
    environment:
      name: staging
      url: ${{ steps.vercel-deploy.outputs.preview-url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build for staging
        run: npm run build
        env:
          VITE_APP_ENVIRONMENT: staging
          VITE_WALLETCONNECT_PROJECT_ID: ${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY: ${{ secrets.VITE_ALCHEMY_API_KEY_STAGING }}
          VITE_INFURA_API_KEY: ${{ secrets.VITE_INFURA_API_KEY_STAGING }}
          VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL_STAGING }}
          VITE_CHAIN_ID: ${{ secrets.VITE_CHAIN_ID_STAGING }}
          VITE_CONTRACT_ADDRESS: ${{ secrets.VITE_CONTRACT_ADDRESS_STAGING }}
          VITE_LUX_TOKEN_ADDRESS: ${{ secrets.VITE_LUX_TOKEN_ADDRESS_STAGING }}
          NODE_OPTIONS: "--max-old-space-size=32768 --max-semi-space-size=1024"

      - name: Create deployment metadata
        run: |
          mkdir -p dist
          cat > dist/deployment-info.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "staging",
            "version": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "pr": "${{ github.event.number }}"
          }
          EOF

      - name: Create health check endpoint
        run: |
          cat > dist/health.json << EOF
          {
            "status": "healthy",
            "environment": "staging",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "version": "${{ github.sha }}",
            "services": {
              "frontend": "operational",
              "build": "successful"
            }
          }
          EOF

      - name: Install Vercel CLI
        run: npm install -g vercel@latest

      - name: Deploy to Vercel
        id: vercel-deploy
        run: |
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }} --yes)
          echo "preview-url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          echo "🚀 Staging deployed to: $DEPLOYMENT_URL"

      - name: Run health checks
        run: |
          sleep 30
          curl -f ${{ steps.vercel-deploy.outputs.preview-url }}/health.json || curl -f ${{ steps.vercel-deploy.outputs.preview-url }}/ || exit 1
          echo "✅ Staging deployment health check passed"

      - name: Comment PR with staging URL
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Staging Deployment Ready**\n\n📱 Preview: ${{ steps.vercel-deploy.outputs.preview-url }}\n\n✅ All checks passed!\n\n🔗 Health Check: ${{ steps.vercel-deploy.outputs.preview-url }}/health.json`
            })

      - name: Notify deployment success
        run: |
          echo "🎉 Staging deployment completed successfully!"
          echo "🌐 Preview URL: ${{ steps.vercel-deploy.outputs.preview-url }}"
          echo "📊 Deployment time: $(date)"
'@

if (-not (Test-Path ".github/workflows")) {
    New-Item -ItemType Directory -Path ".github/workflows" -Force
}

$stagingWorkflow | Out-File -FilePath ".github/workflows/staging-deployment.yml" -Encoding UTF8
Write-Success "Enhanced staging workflow created"

# Create production deployment workflow
Write-Status "Creating production deployment workflow for Dokploy..."

$productionWorkflow = @'
name: Luxcrafts Production Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      deployment_type:
        description: 'Deployment type'
        required: true
        default: 'standard'
        type: choice
        options:
        - standard
        - hotfix
        - rollback

env:
  NODE_VERSION: '20'
  PRODUCTION_DOMAIN: 'luxcrafts.co'

jobs:
  # Pre-deployment checks
  pre-deployment:
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.checks.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run comprehensive tests
        run: |
          npm run check
          npm run lint
          npm test -- --coverage
        env:
          CI: true

      - name: Security audit
        run: npm audit --audit-level=high

      - name: Pre-deployment checks
        id: checks
        run: |
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "✅ All pre-deployment checks passed"

  # Build and Deploy to Production
  deploy-production:
    needs: pre-deployment
    runs-on: ubuntu-latest
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    environment:
      name: production
      url: https://luxcrafts.co
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build for production
        run: npm run build
        env:
          VITE_APP_ENVIRONMENT: production
          VITE_WALLETCONNECT_PROJECT_ID: ${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY: ${{ secrets.VITE_ALCHEMY_API_KEY_PRODUCTION }}
          VITE_INFURA_API_KEY: ${{ secrets.VITE_INFURA_API_KEY_PRODUCTION }}
          VITE_API_BASE_URL: https://api.luxcrafts.co
          VITE_CHAIN_ID: ${{ secrets.VITE_CHAIN_ID_PRODUCTION }}
          VITE_CONTRACT_ADDRESS: ${{ secrets.VITE_CONTRACT_ADDRESS_PRODUCTION }}
          VITE_LUX_TOKEN_ADDRESS: ${{ secrets.VITE_LUX_TOKEN_ADDRESS_PRODUCTION }}
          NODE_OPTIONS: "--max-old-space-size=32768 --max-semi-space-size=1024"

      - name: Create production metadata
        run: |
          cat > dist/deployment-info.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "production",
            "version": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "domain": "luxcrafts.co"
          }
          EOF

      - name: Create production health check
        run: |
          cat > dist/health.json << EOF
          {
            "status": "healthy",
            "environment": "production",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "version": "${{ github.sha }}",
            "domain": "luxcrafts.co",
            "services": {
              "frontend": "operational",
              "build": "successful",
              "ssl": "active"
            }
          }
          EOF

      - name: Create deployment package
        run: |
          tar -czf luxcrafts-production-${{ github.sha }}.tar.gz dist/ dokploy.config.json package.json
          echo "📦 Production package created"

      - name: Deploy to Dokploy VPS
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            # Create deployment directory
            RELEASE_DIR="/opt/luxcrafts-production/releases/$(date +%Y%m%d_%H%M%S)"
            mkdir -p $RELEASE_DIR
            cd /opt/luxcrafts-production
            
            # Backup current deployment
            if [ -d "current" ]; then
              cp -r current backup_$(date +%Y%m%d_%H%M%S)
              echo "✅ Backup created"
            fi
            
            # Download and extract new release
            wget -O luxcrafts-production-${{ github.sha }}.tar.gz "${{ secrets.DEPLOYMENT_ARTIFACT_URL }}"
            tar -xzf luxcrafts-production-${{ github.sha }}.tar.gz -C $RELEASE_DIR/
            
            # Update symlink to new release
            ln -sfn $RELEASE_DIR current
            
            # Deploy with Dokploy
            dokploy deploy --config current/dokploy.config.json --domain luxcrafts.co
            
            echo "🚀 Production deployment initiated"

      - name: Wait for deployment and health check
        run: |
          echo "⏳ Waiting for deployment to complete..."
          sleep 120
          
          # Health check with retries
          for i in {1..10}; do
            if curl -f https://luxcrafts.co/health.json; then
              echo "✅ Production deployment health check passed"
              break
            else
              echo "⚠️ Health check attempt $i failed, retrying..."
              sleep 30
            fi
            
            if [ $i -eq 10 ]; then
              echo "❌ Production deployment health check failed after 10 attempts"
              exit 1
            fi
          done

      - name: Verify SSL and domain configuration
        run: |
          echo "🔒 Verifying SSL certificate..."
          curl -I https://luxcrafts.co | grep -i "HTTP/2 200" || exit 1
          
          echo "🌐 Verifying www redirect..."
          curl -I https://www.luxcrafts.co | grep -i "location: https://luxcrafts.co" || echo "⚠️ WWW redirect not configured"
          
          echo "✅ Domain configuration verified"

      - name: Cleanup old releases
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            cd /opt/luxcrafts-production/releases
            # Keep only the 5 most recent releases
            ls -t | tail -n +6 | xargs -r rm -rf
            echo "🧹 Old releases cleaned up"

      - name: Notify deployment success
        run: |
          echo "🎉 Production deployment to luxcrafts.co completed successfully!"
          echo "🌐 Live at: https://luxcrafts.co"
          echo "📊 Deployment time: $(date)"
          echo "🔗 Health Check: https://luxcrafts.co/health.json"
'@

$productionWorkflow | Out-File -FilePath ".github/workflows/production-deployment.yml" -Encoding UTF8
Write-Success "Production deployment workflow created"

# Create Dokploy configuration
Write-Status "Creating Dokploy configuration for production..."

$dokployConfig = @{
    version = "1.0"
    name = "luxcrafts-production"
    domain = @{
        primary = "luxcrafts.co"
        aliases = @("www.luxcrafts.co")
        ssl = @{
            enabled = $true
            provider = "letsencrypt"
            email = "<EMAIL>"
        }
    }
    deployment = @{
        type = "static"
        source = "dist"
        buildCommand = "npm run build"
        outputDirectory = "dist"
    }
    environment = @{
        NODE_ENV = "production"
        VITE_APP_ENVIRONMENT = "production"
    }
    resources = @{
        cpu = "1000m"
        memory = "2Gi"
        storage = "10Gi"
    }
    scaling = @{
        minReplicas = 2
        maxReplicas = 10
        targetCPU = 70
    }
    healthCheck = @{
        path = "/health.json"
        interval = "30s"
        timeout = "10s"
        retries = 3
    }
    security = @{
        headers = @{
            "X-Content-Type-Options" = "nosniff"
            "X-Frame-Options" = "DENY"
            "X-XSS-Protection" = "1; mode=block"
            "Strict-Transport-Security" = "max-age=31536000; includeSubDomains"
            "Content-Security-Policy" = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-src 'none';"
        }
    }
    backup = @{
        enabled = $true
        schedule = "0 2 * * *"
        retention = "7d"
    }
} | ConvertTo-Json -Depth 5

$dokployConfig | Out-File -FilePath "dokploy.config.json" -Encoding UTF8
Write-Success "Dokploy configuration created"

# Create environment configuration files
Write-Status "Creating environment configuration files..."

# Staging environment
$stagingEnv = @'
# Luxcrafts Staging Environment Configuration
VITE_APP_ENVIRONMENT=staging
VITE_API_BASE_URL=https://api-staging.luxcrafts.co
VITE_CHAIN_ID=5
VITE_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
VITE_ALCHEMY_API_KEY=your_alchemy_staging_key
VITE_INFURA_API_KEY=your_infura_staging_key
VITE_CONTRACT_ADDRESS=0x...
VITE_LUX_TOKEN_ADDRESS=0x...
'@

$stagingEnv | Out-File -FilePath ".env.staging" -Encoding UTF8

# Production environment
$productionEnv = @'
# Luxcrafts Production Environment Configuration
VITE_APP_ENVIRONMENT=production
VITE_API_BASE_URL=https://api.luxcrafts.co
VITE_CHAIN_ID=1
VITE_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
VITE_ALCHEMY_API_KEY=your_alchemy_production_key
VITE_INFURA_API_KEY=your_infura_production_key
VITE_CONTRACT_ADDRESS=0x...
VITE_LUX_TOKEN_ADDRESS=0x...
'@

$productionEnv | Out-File -FilePath ".env.production" -Encoding UTF8
Write-Success "Environment configuration files created"

# Create deployment documentation
Write-Status "Creating deployment documentation..."

$deploymentGuide = @'
# Luxcrafts CI/CD Deployment Guide

## Overview

This guide covers the complete CI/CD pipeline for Luxcrafts, including staging deployment to Vercel and production deployment to luxcrafts.co using Dokploy.

## Architecture

```
Developer Push → GitHub Actions → Build & Test → Deploy
     ↓                ↓              ↓           ↓
Feature Branch → Quality Checks → Artifacts → Staging (Vercel)
     ↓                ↓              ↓           ↓
Develop Branch → Security Scan → Container → Preview URL
     ↓                ↓              ↓           ↓
 Main Branch   → Performance → Registry → Production (luxcrafts.co)
```

## Deployment Environments

### Staging (Vercel)
- **Trigger**: Push to `develop` or `feature/*` branches
- **URL**: Auto-generated Vercel preview URL
- **Purpose**: Integration testing and preview
- **Environment**: Goerli testnet

### Production (Dokploy)
- **Trigger**: Push to `main` branch
- **URL**: https://luxcrafts.co
- **Purpose**: Live production environment
- **Environment**: Ethereum mainnet

## Required GitHub Secrets

### Vercel Secrets
- `VERCEL_TOKEN`: Vercel deployment token
- `VERCEL_ORG_ID`: Vercel organization ID
- `VERCEL_PROJECT_ID`: Vercel project ID

### Production Secrets
- `PRODUCTION_HOST`: VPS server IP address
- `PRODUCTION_USERNAME`: SSH username
- `PRODUCTION_SSH_KEY`: SSH private key
- `PRODUCTION_PORT`: SSH port (default: 22)

### Environment Variables
- `VITE_WALLETCONNECT_PROJECT_ID`: WalletConnect project ID
- `VITE_ALCHEMY_API_KEY_STAGING`: Alchemy API key for staging
- `VITE_ALCHEMY_API_KEY_PRODUCTION`: Alchemy API key for production
- `VITE_INFURA_API_KEY_STAGING`: Infura API key for staging
- `VITE_INFURA_API_KEY_PRODUCTION`: Infura API key for production
- `VITE_CONTRACT_ADDRESS_STAGING`: Smart contract address for staging
- `VITE_CONTRACT_ADDRESS_PRODUCTION`: Smart contract address for production
- `VITE_LUX_TOKEN_ADDRESS_STAGING`: LUX token address for staging
- `VITE_LUX_TOKEN_ADDRESS_PRODUCTION`: LUX token address for production

## Deployment Process

### Staging Deployment
1. Push code to `develop` or feature branch
2. GitHub Actions triggers staging workflow
3. Quality checks (TypeScript, ESLint, tests)
4. Build application with staging environment
5. Deploy to Vercel
6. Run health checks
7. Comment PR with preview URL

### Production Deployment
1. Merge to `main` branch
2. GitHub Actions triggers production workflow
3. Comprehensive pre-deployment checks
4. Build application with production environment
5. Create deployment package
6. Deploy to VPS using Dokploy
7. Health checks and SSL verification
8. Cleanup old releases

## Manual Deployment

### Staging
```bash
# Run staging deployment script
powershell -ExecutionPolicy Bypass -File scripts/deploy-staging.ps1
```

### Production
```bash
# Trigger production deployment
gh workflow run production-deployment.yml
```

## Health Checks

### Staging
- URL: `{preview-url}/health.json`
- Checks: Frontend status, build verification

### Production
- URL: `https://luxcrafts.co/health.json`
- Checks: Frontend, SSL, domain configuration

## Rollback Procedures

### Staging
- Automatic rollback on health check failure
- Manual rollback via Vercel dashboard

### Production
- Automatic rollback on deployment failure
- Manual rollback via SSH to VPS
- Previous releases kept for 5 deployments

## Monitoring

### Deployment Monitoring
- GitHub Actions workflow status
- Vercel deployment logs
- VPS server logs

### Application Monitoring
- Health check endpoints
- SSL certificate monitoring
- Performance metrics

## Troubleshooting

### Common Issues
1. **Build failures**: Check Node.js version and dependencies
2. **Deployment timeouts**: Increase timeout values
3. **Health check failures**: Verify application startup
4. **SSL issues**: Check certificate renewal

### Support
- GitHub Issues for bug reports
- Team Slack for urgent issues
- Documentation updates via PR

## Security

### Best Practices
- All secrets stored in GitHub Secrets
- SSH key authentication for VPS
- SSL/TLS encryption for all traffic
- Security headers configured
- Regular dependency updates

### Compliance
- Audit logs for all deployments
- Access control via GitHub teams
- Backup and recovery procedures
- Incident response plan
'@

$deploymentGuide | Out-File -FilePath "DEPLOYMENT_GUIDE.md" -Encoding UTF8
Write-Success "Deployment documentation created"

# Create quick deployment commands
Write-Status "Creating quick deployment commands..."

$quickCommands = @'
# Luxcrafts Quick Deployment Commands

## Development
```bash
# Start development server
npm run dev

# Run quality checks
npm run check
npm run lint

# Build for production
npm run build
```

## Staging Deployment
```bash
# Automated staging deployment
powershell -ExecutionPolicy Bypass -File scripts/deploy-staging.ps1

# Manual Vercel deployment
npx vercel --prebuilt
```

## Production Deployment
```bash
# Trigger production deployment via GitHub Actions
gh workflow run production-deployment.yml

# Manual production deployment
powershell -ExecutionPolicy Bypass -File scripts/deploy-production.ps1
```

## Health Checks
```bash
# Check staging health
curl https://your-staging-url.vercel.app/health.json

# Check production health
curl https://luxcrafts.co/health.json
```

## Monitoring
```bash
# View GitHub Actions status
gh run list

# View specific workflow run
gh run view [run-id]

# View deployment logs
gh run view [run-id] --log
```
'@

$quickCommands | Out-File -FilePath "QUICK_COMMANDS.md" -Encoding UTF8
Write-Success "Quick deployment commands created"

# Display summary
Write-Host ""
Write-Host "🎯 CI/CD PIPELINE SETUP COMPLETE" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ Enhanced GitHub Actions workflows created" -ForegroundColor Green
Write-Host "✅ Dokploy production configuration ready" -ForegroundColor Green
Write-Host "✅ Environment configurations prepared" -ForegroundColor Green
Write-Host "✅ Deployment documentation generated" -ForegroundColor Green
Write-Host "✅ Quick command reference created" -ForegroundColor Green
Write-Host ""
Write-Host "🔗 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Configure GitHub Secrets (see DEPLOYMENT_GUIDE.md)"
Write-Host "2. Set up Vercel project integration"
Write-Host "3. Configure Dokploy on VPS server"
Write-Host "4. Test staging deployment pipeline"
Write-Host "5. Validate production deployment flow"
Write-Host ""
Write-Host "📚 DOCUMENTATION:" -ForegroundColor Blue
Write-Host "- DEPLOYMENT_GUIDE.md: Complete deployment guide"
Write-Host "- QUICK_COMMANDS.md: Quick reference commands"
Write-Host "- .env.staging: Staging environment template"
Write-Host "- .env.production: Production environment template"
Write-Host ""
Write-Success "🚀 High-momentum CI/CD pipeline established! Ready for agency development and production deployment to luxcrafts.co! 🎉"