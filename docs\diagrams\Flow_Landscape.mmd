graph TD;
    classDef planning fill:#add8e6,stroke:#333,stroke-width:2px;
    classDef draft fill:#ffff99,stroke:#333,stroke-width:2px;
    classDef active fill:#90ee90,stroke:#333,stroke-width:2px;
    classDef deprecated fill:#cccccc,stroke:#333,stroke-width:2px;

    %% Workflow subgraphs will be dynamically generated here

    %% Example of how a workflow subgraph might look (to be generated by the script):
    /*
    subgraph WF_MKT_SAL_W001 ["MKT_SAL_W001: Define Go-To-Market Strategy Workflow"]
        direction TB;
        MKT_SAL_E001["MKT_SAL_E001: Define Go-To-Market Strategy & Branding"]:::planning;
        click MKT_SAL_E001 "../../definitions/MKT_SAL_E001_DefineGoToMarketStrategy.md" "View MKT_SAL_E001 Definition";
        
        %% Other processes belonging to this workflow would be listed here
        %% Potentially, sequence arrows between processes if defined: MKT_SAL_E001 --> ANOTHER_PROCESS_IN_WORKFLOW;
    end
    */
