---
description: Guides the creation of comprehensive ESTRATIX proposals for services, flows, processes, tasks, or agent capabilities.
---

# Workflow: Define ESTRATIX Proposal

**Objective:** To systematically develop compelling and comprehensive proposals that articulate the value and implementation of ESTRATIX solutions, tailored to specific client or internal needs. These proposals can focus on delivering ESTRATIX services, implementing specific flows, defining processes/tasks, or leveraging particular agent capabilities.

**Output:** A well-structured proposal document (e.g., Markdown, PDF) typically stored in `docs/proposals/[Proposal_ID]_[ProposalName].md`.

## Key Proposal Sections (Template)

* **1. Cover Page:** Title, Client/Project Name, Proposal ID, Date, ESTRATIX Logo.
* **2. Executive Summary:** Briefly, what is being proposed and its key benefits.
* **3. Introduction & Context:** Background, problem statement, or opportunity.
* **4. Understanding of Requirements:** Demonstrate understanding of the client's/stakeholder's needs.
* **5. Proposed ESTRATIX Solution:**
  * Overall approach.
  * **Services Involved:** (e.g., reference `SRV_XXX`)
  * **Flows to be Implemented/Utilized:** (e.g., reference `FLW_XXX`)
  * **Processes to be Executed/Defined:** (e.g., reference `P_XXX` or `CKO_PXXX`)
  * **Tasks to be Performed:** (e.g., reference `T_XXX`)
  * **Agent Crew & Key Agents:** (e.g., reference `AGENT_XXX`, Squads, Platoons involved)
  * Technology stack (if relevant and specific beyond standard ESTRATIX stack).
* **6. Scope of Work:** Clearly define what is included and excluded.
* **7. Deliverables:** List all tangible outputs.
* **8. Project Plan & Timeline:** Key phases, milestones, and estimated durations.
* **9. ESTRATIX Team / Agent Crew:** Introduce the key Command Office oversight and specific agent crews/roles involved.
* **10. Pricing / Resource Allocation:** Detailed breakdown of costs or required resources.
* **11. Assumptions & Dependencies:** Critical assumptions made and dependencies for success.
* **12. Call to Action & Next Steps:** What is required to proceed.
* **Appendix (Optional):** Detailed agent profiles, process maps, data models, etc.

## Steps

1. **Initiate Proposal & Define Scope:**
  * Receive request or identify need for a proposal.
  * Clarify the primary focus: Is it for a new ESTRATIX Service, a custom Flow implementation, a set of Processes/Tasks, or showcasing specific Agent capabilities?
  * Assign a Proposal ID (e.g., `PROP_YYYYMMDD_ShortName`).
  * Create a new proposal document using a standard template (e.g., `docs/templates/estratix_proposal_template.md` - *this template should be created if it doesn't exist*).

2. **Gather Requirements & Context:**
  * Engage with client/stakeholders (potentially using `CKO_AXXX_ClientRequirementElicitationAgent`).
  * Analyze existing documentation, opportunity signals (`CKO_M006`), or problem statements.
  * Document all requirements clearly.

3. **Identify Relevant ESTRATIX Assets:**
  * Based on requirements, search and identify existing ESTRATIX Services, Flows, Processes, Tasks, and Agent definitions that can be leveraged.
  * Consult relevant matrices (Service Matrix, Flow Matrix, Process Matrices, Task Matrix) and documentation (`docs/services/`, `docs/flows/`, `docs/processes/`, `docs/tasks/`, `docs/agents/`).
  * Determine if new ESTRATIX assets need to be defined/developed as part of the proposal.

4. **Draft Core Proposal Content (Sections 2-4, 12):**
  * Write the Executive Summary, Introduction, and Understanding of Requirements.
  * Outline the Call to Action.

5. **Detail the Proposed ESTRATIX Solution (Section 5):**
  * Describe the overall solution architecture.
  * Specify which ESTRATIX Services, Flows, Processes, Tasks will be used or created.
  * Define the Agent Crew structure for the project, highlighting key agent roles and their responsibilities. Reference the Agent Organizational Diagram.
  * This section should clearly link the proposed solution back to the requirements.

6. **Define Scope, Deliverables, and Timeline (Sections 6-8):**
  * Clearly articulate what is in and out of scope.
  * List all specific deliverables (documents, software, agent configurations, reports, etc.).
  * Develop a realistic project plan with phases, milestones, and timelines. This might involve `COO_AXXX_ProjectPlanningAgent`.

7. **Specify Team and Resource Allocation (Sections 9-10):**
  * Detail the ESTRATIX team structure for the engagement, including oversight from relevant Chief Officers and the specific agent crews.
  * Provide pricing information or a detailed breakdown of resource requirements (agent-hours, infrastructure, etc.).

8. **State Assumptions and Dependencies (Section 11):**
  * List any critical assumptions that underpin the proposal.
  * Identify any dependencies on the client or other third parties.

9. **Internal Review & Refinement:**
  * Conduct an internal review of the draft proposal with relevant ESTRATIX Command Officers (e.g., CPO for process scope, CTO for tech feasibility, COO for operational plan, CKO for knowledge inputs/outputs).
  * Incorporate feedback and refine the proposal for clarity, accuracy, and completeness.
  * Ensure alignment with ESTRATIX standards and best practices.

10. **Finalize and Package:**
  * Proofread the final proposal document.
  * Convert to the desired output format (e.g., PDF from Markdown).
  * Store the final version in `docs/proposals/` and update any relevant tracking systems.

11. **Present to Client/Stakeholder:**
  * Deliver the proposal and be prepared to discuss and answer questions.

## Supporting Tools & Techniques

* **ESTRATIX Documentation:** Leverage existing definitions of services, flows, processes, tasks, agents.
* **ESTRATIX Matrices:** Use as quick references for available components.
* **Proposal Templates:** Standardize proposal structure and content.
* **Agent Assistance:** Utilize specialized ESTRATIX agents for tasks like requirement gathering, solution design, planning, and document generation.

This workflow aims to ensure that all ESTRATIX proposals are consistent, high-quality, and effectively communicate the value of the proposed solutions.
