# ESTRATIX Flow Definition: CIO_F002_KnowledgeMonitoring

---

## 1. Flow Identity

- **ID:** `CIO_F002`
- **Name:** Knowledge Monitoring Flow
- **Domain:** Information Management
- **Command Office:** `CIO`
- **Version:** 1.0

## 2. Flow Overview

This flow orchestrates the continuous monitoring of registered knowledge sources to detect changes, updates, or staleness. It sequences the execution of the `KnowledgeMonitoringProcess` for each source listed in the `source_matrix.md`.

## 3. Flow Logic & Orchestration

The flow is triggered on a schedule (e.g., daily) or on-demand.

1.  **Initialization:**
    - The flow reads all entries from `source_matrix.md`.

2.  **Iterative Monitoring (Delegation):**
    - For each source in the matrix, the flow invokes the `KnowledgeMonitoringProcess`.
    - **Input to Process:** `source_uri` and `source_id`.
    - The process internally uses the `KnowledgeMonitoringCrew` to perform the following sub-tasks:
        - `monitor_source_task`: Checks the source for changes (e.g., by comparing content hashes).
        - `flag_source_for_review_task`: If a change is detected, this task updates the source's status in `source_matrix.md` to 'Needs Review' and logs the detected change.

3.  **Finalization & Logging:**
    - The flow logs a summary of its run, including the number of sources checked and the number of sources flagged for review, in a `monitoring_log_matrix.md`.

## 4. Associated Components

- **Process Orchestrated:** `KnowledgeMonitoringProcess`
- **Crew Involved:** `KnowledgeMonitoringCrew`
- **Agents Involved:** `CIO_A003_KnowledgeMonitorAgent`
- **Triggers:** Scheduled or manual execution.

## 5. Governance

- **Owner:** Chief Information Officer (CIO)
- **Error Handling:** The flow includes error handling for reading the matrix and for each process invocation. Failures are logged for operator review.

## 6. Guidance for Use

This flow is designed to be an automated, recurring background process that ensures the continuous integrity and currency of the ESTRATIX knowledge base.
