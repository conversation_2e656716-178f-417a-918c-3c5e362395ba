# CKO_M001: KnowledgeSourceRegistry

## 1. Metadata

*   **Data Model ID:** CKO_M001
*   **Data Model Name:** KnowledgeSourceRegistry
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A001_KnowledgeScoutAgent

## 2. Purpose

*   This data model represents an entry in the ESTRATIX registry of external knowledge sources. It stores metadata about each source, its access details, relevance, and performance metrics to guide the `CKO_F001_ExternalKnowledgeIngestionAndCuration` flow.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl, validator
from datetime import datetime, timedelta
from enum import Enum
import uuid

class SourceTypeEnum(str, Enum):
    RSS_FEED = "RSS Feed"
    API = "API"
    WEBSITE = "Website"
    DATABASE = "Database"
    JOURNAL = "Journal"
    NEWS_OUTLET = "News Outlet"
    OTHER = "Other"

class SourceStatusEnum(str, Enum):
    ACTIVE = "Active"
    INACTIVE = "Inactive"
    UNDER_REVIEW = "Under Review"
    DEPRECATED = "Deprecated"

class KnowledgeSourceRegistry(BaseModel):
    source_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the knowledge source.")
    source_name: str = Field(..., description="Descriptive name of the knowledge source (e.g., 'TechCrunch News Feed', 'OpenWeatherMap API').")
    source_url: Union[HttpUrl, str] = Field(..., description="Primary URL or endpoint for accessing the source. Can be a general URL for websites or specific endpoint for APIs/feeds.")
    source_type: SourceTypeEnum = Field(..., description="Type of the knowledge source.")
    description: Optional[str] = Field(None, description="Brief description of the source and the type of information it provides.")
    
    # Access and Configuration
    api_key_name: Optional[str] = Field(None, description="Name of the API key in a secure vault, if required.") # Actual key not stored here
    authentication_method: Optional[str] = Field(None, description="Method of authentication (e.g., 'API Key', 'OAuth2', 'None').")
    request_parameters: Optional[Dict[str, Any]] = Field(None, description="Default parameters for API calls or scraping configurations.")
    update_frequency_requested: timedelta = Field(default=timedelta(days=1), description="Requested frequency for checking updates from this source.")
    rate_limit_info: Optional[str] = Field(None, description="Information about rate limits (e.g., '100 requests/hour').")

    # Relevance and Quality
    keywords: List[str] = Field(default_factory=list, description="Keywords defining the relevance of this source to ESTRATIX interests.")
    primary_domain_tags: List[str] = Field(default_factory=list, description="Primary ESTRATIX domains or topics this source covers (e.g., 'AI', 'Market Trends', 'Cybersecurity').")
    relevance_score: float = Field(default=0.0, description="System or manually assigned score (0.0-1.0) indicating relevance to ESTRATIX.")
    quality_score: float = Field(default=0.0, description="System or manually assigned score (0.0-1.0) indicating data quality and reliability.")
    
    # Operational Metadata
    status: SourceStatusEnum = Field(default=SourceStatusEnum.UNDER_REVIEW, description="Current operational status of the source in the registry.")
    date_added: datetime = Field(default_factory=datetime.utcnow, description="Date the source was added to the registry.")
    last_checked: Optional[datetime] = Field(None, description="Timestamp of the last successful connection or data fetch attempt.")
    last_successful_fetch: Optional[datetime] = Field(None, description="Timestamp of the last successful data fetch that yielded new data.")
    error_count: int = Field(default=0, description="Number of consecutive errors encountered when trying to access this source.")
    notes: Optional[str] = Field(None, description="Any additional notes or comments about the source.")
    added_by: Optional[str] = Field(None, description="Agent/User ID who added this source.") # e.g., CKO_A001
    vetted_by: Optional[str] = Field(None, description="Agent/User ID who vetted this source.")

    @validator('source_url', pre=True)
    def _validate_url_str(cls, v):
        if isinstance(v, HttpUrl):
            return str(v)
        return v

```

## 4. Field Descriptions

| Field Name                 | Type                             | Description                                                                          | Required | Example Value(s)                                      |
|----------------------------|----------------------------------|--------------------------------------------------------------------------------------|----------|-------------------------------------------------------|
| `source_id`                | `uuid.UUID`                      | Unique identifier for the knowledge source.                                          | Yes      | `"123e4567-e89b-12d3-a456-************"`              |
| `source_name`              | `str`                            | Descriptive name of the knowledge source.                                            | Yes      | `"TechCrunch News Feed"`                                |
| `source_url`               | `Union[HttpUrl, str]`            | Primary URL or endpoint for accessing the source.                                    | Yes      | `"https://techcrunch.com/feed/"`, `"api.example.com/data"` |
| `source_type`              | `SourceTypeEnum`                 | Type of the knowledge source.                                                        | Yes      | `"RSS Feed"`, `"API"`                                 |
| `description`              | `Optional[str]`                  | Brief description of the source.                                                     | No       | `"Latest news on technology startups."`                 |
| `api_key_name`             | `Optional[str]`                  | Name of the API key in a secure vault (actual key not stored).                       | No       | `"TECHCRUNCH_API_KEY"`                                |
| `authentication_method`    | `Optional[str]`                  | Method of authentication.                                                            | No       | `"API Key"`, `"None"`                                 |
| `request_parameters`       | `Optional[Dict[str, Any]]`       | Default parameters for API calls or scraping.                                        | No       | `{"category": "ai", "limit": 10}`                   |
| `update_frequency_requested` | `timedelta`                      | Requested frequency for checking updates.                                            | Yes      | `timedelta(hours=1)`                                  |
| `rate_limit_info`          | `Optional[str]`                  | Information about rate limits.                                                       | No       | `"100 requests/hour"`                                 |
| `keywords`                 | `List[str]`                      | Keywords defining the relevance of this source.                                      | Yes      | `["artificial intelligence", "machine learning"]`     |
| `primary_domain_tags`      | `List[str]`                      | Primary ESTRATIX domains this source covers.                                         | Yes      | `["AI", "Market Trends"]`                             |
| `relevance_score`          | `float`                          | System or manually assigned relevance score (0.0-1.0).                               | Yes      | `0.85`                                                |
| `quality_score`            | `float`                          | System or manually assigned data quality score (0.0-1.0).                            | Yes      | `0.90`                                                |
| `status`                   | `SourceStatusEnum`               | Current operational status of the source.                                            | Yes      | `"Active"`                                            |
| `date_added`               | `datetime`                       | Date the source was added to the registry.                                           | Yes      | `"2025-05-26T17:20:00Z"`                              |
| `last_checked`             | `Optional[datetime]`             | Timestamp of the last successful connection or data fetch attempt.                   | No       | `"2025-05-26T18:00:00Z"`                              |
| `last_successful_fetch`    | `Optional[datetime]`             | Timestamp of the last successful data fetch that yielded new data.                   | No       | `"2025-05-26T18:00:00Z"`                              |
| `error_count`              | `int`                            | Number of consecutive errors encountered.                                            | Yes      | `0`, `3`                                              |
| `notes`                    | `Optional[str]`                  | Any additional notes or comments.                                                    | No       | `"Requires specific user-agent header."`                |
| `added_by`                 | `Optional[str]`                  | Agent/User ID who added this source.                                                 | No       | `"CKO_A001"`                                          |
| `vetted_by`                | `Optional[str]`                  | Agent/User ID who vetted this source.                                                | No       | `"CKO_A001"`                                          |

## 5. Relationships to Other Data Models

*   Instances of this model are used by `CKO_F001_ExternalKnowledgeIngestionAndCuration` to fetch data.
*   May be linked to `CKO_M003_CuratedKnowledgeAsset` (implicitly, as the source of an asset).

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_P002_IdentifyAndVetKnowledgeSources` (within `CKO_F001`). Manually created or updated by `CKO_A001_KnowledgeScoutAgent`.
*   **Primary Consuming Flow(s)/Process(es):** `CKO_F001_ExternalKnowledgeIngestionAndCuration` (specifically `CKO_T002_ExecuteKnowledgeAcquisition`).
*   **Key Agents Interacting:** `CKO_A001_KnowledgeScoutAgent` (creates/updates), `CKO_A002_DataIngestionAgent` (reads).

## 7. Notes / Future Considerations

*   Consider adding fields for licensing information or terms of use for each source.
*   A more sophisticated scoring system for relevance and quality might be developed.
*   Could integrate with a secrets management system more directly for API keys rather than just storing the key name.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                               |
| :------ | :--------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------ |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M001 to CKO_M001. Updated ID, version, owner, SME, and all internal KNO references to CKO. Updated Last Updated date. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the KnowledgeSourceRegistry data model.                                                                         |
