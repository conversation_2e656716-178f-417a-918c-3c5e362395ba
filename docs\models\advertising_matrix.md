# ESTRATIX Advertising Platform Matrix

---

## 1. Overview

This matrix catalogs all approved and evaluated advertising platforms used for ESTRATIX client and internal projects. It serves as a central registry for managing ad campaigns, linking them to project objectives, content strategies, and automation scripts.

---

## 2. Platform Inventory

| Platform ID | Platform Name | ESTRATIX Status | Primary Audience/Use Case | Automation/API Link | Project Integration | Content Integration | Notes |
|---|---|---|---|---|---|---|---|
| `AD-PLT-001` | Meta Ads (Facebook/Instagram) | **Approved** | Broad consumer audiences (B2C), community building, visual-first campaigns. | `SCR-API-META-ADS` | Links to `project_matrix.md` for campaign goals. | Links to `content_matrix.md` for ad creatives. | Core platform for social media advertising. |
| `AD-PLT-002` | Google Ads | **Approved** | High-intent search users, display advertising, YouTube campaigns. | `SCR-API-GGL-ADS` | Links to `project_matrix.md` for keyword strategy and budget. | Links to `content_matrix.md` for ad copy and video assets. | Essential for search engine marketing (SEM). |
| `AD-PLT-003` | TikTok Ads | **Approved** | Younger demographics (Gen Z), short-form video content, trend-based marketing. | `SCR-API-TIK-ADS` | Links to `project_matrix.md` for viral campaign tracking. | Links to `content_matrix.md` for short-form video assets. | High potential for viral reach. |
| `AD-PLT-004` | Snapchat Ads | **Evaluating** | Younger demographics, ephemeral content, AR lens campaigns. | `SCR-API-SNP-ADS` | Links to `project_matrix.md` for experimental campaigns. | Links to `content_matrix.md` for vertical video and AR assets. | Niche platform for specific youth-focused brands. |

---

## 3. Guidance for Use

- **Platform Selection**: The choice of platform must align with the campaign objectives and target audience defined in the `project_matrix.md`.
- **Content Strategy**: All ad creatives must be tracked and managed via the `content_matrix.md`.
- **Automation**: Scripts for campaign management, reporting, or optimization must be registered in the `script_matrix.md` and linked here.
