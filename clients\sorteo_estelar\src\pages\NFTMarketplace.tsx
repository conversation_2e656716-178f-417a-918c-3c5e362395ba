import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  <PERSON>rkles, Filter, Search, Grid, List, TrendingUp, 
  Heart, Share2, Eye, ShoppingCart, Zap, Star,
  Clock, Users, Award, Gift, ExternalLink,
  ChevronDown, ChevronUp, Play, Pause
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface NFT {
  id: string;
  name: string;
  description: string;
  image: string;
  animation?: string;
  creator: {
    address: string;
    name?: string;
    avatar?: string;
    verified: boolean;
  };
  owner: {
    address: string;
    name?: string;
    avatar?: string;
  };
  collection: {
    name: string;
    verified: boolean;
  };
  price: {
    amount: number;
    currency: 'ETH' | 'MATIC' | 'BNB';
    usd: number;
  };
  rarity: {
    rank: number;
    score: number;
    traits: Array<{
      type: string;
      value: string;
      rarity: number;
    }>;
  };
  stats: {
    views: number;
    likes: number;
    offers: number;
  };
  auction?: {
    endTime: string;
    highestBid: number;
    bidders: number;
  };
  category: string;
  tags: string[];
  isListed: boolean;
  isAuction: boolean;
  createdAt: string;
  lastSale?: {
    price: number;
    date: string;
  };
}

interface Collection {
  id: string;
  name: string;
  description: string;
  image: string;
  banner: string;
  creator: string;
  verified: boolean;
  floorPrice: number;
  volume24h: number;
  items: number;
  owners: number;
  royalty: number;
}

const NFTMarketplace: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'explore' | 'collections' | 'activity' | 'create'>('explore');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'price_low' | 'price_high' | 'newest' | 'oldest' | 'rarity'>('newest');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [selectedNFT, setSelectedNFT] = useState<NFT | null>(null);
  const [showFilters, setShowFilters] = useState(false);

  // Mock NFT data
  const nfts: NFT[] = [
    {
      id: '1',
      name: 'Cosmic Lottery Ticket #1234',
      description: 'Un ticket de lotería único que representa la participación en el sorteo del MacBook Pro M3. Este NFT incluye metadatos especiales y beneficios exclusivos.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=cosmic%20lottery%20ticket%20NFT%20holographic%20space%20theme%20digital%20art&image_size=square',
      creator: {
        address: '0x1234...5678',
        name: 'Sorteo Estelar',
        verified: true
      },
      owner: {
        address: '0x8765...4321',
        name: 'CryptoCollector'
      },
      collection: {
        name: 'Lottery Tickets Collection',
        verified: true
      },
      price: {
        amount: 0.05,
        currency: 'ETH',
        usd: 125
      },
      rarity: {
        rank: 156,
        score: 8.7,
        traits: [
          { type: 'Background', value: 'Cosmic Purple', rarity: 15 },
          { type: 'Frame', value: 'Golden', rarity: 8 },
          { type: 'Number Pattern', value: 'Sequential', rarity: 25 }
        ]
      },
      stats: {
        views: 1250,
        likes: 89,
        offers: 3
      },
      category: 'lottery-tickets',
      tags: ['lottery', 'cosmic', 'rare'],
      isListed: true,
      isAuction: false,
      createdAt: '2024-01-15T10:00:00Z'
    },
    {
      id: '2',
      name: 'Winner Certificate Tesla Model Y',
      description: 'Certificado NFT que conmemora la victoria en el sorteo del Tesla Model Y. Incluye detalles del sorteo y prueba de autenticidad.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=winner%20certificate%20NFT%20Tesla%20Model%20Y%20golden%20trophy%20digital%20award&image_size=square',
      creator: {
        address: '0x1234...5678',
        name: 'Sorteo Estelar',
        verified: true
      },
      owner: {
        address: '0x9876...1234',
        name: 'LuckyWinner'
      },
      collection: {
        name: 'Winner Certificates',
        verified: true
      },
      price: {
        amount: 2.5,
        currency: 'ETH',
        usd: 6250
      },
      rarity: {
        rank: 1,
        score: 9.9,
        traits: [
          { type: 'Prize Category', value: 'Vehicles', rarity: 2 },
          { type: 'Prize Value', value: 'Ultra High', rarity: 1 },
          { type: 'Winner Status', value: 'Verified', rarity: 5 }
        ]
      },
      stats: {
        views: 5670,
        likes: 234,
        offers: 12
      },
      auction: {
        endTime: '2024-01-25T20:00:00Z',
        highestBid: 2.8,
        bidders: 8
      },
      category: 'certificates',
      tags: ['winner', 'tesla', 'legendary'],
      isListed: true,
      isAuction: true,
      createdAt: '2024-01-10T15:30:00Z',
      lastSale: {
        price: 1.8,
        date: '2024-01-12T12:00:00Z'
      }
    },
    {
      id: '3',
      name: 'Exclusive Access Pass',
      description: 'Pase de acceso exclusivo que otorga beneficios especiales en futuros sorteos, incluyendo descuentos y acceso anticipado.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=exclusive%20access%20pass%20NFT%20VIP%20golden%20card%20luxury%20design&image_size=square',
      creator: {
        address: '0x1234...5678',
        name: 'Sorteo Estelar',
        verified: true
      },
      owner: {
        address: '0x5555...6666',
        name: 'VIPMember'
      },
      collection: {
        name: 'Access Passes',
        verified: true
      },
      price: {
        amount: 0.25,
        currency: 'ETH',
        usd: 625
      },
      rarity: {
        rank: 45,
        score: 7.8,
        traits: [
          { type: 'Access Level', value: 'VIP', rarity: 10 },
          { type: 'Benefits', value: 'Premium', rarity: 20 },
          { type: 'Duration', value: 'Lifetime', rarity: 5 }
        ]
      },
      stats: {
        views: 890,
        likes: 67,
        offers: 5
      },
      category: 'access-passes',
      tags: ['vip', 'access', 'benefits'],
      isListed: true,
      isAuction: false,
      createdAt: '2024-01-08T09:15:00Z'
    }
  ];

  const collections: Collection[] = [
    {
      id: '1',
      name: 'Lottery Tickets Collection',
      description: 'Colección oficial de tickets de lotería NFT de Sorteo Estelar',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=lottery%20tickets%20collection%20NFT%20cosmic%20theme%20holographic&image_size=square',
      banner: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=cosmic%20lottery%20banner%20space%20stars%20nebula%20digital%20art&image_size=landscape_16_9',
      creator: 'Sorteo Estelar',
      verified: true,
      floorPrice: 0.03,
      volume24h: 15.7,
      items: 2500,
      owners: 1200,
      royalty: 5
    },
    {
      id: '2',
      name: 'Winner Certificates',
      description: 'Certificados únicos para ganadores de sorteos',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=winner%20certificate%20collection%20golden%20trophy%20awards&image_size=square',
      banner: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=winner%20certificates%20banner%20golden%20trophies%20celebration&image_size=landscape_16_9',
      creator: 'Sorteo Estelar',
      verified: true,
      floorPrice: 1.2,
      volume24h: 8.9,
      items: 156,
      owners: 156,
      royalty: 7.5
    },
    {
      id: '3',
      name: 'Access Passes',
      description: 'Pases de acceso exclusivo con beneficios especiales',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=access%20pass%20collection%20VIP%20luxury%20cards&image_size=square',
      banner: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=VIP%20access%20passes%20banner%20luxury%20exclusive%20golden&image_size=landscape_16_9',
      creator: 'Sorteo Estelar',
      verified: true,
      floorPrice: 0.15,
      volume24h: 3.2,
      items: 500,
      owners: 320,
      royalty: 10
    }
  ];

  const categories = [
    { id: 'all', name: 'Todas las categorías' },
    { id: 'lottery-tickets', name: 'Tickets de Lotería' },
    { id: 'certificates', name: 'Certificados' },
    { id: 'access-passes', name: 'Pases de Acceso' },
    { id: 'collectibles', name: 'Coleccionables' }
  ];

  const getRarityColor = (rank: number) => {
    if (rank <= 10) return 'from-amber-400 to-amber-600';
    if (rank <= 50) return 'from-purple-400 to-purple-600';
    if (rank <= 200) return 'from-blue-400 to-blue-600';
    return 'from-gray-400 to-gray-600';
  };

  const formatTimeLeft = (endTime: string) => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const diff = end - now;
    
    if (diff <= 0) return 'Finalizada';
    
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    
    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const NFTCard: React.FC<{ nft: NFT }> = ({ nft }) => (
    <Card 
      variant="glass" 
      className="group hover:scale-105 transition-all duration-300 cursor-pointer"
      onClick={() => setSelectedNFT(nft)}
    >
      <div className="space-y-4">
        <div className="relative">
          <img 
            src={nft.image} 
            alt={nft.name}
            className="w-full h-48 object-cover rounded-lg"
          />
          <div className="absolute top-2 left-2">
            <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(nft.rarity.rank)} text-white`}>
              #{nft.rarity.rank}
            </span>
          </div>
          <div className="absolute top-2 right-2 flex space-x-1">
            {nft.collection.verified && (
              <div className="p-1 bg-blue-500/80 rounded-full">
                <Award className="w-3 h-3 text-white" />
              </div>
            )}
            {nft.isAuction && (
              <div className="p-1 bg-red-500/80 rounded-full">
                <Clock className="w-3 h-3 text-white" />
              </div>
            )}
          </div>
          <div className="absolute bottom-2 left-2 right-2 flex justify-between items-center">
            <div className="flex items-center space-x-2 text-white/80 text-xs">
              <Eye className="w-3 h-3" />
              <span>{nft.stats.views}</span>
              <Heart className="w-3 h-3" />
              <span>{nft.stats.likes}</span>
            </div>
            {nft.auction && (
              <div className="text-red-400 text-xs font-medium">
                {formatTimeLeft(nft.auction.endTime)}
              </div>
            )}
          </div>
        </div>
        
        <div className="space-y-2">
          <h3 className="text-white font-semibold text-lg truncate">{nft.name}</h3>
          <p className="text-white/70 text-sm line-clamp-2">{nft.description}</p>
          
          <div className="flex items-center justify-between">
            <div className="text-xs text-white/60">
              <span>Por </span>
              <span className="text-purple-400">{nft.creator.name || nft.creator.address}</span>
            </div>
            {nft.creator.verified && (
              <Award className="w-4 h-4 text-blue-400" />
            )}
          </div>
          
          <div className="flex items-center justify-between pt-2 border-t border-white/20">
            <div>
              <div className="text-white/60 text-xs">Precio</div>
              <div className="text-white font-bold">
                {nft.price.amount} {nft.price.currency}
              </div>
              <div className="text-white/60 text-xs">${nft.price.usd}</div>
            </div>
            
            <div className="flex space-x-2">
              <Button variant="outline" size="sm">
                <ShoppingCart className="w-4 h-4" />
              </Button>
              <Button variant="primary" size="sm">
                {nft.isAuction ? 'Pujar' : 'Comprar'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  const CollectionCard: React.FC<{ collection: Collection }> = ({ collection }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="space-y-4">
        <div className="relative">
          <img 
            src={collection.banner} 
            alt={collection.name}
            className="w-full h-32 object-cover rounded-lg"
          />
          <div className="absolute -bottom-6 left-4">
            <img 
              src={collection.image} 
              alt={collection.name}
              className="w-12 h-12 rounded-lg border-2 border-white/20"
            />
          </div>
          {collection.verified && (
            <div className="absolute top-2 right-2 p-1 bg-blue-500/80 rounded-full">
              <Award className="w-4 h-4 text-white" />
            </div>
          )}
        </div>
        
        <div className="pt-2 space-y-3">
          <div>
            <h3 className="text-white font-semibold text-lg">{collection.name}</h3>
            <p className="text-white/70 text-sm">{collection.description}</p>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white/60">Precio mínimo:</span>
              <div className="text-white font-medium">{collection.floorPrice} ETH</div>
            </div>
            <div>
              <span className="text-white/60">Volumen 24h:</span>
              <div className="text-green-400 font-medium">{collection.volume24h} ETH</div>
            </div>
            <div>
              <span className="text-white/60">Items:</span>
              <div className="text-white font-medium">{collection.items.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-white/60">Propietarios:</span>
              <div className="text-white font-medium">{collection.owners.toLocaleString()}</div>
            </div>
          </div>
          
          <Button variant="primary" className="w-full">
            Ver Colección
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            NFT <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Marketplace</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Descubre, colecciona e intercambia NFTs únicos del ecosistema Sorteo Estelar
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <Card variant="glass">
            <div className="text-center">
              <Sparkles className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">3,156</div>
              <div className="text-white/70 text-sm">NFTs Totales</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Users className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">1,676</div>
              <div className="text-white/70 text-sm">Propietarios</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-400 mb-1">27.8 ETH</div>
              <div className="text-white/70 text-sm">Volumen 24h</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Star className="w-8 h-8 text-amber-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">0.03 ETH</div>
              <div className="text-white/70 text-sm">Precio Mínimo</div>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {[
            { id: 'explore', label: 'Explorar', icon: Search },
            { id: 'collections', label: 'Colecciones', icon: Grid },
            { id: 'activity', label: 'Actividad', icon: TrendingUp },
            { id: 'create', label: 'Crear', icon: Sparkles }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium hidden sm:block">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Filters and Search */}
        {activeTab === 'explore' && (
          <div className="space-y-4 mb-8">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
                <input
                  type="text"
                  placeholder="Buscar NFTs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div className="flex space-x-4">
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  className="px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  <option value="newest">Más recientes</option>
                  <option value="oldest">Más antiguos</option>
                  <option value="price_low">Precio: menor a mayor</option>
                  <option value="price_high">Precio: mayor a menor</option>
                  <option value="rarity">Rareza</option>
                </select>
                
                <div className="flex space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-3 rounded-xl transition-colors ${
                      viewMode === 'grid'
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/10 text-white/70 hover:text-white'
                    }`}
                  >
                    <Grid className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-3 rounded-xl transition-colors ${
                      viewMode === 'list'
                        ? 'bg-purple-500 text-white'
                        : 'bg-white/10 text-white/70 hover:text-white'
                    }`}
                  >
                    <List className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 px-4 py-2 bg-white/10 backdrop-blur-md rounded-lg text-white hover:bg-white/20 transition-colors"
            >
              <Filter className="w-4 h-4" />
              <span>Filtros avanzados</span>
              {showFilters ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </button>
            
            {showFilters && (
              <Card variant="glass">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <label className="block text-white font-medium mb-2">Rango de precios (ETH)</label>
                    <div className="space-y-2">
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value)])}
                        className="w-full"
                      />
                      <div className="flex justify-between text-white/70 text-sm">
                        <span>{priceRange[0]} ETH</span>
                        <span>{priceRange[1]} ETH</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-white font-medium mb-2">Estado</label>
                    <div className="space-y-2">
                      {['En venta', 'En subasta', 'Nuevos', 'Con ofertas'].map(status => (
                        <label key={status} className="flex items-center space-x-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-white/80">{status}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-white font-medium mb-2">Colecciones</label>
                    <div className="space-y-2">
                      {collections.map(collection => (
                        <label key={collection.id} className="flex items-center space-x-2">
                          <input type="checkbox" className="rounded" />
                          <span className="text-white/80">{collection.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>
            )}
          </div>
        )}

        {/* Content */}
        <div className="space-y-6">
          {activeTab === 'explore' && (
            <div className={`grid gap-6 ${
              viewMode === 'grid' 
                ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                : 'grid-cols-1'
            }`}>
              {nfts.map(nft => (
                <NFTCard key={nft.id} nft={nft} />
              ))}
            </div>
          )}

          {activeTab === 'collections' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {collections.map(collection => (
                <CollectionCard key={collection.id} collection={collection} />
              ))}
            </div>
          )}

          {activeTab === 'activity' && (
            <Card variant="glass">
              <div className="text-center py-12">
                <TrendingUp className="w-16 h-16 text-white/50 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Actividad Reciente</h3>
                <p className="text-white/70">Las transacciones y actividades aparecerán aquí</p>
              </div>
            </Card>
          )}

          {activeTab === 'create' && (
            <Card variant="glass">
              <div className="text-center py-12">
                <Sparkles className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Crear NFT</h3>
                <p className="text-white/70 mb-6">Próximamente: herramientas para crear tus propios NFTs</p>
                <Button variant="primary">
                  <Gift className="w-4 h-4 mr-2" />
                  Notificarme cuando esté disponible
                </Button>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default NFTMarketplace;