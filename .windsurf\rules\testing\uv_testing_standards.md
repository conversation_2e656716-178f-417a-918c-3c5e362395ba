---
trigger: always_on
---

# UV-Based Testing Standards for ESTRATIX

## 1. Testing Execution Protocol

### 1.1. Mandatory UV Run Usage

**CRITICAL REQUIREMENT**: All testing activities in ESTRATIX MUST use `uv run` for consistent environment management and dependency resolution.

```bash
# Standard test execution
uv run pytest

# Test with coverage
uv run pytest --cov=src --cov-report=html

# Specific test files
uv run pytest tests/test_document_processor.py

# Test markers
uv run pytest -m "unit"
uv run pytest -m "integration"
uv run pytest -m "agent"
```

### 1.2. Test Environment Management

- **Isolation**: Each test run uses isolated environment via uv
- **Dependencies**: Test dependencies managed in pyproject.toml
- **Reproducibility**: uv.lock ensures consistent test environments
- **Performance**: UV's fast dependency resolution optimizes test startup

## 2. Test Categories & Execution Patterns

### 2.1. Unit Tests

```bash
# Run all unit tests
uv run pytest -m "unit" --tb=short

# Unit tests with coverage
uv run pytest -m "unit" --cov=src/domain --cov-report=term-missing

# Fast unit test execution
uv run pytest -m "unit" --maxfail=1 -x

# Parallel unit tests
uv run pytest -m "unit" -n auto
```

### 2.2. Integration Tests

```bash
# Integration tests (slower)
uv run pytest -m "integration" --tb=long

# Integration with external services
uv run pytest -m "integration" --external-services

# Database integration tests
uv run pytest -m "integration and database"

# API integration tests
uv run pytest -m "integration and api"
```

### 2.3. Agent-Specific Tests

```bash
# Test all agents
uv run pytest -m "agent" --agent-timeout=30

# Test specific agent
uv run pytest tests/agents/test_cto_command_office.py

# Agent performance tests
uv run pytest -m "agent and performance" --benchmark-only

# Agent integration tests
uv run pytest -m "agent and integration"
```

### 2.4. Tool Tests

```bash
# Test all tools
uv run pytest -m "tool"

# Test document processing tools
uv run pytest tests/tools/test_document_processor.py

# Tool performance benchmarks
uv run pytest -m "tool and benchmark"

# Tool integration with agents
uv run pytest -m "tool and agent"
```

## 3. Test Configuration Standards

### 3.1. PyProject.toml Test Configuration

```toml
[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "pytest-benchmark>=4.0.0",
    "pytest-xdist>=3.3.0",
    "httpx>=0.25.0",
    "factory-boy>=3.3.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--strict-markers",
    "--strict-config",
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=term-missing",
    "--cov-report=html:htmlcov",
    "--cov-fail-under=80",
    "--maxfail=3",
]
markers = [
    "unit: Unit tests (fast, isolated)",
    "integration: Integration tests (slower, external deps)",
    "agent: Agent-specific tests",
    "tool: Tool-specific tests",
    "slow: Slow running tests",
    "benchmark: Performance benchmark tests",
    "database: Tests requiring database",
    "api: API endpoint tests",
    "external: Tests requiring external services",
]
filterwarnings = [
    "error",
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]
asyncio_mode = "auto"
```

### 3.2. Test Directory Structure

```
tests/
├── conftest.py                 # Shared fixtures
├── unit/                       # Unit tests
│   ├── agents/
│   ├── tools/
│   ├── domain/
│   └── infrastructure/
├── integration/                # Integration tests
│   ├── agents/
│   ├── workflows/
│   ├── database/
│   └── api/
├── performance/                # Performance tests
│   ├── benchmarks/
│   └── load_tests/
├── fixtures/                   # Test data
│   ├── documents/
│   ├── responses/
│   └── configs/
└── utils/                      # Test utilities
    ├── factories.py
    ├── helpers.py
    └── mocks.py
```

## 4. Advanced Testing Patterns

### 4.1. Parallel Test Execution

```bash
# Auto-detect CPU cores
uv run pytest -n auto

# Specific number of workers
uv run pytest -n 4

# Distribute by test file
uv run pytest --dist=loadfile

# Distribute by test function
uv run pytest --dist=loadscope
```

### 4.2. Test Coverage Analysis

```bash
# Generate HTML coverage report
uv run pytest --cov=src --cov-report=html

# Coverage with missing lines
uv run pytest --cov=src --cov-report=term-missing

# Coverage threshold enforcement
uv run pytest --cov=src --cov-fail-under=80

# Coverage for specific modules
uv run pytest --cov=src.agents --cov=src.tools
```

### 4.3. Performance Testing

```bash
# Benchmark tests
uv run pytest --benchmark-only

# Benchmark with comparison
uv run pytest --benchmark-compare

# Save benchmark results
uv run pytest --benchmark-save=baseline

# Load testing
uv run pytest -m "load" --workers=10
```

## 5. Test Data Management

### 5.1. Fixtures and Factories

```python
# conftest.py
import pytest
from factory import Factory
from src.domain.models import Document

@pytest.fixture
def sample_document():
    return Document(
        id="test-doc-001",
        content="Sample test content",
        metadata={"source": "test"}
    )

@pytest.fixture
def document_factory():
    class DocumentFactory(Factory):
        class Meta:
            model = Document
        
        id = "test-doc"
        content = "Test content"
        metadata = {"source": "factory"}
    
    return DocumentFactory
```

### 5.2. Mock Management

```python
# Test with mocks
import pytest
from unittest.mock import Mock, patch

@pytest.fixture
def mock_llm_service():
    mock = Mock()
    mock.generate.return_value = "Mocked response"
    return mock

@patch('src.services.llm_service.LLMService')
def test_agent_with_mock(mock_llm):
    # Test implementation
    pass
```

## 6. Continuous Integration Testing

### 6.1. CI/CD Pipeline Integration

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v1
      - name: Install dependencies
        run: uv sync --all-extras
      - name: Run tests
        run: uv run pytest --cov=src --cov-report=xml
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### 6.2. Quality Gates

```bash
# Pre-commit testing
uv run pytest --maxfail=1 -x

# Coverage enforcement
uv run pytest --cov=src --cov-fail-under=80

# Performance regression detection
uv run pytest --benchmark-compare=baseline

# Security testing
uv run bandit -r src/
```

## 7. Test Maintenance Standards

### 7.1. Test Naming Conventions

```python
# Good test names
def test_document_processor_extracts_text_from_pdf():
    pass

def test_agent_handles_invalid_input_gracefully():
    pass

def test_tool_returns_expected_format_for_valid_data():
    pass
```

### 7.2. Test Organization

- **One assertion per test**: Focus on single behavior
- **Arrange-Act-Assert**: Clear test structure
- **Descriptive names**: Self-documenting test purposes
- **Isolated tests**: No dependencies between tests
- **Fast execution**: Unit tests under 100ms

### 7.3. Test Documentation

```python
def test_complex_workflow():
    """
    Test that the document processing workflow correctly:
    1. Extracts text from uploaded PDF
    2. Generates embeddings for content
    3. Stores results in vector database
    4. Returns processing confirmation
    """
    # Test implementation
```

## 8. Debugging and Troubleshooting

### 8.1. Debug Mode Testing

```bash
# Verbose output
uv run pytest -v -s

# Debug specific test
uv run pytest tests/test_specific.py::test_function -v -s

# Drop into debugger on failure
uv run pytest --pdb

# Debug on first failure
uv run pytest --pdb-trace
```

### 8.2. Test Isolation Issues

```bash
# Run tests in random order
uv run pytest --random-order

# Detect test dependencies
uv run pytest --lf --tb=short

# Clean test environment
uv run pytest --cache-clear
```

## 9. Performance Optimization

### 9.1. Fast Test Execution

```bash
# Parallel execution
uv run pytest -n auto

# Fail fast
uv run pytest --maxfail=1 -x

# Skip slow tests
uv run pytest -m "not slow"

# Only failed tests
uv run pytest --lf
```

### 9.2. Resource Management

```python
# Efficient fixtures
@pytest.fixture(scope="session")
def expensive_resource():
    # Setup once per session
    resource = create_expensive_resource()
    yield resource
    # Cleanup
    resource.cleanup()
```

## 10. Compliance and Reporting

### 10.1. Test Reports

```bash
# JUnit XML for CI
uv run pytest --junitxml=reports/junit.xml

# HTML report
uv run pytest --html=reports/report.html

# Coverage reports
uv run pytest --cov=src --cov-report=html:reports/coverage
```

### 10.2. Quality Metrics

- **Test Coverage**: Minimum 80% line coverage
- **Test Speed**: Unit tests < 100ms, Integration < 5s
- **Test Reliability**: < 1% flaky test rate
- **Maintenance**: Regular test review and cleanup

---

**ENFORCEMENT**: These standards are mandatory for all ESTRATIX development. Non-compliance blocks PR approval and deployment.

**AUTOMATION**: CI/CD pipelines automatically enforce these standards through quality gates and automated testing workflows.