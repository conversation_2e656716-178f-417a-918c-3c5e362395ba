import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Trophy, Calendar, Clock, Users, Star, Filter, Search, 
  Download, Share2, Eye, ChevronDown, ChevronUp, 
  Award, Gift, Zap, TrendingUp, ExternalLink
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface DrawResult {
  id: string;
  productId: string;
  productName: string;
  productImage: string;
  drawDate: string;
  drawNumber: string;
  winnerAddress: string;
  winnerTickets: number;
  totalTickets: number;
  prizeValue: number;
  category: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  verified: boolean;
  transactionHash?: string;
  drawType: 'lottery-linked' | 'random';
}

interface UpcomingDraw {
  id: string;
  productName: string;
  productImage: string;
  drawDate: string;
  timeLeft: string;
  totalTickets: number;
  soldTickets: number;
  prizeValue: number;
  category: string;
}

interface Winner {
  id: string;
  address: string;
  productName: string;
  prizeValue: number;
  date: string;
  verified: boolean;
  avatar?: string;
}

const Results: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'recent' | 'upcoming' | 'winners' | 'statistics'>('recent');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [expandedResult, setExpandedResult] = useState<string | null>(null);
  const [timeFilter, setTimeFilter] = useState<'all' | 'week' | 'month' | 'year'>('all');

  // Mock data
  const recentResults: DrawResult[] = [
    {
      id: '1',
      productId: 'macbook-pro',
      productName: 'MacBook Pro M3',
      productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=MacBook%20Pro%20M3%20space%20gray%20professional%20workspace&image_size=square',
      drawDate: '2024-01-15T20:00:00Z',
      drawNumber: '5678',
      winnerAddress: '0x1234...5678',
      winnerTickets: 3,
      totalTickets: 2000,
      prizeValue: 75000,
      category: 'tecnologia',
      rarity: 'legendary',
      verified: true,
      transactionHash: '0xabcd...efgh',
      drawType: 'lottery-linked'
    },
    {
      id: '2',
      productId: 'ps5-console',
      productName: 'PlayStation 5',
      productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=PlayStation%205%20console%20white%20modern%20gaming%20setup&image_size=square',
      drawDate: '2024-01-12T19:30:00Z',
      drawNumber: '9876',
      winnerAddress: '0x8765...4321',
      winnerTickets: 5,
      totalTickets: 1500,
      prizeValue: 25000,
      category: 'gaming',
      rarity: 'epic',
      verified: true,
      transactionHash: '0x1234...abcd',
      drawType: 'random'
    },
    {
      id: '3',
      productId: 'iphone-15',
      productName: 'iPhone 15 Pro Max',
      productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20titanium%20professional%20product%20photography&image_size=square',
      drawDate: '2024-01-10T21:00:00Z',
      drawNumber: '1234',
      winnerAddress: '0x9876...1234',
      winnerTickets: 2,
      totalTickets: 2500,
      prizeValue: 50000,
      category: 'tecnologia',
      rarity: 'legendary',
      verified: true,
      transactionHash: '0x5678...9012',
      drawType: 'lottery-linked'
    }
  ];

  const upcomingDraws: UpcomingDraw[] = [
    {
      id: '1',
      productName: 'Tesla Model Y',
      productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Tesla%20Model%20Y%20white%20electric%20car%20futuristic%20design&image_size=square',
      drawDate: '2024-01-20T20:00:00Z',
      timeLeft: '2d 14h 30m',
      totalTickets: 10000,
      soldTickets: 7500,
      prizeValue: 200000,
      category: 'vehiculos'
    },
    {
      id: '2',
      productName: 'Apple Watch Ultra 2',
      productImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Apple%20Watch%20Ultra%202%20titanium%20outdoor%20adventure%20sports&image_size=square',
      drawDate: '2024-01-18T19:00:00Z',
      timeLeft: '1d 8h 15m',
      totalTickets: 1200,
      soldTickets: 950,
      prizeValue: 15000,
      category: 'tecnologia'
    }
  ];

  const topWinners: Winner[] = [
    {
      id: '1',
      address: '0x1234...5678',
      productName: 'MacBook Pro M3',
      prizeValue: 75000,
      date: '2024-01-15',
      verified: true
    },
    {
      id: '2',
      address: '0x8765...4321',
      productName: 'PlayStation 5',
      prizeValue: 25000,
      date: '2024-01-12',
      verified: true
    },
    {
      id: '3',
      address: '0x9876...1234',
      productName: 'iPhone 15 Pro Max',
      prizeValue: 50000,
      date: '2024-01-10',
      verified: true
    }
  ];

  const statistics = {
    totalDraws: 156,
    totalPrizeValue: 2450000,
    totalWinners: 156,
    averagePrize: 15705,
    biggestWin: 200000,
    thisMonthDraws: 12,
    thisMonthPrizes: 185000
  };

  const categories = [
    { id: 'all', name: 'Todas las categorías' },
    { id: 'tecnologia', name: 'Tecnología' },
    { id: 'gaming', name: 'Gaming' },
    { id: 'vehiculos', name: 'Vehículos' },
    { id: 'hogar', name: 'Hogar' },
    { id: 'moda', name: 'Moda' }
  ];

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-600';
      case 'rare': return 'from-blue-400 to-blue-600';
      case 'epic': return 'from-purple-400 to-purple-600';
      case 'legendary': return 'from-amber-400 to-amber-600';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = (sold: number, total: number) => {
    return (sold / total) * 100;
  };

  const ResultCard: React.FC<{ result: DrawResult }> = ({ result }) => {
    const isExpanded = expandedResult === result.id;
    
    return (
      <Card variant="glass" className="hover:scale-105 transition-all duration-300">
        <div className="space-y-4">
          <div className="flex items-start space-x-4">
            <img 
              src={result.productImage} 
              alt={result.productName}
              className="w-16 h-16 rounded-lg object-cover"
            />
            <div className="flex-1">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-white font-semibold text-lg">{result.productName}</h3>
                <div className="flex items-center space-x-2">
                  {result.verified && (
                    <div className="flex items-center space-x-1 px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs">
                      <Award className="w-3 h-3" />
                      <span>Verificado</span>
                    </div>
                  )}
                  <span className={`px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r ${getRarityColor(result.rarity)} text-white`}>
                    {result.rarity.charAt(0).toUpperCase() + result.rarity.slice(1)}
                  </span>
                </div>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-white/60">Fecha:</span>
                  <div className="text-white font-medium">{formatDate(result.drawDate)}</div>
                </div>
                <div>
                  <span className="text-white/60">Número ganador:</span>
                  <div className="text-amber-400 font-bold font-mono">{result.drawNumber}</div>
                </div>
                <div>
                  <span className="text-white/60">Ganador:</span>
                  <div className="text-purple-400 font-mono">{result.winnerAddress}</div>
                </div>
                <div>
                  <span className="text-white/60">Premio:</span>
                  <div className="text-green-400 font-bold">${result.prizeValue.toLocaleString()}</div>
                </div>
              </div>
            </div>
            
            <button
              onClick={() => setExpandedResult(isExpanded ? null : result.id)}
              className="text-white/70 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-colors"
            >
              {isExpanded ? <ChevronUp className="w-5 h-5" /> : <ChevronDown className="w-5 h-5" />}
            </button>
          </div>
          
          {isExpanded && (
            <div className="border-t border-white/20 pt-4 space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-white/60">Tickets del ganador:</span>
                  <div className="text-white font-medium">{result.winnerTickets}</div>
                </div>
                <div>
                  <span className="text-white/60">Total de tickets:</span>
                  <div className="text-white font-medium">{result.totalTickets.toLocaleString()}</div>
                </div>
                <div>
                  <span className="text-white/60">Probabilidad:</span>
                  <div className="text-white font-medium">{((result.winnerTickets / result.totalTickets) * 100).toFixed(4)}%</div>
                </div>
              </div>
              
              {result.transactionHash && (
                <div className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                  <div>
                    <span className="text-white/60 text-sm">Hash de transacción:</span>
                    <div className="text-white font-mono text-sm">{result.transactionHash}</div>
                  </div>
                  <Button variant="outline" size="sm">
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Ver en blockchain
                  </Button>
                </div>
              )}
              
              <div className="flex space-x-3">
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-2" />
                  Compartir
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Descargar certificado
                </Button>
              </div>
            </div>
          )}
        </div>
      </Card>
    );
  };

  const UpcomingCard: React.FC<{ draw: UpcomingDraw }> = ({ draw }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="flex items-start space-x-4">
        <img 
          src={draw.productImage} 
          alt={draw.productName}
          className="w-16 h-16 rounded-lg object-cover"
        />
        <div className="flex-1">
          <h3 className="text-white font-semibold text-lg mb-2">{draw.productName}</h3>
          
          <div className="grid grid-cols-2 gap-4 text-sm mb-4">
            <div>
              <span className="text-white/60">Sorteo en:</span>
              <div className="text-amber-400 font-bold">{draw.timeLeft}</div>
            </div>
            <div>
              <span className="text-white/60">Premio:</span>
              <div className="text-green-400 font-bold">${draw.prizeValue.toLocaleString()}</div>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-white/60">Progreso:</span>
              <span className="text-white">{draw.soldTickets}/{draw.totalTickets} tickets</span>
            </div>
            <div className="w-full bg-white/10 rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-purple-500 to-amber-500 h-2 rounded-full transition-all duration-500"
                style={{ width: `${getProgressPercentage(draw.soldTickets, draw.totalTickets)}%` }}
              ></div>
            </div>
            <div className="text-right text-xs text-white/60">
              {getProgressPercentage(draw.soldTickets, draw.totalTickets).toFixed(1)}% completado
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  const WinnerCard: React.FC<{ winner: Winner }> = ({ winner }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="flex items-center space-x-4">
        <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-amber-500 rounded-full flex items-center justify-center">
          <Trophy className="w-6 h-6 text-white" />
        </div>
        <div className="flex-1">
          <div className="flex items-center justify-between mb-1">
            <h3 className="text-white font-semibold">{winner.productName}</h3>
            {winner.verified && (
              <Award className="w-4 h-4 text-green-400" />
            )}
          </div>
          <div className="text-purple-400 font-mono text-sm mb-1">{winner.address}</div>
          <div className="flex items-center justify-between">
            <span className="text-green-400 font-bold">${winner.prizeValue.toLocaleString()}</span>
            <span className="text-white/60 text-sm">{new Date(winner.date).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Resultados de <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Sorteos</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Descubre los ganadores más recientes y próximos sorteos
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <Card variant="glass">
            <div className="text-center">
              <Trophy className="w-8 h-8 text-amber-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">{statistics.totalDraws}</div>
              <div className="text-white/70 text-sm">Sorteos Totales</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Gift className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-green-400 mb-1">${statistics.totalPrizeValue.toLocaleString()}</div>
              <div className="text-white/70 text-sm">Premios Entregados</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Users className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">{statistics.totalWinners}</div>
              <div className="text-white/70 text-sm">Ganadores</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <div className="text-2xl font-bold text-white mb-1">${statistics.averagePrize.toLocaleString()}</div>
              <div className="text-white/70 text-sm">Premio Promedio</div>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {[
            { id: 'recent', label: 'Resultados Recientes', icon: Trophy },
            { id: 'upcoming', label: 'Próximos Sorteos', icon: Clock },
            { id: 'winners', label: 'Top Ganadores', icon: Star },
            { id: 'statistics', label: 'Estadísticas', icon: TrendingUp }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium hidden sm:block">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Filters */}
        {(activeTab === 'recent' || activeTab === 'upcoming') && (
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
              <input
                type="text"
                placeholder="Buscar por producto..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <select
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value)}
              className="px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              {categories.map(category => (
                <option key={category.id} value={category.id}>{category.name}</option>
              ))}
            </select>
            {activeTab === 'recent' && (
              <select
                value={timeFilter}
                onChange={(e) => setTimeFilter(e.target.value as any)}
                className="px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">Todo el tiempo</option>
                <option value="week">Esta semana</option>
                <option value="month">Este mes</option>
                <option value="year">Este año</option>
              </select>
            )}
          </div>
        )}

        {/* Content */}
        <div className="space-y-6">
          {activeTab === 'recent' && (
            <div className="space-y-4">
              {recentResults.map(result => (
                <ResultCard key={result.id} result={result} />
              ))}
            </div>
          )}

          {activeTab === 'upcoming' && (
            <div className="space-y-4">
              {upcomingDraws.map(draw => (
                <UpcomingCard key={draw.id} draw={draw} />
              ))}
            </div>
          )}

          {activeTab === 'winners' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {topWinners.map(winner => (
                <WinnerCard key={winner.id} winner={winner} />
              ))}
            </div>
          )}

          {activeTab === 'statistics' && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card variant="glass">
                <div className="text-center">
                  <Zap className="w-12 h-12 text-amber-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Mayor Premio</h3>
                  <div className="text-3xl font-bold text-green-400 mb-2">${statistics.biggestWin.toLocaleString()}</div>
                  <p className="text-white/70 text-sm">Tesla Model Y - Enero 2024</p>
                </div>
              </Card>
              
              <Card variant="glass">
                <div className="text-center">
                  <Calendar className="w-12 h-12 text-blue-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Este Mes</h3>
                  <div className="text-3xl font-bold text-white mb-2">{statistics.thisMonthDraws}</div>
                  <p className="text-white/70 text-sm">Sorteos realizados</p>
                  <div className="text-lg font-semibold text-green-400 mt-2">${statistics.thisMonthPrizes.toLocaleString()}</div>
                  <p className="text-white/70 text-xs">en premios entregados</p>
                </div>
              </Card>
              
              <Card variant="glass">
                <div className="text-center">
                  <Award className="w-12 h-12 text-purple-400 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-white mb-2">Tasa de Éxito</h3>
                  <div className="text-3xl font-bold text-white mb-2">100%</div>
                  <p className="text-white/70 text-sm">Sorteos completados exitosamente</p>
                  <div className="text-lg font-semibold text-green-400 mt-2">Verificado</div>
                  <p className="text-white/70 text-xs">por blockchain</p>
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Results;