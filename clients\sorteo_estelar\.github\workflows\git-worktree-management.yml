name: Git Worktree Management & Trunk-Based Development

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'Worktree action'
        required: true
        type: choice
        options:
        - create-feature
        - create-hotfix
        - cleanup-worktrees
        - sync-main
        - validate-branches
      feature_name:
        description: 'Feature/Hotfix name (for create actions)'
        required: false
        type: string
      base_branch:
        description: 'Base branch'
        required: false
        default: 'main'
        type: string
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Daily cleanup at 2 AM UTC
    - cron: '0 2 * * *'

env:
  VPS_HOST: '**************'
  VPS_PORT: '2222'
  VPS_USER: 'admin'
  MAX_WORKTREES: 3
  FEATURE_LIFETIME_DAYS: 3

jobs:
  validate-trunk-state:
    runs-on: ubuntu-latest
    outputs:
      main-status: ${{ steps.check.outputs.main-status }}
      branch-count: ${{ steps.check.outputs.branch-count }}
      stale-branches: ${{ steps.check.outputs.stale-branches }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate trunk state
        id: check
        run: |
          # Check main branch status
          git checkout main
          git pull origin main
          
          # Count active branches
          BRANCH_COUNT=$(git branch -r | grep -v 'origin/HEAD' | grep -v 'origin/main' | wc -l)
          echo "branch-count=$BRANCH_COUNT" >> $GITHUB_OUTPUT
          
          # Find stale branches (older than 3 days)
          STALE_BRANCHES=$(git for-each-ref --format='%(refname:short) %(committerdate)' refs/remotes/origin | \
            awk '$2 < "'$(date -d '3 days ago' '+%Y-%m-%d')'"{print $1}' | \
            grep -v 'origin/main' | grep -v 'origin/HEAD' || echo "none")
          echo "stale-branches=$STALE_BRANCHES" >> $GITHUB_OUTPUT
          
          # Check if main is clean
          if git diff --quiet && git diff --cached --quiet; then
            echo "main-status=clean" >> $GITHUB_OUTPUT
          else
            echo "main-status=dirty" >> $GITHUB_OUTPUT
          fi
          
          echo "=== Trunk State Report ==="
          echo "Main branch status: $(git status --porcelain | wc -l) uncommitted changes"
          echo "Active remote branches: $BRANCH_COUNT"
          echo "Stale branches: $STALE_BRANCHES"

  create-worktree:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'create-feature' || github.event.inputs.action == 'create-hotfix'
    needs: validate-trunk-state
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Create worktree on VPS
        env:
          FEATURE_NAME: ${{ github.event.inputs.feature_name }}
          BASE_BRANCH: ${{ github.event.inputs.base_branch }}
          ACTION_TYPE: ${{ github.event.inputs.action }}
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            cd /opt/sorteo-estelar
            
            # Ensure main repository exists
            if [ ! -d "sorteo-estelar-main" ]; then
              git clone https://github.com/${{ github.repository }}.git sorteo-estelar-main
              cd sorteo-estelar-main
            else
              cd sorteo-estelar-main
              git fetch origin
              git checkout main
              git pull origin main
            fi
            
            # Determine branch prefix
            if [ "'$ACTION_TYPE'" == "create-hotfix" ]; then
              BRANCH_PREFIX="hotfix"
            else
              BRANCH_PREFIX="feature"
            fi
            
            # Create branch name with timestamp
            TIMESTAMP=$(date +"%Y%m%d-%H%M")
            BRANCH_NAME="$BRANCH_PREFIX/'$FEATURE_NAME'-$TIMESTAMP"
            WORKTREE_DIR="../sorteo-estelar-$BRANCH_PREFIX-'$FEATURE_NAME'"
            
            # Check worktree limit
            CURRENT_WORKTREES=$(find .. -maxdepth 1 -name "sorteo-estelar-*" -type d | wc -l)
            if [ $CURRENT_WORKTREES -ge '$MAX_WORKTREES' ]; then
              echo "❌ Maximum worktrees ($MAX_WORKTREES) reached. Clean up existing worktrees first."
              exit 1
            fi
            
            # Create worktree
            git worktree add -b "$BRANCH_NAME" "$WORKTREE_DIR" '$BASE_BRANCH'
            
            # Setup worktree environment
            cd "$WORKTREE_DIR"
            
            # Create worktree metadata
            cat > .worktree-info <<EOF
            BRANCH_NAME=$BRANCH_NAME
            CREATED_AT=$(date -Iseconds)
            CREATED_BY=${{ github.actor }}
            ACTION_TYPE='$ACTION_TYPE'
            BASE_BRANCH='$BASE_BRANCH'
            FEATURE_NAME='$FEATURE_NAME'
            EOF
            
            # Install dependencies
            npm ci
            
            # Run initial tests
            npm run test
            
            echo "✅ Worktree created successfully:"
            echo "   Branch: $BRANCH_NAME"
            echo "   Directory: $WORKTREE_DIR"
            echo "   Base: '$BASE_BRANCH'"
            
            # Push branch to remote
            git push -u origin "$BRANCH_NAME"
          '

  cleanup-worktrees:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'cleanup-worktrees' || github.event_name == 'schedule'
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Cleanup stale worktrees
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            cd /opt/sorteo-estelar
            
            if [ ! -d "sorteo-estelar-main" ]; then
              echo "Main repository not found. Nothing to clean."
              exit 0
            fi
            
            cd sorteo-estelar-main
            
            echo "=== Cleaning up stale worktrees ==="
            
            # Find all worktree directories
            for worktree_dir in ../sorteo-estelar-*; do
              if [ "$worktree_dir" == "../sorteo-estelar-main" ]; then
                continue
              fi
              
              if [ ! -d "$worktree_dir" ]; then
                continue
              fi
              
              echo "Checking worktree: $worktree_dir"
              
              # Check if worktree has metadata
              if [ -f "$worktree_dir/.worktree-info" ]; then
                source "$worktree_dir/.worktree-info"
                
                # Calculate age in days
                CREATED_TIMESTAMP=$(date -d "$CREATED_AT" +%s)
                CURRENT_TIMESTAMP=$(date +%s)
                AGE_DAYS=$(( (CURRENT_TIMESTAMP - CREATED_TIMESTAMP) / 86400 ))
                
                echo "  Branch: $BRANCH_NAME"
                echo "  Age: $AGE_DAYS days"
                echo "  Type: $ACTION_TYPE"
                
                # Remove if older than lifetime
                if [ $AGE_DAYS -gt '$FEATURE_LIFETIME_DAYS' ]; then
                  echo "  🗑️  Removing stale worktree (age: $AGE_DAYS days)"
                  
                  # Remove worktree
                  git worktree remove "$worktree_dir" --force
                  
                  # Delete remote branch if it exists
                  if git ls-remote --heads origin "$BRANCH_NAME" | grep -q "$BRANCH_NAME"; then
                    git push origin --delete "$BRANCH_NAME" || echo "Failed to delete remote branch"
                  fi
                else
                  echo "  ✅ Worktree is still fresh"
                fi
              else
                echo "  ⚠️  No metadata found, removing orphaned worktree"
                git worktree remove "$worktree_dir" --force
              fi
            done
            
            # Prune worktree references
            git worktree prune
            
            echo "=== Cleanup completed ==="
            git worktree list
          '

  sync-main:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'sync-main' || github.ref == 'refs/heads/main'
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Sync main branch across worktrees
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            cd /opt/sorteo-estelar
            
            if [ ! -d "sorteo-estelar-main" ]; then
              echo "Main repository not found."
              exit 1
            fi
            
            cd sorteo-estelar-main
            
            echo "=== Syncing main branch ==="
            git fetch origin
            git checkout main
            git pull origin main
            
            # Update all active worktrees
            for worktree_dir in ../sorteo-estelar-*; do
              if [ "$worktree_dir" == "../sorteo-estelar-main" ]; then
                continue
              fi
              
              if [ ! -d "$worktree_dir" ]; then
                continue
              fi
              
              echo "Updating worktree: $worktree_dir"
              cd "$worktree_dir"
              
              # Fetch latest changes
              git fetch origin
              
              # Check if we can fast-forward merge from main
              CURRENT_BRANCH=$(git branch --show-current)
              MERGE_BASE=$(git merge-base HEAD origin/main)
              MAIN_HEAD=$(git rev-parse origin/main)
              
              if [ "$MERGE_BASE" != "$MAIN_HEAD" ]; then
                echo "  📥 Merging latest main into $CURRENT_BRANCH"
                git merge origin/main --no-edit || {
                  echo "  ⚠️  Merge conflict detected in $CURRENT_BRANCH"
                  git merge --abort
                  echo "  📝 Manual intervention required"
                }
              else
                echo "  ✅ Branch is up to date with main"
              fi
              
              cd /opt/sorteo-estelar/sorteo-estelar-main
            done
            
            echo "=== Sync completed ==="
          '

  validate-branches:
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'validate-branches' || github.event_name == 'pull_request'
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate branch compliance
        run: |
          echo "=== Branch Validation Report ==="
          
          # Check branch naming convention
          CURRENT_BRANCH="${{ github.head_ref || github.ref_name }}"
          echo "Validating branch: $CURRENT_BRANCH"
          
          if [[ "$CURRENT_BRANCH" =~ ^(feature|hotfix|bugfix)/[a-z0-9-]+$ ]]; then
            echo "✅ Branch name follows convention"
          elif [ "$CURRENT_BRANCH" == "main" ] || [ "$CURRENT_BRANCH" == "develop" ]; then
            echo "✅ Protected branch"
          else
            echo "❌ Branch name does not follow convention: feature/*, hotfix/*, or bugfix/*"
            exit 1
          fi
          
          # Check commit message format
          LAST_COMMIT_MSG=$(git log -1 --pretty=format:'%s')
          if [[ "$LAST_COMMIT_MSG" =~ ^(feat|fix|docs|style|refactor|test|chore)(\(.+\))?: .+ ]]; then
            echo "✅ Commit message follows conventional format"
          else
            echo "⚠️  Commit message should follow conventional format: type(scope): description"
          fi
          
          # Check for merge conflicts
          git fetch origin main
          if git merge-tree $(git merge-base HEAD origin/main) HEAD origin/main | grep -q "<<<<<<< "; then
            echo "❌ Merge conflicts detected with main branch"
            exit 1
          else
            echo "✅ No merge conflicts with main"
          fi
          
          # Check branch age
          BRANCH_AGE_DAYS=$(( ($(date +%s) - $(git log -1 --format="%ct" origin/main..HEAD | tail -1)) / 86400 ))
          if [ $BRANCH_AGE_DAYS -gt $FEATURE_LIFETIME_DAYS ]; then
            echo "⚠️  Branch is $BRANCH_AGE_DAYS days old (max recommended: $FEATURE_LIFETIME_DAYS days)"
          else
            echo "✅ Branch age is acceptable ($BRANCH_AGE_DAYS days)"
          fi

  worktree-status:
    runs-on: ubuntu-latest
    if: always()
    needs: [validate-trunk-state]
    steps:
      - name: Setup SSH for VPS
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Report worktree status
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            cd /opt/sorteo-estelar
            
            echo "=== Git Worktree Status Report ==="
            echo "Timestamp: $(date -Iseconds)"
            echo "Triggered by: ${{ github.actor }}"
            echo "Action: ${{ github.event.inputs.action || github.event_name }}"
            echo ""
            
            if [ -d "sorteo-estelar-main" ]; then
              cd sorteo-estelar-main
              
              echo "📊 Repository Status:"
              git status --short
              echo ""
              
              echo "🌳 Active Worktrees:"
              git worktree list
              echo ""
              
              echo "📋 Worktree Details:"
              for worktree_dir in ../sorteo-estelar-*; do
                if [ "$worktree_dir" == "../sorteo-estelar-main" ]; then
                  continue
                fi
                
                if [ -d "$worktree_dir" ] && [ -f "$worktree_dir/.worktree-info" ]; then
                  echo "  📁 $(basename $worktree_dir):"
                  source "$worktree_dir/.worktree-info"
                  echo "     Branch: $BRANCH_NAME"
                  echo "     Created: $CREATED_AT"
                  echo "     By: $CREATED_BY"
                  echo "     Type: $ACTION_TYPE"
                  
                  # Check if branch has uncommitted changes
                  cd "$worktree_dir"
                  if [ -n "$(git status --porcelain)" ]; then
                    echo "     Status: 🔄 Has uncommitted changes"
                  else
                    echo "     Status: ✅ Clean"
                  fi
                  cd /opt/sorteo-estelar/sorteo-estelar-main
                  echo ""
                fi
              done
              
              echo "📈 Branch Statistics:"
              echo "   Main branch commits: $(git rev-list --count main)"
              echo "   Active remote branches: $(git branch -r | grep -v HEAD | wc -l)"
              echo "   Local worktrees: $(git worktree list | wc -l)"
            else
              echo "❌ Main repository not found at /opt/sorteo-estelar/sorteo-estelar-main"
            fi
          '