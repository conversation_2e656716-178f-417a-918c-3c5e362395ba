import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3005'),
  HOST: z.string().default('0.0.0.0'),
  
  // Security
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('24h'),
  CORS_ORIGIN: z.string().default('*'),
  
  // Database
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url().default('redis://localhost:6379'),
  
  // AI Services
  OPENAI_API_KEY: z.string().min(1),
  OPENAI_MODEL: z.string().default('gpt-4'),
  OPENAI_TEMPERATURE: z.string().transform(Number).default('0.7'),
  LANGCHAIN_API_KEY: z.string().optional(),
  LANGCHAIN_TRACING_V2: z.string().transform(Boolean).default('false'),
  
  // External Services
  PROJECT_MANAGEMENT_SERVICE_URL: z.string().url().default('http://localhost:3002'),
  SMART_CONTRACTS_SERVICE_URL: z.string().url().default('http://localhost:3003'),
  CONTENT_STUDIO_SERVICE_URL: z.string().url().default('http://localhost:3001'),
  AGENT_ORCHESTRATION_SERVICE_URL: z.string().url().default('http://localhost:3004'),
  
  // Email Configuration
  SMTP_HOST: z.string().default('smtp.gmail.com'),
  SMTP_PORT: z.string().transform(Number).default('587'),
  SMTP_SECURE: z.string().transform(Boolean).default('false'),
  SMTP_USER: z.string().optional(),
  SMTP_PASS: z.string().optional(),
  EMAIL_FROM: z.string().email().default('<EMAIL>'),
  
  // File Upload Configuration
  UPLOAD_MAX_SIZE: z.string().transform(Number).default('10485760'), // 10MB
  UPLOAD_ALLOWED_TYPES: z.string().default('pdf,doc,docx,txt,jpg,jpeg,png'),
  UPLOAD_STORAGE_PATH: z.string().default('./uploads'),
  
  // RFP Processing
  RFP_AUTO_PROCESSING: z.string().transform(Boolean).default('true'),
  RFP_PROCESSING_TIMEOUT: z.string().transform(Number).default('300000'), // 5 minutes
  RFP_ANALYSIS_MODEL: z.string().default('gpt-4'),
  RFP_SCORING_THRESHOLD: z.string().transform(Number).default('0.7'),
  
  // Client Onboarding
  ONBOARDING_WORKFLOW_ENABLED: z.string().transform(Boolean).default('true'),
  ONBOARDING_AUTO_ASSIGN: z.string().transform(Boolean).default('true'),
  ONBOARDING_NOTIFICATION_ENABLED: z.string().transform(Boolean).default('true'),
  ONBOARDING_SLA_HOURS: z.string().transform(Number).default('24'),
  
  // Queue Configuration
  QUEUE_REDIS_URL: z.string().url().optional(),
  QUEUE_CONCURRENCY: z.string().transform(Number).default('5'),
  QUEUE_RETRY_ATTEMPTS: z.string().transform(Number).default('3'),
  QUEUE_RETRY_DELAY_MS: z.string().transform(Number).default('5000'),
  
  // CRM Integration
  CRM_ENABLED: z.string().transform(Boolean).default('false'),
  CRM_API_URL: z.string().url().optional(),
  CRM_API_KEY: z.string().optional(),
  CRM_SYNC_INTERVAL: z.string().transform(Number).default('3600000'), // 1 hour
  
  // Monitoring
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  METRICS_ENABLED: z.string().transform(Boolean).default('true'),
  HEALTH_CHECK_INTERVAL: z.string().transform(Number).default('30000'),
  
  // Feature Flags
  ENABLE_DOCUMENT_OCR: z.string().transform(Boolean).default('true'),
  ENABLE_SENTIMENT_ANALYSIS: z.string().transform(Boolean).default('true'),
  ENABLE_AUTO_QUALIFICATION: z.string().transform(Boolean).default('true'),
  ENABLE_LEAD_SCORING: z.string().transform(Boolean).default('true')
});

// Validate and export environment
const env = envSchema.parse(process.env);

export const environment = {
  // Server
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  host: env.HOST,
  
  // Security
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN
  },
  cors: {
    origin: env.CORS_ORIGIN
  },
  
  // Database
  database: {
    url: env.DATABASE_URL
  },
  redis: {
    url: env.REDIS_URL
  },
  
  // AI Services
  ai: {
    openai: {
      apiKey: env.OPENAI_API_KEY,
      model: env.OPENAI_MODEL,
      temperature: env.OPENAI_TEMPERATURE
    },
    langchain: {
      apiKey: env.LANGCHAIN_API_KEY,
      tracingEnabled: env.LANGCHAIN_TRACING_V2
    }
  },
  
  // External Services
  services: {
    projectManagement: env.PROJECT_MANAGEMENT_SERVICE_URL,
    smartContracts: env.SMART_CONTRACTS_SERVICE_URL,
    contentStudio: env.CONTENT_STUDIO_SERVICE_URL,
    agentOrchestration: env.AGENT_ORCHESTRATION_SERVICE_URL
  },
  
  // Email
  email: {
    smtp: {
      host: env.SMTP_HOST,
      port: env.SMTP_PORT,
      secure: env.SMTP_SECURE,
      auth: {
        user: env.SMTP_USER,
        pass: env.SMTP_PASS
      }
    },
    from: env.EMAIL_FROM
  },
  
  // File Upload
  upload: {
    maxSize: env.UPLOAD_MAX_SIZE,
    allowedTypes: env.UPLOAD_ALLOWED_TYPES.split(','),
    storagePath: env.UPLOAD_STORAGE_PATH
  },
  
  // RFP Processing
  rfp: {
    autoProcessing: env.RFP_AUTO_PROCESSING,
    processingTimeout: env.RFP_PROCESSING_TIMEOUT,
    analysisModel: env.RFP_ANALYSIS_MODEL,
    scoringThreshold: env.RFP_SCORING_THRESHOLD
  },
  
  // Client Onboarding
  onboarding: {
    workflowEnabled: env.ONBOARDING_WORKFLOW_ENABLED,
    autoAssign: env.ONBOARDING_AUTO_ASSIGN,
    notificationEnabled: env.ONBOARDING_NOTIFICATION_ENABLED,
    slaHours: env.ONBOARDING_SLA_HOURS
  },
  
  // Queue
  queue: {
    redisUrl: env.QUEUE_REDIS_URL || env.REDIS_URL,
    concurrency: env.QUEUE_CONCURRENCY,
    retryAttempts: env.QUEUE_RETRY_ATTEMPTS,
    retryDelayMs: env.QUEUE_RETRY_DELAY_MS
  },
  
  // CRM Integration
  crm: {
    enabled: env.CRM_ENABLED,
    apiUrl: env.CRM_API_URL,
    apiKey: env.CRM_API_KEY,
    syncInterval: env.CRM_SYNC_INTERVAL
  },
  
  // Monitoring
  monitoring: {
    logLevel: env.LOG_LEVEL,
    metricsEnabled: env.METRICS_ENABLED,
    healthCheckInterval: env.HEALTH_CHECK_INTERVAL
  },
  
  // Feature Flags
  features: {
    documentOcr: env.ENABLE_DOCUMENT_OCR,
    sentimentAnalysis: env.ENABLE_SENTIMENT_ANALYSIS,
    autoQualification: env.ENABLE_AUTO_QUALIFICATION,
    leadScoring: env.ENABLE_LEAD_SCORING
  }
};

export type Environment = typeof environment;
export default environment;