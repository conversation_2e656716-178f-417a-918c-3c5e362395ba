# Luxcrafts Platform Deployment Guide

This guide covers deployment strategies for the Luxcrafts Property Services Platform across different environments and platforms.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Configuration](#environment-configuration)
3. [Vercel Deployment](#vercel-deployment)
4. [VPS Deployment](#vps-deployment)
5. [Domain Configuration](#domain-configuration)
6. [SSL Certificate Setup](#ssl-certificate-setup)
7. [Monitoring and Maintenance](#monitoring-and-maintenance)
8. [Troubleshooting](#troubleshooting)

## Prerequisites

### Development Environment
- Node.js 18+ and npm/pnpm
- Git
- Vercel CLI (for Vercel deployment)
- SSH access to VPS (for VPS deployment)

### Required Accounts
- Vercel account
- Domain registrar account
- VPS provider account (DigitalOcean, AWS, etc.)
- Third-party service accounts (OpenAI, Stripe, etc.)

## Environment Configuration

### Environment Files

The project includes three environment configurations:

- `.env` - Development environment
- `.env.staging` - Staging environment
- `.env.production` - Production environment

### Required Environment Variables

```bash
# Application Settings
VITE_APP_NAME="Luxcrafts Property Services Platform"
VITE_APP_VERSION="1.0.0"
VITE_APP_ENVIRONMENT="production"
VITE_APP_URL="https://luxcrafts.com"

# Web3 Configuration
VITE_WALLETCONNECT_PROJECT_ID="your_project_id"
VITE_ALCHEMY_API_KEY="your_alchemy_key"

# API Configuration
VITE_API_BASE_URL="https://api.luxcrafts.com/api"

# Third-party Services
OPENAI_API_KEY="your_openai_key"
STRIPE_PUBLISHABLE_KEY="your_stripe_key"
```

## Vercel Deployment

### 1. Install Vercel CLI

```bash
npm install -g vercel
```

### 2. Login to Vercel

```bash
vercel login
```

### 3. Configure Environment Variables

In your Vercel dashboard:

1. Go to your project settings
2. Navigate to "Environment Variables"
3. Add all required variables for each environment:
   - Development
   - Preview (Staging)
   - Production

### 4. Deploy to Staging

```bash
# Deploy to preview environment
vercel --env=.env.staging
```

### 5. Deploy to Production

```bash
# Deploy to production
vercel --prod
```

### 6. Custom Domain Setup

1. In Vercel dashboard, go to "Domains"
2. Add your custom domain:
   - `staging.luxcrafts.com` for staging
   - `luxcrafts.com` for production
3. Configure DNS records as instructed by Vercel

## VPS Deployment

### 1. Server Requirements

- Ubuntu 20.04+ or CentOS 8+
- 2GB+ RAM
- 20GB+ storage
- Root or sudo access

### 2. Initial Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Nginx
sudo apt install nginx -y

# Install Certbot for SSL
sudo apt install certbot python3-certbot-nginx -y
```

### 3. Deploy Application

```bash
# Clone repository
git clone <your-repo-url>
cd luxcrafts

# Install dependencies
npm install

# Build for production
npm run build

# Run deployment script
sudo chmod +x deploy-vps.sh
sudo ./deploy-vps.sh --env production --domain luxcrafts.com --ssl
```

### 4. Manual Deployment Steps

If you prefer manual deployment:

```bash
# Create application directory
sudo mkdir -p /var/www/luxcrafts

# Copy build files
sudo cp -r dist/* /var/www/luxcrafts/

# Set permissions
sudo chown -R www-data:www-data /var/www/luxcrafts

# Configure Nginx (see nginx.conf example below)
sudo nano /etc/nginx/sites-available/luxcrafts

# Enable site
sudo ln -s /etc/nginx/sites-available/luxcrafts /etc/nginx/sites-enabled/

# Test and restart Nginx
sudo nginx -t
sudo systemctl restart nginx
```

### 5. Nginx Configuration

```nginx
server {
    listen 80;
    server_name luxcrafts.com www.luxcrafts.com;
    root /var/www/luxcrafts;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/json;

    # Static assets caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## Domain Configuration

### DNS Records

Configure the following DNS records:

#### For Vercel Deployment
```
Type    Name                Value
A       @                   *********** (Vercel IP)
CNAME   www                 cname.vercel-dns.com
CNAME   staging             cname.vercel-dns.com
```

#### For VPS Deployment
```
Type    Name                Value
A       @                   YOUR_VPS_IP
A       www                 YOUR_VPS_IP
A       staging             YOUR_VPS_IP
```

### Subdomain Strategy

- `luxcrafts.com` - Production
- `www.luxcrafts.com` - Production (redirect)
- `staging.luxcrafts.com` - Staging environment
- `dev.luxcrafts.com` - Development environment

## SSL Certificate Setup

### Automatic SSL (Recommended)

```bash
# For production domain
sudo certbot --nginx -d luxcrafts.com -d www.luxcrafts.com

# For staging domain
sudo certbot --nginx -d staging.luxcrafts.com
```

### Manual SSL Configuration

```bash
# Generate certificate
sudo certbot certonly --nginx -d luxcrafts.com

# Update Nginx configuration to use SSL
# Add SSL configuration block to nginx.conf
```

### Auto-renewal Setup

```bash
# Add to crontab
sudo crontab -e

# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

## Monitoring and Maintenance

### Health Checks

```bash
# Check Nginx status
sudo systemctl status nginx

# Check SSL certificate
sudo certbot certificates

# Check disk space
df -h

# Check memory usage
free -h
```

### Log Monitoring

```bash
# Nginx access logs
sudo tail -f /var/log/nginx/access.log

# Nginx error logs
sudo tail -f /var/log/nginx/error.log

# System logs
sudo journalctl -f
```

### Backup Strategy

```bash
# Create backup script
#!/bin/bash
BACKUP_DIR="/var/backups/luxcrafts"
DATE=$(date +%Y%m%d-%H%M%S)

mkdir -p $BACKUP_DIR
tar -czf $BACKUP_DIR/luxcrafts-$DATE.tar.gz -C /var/www/luxcrafts .

# Keep only last 7 backups
find $BACKUP_DIR -name "luxcrafts-*.tar.gz" -mtime +7 -delete
```

## Troubleshooting

### Common Issues

#### 1. Build Failures
```bash
# Clear cache and rebuild
npm run clean
npm install
npm run build
```

#### 2. Environment Variable Issues
```bash
# Check environment variables
echo $VITE_APP_ENVIRONMENT

# Verify .env file
cat .env.production
```

#### 3. SSL Certificate Issues
```bash
# Renew certificate manually
sudo certbot renew --force-renewal

# Check certificate status
sudo certbot certificates
```

#### 4. Nginx Configuration Issues
```bash
# Test configuration
sudo nginx -t

# Reload configuration
sudo systemctl reload nginx
```

### Performance Optimization

#### 1. Enable Gzip Compression
Already configured in the Nginx setup above.

#### 2. Optimize Images
```bash
# Install image optimization tools
npm install -g imagemin-cli

# Optimize images
imagemin src/assets/images/* --out-dir=dist/assets/images
```

#### 3. Bundle Analysis
```bash
# Analyze bundle size
npm run build -- --analyze
```

### Security Checklist

- [ ] SSL certificate installed and auto-renewal configured
- [ ] Security headers configured in Nginx
- [ ] Firewall configured (UFW)
- [ ] Regular security updates scheduled
- [ ] Environment variables secured
- [ ] Access logs monitored
- [ ] Backup strategy implemented

## Deployment Checklist

### Pre-deployment
- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Build successful locally
- [ ] Dependencies updated
- [ ] Security scan completed

### Post-deployment
- [ ] Application accessible via domain
- [ ] SSL certificate working
- [ ] All features functional
- [ ] Performance metrics acceptable
- [ ] Monitoring alerts configured
- [ ] Backup verified

## Support

For deployment issues:
1. Check the troubleshooting section
2. Review server logs
3. Contact the development team
4. Create an issue in the repository

---

**Note**: Replace placeholder values (API keys, domains, IPs) with actual values before deployment.