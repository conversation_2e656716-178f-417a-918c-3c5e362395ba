# Terraform IaC Coding Practices

This document defines the standards for using Terraform to manage Infrastructure as Code (IaC) within ESTRATIX. These rules ensure our infrastructure is secure, scalable, and consistently managed.

## 1. Core Principles

- **Idempotency:** All Terraform configurations must be idempotent. Applying the same configuration multiple times should result in the same state.
- **Immutability:** Treat infrastructure as immutable. Do not make manual changes to resources managed by Terraform. Instead, update the code and re-apply.
- **Declarative Definitions:** Define the desired state of the infrastructure, not the steps to get there.

## 2. Code Structure and Naming

- **Modularity:** Encapsulate reusable infrastructure patterns into modules. Use the internal ESTRATIX module registry.
- **File Organization:** Separate resources, variables, and outputs into logical files (e.g., main.tf, ariables.tf, outputs.tf).
- **Naming Conventions:** Use `snake_case` for all resources, variables, and outputs. Names should be descriptive and consistent (e.g., `resource "aws_s3_bucket" "prmary_artifact_storage"`).

## 3. State Management

- **Remote State:** Terraform state must be stored remotely (e.g., AWS S3 with DynamoDB for locking). Local state is forbidden.
- **Workspaces:** Use Terraform workspaces to manage different environments (e.g., dev, staging, prod).
- **Security:** State files must be encrypted at rest. Access must be restricted via IAM policies.

## 4. Security

- **Least Privilege:** All IAM roles and policies defined in Terraform must follow the principle of least privilege.
- **No Hardcoded Secrets:** Never store secrets (API keys, passwords, certificates) in Terraform files. Use a dedicated secrets manager like HashiCorp Vault or AWS Secrets Manager.
- **Security Scanning:** All Terraform code must be scanned for vulnerabilities and misconfigurations using tools like 	fsec or checkov in the CI/CD pipeline.

## 5. CI/CD and Version Control

- **Version Control:** All IaC must be stored in Git.
- **Branching:** Follow the prescribed project branching model.
- **Pull Requests:** All changes must be proposed via Pull Requests and require at least one approval.
- **Automated Pipeline:** The CI/CD pipeline must automatically run 	erraform fmt, alidate, and plan. The pply step must be manually triggered for production environments.
