---
trigger: always_on
---

# ESTRATIX Project Global Rules

## 1. Introduction & Philosophy

This document outlines the global rules, architectural principles, and operational standards for the ESTRATIX project. These rules are designed to be machine-readable and serve as a foundational prompt for all agentic activities, ensuring consistency, quality, and alignment with the project's strategic objectives.

**Core Philosophy**: ESTRATIX is an agentic development framework that models business value chains as a digital twin. It uses a process-centric, hexagonal architecture to create a clear separation of concerns, enabling robust, scalable, and maintainable solutions. **Flows orchestrate Processes, and Processes orchestrate Tasks, Agents, and Tools.**

## 2. Architectural Foundation

### 2.1. Hexagonal Architecture (Ports and Adapters)

- **Domain Layer**: Contains the core business logic, entities, and value objects. It is framework-agnostic and represents the "physical twin" of the documented components. All domain code resides in `src/domain/`.
- **Application Layer**: Contains the application-specific use cases and services. This layer orchestrates the domain logic to fulfill business requirements.
- **Infrastructure Layer**: Contains all external concerns, such as databases, UIs, and third-party APIs. It includes framework-specific implementations. All framework-specific code resides in `src/frameworks/[framework_name]/`.
- **Ports**: Define the interfaces for interacting with the application. They belong to the application layer.
- **Adapters**: Provide the concrete implementations of the ports, connecting the application to external systems.

### 2.2. Process-Centric Orchestration

The core of ESTRATIX orchestration follows a strict hierarchy:

1. **Flows (`F###`)**: High-level business capabilities. A Flow's sole responsibility is to orchestrate one or more Processes in a specific sequence to achieve a business goal. Flows DO NOT contain implementation logic.
2. **Processes (`P###`)**: Cohesive, reusable units of work that encapsulate a specific business or technical process. Processes are the primary unit of orchestration, responsible for sourcing and coordinating Agents, Tasks, and Tools.
3. **Tasks (`T###`)**: The smallest unit of executable work assigned to an Agent. A Task has a clear description, expected output, and leverages specific Tools.
4. **Agents (`A###`)**: Specialized actors responsible for executing Tasks. Agents are defined by their role, goal, backstory, and capabilities (tools).
5. **Tools (`T_***_###`)**: Reusable functions or components that Agents use to perform Tasks. Tools must be framework-agnostic and reside in `src/domain/tools/`.

This model ensures that logic is modular, reusable, and easy to trace, forming a clear "chain of command" from high-level business flow to low-level tool execution.

## 3. Development Rules (SOLID, DRY, TDD)

1. **SOLID Principles**: All code must adhere to SOLID principles.
2. **DRY (Don't Repeat Yourself)**: Maintain clean, non-repetitive code. Centralize reusable logic in the `src/domain` layer, especially for tools and data models.
3. **Test-Driven Development (TDD)**: All major components (especially Tools and Processes) must be developed with corresponding tests.
4. **Type Hints**: All Python code must use type hints for clarity and static analysis.
5. **Atomic Components**: Create focused components with clear, single responsibilities.
6. **Input Validation & Error Handling**: Implement robust validation and error handling at all layers.
7. **Security**: Follow security best practices. Never hardcode credentials; use a secrets management system.
8. **Documentation**: All code must be thoroughly documented with docstrings. All architectural decisions must be recorded in the relevant markdown definition files.

## 4. Rules Management & Orchestration

### 4.1. Rule Activation Modes

ESTRATIX rules operate in four modes to ensure continuous compliance and adaptability:

1. **Always-On (Default)**: These global rules are prepended to every prompt and are active for all agentic activities. They form the baseline operational context.
2. **Workflow-Triggered**: Specific rule files can be activated by generative workflows (e.g., flow_generation.md can activate rules related to code generation standards). This provides context-specific guidance.
3. **Agent-Triggered**: Agents can be given the capability to dynamically load specific rules based on the task at hand, allowing for specialized, fine-grained control.
4. **Multi-Assistant Coordination**: Rules that govern collaboration between multiple AI assistants, ensuring consistent communication protocols and task handoff procedures.

### 4.2. Rules for Rules (Self-Observability)

The management of this rule system is itself an agentic process, managed by **DocOps Agents** under the CIO.

- **Workflow**: The master workflow for this is `value_chain_observability_and_improvement.md`.
- **Process**: The core process is `CIO_P00X_RulesMaintenance`.
- **Task**: Key tasks include linting, validating, and updating rule files.
- **Goal**: To ensure the rule system remains consistent, up-to-date, and effective, creating a self-observing and self-governing system.

### 4.3. Multi-Assistant Coordination Protocol

**Coordination Principles:**
- **Shared Context**: All assistants must maintain awareness of the current project state via the `ESTRATIX_Assistant_Coordination_Worksheet.md`.
- **Task Handoff Protocol**: Clear documentation of deliverables, dependencies, and integration points between assistants.
- **PDCA Feedback Loops**: Plan-Do-Check-Act cycles for continuous improvement of multi-assistant workflows.
- **Conflict Resolution**: Escalation paths for resolving conflicting approaches or priorities between assistants.

**Communication Standards:**
- Daily status updates in the coordination worksheet
- Synchronization points for integration tasks
- Risk assessment and mitigation coordination
- Success metrics alignment across assistants

### 5.3. Communication Standards

**Daily Updates:**
- Each assistant must update their progress in the coordination worksheet
- Status updates must include: completed tasks, current focus, blockers, next steps
- Updates must be timestamped and include estimated completion times

**Synchronization Points:**
- Before starting new major tasks
- After completing significant milestones
- When encountering blockers or conflicts
- During handoffs between assistants

**Risk Coordination:**
- Immediate escalation of blocking issues
- Proactive communication of potential conflicts
- Shared risk register maintenance

**Success Metrics Alignment:**
- All assistants must align on sprint objectives
- Shared definition of "done" for each task
- Consistent quality standards across all work

### 5.4. Workflow Orchestration Patterns

**Iterative Cycle Management:**
- **Sprint Planning**: 2-week sprints with clear objectives and deliverables
- **Daily Standups**: Async updates via coordination worksheet
- **Sprint Reviews**: Demonstration of completed features and validation
- **Retrospectives**: Process improvement and lesson learned capture

**Parallel LLM Call Coordination:**
- **Task Partitioning**: Break complex tasks into independent, parallelizable units
- **Resource Allocation**: Assign specific domains/components to each assistant
- **Dependency Management**: Clear identification and sequencing of dependent tasks
- **Conflict Resolution**: Merge conflict prevention through clear ownership boundaries

**Command Office Patterns:**
- **Single Command Office**: Centralized decision-making for critical path items
- **Multiple Command Offices**: Distributed ownership with clear domain boundaries
- **Cross-Office Coordination**: Regular sync meetings and shared artifact reviews
- **Escalation Protocols**: Clear escalation paths for cross-office conflicts

### 5.5. PDCA Feedback Management

**Plan Phase:**
- Define clear objectives and success criteria
- Identify required resources and dependencies
- Establish timeline and milestone checkpoints
- Document assumptions and risk factors

**Do Phase:**
- Execute planned activities with continuous monitoring
- Document progress and any deviations from plan
- Capture lessons learned and improvement opportunities
- Maintain real-time status updates

**Check Phase:**
- Validate deliverables against success criteria
- Conduct quality reviews and testing
- Gather feedback from stakeholders and users
- Measure performance against established metrics

**Act Phase:**
- Implement improvements based on feedback
- Update processes and documentation
- Share lessons learned across teams
- Plan next iteration incorporating improvements

**Feedback Loop Integration:**
- **Real-time Feedback**: Immediate course correction during execution
- **Sprint Feedback**: End-of-sprint retrospectives and planning adjustments
- **Release Feedback**: Post-deployment monitoring and user feedback integration
- **Strategic Feedback**: Quarterly architecture and process reviews

## 5. Multi-Agent Orchestration Patterns

ESTRATIX will progressively implement advanced multi-agent patterns:

- **MCP (Multi-Crew/Company Protocol)**: Defines standards for interaction between different Command Offices or major crews.
- **A2A (Agent-to-Agent Protocol)**: Defines direct communication and collaboration patterns between individual agents.
- **ACP (Agent-to-Component Protocol)**: Defines how agents interact with non-agentic components like APIs, databases, and legacy systems.

These patterns will be defined as ESTRATIX Standards (`docs/standards/`) and implemented within the orchestration logic of Processes and Flows.

## 6. GitOps & DevOps Workflow Orchestration

### 6.1. Trunk-Based Development with Git Worktree Management

**Branch Strategy:**
- **Main Branch**: Production-ready code, protected with required reviews
- **Feature Branches**: Short-lived branches for specific features/tasks (max 3 days)
- **Git Worktree**: Multiple working directories for parallel development without branch switching

**Worktree Management Rules:**
- Each major task gets its own worktree: `git worktree add ../estratix-feature-xyz feature/xyz`
- Worktrees must be cleaned up after feature completion
- Maximum 3 active worktrees per developer/assistant
- Worktree naming convention: `estratix-[task-type]-[task-id]`

### 6.2. Agile Feature Testing & Validation

**Testing Pipeline:**
1. **Unit Tests**: Required for all domain layer components (Tools, Processes)
2. **Integration Tests**: Required for multi-component workflows
3. **Agent Validation Tests**: Verify agent behavior and tool usage
4. **End-to-End Tests**: Complete workflow validation

**Validation Gates:**
- Code quality checks (linting, type checking)
- Security scanning for sensitive data exposure
- Performance benchmarks for critical paths
- Documentation completeness verification

### 6.3. Continuous Integration & Deployment

**CI/CD Pipeline Stages:**
1. **Pre-commit**: Local validation (pre-commit hooks)
2. **Build**: Dependency resolution and artifact creation
3. **Test**: Automated test suite execution
4. **Security**: Vulnerability scanning and compliance checks
5. **Deploy**: Staged deployment with rollback capability

**Deployment Strategy:**
- **Development**: Automatic deployment on feature branch push
- **Staging**: Manual promotion after integration testing
- **Production**: Blue-green deployment with health checks

## 7. Digital Twin Implementation

The entire `docs` directory serves as the **Digital Twin** of the ESTRATIX system. The `src` directory is the **Physical Twin** (the implementation).

- There must be a 1:1 correspondence between a conceptual component in `docs` (e.g., a tool definition) and its implementation in `src`.
- The `value_chain_observability_and_improvement.md` workflow is responsible for orchestrating agents that continuously scan both twins to ensure they remain synchronized. Any drift must be flagged and corrected.

### 7.1. Digital Twin Synchronization

**Synchronization Rules:**
- Every component in `src/` must have corresponding documentation in `docs/`
- Matrix files must be updated when new components are added
- Architecture diagrams must reflect current implementation state
- API documentation must be auto-generated from code annotations

## 8. Technology Integration & Automation

### 8.1. Development Environment Standards

**Required Tools:**
- **Version Control**: Git with conventional commits
- **Package Management**: uv for Python dependency management
- **Code Quality**: pre-commit hooks, black, isort, mypy
- **Testing**: pytest with coverage reporting
- **Documentation**: Sphinx with auto-generation from docstrings

**IDE Configuration:**
- Consistent formatting and linting rules across all environments
- Shared VS Code/IDE settings for team consistency
- Required extensions for Python development and documentation

### 8.2. Automation Rules

**Automated Processes:**
- **Code Generation**: Template-based component scaffolding
- **Documentation Updates**: Auto-sync between code and docs
- **Testing**: Automated test execution on code changes
- **Deployment**: Automated staging deployments for feature branches
- **Monitoring**: Automated health checks and performance monitoring

**Manual Approval Gates:**
- Production deployments
- Architecture changes
- Security-sensitive modifications
- Breaking API changes

### 8.3. Performance & Scalability

**Performance Standards:**
- API response times < 200ms for 95th percentile
- Database query optimization for all data access patterns
- Caching strategies for frequently accessed data
- Asynchronous processing for long-running operations

**Scalability Requirements:**
- Horizontal scaling capability for all services
- Stateless service design where possible
- Database sharding strategies for large datasets
- Load balancing and auto-scaling configurations

## 9. Compliance & Security

### 9.1. Security Standards

**Code Security:**
- No hardcoded secrets or credentials
- Input validation for all external data
- Secure authentication and authorization
- Regular security dependency updates

**Data Protection:**
- Encryption at rest and in transit
- Personal data anonymization where required
- Audit logging for sensitive operations
- Regular security assessments

### 9.2. Compliance Requirements

**Documentation Compliance:**
- All public APIs must have OpenAPI specifications
- Security documentation for all components
- Data flow diagrams for compliance audits
- Regular compliance reviews and updates

**Operational Compliance:**
- Backup and disaster recovery procedures
- Incident response and escalation procedures
- Change management and approval processes
- Regular compliance training and awareness
