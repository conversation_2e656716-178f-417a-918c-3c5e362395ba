# ESTRATIX Project Management Database Schema

## Overview

This document defines the persistent database data structures and API architecture management endpoints for systemic project management execution, enabling proper CRUD operations for project matrices and task management.

## Database Schema Design

### 1. Core Project Management Tables

#### 1.1 Projects Table
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id VARCHAR(50) UNIQUE NOT NULL, -- e.g., 'INT_CTO_P004'
    project_name VARCHAR(255) NOT NULL,
    originating_proposal_id VARCHAR(50),
    project_type VARCHAR(100) NOT NULL,
    sponsoring_co VARCHAR(10) NOT NULL,
    project_manager VARCHAR(100),
    client_id VARCHAR(50),
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(20) NOT NULL,
    risk_level VARCHAR(20) NOT NULL,
    start_date_planned DATE,
    start_date_actual DATE,
    end_date_planned DATE,
    end_date_actual DATE,
    budget_planned DECIMAL(15,2),
    budget_actual DECIMAL(15,2),
    definition_link TEXT,
    project_directory_link TEXT,
    key_objectives_summary TEXT,
    primary_services_impacted TEXT[],
    primary_flows_involved TEXT[],
    primary_processes_involved TEXT[],
    conceptual_diagram_link TEXT,
    relevant_matrices TEXT[],
    key_stakeholders TEXT[],
    date_last_review DATE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 1.2 Tasks Table
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(50) UNIQUE NOT NULL,
    project_id VARCHAR(50) REFERENCES projects(project_id),
    category VARCHAR(20) NOT NULL,
    task_description TEXT NOT NULL,
    status VARCHAR(50) NOT NULL,
    priority VARCHAR(20),
    assignee VARCHAR(100),
    assistant VARCHAR(50),
    important BOOLEAN DEFAULT FALSE,
    urgent BOOLEAN DEFAULT FALSE,
    due_date DATE,
    notes TEXT,
    dependencies TEXT[],
    acceptance_criteria TEXT,
    completion_percentage INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP
);
```

#### 1.3 Project Status History Table
```sql
CREATE TABLE project_status_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id VARCHAR(50) REFERENCES projects(project_id),
    previous_status VARCHAR(50),
    new_status VARCHAR(50),
    changed_by VARCHAR(100),
    change_reason TEXT,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 1.4 Task Dependencies Table
```sql
CREATE TABLE task_dependencies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    task_id VARCHAR(50) REFERENCES tasks(task_id),
    depends_on_task_id VARCHAR(50) REFERENCES tasks(task_id),
    dependency_type VARCHAR(50) DEFAULT 'finish_to_start',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Configuration and Metadata Tables

#### 2.1 Project Types Table
```sql
CREATE TABLE project_types (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    type_code VARCHAR(50) UNIQUE NOT NULL,
    type_name VARCHAR(255) NOT NULL,
    description TEXT,
    default_template_path TEXT,
    active BOOLEAN DEFAULT TRUE
);
```

#### 2.2 Command Offices Table
```sql
CREATE TABLE command_offices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    co_code VARCHAR(10) UNIQUE NOT NULL,
    co_name VARCHAR(100) NOT NULL,
    description TEXT,
    lead_agent VARCHAR(100),
    active BOOLEAN DEFAULT TRUE
);
```

#### 2.3 Agents Table
```sql
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    agent_id VARCHAR(50) UNIQUE NOT NULL,
    agent_name VARCHAR(255) NOT NULL,
    agent_type VARCHAR(50),
    capabilities TEXT[],
    command_office VARCHAR(10) REFERENCES command_offices(co_code),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Architecture Management Endpoints

### 1. Project Management API

#### 1.1 Project CRUD Operations
```typescript
// GET /api/v1/projects
// GET /api/v1/projects/{project_id}
// POST /api/v1/projects
// PUT /api/v1/projects/{project_id}
// DELETE /api/v1/projects/{project_id}
// PATCH /api/v1/projects/{project_id}/status
```

#### 1.2 Task Management API
```typescript
// GET /api/v1/tasks
// GET /api/v1/tasks/{task_id}
// GET /api/v1/projects/{project_id}/tasks
// POST /api/v1/tasks
// PUT /api/v1/tasks/{task_id}
// DELETE /api/v1/tasks/{task_id}
// PATCH /api/v1/tasks/{task_id}/status
// PATCH /api/v1/tasks/{task_id}/progress
```

#### 1.3 Project Matrix API
```typescript
// GET /api/v1/matrix/projects
// GET /api/v1/matrix/projects/export
// POST /api/v1/matrix/projects/sync
// GET /api/v1/matrix/projects/dashboard
```

### 2. API Implementation Framework

#### 2.1 FastAPI Application Structure
```python
# src/api/main.py
from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware
from src.api.routers import projects, tasks, matrix
from src.api.database import get_db

app = FastAPI(title="ESTRATIX Project Management API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(projects.router, prefix="/api/v1/projects", tags=["projects"])
app.include_router(tasks.router, prefix="/api/v1/tasks", tags=["tasks"])
app.include_router(matrix.router, prefix="/api/v1/matrix", tags=["matrix"])
```

#### 2.2 Database Models (SQLAlchemy)
```python
# src/api/models/project.py
from sqlalchemy import Column, String, Text, DateTime, Boolean, DECIMAL, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from src.api.database import Base
import uuid

class Project(Base):
    __tablename__ = "projects"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(String(50), unique=True, nullable=False)
    project_name = Column(String(255), nullable=False)
    status = Column(String(50), nullable=False)
    priority = Column(String(20), nullable=False)
    # ... additional fields
```

#### 2.3 Pydantic Schemas
```python
# src/api/schemas/project.py
from pydantic import BaseModel
from typing import List, Optional
from datetime import date, datetime

class ProjectBase(BaseModel):
    project_id: str
    project_name: str
    project_type: str
    status: str
    priority: str
    # ... additional fields

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    project_name: Optional[str] = None
    status: Optional[str] = None
    # ... optional fields for updates

class Project(ProjectBase):
    id: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

### 3. Integration with Existing Systems

#### 3.1 Matrix Synchronization Service
```python
# src/services/matrix_sync.py
class MatrixSyncService:
    def sync_project_matrix_to_db(self, matrix_file_path: str):
        """Sync project matrix markdown file to database"""
        pass
    
    def export_db_to_matrix_file(self, output_path: str):
        """Export database projects to matrix markdown format"""
        pass
    
    def validate_matrix_consistency(self):
        """Validate consistency between matrix file and database"""
        pass
```

#### 3.2 Task List Synchronization Service
```python
# src/services/task_sync.py
class TaskSyncService:
    def sync_master_task_list_to_db(self, task_list_path: str):
        """Sync master task list to database"""
        pass
    
    def export_db_to_task_list(self, output_path: str):
        """Export database tasks to master task list format"""
        pass
```

## Implementation Roadmap

### Phase 1: Database Setup (Week 1)
- [ ] Set up PostgreSQL database
- [ ] Create database schema and tables
- [ ] Implement basic CRUD operations
- [ ] Set up database migrations

### Phase 2: API Development (Week 2)
- [ ] Implement FastAPI application structure
- [ ] Create project management endpoints
- [ ] Create task management endpoints
- [ ] Implement authentication and authorization

### Phase 3: Integration (Week 3)
- [ ] Develop matrix synchronization service
- [ ] Implement task list synchronization
- [ ] Create data validation and consistency checks
- [ ] Set up automated backups

### Phase 4: Dashboard and Monitoring (Week 4)
- [ ] Create project dashboard API endpoints
- [ ] Implement real-time status updates
- [ ] Set up monitoring and logging
- [ ] Performance optimization

## Security Considerations

- **Authentication**: JWT-based authentication for API access
- **Authorization**: Role-based access control (RBAC)
- **Data Validation**: Input validation and sanitization
- **Audit Trail**: Complete audit log for all changes
- **Backup Strategy**: Automated daily backups with point-in-time recovery

## Performance Optimization

- **Database Indexing**: Proper indexing on frequently queried columns
- **Caching**: Redis caching for frequently accessed data
- **Connection Pooling**: Database connection pooling for scalability
- **API Rate Limiting**: Rate limiting to prevent abuse

This database schema and API architecture provides a robust foundation for persistent project management with full CRUD operations, maintaining consistency between markdown matrices and database records while enabling real-time project tracking and management.