# Dockerfile for MedusaJS Backend

# Use an official Node runtime as a parent image
FROM node:18-alpine AS base

# Set the working directory
WORKDIR /app

# --- Dependencies ---
FROM base AS deps
# Copy package.json and lock file
COPY package.json yarn.lock* package-lock.json* ./
# Install dependencies
RUN yarn install --frozen-lockfile || npm install

# --- Builder ---
FROM base AS builder
# Copy dependencies from the 'deps' stage
COPY --from=deps /app/node_modules ./node_modules/
# Copy the rest of the application code
COPY . .
# Build the Medusa project
RUN yarn build || npm run build

# --- Runner ---
FROM base AS runner
WORKDIR /app

# Set environment variables (can be overridden)
ENV NODE_ENV production
# ENV DATABASE_URL postgres://user:password@host:port/db
# ENV REDIS_URL redis://host:port
# ENV JWT_SECRET your-jwt-secret
# ENV COOKIE_SECRET your-cookie-secret

# Copy necessary artifacts from the builder stage
COPY --from=builder /app/dist ./dist/
COPY --from=builder /app/node_modules ./node_modules/
COPY --from=builder /app/package.json ./package.json
# Copy migrations if they exist outside /dist (adjust if needed)
# COPY --from=builder /app/migrations ./migrations/

# Expose the port Medusa runs on (default 9000)
EXPOSE 9000

# Define the command to run the application
# Assumes Medusa CLI is installed globally or run via npx/yarn
# Use 'medusa start' or 'npm run start' / 'yarn start' depending on your setup
CMD ["node", "dist/main.js"]
# Or: CMD ["npm", "run", "start"]
# Or: CMD ["yarn", "start"]
