{"version": "2.0", "name": "luxcrafts-enterprise", "environments": {"staging": {"domain": {"primary": "staging.luxcrafts.co", "aliases": ["staging-www.luxcrafts.co"], "ssl": {"provider": "letsencrypt", "email": "<EMAIL>", "enabled": true, "hsts": true, "ocsp_stapling": true, "cipher_suite": "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384"}}, "resources": {"cpu": "500m", "memory": "1Gi", "storage": "5Gi", "replicas": 1}, "environment": {"NODE_ENV": "staging", "VITE_APP_ENVIRONMENT": "staging", "VITE_API_BASE_URL": "https://api-staging.luxcrafts.co", "VITE_SENTRY_DSN": "${STAGING_SENTRY_DSN}", "VITE_MONITORING_ENABLED": "true"}}, "production": {"domain": {"primary": "luxcrafts.co", "aliases": ["www.luxcrafts.co"], "ssl": {"provider": "letsencrypt", "email": "<EMAIL>", "enabled": true, "hsts": true, "ocsp_stapling": true, "cipher_suite": "ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384", "tls_version": "1.3"}}, "resources": {"cpu": "2000m", "memory": "4Gi", "storage": "20Gi", "replicas": 3}, "environment": {"NODE_ENV": "production", "VITE_APP_ENVIRONMENT": "production", "VITE_API_BASE_URL": "https://api.luxcrafts.co", "VITE_SENTRY_DSN": "${PRODUCTION_SENTRY_DSN}", "VITE_MONITORING_ENABLED": "true"}}}, "security": {"headers": {"X-Frame-Options": "DENY", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Strict-Transport-Security": "max-age=63072000; includeSubDomains; preload", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://unpkg.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https: blob:; connect-src 'self' https: wss: ws:; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; upgrade-insecure-requests;", "Referrer-Policy": "strict-origin-when-cross-origin", "Permissions-Policy": "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()", "Cross-Origin-Embedder-Policy": "require-corp", "Cross-Origin-Opener-Policy": "same-origin", "Cross-Origin-Resource-Policy": "same-origin"}, "firewall": {"enabled": true, "rules": [{"name": "allow-http", "port": 80, "protocol": "tcp", "source": "0.0.0.0/0"}, {"name": "allow-https", "port": 443, "protocol": "tcp", "source": "0.0.0.0/0"}, {"name": "allow-ssh", "port": 22, "protocol": "tcp", "source": "${ADMIN_IP_WHITELIST}"}, {"name": "deny-all", "action": "deny", "priority": 1000}], "rate_limiting": {"enabled": true, "requests_per_minute": 60, "burst": 10, "whitelist": ["${ADMIN_IP_WHITELIST}"]}, "ddos_protection": {"enabled": true, "threshold": 1000, "ban_duration": "1h"}}, "intrusion_detection": {"enabled": true, "fail2ban": {"enabled": true, "max_retry": 3, "ban_time": "1h", "find_time": "10m"}, "log_monitoring": {"enabled": true, "suspicious_patterns": ["SQL injection", "XSS attempt", "Directory traversal", "Brute force"]}}, "vulnerability_scanning": {"enabled": true, "schedule": "0 2 * * *", "tools": ["trivy", "grype", "snyk"], "auto_patch": false, "notifications": {"webhook": "${SECURITY_WEBHOOK_URL}", "email": "<EMAIL>"}}}, "deployment": {"type": "static", "source": "dist", "buildCommand": "npm run build", "outputDirectory": "dist", "strategy": "rolling", "health_check": {"path": "/health.json", "interval": "30s", "timeout": "10s", "retries": 3, "success_threshold": 2}, "rollback": {"enabled": true, "auto_rollback": true, "health_check_timeout": "5m"}, "blue_green": {"enabled": true, "traffic_split": {"canary_percentage": 10, "duration": "5m"}}}, "scaling": {"auto_scaling": {"enabled": true, "min_replicas": 2, "max_replicas": 10, "target_cpu": 70, "target_memory": 80, "scale_up_cooldown": "5m", "scale_down_cooldown": "10m"}, "load_balancer": {"algorithm": "round_robin", "health_check": true, "session_affinity": false}}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": {"daily": 7, "weekly": 4, "monthly": 12}, "storage": {"type": "s3", "bucket": "luxcrafts-backups", "encryption": true}, "verification": {"enabled": true, "schedule": "0 4 * * 0"}}, "monitoring": {"metrics": {"enabled": true, "prometheus": {"enabled": true, "port": 9090}, "grafana": {"enabled": true, "port": 3000}}, "logging": {"enabled": true, "level": "info", "format": "json", "retention": "30d", "aggregation": {"enabled": true, "elasticsearch": {"host": "${ELASTICSEARCH_HOST}", "index": "luxcrafts-logs"}}}, "alerting": {"enabled": true, "channels": [{"type": "slack", "webhook": "${SLACK_WEBHOOK_URL}", "severity": ["critical", "high"]}, {"type": "email", "recipients": ["<EMAIL>"], "severity": ["critical"]}, {"type": "<PERSON><PERSON><PERSON><PERSON>", "integration_key": "${PAGERDUTY_KEY}", "severity": ["critical"]}], "rules": [{"name": "high_cpu_usage", "condition": "cpu > 80%", "duration": "5m", "severity": "high"}, {"name": "high_memory_usage", "condition": "memory > 85%", "duration": "5m", "severity": "high"}, {"name": "application_down", "condition": "health_check_failed", "duration": "1m", "severity": "critical"}, {"name": "ssl_certificate_expiry", "condition": "ssl_expires_in < 7d", "severity": "high"}]}, "uptime": {"enabled": true, "endpoints": ["https://luxcrafts.co", "https://staging.luxcrafts.co", "https://api.luxcrafts.co/health"], "interval": "1m", "timeout": "30s"}}, "compliance": {"gdpr": {"enabled": true, "data_retention": "2y", "anonymization": true}, "security_standards": {"iso27001": true, "soc2": true, "pci_dss": false}, "audit_logging": {"enabled": true, "events": ["deployment", "configuration_change", "access", "security_event"], "retention": "7y"}}, "disaster_recovery": {"enabled": true, "rto": "15m", "rpo": "1h", "backup_regions": ["us-east-1", "eu-west-1"], "failover": {"automatic": true, "health_check_failures": 3, "timeout": "5m"}}}