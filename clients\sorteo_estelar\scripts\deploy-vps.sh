#!/bin/bash

# So<PERSON><PERSON> Estelar VPS Deployment Script
# This script automates the complete deployment process to VPS with Dokploy

set -euo pipefail

# Configuration
VPS_HOST="**************"
VPS_PORT="2222"
VPS_USER="admin"
DOMAIN="www.sorteoestelar.com"
STAGING_DOMAIN="staging.sorteoestelar.com"
MONITORING_DOMAIN="monitoring.sorteoestelar.com"
DOKPLOY_PORT="3000"
PROJECT_NAME="sorteo-estelar"
REPO_URL="https://github.com/estratix/sorteo-estelar.git"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if SSH key exists
    if [ ! -f ~/.ssh/id_rsa ]; then
        log_error "SSH private key not found at ~/.ssh/id_rsa"
        log_info "Please ensure your SSH key is properly configured"
        exit 1
    fi
    
    # Check required tools
    local tools=("ssh" "scp" "curl" "jq" "docker")
    for tool in "${tools[@]}"; do
        if ! command -v "$tool" &> /dev/null; then
            log_error "Required tool '$tool' is not installed"
            exit 1
        fi
    done
    
    log_success "Prerequisites check passed"
}

# Test SSH connection
test_ssh_connection() {
    log_info "Testing SSH connection to VPS..."
    
    if ssh -o ConnectTimeout=10 -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" 'echo "SSH connection successful"' &> /dev/null; then
        log_success "SSH connection established"
    else
        log_error "Failed to establish SSH connection"
        log_info "Please check your SSH configuration and VPS accessibility"
        exit 1
    fi
}

# Setup VPS environment
setup_vps_environment() {
    log_info "Setting up VPS environment..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== Updating system packages ==="
        sudo apt update && sudo apt upgrade -y
        
        echo "=== Installing required packages ==="
        sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
        
        echo "=== Installing Docker ==="
        if ! command -v docker &> /dev/null; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            sudo systemctl enable docker
            sudo systemctl start docker
            rm get-docker.sh
        else
            echo "Docker already installed"
        fi
        
        echo "=== Installing Docker Compose ==="
        if ! command -v docker-compose &> /dev/null; then
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        else
            echo "Docker Compose already installed"
        fi
        
        echo "=== Installing Node.js ==="
        if ! command -v node &> /dev/null; then
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt-get install -y nodejs
        else
            echo "Node.js already installed"
        fi
        
        echo "=== Installing Nginx ==="
        if ! command -v nginx &> /dev/null; then
            sudo apt install -y nginx
            sudo systemctl enable nginx
            sudo systemctl start nginx
        else
            echo "Nginx already installed"
        fi
        
        echo "=== Installing Certbot ==="
        if ! command -v certbot &> /dev/null; then
            sudo apt install -y certbot python3-certbot-nginx
        else
            echo "Certbot already installed"
        fi
        
        echo "=== Creating project directories ==="
        sudo mkdir -p /opt/sorteo-estelar/{data,logs,backups,ssl,monitoring}
        sudo chown -R admin:admin /opt/sorteo-estelar
        
        echo "=== Configuring firewall ==="
        sudo ufw --force reset
        sudo ufw default deny incoming
        sudo ufw default allow outgoing
        sudo ufw allow 2222/tcp comment "SSH"
        sudo ufw allow 80/tcp comment "HTTP"
        sudo ufw allow 443/tcp comment "HTTPS"
        sudo ufw allow 3000/tcp comment "Dokploy"
        sudo ufw allow 9090/tcp comment "Prometheus"
        sudo ufw allow 3001/tcp comment "Grafana"
        sudo ufw --force enable
        
        echo "✅ VPS environment setup completed"
EOF
    
    log_success "VPS environment configured"
}

# Install Dokploy
install_dokploy() {
    log_info "Installing Dokploy..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
        set -euo pipefail
        
        cd /opt/sorteo-estelar
        
        echo "=== Creating Dokploy configuration ==="
        cat > docker-compose.dokploy.yml <<COMPOSE_EOF
version: "3.8"
services:
  dokploy:
    image: dokploy/dokploy:latest
    container_name: dokploy
    restart: unless-stopped
    ports:
      - "$DOKPLOY_PORT:3000"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - dokploy_data:/app/data
      - ./ssl:/app/ssl
    environment:
      - NODE_ENV=production
      - DATABASE_URL=sqlite:///app/data/dokploy.db
      - DOMAIN=$DOMAIN
      - SSL_ENABLED=true
    networks:
      - dokploy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  dokploy_data:

networks:
  dokploy:
    driver: bridge
COMPOSE_EOF
        
        echo "=== Starting Dokploy ==="
        docker-compose -f docker-compose.dokploy.yml up -d
        
        echo "=== Waiting for Dokploy to start ==="
        sleep 60
        
        # Check if Dokploy is running
        if curl -f http://localhost:$DOKPLOY_PORT/health &> /dev/null; then
            echo "✅ Dokploy is running successfully"
        else
            echo "❌ Dokploy failed to start"
            docker-compose -f docker-compose.dokploy.yml logs
            exit 1
        fi
EOF
    
    log_success "Dokploy installed and running"
}

# Configure SSL certificates
setup_ssl_certificates() {
    log_info "Setting up SSL certificates..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
        set -euo pipefail
        
        echo "=== Configuring Nginx for domain verification ==="
        sudo tee /etc/nginx/sites-available/sorteo-estelar > /dev/null <<NGINX_EOF
server {
    listen 80;
    server_name $DOMAIN $STAGING_DOMAIN $MONITORING_DOMAIN;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
NGINX_EOF
        
        sudo ln -sf /etc/nginx/sites-available/sorteo-estelar /etc/nginx/sites-enabled/
        sudo rm -f /etc/nginx/sites-enabled/default
        sudo nginx -t
        sudo systemctl reload nginx
        
        echo "=== Obtaining SSL certificates ==="
        sudo certbot certonly --webroot -w /var/www/html -d $DOMAIN -d $STAGING_DOMAIN -d $MONITORING_DOMAIN --non-interactive --agree-tos --email <EMAIL>
        
        echo "=== Setting up SSL auto-renewal ==="
        (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
        
        echo "✅ SSL certificates configured"
EOF
    
    log_success "SSL certificates setup completed"
}

# Deploy application
deploy_application() {
    log_info "Deploying Sorteo Estelar application..."
    
    # Copy deployment files to VPS
    scp -P "$VPS_PORT" -r ./dokploy.config.json "$VPS_USER@$VPS_HOST:/opt/sorteo-estelar/"
    scp -P "$VPS_PORT" -r ./Dockerfile "$VPS_USER@$VPS_HOST:/opt/sorteo-estelar/"
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << EOF
        set -euo pipefail
        
        cd /opt/sorteo-estelar
        
        echo "=== Cloning repository ==="
        if [ ! -d "sorteo-estelar-repo" ]; then
            git clone $REPO_URL sorteo-estelar-repo
        else
            cd sorteo-estelar-repo
            git fetch origin
            git checkout main
            git pull origin main
            cd ..
        fi
        
        echo "=== Creating application docker-compose ==="
        cat > docker-compose.app.yml <<APP_COMPOSE_EOF
version: "3.8"
services:
  frontend:
    build:
      context: ./sorteo-estelar-repo
      dockerfile: Dockerfile
    container_name: sorteo-estelar-frontend
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      - NODE_ENV=production
      - VITE_API_URL=https://api.sorteoestelar.com
      - VITE_WS_URL=wss://api.sorteoestelar.com
    networks:
      - sorteo-estelar
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15
    container_name: sorteo-estelar-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=sorteo_estelar
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=\${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - sorteo-estelar
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7-alpine
    container_name: sorteo-estelar-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    networks:
      - sorteo-estelar
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:

networks:
  sorteo-estelar:
    driver: bridge
APP_COMPOSE_EOF
        
        echo "=== Creating environment file ==="
        cat > .env <<ENV_EOF
DB_PASSWORD=\${DB_PASSWORD:-sorteo_secure_password_2024}
REDIS_URL=redis://redis:6379
DATABASE_URL=postgresql://postgres:\${DB_PASSWORD}@postgres:5432/sorteo_estelar
ENV_EOF
        
        echo "=== Building and starting application ==="
        docker-compose -f docker-compose.app.yml up -d --build
        
        echo "=== Waiting for services to start ==="
        sleep 60
        
        # Check service health
        echo "=== Checking service health ==="
        docker-compose -f docker-compose.app.yml ps
        
        if docker ps | grep -q "sorteo-estelar-frontend"; then
            echo "✅ Frontend service is running"
        else
            echo "❌ Frontend service failed to start"
            docker-compose -f docker-compose.app.yml logs frontend
        fi
        
        if docker ps | grep -q "sorteo-estelar-postgres"; then
            echo "✅ PostgreSQL service is running"
        else
            echo "❌ PostgreSQL service failed to start"
            docker-compose -f docker-compose.app.yml logs postgres
        fi
        
        if docker ps | grep -q "sorteo-estelar-redis"; then
            echo "✅ Redis service is running"
        else
            echo "❌ Redis service failed to start"
            docker-compose -f docker-compose.app.yml logs redis
        fi
EOF
    
    log_success "Application deployed successfully"
}

# Configure reverse proxy
setup_reverse_proxy() {
    log_info "Configuring reverse proxy..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== Configuring Nginx reverse proxy ==="
        sudo tee /etc/nginx/sites-available/sorteo-estelar > /dev/null <<NGINX_EOF
# Main production site
server {
    listen 443 ssl http2;
    server_name www.sorteoestelar.com;
    
    ssl_certificate /etc/letsencrypt/live/www.sorteoestelar.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.sorteoestelar.com/privkey.pem;
    
    # SSL configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    location /health {
        proxy_pass http://localhost:8080/health;
        access_log off;
    }
}

# Staging site
server {
    listen 443 ssl http2;
    server_name staging.sorteoestelar.com;
    
    ssl_certificate /etc/letsencrypt/live/www.sorteoestelar.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.sorteoestelar.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:8081;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Monitoring site
server {
    listen 443 ssl http2;
    server_name monitoring.sorteoestelar.com;
    
    ssl_certificate /etc/letsencrypt/live/www.sorteoestelar.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.sorteoestelar.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Dokploy admin
server {
    listen 443 ssl http2;
    server_name dokploy.sorteoestelar.com;
    
    ssl_certificate /etc/letsencrypt/live/www.sorteoestelar.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.sorteoestelar.com/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# HTTP redirect
server {
    listen 80;
    server_name www.sorteoestelar.com staging.sorteoestelar.com monitoring.sorteoestelar.com dokploy.sorteoestelar.com;
    
    location /.well-known/acme-challenge/ {
        root /var/www/html;
    }
    
    location / {
        return 301 https://$server_name$request_uri;
    }
}
NGINX_EOF
        
        sudo nginx -t
        sudo systemctl reload nginx
        
        echo "✅ Reverse proxy configured"
EOF
    
    log_success "Reverse proxy setup completed"
}

# Setup monitoring
setup_monitoring() {
    log_info "Setting up monitoring stack..."
    
    ssh -p "$VPS_PORT" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        cd /opt/sorteo-estelar/monitoring
        
        echo "=== Creating monitoring docker-compose ==="
        cat > docker-compose.monitoring.yml <<MONITORING_EOF
version: "3.8"
services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - monitoring

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_SERVER_DOMAIN=monitoring.sorteoestelar.com
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - monitoring

  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - monitoring

volumes:
  prometheus_data:
  grafana_data:

networks:
  monitoring:
    driver: bridge
MONITORING_EOF
        
        echo "=== Creating Prometheus configuration ==="
        cat > prometheus.yml <<PROMETHEUS_EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  - job_name: 'sorteo-estelar-frontend'
    static_configs:
      - targets: ['host.docker.internal:8080']
    metrics_path: '/metrics'

  - job_name: 'dokploy'
    static_configs:
      - targets: ['host.docker.internal:3000']
    metrics_path: '/metrics'
PROMETHEUS_EOF
        
        echo "=== Starting monitoring stack ==="
        docker-compose -f docker-compose.monitoring.yml up -d
        
        echo "✅ Monitoring stack deployed"
EOF
    
    log_success "Monitoring setup completed"
}

# Verify deployment
verify_deployment() {
    log_info "Verifying deployment..."
    
    # Test main domain
    if curl -f -s "https://$DOMAIN/health" > /dev/null; then
        log_success "Main domain is responding: https://$DOMAIN"
    else
        log_warning "Main domain is not responding: https://$DOMAIN"
    fi
    
    # Test Dokploy
    if curl -f -s "http://$VPS_HOST:$DOKPLOY_PORT/health" > /dev/null; then
        log_success "Dokploy is accessible: http://$VPS_HOST:$DOKPLOY_PORT"
    else
        log_warning "Dokploy is not accessible"
    fi
    
    # Test monitoring
    if curl -f -s "http://$VPS_HOST:9090" > /dev/null; then
        log_success "Prometheus is accessible: http://$VPS_HOST:9090"
    else
        log_warning "Prometheus is not accessible"
    fi
    
    if curl -f -s "http://$VPS_HOST:3001" > /dev/null; then
        log_success "Grafana is accessible: http://$VPS_HOST:3001"
    else
        log_warning "Grafana is not accessible"
    fi
    
    # Show deployment summary
    log_info "Deployment Summary:"
    echo "  🌐 Main Site: https://$DOMAIN"
    echo "  🚀 Staging: https://$STAGING_DOMAIN"
    echo "  📊 Monitoring: https://$MONITORING_DOMAIN"
    echo "  ⚙️  Dokploy: http://$VPS_HOST:$DOKPLOY_PORT"
    echo "  📈 Prometheus: http://$VPS_HOST:9090"
    echo "  📊 Grafana: http://$VPS_HOST:3001"
}

# Main deployment function
main() {
    log_info "Starting Sorteo Estelar VPS deployment..."
    
    check_prerequisites
    test_ssh_connection
    setup_vps_environment
    install_dokploy
    setup_ssl_certificates
    deploy_application
    setup_reverse_proxy
    setup_monitoring
    verify_deployment
    
    log_success "🎉 Sorteo Estelar deployment completed successfully!"
    log_info "Next steps:"
    echo "  1. Configure DNS records to point to $VPS_HOST"
    echo "  2. Access Dokploy at http://$VPS_HOST:$DOKPLOY_PORT to complete setup"
    echo "  3. Configure monitoring dashboards in Grafana"
    echo "  4. Set up automated backups"
    echo "  5. Configure CI/CD webhooks"
}

# Handle script arguments
case "${1:-}" in
    "prerequisites")
        check_prerequisites
        ;;
    "ssh-test")
        test_ssh_connection
        ;;
    "vps-setup")
        setup_vps_environment
        ;;
    "dokploy")
        install_dokploy
        ;;
    "ssl")
        setup_ssl_certificates
        ;;
    "deploy")
        deploy_application
        ;;
    "proxy")
        setup_reverse_proxy
        ;;
    "monitoring")
        setup_monitoring
        ;;
    "verify")
        verify_deployment
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [prerequisites|ssh-test|vps-setup|dokploy|ssl|deploy|proxy|monitoring|verify]"
        echo "  Run without arguments to execute full deployment"
        exit 1
        ;;
esac