---
description: "Guides the creation and refinement of the ESTRATIX Command Officer Organizational Chart, illustrating the hierarchy and classification (Primary/Support) of Command Offices."
---

# Workflow: Update ESTRATIX Command Officer Organizational Chart

This workflow details the process for generating or updating the ESTRATIX Command Officer organizational chart. The diagram visually represents the command hierarchy and classifies officers by their primary or support roles.

**Objective:** To produce an up-to-date Mermaid diagram (`.mmd`) in `docs/diagrams/Command_Officers_Organizational_Chart.mmd` that accurately reflects the Command Officer structure defined in `docs/matrices/organization_matrix.md`.

**Relies on:**
-   `docs/matrices/organization_matrix.md` (for Command Officer list, hierarchy, and type)

**Key Output:**
-   An updated `docs/diagrams/Command_Officers_Organizational_Chart.mmd` file.

---

## Steps:

1.  **Data Ingestion & Preparation:**
    *   **Action (Automated/Agent-Assisted):**
        1.  Read `docs/matrices/organization_matrix.md` into a structured format (e.g., list of dictionaries, DataFrame). Extract Officer ID, Acronym, Officer Name, and Type (Primary/Support).
    *   **Tooling:** Scripting (Python with CSV/Markdown parsing libraries), data manipulation libraries.

2.  **Diagram Generation Logic (Mermaid Syntax):**
    *   **Action (Automated/Agent-Assisted):** Based on the ingested data, construct the Mermaid graph definition.
    *   **Guidance for Mermaid Structure:**
        *   Start with a `graph TD` (Top-Down) definition.
        *   Define the CEO as the root node (e.g., `ORG_O001["CEO (Chief Executive Officer)"]`).
        *   Create two main subgraphs: "Primary Activities Command Offices" and "Support Activities Command Offices".
        *   Iterate through the structured data from `organization_matrix.md`:
            *   For each Command Officer (excluding CEO if handled as root):
                *   Create a node (e.g., `ORG_O002["CPO (Chief Process Officer)"]`).
                *   Place the node within the appropriate "Primary" or "Support" subgraph based on their 'Type'.
                *   Establish a link from the CEO to each Command Officer (e.g., `ORG_O001 --> ORG_O002`). (Adjust if a more complex hierarchy than direct reports to CEO is defined in the matrix for some officers).
        *   **Example Snippet:**
            ```mermaid
            graph TD
                CEO(ORG_O001 CEO - Chief Executive Officer)

                subgraph Primary Activities Command Offices
                    direction LR
                    ORG_O002["CPO (Chief Process Officer)"]
                    ORG_O004["COO (Chief Operations Officer)"]
                    %% ... other primary officers
                end

                subgraph Support Activities Command Offices
                    direction LR
                    ORG_O006["CAO (Chief Administrative Officer)"]
                    ORG_O007["CDO (Chief Data Officer)"]
                    %% ... other support officers
                end

                CEO --> ORG_O002
                CEO --> ORG_O004
                CEO --> ORG_O006
                CEO --> ORG_O007
                %% ... other links from CEO
            ```
        *   Style nodes or links as needed for clarity (e.g., different shapes or colors for Primary vs. Support, though subgraph grouping should be primary). Consider using `classDef` for styling.
    *   **Tooling:** Scripting to generate Mermaid text.

3.  **Write/Update Diagram File:**
    *   **Action (Automated/Agent-Assisted):**
        1.  Ensure the directory `docs/diagrams/` exists.
        2.  Write the generated Mermaid syntax to `docs/diagrams/Command_Officers_Organizational_Chart.mmd`. Overwrite the existing file if it exists.
    *   **Tooling:** File system operations.

4.  **Review and Validation (Manual/Visual):**
    *   **Action (User):**
        1.  Open `Command_Officers_Organizational_Chart.mmd` using a Mermaid-compatible viewer.
        2.  Visually inspect the diagram for accuracy, completeness, and clarity, especially the Primary/Support grouping.
        3.  Compare against `organization_matrix.md`.
    *   **Input:** Visual feedback.
    *   **Output:** Confirmation or issues to refine in Step 2.

5.  **Iteration (If Necessary):**
    *   **Action:** If the diagram is not satisfactory, revisit Step 2. Repeat Step 3 and 4.

6.  **Finalization & Communication:**
    *   **Action:** Once approved, announce its update.
    *   **Consideration:** This workflow should be triggered whenever significant changes occur in `organization_matrix.md` (e.g., new officer, change in type).

---
**Next Steps:**
-   Implement the initial version of the script/tooling for Step 1, 2, and 3.
-   Execute this workflow to generate the first `Command_Officers_Organizational_Chart.mmd`.
