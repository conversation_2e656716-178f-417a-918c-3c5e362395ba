version: '3.8'

services:
  luxcrafts-app:
    # The image is specified via the DOCKER_IMAGE_TAG environment variable
    image: ${DOCKER_IMAGE_TAG}
    container_name: luxcrafts-platform
    restart: unless-stopped
    networks:
      - dokploy-network
    labels:
      # Traefik labels for routing, TLS, and service definition
      - "traefik.enable=true"
      - "traefik.docker.network=dokploy-network"
      
      # Router definition for HTTPS
      - "traefik.http.routers.luxcrafts.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.luxcrafts.entrypoints=websecure"
      - "traefik.http.routers.luxcrafts.tls=true"
      - "traefik.http.routers.luxcrafts.tls.certresolver=letsencrypt"
      
      # Optional: Redirect HTTP to HTTPS
      - "traefik.http.routers.luxcrafts-http.rule=Host(`${DOMAIN_NAME}`)"
      - "traefik.http.routers.luxcrafts-http.entrypoints=web"
      - "traefik.http.routers.luxcrafts-http.middlewares=redirect-to-https"
      
      # Middleware for the redirect
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.permanent=true"
      
      # Service definition
      - "traefik.http.services.luxcrafts.loadbalancer.server.port=80"

networks:
  dokploy-network:
    external: true
    name: dokploy-network
