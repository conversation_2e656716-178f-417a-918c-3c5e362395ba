import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { v4 as uuidv4 } from 'uuid';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';

export interface WebSocketClient {
  id: string;
  socket: WebSocket;
  userId?: string;
  organizationId?: string;
  subscriptions: Set<string>;
  lastPing: Date;
  isAlive: boolean;
}

export interface WebSocketMessage {
  type: 'subscribe' | 'unsubscribe' | 'ping' | 'pong' | 'notification' | 'agent_status' | 'workflow_status' | 'task_update';
  payload?: any;
  timestamp: Date;
}

export class WebSocketService {
  private wss?: WebSocketServer;
  private clients: Map<string, WebSocketClient> = new Map();
  private subscriptions: Map<string, Set<string>> = new Map(); // topic -> client IDs
  private isInitialized = false;
  private heartbeatInterval?: NodeJS.Timeout;

  async initialize(server?: any): Promise<void> {
    try {
      logger.info('Initializing WebSocket Service...');
      
      // Create WebSocket server
      this.wss = new WebSocketServer({
        port: environment.websocket.port,
        path: environment.websocket.path
      });
      
      // Set up connection handling
      this.wss.on('connection', (socket: WebSocket, request: IncomingMessage) => {
        this.handleConnection(socket, request);
      });
      
      // Start heartbeat
      this.startHeartbeat();
      
      this.isInitialized = true;
      logger.info(`WebSocket Service initialized on port ${environment.websocket.port}`);
    } catch (error) {
      logger.error(error, 'Failed to initialize WebSocket Service');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up WebSocket Service...');
      
      // Stop heartbeat
      if (this.heartbeatInterval) {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = undefined;
      }
      
      // Close all client connections
      for (const client of this.clients.values()) {
        client.socket.close(1000, 'Server shutdown');
      }
      
      // Close WebSocket server
      if (this.wss) {
        this.wss.close();
      }
      
      this.clients.clear();
      this.subscriptions.clear();
      this.isInitialized = false;
      
      logger.info('WebSocket Service cleanup completed');
    } catch (error) {
      logger.error(error, 'Error during WebSocket Service cleanup');
      throw error;
    }
  }

  async getStatus(): Promise<string> {
    if (!this.isInitialized || !this.wss) {
      return 'initializing';
    }
    
    const connectedClients = this.clients.size;
    
    if (connectedClients > environment.websocket.maxConnections * 0.9) {
      return 'overloaded';
    }
    
    return 'healthy';
  }

  async getMetrics(): Promise<{ connectedClients: number; totalSubscriptions: number; activeTopics: number }> {
    const connectedClients = this.clients.size;
    const totalSubscriptions = Array.from(this.clients.values())
      .reduce((sum, client) => sum + client.subscriptions.size, 0);
    const activeTopics = this.subscriptions.size;
    
    return {
      connectedClients,
      totalSubscriptions,
      activeTopics
    };
  }

  // Broadcast message to all clients subscribed to a topic
  broadcast(topic: string, message: any): void {
    const clientIds = this.subscriptions.get(topic);
    if (!clientIds) {
      return;
    }
    
    const wsMessage: WebSocketMessage = {
      type: 'notification',
      payload: {
        topic,
        data: message
      },
      timestamp: new Date()
    };
    
    for (const clientId of clientIds) {
      const client = this.clients.get(clientId);
      if (client && client.socket.readyState === WebSocket.OPEN) {
        this.sendMessage(client, wsMessage);
      }
    }
  }

  // Send message to specific client
  sendToClient(clientId: string, message: any): void {
    const client = this.clients.get(clientId);
    if (!client || client.socket.readyState !== WebSocket.OPEN) {
      return;
    }
    
    const wsMessage: WebSocketMessage = {
      type: 'notification',
      payload: message,
      timestamp: new Date()
    };
    
    this.sendMessage(client, wsMessage);
  }

  // Send agent status update
  broadcastAgentStatus(agentId: string, status: string, data?: any): void {
    this.broadcast(`agent:${agentId}`, {
      type: 'agent_status',
      agentId,
      status,
      data,
      timestamp: new Date()
    });
    
    // Also broadcast to general agent updates topic
    this.broadcast('agents', {
      type: 'agent_status',
      agentId,
      status,
      data,
      timestamp: new Date()
    });
  }

  // Send workflow status update
  broadcastWorkflowStatus(workflowId: string, status: string, data?: any): void {
    this.broadcast(`workflow:${workflowId}`, {
      type: 'workflow_status',
      workflowId,
      status,
      data,
      timestamp: new Date()
    });
    
    // Also broadcast to general workflow updates topic
    this.broadcast('workflows', {
      type: 'workflow_status',
      workflowId,
      status,
      data,
      timestamp: new Date()
    });
  }

  // Send task update
  broadcastTaskUpdate(taskId: string, status: string, data?: any): void {
    this.broadcast(`task:${taskId}`, {
      type: 'task_update',
      taskId,
      status,
      data,
      timestamp: new Date()
    });
    
    // Also broadcast to general task updates topic
    this.broadcast('tasks', {
      type: 'task_update',
      taskId,
      status,
      data,
      timestamp: new Date()
    });
  }

  private handleConnection(socket: WebSocket, request: IncomingMessage): void {
    const clientId = uuidv4();
    
    const client: WebSocketClient = {
      id: clientId,
      socket,
      subscriptions: new Set(),
      lastPing: new Date(),
      isAlive: true
    };
    
    this.clients.set(clientId, client);
    
    logger.info(`WebSocket client connected: ${clientId}`);
    
    // Set up message handling
    socket.on('message', (data: Buffer) => {
      this.handleMessage(client, data);
    });
    
    // Set up close handling
    socket.on('close', (code: number, reason: Buffer) => {
      this.handleDisconnection(client, code, reason.toString());
    });
    
    // Set up error handling
    socket.on('error', (error: Error) => {
      logger.error(error, `WebSocket error for client ${clientId}`);
    });
    
    // Set up pong handling for heartbeat
    socket.on('pong', () => {
      client.isAlive = true;
      client.lastPing = new Date();
    });
    
    // Send welcome message
    this.sendMessage(client, {
      type: 'notification',
      payload: {
        message: 'Connected to ESTRATIX Agent Orchestration',
        clientId
      },
      timestamp: new Date()
    });
  }

  private handleMessage(client: WebSocketClient, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString()) as WebSocketMessage;
      
      switch (message.type) {
        case 'subscribe':
          this.handleSubscribe(client, message.payload.topic);
          break;
        case 'unsubscribe':
          this.handleUnsubscribe(client, message.payload.topic);
          break;
        case 'ping':
          this.handlePing(client);
          break;
        default:
          logger.warn(`Unknown message type: ${message.type} from client ${client.id}`);
      }
    } catch (error) {
      logger.error(error, `Error handling message from client ${client.id}`);
    }
  }

  private handleSubscribe(client: WebSocketClient, topic: string): void {
    if (!topic) {
      return;
    }
    
    client.subscriptions.add(topic);
    
    if (!this.subscriptions.has(topic)) {
      this.subscriptions.set(topic, new Set());
    }
    
    this.subscriptions.get(topic)!.add(client.id);
    
    logger.debug(`Client ${client.id} subscribed to topic: ${topic}`);
    
    // Send confirmation
    this.sendMessage(client, {
      type: 'notification',
      payload: {
        message: `Subscribed to topic: ${topic}`
      },
      timestamp: new Date()
    });
  }

  private handleUnsubscribe(client: WebSocketClient, topic: string): void {
    if (!topic) {
      return;
    }
    
    client.subscriptions.delete(topic);
    
    const topicClients = this.subscriptions.get(topic);
    if (topicClients) {
      topicClients.delete(client.id);
      
      // Remove topic if no clients are subscribed
      if (topicClients.size === 0) {
        this.subscriptions.delete(topic);
      }
    }
    
    logger.debug(`Client ${client.id} unsubscribed from topic: ${topic}`);
    
    // Send confirmation
    this.sendMessage(client, {
      type: 'notification',
      payload: {
        message: `Unsubscribed from topic: ${topic}`
      },
      timestamp: new Date()
    });
  }

  private handlePing(client: WebSocketClient): void {
    client.lastPing = new Date();
    client.isAlive = true;
    
    this.sendMessage(client, {
      type: 'pong',
      timestamp: new Date()
    });
  }

  private handleDisconnection(client: WebSocketClient, code: number, reason: string): void {
    logger.info(`WebSocket client disconnected: ${client.id} (code: ${code}, reason: ${reason})`);
    
    // Remove client from all subscriptions
    for (const topic of client.subscriptions) {
      const topicClients = this.subscriptions.get(topic);
      if (topicClients) {
        topicClients.delete(client.id);
        
        // Remove topic if no clients are subscribed
        if (topicClients.size === 0) {
          this.subscriptions.delete(topic);
        }
      }
    }
    
    // Remove client
    this.clients.delete(client.id);
  }

  private sendMessage(client: WebSocketClient, message: WebSocketMessage): void {
    try {
      if (client.socket.readyState === WebSocket.OPEN) {
        client.socket.send(JSON.stringify(message));
      }
    } catch (error) {
      logger.error(error, `Error sending message to client ${client.id}`);
    }
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      for (const client of this.clients.values()) {
        if (!client.isAlive) {
          // Client didn't respond to ping, terminate connection
          client.socket.terminate();
          continue;
        }
        
        // Send ping
        client.isAlive = false;
        if (client.socket.readyState === WebSocket.OPEN) {
          client.socket.ping();
        }
      }
    }, environment.websocket.heartbeatInterval);
  }
}