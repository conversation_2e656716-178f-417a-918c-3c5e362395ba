import { logger } from '../utils/logger';
import { config } from '../config/environment';

interface Notification {
  id: string;
  type: 'project_created' | 'project_updated' | 'task_assigned' | 'deadline_approaching' | 'milestone_completed';
  title: string;
  message: string;
  userId: string;
  projectId?: string;
  taskId?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  read: boolean;
  createdAt: Date;
  readAt?: Date;
  metadata?: Record<string, any>;
}

interface NotificationTemplate {
  type: string;
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

class NotificationService {
  private notifications: Map<string, Notification> = new Map();
  private templates: Map<string, NotificationTemplate> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Notification Service...');
      
      // Initialize notification templates
      this.initializeTemplates();
      
      // Initialize queue connections
      if (config.ENABLE_NOTIFICATIONS) {
        await this.initializeQueue();
      }
      
      this.initialized = true;
      logger.info('Notification Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Notification Service:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Notification Service...');
    // Cleanup queue connections, etc.
    this.initialized = false;
  }

  async createNotification(data: {
    type: Notification['type'];
    userId: string;
    projectId?: string;
    taskId?: string;
    customTitle?: string;
    customMessage?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    metadata?: Record<string, any>;
  }): Promise<Notification> {
    const id = this.generateId();
    const template = this.templates.get(data.type);
    
    if (!template) {
      throw new Error(`Unknown notification type: ${data.type}`);
    }
    
    const notification: Notification = {
      id,
      type: data.type,
      title: data.customTitle || template.title,
      message: data.customMessage || template.message,
      userId: data.userId,
      projectId: data.projectId,
      taskId: data.taskId,
      priority: data.priority || template.priority,
      read: false,
      createdAt: new Date(),
      metadata: data.metadata
    };
    
    this.notifications.set(id, notification);
    
    // Send notification through various channels
    await this.sendNotification(notification);
    
    logger.info(`Notification created: ${id}`, {
      notificationId: id,
      type: notification.type,
      userId: notification.userId
    });
    
    return notification;
  }

  async getUserNotifications(
    userId: string,
    options: {
      page?: number;
      limit?: number;
      unreadOnly?: boolean;
      type?: string;
    } = {}
  ): Promise<{
    notifications: Notification[];
    total: number;
    unreadCount: number;
  }> {
    let userNotifications = Array.from(this.notifications.values())
      .filter(n => n.userId === userId);
    
    // Apply filters
    if (options.unreadOnly) {
      userNotifications = userNotifications.filter(n => !n.read);
    }
    
    if (options.type) {
      userNotifications = userNotifications.filter(n => n.type === options.type);
    }
    
    // Sort by creation date (newest first)
    userNotifications.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
    
    const total = userNotifications.length;
    const unreadCount = Array.from(this.notifications.values())
      .filter(n => n.userId === userId && !n.read).length;
    
    // Apply pagination
    const page = options.page || 1;
    const limit = options.limit || 20;
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const notifications = userNotifications.slice(startIndex, endIndex);
    
    return {
      notifications,
      total,
      unreadCount
    };
  }

  async markAsRead(notificationId: string, userId: string): Promise<boolean> {
    const notification = this.notifications.get(notificationId);
    
    if (!notification || notification.userId !== userId) {
      return false;
    }
    
    notification.read = true;
    notification.readAt = new Date();
    
    this.notifications.set(notificationId, notification);
    
    logger.info(`Notification marked as read: ${notificationId}`, {
      notificationId,
      userId
    });
    
    return true;
  }

  async markAllAsRead(userId: string): Promise<number> {
    let markedCount = 0;
    
    for (const notification of this.notifications.values()) {
      if (notification.userId === userId && !notification.read) {
        notification.read = true;
        notification.readAt = new Date();
        this.notifications.set(notification.id, notification);
        markedCount++;
      }
    }
    
    logger.info(`Marked ${markedCount} notifications as read for user ${userId}`);
    
    return markedCount;
  }

  async deleteNotification(notificationId: string, userId: string): Promise<boolean> {
    const notification = this.notifications.get(notificationId);
    
    if (!notification || notification.userId !== userId) {
      return false;
    }
    
    this.notifications.delete(notificationId);
    
    logger.info(`Notification deleted: ${notificationId}`, {
      notificationId,
      userId
    });
    
    return true;
  }

  async getNotificationStats(userId: string): Promise<{
    total: number;
    unread: number;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    const userNotifications = Array.from(this.notifications.values())
      .filter(n => n.userId === userId);
    
    const total = userNotifications.length;
    const unread = userNotifications.filter(n => !n.read).length;
    
    const byType: Record<string, number> = {};
    const byPriority: Record<string, number> = {};
    
    for (const notification of userNotifications) {
      byType[notification.type] = (byType[notification.type] || 0) + 1;
      byPriority[notification.priority] = (byPriority[notification.priority] || 0) + 1;
    }
    
    return {
      total,
      unread,
      byType,
      byPriority
    };
  }

  // Project-specific notification methods
  async notifyProjectCreated(projectId: string, projectName: string, ownerId: string): Promise<void> {
    await this.createNotification({
      type: 'project_created',
      userId: ownerId,
      projectId,
      customTitle: 'Project Created',
      customMessage: `Your project "${projectName}" has been created successfully.`,
      priority: 'medium',
      metadata: { projectName }
    });
  }

  async notifyProjectUpdated(projectId: string, projectName: string, userId: string): Promise<void> {
    await this.createNotification({
      type: 'project_updated',
      userId,
      projectId,
      customTitle: 'Project Updated',
      customMessage: `Project "${projectName}" has been updated.`,
      priority: 'low',
      metadata: { projectName }
    });
  }

  async notifyTaskAssigned(taskId: string, taskName: string, assigneeId: string, projectId?: string): Promise<void> {
    await this.createNotification({
      type: 'task_assigned',
      userId: assigneeId,
      taskId,
      projectId,
      customTitle: 'Task Assigned',
      customMessage: `You have been assigned to task "${taskName}".`,
      priority: 'medium',
      metadata: { taskName }
    });
  }

  async notifyDeadlineApproaching(taskId: string, taskName: string, userId: string, daysLeft: number): Promise<void> {
    await this.createNotification({
      type: 'deadline_approaching',
      userId,
      taskId,
      customTitle: 'Deadline Approaching',
      customMessage: `Task "${taskName}" is due in ${daysLeft} day(s).`,
      priority: daysLeft <= 1 ? 'urgent' : 'high',
      metadata: { taskName, daysLeft }
    });
  }

  async notifyMilestoneCompleted(projectId: string, milestoneName: string, userId: string): Promise<void> {
    await this.createNotification({
      type: 'milestone_completed',
      userId,
      projectId,
      customTitle: 'Milestone Completed',
      customMessage: `Milestone "${milestoneName}" has been completed.`,
      priority: 'medium',
      metadata: { milestoneName }
    });
  }

  private initializeTemplates(): void {
    const templates: NotificationTemplate[] = [
      {
        type: 'project_created',
        title: 'Project Created',
        message: 'A new project has been created.',
        priority: 'medium'
      },
      {
        type: 'project_updated',
        title: 'Project Updated',
        message: 'A project has been updated.',
        priority: 'low'
      },
      {
        type: 'task_assigned',
        title: 'Task Assigned',
        message: 'You have been assigned a new task.',
        priority: 'medium'
      },
      {
        type: 'deadline_approaching',
        title: 'Deadline Approaching',
        message: 'A task deadline is approaching.',
        priority: 'high'
      },
      {
        type: 'milestone_completed',
        title: 'Milestone Completed',
        message: 'A project milestone has been completed.',
        priority: 'medium'
      }
    ];
    
    for (const template of templates) {
      this.templates.set(template.type, template);
    }
  }

  private async initializeQueue(): Promise<void> {
    // Initialize Redis queue for notification processing
    // This would connect to Redis and set up job processing
    logger.info('Notification queue initialized');
  }

  private async sendNotification(notification: Notification): Promise<void> {
    try {
      // Send through various channels based on user preferences
      // This could include email, push notifications, webhooks, etc.
      
      if (config.ENABLE_NOTIFICATIONS) {
        // Mock sending notification
        logger.info(`Sending notification: ${notification.id}`, {
          type: notification.type,
          userId: notification.userId,
          priority: notification.priority
        });
      }
    } catch (error) {
      logger.error('Failed to send notification:', error);
    }
  }

  private generateId(): string {
    return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

export const notificationService = new NotificationService();