import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  ChevronDown, ChevronUp, Search, HelpCircle, 
  Shield, CreditCard, Coins, Users, Award,
  Globe, Smartphone, Lock, TrendingUp, MessageCircle
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
}

interface FAQCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  count: number;
}

const FAQ: React.FC = () => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  const categories: FAQCategory[] = [
    {
      id: 'all',
      name: 'Todas las Preguntas',
      icon: <HelpCircle className="w-5 h-5" />,
      color: 'purple',
      count: 24
    },
    {
      id: 'general',
      name: 'General',
      icon: <Globe className="w-5 h-5" />,
      color: 'blue',
      count: 6
    },
    {
      id: 'payments',
      name: 'Pagos',
      icon: <CreditCard className="w-5 h-5" />,
      color: 'green',
      count: 5
    },
    {
      id: 'security',
      name: 'Seguridad',
      icon: <Shield className="w-5 h-5" />,
      color: 'red',
      count: 4
    },
    {
      id: 'crypto',
      name: 'Criptomonedas',
      icon: <Coins className="w-5 h-5" />,
      color: 'amber',
      count: 4
    },
    {
      id: 'account',
      name: 'Cuenta',
      icon: <Users className="w-5 h-5" />,
      color: 'indigo',
      count: 3
    },
    {
      id: 'prizes',
      name: 'Premios',
      icon: <Award className="w-5 h-5" />,
      color: 'pink',
      count: 2
    }
  ];

  const faqItems: FAQItem[] = [
    // General
    {
      id: '1',
      question: '¿Qué es Sorteo Estelar?',
      answer: 'Sorteo Estelar es una plataforma revolucionaria de loterías digitales que utiliza tecnología blockchain para garantizar transparencia, seguridad y equidad en todos los sorteos. Ofrecemos una experiencia única que combina loterías tradicionales con las ventajas de Web3, incluyendo pagos con criptomonedas, smart contracts verificables y un ecosistema DeFi completo.',
      category: 'general',
      tags: ['plataforma', 'blockchain', 'lotería']
    },
    {
      id: '2',
      question: '¿Cómo funciona la tecnología blockchain en los sorteos?',
      answer: 'Utilizamos smart contracts auditados en blockchain para generar números aleatorios verificables y gestionar automáticamente la distribución de premios. Cada sorteo queda registrado permanentemente en la blockchain, permitiendo que cualquier persona pueda verificar la legitimidad y transparencia del proceso. Los algoritmos de generación aleatoria son públicos y auditables.',
      category: 'general',
      tags: ['blockchain', 'smart contracts', 'transparencia']
    },
    {
      id: '3',
      question: '¿En qué países está disponible Sorteo Estelar?',
      answer: 'Actualmente operamos en más de 25 países, incluyendo Estados Unidos, Canadá, Reino Unido, España, México, Brasil, Argentina, y muchos otros. Estamos en constante expansión y trabajamos para cumplir con las regulaciones locales de cada jurisdicción. Puedes verificar la disponibilidad en tu país durante el proceso de registro.',
      category: 'general',
      tags: ['países', 'disponibilidad', 'regulaciones']
    },
    {
      id: '4',
      question: '¿Qué tipos de sorteos ofrecen?',
      answer: 'Ofrecemos varios tipos de sorteos: Sorteos de 2-3 dígitos vinculados a loterías oficiales para mayor transparencia, sorteos de 4+ dígitos con generación aleatoria propia, sorteos especiales con premios únicos como NFTs y experiencias exclusivas, y sorteos DeFi donde puedes ganar tokens y participar en yield farming.',
      category: 'general',
      tags: ['tipos', 'sorteos', 'premios']
    },
    {
      id: '5',
      question: '¿Cómo puedo verificar que los sorteos son justos?',
      answer: 'Todos nuestros sorteos son completamente verificables. Puedes consultar los smart contracts en exploradores de blockchain como Etherscan, revisar el código fuente de nuestros algoritmos de generación aleatoria, verificar las transacciones de distribución de premios, y acceder a auditorías de seguridad realizadas por terceros independientes.',
      category: 'general',
      tags: ['verificación', 'transparencia', 'auditorías']
    },
    {
      id: '6',
      question: '¿Hay una aplicación móvil disponible?',
      answer: 'Sí, tenemos aplicaciones nativas para iOS y Android que ofrecen todas las funcionalidades de la plataforma web. También nuestra aplicación web es completamente responsive y funciona perfectamente en dispositivos móviles. Las apps móviles incluyen notificaciones push para resultados de sorteos y funciones de wallet integradas.',
      category: 'general',
      tags: ['móvil', 'aplicación', 'iOS', 'Android']
    },

    // Payments
    {
      id: '7',
      question: '¿Qué métodos de pago aceptan?',
      answer: 'Aceptamos una amplia variedad de métodos de pago: Tarjetas de crédito y débito (Visa, Mastercard, American Express), transferencias bancarias tradicionales, servicios digitales como PayPal, Wise y Payoneer, y criptomonedas (Bitcoin, Ethereum, USDC, USDT) en múltiples redes blockchain.',
      category: 'payments',
      tags: ['métodos de pago', 'tarjetas', 'criptomonedas']
    },
    {
      id: '8',
      question: '¿Hay comisiones por los pagos?',
      answer: 'Las comisiones varían según el método de pago: Tarjetas de crédito: 2.9% + $0.30, transferencias bancarias: $5 fijo, PayPal: 3.4% + $0.30, criptomonedas: solo las fees de red (gas fees), Wise/Payoneer: 1.5% + $2. Siempre mostramos las comisiones claramente antes de confirmar cualquier transacción.',
      category: 'payments',
      tags: ['comisiones', 'fees', 'costos']
    },
    {
      id: '9',
      question: '¿Cuánto tiempo tardan los depósitos?',
      answer: 'Los tiempos de procesamiento dependen del método: Tarjetas de crédito/débito: instantáneo, PayPal: instantáneo, transferencias bancarias: 1-3 días hábiles, Wise/Payoneer: 1-2 días hábiles, criptomonedas: 1-30 minutos dependiendo de la red y congestión.',
      category: 'payments',
      tags: ['depósitos', 'tiempo', 'procesamiento']
    },
    {
      id: '10',
      question: '¿Cómo funcionan los retiros de ganancias?',
      answer: 'Los retiros se procesan al mismo método de pago utilizado para el depósito por seguridad. Para retiros grandes, ofrecemos transferencias bancarias directas. Los retiros en criptomonedas son instantáneos una vez confirmados. Todos los retiros requieren verificación de identidad para cumplir con regulaciones anti-lavado de dinero.',
      category: 'payments',
      tags: ['retiros', 'ganancias', 'verificación']
    },
    {
      id: '11',
      question: '¿Puedo cambiar mi método de pago preferido?',
      answer: 'Sí, puedes agregar, eliminar o modificar tus métodos de pago en cualquier momento desde tu panel de usuario. Para seguridad, algunos cambios pueden requerir verificación adicional. También puedes establecer límites de gasto y configurar métodos de pago automáticos para participaciones recurrentes.',
      category: 'payments',
      tags: ['métodos de pago', 'configuración', 'seguridad']
    },

    // Security
    {
      id: '12',
      question: '¿Qué medidas de seguridad implementan?',
      answer: 'Implementamos múltiples capas de seguridad: Encriptación AES-256 para todos los datos, autenticación de dos factores (2FA) obligatoria, almacenamiento en cold wallets para criptomonedas, auditorías de seguridad regulares por terceros, cumplimiento con estándares SOC 2 y ISO 27001, y monitoreo 24/7 de actividades sospechosas.',
      category: 'security',
      tags: ['seguridad', 'encriptación', '2FA']
    },
    {
      id: '13',
      question: '¿Cómo protegen mis datos personales?',
      answer: 'Cumplimos estrictamente con GDPR, CCPA y otras regulaciones de privacidad. Tus datos están encriptados en tránsito y en reposo, nunca compartimos información personal con terceros sin tu consentimiento, implementamos políticas de retención de datos limitadas, y tienes control total sobre tus datos con opciones de exportación y eliminación.',
      category: 'security',
      tags: ['privacidad', 'GDPR', 'datos personales']
    },
    {
      id: '14',
      question: '¿Qué hago si sospecho actividad fraudulenta en mi cuenta?',
      answer: 'Si detectas actividad sospechosa: Cambia tu contraseña inmediatamente, contacta a nuestro equipo de seguridad 24/7, revisa tu historial de transacciones, habilita alertas de seguridad adicionales. Tenemos sistemas de detección automática de fraude y un equipo especializado que responde en menos de 1 hora a reportes de seguridad.',
      category: 'security',
      tags: ['fraude', 'actividad sospechosa', 'soporte']
    },
    {
      id: '15',
      question: '¿Están asegurados los fondos de los usuarios?',
      answer: 'Sí, mantenemos seguros de responsabilidad civil y ciberseguridad que cubren los fondos de usuarios. Además, los fondos en criptomonedas se almacenan en cold wallets aseguradas, utilizamos custodios regulados para fondos fiat, y mantenemos reservas de liquidez para garantizar retiros inmediatos.',
      category: 'security',
      tags: ['seguros', 'fondos', 'protección']
    },

    // Crypto
    {
      id: '16',
      question: '¿Qué criptomonedas aceptan?',
      answer: 'Aceptamos las principales criptomonedas: Bitcoin (BTC), Ethereum (ETH), USD Coin (USDC), Tether (USDT), Binance Coin (BNB), Polygon (MATIC), y muchas otras. Soportamos múltiples redes incluyendo Ethereum, Polygon, Binance Smart Chain, y Arbitrum para minimizar las fees de transacción.',
      category: 'crypto',
      tags: ['criptomonedas', 'Bitcoin', 'Ethereum']
    },
    {
      id: '17',
      question: '¿Cómo conecto mi wallet de criptomonedas?',
      answer: 'Soportamos los wallets más populares: MetaMask, WalletConnect, Coinbase Wallet, Trust Wallet, y Ledger. Simplemente haz clic en "Conectar Wallet" y selecciona tu wallet preferido. La conexión es segura y nunca almacenamos tus claves privadas. También puedes desconectar tu wallet en cualquier momento.',
      category: 'crypto',
      tags: ['wallet', 'MetaMask', 'conexión']
    },
    {
      id: '18',
      question: '¿Qué son los gas fees y quién los paga?',
      answer: 'Los gas fees son comisiones de la red blockchain necesarias para procesar transacciones. En Sorteo Estelar, tú pagas los gas fees para depósitos desde tu wallet, nosotros cubrimos los gas fees para distribución de premios, utilizamos redes de bajo costo como Polygon para minimizar fees, y mostramos estimaciones de gas antes de cada transacción.',
      category: 'crypto',
      tags: ['gas fees', 'comisiones', 'blockchain']
    },
    {
      id: '19',
      question: '¿Puedo participar en DeFi con mis tokens?',
      answer: 'Sí, ofrecemos múltiples oportunidades DeFi: Staking de tokens nativos con APY competitivos, pools de liquidez para ganar fees de trading, yield farming con recompensas adicionales, governance tokens para votar en decisiones de la plataforma, y NFT marketplace para coleccionables únicos.',
      category: 'crypto',
      tags: ['DeFi', 'staking', 'yield farming']
    },

    // Account
    {
      id: '20',
      question: '¿Cómo creo una cuenta?',
      answer: 'Crear una cuenta es simple: Visita nuestro sitio web y haz clic en "Registrarse", proporciona tu email y crea una contraseña segura, verifica tu email haciendo clic en el enlace enviado, completa tu perfil con información básica, y habilita la autenticación de dos factores para mayor seguridad.',
      category: 'account',
      tags: ['registro', 'cuenta', 'verificación']
    },
    {
      id: '21',
      question: '¿Qué documentos necesito para verificar mi cuenta?',
      answer: 'Para verificación completa necesitas: Documento de identidad válido (pasaporte, licencia de conducir, o cédula), comprobante de domicilio reciente (factura de servicios, estado de cuenta bancario), y para retiros grandes, puede requerirse comprobante de ingresos. El proceso de verificación toma 24-48 horas.',
      category: 'account',
      tags: ['verificación', 'documentos', 'KYC']
    },
    {
      id: '22',
      question: '¿Puedo tener múltiples cuentas?',
      answer: 'No, por políticas de seguridad y cumplimiento regulatorio, cada persona puede tener solo una cuenta. Las cuentas múltiples pueden resultar en suspensión permanente. Si necesitas cerrar tu cuenta actual y crear una nueva, contacta a nuestro equipo de soporte para asistencia.',
      category: 'account',
      tags: ['múltiples cuentas', 'políticas', 'cumplimiento']
    },

    // Prizes
    {
      id: '23',
      question: '¿Cómo se distribuyen los premios?',
      answer: 'Los premios se distribuyen automáticamente mediante smart contracts: Los ganadores son seleccionados por algoritmos verificables, los premios se transfieren automáticamente a las wallets ganadoras, recibes notificaciones inmediatas si ganas, y puedes verificar todas las transacciones en blockchain explorers.',
      category: 'prizes',
      tags: ['premios', 'distribución', 'smart contracts']
    },
    {
      id: '24',
      question: '¿Hay límites en los premios que puedo ganar?',
      answer: 'No hay límites en la cantidad de premios que puedes ganar. Sin embargo, para retiros grandes (más de $10,000), pueden aplicarse verificaciones adicionales de seguridad y cumplimiento. Los premios en criptomonedas no tienen límites, mientras que los premios en fiat pueden estar sujetos a regulaciones locales de tu país.',
      category: 'prizes',
      tags: ['límites', 'premios', 'retiros']
    }
  ];

  const toggleExpanded = (id: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(id)) {
      newExpanded.delete(id);
    } else {
      newExpanded.add(id);
    }
    setExpandedItems(newExpanded);
  };

  const filteredFAQs = faqItems.filter(item => {
    const matchesSearch = item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.answer.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  const getCategoryColor = (color: string) => {
    switch (color) {
      case 'purple': return 'text-purple-400 bg-purple-500/20';
      case 'blue': return 'text-blue-400 bg-blue-500/20';
      case 'green': return 'text-green-400 bg-green-500/20';
      case 'red': return 'text-red-400 bg-red-500/20';
      case 'amber': return 'text-amber-400 bg-amber-500/20';
      case 'indigo': return 'text-indigo-400 bg-indigo-500/20';
      case 'pink': return 'text-pink-400 bg-pink-500/20';
      default: return 'text-white bg-white/20';
    }
  };

  const getCategoryBorder = (color: string) => {
    switch (color) {
      case 'purple': return 'border-purple-500/50';
      case 'blue': return 'border-blue-500/50';
      case 'green': return 'border-green-500/50';
      case 'red': return 'border-red-500/50';
      case 'amber': return 'border-amber-500/50';
      case 'indigo': return 'border-indigo-500/50';
      case 'pink': return 'border-pink-500/50';
      default: return 'border-white/50';
    }
  };

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Preguntas <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Frecuentes</span>
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Encuentra respuestas a las preguntas más comunes sobre Sorteo Estelar, 
            desde cómo funciona hasta detalles sobre seguridad y pagos.
          </p>
        </div>

        {/* Search */}
        <Card variant="glass" className="mb-8">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
            <input
              type="text"
              placeholder="Buscar preguntas..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-12 pr-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
        </Card>

        {/* Categories */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-3">
            {categories.map(category => {
              const isSelected = selectedCategory === category.id;
              return (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-xl border transition-all duration-200 ${
                    isSelected
                      ? `${getCategoryColor(category.color)} ${getCategoryBorder(category.color)} border-2`
                      : 'text-white/70 bg-white/10 border-white/20 hover:bg-white/20 hover:text-white'
                  }`}
                >
                  {category.icon}
                  <span className="font-medium">{category.name}</span>
                  <span className="text-xs opacity-70">({category.count})</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-12">
          {filteredFAQs.length === 0 ? (
            <Card variant="glass">
              <div className="text-center py-12">
                <HelpCircle className="w-16 h-16 text-white/30 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No se encontraron preguntas</h3>
                <p className="text-white/70">
                  Intenta con otros términos de búsqueda o selecciona una categoría diferente.
                </p>
              </div>
            </Card>
          ) : (
            filteredFAQs.map(item => {
              const isExpanded = expandedItems.has(item.id);
              return (
                <Card key={item.id} variant="glass" className="overflow-hidden">
                  <button
                    onClick={() => toggleExpanded(item.id)}
                    className="w-full text-left p-6 hover:bg-white/5 transition-colors duration-200"
                  >
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold text-white pr-4">
                        {item.question}
                      </h3>
                      <div className="flex-shrink-0">
                        {isExpanded ? (
                          <ChevronUp className="w-5 h-5 text-purple-400" />
                        ) : (
                          <ChevronDown className="w-5 h-5 text-white/50" />
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 mt-2">
                      <span className="text-xs px-2 py-1 bg-purple-500/20 text-purple-400 rounded-full">
                        {categories.find(cat => cat.id === item.category)?.name}
                      </span>
                      {item.tags.map(tag => (
                        <span key={tag} className="text-xs px-2 py-1 bg-white/10 text-white/60 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </button>
                  
                  {isExpanded && (
                    <div className="px-6 pb-6 border-t border-white/10">
                      <div className="pt-4 text-white/80 leading-relaxed">
                        {item.answer}
                      </div>
                    </div>
                  )}
                </Card>
              );
            })
          )}
        </div>

        {/* Contact Support */}
        <Card variant="glass">
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-purple-500/20 rounded-full mx-auto flex items-center justify-center">
              <MessageCircle className="w-8 h-8 text-purple-400" />
            </div>
            
            <div>
              <h2 className="text-2xl font-bold text-white mb-2">¿No encontraste lo que buscabas?</h2>
              <p className="text-white/80 max-w-2xl mx-auto">
                Nuestro equipo de soporte está disponible 24/7 para ayudarte con cualquier pregunta 
                o problema que puedas tener. No dudes en contactarnos.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary">
                <MessageCircle className="w-4 h-4 mr-2" />
                Chat en Vivo
              </Button>
              <Button variant="outline">
                <HelpCircle className="w-4 h-4 mr-2" />
                Centro de Ayuda
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto pt-6 border-t border-white/10">
              <div className="text-center">
                <div className="w-12 h-12 bg-blue-500/20 rounded-full mx-auto mb-3 flex items-center justify-center">
                  <MessageCircle className="w-6 h-6 text-blue-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">Chat en Vivo</h3>
                <p className="text-white/70 text-sm">Respuesta inmediata</p>
                <p className="text-white/70 text-sm">24/7 disponible</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-green-500/20 rounded-full mx-auto mb-3 flex items-center justify-center">
                  <Smartphone className="w-6 h-6 text-green-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">WhatsApp</h3>
                <p className="text-white/70 text-sm">+****************</p>
                <p className="text-white/70 text-sm">Lun-Dom 8AM-10PM</p>
              </div>
              
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full mx-auto mb-3 flex items-center justify-center">
                  <HelpCircle className="w-6 h-6 text-purple-400" />
                </div>
                <h3 className="text-white font-semibold mb-1">Email</h3>
                <p className="text-white/70 text-sm"><EMAIL></p>
                <p className="text-white/70 text-sm">Respuesta en 2-4 horas</p>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default FAQ;