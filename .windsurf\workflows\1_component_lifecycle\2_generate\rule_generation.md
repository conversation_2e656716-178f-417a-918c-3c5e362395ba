---
description: Guides the generation of a new ESTRATIX Rule in the machine-readable .md format for agentic consumption.
---

# ESTRATIX .md Rule Generation Workflow

## 1. Purpose

This workflow guides a user or agent through the creation of a new, granular, **machine-readable** ESTRATIX Rule using the `.md` format. These rules are designed to be dynamically loaded by agents to enforce specific conventions, patterns, and behaviors based on the project context.

## 2. Rule Architecture

The ESTRATIX rule system is dual-layered:

- **High-Level Standards (.md):** Broad coding and usage practices (e.g., `coding_practices.md`) that provide general, human-readable context for a domain.
- **Machine-Readable Rules (.md):** Atomic, trigger-based rules that enforce specific, verifiable constraints. An agent loads both the high-level standard and any relevant `.md` files for its task.

This workflow focuses exclusively on creating the `.md` files.

## 3. Workflow Steps

### Step 3.1: Define the Rule''s Core Attributes

1. **Description:** Write a concise, one-line description in the format: `ACTION when TRIGGER to OUTCOME`. This is critical for the AI''s ability to select the rule.
2. **Globs:** Define the file or directory patterns where this rule should apply (e.g., `src/api/routes/**/*.py`, `*.component.ts`). This is the primary trigger for loading the rule.
3. **Always Apply:** Determine if the rule is foundational (`true`) or context-specific (`false`). Foundational rules are always loaded for their domain.

### Step 3.2: Determine the Rule''s Location

The location determines the rule''s scope.

1. **Master Project Rules:**
    - **Location:** Place the rule within the appropriate category in the master project''s `/.windsurf/rules/` directory.
    - **Example:** A new Python linting rule would go in `/.windsurf/rules/backend/python/`.

2. **Client-Specific Rules:**
    - **Location:** Place the rule within the client''s dedicated rules directory.
    - **Example:** `/clients/[ClientID]/.windsurf/rules/backend/python/`.

3. **Naming Convention:** Name the file descriptively, using `snake_case` (e.g., `rest_endpoint_naming.md`).

### Step 3.3: Populate the Rule File using the Template

Create the file and populate it using the following template. **All fields are required.**

```md
---
description: "[ACTION when TRIGGER to OUTCOME]"
globs: "[glob pattern for files/folders]"
alwaysApply: [true or false]
---

# [Rule Title]

## Context

- [Describe when to apply this rule and any prerequisite conditions.]

## Requirements

- [List concise, actionable, and testable requirements.]
- [Each requirement should be a clear instruction for the agent.]

## Examples

<example>
[Provide a good, concise example of conforming code or behavior, with a brief explanation.]
</example>

<example type="invalid">
[Provide a bad, concise example of non-conforming code or behavior, with a brief explanation.]
</example>

## Critical Rules

- [Summarize the absolute most important do''s and don''ts in short bullet points.]
- [NEVER do Y.]
```

### Step 3.4: Register the Rule in the Matrix

1. **Open the Rule Matrix:** Navigate to `[docs/matrices/rule_matrix.md](../../../../docs/matrices/rule_matrix.md)`.
2. **Add a new row:** Add an entry for the new rule, filling in all columns. The `Source/Location` must be the correct relative path to the new `.md` file from the project root.

### Step 3.5: Propose Changes

Commit the new `.md` rule file and the updated `rule_matrix.md` to version control.
