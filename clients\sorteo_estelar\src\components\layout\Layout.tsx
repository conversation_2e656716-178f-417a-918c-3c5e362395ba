import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Menu, X, Globe, Wallet, User, Settings, LogOut } from 'lucide-react';
import Button from '../ui/Button';
import Card from '../ui/Card';
import LanguageSelector from '../ui/LanguageSelector';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { t } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const location = useLocation();

  const navigation = [
    { name: t('nav.home'), href: '/', icon: '🏠' },
    { name: t('nav.catalog'), href: '/catalog', icon: '🎁' },
    { name: t('nav.myNumbers'), href: '/my-numbers', icon: '🎫' },
    { name: t('nav.results'), href: '/results', icon: '🏆' },
    { name: t('nav.defi'), href: '/defi', icon: '💎' },
    { name: t('nav.nftMarketplace'), href: '/nft-marketplace', icon: '🛒' },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      {/* Navigation Header */}
      <nav className="bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link to="/" className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">⭐</span>
                </div>
                <span className="text-white font-bold text-xl">Sorteo Estelar</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-white/20 text-white'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <span>{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              ))}
            </div>

            {/* Right side buttons */}
            <div className="flex items-center space-x-4">
              {/* Language Selector */}
              <LanguageSelector />

              {/* Wallet Connection */}
              <Button variant="outline" size="sm" className="hidden sm:flex border-white/30 text-white hover:bg-white/10">
                <Wallet className="w-4 h-4 mr-2" />
                {t('nav.connectWallet')}
              </Button>

              {/* User Menu */}
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-all duration-200"
                >
                  <User className="w-5 h-5" />
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 py-1 z-50">
                    <Link to="/profile" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <User className="w-4 h-4 mr-3" />
                      {t('nav.profile')}
                    </Link>
                    <Link to="/settings" className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                      <Settings className="w-4 h-4 mr-3" />
                      {t('nav.settings')}
                    </Link>
                    <hr className="my-1" />
                    <button className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                      <LogOut className="w-4 h-4 mr-3" />
                      {t('nav.logout')}
                    </button>
                  </div>
                )}
              </div>

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden text-white/80 hover:text-white p-2 rounded-lg hover:bg-white/10 transition-all duration-200"
              >
                {isMobileMenuOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white/10 backdrop-blur-md border-t border-white/20">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-base font-medium transition-all duration-200 ${
                    isActive(item.href)
                      ? 'bg-white/20 text-white'
                      : 'text-white/80 hover:text-white hover:bg-white/10'
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <span>{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              ))}
              <div className="pt-2">
                <Button variant="outline" size="sm" className="w-full border-white/30 text-white hover:bg-white/10">
                  <Wallet className="w-4 h-4 mr-2" />
                  {t('nav.connectWallet')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-black/20 backdrop-blur-md border-t border-white/10 mt-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">⭐</span>
                </div>
                <span className="text-white font-bold text-xl">Sorteo Estelar</span>
              </div>
              <p className="text-white/80 text-sm mb-4">
                La plataforma Web3 más innovadora para sorteos y lotería en Colombia. 
                Combina tecnología blockchain con experiencias inmersivas 3D.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <span className="sr-only">Instagram</span>
                  📷
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <span className="sr-only">TikTok</span>
                  🎵
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <span className="sr-only">WhatsApp</span>
                  💬
                </a>
                <a href="#" className="text-white/60 hover:text-white transition-colors">
                  <span className="sr-only">Telegram</span>
                  ✈️
                </a>
              </div>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Plataforma</h3>
              <ul className="space-y-2 text-sm text-white/80">
                <li><Link to="/catalog" className="hover:text-white transition-colors">Catálogo de Premios</Link></li>
                <li><Link to="/my-numbers" className="hover:text-white transition-colors">Mis Números</Link></li>
                <li><Link to="/results" className="hover:text-white transition-colors">Resultados</Link></li>
                <li><Link to="/defi" className="hover:text-white transition-colors">DeFi Hub</Link></li>
                <li><Link to="/nft-marketplace" className="hover:text-white transition-colors">NFT Marketplace</Link></li>
                <li><Link to="/payment" className="hover:text-white transition-colors">Pagos</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Soporte</h3>
              <ul className="space-y-2 text-sm text-white/80">
                <li><Link to="/faq" className="hover:text-white transition-colors">Preguntas Frecuentes</Link></li>
                <li><Link to="/about" className="hover:text-white transition-colors">Acerca de</Link></li>
                <li><Link to="/terms" className="hover:text-white transition-colors">Términos y Condiciones</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-8 pt-8 text-center">
            <p className="text-white/60 text-sm">
              © 2024 Sorteo Estelar. Todos los derechos reservados. Powered by ESTRATIX Agentic Framework.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;