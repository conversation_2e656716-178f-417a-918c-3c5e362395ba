# PAT-002: Continuous Knowledge Monitoring Pattern Definition

**Pattern ID**: PAT-002

**Pattern Name**: Continuous Knowledge Monitoring

**Category**: Agentic

## 1. Purpose & Description

This pattern defines a systematic, automated process for continuously monitoring external knowledge sources (e.g., websites, documentation, code repositories). Its primary goal is to detect changes, updates, or new information and trigger the appropriate workflows for ingestion, analysis, and integration into the ESTRATIX knowledge base. This ensures the system's understanding of the world remains current and relevant.

## 2. Context

This pattern is applicable in any scenario where an agentic system relies on external, dynamic information. It is foundational for:

- Maintaining expert knowledge in specific domains.
- Competitive analysis and market research.
- Technology scouting and framework updates.
- Ensuring compliance with changing standards or documentation.

## 3. Structure & Components

This pattern orchestrates the following ESTRATIX components:

- **Flow (`CIO_F002_KnowledgeMonitoringFlow`)**: The master orchestrator that reads from the `source_matrix.md` and initiates monitoring for each source.
- **Process/Crew (`CIO_P005_KnowledgeMonitoringCrew`)**: The core execution unit responsible for performing the monitoring of a single source.
- **Agents (`CIO_A001_KnowledgeMonitorAgent`)**: The specialized agent that uses tools to scan the source and analyze its content.
- **Tasks (`CIO_T001_KnowledgeMonitorTasks`)**: The specific instructions for the agent, such as scanning a URI and flagging it for re-ingestion if changes are detected.
- **Tools (`SourceScannerTool`, `MatrixEditorTool`)**: The functions used by the agent to interact with external sources and internal matrices.
- **Matrices (`source_matrix.md`)**: The configuration file that provides the list of sources to monitor.

## 4. Workflow

1. The `ContinuousKnowledgeMonitoring` flow is triggered (e.g., on a schedule or manually).
2. The flow reads the `source_matrix.md` to get the list of URIs to monitor.
3. For each source, the flow invokes the `KnowledgeMonitoringCrew`.
4. The `KnowledgeMonitorAgent` within the crew executes the `monitor_source_task`.
5. The agent uses the `SourceScannerTool` to check the source for updates.
6. If an update is detected, the agent executes the `flag_source_for_reingestion_task`, using the `MatrixEditorTool` to update the status of the source in the matrix.
7. The flow collects the results from all crew executions.

## 5. Status

`Defined`
