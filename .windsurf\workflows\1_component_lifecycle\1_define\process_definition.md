---
description: Guides the user or agent through defining a new ESTRATIX process according to P001 standards.
---

# Workflow: Define ESTRATIX Process

## Objective

To produce a comprehensive and standardized definition document for a new ESTRATIX Process, ensuring it is atomically registered in the `process_matrix.md` and stored in the correct directory: `docs/processes/[Owner_Office_Code]/`.

## Agent Persona

`AGENT_Architect_Expert`

## Steps

1. **Initiation & Information Gathering**
   - **Action**: An agent or user identifies the need for a new process.
   - **Input**: `Process_Name` (e.g., "New Technology Analysis"), `Owner_Office_Code` (e.g., "CTO"), and a brief `Description`.
   - **Verification**: Check `docs/models/process_matrix.md` to ensure a similar process does not already exist.

2. **Register Process & Reserve ID (Atomic Operation)**
   - **Action (Automated)**: An agent programmatically reserves a unique ID and registers the process.
   - **Logic**:
     1. Read `docs/models/process_matrix.md`.
     2. Determine the next available `Process_ID` (e.g., if the last ID is `p035`, the new one is `p036`).
     3. Add a new row to the matrix with the new `Process_ID`, `Process_Name`, `Owner_Office_Code`, a placeholder `Description`, and set the `Status` to `Pending Definition`.
     4. Save the updated `docs/models/process_matrix.md`.
   - **Output**: The newly reserved `Process_ID` (e.g., `p036`).

3. **Create Process Definition File from Template**
   - **Action (Automated)**:
     1. Create the target directory if it doesn't exist: `docs/processes/[Owner_Office_Code]/`.
     2. Copy the template `docs/templates/estratix_process_definition_template.md` to the target file: `docs/processes/[Owner_Office_Code]/[Process_ID]_[Process_Name_PascalCase]_Definition.md`.
     3. Replace placeholders like `[Process_ID]` and `[ProcessName]` in the new file.
   - **Tooling**: File system operations, text replacement.

4. **Populate Process Definition**
   - **Action (User/Agent)**: Open the newly created definition file and populate all sections based on the template's guidance (Objective, Triggers, Agents, Tasks, Tools, etc.).

5. **Review and Finalize**
   - **Action**: Review the populated definition for clarity, completeness, and feasibility.
   - **Input**: Feedback from the `Owner_Office_Code` Command Officer.
   - **Output**: A finalized definition document.

6. **Update Matrix with Definition Link**
   - **Action (Automated)**: Upon finalization, an agent updates the process's entry in the matrix.
   - **Logic**:
     1. Open `docs/models/process_matrix.md`.
     2. Locate the row corresponding to the `Process_ID`.
     3. Update the `Description` with the final summary from the definition file.
     4. Set the `Definition Link` to point to the new definition file.
     5. Update the `Status` from `Pending Definition` to `Defined`.
   - **Tooling**: Markdown table manipulation script.

7. **Confirmation**
   - **Action**: Confirm to the user that the process has been successfully defined and registered.
   - **Output**: Provide a direct link to the new definition file and the updated process matrix.
