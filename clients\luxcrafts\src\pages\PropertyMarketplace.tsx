import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  MagnifyingGlassIcon,
  MapPinIcon,
  CurrencyDollarIcon,
  HomeIcon,
  ChartBarIcon,
  StarIcon,
  HeartIcon,
  ShareIcon,
  EyeIcon,
  CalendarIcon,
  UserGroupIcon,
  BanknotesIcon,
  ShieldCheckIcon,
  GlobeAltIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';

const propertyListings = [
  {
    id: 1,
    title: 'Luxury Penthouse in Back Bay',
    address: '456 Newbury Street, Boston, MA',
    price: 2850000,
    tokenPrice: 285,
    totalTokens: 10000,
    availableTokens: 3500,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20boston%20back%20bay%20modern%20interior&image_size=landscape_16_9',
    bedrooms: 3,
    bathrooms: 2.5,
    sqft: 2400,
    yearBuilt: 2019,
    propertyType: 'Condo',
    roi: 8.5,
    rentalYield: 6.2,
    appreciation: 12.3,
    nftCertificate: true,
    verified: true,
    featured: true,
    views: 1247,
    favorites: 89,
    daysOnMarket: 15,
    virtualTour: true,
    amenities: ['Rooftop Deck', 'Concierge', 'Gym', 'Parking'],
  },
  {
    id: 2,
    title: 'Historic Brownstone in Beacon Hill',
    address: '123 Beacon Street, Boston, MA',
    price: 4200000,
    tokenPrice: 420,
    totalTokens: 10000,
    availableTokens: 2100,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=historic%20brownstone%20beacon%20hill%20boston%20luxury%20exterior&image_size=landscape_16_9',
    bedrooms: 4,
    bathrooms: 3.5,
    sqft: 3200,
    yearBuilt: 1890,
    propertyType: 'Townhouse',
    roi: 9.2,
    rentalYield: 5.8,
    appreciation: 15.1,
    nftCertificate: true,
    verified: true,
    featured: true,
    views: 2156,
    favorites: 156,
    daysOnMarket: 8,
    virtualTour: true,
    amenities: ['Garden', 'Fireplace', 'Wine Cellar', 'Parking'],
  },
  {
    id: 3,
    title: 'Modern Waterfront Condo',
    address: '789 Harbor View, Cambridge, MA',
    price: 1950000,
    tokenPrice: 195,
    totalTokens: 10000,
    availableTokens: 5200,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20waterfront%20condo%20cambridge%20harbor%20view&image_size=landscape_16_9',
    bedrooms: 2,
    bathrooms: 2,
    sqft: 1800,
    yearBuilt: 2021,
    propertyType: 'Condo',
    roi: 7.8,
    rentalYield: 7.1,
    appreciation: 10.5,
    nftCertificate: true,
    verified: true,
    featured: false,
    views: 892,
    favorites: 67,
    daysOnMarket: 22,
    virtualTour: true,
    amenities: ['Water View', 'Balcony', 'Pool', 'Gym'],
  },
];

const marketData = [
  { month: 'Jan', avgPrice: 2100000, volume: 45, tokenVolume: 125000 },
  { month: 'Feb', avgPrice: 2150000, volume: 52, tokenVolume: 142000 },
  { month: 'Mar', avgPrice: 2200000, volume: 48, tokenVolume: 138000 },
  { month: 'Apr', avgPrice: 2180000, volume: 55, tokenVolume: 165000 },
  { month: 'May', avgPrice: 2250000, volume: 62, tokenVolume: 189000 },
  { month: 'Jun', avgPrice: 2300000, volume: 58, tokenVolume: 176000 },
];

const portfolioData = [
  { name: 'Residential', value: 65, color: '#3b82f6' },
  { name: 'Commercial', value: 25, color: '#10b981' },
  { name: 'Mixed Use', value: 10, color: '#f59e0b' },
];

const investmentStats = [
  { label: 'Total Properties', value: '247', change: '+12%', icon: HomeIcon },
  { label: 'Market Cap', value: '$1.2B', change: '+8.5%', icon: CurrencyDollarIcon },
  { label: 'Active Investors', value: '15.2K', change: '+23%', icon: UserGroupIcon },
  { label: 'Avg ROI', value: '8.9%', change: '+1.2%', icon: ChartBarIcon },
];

export default function PropertyMarketplace() {
  const [activeTab, setActiveTab] = useState('marketplace');
  const [selectedProperty, setSelectedProperty] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState([0, 10000000]);
  const [showInvestModal, setShowInvestModal] = useState(false);

  const tabs = [
    { id: 'marketplace', name: 'Marketplace' },
    { id: 'portfolio', name: 'My Portfolio' },
    { id: 'analytics', name: 'Market Analytics' },
    { id: 'nft', name: 'NFT Certificates' },
  ];

  const formatPrice = (price) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Tokenized Real Estate Marketplace
          </h1>
          <p className="text-xl text-gray-600">
            Invest in fractional ownership of premium properties with blockchain technology
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Marketplace Tab */}
        {activeTab === 'marketplace' && (
          <div className="space-y-8">
            {/* Market Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {investmentStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-xl p-6 shadow-lg"
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                      <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                      <p className="text-sm text-green-600">{stat.change}</p>
                    </div>
                    <stat.icon className="w-8 h-8 text-blue-500" />
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Search and Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search properties by location, type, or features..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex space-x-4">
                  <select
                    value={filterType}
                    onChange={(e) => setFilterType(e.target.value)}
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Types</option>
                    <option value="condo">Condos</option>
                    <option value="townhouse">Townhouses</option>
                    <option value="single-family">Single Family</option>
                  </select>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="featured">Featured</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="roi">Highest ROI</option>
                    <option value="newest">Newest</option>
                  </select>
                  <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    Advanced Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Property Listings */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {propertyListings.map((property) => (
                <motion.div
                  key={property.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                  onClick={() => setSelectedProperty(property)}
                >
                  <div className="relative">
                    <img
                      src={property.image}
                      alt={property.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-4 left-4 flex space-x-2">
                      {property.featured && (
                        <span className="px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full">
                          Featured
                        </span>
                      )}
                      {property.nftCertificate && (
                        <span className="px-2 py-1 bg-purple-500 text-white text-xs font-medium rounded-full">
                          NFT
                        </span>
                      )}
                      {property.verified && (
                        <ShieldCheckIcon className="w-5 h-5 text-green-500 bg-white rounded-full p-1" />
                      )}
                    </div>
                    <div className="absolute top-4 right-4 flex space-x-2">
                      <button className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                        <HeartIcon className="w-4 h-4 text-gray-600" />
                      </button>
                      <button className="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
                        <ShareIcon className="w-4 h-4 text-gray-600" />
                      </button>
                    </div>
                    <div className="absolute bottom-4 right-4">
                      {property.virtualTour && (
                        <span className="px-2 py-1 bg-blue-500 text-white text-xs font-medium rounded-full flex items-center space-x-1">
                          <EyeIcon className="w-3 h-3" />
                          <span>Virtual Tour</span>
                        </span>
                      )}
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-bold text-gray-900 mb-1">{property.title}</h3>
                        <div className="flex items-center text-gray-600 text-sm">
                          <MapPinIcon className="w-4 h-4 mr-1" />
                          <span>{property.address}</span>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-4 text-sm">
                      <div className="text-center">
                        <p className="text-gray-500">Bedrooms</p>
                        <p className="font-semibold">{property.bedrooms}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-500">Bathrooms</p>
                        <p className="font-semibold">{property.bathrooms}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-gray-500">Sq Ft</p>
                        <p className="font-semibold">{property.sqft.toLocaleString()}</p>
                      </div>
                    </div>

                    <div className="border-t border-gray-200 pt-4 mb-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500">Total Value</span>
                        <span className="text-lg font-bold text-gray-900">{formatPrice(property.price)}</span>
                      </div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm text-gray-500">Token Price</span>
                        <span className="text-sm font-semibold">${property.tokenPrice}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Available Tokens</span>
                        <span className="text-sm font-semibold text-green-600">
                          {property.availableTokens.toLocaleString()}/{property.totalTokens.toLocaleString()}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-2 mb-4 text-xs">
                      <div className="text-center p-2 bg-green-50 rounded">
                        <p className="text-green-600 font-semibold">{property.roi}%</p>
                        <p className="text-gray-500">ROI</p>
                      </div>
                      <div className="text-center p-2 bg-blue-50 rounded">
                        <p className="text-blue-600 font-semibold">{property.rentalYield}%</p>
                        <p className="text-gray-500">Yield</p>
                      </div>
                      <div className="text-center p-2 bg-purple-50 rounded">
                        <p className="text-purple-600 font-semibold">{property.appreciation}%</p>
                        <p className="text-gray-500">Growth</p>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-gray-500 mb-4">
                      <div className="flex items-center space-x-3">
                        <span>{formatNumber(property.views)} views</span>
                        <span>{property.favorites} favorites</span>
                      </div>
                      <span>{property.daysOnMarket} days on market</span>
                    </div>

                    <div className="flex space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowInvestModal(true);
                        }}
                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm"
                      >
                        Invest Now
                      </button>
                      <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                        Details
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Portfolio Tab */}
        {activeTab === 'portfolio' && (
          <div className="space-y-8">
            {/* Portfolio Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Portfolio Performance</h3>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={marketData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="month" />
                        <YAxis />
                        <Tooltip formatter={(value) => [`$${(Number(value) / 1000000).toFixed(1)}M`, 'Value']} />
                        <Line type="monotone" dataKey="avgPrice" stroke="#3b82f6" strokeWidth={3} />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              </div>
              <div className="space-y-6">
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Portfolio Distribution</h3>
                  <div className="h-48">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={portfolioData}
                          cx="50%"
                          cy="50%"
                          innerRadius={40}
                          outerRadius={80}
                          dataKey="value"
                        >
                          {portfolioData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>
                  <div className="space-y-2">
                    {portfolioData.map((item) => (
                      <div key={item.name} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }} />
                          <span className="text-sm text-gray-600">{item.name}</span>
                        </div>
                        <span className="text-sm font-semibold">{item.value}%</span>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">Quick Stats</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Investment</span>
                      <span className="font-semibold">$125,000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Current Value</span>
                      <span className="font-semibold text-green-600">$142,500</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Return</span>
                      <span className="font-semibold text-green-600">+14.0%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Monthly Income</span>
                      <span className="font-semibold">$1,247</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Market Analytics</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={marketData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Line yAxisId="left" type="monotone" dataKey="avgPrice" stroke="#3b82f6" strokeWidth={3} />
                    <Line yAxisId="right" type="monotone" dataKey="tokenVolume" stroke="#10b981" strokeWidth={3} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {/* NFT Certificates Tab */}
        {activeTab === 'nft' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">NFT Property Certificates</h3>
            <div className="text-center py-12">
              <GlobeAltIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h4 className="text-lg font-semibold text-gray-900 mb-2">NFT Marketplace Coming Soon</h4>
              <p className="text-gray-600 mb-6">
                Trade property ownership certificates as NFTs on our decentralized marketplace.
              </p>
              <button className="px-6 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors font-medium">
                Join Waitlist
              </button>
            </div>
          </div>
        )}

        {/* Investment Modal */}
        {showInvestModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900">Invest in Property</h3>
                <button
                  onClick={() => setShowInvestModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Investment Amount (USD)
                  </label>
                  <input
                    type="number"
                    placeholder="1000"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Number of Tokens
                  </label>
                  <input
                    type="number"
                    placeholder="Calculated automatically"
                    disabled
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50"
                  />
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Investment Summary</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span>Token Price:</span>
                      <span>$285</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Platform Fee:</span>
                      <span>2.5%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Gas Fee:</span>
                      <span>~$15</span>
                    </div>
                    <div className="border-t pt-1 flex justify-between font-semibold">
                      <span>Total:</span>
                      <span>$1,040</span>
                    </div>
                  </div>
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowInvestModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Invest Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}