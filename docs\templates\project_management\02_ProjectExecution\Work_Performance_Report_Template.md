# Work Performance Report: [Project Name]

## Document Control
*   **Report Title:** Work Performance Report: `[Full Official Project Name]`
*   **Project Name:** `[Full Official Project Name]`
*   **Project ID:** `[Unique Project Identifier]`
*   **Client Name (if applicable):** `[Client Name]`
*   **Client ID (ESTRATIX):** `[Client_ID]`
*   **Reporting Period:** `[YYYY-MM-DD]` to `[YYYY-MM-DD]`
*   **Report Version:** `[e.g., 1.0, 1.1]`
*   **Date Prepared:** `[YYYY-MM-DD]`
*   **Prepared By:** `[Project Manager Name / ESTRATIX Agent ID, e.g., CPO_AXXX_ProjectManager or CPO_AXXX_ReportingAgent]`
*   **Document Status:** `[e.g., Draft, Submitted for Review, Approved]`
*   **Security Classification:** `[e.g., ESTRATIX Internal, Client Confidential]`
*   **Distribution List:** `[List of key stakeholder roles/groups, e.g., Project Sponsor, Steering Committee, Client Lead, ESTRATIX Command Offices]`
*   **Related Project Plan ID:** `[Link to ../01_ProjectPlanning/Project_Plan_Template.md or actual Project Plan document ID]`

## 1. Executive Summary
*   **Overall Project Health:** `[RAG Status: Green/Amber/Red - Brief justification, e.g., Green - Project is on track for schedule and budget with minor manageable issues.]`
*   **Key Accomplishments (this period):**
    *   `[Accomplishment 1, e.g., Milestone M2.1 Achieved]`
    *   `[Accomplishment 2, e.g., Deliverable D1.3 Submitted and Accepted]`
*   **Major Issues & Risks (Summary):**
    *   Issues: `[e.g., 1 Critical, 3 High priority issues being actively managed. Refer to Section 3.7 for details.]`
    *   Risks: `[e.g., Risk R-005 (External Dependency Delay) probability increased. Mitigation actions in progress. Refer to Section 3.6 for details.]`
*   **Performance Summary vs. Baselines:**
    *   Schedule: `[e.g., On track, SPI = 1.02]`
    *   Cost: `[e.g., Slightly over budget, CPI = 0.98]`
    *   Scope: `[e.g., All planned scope for the period completed. One minor CR approved.]`
    *   Quality: `[e.g., Defect resolution rate at 95%. No critical defects outstanding.]`
*   **Outlook for Next Period:** `[e.g., Focus on completing Phase 2 deliverables. Key milestone M3.0 planned.]`

## 2. Overall Project Status (Narrative)
`[Provide a more detailed narrative of the overall project status, expanding on the executive summary. Highlight significant events, decisions, or challenges encountered during the reporting period.]`

## 3. Detailed Performance Analysis

### 3.1. Schedule Performance
*   **Milestones (this period):**
    | Milestone ID | Milestone Name          | Baseline Date | Planned Date | Actual/Forecast Date | Status      | Comments |
    | :----------- | :---------------------- | :------------ | :----------- | :------------------- | :---------- | :------- |
    | `[M2.1]`     | `[e.g., Design Approved]` | `[YYYY-MM-DD]`| `[YYYY-MM-DD]` | `[YYYY-MM-DD]`       | `[Achieved]`|          |
    | `[M2.2]`     | `[e.g., Build Complete]`  | `[YYYY-MM-DD]`| `[YYYY-MM-DD]` | `[YYYY-MM-DD]`       | `[Forecast]`|`[Slight delay due to...]`|
*   **Key Activities Completed vs. Planned:** `[Summary of task completion rates, e.g., 90% of planned tasks completed. List key variances.]`
*   **Schedule Variance (SV):** `[Value, e.g., +$5,000 or +2 days]`
*   **Schedule Performance Index (SPI):** `[Value, e.g., 1.02]`
    *   Interpretation: `[e.g., Project is progressing slightly ahead of schedule.]`
*   **Critical Path Analysis:** `[Summary of critical path status, any changes, float/slack, top 3 near-critical paths, and any concerns.]`
*   **Reference:** `[Link to live Project Schedule / ../01_ProjectPlanning/Project_Schedule_Template.md]`

### 3.2. Cost Performance (Earned Value Management - EVM)
*   **Planned Value (PV) (BCWS):** Period: `[$]` Cumulative: `[$]`
*   **Earned Value (EV) (BCWP):** Period: `[$]` Cumulative: `[$]`
*   **Actual Cost (AC) (ACWP):** Period: `[$]` Cumulative: `[$]`
*   **Cost Variance (CV):** `[EV - AC = $Value]`
*   **Cost Performance Index (CPI):** `[EV / AC = Value]`
    *   Interpretation: `[e.g., Project is slightly over budget.]`
*   **Budget at Completion (BAC):** `[$]`
*   **Estimate at Completion (EAC):** `[Formula used, e.g., BAC/CPI = $Value]`
*   **Estimate to Complete (ETC):** `[EAC - AC = $Value]`
*   **Variance at Completion (VAC):** `[BAC - EAC = $Value]`
*   **Burn Rate (Period/Cumulative):** `[$ per unit of time]`
*   **Reference:** `[Link to live Financial Data / ../01_ProjectPlanning/Budget_Plan_Template.md]`

### 3.3. Scope Performance
*   **Deliverables Completed/Submitted/Accepted (this period):**
    | Deliverable ID | Deliverable Name | Status (Submitted/Accepted/Rejected) | Date       | Acceptance Form Ref. |
    | :------------- | :--------------- | :----------------------------------- | :--------- | :------------------- |
    | `[D1.3]`       | `[User Manual]`  | `[Accepted]`                         | `[YYYY-MM-DD]` | `[DAF-005]`          |
*   **Scope Changes (this period):**
    | CR ID   | Description          | Status (Approved/Rejected/Pending) | Impact Summary |
    | :------ | :------------------- | :--------------------------------- | :------------- |
    | `[CR-003]`| `[Add new report]`   | `[Approved]`                       | `[+5 days, +$2k]`|
    *   Reference: `[Link to Change Log / ../04_ProjectClosure/Change_Request_Form_Template.md]`
*   **Scope Creep Concerns:** `[Identify any unmanaged changes or potential scope creep.]`

### 3.4. Quality Performance
*   **QA Activities Conducted:** `[e.g., Process Audit for Development Phase completed on YYYY-MM-DD. Findings: ...]`
*   **QC Activities & Metrics:** `[e.g., System Testing: 250 test cases executed, 92% pass rate. Peer Reviews: 5 design documents reviewed, 15 major comments identified.]`
*   **Defect Summary:**
    *   New Defects Logged (this period): `[Number]` (Critical: `[N]`, High: `[N]`, Medium: `[N]`, Low: `[N]`)
    *   Defects Resolved (this period): `[Number]`
    *   Defects Outstanding: `[Number]` (Critical: `[N]`, High: `[N]`, Medium: `[N]`, Low: `[N]`)
    *   Defect Resolution Rate: `[%]`
*   **Reference:** `[Link to Defect Tracking System / ../01_ProjectPlanning/Quality_Management_Plan_Template.md]`

### 3.5. Resource Performance
*   **Resource Utilization:** `[Summary of planned vs. actual for key resources/roles. Highlight any significant variances, e.g., Developer Team: 110% utilized (overtime), BA Team: 85% utilized.]`
*   **Resource Issues:** `[e.g., Resource X unavailable for 3 days. Unplanned attrition of Role Y. Agent Z performance below expectation.]`
*   **Team Morale/Health (Qualitative):** `[e.g., Team morale is generally positive, some stress noted due to tight deadlines.]`
*   **Reference:** `[Resource Management Plan section in Project_Plan_Template.md]`

### 3.6. Risk Management Update
*   **New Risks Identified (this period):**
    | Risk ID | Description | Likelihood (1-5) | Impact (1-5) | Owner (Agent ID/Role) |
    | :------ | :---------- | :--------------- | :----------- | :-------------------- |
    | `[R-015]` | `[...]`     | `[3]`            | `[4]`        | `[CPO_AXXX_RiskManager]`|
*   **Status of Top Critical Risks:**
    | Risk ID | Description | Current Status/Mitigation Actions Taken | Updated Exposure |
    | :------ | :---------- | :-------------------------------------- | :--------------- |
    | `[R-005]` | `[...]`     | `[Mitigation X implemented. Monitoring.]` | `[Medium]`       |
*   **Risks Realized/Closed (this period):** `[List any risks that occurred or were formally closed.]`
*   **Reference:** `[Link to ../03_ProjectMonitoringAndControlling/Risk_Register_Template.md]`

### 3.7. Issue Management Update
*   **New Issues Logged (this period):** `[Number]` (Critical: `[N]`, High: `[N]`, Medium: `[N]`, Low: `[N]`)
*   **Issues Resolved (this period):** `[Number]`
*   **Issues Outstanding:** `[Number]` (Critical: `[N]`, High: `[N]`, Medium: `[N]`, Low: `[N]`)
*   **Status of Top Critical Issues:**
    | Issue ID  | Description | Current Status/Resolution Actions | Target Date |
    | :-------- | :---------- | :-------------------------------- | :---------- |
    | `[ISS-010]`| `[...]`     | `[Resolution in progress.]`       | `[YYYY-MM-DD]`|
*   **Reference:** `[Link to ./Issue_Log_Template.md]`

### 3.8. Stakeholder Engagement Update
*   **Key Stakeholder Interactions:** `[e.g., Weekly status meeting with Client Lead held on YYYY-MM-DD. Steering Committee presentation on YYYY-MM-DD.]`
*   **Stakeholder Sentiment/Concerns:** `[e.g., Client Sponsor expressed satisfaction with progress. End-user group raised concerns about UI changes.]`
*   **Reference:** `[../01_ProjectPlanning/Communication_Plan_Template.md]`

### 3.9. Procurement Management Update (if applicable)
*   **Status of Key Procurements:** `[e.g., Contract for Vendor X signed. Hardware delivery from Vendor Y pending.]`
*   **Vendor Performance:** `[e.g., Vendor X meeting SLAs. Vendor Z performance below expectation, under review.]`
*   **Reference:** `[Procurement Management Plan section in Project_Plan_Template.md]`

## 4. Forecasts
*   **Schedule Forecast:**
    *   Projected Project Completion Date: `[YYYY-MM-DD]` (Variance from baseline: `[+/- X days]`)
    *   Projected Key Milestone M3.0: `[YYYY-MM-DD]`
*   **Cost Forecast:**
    *   Estimate at Completion (EAC): `[$ (as calculated in 3.2)]`
    *   Estimate to Complete (ETC): `[$ (as calculated in 3.2)]`
*   **Anticipated Deviations:** `[Highlight any significant deviations from baselines expected in the future and reasons.]`

## 5. Planned Activities for Next Reporting Period
*   **Key Tasks:** `[List major tasks to be worked on.]`
*   **Milestones:** `[List milestones planned for completion.]`
*   **Deliverables:** `[List deliverables planned for submission/acceptance.]`
*   **Focus Areas:** `[e.g., Resolve critical issues, prepare for UAT, onboard new team member.]`

## 6. Action Items from Previous Report
| Action Item ID | Description                                 | Owner (Agent ID/Role)   | Original Due Date | Status (Open/In Progress/Closed) | Comments                                   |
| :------------- | :------------------------------------------ | :---------------------- | :---------------- | :------------------------------- | :----------------------------------------- |
| `[WPR-PREV-01]`| `[e.g., Follow up on vendor payment]`       | `[CPO_AXXX_PM]`         | `[YYYY-MM-DD]`    | `[Closed]`                       | `[Payment confirmed on YYYY-MM-DD]`        |
| `[WPR-PREV-02]`| `[e.g., Schedule risk review workshop]`     | `[CPO_AXXX_RiskManager]`| `[YYYY-MM-DD]`    | `[Open]`                         | `[To be scheduled next week due to ...]`   |

## 7. Appendix (Optional)
*   `[e.g., EVM S-Curve Chart]`
*   `[e.g., Milestone Trend Analysis Chart]`
*   `[e.g., Detailed Resource Allocation Table]`
*   **Glossary of Terms & Acronyms:**
    | Term/Acronym | Definition |
    | :----------- | :--------- |
    | `[AC]`       | `[Actual Cost (ACWP - Actual Cost of Work Performed)]` |
    | `[BAC]`      | `[Budget at Completion]` |
    | `[CPI]`      | `[Cost Performance Index]` |
    | `[CV]`       | `[Cost Variance]` |
    | `[EAC]`      | `[Estimate at Completion]` |
    | `[ETC]`      | `[Estimate to Complete]` |
    | `[EV]`       | `[Earned Value (BCWP - Budgeted Cost of Work Performed)]` |
    | `[PV]`       | `[Planned Value (BCWS - Budgeted Cost of Work Scheduled)]` |
    | `[RAG]`      | `[Red, Amber, Green (Status Indicator)]` |
    | `[SPI]`      | `[Schedule Performance Index]` |
    | `[SV]`       | `[Schedule Variance]` |
    | `[VAC]`      | `[Variance at Completion]` |

## 8. Guidance for Use
*   **Data Sources:** This report should be populated using data from the ESTRATIX Project Management Information System (PMIS), including integrated scheduling tools, financial systems, risk and issue logs, quality dashboards, and agent-generated performance data.
*   **Frequency:** `[As defined in Communication_Plan_Template.md, e.g., Weekly for internal team, Bi-Weekly for Client, Monthly for Steering Committee.]`
*   **Distribution:** Refer to the Distribution List in the Document Control section and the Communication Management Plan.
*   **Approval:** `[Outline approval process if applicable, e.g., Reviewed by Project Manager, Approved by Project Sponsor.]`

---
*This Work Performance Report is an official project record. It should be stored in the ESTRATIX Project Document Repository at `[Link to Repository/Project_XYZ/Reporting/]` and be accessible to all relevant project stakeholders as defined in the Communication Management Plan.*
