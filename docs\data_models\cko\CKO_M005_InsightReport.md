# CKO_M005: InsightReport

## 1. Metadata

*   **Data Model ID:** CKO_M005
*   **Data Model Name:** InsightReport
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A004_KnowledgeAnalystAgent

## 2. Purpose

*   This data model represents a structured report detailing insights, findings, and analyses derived from `CKO_M003_CuratedKnowledgeAsset`s and the `CKO_M008_KnowledgeGraphNode` network. It serves as a key output of `CKO_F002_KnowledgeAnalysisAndInsightGeneration` and provides actionable intelligence to various ESTRATIX Command Offices.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from enum import Enum
import uuid

# Re-using SensitivityLevelEnum from CKO_M003 or defining it if not globally available
class CKO_SensitivityLevelEnum(str, Enum):
    PUBLIC = "Public"
    INTERNAL = "Internal"
    CONFIDENTIAL = "Confidential"
    STRICTLY_CONFIDENTIAL = "Strictly Confidential"

class CKO_ReportStatusEnum(str, Enum):
    DRAFT = "Draft"
    UNDER_REVIEW = "Under Review"
    PUBLISHED = "Published"
    ARCHIVED = "Archived"

class CKO_InsightReport(BaseModel):
    report_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the insight report.")
    title: str = Field(..., description="Title of the insight report.")
    executive_summary: str = Field(..., description="A concise overview of the key insights and recommendations.")
    
    # Content Sections
    introduction_background: Optional[str] = Field(None, description="Background information and context for the report.")
    methodology: Optional[str] = Field(None, description="Description of the methods used for analysis and insight generation.")
    key_findings: List[Dict[str, Any]] = Field(..., description="List of key findings. Each finding can be a structured dict with 'finding_text', 'supporting_evidence_ids', 'implications'.")
    # Example key_finding: {'finding_text': '...', 'supporting_asset_ids': [uuid1, uuid2], 'implications': '...'}
    detailed_analysis: Optional[str] = Field(None, description="In-depth analysis supporting the findings.")
    visualizations: Optional[List[Dict[str, Union[str, HttpUrl]]]] = Field(default_factory=list, description="Links to or embedded data for charts, graphs, or other visualizations. Each dict: {'type': 'image/link', 'url/data': '...', 'caption': '...'}")
    conclusions: str = Field(..., description="Overall conclusions drawn from the analysis.")
    recommendations: Optional[List[str]] = Field(default_factory=list, description="Actionable recommendations based on the insights.")
    
    # Provenance and Context
    source_asset_ids: List[uuid.UUID] = Field(default_factory=list, description="List of primary CKO_M003_CuratedKnowledgeAsset IDs used in this report.")
    source_graph_node_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="List of key CKO_M008_KnowledgeGraphNode IDs referenced or analyzed.")
    analysis_query_or_parameters: Optional[str] = Field(None, description="The query, parameters, or prompt used to generate this report, if applicable.")
    
    # Authorship and Review
    author_agent_id: str = Field(..., description="ID of the ESTRATIX agent (e.g., CKO_A004) who authored the report.")
    contributors_ids: Optional[List[str]] = Field(default_factory=list, description="IDs of other agents or SMEs who contributed.")
    reviewers_ids: Optional[List[str]] = Field(default_factory=list, description="IDs of agents or SMEs who reviewed the report.")
    report_status: CKO_ReportStatusEnum = Field(default=CKO_ReportStatusEnum.DRAFT, description="Current status of the report.")
    creation_date: datetime = Field(default_factory=datetime.utcnow, description="Date the report was initially created.")
    last_modified_date: datetime = Field(default_factory=datetime.utcnow, description="Date the report was last modified.")
    publication_date: Optional[datetime] = Field(None, description="Date the report was officially published.")
    
    # Distribution and Impact
    target_audience_cos: List[str] = Field(default_factory=list, description="Target Command Offices or roles for this report (e.g., ['CPO', 'CSO', 'CTO']).")
    sensitivity_level: CKO_SensitivityLevelEnum = Field(default=CKO_SensitivityLevelEnum.INTERNAL, description="Sensitivity level of the report content.")
    tags_keywords: List[str] = Field(default_factory=list, description="Keywords for categorizing and searching the report.")
    
    # Feedback and Follow-up
    feedback_summary: Optional[str] = Field(None, description="Summary of feedback received on this report.")
    related_opportunity_signal_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="IDs of CKO_M006_OpportunitySignal generated from this report.")
    related_threat_alert_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="IDs of CKO_M007_ThreatAlert generated from this report.")

    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, report-specific metadata.")

## 4. Field Descriptions

| Field Name                        | Type                                                      | Description                                                                                                 | Required | Example Value(s)                                                               |
|-----------------------------------|-----------------------------------------------------------|-------------------------------------------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------|
| `report_id`                       | `uuid.UUID`                                               | Unique identifier for the insight report.                                                                   | Yes      | `"f0e1d2c3-b4a5-6789-0123-456789abcdef"`                                       |
| `title`                           | `str`                                                     | Title of the insight report.                                                                                | Yes      | `"Analysis of Emerging AI Trends Q2 2025"`                                       |
| `executive_summary`               | `str`                                                     | A concise overview of the key insights and recommendations.                                                 | Yes      | `"This report highlights three key AI trends..."`                                |
| `introduction_background`         | `Optional[str]`                                           | Background information and context for the report.                                                          | No       | `"Recent advancements in large language models..."`                              |
| `methodology`                     | `Optional[str]`                                           | Description of the methods used for analysis.                                                               | No       | `"Analysis based on 150 curated assets and graph network analysis..."`         |
| `key_findings`                    | `List[Dict[str, Any]]`                                    | List of key findings (structured).                                                                          | Yes      | `[{"finding_text": "...", "supporting_asset_ids": ["c1d2..."], "implications": "..."}]` |
| `detailed_analysis`               | `Optional[str]`                                           | In-depth analysis supporting the findings.                                                                  | No       | `"The data shows a significant correlation..."`                                  |
| `visualizations`                  | `Optional[List[Dict[str, Union[str, HttpUrl]]]]`          | Links to or embedded data for charts, graphs.                                                               | No       | `[{"type": "image", "url": "...", "caption": "Trend Graph"}]`                 |
| `conclusions`                     | `str`                                                     | Overall conclusions drawn from the analysis.                                                                | Yes      | `"The identified AI trends present both opportunities and challenges..."`        |
| `recommendations`                 | `Optional[List[str]]`                                     | Actionable recommendations based on the insights.                                                           | No       | `["Invest in R&D for X technology", "Monitor competitor Y"]`                   |
| `source_asset_ids`                | `List[uuid.UUID]`                                         | List of primary `CKO_M003` asset IDs used.                                                                  | Yes      | `["c1d2e3f4-..."]`                                                              |
| `source_graph_node_ids`           | `Optional[List[uuid.UUID]]`                               | List of key `CKO_M008` graph node IDs referenced.                                                           | No       | `["e1f2a3b4-..."]`                                                              |
| `analysis_query_or_parameters`    | `Optional[str]`                                           | The query or parameters used for generation.                                                                | No       | `"Find all articles related to 'quantum computing' published in last 6 months."` |
| `author_agent_id`                 | `str`                                                     | ID of the ESTRATIX agent who authored the report.                                                           | Yes      | `"CKO_A004"`                                                                   |
| `contributors_ids`                | `Optional[List[str]]`                                     | IDs of other agents or SMEs who contributed.                                                                | No       | `["CTO_A001"]`                                                                 |
| `reviewers_ids`                   | `Optional[List[str]]`                                     | IDs of agents or SMEs who reviewed the report.                                                              | No       | `["CSO_A001"]`                                                                 |
| `report_status`                   | `CKO_ReportStatusEnum`                                    | Current status of the report.                                                                               | Yes      | `"Published"`                                                                  |
| `creation_date`                   | `datetime`                                                | Date the report was initially created.                                                                      | Yes      | `"2025-05-27T10:00:00Z"`                                                      |
| `last_modified_date`              | `datetime`                                                | Date the report was last modified.                                                                          | Yes      | `"2025-05-27T11:00:00Z"`                                                      |
| `publication_date`                | `Optional[datetime]`                                      | Date the report was officially published.                                                                   | No       | `"2025-05-28T09:00:00Z"`                                                      |
| `target_audience_cos`             | `List[str]`                                               | Target Command Offices or roles for this report.                                                            | Yes      | `["CPO", "CSO"]`                                                               |
| `sensitivity_level`               | `CKO_SensitivityLevelEnum`                                | Sensitivity level of the report content.                                                                    | Yes      | `"Confidential"`                                                               |
| `tags_keywords`                   | `List[str]`                                               | Keywords for categorizing and searching the report.                                                         | Yes      | `["AI", "Q2 2025", "Market Analysis"]`                                       |
| `feedback_summary`                | `Optional[str]`                                           | Summary of feedback received on this report.                                                                | No       | `"Positive feedback, request for more detail on X."`                           |
| `related_opportunity_signal_ids`  | `Optional[List[uuid.UUID]]`                               | IDs of `CKO_M006_OpportunitySignal` generated from this report.                                             | No       | `["abc123ef-..."]`                                                              |
| `related_threat_alert_ids`        | `Optional[List[uuid.UUID]]`                               | IDs of `CKO_M007_ThreatAlert` generated from this report.                                                   | No       | `["def456gh-..."]`                                                              |
| `custom_fields`                   | `Optional[Dict[str, Any]]`                                | Flexible dictionary for additional, report-specific metadata.                                               | No       | `{"strategic_initiative_code": "SI_005"}`                                     |

## 5. Relationships to Other Data Models

*   **`source_asset_ids` (references `CKO_M003_CuratedKnowledgeAsset.asset_id`):** The curated knowledge assets that form the basis of the report.
*   **`source_graph_node_ids` (references `CKO_M008_KnowledgeGraphNode.node_id`):** Key entities from the knowledge graph that were analyzed or are central to the report.
*   This report can lead to the creation of `CKO_M006_OpportunitySignal` and `CKO_M007_ThreatAlert` instances.
*   Serves as a primary input for `CPO_F001_StrategicOpportunityToProposalDevelopment`.

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (specifically `CKO_P006_SynthesizeAndReportInsights`). Created by `CKO_A004_KnowledgeAnalystAgent`.
*   **Primary Consuming Flow(s)/Process(es):** `CPO_F001_StrategicOpportunityToProposalDevelopment`, various strategic planning processes within Command Offices, decision-making meetings.
*   **Key Agents Interacting:** `CKO_A004_KnowledgeAnalystAgent` (creates, publishes), Command Officers (CPO, CSO, CEO, etc. - read, review), Strategic Planners from various COs (read).

## 7. Notes / Future Considerations

*   The structure of `key_findings` could be further standardized with specific sub-fields for different types of findings.
*   A workflow for review and approval of reports (`report_status` changes) should be well-defined.
*   Integration with presentation generation tools could be considered for `visualizations`.
*   Mechanisms for tracking the impact or actions taken based on the report's `recommendations` would be valuable.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                                                                                             |
| :------ | :--------- | :--------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M005 to CKO_M005. Updated ID, version, owner, SME, Pydantic enum names, and all internal KNO references to CKO (including CKO_M008). Updated Last Updated date. Added revision history. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the InsightReport data model.                                                                                                                                                 |
