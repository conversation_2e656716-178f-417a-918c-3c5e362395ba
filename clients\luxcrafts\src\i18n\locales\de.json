{"common": {"loading": "Laden...", "error": "<PERSON><PERSON>", "success": "Erfolg", "cancel": "Abbrechen", "confirm": "Bestätigen", "save": "Speichern", "edit": "<PERSON><PERSON><PERSON>", "delete": "Löschen", "back": "Zurück", "next": "<PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "filter": "Filter", "sort": "<PERSON><PERSON><PERSON><PERSON>", "close": "Schließen", "open": "<PERSON><PERSON><PERSON>", "view": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "upload": "Hochladen", "connect": "Verbinden", "disconnect": "<PERSON><PERSON><PERSON>", "wallet": "Wallet", "balance": "<PERSON><PERSON><PERSON><PERSON>", "transaction": "Transaktion", "transactions": "Transaktionen", "address": "<PERSON><PERSON><PERSON>", "amount": "Betrag", "fee": "<PERSON><PERSON><PERSON><PERSON>", "total": "Gesamt", "status": "Status", "pending": "<PERSON><PERSON><PERSON><PERSON>", "completed": "Abgeschlossen", "failed": "Fehlgeschlagen", "approved": "<PERSON><PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "date": "Datum", "time": "Zeit", "name": "Name", "email": "E-Mail", "phone": "Telefon", "location": "<PERSON><PERSON>", "price": "Pre<PERSON>", "rating": "Bewertung", "reviews": "Bewertungen", "availability": "Verfügbarkeit", "book": "Buchen", "booked": "Gebucht", "booking": "<PERSON><PERSON><PERSON>", "bookings": "Buchungen", "service": "Service", "services": "Services", "provider": "<PERSON><PERSON><PERSON>", "providers": "<PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": "Immobilien", "dashboard": "Dashboard", "profile": "Profil", "settings": "Einstellungen", "help": "<PERSON><PERSON><PERSON>", "support": "Support", "contact": "Kontakt", "about": "Über uns", "terms": "Nutzungsbedingungen", "privacy": "Datenschutz", "logout": "Abmelden", "login": "Anmelden", "register": "Registrieren", "signup": "Registrieren", "signin": "Anmelden", "learn_more": "<PERSON><PERSON> er<PERSON>"}, "navigation": {"home": "Startseite", "marketplace": "Marktplatz", "services": "Services", "dashboard": "Dashboard", "tokens": "Token", "nft_marketplace": "NFT-Marktplatz", "defi_pools": "DeFi-Pools", "admin_dashboard": "Admin-Dashboard", "provider_portal": "Anbieter-Portal", "content_studio": "Content-Studio", "property_acquisition": "Immobilienerwerb", "ai_agents": "KI-Agenten", "lux_tokens": "LUX-Token", "dev_tools": "Entwicklertools", "admin_console": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "profile_settings": "Profil-Einstellungen", "sign_out": "Abmelden", "sign_in": "Anmelden"}, "home": {"hero": {"title": "Luxus-Immobilienservices", "subtitle": "Powered by Web3", "description": "Revolution<PERSON>re Plattform, die Immobilieneigentümer mit Premium-Dienstleistern durch Blockchain-Technologie, KI-gestütztes Matching und tokenisierte Belohnungen verbindet.", "cta_primary": "Services jetzt buchen", "cta_secondary": "Marktplatz erkunden"}, "stats": {"properties_managed": "Verwaltete Immobilien", "service_providers": "Dienstleister", "tokens_staked": "LUX-Token gestaked", "satisfaction": "Kundenzufriedenheit"}, "services": {"title": "Premium-Immobilienservices", "description": "Entdecken Sie unser umfassendes Angebot an Luxus-Immobilienservices, alle unterstützt von modernster Technologie und geliefert von zertifizierten Fachkräften.", "property_management": {"name": "Immobilienverwaltung", "description": "Umfassende Immobilienbetreuung mit KI-gestützter Wartungsplanung"}, "luxury_cleaning": {"name": "Luxus-Reinigung", "description": "Premium-Reinigungsservices mit umweltfreundlichen Produkten und White-Glove-Service"}, "landscaping": {"name": "Landschaftsgestaltung & Design", "description": "Professionelle Landschaftsgestaltung mit nachhaltigem Design und intelligenter Bewässerung"}, "remediation": {"name": "Immobiliensanierung", "description": "Expertenrestaurierung bei Wasserschäden, Schimmel und strukturellen Problemen"}, "analytics": {"name": "Investment-Analytik", "description": "KI-gestützte Immobilienbewertung und Analyse von Investitionsmöglichkeiten"}, "crypto_payments": {"name": "Krypto-Zahlungen", "description": "Nahtlose Web3-Zahlungen mit LUX-Token-Belohnungen und NFT-Zertifikaten"}}, "web3": {"title": "Web3-gestütztes Immobilien-Ökosystem", "description": "Erleben Sie die Zukunft der Immobilienservices mit Blockchain-Technologie, tokenisierten Belohnungen und dezentraler Finanzintegration.", "features": {"tokens": {"title": "LUX-Token-Belohnungen", "description": "Verdienen Sie LUX-Token für jede Servicebuchung, Immobilienverbesserung und Plattformteilnahme. Staken Sie Token für Premium-Vorteile."}, "tokenization": {"title": "Immobilien-Tokenisierung", "description": "Tokenisieren Sie Immobilienvermögen für Bruchteilseigentum, transparente Mieteinnahmenverteilung und liquide Immobilieninvestitionen."}, "nft": {"title": "NFT-Zertifikate", "description": "Erhalten Sie verifizierbare NFT-Zertifikate für Immobilienverbesserungen, Serviceabschlüsse und Mitgliedschaftsstufen."}}, "dashboard": {"title": "Token-Dashboard-Vorschau", "balance": "LUX-Guthaben", "staked": "Gestaketer Betrag", "rewards": "<PERSON><PERSON>", "nfts": "Besessene NFTs", "cta": "Token-Dashboard aufrufen"}}, "cta": {"title": "Be<PERSON><PERSON>, Ihr Immobilienerlebnis zu transformieren?", "description": "Schl<PERSON>ßen Sie sich Tausenden von Immobilieneigentümern an, die Luxcrafts für Premium-Services und innovative Web3-Belohnungen vertrauen.", "primary": "Services buchen starten", "secondary": "Anbieter werden"}}, "payment": {"web2": {"title": "Traditionelles Banking", "description": "Zahlen Sie mit Kreditkarten, Banküberweisungen und ACH-Zahlungen", "available_in": "Verfügbar in den Vereinigten Staaten", "methods": {"credit_card": "Kreditkarte", "bank_transfer": "Banküberweisung", "ach": "ACH-Zahlung", "paypal": "PayPal"}}, "web3": {"title": "Kryptowährungs-Zahlungen", "description": "Zahlen Sie mit Bitcoin, Ethereum, Stablecoins und LUX-Token", "available_globally": "Global verfügbar", "methods": {"bitcoin": "Bitcoin", "ethereum": "Ethereum", "usdc": "USDC", "usdt": "USDT", "lux_token": "LUX-Token"}}, "toggle": {"web2": "Traditionelles Banking", "web3": "Krypto-Zahlungen", "switch_to_web2": "Zu traditionellem Banking wechseln", "switch_to_web3": "Zu Krypto-Zahlungen wechseln"}}, "accessibility": {"skip_to_content": "Zum Hauptinhalt springen", "menu": "<PERSON><PERSON>", "close_menu": "<PERSON><PERSON> sch<PERSON>ßen", "open_menu": "<PERSON><PERSON>", "language_selector": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "current_language": "Aktuelle Sprache", "change_language": "Sprache ändern", "high_contrast": "<PERSON><PERSON>", "font_size": "Schriftgröße", "increase_font": "Sc<PERSON><PERSON> vergrößern", "decrease_font": "Sc<PERSON><PERSON> verkleinern", "reset_font": "Schrift zurücksetzen", "screen_reader": "Screenreader-Unterstützung", "keyboard_navigation": "Tastaturnavigation", "focus_indicator": "Fokus-Indikator", "aria_label": "ARIA-Label", "alt_text": "Alt-Text"}, "errors": {"general": "Ein unerwarteter Fehler ist aufgetreten", "network": "Netzwerkfehler", "timeout": "Anfrage-Timeout", "unauthorized": "Nicht autorisiert", "forbidden": "<PERSON><PERSON><PERSON> verweigert", "not_found": "Nicht gefunden", "server_error": "<PERSON><PERSON><PERSON>", "validation": "Validierungsfehler", "required_field": "<PERSON><PERSON> ist erford<PERSON>lich", "invalid_email": "Ungültige E-Mail-Adresse", "invalid_phone": "Ungültige Telefonnummer", "password_too_short": "Passwort zu kurz", "passwords_dont_match": "Passwörter stimmen nicht überein", "wallet_not_connected": "Wallet nicht verbunden", "insufficient_balance": "Unzureichendes G<PERSON>aben", "transaction_failed": "Transaktion fehlgeschlagen", "file_too_large": "<PERSON><PERSON> zu groß", "invalid_file_type": "Ungültiger Dateityp", "upload_failed": "Upload fehlgeschlagen", "connection_lost": "Verbindung verloren", "session_expired": "Sitzung abgelaufen", "rate_limit_exceeded": "Rate-Limit überschritten", "maintenance_mode": "Wartungsmodus", "feature_unavailable": "Feature nicht verfügbar", "geolocation_denied": "Standortzugriff verweigert", "camera_access_denied": "Kamerazugriff verweigert", "microphone_access_denied": "Mikrofonzugriff verweigert", "notification_permission_denied": "Benachrichtigungsberechtigung verweigert", "storage_quota_exceeded": "Speicherplatz-Kontingent überschritten", "browser_not_supported": "Browser nicht unterstützt", "javascript_disabled": "JavaScript deaktiviert", "cookies_disabled": "<PERSON><PERSON> de<PERSON>", "third_party_service_unavailable": "Drittanbieter-Service nicht verfügbar", "api_key_invalid": "Ungültiger API-Schlüssel", "quota_exceeded": "<PERSON>ntin<PERSON> überschritten", "service_temporarily_unavailable": "Service vorübergehend nicht verfügbar", "data_corruption": "Datenkorruption", "sync_failed": "Synchronisation fehlgeschlagen", "backup_failed": "Backup fehlgeschlagen", "restore_failed": "Wiederherstellung fehlgeschlagen", "export_failed": "Export fehlgeschlagen", "import_failed": "Import fehlgeschlagen", "parsing_error": "Parsing<PERSON><PERSON><PERSON>", "encoding_error": "Kodierungsfehler", "decoding_error": "Dekodierungsfehler", "compression_failed": "Komprimierung fehlgeschlagen", "decompression_failed": "Dekomprimierung fehlgeschlagen", "encryption_failed": "Verschlüsselung fehlgeschlagen", "decryption_failed": "Entschlüsselung fehlgeschlagen", "signature_verification_failed": "Signaturverifikation fehlgeschlagen", "certificate_invalid": "Ungültiges Zertifikat", "ssl_handshake_failed": "SSL-Handshake fehlgeschlagen", "dns_resolution_failed": "DNS-Auflösung fehlgeschlagen", "proxy_error": "Proxy-<PERSON><PERSON>", "firewall_blocked": "<PERSON> Firewall blockiert", "antivirus_blocked": "Von Antivirus blockiert", "content_blocked": "In<PERSON> blockiert", "region_blocked": "Region blockiert", "age_verification_required": "Altersverifikation er<PERSON>lich", "terms_acceptance_required": "Zustimmu<PERSON> zu Bedingungen erforderlich", "privacy_consent_required": "Datenschutz-Einverständ<PERSON>", "email_verification_required": "E-Mail-Verifikation erforderlich", "phone_verification_required": "Telefon-Verifikation erforderlich", "identity_verification_required": "Identitätsverifikation erforderlich", "two_factor_required": "Zwei-Faktor-Authentifizierung erforderlich", "captcha_required": "CAPTCHA er<PERSON>lich", "human_verification_required": "Menschliche Verifikation erforderlich", "account_locked": "<PERSON><PERSON> g<PERSON>", "account_suspended": "<PERSON><PERSON> susp<PERSON><PERSON>", "account_deleted": "<PERSON><PERSON>", "subscription_expired": "Abonnement abgelaufen", "subscription_cancelled": "Abonnement gekündigt", "payment_required": "Zahlung er<PERSON>lich", "payment_failed": "Zahlung fehlgeschlagen", "refund_failed": "Rückerstattung fehlgeschlagen", "chargeback_issued": "Rückbuchung ausgestellt", "fraud_detected": "Betrug erkannt", "suspicious_activity": "Verdächtige Aktivität", "security_breach": "Sicherheitsverletzung", "data_breach": "Datenverletzung", "privacy_violation": "Datenschutzverletzung", "copyright_violation": "Urheberrechtsverletzung", "trademark_violation": "Markenrechtsverletzung", "patent_violation": "Patentverletzung", "license_violation": "Lizenzverletzung", "terms_violation": "Verstoß gegen Bedingungen", "community_guidelines_violation": "Verstoß gegen Community-Richtlinien", "spam_detected": "<PERSON><PERSON> er<PERSON>", "abuse_detected": "<PERSON><PERSON><PERSON> er<PERSON>", "harassment_detected": "Belästigung erkannt", "hate_speech_detected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "violence_detected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "illegal_content_detected": "Illegaler Inhalt erkannt", "inappropriate_content": "Unangemessener Inhalt", "content_removed": "Inhalt entfernt", "content_flagged": "<PERSON><PERSON> markiert", "content_under_review": "Inhalt wird überprüft", "moderation_required": "Moderation erford<PERSON>lich", "manual_review_required": "<PERSON><PERSON> Überprüfu<PERSON>", "approval_pending": "Genehmigung ausstehend", "verification_pending": "Verifikation ausstehend", "processing_delayed": "Verarbeitung verzögert", "system_overloaded": "System überlastet", "capacity_exceeded": "Kapazität überschritten", "resource_unavailable": "Ressource nicht verfügbar", "dependency_failed": "Abhängigkeit fehlgeschlagen", "integration_failed": "Integration fehlgeschlagen", "migration_failed": "Migration fehlgeschlagen", "rollback_failed": "Rollback fehlgeschlagen", "deployment_failed": "Deployment fehlgeschlagen", "configuration_error": "<PERSON>n<PERSON><PERSON><PERSON><PERSON><PERSON>", "environment_error": "Umgebungsfehler", "compatibility_issue": "Kompatibilitätsproblem", "version_mismatch": "Versionskonflikt", "dependency_conflict": "Abhängigkeitskonflikt", "library_missing": "Bibliothek fehlt", "plugin_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "extension_error": "Erweiterungs-Fehler", "driver_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hardware_error": "Hardware-<PERSON><PERSON>", "memory_error": "Speicher<PERSON><PERSON><PERSON>", "disk_error": "Festplatten-Fehler", "cpu_error": "CPU-<PERSON><PERSON>", "gpu_error": "GPU-<PERSON><PERSON>", "sensor_error": "Sensor<PERSON><PERSON><PERSON>", "calibration_error": "Kalibrierungsfehler", "measurement_error": "<PERSON><PERSON><PERSON><PERSON>", "calculation_error": "Be<PERSON><PERSON><PERSON>ngsfehler", "algorithm_error": "Algorithmus<PERSON><PERSON><PERSON>", "model_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "prediction_error": "Vorhersage-<PERSON><PERSON>", "classification_error": "Klassifizierungsfehler", "recognition_error": "Erk<PERSON>nungsfehler", "detection_error": "Erk<PERSON>nungsfehler", "tracking_error": "Tracking-<PERSON><PERSON>", "localization_error": "Lokalisierungsfehler", "navigation_error": "Navigationsfehler", "routing_error": "Routing-<PERSON><PERSON>", "pathfinding_error": "Pfadfindungs-Fehler", "optimization_error": "Optimierungsfehler", "performance_degraded": "Leistung verschlechtert", "quality_degraded": "Qualität verschlechtert", "accuracy_degraded": "Genauigkeit verschlechtert", "precision_degraded": "Präzision verschlechtert", "reliability_degraded": "Zuverlässigkeit verschlechtert", "stability_degraded": "Stabilität verschlechtert", "consistency_degraded": "Konsis<PERSON><PERSON> verschlechtert", "availability_degraded": "Verfügbarkeit verschlechtert", "scalability_limited": "Skalierbarkeit begrenzt", "throughput_limited": "Durchsatz begrenzt", "bandwidth_limited": "Bandbreite begrenzt", "latency_high": "<PERSON><PERSON> Latenz", "jitter_high": "<PERSON><PERSON>", "packet_loss": "Paketverlust", "signal_weak": "Schwaches Signal", "interference_detected": "Interferenz er<PERSON>nt", "noise_high": "<PERSON><PERSON>", "distortion_detected": "Verzerrung erkannt", "corruption_detected": "Korruption erkannt", "anomaly_detected": "<PERSON><PERSON><PERSON>", "outlier_detected": "<PERSON><PERSON><PERSON>i<PERSON><PERSON> er<PERSON>", "threshold_exceeded": "Schwellenwert überschritten", "limit_reached": "<PERSON>it erreicht", "boundary_exceeded": "<PERSON><PERSON><PERSON> ü<PERSON>ch<PERSON>", "range_exceeded": "Bereich überschritten", "constraint_violated": "Einschränkung verletzt", "rule_violated": "Regel verletzt", "policy_violated": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "standard_violated": "Standard verletzt", "protocol_violated": "Protokoll verletzt", "specification_violated": "Spezifikation verletzt", "contract_violated": "<PERSON><PERSON><PERSON> verle<PERSON>", "agreement_violated": "Vereinbarung verletzt", "warranty_voided": "<PERSON><PERSON><PERSON>", "support_expired": "Support abgelaufen", "license_expired": "Lizenz abgelaufen", "certificate_expired": "Zertifikat abgelaufen", "token_expired": "Token abgelaufen", "session_timeout": "Sitzungs-Timeout", "connection_timeout": "Verbindungs-Timeout", "request_timeout": "Anfrage-Timeout", "response_timeout": "Antwort-Timeout", "operation_timeout": "Vorgangs-Timeout", "transaction_timeout": "Transaktions-Timeout", "lock_timeout": "S<PERSON>r-Timeout", "wait_timeout": "Warte-Timeout", "idle_timeout": "<PERSON><PERSON><PERSON><PERSON>-Timeout", "keepalive_timeout": "Keepalive-Timeout", "heartbeat_timeout": "Heartbeat-Timeout", "ping_timeout": "Ping-Timeout", "health_check_failed": "Gesundheitsprüfung fehlgeschlagen", "readiness_check_failed": "Bereitschaftsprüfung fehlgeschlagen", "liveness_check_failed": "Lebendigkeitsprüfung fehlgeschlagen", "startup_failed": "Start fehlgeschlagen", "shutdown_failed": "Herunterfahren fehlgeschlagen", "restart_required": "<PERSON><PERSON><PERSON><PERSON>", "reboot_required": "<PERSON><PERSON><PERSON><PERSON>", "update_required": "Update er<PERSON><PERSON><PERSON>", "upgrade_required": "Upgrade erforderlich", "patch_required": "<PERSON>", "hotfix_required": "<PERSON><PERSON><PERSON>", "maintenance_required": "<PERSON><PERSON><PERSON>", "cleanup_required": "<PERSON><PERSON>ini<PERSON><PERSON>", "optimization_required": "Optimierung erforderlich", "reconfiguration_required": "Neukonfiguration er<PERSON><PERSON>lich", "reinstallation_required": "Neuinstallation er<PERSON><PERSON>lich", "factory_reset_required": "Werksreset er<PERSON>lich", "data_recovery_required": "Datenwiederherstel<PERSON> er<PERSON>lich", "backup_restoration_required": "Backup-Wiederherstel<PERSON>lich", "system_recovery_required": "Systemwiederherstellung erforderlich", "disaster_recovery_initiated": "Notfallwiederherstellung eingeleitet", "emergency_mode_activated": "Notfallmodus aktiviert", "safe_mode_activated": "Abgesicherter Modus aktiviert", "debug_mode_activated": "Debug-Modus aktiviert", "test_mode_activated": "Testmodus aktiviert", "demo_mode_activated": "Demo-Modus aktiviert", "readonly_mode_activated": "Nur-Lese-Modus aktiviert", "offline_mode_activated": "Offline-Modus aktiviert", "airplane_mode_activated": "Flugmodus aktiviert", "power_saving_mode_activated": "Energiesparmodus aktiviert", "low_power_mode_activated": "Niedriger Energiemodus aktiviert", "battery_low": "<PERSON><PERSON><PERSON> schwach", "battery_critical": "Batterie kritisch", "battery_empty": "<PERSON><PERSON><PERSON> leer", "charging_required": "<PERSON><PERSON>", "power_disconnected": "Strom getrennt", "power_failure": "Stromausfall", "ups_activated": "USV aktiviert", "generator_activated": "Generator aktiviert", "cooling_failure": "Kühlungsausfall", "overheating_detected": "Überhitzung erkannt", "temperature_critical": "Temperatur kritisch", "humidity_critical": "Luftfeuchtigkeit kritisch", "pressure_critical": "Druck kritisch", "vibration_excessive": "Übermäßige Vibration", "shock_detected": "<PERSON><PERSON><PERSON> er<PERSON>", "impact_detected": "<PERSON><PERSON><PERSON><PERSON>", "motion_detected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "intrusion_detected": "Eindringen erkannt", "tampering_detected": "Manipulation erkannt", "unauthorized_access": "Unbefugter Zugriff", "security_alert": "Sicherheitsalarm", "fire_alarm": "Feueralarm", "smoke_detected": "<PERSON><PERSON> er<PERSON>", "gas_leak_detected": "<PERSON><PERSON><PERSON> er<PERSON>", "water_leak_detected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "flood_detected": "Überschwemmung erkannt", "earthquake_detected": "<PERSON><PERSON><PERSON><PERSON> er<PERSON>", "storm_warning": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "weather_alert": "Wetteralarm", "emergency_broadcast": "Notfallsendung", "evacuation_order": "Evakuierungsbefehl", "shelter_in_place": "An Ort und Stelle bleiben", "lockdown_initiated": "<PERSON><PERSON><PERSON> e<PERSON>t", "quarantine_required": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isolation_required": "Isolation erford<PERSON>lich", "medical_attention_required": "Medizinische Hilfe erforderlich", "first_aid_required": "<PERSON><PERSON><PERSON>", "emergency_services_contacted": "Notdienste kontaktiert", "help_on_the_way": "Hilfe ist unterwegs", "rescue_in_progress": "Rettung im Gange", "situation_under_control": "Situation unter Kontrolle", "all_clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "normal_operations_resumed": "Normaler Betrieb wieder aufgenommen"}}