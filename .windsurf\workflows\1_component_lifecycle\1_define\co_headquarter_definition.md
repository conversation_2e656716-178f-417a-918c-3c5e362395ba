---
description: "Guides the definition of a new ESTRATIX Command Officer Headquarters, detailing its mandate, responsibilities, structure, and operational context."
---

# WORKFLOW: Define New ESTRATIX Command Officer Headquarters

**Objective**: To produce a comprehensive and standardized definition document for a Command Officer's headquarters, located at `docs/organization/[Office_ID]_Headquarters.md`, and register it in the organization matrix.

**Agentic Orchestrator**: `AGENT_SystemArchitect_Expert`, `CHRO_Agent`

**Dependencies**:

- `docs/templates/estratix_officer_headquarters_template.md`
- `docs/matrices/organization_matrix.md`

---

## Phase 1: Collect Inputs

**Action**: The orchestrating agent must gather the necessary information for the new headquarters before proceeding.

- **Input `[Office_ID]`**: The unique acronym for the office (e.g., `CTO`).
- **Input `[Office_FullName]`**: The full name of the office (e.g., `Chief Technology Officer`).
- **Input `[Target_File_Path]`**: The **absolute** destination path for the new definition file (e.g., [c:\Users\<USER>\Downloads\PROJECTS\ESTRATIX\DEVELOPMENT\projectManagement\estratix_v3\docs\organization\CTO_Headquarters.md](cci:7://file:///c:/Users/<USER>/Downloads/PROJECTS/ESTRATIX/DEVELOPMENT/projectManagement/estratix_v3/docs/organization/CTO_Headquarters.md:0:0-0:0)).
- **Input `[Date]`**: The current date in `YYYY-MM-DD` format.

---

## Phase 2: Generate Definition from Template

**Action**: The orchestrating agent will create the new definition file by copying and populating the standard template using the inputs from Phase 1.

1.  **Copy the template file to the target destination.**

    **Guidance**: The agent should execute a `run_command` tool call to copy the template. The `CommandLine` should use relative paths from the `Cwd`.

    **Tool Call Example**:

    ```xml
    
    ```

2.  **Replace placeholders in the new definition file.**

    **Guidance**: The agent should execute a `replace_file_content` tool call, substituting all variables with the values collected in Phase 1.

    **Tool Call Example**:

    ```xml