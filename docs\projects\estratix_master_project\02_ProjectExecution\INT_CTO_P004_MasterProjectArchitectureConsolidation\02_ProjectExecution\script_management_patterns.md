# ESTRATIX Master Builder Agent - Script Management Patterns

**Version:** 1.0  
**Date:** 2025-07-21  
**Status:** Active  
**Author:** ESTRATIX Infrastructure Team

## 📋 Overview

This document defines the systematic patterns and best practices for the Master Builder Agent to manage, generate, and maintain infrastructure and application scripts within the ESTRATIX framework. It ensures consistent script creation, registration, and integration with agentic frameworks.

## 🎯 Core Principles

### **1. Systematic Script Registration**
- All scripts MUST be registered in `docs/models/script_matrix.md`
- Follow ESTRATIX naming conventions: `S-[CATEGORY]-[ID]`
- Associate with specific Command Office processes
- Maintain version control and status tracking

### **2. DDD Hexagonal Architecture Alignment**
- Scripts belong to appropriate layers:
  - **Domain Scripts:** Business logic automation
  - **Infrastructure Scripts:** System provisioning, deployment
  - **Application Scripts:** Service orchestration, workflow automation

### **3. Agentic Framework Integration**
- Scripts serve as MCP tools for agent operations
- Enable autonomous infrastructure management
- Support distributed systems orchestration
- Integrate with CI/CD and GitOps workflows

## 🏗️ Script Categories & Patterns

### **Infrastructure Scripts (S-INFRA-###)**

```bash
#!/bin/bash
# ESTRATIX Infrastructure Script Template
# ============================================================================
# Script: [SCRIPT_NAME]
# Purpose: [BRIEF_DESCRIPTION]
# Category: Infrastructure
# Command Office: CTO
# Version: 1.0
# ============================================================================

set -euo pipefail

# Configuration
readonly SCRIPT_NAME="[SCRIPT_NAME]"
readonly VERSION="1.0"
readonly LOG_FILE="/var/log/estratix/${SCRIPT_NAME}.log"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# Logging functions
log_info() { echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"; }

# Main execution function
main() {
    log_info "Starting $SCRIPT_NAME v$VERSION..."
    
    # Script logic here
    
    log_success "$SCRIPT_NAME completed successfully"
}

# Execute main function
main "$@"
```

### **Application Scripts (S-APP-###)**

```python
#!/usr/bin/env python3
"""
ESTRATIX Application Script Template
====================================
Script: [SCRIPT_NAME]
Purpose: [BRIEF_DESCRIPTION]
Category: Application
Command Office: [CO_ACRONYM]
Version: 1.0
"""

import logging
import sys
from pathlib import Path
from typing import Dict, List, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'/var/log/estratix/{Path(__file__).stem}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ESTRATIXApplicationScript:
    """Base class for ESTRATIX application scripts"""
    
    def __init__(self, script_name: str, version: str = "1.0"):
        self.script_name = script_name
        self.version = version
        self.config = self.load_config()
    
    def load_config(self) -> Dict:
        """Load script configuration"""
        # Implementation here
        return {}
    
    def validate_prerequisites(self) -> bool:
        """Validate script prerequisites"""
        # Implementation here
        return True
    
    def execute(self) -> bool:
        """Main execution logic"""
        logger.info(f"Starting {self.script_name} v{self.version}...")
        
        if not self.validate_prerequisites():
            logger.error("Prerequisites validation failed")
            return False
        
        try:
            # Script logic here
            logger.info(f"{self.script_name} completed successfully")
            return True
        except Exception as e:
            logger.error(f"{self.script_name} failed: {str(e)}")
            return False

def main():
    """Main execution function"""
    script = ESTRATIXApplicationScript("[SCRIPT_NAME]")
    success = script.execute()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
```

### **Maintenance Scripts (S-MAINT-###)**

```python
#!/usr/bin/env python3
"""
ESTRATIX Maintenance Script Template
===================================
Script: [SCRIPT_NAME]
Purpose: [BRIEF_DESCRIPTION]
Category: Maintenance
Command Office: CIO
Version: 1.0
"""

import os
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple

class ESTRATIXMaintenanceScript:
    """Base class for ESTRATIX maintenance scripts"""
    
    def __init__(self, script_name: str):
        self.script_name = script_name
        self.project_root = Path(__file__).parent.parent.parent.parent
        self.fixes_applied = []
        self.errors_found = []
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
    
    def analyze_system(self) -> Dict:
        """Analyze system for maintenance issues"""
        # Implementation here
        return {}
    
    def apply_fixes(self) -> None:
        """Apply maintenance fixes"""
        # Implementation here
        pass
    
    def generate_report(self) -> str:
        """Generate maintenance report"""
        report = f"""
ESTRATIX {self.script_name} Report
{'=' * 50}
Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

FIXES APPLIED ({len(self.fixes_applied)}):
{chr(10).join(f"✓ {fix}" for fix in self.fixes_applied)}

ERRORS ENCOUNTERED ({len(self.errors_found)}):
{chr(10).join(f"✗ {error}" for error in self.errors_found)}

SUMMARY:
- Total fixes applied: {len(self.fixes_applied)}
- Total errors: {len(self.errors_found)}
- Status: {"SUCCESS" if len(self.errors_found) == 0 else "PARTIAL SUCCESS"}
"""
        return report
    
    def run_maintenance(self) -> str:
        """Run complete maintenance process"""
        self.logger.info(f"Starting {self.script_name}...")
        
        # Analyze system
        analysis = self.analyze_system()
        
        # Apply fixes
        self.apply_fixes()
        
        # Generate report
        report = self.generate_report()
        
        # Save report
        report_path = self.project_root / f"{self.script_name.lower()}_report.txt"
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        self.logger.info(f"Maintenance completed. Report saved to: {report_path}")
        return report

def main():
    """Main execution function"""
    script = ESTRATIXMaintenanceScript("[SCRIPT_NAME]")
    report = script.run_maintenance()
    print(report)

if __name__ == "__main__":
    main()
```

## 🔧 Script Generation Patterns

### **1. Infrastructure Provisioning Scripts**

```bash
# Pattern: Multi-VPS cluster setup
generate_cluster_script() {
    local cluster_type="$1"  # kubernetes, docker-swarm, hybrid
    local node_count="$2"
    local security_level="$3"  # basic, enhanced, maximum
    
    # Generate cluster-specific configuration
    # Apply security hardening based on level
    # Configure monitoring and observability
    # Setup backup and recovery procedures
}
```

### **2. Application Deployment Scripts**

```python
def generate_deployment_script(
    app_type: str,  # fastapi, react, agent, microservice
    deployment_target: str,  # kubernetes, docker, serverless
    environment: str,  # development, staging, production
    scaling_config: Dict  # horizontal, vertical, auto-scaling
) -> str:
    """Generate application deployment script"""
    
    # Configure deployment pipeline
    # Setup environment-specific configurations
    # Apply scaling and resource management
    # Integrate monitoring and logging
    
    return deployment_script
```

### **3. Maintenance Automation Scripts**

```python
def generate_maintenance_script(
    maintenance_type: str,  # cleanup, optimization, security, backup
    schedule: str,  # daily, weekly, monthly, on-demand
    scope: List[str],  # components to maintain
    notification_channels: List[str]  # alert channels
) -> str:
    """Generate maintenance automation script"""
    
    # Define maintenance procedures
    # Configure scheduling and automation
    # Setup monitoring and alerting
    # Generate reports and metrics
    
    return maintenance_script
```

## 📊 Script Registration & Management

### **Automatic Registration Pattern**

```python
def register_script_in_matrix(
    script_id: str,
    description: str,
    source_location: str,
    associated_service: str,
    language: str,
    version: str,
    status: str = "Active"
) -> None:
    """Automatically register script in matrix"""
    
    matrix_path = Path("docs/models/script_matrix.md")
    
    # Read current matrix
    with open(matrix_path, 'r') as f:
        content = f.read()
    
    # Add new entry
    new_entry = f"| {script_id} | {description} | {source_location} | {associated_service} | {language} | {version} | {status} |"
    
    # Update matrix file
    # Maintain alphabetical order by script_id
    # Validate entry format
    
    # Save updated matrix
    with open(matrix_path, 'w') as f:
        f.write(updated_content)
```

### **Script Validation Pattern**

```python
def validate_script_compliance(script_path: Path) -> Tuple[bool, List[str]]:
    """Validate script compliance with ESTRATIX standards"""
    
    issues = []
    
    # Check naming convention
    if not script_path.name.startswith(('S-', 's-')):
        issues.append("Script name must follow S-[CATEGORY]-[ID] pattern")
    
    # Check header documentation
    with open(script_path, 'r') as f:
        content = f.read()
        
    if "ESTRATIX" not in content[:500]:
        issues.append("Missing ESTRATIX header documentation")
    
    # Check logging implementation
    if script_path.suffix == '.sh' and 'log_info' not in content:
        issues.append("Missing logging functions")
    
    # Check error handling
    if 'set -euo pipefail' not in content and script_path.suffix == '.sh':
        issues.append("Missing bash error handling")
    
    return len(issues) == 0, issues
```

## 🚀 Integration with Agentic Frameworks

### **MCP Tool Integration**

```python
class ScriptExecutionTool:
    """MCP tool for script execution by agents"""
    
    def __init__(self, script_registry: Dict):
        self.script_registry = script_registry
    
    def execute_script(
        self,
        script_id: str,
        parameters: Dict,
        execution_context: str
    ) -> Dict:
        """Execute script with agent context"""
        
        # Validate script exists and is active
        # Check agent permissions
        # Prepare execution environment
        # Execute script with monitoring
        # Return results and logs
        
        return {
            "status": "success",
            "output": "Script execution output",
            "logs": "Execution logs",
            "metrics": "Performance metrics"
        }
```

### **GitOps Integration**

```yaml
# .github/workflows/script-deployment.yml
name: ESTRATIX Script Deployment

on:
  push:
    paths:
      - 'src/infrastructure/deployment/scripts/**'
      - 'docs/models/script_matrix.md'

jobs:
  validate-scripts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Validate Script Compliance
        run: uv run python scripts/validate_script_compliance.py
      
  deploy-scripts:
    needs: validate-scripts
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to Infrastructure
        run: |
          # Copy scripts to target servers
          # Update script registry
          # Restart dependent services
          # Validate deployment
```

## 📈 Performance & Monitoring

### **Script Execution Metrics**

```python
class ScriptMetrics:
    """Track script execution metrics"""
    
    def __init__(self):
        self.execution_times = {}
        self.success_rates = {}
        self.resource_usage = {}
    
    def track_execution(
        self,
        script_id: str,
        execution_time: float,
        success: bool,
        resources: Dict
    ) -> None:
        """Track script execution metrics"""
        
        # Update execution times
        # Calculate success rates
        # Monitor resource usage
        # Generate alerts for anomalies
```

### **Automated Optimization**

```python
def optimize_script_performance(script_path: Path) -> List[str]:
    """Analyze and suggest script optimizations"""
    
    optimizations = []
    
    # Analyze execution patterns
    # Identify bottlenecks
    # Suggest improvements
    # Generate optimized version
    
    return optimizations
```

## 🔒 Security & Compliance

### **Security Validation**

```python
def validate_script_security(script_path: Path) -> Tuple[bool, List[str]]:
    """Validate script security compliance"""
    
    security_issues = []
    
    # Check for hardcoded credentials
    # Validate input sanitization
    # Check privilege escalation
    # Verify secure communication
    
    return len(security_issues) == 0, security_issues
```

### **Audit Trail**

```python
class ScriptAuditTrail:
    """Maintain audit trail for script operations"""
    
    def log_script_execution(
        self,
        script_id: str,
        executor: str,
        parameters: Dict,
        result: Dict
    ) -> None:
        """Log script execution for audit"""
        
        audit_entry = {
            "timestamp": datetime.now().isoformat(),
            "script_id": script_id,
            "executor": executor,
            "parameters": parameters,
            "result": result,
            "compliance_status": "validated"
        }
        
        # Store in audit database
        # Generate compliance reports
        # Alert on security violations
```

## 📚 Best Practices Summary

### **1. Script Development**
- Follow ESTRATIX naming conventions
- Implement comprehensive logging
- Include error handling and validation
- Document purpose and usage
- Register in script matrix

### **2. Integration**
- Design for agentic framework compatibility
- Support MCP tool integration
- Enable GitOps workflows
- Implement monitoring and metrics
- Maintain security compliance

### **3. Maintenance**
- Regular security audits
- Performance optimization
- Version control and updates
- Documentation maintenance
- Compliance validation

---

**This document serves as the foundation for Master Builder Agent script management within the ESTRATIX framework, ensuring systematic, secure, and efficient automation across all infrastructure and application operations.**
