import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  ChartBarIcon,
  CogIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowDownTrayIcon,
  BellIcon,
  ClockIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  UserIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  ServerIcon,
  CpuChipIcon,
  CircleStackIcon,
  WifiIcon,
  LockClosedIcon,
  KeyIcon,
  BanknotesIcon,
  TrophyIcon,
  StarIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

const platformStats = {
  totalUsers: 12847,
  activeUsers: 8932,
  totalProviders: 1247,
  activeProviders: 892,
  totalBookings: 45632,
  completedBookings: 43891,
  totalRevenue: 2847392.50,
  monthlyRevenue: 284739.25,
  averageRating: 4.7,
  systemUptime: 99.8,
};

const recentUsers = [
  {
    id: 1,
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    role: 'Property Owner',
    status: 'active',
    joinDate: '2024-01-15',
    lastActive: '2024-01-18',
    totalBookings: 12,
    totalSpent: 2840.50,
  },
  {
    id: 2,
    name: 'Michael Chen',
    email: '<EMAIL>',
    role: 'Service Provider',
    status: 'active',
    joinDate: '2024-01-10',
    lastActive: '2024-01-18',
    totalBookings: 45,
    totalEarned: 8920.75,
  },
  {
    id: 3,
    name: 'Emily Davis',
    email: '<EMAIL>',
    role: 'Property Manager',
    status: 'pending',
    joinDate: '2024-01-17',
    lastActive: '2024-01-17',
    totalBookings: 0,
    totalSpent: 0,
  },
  {
    id: 4,
    name: 'Robert Wilson',
    email: '<EMAIL>',
    role: 'Real Estate Agent',
    status: 'suspended',
    joinDate: '2024-01-05',
    lastActive: '2024-01-16',
    totalBookings: 8,
    totalSpent: 1240.00,
  },
];

const systemAlerts = [
  {
    id: 1,
    type: 'warning',
    title: 'High Server Load',
    message: 'Server CPU usage is at 85%. Consider scaling resources.',
    timestamp: '2024-01-18 14:30',
    status: 'active',
  },
  {
    id: 2,
    type: 'info',
    title: 'Scheduled Maintenance',
    message: 'Database maintenance scheduled for tonight at 2:00 AM EST.',
    timestamp: '2024-01-18 12:00',
    status: 'active',
  },
  {
    id: 3,
    type: 'error',
    title: 'Payment Gateway Issue',
    message: 'Stripe webhook failed. Some payments may not be processed.',
    timestamp: '2024-01-18 10:15',
    status: 'resolved',
  },
  {
    id: 4,
    type: 'success',
    title: 'Backup Completed',
    message: 'Daily database backup completed successfully.',
    timestamp: '2024-01-18 03:00',
    status: 'resolved',
  },
];

const revenueData = [
  { month: 'Jul', revenue: 180000, users: 8500 },
  { month: 'Aug', revenue: 210000, users: 9200 },
  { month: 'Sep', revenue: 195000, users: 9800 },
  { month: 'Oct', revenue: 240000, users: 10500 },
  { month: 'Nov', revenue: 275000, users: 11200 },
  { month: 'Dec', revenue: 260000, users: 11800 },
  { month: 'Jan', revenue: 285000, users: 12400 },
];

const userGrowthData = [
  { date: '2024-01-01', users: 11800 },
  { date: '2024-01-05', users: 11950 },
  { date: '2024-01-10', users: 12100 },
  { date: '2024-01-15', users: 12400 },
  { date: '2024-01-18', users: 12847 },
];

const serviceDistribution = [
  { name: 'Cleaning', value: 35, color: '#3b82f6' },
  { name: 'Maintenance', value: 28, color: '#10b981' },
  { name: 'Landscaping', value: 18, color: '#f59e0b' },
  { name: 'Security', value: 12, color: '#8b5cf6' },
  { name: 'Other', value: 7, color: '#ef4444' },
];

const systemMetrics = {
  serverLoad: 72,
  memoryUsage: 68,
  diskUsage: 45,
  networkLatency: 23,
  activeConnections: 1247,
  requestsPerMinute: 2840,
};

export default function AdminConsole() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedUser, setSelectedUser] = useState(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [userFilter, setUserFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: ChartBarIcon },
    { id: 'users', name: 'User Management', icon: UserGroupIcon },
    { id: 'analytics', name: 'Analytics', icon: ChartBarIcon },
    { id: 'system', name: 'System Health', icon: ServerIcon },
    { id: 'security', name: 'Security', icon: ShieldCheckIcon },
    { id: 'settings', name: 'Settings', icon: CogIcon },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'suspended': return 'text-red-600 bg-red-100';
      case 'inactive': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getAlertColor = (type) => {
    switch (type) {
      case 'error': return 'text-red-600 bg-red-100 border-red-200';
      case 'warning': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'info': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'success': return 'text-green-600 bg-green-100 border-green-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getAlertIcon = (type) => {
    switch (type) {
      case 'error': return XCircleIcon;
      case 'warning': return ExclamationTriangleIcon;
      case 'info': return BellIcon;
      case 'success': return CheckCircleIcon;
      default: return BellIcon;
    }
  };

  const filteredUsers = recentUsers.filter(user => {
    const matchesFilter = userFilter === 'all' || user.status === userFilter;
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Admin Console
              </h1>
              <p className="text-xl text-gray-600">
                Platform administration and system monitoring
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 px-3 py-2 bg-green-100 text-green-800 rounded-lg">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">System Online</span>
              </div>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors relative">
                <BellIcon className="w-6 h-6" />
                <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Users</p>
                    <p className="text-2xl font-bold text-gray-900">{platformStats.totalUsers.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+12.5% this month</p>
                  </div>
                  <UserGroupIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Providers</p>
                    <p className="text-2xl font-bold text-gray-900">{platformStats.activeProviders.toLocaleString()}</p>
                    <p className="text-sm text-blue-600">{((platformStats.activeProviders / platformStats.totalProviders) * 100).toFixed(1)}% active rate</p>
                  </div>
                  <BuildingOfficeIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Monthly Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">${platformStats.monthlyRevenue.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+8.3% vs last month</p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">System Uptime</p>
                    <p className="text-2xl font-bold text-gray-900">{platformStats.systemUptime}%</p>
                    <p className="text-sm text-green-600">Excellent</p>
                  </div>
                  <ServerIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Revenue Trend */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Revenue &amp; User Growth</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={revenueData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Area
                        yAxisId="left"
                        type="monotone"
                        dataKey="revenue"
                        stackId="1"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.6}
                      />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="users"
                        stroke="#10b981"
                        strokeWidth={3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Service Distribution */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Service Distribution</h3>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={serviceDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {serviceDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>

            {/* System Alerts */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">System Alerts</h3>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                  View All
                </button>
              </div>
              <div className="space-y-4">
                {systemAlerts.slice(0, 4).map((alert) => {
                  const AlertIcon = getAlertIcon(alert.type);
                  return (
                    <div
                      key={alert.id}
                      className={`p-4 rounded-lg border ${getAlertColor(alert.type)} ${
                        alert.status === 'resolved' ? 'opacity-60' : ''
                      }`}
                    >
                      <div className="flex items-start space-x-3">
                        <AlertIcon className="w-5 h-5 mt-0.5" />
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{alert.title}</h4>
                            <span className="text-xs">{alert.timestamp}</span>
                          </div>
                          <p className="text-sm mt-1">{alert.message}</p>
                          {alert.status === 'resolved' && (
                            <span className="inline-block mt-2 px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                              Resolved
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="space-y-8">
            {/* User Management Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-900">User Management</h3>
              <button
                onClick={() => setShowUserModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center space-x-2"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Add User</span>
              </button>
            </div>

            {/* Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <MagnifyingGlassIcon className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                <div className="flex space-x-4">
                  <select
                    value={userFilter}
                    onChange={(e) => setUserFilter(e.target.value)}
                    className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="pending">Pending</option>
                    <option value="suspended">Suspended</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  <button className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <FunnelIcon className="w-5 h-5" />
                    <span>Filters</span>
                  </button>
                  <button className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-2">
                    <ArrowDownTrayIcon className="w-5 h-5" />
                    <span>Export</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-900">Users ({filteredUsers.length})</h3>
              </div>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Join Date
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Activity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <UserIcon className="w-5 h-5 text-blue-600" />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{user.name}</div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="text-sm text-gray-900">{user.role}</span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                            {user.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {user.joinDate}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{user.totalBookings} bookings</div>
                          <div className="text-sm text-gray-500">Last: {user.lastActive}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-900">
                              <EyeIcon className="w-4 h-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <PencilIcon className="w-4 h-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <TrashIcon className="w-4 h-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* System Health Tab */}
        {activeTab === 'system' && (
          <div className="space-y-8">
            {/* System Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Server Load</h4>
                  <CpuChipIcon className="w-6 h-6 text-blue-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          systemMetrics.serverLoad > 80 ? 'bg-red-500' :
                          systemMetrics.serverLoad > 60 ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: `${systemMetrics.serverLoad}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{systemMetrics.serverLoad}%</span>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Memory Usage</h4>
                  <CircleStackIcon className="w-6 h-6 text-green-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          systemMetrics.memoryUsage > 80 ? 'bg-red-500' :
                          systemMetrics.memoryUsage > 60 ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: `${systemMetrics.memoryUsage}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{systemMetrics.memoryUsage}%</span>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Disk Usage</h4>
                  <ServerIcon className="w-6 h-6 text-purple-500" />
                </div>
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          systemMetrics.diskUsage > 80 ? 'bg-red-500' :
                          systemMetrics.diskUsage > 60 ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`}
                        style={{ width: `${systemMetrics.diskUsage}%` }}
                      ></div>
                    </div>
                  </div>
                  <span className="text-lg font-bold text-gray-900">{systemMetrics.diskUsage}%</span>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Network Latency</h4>
                  <WifiIcon className="w-6 h-6 text-yellow-500" />
                </div>
                <div className="text-center">
                  <span className="text-2xl font-bold text-gray-900">{systemMetrics.networkLatency}ms</span>
                  <p className="text-sm text-gray-500">Average response time</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Active Connections</h4>
                  <GlobeAltIcon className="w-6 h-6 text-blue-500" />
                </div>
                <div className="text-center">
                  <span className="text-2xl font-bold text-gray-900">{systemMetrics.activeConnections.toLocaleString()}</span>
                  <p className="text-sm text-gray-500">Current connections</p>
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-gray-900">Requests/Min</h4>
                  <ChartBarIcon className="w-6 h-6 text-green-500" />
                </div>
                <div className="text-center">
                  <span className="text-2xl font-bold text-gray-900">{systemMetrics.requestsPerMinute.toLocaleString()}</span>
                  <p className="text-sm text-gray-500">API requests per minute</p>
                </div>
              </div>
            </div>

            {/* System Status */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Service Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {[
                  { name: 'Web Server', status: 'operational', uptime: '99.9%' },
                  { name: 'Database', status: 'operational', uptime: '99.8%' },
                  { name: 'API Gateway', status: 'operational', uptime: '99.7%' },
                  { name: 'Payment System', status: 'operational', uptime: '99.9%' },
                  { name: 'File Storage', status: 'operational', uptime: '99.6%' },
                  { name: 'Email Service', status: 'degraded', uptime: '98.2%' },
                ].map((service, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        service.status === 'operational' ? 'bg-green-500' :
                        service.status === 'degraded' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}></div>
                      <span className="font-medium text-gray-900">{service.name}</span>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium text-gray-900">{service.uptime}</p>
                      <p className={`text-xs ${
                        service.status === 'operational' ? 'text-green-600' :
                        service.status === 'degraded' ? 'text-yellow-600' :
                        'text-red-600'
                      }`}>
                        {service.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            {/* Analytics Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Bookings</p>
                    <p className="text-2xl font-bold text-gray-900">{platformStats.totalBookings.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+15.3% this month</p>
                  </div>
                  <DocumentTextIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completion Rate</p>
                    <p className="text-2xl font-bold text-gray-900">{((platformStats.completedBookings / platformStats.totalBookings) * 100).toFixed(1)}%</p>
                    <p className="text-sm text-green-600">Excellent</p>
                  </div>
                  <CheckCircleIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <p className="text-2xl font-bold text-gray-900">{platformStats.averageRating}</p>
                    <p className="text-sm text-green-600">High satisfaction</p>
                  </div>
                  <StarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">${(platformStats.totalRevenue / 1000000).toFixed(1)}M</p>
                    <p className="text-sm text-green-600">+22.1% YoY</p>
                  </div>
                  <BanknotesIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* User Growth Chart */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">User Growth Trend</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={userGrowthData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="users"
                      stroke="#3b82f6"
                      strokeWidth={3}
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}