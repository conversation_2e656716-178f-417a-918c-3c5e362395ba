name: Luxcrafts CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feature/* ]
    paths:
      - 'clients/luxcrafts/**'
      - '.github/workflows/luxcrafts-cicd.yml'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'clients/luxcrafts/**'
  workflow_dispatch:
    inputs:
      deployment_target:
        description: 'Deployment target'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  WORKING_DIRECTORY: './clients/luxcrafts'

jobs:
  # Code Quality and Security Analysis
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    outputs:
      quality-score: ${{ steps.quality-check.outputs.score }}
      security-score: ${{ steps.security-check.outputs.score }}
      should-deploy: ${{ steps.gate-decision.outputs.deploy }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '${{ env.WORKING_DIRECTORY }}/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: TypeScript compilation check
        run: npx tsc --noEmit

      - name: ESLint analysis
        run: |
          npm run lint -- --format json --output-file eslint-report.json || true
          echo "eslint_errors=$(jq '.[] | select(.errorCount > 0) | .errorCount' eslint-report.json | jq -s 'add // 0')" >> $GITHUB_OUTPUT
        id: eslint

      - name: Security audit
        run: |
          npm audit --audit-level=moderate --json > audit-report.json || true
          echo "vulnerabilities=$(jq '.metadata.vulnerabilities | to_entries | map(.value) | add' audit-report.json)" >> $GITHUB_OUTPUT
        id: security

      - name: Calculate quality score
        id: quality-check
        run: |
          eslint_errors=${{ steps.eslint.outputs.eslint_errors }}
          score=$((100 - eslint_errors * 5))
          echo "score=$score" >> $GITHUB_OUTPUT
          echo "Quality Score: $score"

      - name: Calculate security score
        id: security-check
        run: |
          vulnerabilities=${{ steps.security.outputs.vulnerabilities }}
          score=$((100 - vulnerabilities * 10))
          echo "score=$score" >> $GITHUB_OUTPUT
          echo "Security Score: $score"

      - name: Quality gate decision
        id: gate-decision
        run: |
          quality_score=${{ steps.quality-check.outputs.score }}
          security_score=${{ steps.security-check.outputs.score }}
          force_deploy=${{ github.event.inputs.force_deploy }}
          
          if [[ $quality_score -ge 80 && $security_score -ge 90 ]] || [[ "$force_deploy" == "true" ]]; then
            echo "deploy=true" >> $GITHUB_OUTPUT
            echo "✅ Quality gate passed"
          else
            echo "deploy=false" >> $GITHUB_OUTPUT
            echo "❌ Quality gate failed - Quality: $quality_score, Security: $security_score"
          fi

  # Build and Test
  build-and-test:
    name: Build and Test
    runs-on: ubuntu-latest
    needs: quality-gate
    if: needs.quality-gate.outputs.should-deploy == 'true'
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: '${{ env.WORKING_DIRECTORY }}/package-lock.json'

      - name: Install dependencies
        run: npm ci

      - name: Run tests
        run: npm test -- --coverage --watchAll=false

      - name: Upload coverage reports
        uses: codecov/codecov-action@v4
        with:
          directory: ${{ env.WORKING_DIRECTORY }}/coverage
          flags: luxcrafts-frontend
          name: luxcrafts-coverage

      - name: Build application
        run: npm run build
        env:
          VITE_APP_ENVIRONMENT: ${{ github.ref == 'refs/heads/main' && 'production' || 'staging' }}
          VITE_API_BASE_URL: ${{ github.ref == 'refs/heads/main' && 'https://api.luxcrafts.co' || 'https://staging-api.luxcrafts.co' }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: ${{ env.WORKING_DIRECTORY }}/dist
          retention-days: 7

  # Staging Deployment
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [quality-gate, build-and-test]
    if: |
      needs.quality-gate.outputs.should-deploy == 'true' && 
      (github.ref == 'refs/heads/develop' || 
       github.event.inputs.deployment_target == 'staging' ||
       startsWith(github.ref, 'refs/heads/feature/'))
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    environment:
      name: staging
      url: https://luxcrafts-platform-staging.vercel.app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ${{ env.WORKING_DIRECTORY }}/dist

      - name: Deploy to Vercel Staging
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ${{ env.WORKING_DIRECTORY }}
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: Health check
        run: |
          sleep 30
          curl -f https://luxcrafts-platform-staging.vercel.app/health || exit 1

      - name: Performance test
        run: |
          npx lighthouse https://luxcrafts-platform-staging.vercel.app --output=json --output-path=lighthouse-report.json
          score=$(jq '.categories.performance.score * 100' lighthouse-report.json)
          echo "Performance Score: $score"
          if (( $(echo "$score < 80" | bc -l) )); then
            echo "⚠️ Performance score below threshold: $score"
          fi

  # Production Deployment
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [quality-gate, build-and-test]
    if: |
      needs.quality-gate.outputs.should-deploy == 'true' && 
      (github.ref == 'refs/heads/main' || 
       github.event.inputs.deployment_target == 'production')
    defaults:
      run:
        working-directory: ${{ env.WORKING_DIRECTORY }}
    environment:
      name: production
      url: https://luxcrafts-platform.vercel.app
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts
          path: ${{ env.WORKING_DIRECTORY }}/dist

      - name: Deploy to Vercel Production
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
          working-directory: ${{ env.WORKING_DIRECTORY }}
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: Health check
        run: |
          sleep 30
          curl -f https://luxcrafts-platform.vercel.app/health || exit 1

      - name: Notify deployment success
        run: |
          echo "🚀 Production deployment successful!"
          echo "URL: https://luxcrafts-platform.vercel.app"

  # Dokploy VPS Deployment (Future)
  deploy-vps:
    name: Deploy to VPS (Dokploy)
    runs-on: ubuntu-latest
    needs: [deploy-production]
    if: github.ref == 'refs/heads/main'
    environment:
      name: vps-production
      url: https://www.luxcrafts.co
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.7.0
        with:
          ssh-private-key: ${{ secrets.VPS_SSH_KEY }}

      - name: Deploy to VPS via Dokploy
        run: |
          echo "🚧 VPS deployment via Dokploy - Coming Soon"
          echo "This will deploy to www.luxcrafts.co using Dokploy"
          # ssh ${{ secrets.VPS_USER }}@${{ secrets.VPS_HOST }} 'dokploy deploy luxcrafts'

  # Monitoring and Alerts
  post-deployment:
    name: Post-Deployment Monitoring
    runs-on: ubuntu-latest
    needs: [deploy-staging, deploy-production]
    if: always() && (needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success')
    
    steps:
      - name: Setup monitoring
        run: |
          echo "📊 Setting up monitoring and alerts"
          echo "Quality Score: ${{ needs.quality-gate.outputs.quality-score }}"
          echo "Security Score: ${{ needs.quality-gate.outputs.security-score }}"

      - name: Create deployment summary
        run: |
          echo "## 🚀 Luxcrafts Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Quality Metrics" >> $GITHUB_STEP_SUMMARY
          echo "- **Quality Score:** ${{ needs.quality-gate.outputs.quality-score }}/100" >> $GITHUB_STEP_SUMMARY
          echo "- **Security Score:** ${{ needs.quality-gate.outputs.security-score }}/100" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Deployment Status" >> $GITHUB_STEP_SUMMARY
          if [[ "${{ needs.deploy-staging.result }}" == "success" ]]; then
            echo "- ✅ **Staging:** Deployed successfully" >> $GITHUB_STEP_SUMMARY
          fi
          if [[ "${{ needs.deploy-production.result }}" == "success" ]]; then
            echo "- ✅ **Production:** Deployed successfully" >> $GITHUB_STEP_SUMMARY
          fi
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Next Steps" >> $GITHUB_STEP_SUMMARY
          echo "- Monitor application performance" >> $GITHUB_STEP_SUMMARY
          echo "- Review deployment metrics" >> $GITHUB_STEP_SUMMARY
          echo "- Prepare for VPS deployment via Dokploy" >> $GITHUB_STEP_SUMMARY