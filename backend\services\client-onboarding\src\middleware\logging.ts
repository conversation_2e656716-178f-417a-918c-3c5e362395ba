import { FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { logger } from '@/utils/logger';
import { v4 as uuidv4 } from 'uuid';

export interface RequestWithId extends FastifyRequest {
  id: string;
}

export async function requestLogging(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const requestId = uuidv4();
  (request as RequestWithId).id = requestId;

  const startTime = Date.now();
  const { method, url, headers, query, params } = request;

  logger.info('Incoming request', {
    requestId,
    method,
    url,
    userAgent: headers['user-agent'],
    ip: request.ip,
    query: Object.keys(query || {}).length > 0 ? query : undefined,
    params: Object.keys(params || {}).length > 0 ? params : undefined
  });

  reply.header('X-Request-ID', requestId);

  const originalSend = reply.send.bind(reply);
  reply.send = function(payload: any) {
    const duration = Date.now() - startTime;
    
    logger.info('Request completed', {
      requestId,
      method,
      url,
      statusCode: reply.statusCode,
      duration: `${duration}ms`,
      responseSize: payload ? JSON.stringify(payload).length : 0
    });

    return originalSend(payload);
  };
}

export async function responseLogging(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  const requestId = (request as RequestWithId).id;

  if (reply.statusCode >= 400) {
    logger.error('Request failed', {
      requestId,
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode
    });
  }
}

export function errorHandler(
  error: FastifyError,
  request: FastifyRequest,
  reply: FastifyReply
): void {
  const requestId = (request as RequestWithId).id;

  logger.error('Unhandled error', {
    requestId,
    method: request.method,
    url: request.url,
    error: {
      name: error.name,
      message: error.message,
      stack: error.stack
    }
  });

  reply.status(500).send({
    error: 'Internal Server Error',
    code: 'INTERNAL_ERROR',
    requestId
  });
}