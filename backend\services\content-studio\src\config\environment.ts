import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  // Server configuration
  port: parseInt(process.env.PORT || '3001', 10),
  host: process.env.HOST || '0.0.0.0',
  nodeEnv: process.env.NODE_ENV || 'development',

  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },

  // CORS configuration
  cors: {
    origins: process.env.CORS_ORIGINS?.split(',') || [
      'http://localhost:3000',
      'http://localhost:5173',
      'https://luxcrafts.vercel.app',
      'https://*.luxcrafts.com',
    ],
  },

  // AI Service APIs
  ai: {
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_MODEL || 'gpt-4-turbo',
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '4000', 10),
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: process.env.ANTHROPIC_MODEL || 'claude-3-opus-20240229',
      maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '4000', 10),
    },
    stability: {
      apiKey: process.env.STABILITY_API_KEY,
      model: process.env.STABILITY_MODEL || 'stable-diffusion-xl-1024-v1-0',
    },
  },

  // Database configuration
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/estratix-content-studio',
      options: {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      },
    },
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379', 10),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0', 10),
    },
  },

  // Vector database (Milvus)
  vectorDb: {
    host: process.env.MILVUS_HOST || 'localhost',
    port: parseInt(process.env.MILVUS_PORT || '19530', 10),
    username: process.env.MILVUS_USERNAME,
    password: process.env.MILVUS_PASSWORD,
    database: process.env.MILVUS_DATABASE || 'estratix_content',
  },

  // Queue configuration
  queue: {
    redis: {
      host: process.env.QUEUE_REDIS_HOST || process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.QUEUE_REDIS_PORT || process.env.REDIS_PORT || '6379', 10),
      password: process.env.QUEUE_REDIS_PASSWORD || process.env.REDIS_PASSWORD,
      db: parseInt(process.env.QUEUE_REDIS_DB || '1', 10),
    },
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '5', 10),
    retryAttempts: parseInt(process.env.QUEUE_RETRY_ATTEMPTS || '3', 10),
  },

  // File storage
  storage: {
    provider: process.env.STORAGE_PROVIDER || 'local', // 'local', 's3', 'gcs'
    local: {
      uploadPath: process.env.LOCAL_UPLOAD_PATH || './uploads',
      maxFileSize: parseInt(process.env.MAX_FILE_SIZE || '52428800', 10), // 50MB
    },
    s3: {
      bucket: process.env.S3_BUCKET,
      region: process.env.S3_REGION || 'us-east-1',
      accessKeyId: process.env.S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
    },
    gcs: {
      bucket: process.env.GCS_BUCKET,
      projectId: process.env.GCS_PROJECT_ID,
      keyFilename: process.env.GCS_KEY_FILENAME,
    },
  },

  // Analytics and monitoring
  analytics: {
    googleAnalytics: {
      trackingId: process.env.GA_TRACKING_ID,
      apiKey: process.env.GA_API_KEY,
    },
    mixpanel: {
      token: process.env.MIXPANEL_TOKEN,
    },
    sentry: {
      dsn: process.env.SENTRY_DSN,
      environment: process.env.NODE_ENV || 'development',
    },
  },

  // External APIs
  external: {
    unsplash: {
      accessKey: process.env.UNSPLASH_ACCESS_KEY,
    },
    pexels: {
      apiKey: process.env.PEXELS_API_KEY,
    },
    giphy: {
      apiKey: process.env.GIPHY_API_KEY,
    },
  },

  // Rate limiting
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX || '100', 10), // limit each IP to 100 requests per windowMs
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    format: process.env.LOG_FORMAT || 'json',
  },
};

// Validate required environment variables
const requiredEnvVars = [
  'JWT_SECRET',
];

if (config.nodeEnv === 'production') {
  requiredEnvVars.push(
    'OPENAI_API_KEY',
    'MONGODB_URI',
    'REDIS_HOST'
  );
}

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Required environment variable ${envVar} is not set`);
  }
}

export default config;