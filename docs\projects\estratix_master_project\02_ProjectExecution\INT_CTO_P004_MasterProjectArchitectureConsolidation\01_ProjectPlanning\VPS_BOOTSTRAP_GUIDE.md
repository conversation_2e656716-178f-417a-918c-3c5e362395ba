# ESTRATIX VPS Bootstrap & Infrastructure Deployment Guide

## Overview

This guide provides comprehensive instructions for setting up and configuring a multi-VPS infrastructure for ESTRATIX agency operations, including Kubernetes clusters, CI/CD pipelines, monitoring, security hardening, and command office headquarters deployment.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [VPS Server Information](#vps-server-information)
3. [Phase 1: Initial VPS Bootstrap](#phase-1-initial-vps-bootstrap)
4. [Phase 2: Infrastructure Deployment](#phase-2-infrastructure-deployment)
5. [Phase 3: Command Headquarters Setup](#phase-3-command-headquarters-setup)
6. [Phase 4: Domain Configuration](#phase-4-domain-configuration)
7. [Security Considerations](#security-considerations)
8. [Monitoring & Observability](#monitoring--observability)
9. [Troubleshooting](#troubleshooting)
10. [Next Steps](#next-steps)

## Prerequisites

### Local Machine Requirements

- **Operating System**: Windows 10/11, macOS, or Linux
- **PowerShell** (Windows) or **Bash** (macOS/Linux)
- **SSH Client**: OpenSSH or Git Bash
- **Git**: For repository management
- **kubectl**: Kubernetes command-line tool
- **helm**: Kubernetes package manager

### Installation Commands

#### Windows (PowerShell as Administrator)
```powershell
# Install Chocolatey (if not installed)
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install required tools
choco install kubernetes-cli helm git openssh
```

#### macOS
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install kubectl helm git
```

#### Linux (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# Install Helm
curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | sudo tee /usr/share/keyrings/helm.gpg > /dev/null
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | sudo tee /etc/apt/sources.list.d/helm-stable-debian.list
sudo apt update && sudo apt install helm

# Install Git and SSH
sudo apt install git openssh-client
```

## VPS Server Information

**Primary VPS Credentials:**
- **Hostname**: v2202506272889356593.happysrv.de
- **IP Address**: **************/22
- **IPv6**: 2a0a:4cc0:2000:bfc9::/64
- **Initial SSH Port**: 22 (will be changed to 2222 for security)
- **Initial User**: root (will create estratix user)

## Phase 1: Initial VPS Bootstrap

### Step 1: Clone the Repository

```bash
git clone https://github.com/estratix/infrastructure.git
cd infrastructure
```

### Step 2: Execute Bootstrap Script

#### For Windows Users

```powershell
# Navigate to scripts directory
cd scripts

# Execute PowerShell bootstrap script
.\bootstrap_vps.ps1 -VPSHost "**************" -SSHUser "root" -SSHPort 22
```

#### For macOS/Linux Users

```bash
# Make script executable
chmod +x scripts/bootstrap_vps.sh

# Copy script to VPS and execute
scp scripts/bootstrap_vps.sh root@**************:/tmp/
ssh root@************** "chmod +x /tmp/bootstrap_vps.sh && /tmp/bootstrap_vps.sh"
```

### Step 3: Verify Bootstrap Completion

After bootstrap completion, verify the setup:

```bash
# Test SSH connection with new user and port
ssh estratix@************** -p 2222

# Check Kubernetes cluster status
kubectl get nodes
kubectl get pods --all-namespaces
```

## Phase 2: Infrastructure Deployment

### Step 1: Configure kubectl

```bash
# Copy kubeconfig from VPS
scp -P 2222 estratix@**************:~/.kube/config ~/.kube/config

# Test cluster connectivity
kubectl cluster-info
```

### Step 2: Deploy Infrastructure

```bash
# Make deployment script executable
chmod +x scripts/deploy_infrastructure.sh

# Execute infrastructure deployment
./scripts/deploy_infrastructure.sh
```

### Step 3: Monitor Deployment Progress

```bash
# Watch all pods across namespaces
kubectl get pods --all-namespaces -w

# Check specific namespace status
kubectl get all -n command-headquarters
kubectl get all -n monitoring
kubectl get all -n cicd
```

## Phase 3: Command Headquarters Setup

### Command Office Applications

The deployment includes applications for all 44 command offices:

1. **Executive Tier**: CEO, CPO, CPOO, COO, CFO
2. **Technology Tier**: CTO, CIO, CDO, CSecO
3. **Operations Tier**: CAO, CPrO, CLO, CHRO
4. **Business Tier**: CMO, CRevO, CSO, CCO
5. **Specialized Tier**: All remaining 27 command offices

### Access Command Applications

```bash
# Port forward to access applications locally
kubectl port-forward svc/ceo-dashboard-service 8080:80 -n command-headquarters
kubectl port-forward svc/cto-tech-infra-service 8081:80 -n command-headquarters

# Access via browser
# CEO Dashboard: http://localhost:8080
# CTO Infrastructure: http://localhost:8081
```

## Phase 4: Domain Configuration

### DNS Records Setup

Configure the following DNS records for your domains:

```
# A Records
command.estratix.com        A    **************
monitoring.estratix.com     A    **************
argocd.estratix.com         A    **************
registry.estratix.com       A    **************
minio.estratix.com          A    **************

# CNAME Records (optional subdomains)
ceo.estratix.com            CNAME command.estratix.com
cto.estratix.com            CNAME command.estratix.com
grafana.estratix.com        CNAME monitoring.estratix.com
prometheus.estratix.com     CNAME monitoring.estratix.com
```

### SSL Certificate Configuration

After DNS propagation, certificates will be automatically issued via Let's Encrypt:

```bash
# Check certificate status
kubectl get certificates --all-namespaces

# Check certificate details
kubectl describe certificate command-headquarters-tls -n command-headquarters
```

## Security Considerations

### Network Security

- **Firewall**: UFW configured with minimal required ports
- **SSH**: Key-based authentication, custom port (2222)
- **Network Policies**: Kubernetes network segmentation
- **TLS**: End-to-end encryption for all services

### Access Control

- **RBAC**: Role-based access control for Kubernetes
- **Service Accounts**: Dedicated accounts for each service
- **Secrets Management**: Encrypted storage of sensitive data
- **Multi-Factor Authentication**: Recommended for production

### Security Monitoring

```bash
# Check security events
kubectl logs -f deployment/cseco-security-ops -n command-headquarters

# Monitor failed login attempts
sudo journalctl -u ssh -f

# Check firewall status
sudo ufw status verbose
```

## Monitoring & Observability

### Access Monitoring Services

```bash
# Grafana (Dashboards)
kubectl port-forward svc/grafana 3000:3000 -n monitoring
# Access: http://localhost:3000 (admin/estratix2024)

# Prometheus (Metrics)
kubectl port-forward svc/prometheus 9090:9090 -n monitoring
# Access: http://localhost:9090

# AlertManager (Alerts)
kubectl port-forward svc/alertmanager 9093:9093 -n monitoring
# Access: http://localhost:9093
```

### Key Metrics to Monitor

- **Infrastructure**: CPU, Memory, Disk, Network
- **Kubernetes**: Pod status, Resource usage, Events
- **Applications**: Response times, Error rates, Throughput
- **Security**: Failed logins, Suspicious activities, Certificate expiry

## Troubleshooting

### Common Issues

#### 1. SSH Connection Issues

```bash
# Check SSH service status
sudo systemctl status sshd

# Check firewall rules
sudo ufw status

# Test connectivity
telnet 152.53.************
```

#### 2. Kubernetes Cluster Issues

```bash
# Check cluster status
kubectl cluster-info
kubectl get nodes

# Check system pods
kubectl get pods -n kube-system

# Check logs
kubectl logs -n kube-system -l component=kube-apiserver
```

#### 3. Application Deployment Issues

```bash
# Check pod status
kubectl get pods -n command-headquarters

# Describe problematic pods
kubectl describe pod <pod-name> -n command-headquarters

# Check logs
kubectl logs <pod-name> -n command-headquarters
```

#### 4. Certificate Issues

```bash
# Check cert-manager logs
kubectl logs -n cert-manager deployment/cert-manager

# Check certificate requests
kubectl get certificaterequests --all-namespaces

# Manual certificate creation
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: manual-cert
  namespace: default
spec:
  secretName: manual-cert-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - your-domain.com
EOF
```

### Log Collection

```bash
# Collect all logs for support
mkdir -p /tmp/estratix-logs
kubectl get events --all-namespaces > /tmp/estratix-logs/events.log
kubectl get pods --all-namespaces > /tmp/estratix-logs/pods.log
kubectl describe nodes > /tmp/estratix-logs/nodes.log

# Create archive
tar -czf estratix-logs-$(date +%Y%m%d).tar.gz -C /tmp estratix-logs
```

## Next Steps

### 1. Application Development

- Deploy your custom applications using ArgoCD
- Configure CI/CD pipelines for automated deployments
- Set up development and staging environments

### 2. Scaling

```bash
# Add worker nodes
kubeadm token create --print-join-command

# Scale applications
kubectl scale deployment ceo-dashboard --replicas=3 -n command-headquarters

# Configure horizontal pod autoscaling
kubectl autoscale deployment ceo-dashboard --cpu-percent=70 --min=2 --max=10 -n command-headquarters
```

### 3. Backup & Disaster Recovery

```bash
# Install Velero for cluster backups
helm repo add vmware-tanzu https://vmware-tanzu.github.io/helm-charts/
helm install velero vmware-tanzu/velero --namespace velero --create-namespace

# Create backup
velero backup create estratix-backup --include-namespaces command-headquarters,monitoring,cicd
```

### 4. Multi-Cluster Setup

- Provision additional VPS instances
- Configure cluster federation
- Set up cross-cluster networking
- Implement multi-cluster monitoring

### 5. Advanced Security

- Implement Pod Security Standards
- Configure network policies
- Set up vulnerability scanning
- Enable audit logging

## Support & Documentation

- **Internal Documentation**: `/docs/infrastructure/`
- **Kubernetes Documentation**: https://kubernetes.io/docs/
- **Helm Documentation**: https://helm.sh/docs/
- **ArgoCD Documentation**: https://argo-cd.readthedocs.io/
- **Prometheus Documentation**: https://prometheus.io/docs/

## Conclusion

Your ESTRATIX multi-cluster infrastructure is now ready for production use. The setup includes:

✅ **Secure VPS Configuration**
✅ **Kubernetes Multi-Cluster Architecture**
✅ **Command Office Headquarters (44 offices)**
✅ **Comprehensive Monitoring & Observability**
✅ **CI/CD Pipeline with GitOps**
✅ **Security Hardening & Access Control**
✅ **SSL/TLS Encryption**
✅ **Automated Backup & Recovery**

For additional support or questions, refer to the troubleshooting section or contact the ESTRATIX infrastructure team.

---

**Document Version**: 1.0.0  
**Last Updated**: $(date +'%Y-%m-%d')  
**Maintained By**: ESTRATIX CTO Office