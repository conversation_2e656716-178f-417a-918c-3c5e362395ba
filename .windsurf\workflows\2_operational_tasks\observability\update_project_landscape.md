---
description: Guides the generation and update of the Project Landscape diagram based on the project_matrix.md.
---

# Workflow: Update Project Landscape Diagram

This workflow details the steps to generate or update the `Project_Landscape.mmd` diagram, providing a visual representation of all ESTRATIX projects, their status, and key relationships as defined in `docs/matrices/project_matrix.md`.

**Responsible Command Office:** PMO (if exists), CStO, or COO.

## Prerequisites:

*   Access to `docs/matrices/project_matrix.md`.
*   Understanding of Mermaid syntax for diagrams.
*   (Optional) A script or tool capable of parsing the matrix and generating Mermaid code.

## Steps:

1.  **Access Project Matrix Data:**
    *   Action: Read the latest `docs/matrices/project_matrix.md`.
    *   Tool: `view_file` to load the content of the project matrix.
    *   Extract relevant data for each project:
        *   `Project ID` (for node ID and linking)
        *   `Project Name` (for node label)
        *   `Project Type` (for styling/grouping)
        *   `Sponsoring CO (Acronym)` (for grouping in subgraphs)
        *   `Status` (for styling)
        *   `Priority` (can be indicated on node)
        *   `Client (ID/Name, if applicable)` (for grouping or labeling)
        *   `Primary Service(s) Impacted` (for potential future relationship lines)
        *   `Primary Flow(s) Involved` (for potential future relationship lines)
        *   `Primary Process(es) Involved` (for potential future relationship lines)

2.  **Define Diagram Scope and Layout (Initial Setup or Major Revision):**
    *   Action: Determine the key information to visualize and the desired layout.
    *   Consider using Mermaid subgraphs to group projects by `Sponsoring CO (Acronym)`, `Project Type`, or `Client`.
    *   Define node shapes/styles based on `Status` (e.g., Planning, Active, Completed) and `Priority`.
    *   Ensure `Project ID` is used as the Mermaid node ID to allow direct linking (e.g., `Project_Landscape.mmd#INT_CEO_P001`).

3.  **Generate/Update Mermaid Diagram Code:**
    *   Action: Translate the project matrix data into Mermaid diagram syntax.
    *   **Manual Method:** Manually write/edit the Mermaid code in `docs/projects/diagrams/Project_Landscape.mmd`.
        *   Represent each project as a node, using its `Project ID` as the Mermaid node ID.
        *   Node Label Example: `"Project Name\nType: Project Type\nStatus: Status\nPriority: Priority"`
        *   Use styling to indicate status and priority.
        *   Group projects into subgraphs as defined in Step 2.
    *   **Automated/Scripted Method (Preferred for Scalability):**
        *   Develop or use a script (e.g., Python) to parse `docs/matrices/project_matrix.md`.
        *   The script should generate Mermaid syntax for nodes (using `Project ID` as node ID and a descriptive label), subgraphs, and styling.
        *   The script outputs the complete Mermaid code.

4.  **Write to Project Landscape Diagram File:**
    *   Action: Save the generated/updated Mermaid code to `docs/projects/diagrams/Project_Landscape.mmd`.
    *   Tool: `write_to_file` (if creating new or fully overwriting) or `edit_file` (for manual/incremental updates).

5.  **Validate Diagram:**
    *   Action: Use a Mermaid preview tool to render `docs/projects/diagrams/Project_Landscape.mmd` and verify its correctness.

6.  **Commit and Document Changes:**
    *   Action: Commit updated diagram and matrix to version control.
    *   Document significant changes.

## Outputs:

*   An updated `docs/projects/diagrams/Project_Landscape.mmd` file.

## Next Steps:

*   Embed or link `Project_Landscape.mmd` in relevant ESTRATIX documentation or dashboards.
*   Run this workflow regularly, especially after project definitions or status updates.

## Considerations for Automation:

*   A script to parse `project_matrix.md` and generate Mermaid code is highly recommended.
*   Consider CI/CD triggers for automatic updates when `project_matrix.md` changes.
