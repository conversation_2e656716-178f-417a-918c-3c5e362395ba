---
description: Guides the definition of a new Client Master Project, including its charter creation and registration in the project matrix.
---

# Workflow: Define New Client Project

## Objective

To establish the official identity of a new Client Project by assigning it a unique ID, creating its root documentation folder, and registering it in the ESTRATIX system.

## Orchestrating Command Office

The COO or a dedicated Client Management Office.

## Prerequisite

* An approved proposal for the client project exists in `docs/proposals/`.

## Steps

1. **Gather Project Metadata**
   * **Action:** From the approved proposal, extract the official `Client Name` and `Project Name`.
   * **Action:** Consult `docs/matrices/project_matrix.md` to determine the next sequential `Project ID` (e.g., `CL-ZUR-P001`).

2. **Create Project Documentation Root Folder**
   * **Action:** Create the top-level directory that will house all management and documentation artifacts for this client project.
   * **Architectural Rule:** The path must follow the convention: `docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName_Snake_Case]`.
   * **Tool:** `run_command` with `mkdir -p` to create the nested directories.
   * **Example:** `mkdir -p docs/clients/zuru_x/projects/CL-ZUR-P001_Website_Revamp`

3. **Register Project in Matrix**
   * **Action:** Add a new entry for the project to `docs/matrices/project_matrix.md`.
   * **Tool:** `edit_file`.
   * **Details:**
     * `Project ID`: The newly assigned ID.
     * `Project Name`: The official project name.
     * `Client Name`: The client's name.
     * `Definition Link`: A direct link to the newly created root folder: `docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName_Snake_Case]`.
     * `Implementation Link`: A link to the separate implementation repository/directory, e.g., `clients/[ProjectName_Snake_Case]`.
     * `Status`: Set to `Defined`.

## Outputs

* An empty root folder for the client project at `docs/clients/[Client_Name_Snake_Case]/projects/[Project_ID]_[ProjectName_Snake_Case]`.
* A new, corresponding entry in `docs/matrices/project_matrix.md`.
