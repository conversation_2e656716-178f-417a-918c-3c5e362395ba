---
description: "Guides the lifecycle of defining and registering a new ESTRATIX Tool, from idea to conceptual definition and matrix update."
---

# Workflow: Define ESTRATIX Tool

## Objective

To systematically define a new, reusable ESTRATIX Tool, ensuring it is atomically registered in the `tool_matrix.md`, documented using a standard template, and stored in the correct directory: `docs/tools/[Owner_Office_Code]/`.

## Trigger

An agent or developer identifies the need for a new, reusable capability (e.g., a specific API client, a data transformation function, a file system utility) that can be encapsulated as a tool.

## Responsible Command Office (Lead)

Typically the CTO, but can be delegated to the office that requires the tool.

## Key ESTRATIX Components Involved

* `docs/templates/estratix_tool_definition_template.md` (Input Template)
* `docs/tools/[Owner_Office_Code]/[Tool_ID]_[Tool_Name_PascalCase]_Definition.md` (Output: New Tool Definition)
* `docs/models/tool_matrix.md` (Output: Updated Matrix)

## Steps

1.  **Initiation & Information Gathering**
    * **Action**: An agent or user identifies the need for a new tool.
    * **Input**: `Tool_Name`, `Owner_Office_Code` (e.g., "cto"), and a brief `Description` of its function.
    * **Verification**: Check `docs/models/tool_matrix.md` to ensure a similar tool does not already exist.

2.  **Register Tool & Reserve ID (Atomic Operation)**
    * **Action (Automated)**: An agent programmatically reserves a unique ID and registers the tool.
    * **Logic**:
        1. Read `docs/models/tool_matrix.md`.
        2. Determine the next available `Tool_ID` (e.g., if the last ID is `k006`, the new one is `k007`).
        3. Add a new row to the matrix with the new `Tool_ID`, `Tool_Name`, `Owner_Office_Code`, a placeholder `Description`, and set the `Status` to `Pending Definition`.
        4. Save the updated `docs/models/tool_matrix.md`.
    * **Output**: The newly reserved `Tool_ID` (e.g., `k007`).

3.  **Create Tool Definition File from Template**
    * **Action (Automated)**:
        1. Create the target directory if it doesn't exist: `docs/tools/[Owner_Office_Code]/`.
        2. Copy the template `docs/templates/estratix_tool_definition_template.md` to the target file: `docs/tools/[Owner_Office_Code]/[Tool_ID]_[Tool_Name_PascalCase]_Definition.md`.
        3. Replace placeholders like `[Tool_ID]` and `[ToolName]` in the new file.
    * **Tooling**: File system operations, text replacement.

4.  **Populate Tool Definition**
    * **Action (User/Agent)**: Open the newly created definition file and populate all sections, including detailed technical specifications, function signatures, parameters, and return values.

5.  **Review and Finalize**
    * **Action**: Review the populated definition for technical accuracy, clarity, and completeness.
    * **Input**: Feedback from the `Owner_Office_Code` Command Officer.
    * **Output**: A finalized definition document.

6.  **Update Matrix with Definition Link**
    * **Action (Automated)**: Upon finalization, an agent updates the tool's entry in the matrix.
    * **Logic**:
        1. Open `docs/models/tool_matrix.md`.
        2. Locate the row corresponding to the `Tool_ID`.
        3. Update the `Description` with the final summary from the definition file.
        4. Set the `Definition Link` to point to the new definition file.
        5. Update the `Status` from `Pending Definition` to `Defined`.
    * **Tooling**: Markdown table manipulation script.

7.  **Confirmation & Next Steps**
    * **Action**: Confirm that the tool has been defined and registered.
    * **Output**: Provide a direct link to the new definition file and suggest running the `/tool_generation` workflow.

## Guidance for Use

* A well-defined tool is the foundation of an effective agent. Be explicit and thorough in the technical specifications.
* This workflow focuses on the *conceptual definition*. The actual implementation is handled by the `tool_generation` workflow.
* Ensure the `Owner Office` is appropriate, as they will be responsible for the tool's maintenance and governance.