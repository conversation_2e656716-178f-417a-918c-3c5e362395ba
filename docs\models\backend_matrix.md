# ESTRATIX Backend & Database Matrix

---

## 1. Overview

This matrix catalogs all approved and evaluated backend technologies, including databases, Backend-as-a-Service (BaaS) platforms, and Object-Relational Mappers (ORMs). It serves as a governance tool to guide technology selection for ESTRATIX projects, ensuring alignment with architectural standards and client requirements.

---

## 2. Technology Inventory

| Category | Technology | License | ESTRATIX Status | Primary Use Case | Integration Notes & Patterns | Traceability/Project Link |
|---|---|---|---|---|---|---|
| **BaaS Platform** | Supabase | Apache-2.0 | **Evaluating** | All-in-one backend with Postgres DB, Auth, Storage. | Good for rapid development of full-stack applications. | `R&D-BAAS-EVAL` |
| **BaaS Platform** | Convex | Apache-2.0 | **Evaluating** | Reactive backend platform with first-class TypeScript support. | Ideal for real-time applications and serverless architectures. | `R&D-BAAS-EVAL` |
| **Database** | Neon | Apache-2.0 | **Evaluating** | Serverless Postgres provider. | For projects requiring scalable, managed Postgres instances. | `R&D-DB-EVAL` |
| **Database** | MongoDB Atlas | SSPL | **Approved** | Managed NoSQL database service. | Primary NoSQL solution for flexible schema requirements. | `PROJ-CORE-INFRA` |
| **ORM** | Prisma | Apache-2.0 | **Evaluating** | Next-generation ORM for Node.js and TypeScript. | For projects needing strong type-safety and a modern query builder. | `R&D-ORM-EVAL` |
| **ORM** | Drizzle ORM | Apache-2.0 | **Evaluating** | TypeScript ORM with a focus on performance and SQL-like syntax. | Lightweight alternative to Prisma, good for performance-critical apps. | `R&D-ORM-EVAL` |
| **ORM** | TypeORM | MIT | **Evaluating** | Mature ORM for TypeScript and JavaScript. | Supports both Active Record and Data Mapper patterns. | `R&D-ORM-EVAL` |
| **DB Driver** | Mongoose | MIT | **Approved** | MongoDB object modeling tool for Node.js. | Standard for Node.js applications interacting with MongoDB. | `LIB-MONGO-NODE` |
| **DB Driver** | Motor | Apache-2.0 | **Approved** | Asynchronous Python driver for MongoDB. | Standard for Python applications interacting with MongoDB. | `LIB-MONGO-PY-ASYNC` |
| **DB Driver** | PyMongo | Apache-2.0 | **Approved** | Synchronous Python driver for MongoDB. | Standard for synchronous Python applications. | `LIB-MONGO-PY-SYNC` |
| **API Framework** | FastAPI | MIT | **Approved** | High-performance Python framework for building APIs. | Used for creating ESTRATIX microservices and backend endpoints. | `LIB-FASTAPI` |
| **Agentic Framework** | CrewAI | MIT | **Approved** | Framework for orchestrating role-playing, autonomous AI agents. | Used for building agent crews that execute complex tasks. | `LIB-CREWAI` |
| **Agentic Framework** | Pydantic-AI | MIT | **Approved** | Structured AI framework using Pydantic models for inputs/outputs. | Used for creating structured, multi-agent graphs and workflows. | `LIB-PYDANTIC-AI` |
| **Serverless Platform** | AWS Lambda | AWS Service Terms | **Approved** | Event-driven, serverless compute service. | For deploying individual microservices and functions. | `PLAT-AWS-LAMBDA` |
| **Serverless Platform** | Azure Functions | Microsoft Service Terms | **Approved** | Alternative to AWS Lambda for Azure-based deployments. | For deploying individual microservices and functions in Azure environments. | `PLAT-AZURE-FUNC` |

---

## 3. Maintenance

This matrix must be updated when new backend technologies are considered or when their status changes. Selections must be linked to the `project_matrix.md` to ensure clear traceability for client and internal projects.
