# ESTRATIX Deployment Matrix

## 1. Overview

This matrix tracks all application and service deployments across various environments. It serves as a central registry for deployment history, status, and links to associated plans, pipelines, and infrastructure.

## 2. Deployment Registry

| Deployment ID | Application/Service (Service Matrix) | Version | Environment | Status      | Deployed At         | Deployment Plan (Template)          | CI/CD Pipeline Link |
| :------------ | :----------------------------------- | :------ | :---------- | :---------- | :------------------ | :---------------------------------- | :------------------ |
| DEP-001       | User Management Service (SVC-001)    | 1.2.0   | Production  | `Success`   | 2024-07-10 15:30 UTC | [DEP-001_Plan.md](./definitions/DEP-001.md) | [Link to Pipeline](https://cicd.estratix.co/pipelines/1) |
| DEP-002       | Frontend Web App (WEB-001)           | 2.5.1   | Staging     | `In Progress` | 2024-07-10 16:00 UTC | [DEP-002_Plan.md](./definitions/DEP-002.md) | [Link to Pipeline](https://cicd.estratix.co/pipelines/2) |

## 3. Status Definitions

- **Queued**: The deployment is waiting for an available agent/runner.
- **In Progress**: The deployment is actively running.
- **Success**: The deployment completed successfully and all post-deployment checks passed.
- **Failed**: The deployment failed at some stage.
- **Rolled Back**: The deployment failed and was successfully rolled back to the previous version.
- **Cancelled**: The deployment was manually cancelled before completion.

## 4. Integration Points

- **Service Matrix**: Links deployments to the specific application or service being deployed.
- **Container Matrix**: Provides the specific container image tags used in the deployment.
- **Kubernetes Cluster Matrix**: Specifies the target cluster for the deployment.
- **VPC Server Matrix**: Defines the underlying infrastructure where services are deployed.
- **Deployment Plan Templates**: Uses standardized templates to ensure comprehensive and repeatable deployment planning.

---

**Last Updated**: YYYY-MM-DD  
**Next Review**: YYYY-MM-DD  
**Owner**: CTO (Chief Technology Officer)
