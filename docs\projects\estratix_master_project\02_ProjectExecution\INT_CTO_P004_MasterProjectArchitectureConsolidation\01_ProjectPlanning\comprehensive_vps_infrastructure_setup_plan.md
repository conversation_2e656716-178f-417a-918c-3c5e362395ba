# ESTRATIX Comprehensive VPS Infrastructure Setup Plan

**Version:** 1.0  
**Status:** Active  
**Date Created:** 2025-01-27  
**Last Updated:** 2025-01-27  
**Responsible Officer:** CTO  

## 1. Executive Summary

This document provides a comprehensive roadmap for setting up and configuring a multi-VPS infrastructure for ESTRATIX agency project deployments. The architecture leverages modern DevOps tools and follows security best practices to create a scalable, secure, and automated deployment platform.

### 1.1 Primary VPS Credentials
- **Hostname:** v2202506272889356593.happysrv.de
- **IP Address:** **************/22
- **IPv6:** 2a0a:4cc0:2000:bfc9::/64
- **Provider:** HappySrv
- **Region:** Germany

## 2. Infrastructure Architecture Overview

### 2.1 Multi-VPS Cluster Design

```
┌─────────────────────────────────────────────────────────────────┐
│                    ESTRATIX VPS Infrastructure                  │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────┐ │
│  │   Control   │  │   Worker    │  │ Monitoring  │  │  CI/CD  │ │
│  │   Plane     │  │   Nodes     │  │   Stack     │  │  Stack  │ │
│  │             │  │             │  │             │  │         │ │
│  │ K8s Master  │  │ Workloads   │  │ Prometheus  │  │ Coolify │ │
│  │ etcd        │  │ Containers  │  │ Grafana     │  │ GitLab  │ │
│  │ API Server  │  │ Pods        │  │ AlertMgr    │  │ Registry│ │
│  └─────────────┘  └─────────────┘  └─────────────┘  └─────────┘ │
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐             │
│  │ Load Balancer│  │  Security   │  │   Storage   │             │
│  │             │  │   Gateway   │  │   Cluster   │             │
│  │ Traefik     │  │             │  │             │             │
│  │ Nginx       │  │ Firewall    │  │ MinIO       │             │
│  │ SSL Term    │  │ VPN         │  │ Longhorn    │             │
│  └─────────────┘  └─────────────┘  └─────────────┘             │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 Tool Stack Comparison and Selection

Based on research and analysis <mcreference link="https://www.hostinger.com/tutorials/coolify-vs-dokploy" index="1">1</mcreference> <mcreference link="https://www.kubero.dev/blog/comparison-kubero-coolify/" index="2">2</mcreference> <mcreference link="https://docs.dokploy.com/docs/core/comparison" index="3">3</mcreference>:

#### Primary Platform: **Coolify** 
- **Rationale:** Best balance of features, ease of use, and community support <mcreference link="https://www.hostinger.com/tutorials/coolify-vs-dokploy" index="1">1</mcreference>
- **Advantages:** Auto SSL, Git-based deployment, extensive language support
- **Use Case:** Primary deployment platform for applications

#### Kubernetes Management: **Kubero**
- **Rationale:** Kubernetes-native with advanced scaling capabilities <mcreference link="https://www.kubero.dev/blog/comparison-kubero-coolify/" index="2">2</mcreference>
- **Advantages:** Autoscaling, vulnerability scanning, application metrics
- **Use Case:** Enterprise-grade container orchestration

#### Container Management: **Dokploy**
- **Rationale:** Superior Docker Compose support and multi-node capabilities <mcreference link="https://docs.dokploy.com/docs/core/comparison" index="3">3</mcreference>
- **Advantages:** Traefik integration, advanced permissions, backup features
- **Use Case:** Complex multi-container applications

## 3. Phase 1: Primary VPS Bootstrap and Security Hardening

### 3.1 Initial Server Setup

```bash
#!/bin/bash
# ESTRATIX VPS Bootstrap Script - Phase 1
# Security Hardening and Base Configuration

# Update system
apt update && apt upgrade -y

# Install essential packages
apt install -y curl wget git vim htop ufw fail2ban unattended-upgrades

# Configure SSH security
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
echo "AllowUsers estratix" >> /etc/ssh/sshd_config
systemctl restart ssh

# Setup firewall
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 6443/tcp  # Kubernetes API
ufw --force enable

# Configure fail2ban
cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local
systemctl enable fail2ban
systemctl start fail2ban
```

### 3.2 User Management and SSH Keys

```bash
# Create estratix user
useradd -m -s /bin/bash estratix
usermod -aG sudo estratix

# Setup SSH keys (replace with your public key)
mkdir -p /home/<USER>/.ssh
echo "ssh-rsa YOUR_PUBLIC_KEY_HERE" > /home/<USER>/.ssh/authorized_keys
chown -R estratix:estratix /home/<USER>/.ssh
chmod 700 /home/<USER>/.ssh
chmod 600 /home/<USER>/.ssh/authorized_keys
```

## 4. Phase 2: Container Runtime and Kubernetes Setup

### 4.1 Docker Installation

```bash
# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
usermod -aG docker estratix
systemctl enable docker
systemctl start docker

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
```

### 4.2 Kubernetes Cluster Initialization

```bash
# Install kubeadm, kubelet, kubectl
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -
echo "deb https://apt.kubernetes.io/ kubernetes-xenial main" > /etc/apt/sources.list.d/kubernetes.list
apt update
apt install -y kubelet kubeadm kubectl
apt-mark hold kubelet kubeadm kubectl

# Initialize Kubernetes cluster
kubeadm init --pod-network-cidr=**********/16 --apiserver-advertise-address=**************

# Configure kubectl for estratix user
mkdir -p /home/<USER>/.kube
cp -i /etc/kubernetes/admin.conf /home/<USER>/.kube/config
chown estratix:estratix /home/<USER>/.kube/config

# Install Flannel CNI
kubectl apply -f https://raw.githubusercontent.com/flannel-io/flannel/master/Documentation/kube-flannel.yml
```

## 5. Phase 3: Platform Deployment

### 5.1 Coolify Installation

```bash
# Install Coolify
curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash

# Configure Coolify
docker network create coolify
docker volume create coolify-db

# Start Coolify services
docker run -d \
  --name coolify \
  --restart unless-stopped \
  -p 8000:80 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v coolify-db:/app/db \
  --network coolify \
  coollabsio/coolify:latest
```

### 5.2 Kubero Deployment

```bash
# Install Kubero CLI
curl -fsSL get.kubero.dev | bash

# Install Kubero on Kubernetes
kubero install

# Configure Kubero
kubectl create namespace kubero
helm repo add kubero https://charts.kubero.dev
helm install kubero kubero/kubero --namespace kubero
```

### 5.3 Dokploy Setup

```bash
# Install Dokploy
curl -sSL https://dokploy.com/install.sh | sh

# Configure Dokploy
docker run -d \
  --name dokploy \
  --restart unless-stopped \
  -p 3000:3000 \
  -v /var/run/docker.sock:/var/run/docker.sock \
  -v dokploy-data:/app/data \
  dokploy/dokploy:latest
```

## 6. Phase 4: Monitoring and Observability

### 6.1 Prometheus Stack Deployment

```yaml
# prometheus-stack.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
      volumes:
      - name: config
        configMap:
          name: prometheus-config
```

### 6.2 Grafana Configuration

```yaml
# grafana-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          value: "estratix-admin-2025"
```

## 7. Phase 5: CI/CD Pipeline Configuration

### 7.1 GitLab Runner Setup

```bash
# Install GitLab Runner
curl -L "https://packages.gitlab.com/install/repositories/runner/gitlab-runner/script.deb.sh" | bash
apt install gitlab-runner

# Register runner
gitlab-runner register \
  --url https://gitlab.com/ \
  --registration-token YOUR_TOKEN \
  --executor docker \
  --docker-image alpine:latest \
  --description "ESTRATIX VPS Runner"
```

### 7.2 Automated Deployment Pipeline

```yaml
# .gitlab-ci.yml template
stages:
  - build
  - test
  - deploy

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"

build:
  stage: build
  script:
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA

deploy:
  stage: deploy
  script:
    - kubectl set image deployment/app app=$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  only:
    - main
```

## 8. Phase 6: Security and Compliance

### 8.1 Network Security

```bash
# Install and configure WireGuard VPN
apt install wireguard

# Generate keys
wg genkey | tee /etc/wireguard/private.key | wg pubkey > /etc/wireguard/public.key

# Configure WireGuard
cat > /etc/wireguard/wg0.conf << EOF
[Interface]
PrivateKey = $(cat /etc/wireguard/private.key)
Address = ********/24
ListenPort = 51820

[Peer]
PublicKey = CLIENT_PUBLIC_KEY
AllowedIPs = ********/32
EOF

# Enable WireGuard
systemctl enable wg-quick@wg0
systemctl start wg-quick@wg0
```

### 8.2 SSL/TLS Configuration

```bash
# Install Certbot
apt install certbot python3-certbot-nginx

# Generate SSL certificates
certbot --nginx -d your-domain.com

# Auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | crontab -
```

## 9. Phase 7: Multi-VPS Scaling

### 9.1 Worker Node Addition

```bash
# On worker nodes
kubeadm join **************:6443 --token TOKEN \
  --discovery-token-ca-cert-hash sha256:HASH

# Verify nodes
kubectl get nodes
```

### 9.2 Load Balancer Configuration

```yaml
# traefik-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: traefik
  namespace: kube-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: traefik
  template:
    metadata:
      labels:
        app: traefik
    spec:
      containers:
      - name: traefik
        image: traefik:v2.10
        args:
          - --api.insecure=true
          - --providers.kubernetes=true
          - --entrypoints.web.address=:80
          - --entrypoints.websecure.address=:443
        ports:
        - containerPort: 80
        - containerPort: 443
        - containerPort: 8080
```

## 10. Management and Operations

### 10.1 SSH Management Script

```bash
#!/bin/bash
# ESTRATIX Multi-VPS SSH Manager

VPS_LIST=(
  "control-plane:**************"
  "worker-1:**************"
  "monitoring:**************"
  "cicd:**************"
)

function connect_vps() {
  local vps_name=$1
  local vps_ip=$(echo "${VPS_LIST[@]}" | grep "$vps_name:" | cut -d':' -f2)
  
  if [ -n "$vps_ip" ]; then
    ssh estratix@$vps_ip
  else
    echo "VPS not found: $vps_name"
    echo "Available VPS:"
    for vps in "${VPS_LIST[@]}"; do
      echo "  - $(echo $vps | cut -d':' -f1)"
    done
  fi
}

# Usage: ./ssh-manager.sh control-plane
connect_vps $1
```

### 10.2 Health Monitoring Dashboard

```python
#!/usr/bin/env python3
# ESTRATIX Infrastructure Health Monitor

import requests
import subprocess
import json
from datetime import datetime

class InfrastructureMonitor:
    def __init__(self):
        self.vps_endpoints = {
            'control-plane': '**************',
            'worker-1': '**************',
            'monitoring': '**************',
            'cicd': '**************'
        }
    
    def check_vps_health(self, vps_name, ip):
        try:
            # Ping test
            result = subprocess.run(['ping', '-c', '1', ip], 
                                  capture_output=True, text=True)
            ping_success = result.returncode == 0
            
            # SSH connectivity test
            ssh_result = subprocess.run(['ssh', '-o', 'ConnectTimeout=5', 
                                       f'estratix@{ip}', 'echo "OK"'], 
                                      capture_output=True, text=True)
            ssh_success = ssh_result.returncode == 0
            
            return {
                'vps': vps_name,
                'ip': ip,
                'ping': ping_success,
                'ssh': ssh_success,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'vps': vps_name,
                'ip': ip,
                'ping': False,
                'ssh': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def generate_report(self):
        report = []
        for vps_name, ip in self.vps_endpoints.items():
            health = self.check_vps_health(vps_name, ip)
            report.append(health)
        
        return report

if __name__ == "__main__":
    monitor = InfrastructureMonitor()
    health_report = monitor.generate_report()
    print(json.dumps(health_report, indent=2))
```

## 11. Backup and Disaster Recovery

### 11.1 Automated Backup Strategy

```bash
#!/bin/bash
# ESTRATIX Backup Script

BACKUP_DIR="/backup/estratix"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR/$DATE

# Backup Kubernetes etcd
ETCDCTL_API=3 etcdctl snapshot save $BACKUP_DIR/$DATE/etcd-snapshot.db

# Backup application data
kubectl get all --all-namespaces -o yaml > $BACKUP_DIR/$DATE/k8s-resources.yaml

# Backup persistent volumes
for pv in $(kubectl get pv -o jsonpath='{.items[*].metadata.name}'); do
  kubectl get pv $pv -o yaml > $BACKUP_DIR/$DATE/pv-$pv.yaml
done

# Compress backup
tar -czf $BACKUP_DIR/estratix-backup-$DATE.tar.gz -C $BACKUP_DIR $DATE

# Clean up old backups (keep last 7 days)
find $BACKUP_DIR -name "estratix-backup-*.tar.gz" -mtime +7 -delete

echo "Backup completed: estratix-backup-$DATE.tar.gz"
```

## 12. Domain Integration and DNS Configuration

### 12.1 DNS Setup for Applications

```bash
# Configure DNS records for your domains
# Example for Cloudflare API

curl -X POST "https://api.cloudflare.com/client/v4/zones/ZONE_ID/dns_records" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  --data '{
    "type": "A",
    "name": "app.yourdomain.com",
    "content": "**************",
    "ttl": 1
  }'
```

### 12.2 Ingress Configuration

```yaml
# ingress-controller.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: estratix-ingress
  annotations:
    kubernetes.io/ingress.class: "traefik"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - app.yourdomain.com
    secretName: app-tls
  rules:
  - host: app.yourdomain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app-service
            port:
              number: 80
```

## 13. Next Steps and Recommendations

### 13.1 Immediate Actions
1. Execute Phase 1 security hardening on primary VPS
2. Install and configure Coolify for initial application deployments
3. Set up basic monitoring with Prometheus and Grafana
4. Configure automated backups

### 13.2 Short-term Goals (1-2 weeks)
1. Deploy Kubernetes cluster with Kubero
2. Configure CI/CD pipelines with GitLab Runner
3. Implement SSL/TLS certificates for all services
4. Set up VPN access for secure remote management

### 13.3 Medium-term Goals (1-2 months)
1. Scale to additional VPS nodes
2. Implement advanced monitoring and alerting
3. Deploy production applications
4. Establish disaster recovery procedures

### 13.4 Long-term Goals (3-6 months)
1. Multi-region deployment
2. Advanced security compliance (SOC 2, ISO 27001)
3. Automated scaling and cost optimization
4. Integration with ESTRATIX agentic frameworks

## 14. Cost Optimization and Resource Management

### 14.1 Resource Monitoring
- Implement resource quotas and limits
- Use horizontal pod autoscaling
- Monitor and optimize storage usage
- Regular cost analysis and optimization

### 14.2 Performance Optimization
- Implement caching strategies
- Optimize container images
- Use CDN for static content
- Database performance tuning

## 15. Compliance and Security Audit

### 15.1 Security Checklist
- [ ] SSH key-based authentication
- [ ] Firewall configuration
- [ ] SSL/TLS encryption
- [ ] Regular security updates
- [ ] Intrusion detection system
- [ ] Backup and recovery testing
- [ ] Access control and permissions
- [ ] Network segmentation

### 15.2 Compliance Framework
- GDPR compliance for data handling
- SOC 2 Type II certification path
- ISO 27001 information security management
- Regular penetration testing

This comprehensive plan provides a solid foundation for deploying and managing a scalable, secure, and efficient multi-VPS infrastructure for ESTRATIX agency operations.