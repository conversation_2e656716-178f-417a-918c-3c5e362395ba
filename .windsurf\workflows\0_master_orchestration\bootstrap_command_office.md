---
description: (MASTER WORKFLOW) Orchestrates the complete bootstrapping of a new ESTRATIX Command Office Headquarters, from definition to the generation of its entire component chain.
---

# MASTER WORKFLOW: Bootstrap Command Office Headquarters

**Objective**: To orchestrate the end-to-end creation of a new Command Office, guiding an agent from high-level conceptual definition to the generation of a complete, operational set of components.

**Agentic Orchestrator**: `AGENT_SystemArchitect_Expert`, `CHRO_Agent`

**Guidance**: This master workflow is a comprehensive, sequential guide for building a new Command Office. Follow the phases in order to ensure all digital twin (definitions) and physical twin (code) components are created and integrated correctly.

---

## Phase 1: Define & Register the Headquarters

**Objective**: To create the formal definition for the new Command Office and register it within the ESTRATIX organizational landscape.

- **Action**: Detail the office's mandate, responsibilities, and structure.
- **Workflow Trigger**: `/co_headquarter_definition`
- **Inputs**: Office Name (e.g., "Chief Technology Officer"), Office ID (e.g., "CTO"), Mandate, Responsibilities.
- **Outputs**:
  - A new definition file: `docs/organization/[Office_ID]_Headquarters.md`.
  - A new entry in `docs/matrices/organization_matrix.md`.
  - The `[Office_ID]` variable is now available for subsequent phases.

---

## Phase 2: Scaffold the Operational Structure

**Objective**: To generate the foundational directory structure for the Command Office's components, preparing for implementation.

- **Action**: Translate the conceptual definition from Phase 1 into a tangible directory structure for both documentation and source code.
- **Workflow Trigger**: `/co_headquarter_generation`
- **Inputs**: `[Office_ID]` from Phase 1.
- **Outputs**:
  - Standardized documentation directory: `docs/organization/[Office_ID]_Headquarters/`
  - Standardized, framework-specific source code directory: `src/frameworks/[framework]/[Office_ID]_Headquarters/`

---

## Phase 3: Generate Core Operational Components (Recursive & Iterative)

**Objective**: To populate the scaffolded structure by defining and generating the full chain of components that bring the Command Office to life, following a process-centric, recursive model.

**Guidance**: The primary unit of generation is the **Process**. The `/process_generation` workflow is the orchestrator responsible for recursively triggering the generation of its required agents, tasks, and tools. This ensures a cohesive, self-contained implementation for each business capability.

1. **Define & Generate Core Processes**
   - **Action**: Model and implement the core business logic and workflows for the Command Office. This is the main iterative loop.
   - **Workflow**: `/process_definition` -> `/process_generation`
   - **Recursive Generation**: The `/process_generation` workflow will automatically:
     - Trigger `/agent` for each required agent.
     - Trigger `/task` for each required task.
     - Ensure all required tools from `src/domain/tools` are available.

2. **Define & Generate Orchestrating Flows**
   - **Action**: Once multiple processes are implemented, define and generate flows to orchestrate them to achieve larger business objectives.
   - **Workflow**: `/flow_definition` -> `/flow_generation`
   - **Recursive Generation**: The `/flow_generation` workflow orchestrates the implemented `Process` components.

3. **Define & Generate Supporting Components (As Needed)**
   - **Action**: Define and generate other components required to support the office's processes and flows.
   - **Workflows**:
     - Data Models: `/data_model_definition` -> `/data_model_generation`
     - Domain Tools: `/tool_definition` -> `/tool_generation`
     - Domain Services: `/service_definition` -> `/service_generation`
     - Internal Subprojects: `/bootstrap_estratix_project`

---

## Phase 4: Verify & Integrate

**Objective**: To ensure all generated components are complete, correctly located, and properly integrated.

1. **Verify Component Integrity**
   - **Action**: For each component generated in Phase 3, confirm its definition exists in `docs/`, its implementation exists in `src/`, and it is correctly registered in its corresponding matrix.

2. **Final Review**
   - **Action**: Review all generated artifacts to ensure they align with the Command Office's mandate and are ready for operation.

**Confirmation**: The new Command Office Headquarters is successfully bootstrapped and integrated. All subsequent changes should be committed with descriptive messages.
