{"name": "@estratix/content-studio", "version": "1.0.0", "description": "AI-powered content generation and management service for ESTRATIX agency", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon --exec tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/multipart": "^8.3.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.1.0", "@langchain/anthropic": "^0.2.16", "@langchain/openai": "^0.2.8", "axios": "^1.7.4", "bull": "^4.12.9", "dotenv": "^16.4.5", "fastify": "^4.28.1", "openai": "^4.56.0", "pino": "^9.0.0", "pino-pretty": "^11.0.0", "@zilliz/milvus2-sdk-node": "^2.4.4", "redis": "^4.6.0", "zod": "^3.22.0", "mongodb": "^6.0.0", "ioredis": "^5.3.0"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/node": "^20.14.15", "@types/redis": "^4.0.0", "nodemon": "^3.1.4", "ts-node": "^10.9.2", "typescript": "^5.5.4", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "tsx": "^4.0.0"}}