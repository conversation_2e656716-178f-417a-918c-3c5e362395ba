---
# Ansible Playbook for Luxcrafts Enterprise Deployment
# Comprehensive configuration management and deployment automation

- name: Luxcrafts Enterprise Infrastructure Setup
  hosts: all
  become: yes
  vars:
    app_name: luxcrafts
    app_user: luxcrafts
    app_dir: /var/www/luxcrafts
    nginx_dir: /etc/nginx
    ssl_dir: /etc/ssl/certs
    backup_dir: /var/backups/luxcrafts
    log_dir: /var/log/luxcrafts
    node_version: "20"
    pm2_apps: 2
    
    # Security settings
    fail2ban_bantime: 3600
    fail2ban_maxretry: 3
    ssh_port: 22
    
    # Monitoring settings
    alert_email: "<EMAIL>"
    monitoring_interval: 300
    
    # SSL settings
    ssl_protocols: "TLSv1.2 TLSv1.3"
    ssl_ciphers: "ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384"
    
  tasks:
    - name: Update system packages
      apt:
        update_cache: yes
        upgrade: dist
        autoremove: yes
        autoclean: yes
      tags: [system, update]

    - name: Install essential packages
      apt:
        name:
          - curl
          - wget
          - git
          - htop
          - vim
          - ufw
          - fail2ban
          - unattended-upgrades
          - nginx
          - certbot
          - python3-certbot-nginx
          - docker.io
          - docker-compose
          - nodejs
          - npm
          - build-essential
          - software-properties-common
          - apt-transport-https
          - ca-certificates
          - gnupg
          - lsb-release
          - rkhunter
          - chkrootkit
          - lynis
          - aide
          - clamav
          - clamav-daemon
          - auditd
          - psad
          - logwatch
          - mailutils
          - postfix
          - rsyslog
          - ntp
          - chrony
          - jq
          - bc
        state: present
      tags: [packages]

    - name: Create application user
      user:
        name: "{{ app_user }}"
        groups: sudo,docker
        shell: /bin/bash
        create_home: yes
        append: yes
      tags: [user]

    - name: Create application directories
      file:
        path: "{{ item }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0755'
      loop:
        - "{{ app_dir }}"
        - "{{ backup_dir }}"
        - "{{ log_dir }}"
        - /var/www/html
        - /etc/dokploy
      tags: [directories]

    - name: Install Node.js {{ node_version }}
      shell: |
        curl -fsSL https://deb.nodesource.com/setup_{{ node_version }}.x | bash -
        apt-get install -y nodejs
      args:
        creates: /usr/bin/node
      tags: [nodejs]

    - name: Install PM2 globally
      npm:
        name: pm2
        global: yes
        state: present
      tags: [pm2]

    - name: Configure SSH hardening
      template:
        src: sshd_config.j2
        dest: /etc/ssh/sshd_config
        backup: yes
        mode: '0600'
      notify: restart ssh
      tags: [ssh, security]

    - name: Configure Fail2ban
      template:
        src: jail.local.j2
        dest: /etc/fail2ban/jail.local
        backup: yes
      notify: restart fail2ban
      tags: [fail2ban, security]

    - name: Configure UFW firewall
      ufw:
        rule: "{{ item.rule }}"
        port: "{{ item.port }}"
        proto: "{{ item.proto | default('tcp') }}"
        comment: "{{ item.comment | default('') }}"
      loop:
        - { rule: 'allow', port: '{{ ssh_port }}', comment: 'SSH' }
        - { rule: 'allow', port: '80', comment: 'HTTP' }
        - { rule: 'allow', port: '443', comment: 'HTTPS' }
        - { rule: 'allow', port: '3000', comment: 'Dokploy' }
      tags: [firewall, security]

    - name: Enable UFW
      ufw:
        state: enabled
        policy: deny
        direction: incoming
      tags: [firewall, security]

    - name: Configure Nginx main config
      template:
        src: nginx.conf.j2
        dest: "{{ nginx_dir }}/nginx.conf"
        backup: yes
      notify: restart nginx
      tags: [nginx]

    - name: Remove default Nginx site
      file:
        path: "{{ nginx_dir }}/sites-enabled/default"
        state: absent
      notify: restart nginx
      tags: [nginx]

    - name: Configure Nginx site for Luxcrafts
      template:
        src: luxcrafts.conf.j2
        dest: "{{ nginx_dir }}/sites-available/luxcrafts.conf"
        backup: yes
      notify: restart nginx
      tags: [nginx]

    - name: Enable Luxcrafts site
      file:
        src: "{{ nginx_dir }}/sites-available/luxcrafts.conf"
        dest: "{{ nginx_dir }}/sites-enabled/luxcrafts.conf"
        state: link
      notify: restart nginx
      tags: [nginx]

    - name: Configure system security parameters
      sysctl:
        name: "{{ item.name }}"
        value: "{{ item.value }}"
        state: present
        reload: yes
      loop:
        - { name: 'net.ipv4.conf.default.rp_filter', value: '1' }
        - { name: 'net.ipv4.conf.all.rp_filter', value: '1' }
        - { name: 'net.ipv4.conf.all.accept_redirects', value: '0' }
        - { name: 'net.ipv6.conf.all.accept_redirects', value: '0' }
        - { name: 'net.ipv4.conf.all.send_redirects', value: '0' }
        - { name: 'net.ipv4.conf.all.accept_source_route', value: '0' }
        - { name: 'net.ipv6.conf.all.accept_source_route', value: '0' }
        - { name: 'net.ipv4.conf.all.log_martians', value: '1' }
        - { name: 'net.ipv4.icmp_echo_ignore_all', value: '1' }
        - { name: 'net.ipv4.icmp_echo_ignore_broadcasts', value: '1' }
        - { name: 'net.ipv4.tcp_syncookies', value: '1' }
        - { name: 'net.ipv4.tcp_max_syn_backlog', value: '2048' }
        - { name: 'kernel.randomize_va_space', value: '2' }
      tags: [security, sysctl]

    - name: Install Dokploy
      shell: |
        curl -sSL https://dokploy.com/install.sh | sh
      args:
        creates: /usr/local/bin/dokploy
      tags: [dokploy]

    - name: Configure audit rules
      template:
        src: audit.rules.j2
        dest: /etc/audit/rules.d/audit.rules
        backup: yes
      notify: restart auditd
      tags: [audit, security]

    - name: Initialize AIDE database
      shell: |
        aideinit
        mv /var/lib/aide/aide.db.new /var/lib/aide/aide.db
      args:
        creates: /var/lib/aide/aide.db
      tags: [aide, security]

    - name: Update ClamAV database
      shell: freshclam
      ignore_errors: yes
      tags: [clamav, security]

    - name: Create monitoring scripts
      template:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        mode: '0755'
        owner: root
        group: root
      loop:
        - { src: 'system-monitor.sh.j2', dest: '/usr/local/bin/system-monitor' }
        - { src: 'security-scan.sh.j2', dest: '/usr/local/bin/security-scan' }
        - { src: 'backup-script.sh.j2', dest: '/usr/local/bin/backup-luxcrafts' }
        - { src: 'deploy-script.sh.j2', dest: '/usr/local/bin/deploy-luxcrafts' }
      tags: [monitoring, scripts]

    - name: Configure cron jobs
      cron:
        name: "{{ item.name }}"
        minute: "{{ item.minute }}"
        hour: "{{ item.hour }}"
        day: "{{ item.day | default('*') }}"
        month: "{{ item.month | default('*') }}"
        weekday: "{{ item.weekday | default('*') }}"
        job: "{{ item.job }}"
        user: "{{ item.user | default('root') }}"
      loop:
        - { name: 'System monitoring', minute: '*/5', hour: '*', job: '/usr/local/bin/system-monitor' }
        - { name: 'Security scan', minute: '0', hour: '2', job: '/usr/local/bin/security-scan' }
        - { name: 'AIDE check', minute: '0', hour: '3', job: '/usr/bin/aide --check' }
        - { name: 'ClamAV scan', minute: '0', hour: '4', job: '/usr/bin/clamscan -r {{ app_dir }} --log={{ log_dir }}/clamav.log' }
        - { name: 'Backup', minute: '0', hour: '1', job: '/usr/local/bin/backup-luxcrafts' }
        - { name: 'SSL renewal', minute: '0', hour: '12', job: '/usr/bin/certbot renew --quiet --nginx' }
        - { name: 'Log rotation', minute: '0', hour: '0', job: '/usr/sbin/logrotate /etc/logrotate.conf' }
      tags: [cron, monitoring]

    - name: Configure log rotation
      template:
        src: luxcrafts-logrotate.j2
        dest: /etc/logrotate.d/luxcrafts
        mode: '0644'
      tags: [logging]

    - name: Configure automatic updates
      template:
        src: "{{ item.src }}"
        dest: "{{ item.dest }}"
        mode: '0644'
      loop:
        - { src: '50unattended-upgrades.j2', dest: '/etc/apt/apt.conf.d/50unattended-upgrades' }
        - { src: '20auto-upgrades.j2', dest: '/etc/apt/apt.conf.d/20auto-upgrades' }
      tags: [updates, security]

    - name: Start and enable services
      systemd:
        name: "{{ item }}"
        state: started
        enabled: yes
      loop:
        - nginx
        - fail2ban
        - auditd
        - docker
        - ufw
        - clamav-daemon
        - postfix
        - rsyslog
        - ntp
      tags: [services]

    - name: Configure Docker daemon
      template:
        src: docker-daemon.json.j2
        dest: /etc/docker/daemon.json
        mode: '0644'
      notify: restart docker
      tags: [docker]

    - name: Add user to docker group
      user:
        name: "{{ app_user }}"
        groups: docker
        append: yes
      tags: [docker, user]

    - name: Create SSL certificate directory
      file:
        path: "{{ ssl_dir }}/luxcrafts"
        state: directory
        mode: '0755'
      tags: [ssl]

    - name: Generate DH parameters
      shell: openssl dhparam -out {{ ssl_dir }}/luxcrafts/dhparam.pem 2048
      args:
        creates: "{{ ssl_dir }}/luxcrafts/dhparam.pem"
      tags: [ssl]

    - name: Configure Postfix for alerts
      template:
        src: postfix-main.cf.j2
        dest: /etc/postfix/main.cf
        backup: yes
      notify: restart postfix
      tags: [mail]

    - name: Create application deployment structure
      file:
        path: "{{ app_dir }}/{{ item }}"
        state: directory
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0755'
      loop:
        - current
        - releases
        - shared
        - shared/logs
        - shared/config
      tags: [deployment]

    - name: Configure PM2 ecosystem
      template:
        src: ecosystem.config.js.j2
        dest: "{{ app_dir }}/shared/config/ecosystem.config.js"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0644'
      tags: [pm2, deployment]

    - name: Setup PM2 startup
      shell: |
        sudo -u {{ app_user }} pm2 startup systemd -u {{ app_user }} --hp /home/<USER>
        systemctl enable pm2-{{ app_user }}
      args:
        creates: /etc/systemd/system/pm2-{{ app_user }}.service
      tags: [pm2]

    - name: Configure environment variables
      template:
        src: environment.j2
        dest: "{{ app_dir }}/shared/config/.env"
        owner: "{{ app_user }}"
        group: "{{ app_user }}"
        mode: '0600'
      tags: [environment]

    - name: Create health check script
      template:
        src: health-check.sh.j2
        dest: /usr/local/bin/health-check
        mode: '0755'
      tags: [monitoring]

    - name: Configure Dokploy settings
      template:
        src: dokploy.config.json.j2
        dest: /etc/dokploy/config.json
        mode: '0644'
      notify: restart dokploy
      tags: [dokploy]

    - name: Final security hardening
      shell: |
        # Set proper file permissions
        chmod 700 /root
        chmod 600 /etc/ssh/sshd_config
        chmod 644 /etc/passwd
        chmod 644 /etc/group
        chmod 600 /etc/shadow
        chmod 600 /etc/gshadow
        
        # Remove unnecessary packages
        apt-get autoremove -y
        apt-get autoclean
        
        # Update locate database
        updatedb
      tags: [security, cleanup]

  handlers:
    - name: restart ssh
      systemd:
        name: sshd
        state: restarted

    - name: restart nginx
      systemd:
        name: nginx
        state: restarted

    - name: restart fail2ban
      systemd:
        name: fail2ban
        state: restarted

    - name: restart docker
      systemd:
        name: docker
        state: restarted

    - name: restart auditd
      systemd:
        name: auditd
        state: restarted

    - name: restart postfix
      systemd:
        name: postfix
        state: restarted

    - name: restart dokploy
      systemd:
        name: dokploy
        state: restarted

# Deployment playbook
- name: Deploy Luxcrafts Application
  hosts: all
  become: yes
  become_user: "{{ app_user | default('luxcrafts') }}"
  vars:
    app_name: luxcrafts
    app_user: luxcrafts
    app_dir: /var/www/luxcrafts
    repo_url: "{{ repository_url }}"
    branch: "{{ deploy_branch | default('main') }}"
    release_timestamp: "{{ ansible_date_time.epoch }}"
    
  tasks:
    - name: Clone application repository
      git:
        repo: "{{ repo_url }}"
        dest: "{{ app_dir }}/releases/{{ release_timestamp }}"
        version: "{{ branch }}"
        force: yes
      tags: [deploy, git]

    - name: Install application dependencies
      npm:
        path: "{{ app_dir }}/releases/{{ release_timestamp }}/clients/luxcrafts"
        state: present
        production: yes
      tags: [deploy, npm]

    - name: Build application
      shell: |
        cd {{ app_dir }}/releases/{{ release_timestamp }}/clients/luxcrafts
        npm run build
      tags: [deploy, build]

    - name: Link shared files
      file:
        src: "{{ app_dir }}/shared/config/{{ item }}"
        dest: "{{ app_dir }}/releases/{{ release_timestamp }}/clients/luxcrafts/{{ item }}"
        state: link
        force: yes
      loop:
        - .env
        - ecosystem.config.js
      tags: [deploy, link]

    - name: Update current symlink
      file:
        src: "{{ app_dir }}/releases/{{ release_timestamp }}"
        dest: "{{ app_dir }}/current"
        state: link
        force: yes
      tags: [deploy, symlink]

    - name: Restart application with PM2
      shell: |
        cd {{ app_dir }}/current/clients/luxcrafts
        pm2 reload ecosystem.config.js --env production
      tags: [deploy, restart]

    - name: Save PM2 configuration
      shell: pm2 save
      tags: [deploy, pm2]

    - name: Clean old releases
      shell: |
        cd {{ app_dir }}/releases
        ls -t | tail -n +6 | xargs rm -rf
      tags: [deploy, cleanup]

    - name: Verify deployment
      uri:
        url: "http://localhost:3000/health"
        method: GET
        status_code: 200
      retries: 5
      delay: 10
      tags: [deploy, verify]