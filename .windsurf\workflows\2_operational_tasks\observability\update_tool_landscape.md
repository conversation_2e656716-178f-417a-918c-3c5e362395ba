---
Description: Reads the tools matrix and automatically updates the Tool Landscape Mermaid diagram.
---

# Workflow: Update Tool Landscape Diagram

**Objective:** To ensure the `docs/diagrams/tool_landscape.mmd` accurately reflects the tools registered in `docs/matrices/tools_matrix.md` and their key relationships or categorizations.

**Trigger:**

* Manual execution.
* Can be triggered automatically after an update to `docs/matrices/tools_matrix.md` (e.g., via a file watcher, as a post-commit hook, or as a step in `/tool_definition`).

**Responsible Command Office (Lead):** CTO (for maintaining the integrity of the landscape diagram).

**Key ESTRATIX Components Involved:**

* `docs/matrices/tools_matrix.md` (Input)
* `docs/diagrams/tool_landscape.mmd` (Output)

## Steps

1.  **Read Tools Matrix Data**
    *   **Action:** Parse the `docs/matrices/tools_matrix.md` file.
    *   **Tooling:** Use a script (e.g., Python with a Markdown parsing library) or a specialized agent capable of table data extraction.
    *   **Input:** `docs/matrices/tools_matrix.md`.
    *   **Output:** Structured data representing the tools matrix (e.g., a list of dictionaries).

2.  **Identify Tools and Key Attributes/Relationships**
    *   **Action:** From the structured data, extract relevant information for each tool:
        *   Tool ID
        *   Tool Name
        *   Tool Type (e.g., Internal, External, MCP, Function)
        *   Responsible Command Office / Owner
        *   Status
        *   Related Components (e.g., Processes, Agents, Services that use this tool - this might require adding a column to `tools_matrix.md` or inferring from other matrices).
        *   Category/Domain (if applicable, for grouping).
    *   **Guidance:** Filter out tools with statuses like 'Deprecated' or 'Archived' if desired for the main landscape view, or represent them differently.
    *   **Output:** Lists of tools and their attributes/relationships.

3.  **Generate Mermaid Diagram Syntax**
    *   **Action:** Construct the Mermaid `graph TD` syntax based on the extracted tools and their attributes.
    *   **Logic:**
        *   Represent each tool as a node (e.g., `TOOL_ID["Tool Name (Type)"]`).
        *   Group tools by Category, Responsible Office, or Type using subgraphs.
        *   If relationships to other components (Processes, Agents) are identified, represent those components as nodes and create links.
        *   Apply styling using `classDef` for different tool types or statuses.
    *   **Example Snippet (Conceptual):**

        ```mermaid
        graph TD
            subgraph "Internal Tools - CTO"
                direction LR
                CTO_T001["Code Linter (Internal)"]
                CTO_T002["Deployment Script (Internal)"]
            end

            subgraph "External MCPs - CIO"
                direction LR
                CIO_T005["Azure MCP (MCP)"]
                CIO_T006["Brave Search MCP (MCP)"]
            end

            subgraph "Processes"
                CTO_P003["Software Development (CTO_P003)"]
            end
            
            CTO_T001 --> CTO_P003; // Linter used in Dev Process

            classDef internal fill:#lightblue,stroke:#333,stroke-width:2px;
            classDef mcp fill:#lightgreen,stroke:#333,stroke-width:2px;
            classDef process fill:#whitesmoke,stroke:#333,stroke-width:2px;

            class CTO_T001,CTO_T002 internal;
            class CIO_T005,CIO_T006 mcp;
            class CTO_P003 process;
        ```

    *   **Output:** A string containing the complete Mermaid syntax for the diagram.

4.  **Write to Tool Landscape Diagram File**
    *   **Action:** Overwrite the content of `docs/diagrams/tool_landscape.mmd` with the newly generated Mermaid syntax.
    *   **Tooling:** File system operations.
    *   **Input:** Generated Mermaid syntax string.
    *   **Output:** Updated `docs/diagrams/tool_landscape.mmd`.

5.  **Commit Changes (If in a Git Repository)**
    *   **Action (Optional):** If this workflow is part of an automated CI/CD pipeline or script, commit the changes to the `tool_landscape.mmd` file.
    *   **Tooling:** Git commands.
    *   **Output:** Committed changes to version control.

## Considerations & Enhancements

*   **Matrix Columns:** The richness of the diagram depends on the information available in `tools_matrix.md`. Consider adding columns for 'Category', 'Used By (Component IDs)', etc., to enable more detailed visualizations.
*   **Automated Execution:** Implement this workflow as a script for easy execution.
*   **Error Handling:** Robust error handling for parsing and file operations.
