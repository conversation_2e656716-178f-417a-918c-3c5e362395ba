# ESTRATIX VPS Infrastructure Configuration
# Comprehensive setup for agency project deployments
# Version: 2.0.0
# Date: 2024

project:
  name: "estratix-agency-infrastructure"
  version: "2.0.0"
  environment: "production"
  region: "eu-central"

# Primary VPS Configuration
primary_vps:
  hostname: "v2202506272889356593.happysrv.de"
  ip_address: "**************"
  ipv6_address: "2a0a:4cc0:2000:bfc9::/64"
  subnet_mask: "/22"
  ssh_port: 22
  ssh_user: "root"
  provider: "happysrv"
  region: "germany"
  
  # Hardware Specifications
  specs:
    cpu_cores: 8
    ram_gb: 16
    storage_gb: 200
    network_speed: "1Gbps"
    os: "Ubuntu 22.04 LTS"

# Multi-VPS Architecture Plan
vps_roles:
  master:
    name: "kubernetes-master"
    description: "Kubernetes control plane and API server"
    services:
      - kubernetes-master
      - etcd
      - api-server
      - kube-scheduler
      - kube-controller-manager
    ports:
      - 6443   # Kubernetes API
      - 2379   # etcd client
      - 2380   # etcd peer
      - 10250  # kubelet
      - 10251  # kube-scheduler
      - 10252  # kube-controller-manager
    resources:
      cpu: 4
      ram: 8
      storage: 100
  
  worker:
    name: "kubernetes-worker"
    description: "Kubernetes worker nodes for application workloads"
    services:
      - kubelet
      - kube-proxy
      - container-runtime
    ports:
      - 10250      # kubelet
      - 30000-32767 # NodePort services
    resources:
      cpu: 8
      ram: 16
      storage: 200
  
  monitoring:
    name: "monitoring-stack"
    description: "Prometheus, Grafana, and AlertManager"
    services:
      - prometheus
      - grafana
      - alertmanager
      - node-exporter
    ports:
      - 9090  # Prometheus
      - 3000  # Grafana
      - 9093  # AlertManager
      - 9100  # Node Exporter
    resources:
      cpu: 4
      ram: 8
      storage: 500
  
  cicd:
    name: "cicd-platform"
    description: "Coolify CI/CD and Docker registry"
    services:
      - coolify
      - docker-registry
      - gitlab-runner
    ports:
      - 8000  # Coolify
      - 5000  # Docker Registry
      - 8080  # GitLab Runner
    resources:
      cpu: 6
      ram: 12
      storage: 300
  
  loadbalancer:
    name: "load-balancer"
    description: "Traefik reverse proxy and load balancer"
    services:
      - traefik
      - nginx
      - certbot
    ports:
      - 80    # HTTP
      - 443   # HTTPS
      - 8080  # Traefik Dashboard
    resources:
      cpu: 2
      ram: 4
      storage: 50

# Kubernetes Configuration
kubernetes:
  version: "1.28.0"
  cluster_name: "estratix-cluster"
  
  network:
    pod_cidr: "***********/16"
    service_cidr: "*********/12"
    dns_domain: "cluster.local"
  
  cni:
    provider: "calico"
    version: "3.26.0"
  
  ingress:
    provider: "traefik"
    version: "2.10.0"
    
  addons:
    - name: "metrics-server"
      enabled: true
    - name: "dashboard"
      enabled: true
    - name: "argocd"
      enabled: true
    - name: "prometheus-operator"
      enabled: true

# Security Configuration
security:
  ssh:
    port: 2222
    user: "estratix"
    key_only_auth: true
    disable_root: true
    max_auth_tries: 3
    client_alive_interval: 300
  
  firewall:
    provider: "ufw"
    default_incoming: "deny"
    default_outgoing: "allow"
    rules:
      - port: 2222
        protocol: "tcp"
        source: "any"
        comment: "SSH"
      - port: 80
        protocol: "tcp"
        source: "any"
        comment: "HTTP"
      - port: 443
        protocol: "tcp"
        source: "any"
        comment: "HTTPS"
      - port: 6443
        protocol: "tcp"
        source: "10.0.0.0/16"
        comment: "Kubernetes API"
  
  fail2ban:
    enabled: true
    max_retry: 3
    ban_time: 3600
    find_time: 600
    
  automatic_updates:
    enabled: true
    reboot: false
    
  intrusion_detection:
    provider: "aide"
    enabled: true

# Monitoring Configuration
monitoring:
  prometheus:
    version: "2.45.0"
    retention: "30d"
    storage: "100Gi"
    
  grafana:
    version: "10.0.0"
    admin_user: "admin"
    plugins:
      - "grafana-piechart-panel"
      - "grafana-worldmap-panel"
      
  alertmanager:
    version: "0.25.0"
    
  exporters:
    - name: "node-exporter"
      port: 9100
    - name: "cadvisor"
      port: 8080
    - name: "blackbox-exporter"
      port: 9115

# CI/CD Configuration
cicd:
  coolify:
    version: "4.0.0"
    domain: "coolify.estratix.dev"
    email: "<EMAIL>"
    
  docker_registry:
    version: "2.8.0"
    storage: "100Gi"
    
  gitlab_runner:
    version: "16.0.0"
    concurrent: 4

# Load Balancer Configuration
loadbalancer:
  traefik:
    version: "2.10.0"
    dashboard: true
    api: true
    
  nginx:
    version: "1.24.0"
    
  ssl:
    provider: "letsencrypt"
    email: "<EMAIL>"

# Storage Configuration
storage:
  type: "local"
  backup:
    enabled: true
    schedule: "0 2 * * *"  # Daily at 2 AM
    retention: "30d"
    
  volumes:
    - name: "prometheus-data"
      size: "100Gi"
      path: "/data/prometheus"
    - name: "grafana-data"
      size: "10Gi"
      path: "/data/grafana"
    - name: "registry-data"
      size: "100Gi"
      path: "/data/registry"

# Network Configuration
network:
  vpc_cidr: "10.0.0.0/16"
  subnets:
    control_plane: "10.0.1.0/24"
    workers: "10.0.2.0/24"
    services: "10.0.3.0/24"
    monitoring: "10.0.4.0/24"
  
  dns:
    primary: "8.8.8.8"
    secondary: "8.8.4.4"
    
  domains:
    - name: "estratix.dev"
      subdomains:
        - "api"
        - "app"
        - "grafana"
        - "prometheus"
        - "coolify"
        - "traefik"

# Application Deployment Configuration
applications:
  web_apps:
    - name: "estratix-frontend"
      type: "react"
      port: 3000
      replicas: 3
      resources:
        cpu: "500m"
        memory: "512Mi"
    
    - name: "estratix-api"
      type: "fastapi"
      port: 8000
      replicas: 2
      resources:
        cpu: "1000m"
        memory: "1Gi"
  
  databases:
    - name: "postgresql"
      version: "15.0"
      storage: "50Gi"
      replicas: 1
    
    - name: "redis"
      version: "7.0"
      storage: "10Gi"
      replicas: 1

# Backup and Disaster Recovery
backup:
  strategy: "incremental"
  schedule:
    full: "0 1 * * 0"     # Weekly full backup
    incremental: "0 2 * * 1-6"  # Daily incremental
  
  retention:
    daily: 7
    weekly: 4
    monthly: 12
  
  destinations:
    - type: "s3"
      bucket: "estratix-backups"
      region: "eu-central-1"

# Logging Configuration
logging:
  centralized: true
  
  stack:
    - name: "elasticsearch"
      version: "8.8.0"
    - name: "logstash"
      version: "8.8.0"
    - name: "kibana"
      version: "8.8.0"
  
  retention: "30d"
  
  log_levels:
    application: "info"
    system: "warn"
    security: "debug"

# Performance and Scaling
scaling:
  horizontal:
    enabled: true
    min_replicas: 2
    max_replicas: 10
    target_cpu: 70
    target_memory: 80
  
  vertical:
    enabled: true
    update_mode: "Auto"
  
  cluster_autoscaler:
    enabled: true
    min_nodes: 1
    max_nodes: 5

# Development and Testing
development:
  environments:
    - name: "development"
      namespace: "dev"
      resources:
        cpu_limit: "2"
        memory_limit: "4Gi"
    
    - name: "staging"
      namespace: "staging"
      resources:
        cpu_limit: "4"
        memory_limit: "8Gi"
  
  testing:
    unit_tests: true
    integration_tests: true
    e2e_tests: true
    performance_tests: true

# Maintenance and Updates
maintenance:
  update_strategy: "rolling"
  
  windows:
    - day: "sunday"
      time: "02:00"
      duration: "4h"
  
  notifications:
    email: "<EMAIL>"
    slack: "#ops-alerts"

# Cost Optimization
cost_optimization:
  resource_requests:
    enabled: true
    
  spot_instances:
    enabled: false  # Not applicable for VPS
    
  scheduled_scaling:
    enabled: true
    scale_down:
      time: "20:00"
      replicas: 1
    scale_up:
      time: "08:00"
      replicas: 3