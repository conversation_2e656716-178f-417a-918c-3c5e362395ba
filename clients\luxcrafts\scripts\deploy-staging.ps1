# Luxcrafts Staging Deployment Script (PowerShell)
# High-momentum CI/CD deployment to Vercel staging

$ErrorActionPreference = "Stop"

Write-Host "🚀 Starting Luxcrafts Staging Deployment with High Momentum" -ForegroundColor Cyan
Write-Host "=================================================" -ForegroundColor Cyan

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error-Custom {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if we're in the correct directory
if (-not (Test-Path "package.json")) {
    Write-Error-Custom "package.json not found. Please run this script from the luxcrafts client directory."
    exit 1
}

Write-Status "Verifying project structure..."
if (-not (Test-Path "vercel.json")) {
    Write-Error-Custom "vercel.json not found. Vercel configuration is required."
    exit 1
}

Write-Success "Project structure verified"

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Status "Installing dependencies..."
    npm ci
    if ($LASTEXITCODE -ne 0) {
        Write-Error-Custom "Failed to install dependencies"
        exit 1
    }
    Write-Success "Dependencies installed"
} else {
    Write-Status "Dependencies already installed"
}

# Run quality checks
Write-Status "Running quality checks..."

Write-Status "Type checking..."
npm run check
if ($LASTEXITCODE -ne 0) {
    Write-Error-Custom "Type checking failed"
    exit 1
}
Write-Success "Type checking passed"

Write-Status "Linting..."
npm run lint
if ($LASTEXITCODE -ne 0) {
    Write-Warning "Linting issues found, but continuing..."
} else {
    Write-Success "Linting passed"
}

# Build the project
Write-Status "Building project for staging..."
$env:VITE_APP_ENVIRONMENT = "staging"
$env:NODE_OPTIONS = "--max-old-space-size=32768 --max-semi-space-size=1024"

npm run build
if ($LASTEXITCODE -ne 0) {
    Write-Error-Custom "Build failed"
    exit 1
}
Write-Success "Build completed successfully"

# Check build output
if (-not (Test-Path "dist")) {
    Write-Error-Custom "Build failed - dist directory not found"
    exit 1
}

Write-Status "Build output size:"
$distSize = (Get-ChildItem -Path "dist" -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
Write-Host "Build size: $([math]::Round($distSize, 2)) MB"

# Verify critical files exist
if (-not (Test-Path "dist/index.html")) {
    Write-Error-Custom "Build failed - index.html not found in dist"
    exit 1
}

Write-Success "Build verification passed"

# Create deployment info
Write-Status "Creating deployment metadata..."
$timestamp = (Get-Date).ToUniversalTime().ToString("yyyy-MM-ddTHH:mm:ssZ")
$gitHash = try { git rev-parse --short HEAD } catch { "unknown" }
$gitBranch = try { git branch --show-current } catch { "unknown" }

$deploymentInfo = @{
    timestamp = $timestamp
    environment = "staging"
    version = $gitHash
    branch = $gitBranch
    buildTime = $timestamp
} | ConvertTo-Json -Depth 3

$deploymentInfo | Out-File -FilePath "dist/deployment-info.json" -Encoding UTF8
Write-Success "Deployment metadata created"

# Create health check endpoint
Write-Status "Creating health check endpoint..."
$healthCheck = @{
    status = "healthy"
    environment = "staging"
    timestamp = $timestamp
    version = $gitHash
    services = @{
        frontend = "operational"
        build = "successful"
    }
} | ConvertTo-Json -Depth 3

$healthCheck | Out-File -FilePath "dist/health.json" -Encoding UTF8
Write-Success "Health check endpoint created"

# Create optimized vercel.json for staging
Write-Status "Creating optimized Vercel configuration..."
$vercelConfig = @{
    version = 2
    buildCommand = "echo 'Using pre-built artifacts'"
    outputDirectory = "dist"
    framework = $null
    rewrites = @(
        @{
            source = "/(.*)"
            destination = "/index.html"
        }
    )
    headers = @(
        @{
            source = "/assets/(.*)"
            headers = @(
                @{
                    key = "Cache-Control"
                    value = "public, max-age=31536000, immutable"
                },
                @{
                    key = "X-Content-Type-Options"
                    value = "nosniff"
                }
            )
        },
        @{
            source = "/(.*)"
            headers = @(
                @{
                    key = "X-Content-Type-Options"
                    value = "nosniff"
                },
                @{
                    key = "X-Frame-Options"
                    value = "DENY"
                },
                @{
                    key = "X-XSS-Protection"
                    value = "1; mode=block"
                },
                @{
                    key = "Referrer-Policy"
                    value = "strict-origin-when-cross-origin"
                }
            )
        }
    )
} | ConvertTo-Json -Depth 5

$vercelConfig | Out-File -FilePath "vercel-staging.json" -Encoding UTF8
Write-Success "Staging configuration optimized"

# Display deployment summary
Write-Host ""
Write-Host "📊 DEPLOYMENT SUMMARY" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "Environment: Staging"
Write-Host "Build Status: ✅ Success"
Write-Host "Build Size: $([math]::Round($distSize, 2)) MB"
$fileCount = (Get-ChildItem -Path "dist" -Recurse -File).Count
Write-Host "Files: $fileCount files"
Write-Host "Timestamp: $(Get-Date)"
Write-Host ""

Write-Success "🎉 Staging deployment preparation completed!"
Write-Status "Ready for Vercel deployment"

Write-Host ""
Write-Host "🔗 NEXT STEPS:" -ForegroundColor Yellow
Write-Host "1. Configure Vercel project settings"
Write-Host "2. Set up environment variables"
Write-Host "3. Deploy to staging environment"
Write-Host "4. Run health checks"
Write-Host "5. Notify team of deployment"
Write-Host ""

Write-Success "High-momentum staging deployment script completed successfully! 🚀"

# Attempt to deploy to Vercel if CLI is available
Write-Status "Attempting Vercel deployment..."
try {
    # Check if Vercel CLI is available
    $vercelVersion = vercel --version 2>$null
    if ($vercelVersion) {
        Write-Status "Vercel CLI detected: $vercelVersion"
        Write-Status "Initiating deployment to staging..."
        
        # Deploy using the optimized configuration
        vercel --prebuilt --config vercel-staging.json --yes
        
        if ($LASTEXITCODE -eq 0) {
            Write-Success "🚀 Deployment to Vercel staging completed successfully!"
        } else {
            Write-Warning "Vercel deployment encountered issues. Please check manually."
        }
    } else {
        Write-Warning "Vercel CLI not found. Please install it with: npm i -g vercel"
    }
} catch {
    Write-Warning "Could not automatically deploy to Vercel. Manual deployment may be required."
}

Write-Host ""
Write-Success "🎯 High-momentum deployment process completed! Ready for production pipeline! 🚀"