import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Calendar, Clock, Trophy, Star, Filter, Search, Download, Share2, Eye, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { useUserStore } from '../store';

interface Ticket {
  id: string;
  productId: string;
  productName: string;
  numbers: string[];
  purchaseDate: string;
  drawDate: string;
  status: 'pending' | 'drawn' | 'won' | 'lost';
  prize?: number;
  image: string;
  category: string;
  ticketType: 'lottery-linked' | 'random';
  drawNumber?: string;
}

interface WinningHistory {
  id: string;
  productName: string;
  prize: number;
  date: string;
  numbers: string[];
  image: string;
}

const MyNumbers: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useUserStore();
  const [activeTab, setActiveTab] = useState<'active' | 'history' | 'winners'>('active');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'drawn' | 'won' | 'lost'>('all');
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);

  // Mock data
  const tickets: Ticket[] = [
    {
      id: '1',
      productId: 'iphone-15',
      productName: 'iPhone 15 Pro Max',
      numbers: ['1234'],
      purchaseDate: '2024-01-15',
      drawDate: '2024-01-20',
      status: 'pending',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20titanium%20professional%20product%20photography&image_size=square',
      category: 'tecnologia',
      ticketType: 'lottery-linked'
    },
    {
      id: '2',
      productId: 'ps5-console',
      productName: 'PlayStation 5',
      numbers: ['7891', '2345'],
      purchaseDate: '2024-01-10',
      drawDate: '2024-01-18',
      status: 'drawn',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=PlayStation%205%20console%20white%20modern%20gaming%20setup&image_size=square',
      category: 'gaming',
      ticketType: 'random'
    },
    {
      id: '3',
      productId: 'macbook-pro',
      productName: 'MacBook Pro M3',
      numbers: ['5678'],
      purchaseDate: '2024-01-05',
      drawDate: '2024-01-12',
      status: 'won',
      prize: 75000,
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=MacBook%20Pro%20M3%20space%20gray%20professional%20workspace&image_size=square',
      category: 'tecnologia',
      ticketType: 'lottery-linked',
      drawNumber: '5678'
    }
  ];

  const winningHistory: WinningHistory[] = [
    {
      id: '1',
      productName: 'MacBook Pro M3',
      prize: 75000,
      date: '2024-01-12',
      numbers: ['5678'],
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=MacBook%20Pro%20M3%20space%20gray%20professional%20workspace&image_size=square'
    },
    {
      id: '2',
      productName: 'AirPods Pro 2',
      prize: 8000,
      date: '2023-12-20',
      numbers: ['1234'],
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=AirPods%20Pro%202%20white%20floating%20wireless%20earbuds&image_size=square'
    }
  ];

  const filteredTickets = tickets.filter(ticket => {
    const matchesSearch = ticket.productName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         ticket.numbers.some(num => num.includes(searchTerm));
    const matchesFilter = filterStatus === 'all' || ticket.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4 text-amber-400" />;
      case 'drawn': return <Eye className="w-4 h-4 text-blue-400" />;
      case 'won': return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'lost': return <XCircle className="w-4 h-4 text-red-400" />;
      default: return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-amber-500/20 text-amber-400 border-amber-500/30';
      case 'drawn': return 'bg-blue-500/20 text-blue-400 border-blue-500/30';
      case 'won': return 'bg-green-500/20 text-green-400 border-green-500/30';
      case 'lost': return 'bg-red-500/20 text-red-400 border-red-500/30';
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30';
    }
  };

  const TicketCard: React.FC<{ ticket: Ticket }> = ({ ticket }) => (
    <Card 
      variant="glass" 
      className="hover:scale-105 transition-all duration-300 cursor-pointer"
      onClick={() => setSelectedTicket(ticket)}
    >
      <div className="flex items-start space-x-4">
        <img 
          src={ticket.image} 
          alt={ticket.productName}
          className="w-16 h-16 rounded-lg object-cover"
        />
        <div className="flex-1">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-white font-semibold">{ticket.productName}</h3>
            <div className={`flex items-center space-x-1 px-2 py-1 rounded-full border text-xs ${getStatusColor(ticket.status)}`}>
              {getStatusIcon(ticket.status)}
              <span className="capitalize">{ticket.status}</span>
            </div>
          </div>
          
          <div className="flex flex-wrap gap-2 mb-3">
            {ticket.numbers.map((number, index) => (
              <span 
                key={index}
                className="px-3 py-1 bg-gradient-to-r from-purple-500 to-amber-500 text-white rounded-lg font-mono font-bold text-sm"
              >
                {number}
              </span>
            ))}
          </div>
          
          <div className="flex items-center justify-between text-sm text-white/70">
            <div className="flex items-center space-x-4">
              <span>Compra: {new Date(ticket.purchaseDate).toLocaleDateString()}</span>
              <span>Sorteo: {new Date(ticket.drawDate).toLocaleDateString()}</span>
            </div>
            {ticket.prize && (
              <span className="text-green-400 font-semibold">
                +${ticket.prize.toLocaleString()}
              </span>
            )}
          </div>
        </div>
      </div>
    </Card>
  );

  const WinningCard: React.FC<{ win: WinningHistory }> = ({ win }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="flex items-center space-x-4">
        <img 
          src={win.image} 
          alt={win.productName}
          className="w-12 h-12 rounded-lg object-cover"
        />
        <div className="flex-1">
          <h3 className="text-white font-semibold mb-1">{win.productName}</h3>
          <div className="flex items-center space-x-2 mb-2">
            <Trophy className="w-4 h-4 text-amber-400" />
            <span className="text-green-400 font-bold">${win.prize.toLocaleString()}</span>
          </div>
          <div className="flex items-center justify-between text-sm text-white/70">
            <span>{new Date(win.date).toLocaleDateString()}</span>
            <div className="flex space-x-1">
              {win.numbers.map((number, index) => (
                <span key={index} className="px-2 py-1 bg-amber-500/20 text-amber-400 rounded text-xs font-mono">
                  {number}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Mis <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Números</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Gestiona tus números de la suerte y revisa el estado de tus participaciones
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card variant="glass">
            <div className="text-center">
              <div className="text-3xl font-bold text-white mb-2">{tickets.length}</div>
              <div className="text-white/70 text-sm">Tickets Activos</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-400 mb-2">{winningHistory.length}</div>
              <div className="text-white/70 text-sm">Premios Ganados</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <div className="text-3xl font-bold text-amber-400 mb-2">
                ${winningHistory.reduce((sum, win) => sum + win.prize, 0).toLocaleString()}
              </div>
              <div className="text-white/70 text-sm">Total Ganado</div>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-400 mb-2">
                {tickets.filter(t => t.status === 'pending').length}
              </div>
              <div className="text-white/70 text-sm">Pendientes</div>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {[
            { id: 'active', label: 'Tickets Activos', icon: Clock },
            { id: 'history', label: 'Historial', icon: Calendar },
            { id: 'winners', label: 'Mis Premios', icon: Trophy }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Search and Filters */}
        {activeTab === 'active' && (
          <div className="flex flex-col sm:flex-row gap-4 mb-8">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50 w-5 h-5" />
              <input
                type="text"
                placeholder="Buscar por producto o número..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="px-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">Todos los estados</option>
              <option value="pending">Pendientes</option>
              <option value="drawn">Sorteados</option>
              <option value="won">Ganados</option>
              <option value="lost">Perdidos</option>
            </select>
          </div>
        )}

        {/* Content */}
        <div className="space-y-6">
          {activeTab === 'active' && (
            <div className="space-y-4">
              {filteredTickets.length > 0 ? (
                filteredTickets.map(ticket => (
                  <TicketCard key={ticket.id} ticket={ticket} />
                ))
              ) : (
                <Card variant="glass" className="text-center py-12">
                  <AlertCircle className="w-12 h-12 text-white/50 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No hay tickets</h3>
                  <p className="text-white/70 mb-6">No tienes tickets que coincidan con los filtros seleccionados</p>
                  <Button>Explorar Catálogo</Button>
                </Card>
              )}
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-4">
              {tickets.map(ticket => (
                <TicketCard key={ticket.id} ticket={ticket} />
              ))}
            </div>
          )}

          {activeTab === 'winners' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {winningHistory.length > 0 ? (
                winningHistory.map(win => (
                  <WinningCard key={win.id} win={win} />
                ))
              ) : (
                <div className="col-span-full">
                  <Card variant="glass" className="text-center py-12">
                    <Trophy className="w-12 h-12 text-white/50 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-white mb-2">Aún no has ganado</h3>
                    <p className="text-white/70 mb-6">¡Sigue participando para ganar increíbles premios!</p>
                    <Button>Ver Catálogo</Button>
                  </Card>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Ticket Detail Modal */}
      {selectedTicket && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card variant="glass" className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Detalle del Ticket</h2>
              <button
                onClick={() => setSelectedTicket(null)}
                className="text-white/70 hover:text-white p-2 rounded-lg hover:bg-white/10"
              >
                <XCircle className="w-6 h-6" />
              </button>
            </div>
            
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <img 
                  src={selectedTicket.image} 
                  alt={selectedTicket.productName}
                  className="w-24 h-24 rounded-xl object-cover"
                />
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-white mb-2">{selectedTicket.productName}</h3>
                  <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full border text-sm ${getStatusColor(selectedTicket.status)}`}>
                    {getStatusIcon(selectedTicket.status)}
                    <span className="capitalize">{selectedTicket.status}</span>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="text-white font-semibold mb-3">Mis Números</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedTicket.numbers.map((number, index) => (
                      <span 
                        key={index}
                        className="px-4 py-2 bg-gradient-to-r from-purple-500 to-amber-500 text-white rounded-lg font-mono font-bold"
                      >
                        {number}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-white font-semibold mb-3">Información</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-white/70">Fecha de compra:</span>
                      <span className="text-white">{new Date(selectedTicket.purchaseDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Fecha de sorteo:</span>
                      <span className="text-white">{new Date(selectedTicket.drawDate).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Tipo:</span>
                      <span className="text-white capitalize">{selectedTicket.ticketType.replace('-', ' ')}</span>
                    </div>
                    {selectedTicket.drawNumber && (
                      <div className="flex justify-between">
                        <span className="text-white/70">Número ganador:</span>
                        <span className="text-green-400 font-bold">{selectedTicket.drawNumber}</span>
                      </div>
                    )}
                    {selectedTicket.prize && (
                      <div className="flex justify-between">
                        <span className="text-white/70">Premio:</span>
                        <span className="text-green-400 font-bold">${selectedTicket.prize.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex space-x-4">
                <Button variant="outline" className="flex-1">
                  <Share2 className="w-4 h-4 mr-2" />
                  Compartir
                </Button>
                <Button variant="outline" className="flex-1">
                  <Download className="w-4 h-4 mr-2" />
                  Descargar
                </Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
};

export default MyNumbers;