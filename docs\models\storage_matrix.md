# ESTRATIX Storage Matrix

## 1. Overview

This matrix catalogs all object storage resources (e.g., AWS S3 buckets, Azure Blob Storage containers) used by ESTRATIX. It tracks their purpose, location, access policies, and lifecycle rules.

## 2. Storage Resource Registry

| Storage ID | Bucket/Container Name      | Type          | Provider | Region    | Environment | Purpose                | Definition Link |
| :--------- | :------------------------- | :------------ | :------- | :-------- | :---------- | :--------------------- | :-------------- |
| STG-S3-001 | estratix-prod-assets       | Object Storage| AWS S3   | us-east-1 | Production  | Application static assets | [STG-S3-001_Definition.md](./definitions/STG-S3-001.md) |
| STG-S3-002 | estratix-prod-backups      | Object Storage| AWS S3   | us-east-1 | Production  | Database backups       | [STG-S3-002_Definition.md](./definitions/STG-S3-002.md) |
| STG-S3-003 | estratix-staging-uploads   | Object Storage| AWS S3   | us-east-1 | Staging     | User content uploads   | [STG-S3-003_Definition.md](./definitions/STG-S3-003.md) |

## 3. Storage Types

- **Object Storage**: For unstructured data like files, images, videos, and backups (e.g., S3, Azure Blob).
- **Block Storage**: For persistent disks attached to virtual servers (e.g., EBS, Azure Disk Storage).
- **File Storage**: For shared file systems (e.g., EFS, Azure Files).

## 4. Integration Points

- **Application/Service Matrix**: Applications link to the storage buckets they use for assets, uploads, or data processing.
- **Database Matrix**: Database backup strategies will reference target storage buckets.
- **VPC Server Matrix**: Servers may mount block or file storage volumes defined here.

---

**Last Updated**: YYYY-MM-DD  
**Next Review**: YYYY-MM-DD  
**Owner**: CTO (Chief Technology Officer)
