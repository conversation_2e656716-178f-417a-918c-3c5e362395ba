import { apiClient, ApiResponse } from './api.client';
import { User, LoginCredentials, RegisterData, UserRole, Permission } from '../types/auth';
import { BaseService } from './base.service';
import { buildSearchParams } from './service.utils';

// Auth service types
export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface LoginResponse {
  user: User;
  tokens: AuthTokens;
}

export interface RefreshTokenResponse {
  tokens: AuthTokens;
}

// Mock users for development
const mockUsers: Record<string, User> = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    name: 'Super Admin',
    role: UserRole.SUPER_ADMIN,
    permissions: Object.values(Permission),
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '2',
    email: '<EMAIL>',
    name: 'Agency Admin',
    role: UserRole.AGENCY_ADMIN,
    permissions: [
      Permission.MANAGE_CLIENT_ACCOUNTS,
      Permission.VIEW_ALL_ANALYTICS,
      Permission.MANAGE_DEVELOPERS
    ],
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>',
    name: 'Developer',
    role: UserRole.DEVELOPER,
    permissions: [
      Permission.ACCESS_DEV_TOOLS,
      Permission.DEPLOY_APPLICATIONS,
      Permission.MANAGE_INTEGRATIONS
    ],
    subdomain: 'dev',
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '4',
    email: '<EMAIL>',
    name: 'Property Manager',
    role: UserRole.PROPERTY_MANAGER,
    permissions: [
      Permission.MANAGE_LISTINGS,
      Permission.HANDLE_BOOKINGS,
      Permission.MANAGE_MAINTENANCE
    ],
    subdomain: 'properties',
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '5',
    email: '<EMAIL>',
    name: 'AI Agent Operator',
    role: UserRole.AI_AGENT_OPERATOR,
    permissions: [
      Permission.MANAGE_AI_AGENTS,
      Permission.TRAIN_MODELS,
      Permission.VIEW_AI_ANALYTICS
    ],
    subdomain: 'ai',
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '6',
    email: '<EMAIL>',
    name: 'Investor',
    role: UserRole.INVESTOR,
    permissions: [
      Permission.VIEW_INVESTMENT_OPPORTUNITIES,
      Permission.MANAGE_PORTFOLIO,
      Permission.ACCESS_FINANCIAL_DATA
    ],
    subdomain: 'invest',
    createdAt: new Date(),
    isActive: true
  },
  '<EMAIL>': {
    id: '7',
    email: '<EMAIL>',
    name: 'Consumer',
    role: UserRole.CONSUMER,
    permissions: [
      Permission.BOOK_SERVICES,
      Permission.MANAGE_BOOKINGS,
      Permission.LEAVE_REVIEWS
    ],
    createdAt: new Date(),
    isActive: true
  }
};

function getDefaultPermissions(role: UserRole): Permission[] {
  switch (role) {
    case UserRole.SUPER_ADMIN:
      return Object.values(Permission);
    case UserRole.AGENCY_ADMIN:
      return [
        Permission.MANAGE_CLIENT_ACCOUNTS,
        Permission.VIEW_ALL_ANALYTICS,
        Permission.MANAGE_DEVELOPERS
      ];
    case UserRole.CLIENT_ADMIN:
      return [
        Permission.MANAGE_PROPERTIES,
        Permission.MANAGE_TEAM,
        Permission.VIEW_CLIENT_ANALYTICS
      ];
    case UserRole.DEVELOPER:
      return [
        Permission.ACCESS_DEV_TOOLS,
        Permission.DEPLOY_APPLICATIONS,
        Permission.MANAGE_INTEGRATIONS
      ];
    case UserRole.PROPERTY_MANAGER:
      return [
        Permission.MANAGE_LISTINGS,
        Permission.HANDLE_BOOKINGS,
        Permission.MANAGE_MAINTENANCE
      ];
    case UserRole.AI_AGENT_OPERATOR:
      return [
        Permission.MANAGE_AI_AGENTS,
        Permission.TRAIN_MODELS,
        Permission.VIEW_AI_ANALYTICS
      ];
    case UserRole.CONTENT_MANAGER:
      return [
        Permission.MANAGE_CONTENT,
        Permission.MANAGE_MEDIA,
        Permission.PUBLISH_CONTENT
      ];
    case UserRole.INVESTOR:
      return [
        Permission.VIEW_INVESTMENT_OPPORTUNITIES,
        Permission.MANAGE_PORTFOLIO,
        Permission.ACCESS_FINANCIAL_DATA
      ];
    case UserRole.SERVICE_PROVIDER:
      return [
        Permission.MANAGE_SERVICES,
        Permission.HANDLE_SERVICE_BOOKINGS,
        Permission.VIEW_EARNINGS
      ];
    case UserRole.CONSUMER:
      return [
        Permission.BOOK_SERVICES,
        Permission.MANAGE_BOOKINGS,
        Permission.LEAVE_REVIEWS
      ];
    default:
      return [];
  }
}

class AuthService extends BaseService {
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
        return response.data;
      },
      async () => {
      
      const user = mockUsers[credentials.email];
      if (!user) {
        throw new Error('Invalid credentials');
      }

      const tokens: AuthTokens = {
        accessToken: `mock-token-${user.id}`,
        refreshToken: `mock-refresh-${user.id}`,
        expiresIn: 3600
      };

        return {
          user: { ...user, lastLogin: new Date() },
          tokens
        };
      },
      1000
    );
  }

  async register(data: RegisterData): Promise<LoginResponse> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<LoginResponse>('/auth/register', data);
        return response.data;
      },
      async () => {
      
      const newUser: User = {
        id: Date.now().toString(),
        email: data.email,
        name: data.name,
        role: data.role,
        permissions: getDefaultPermissions(data.role),
        subdomain: data.subdomain,
        createdAt: new Date(),
        isActive: true
      };

      const tokens: AuthTokens = {
        accessToken: `mock-token-${newUser.id}`,
        refreshToken: `mock-refresh-${newUser.id}`,
        expiresIn: 3600
      };

        return {
          user: newUser,
          tokens
        };
      },
      1200
    );
  }

  async logout(): Promise<void> {
    if (this.isDevelopment) {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      return;
    }

    // Production API call
    await apiClient.post('/auth/logout');
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    if (this.isDevelopment) {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        tokens: {
          accessToken: `mock-refreshed-token-${Date.now()}`,
          refreshToken: `mock-refresh-${Date.now()}`,
          expiresIn: 3600
        }
      };
    }

    // Production API call
    const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh', {
      refreshToken
    });
    return response.data;
  }

  async getCurrentUser(): Promise<User> {
    if (this.isDevelopment) {
      // Mock implementation - get from localStorage
      const storedAuth = localStorage.getItem('auth-storage');
      if (storedAuth) {
        const { user } = JSON.parse(storedAuth);
        return user;
      }
      throw new Error('No authenticated user');
    }

    // Production API call
    const response = await apiClient.get<User>('/auth/me');
    return response.data;
  }

  async updateProfile(updates: Partial<User>): Promise<User> {
    if (this.isDevelopment) {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const storedAuth = localStorage.getItem('auth-storage');
      if (storedAuth) {
        const authData = JSON.parse(storedAuth);
        const updatedUser = { ...authData.user, ...updates };
        localStorage.setItem('auth-storage', JSON.stringify({
          ...authData,
          user: updatedUser
        }));
        return updatedUser;
      }
      throw new Error('No authenticated user');
    }

    // Production API call
    const response = await apiClient.patch<User>('/auth/profile', updates);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    if (this.isDevelopment) {
      // Mock implementation
      await new Promise(resolve => setTimeout(resolve, 1000));
      return;
    }

    // Production API call
    await apiClient.post('/auth/change-password', {
      currentPassword,
      newPassword
    });
  }

  async forgotPassword(email: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.post('/auth/forgot-password', { email });
      },
      async () => {
        // Mock implementation - no action needed
      },
      1000
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.post('/auth/reset-password', { token, newPassword });
      },
      async () => {
        // Mock implementation - no action needed
      },
      1000
    );
  }
}

export const authService = new AuthService();
export default authService;