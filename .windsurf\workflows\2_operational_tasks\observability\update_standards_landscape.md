---
Description: Reads the standards matrix and automatically updates the Standards Landscape Mermaid diagram.
---

# Workflow: Update Standards Landscape Diagram

**Objective:** To ensure the `docs/diagrams/standards_landscape.mmd` accurately reflects the standards registered in `docs/matrices/standards_matrix.md`, their categories, responsible offices, and potentially key relationships to other ESTRATIX components.

**Trigger:**

* Manual execution.
* Can be triggered automatically after an update to `docs/matrices/standards_matrix.md` (e.g., as a step in `1_component_lifecycle/1_define/standard_definition.md`).

**Responsible Command Office (Lead):** CTO (for maintaining the integrity of the landscape diagram, or the office overseeing standards governance).

**Key ESTRATIX Components Involved:**

* `docs/matrices/standards_matrix.md` (Input)
* `docs/diagrams/standards_landscape.mmd` (Output)

## Steps

1. **Read Standards Matrix Data:**
   * **Action:** Parse the `docs/matrices/standards_matrix.md` file.
   * **Tooling:** Use a script (e.g., Python with a Markdown parsing library) or a specialized agent capable of table data extraction.
   * **Input:** `docs/matrices/standards_matrix.md`.
   * **Output:** Structured data representing the standards matrix (e.g., a list of dictionaries).

2. **Identify Standards and Key Attributes:**
   * **Action:** From the structured data, extract relevant information for each standard:
     * Standard ID
     * Standard Name
     * Responsible Command Office
     * Category
     * Status (to potentially style or filter out deprecated/archived standards)
     * Key Applicability (to hint at relationships)
   * **Output:** Lists of standards and their attributes.

3. **Generate Mermaid Diagram Syntax:**
   * **Action:** Construct the Mermaid `graph TD` syntax based on the extracted standards.
   * **Logic:**
     * Represent each standard as a node (e.g., `STD_ID["Standard Name (Status)"]`).
     * Group standards by `Category` and/or `Responsible Command Office` using subgraphs.
     * Optionally, if `Key Applicability` or other fields in the matrix suggest relationships to processes, services, or projects, these could be represented as nodes with links (this might require more sophisticated parsing or additional matrix columns).
     * Apply styling using `classDef` for different standard categories, statuses, or office ownership.
   * **Example Snippet (Conceptual):**

     ```mermaid
     graph TD
         subgraph "Standards by Category"
             direction LR
             subgraph "Coding Conventions"
                 STD_CODING_PYTHON_001["STD_CODING_PYTHON_001 <br/> Python Coding (Active)"]
             end
             subgraph "Project Management"
                 STD_PM_AGILE_SCRUM_001["STD_PM_AGILE_SCRUM_001 <br/> Agile Scrum (Active)"]
             end
         end

         subgraph "Ownership"
             CTO --> STD_CODING_PYTHON_001
             CPrO --> STD_PM_AGILE_SCRUM_001
         end

     classDef active_standard fill:#c9ffc9,stroke:#333,stroke-width:2px;
     classDef cto_owner fill:#lightblue,stroke:#333,stroke-width:1px;
     classDef cpro_owner fill:#lightcoral,stroke:#333,stroke-width:1px;

     class STD_CODING_PYTHON_001 active_standard;
     class STD_PM_AGILE_SCRUM_001 active_standard;
     class CTO cto_owner;
     class CPrO cpro_owner;
     ```

   * **Output:** A string containing the complete Mermaid syntax for the diagram.

4. **Write to Standards Landscape Diagram File:**
   * **Action:** Overwrite the content of `docs/diagrams/standards_landscape.mmd` with the newly generated Mermaid syntax.
   * **Tooling:** File system operations.
   * **Input:** Generated Mermaid syntax string.
   * **Output:** Updated `docs/diagrams/standards_landscape.mmd`.

5. **Commit Changes (If in a Git Repository):**
   * **Action (Optional):** If this workflow is part of an automated CI/CD pipeline or script, commit the changes to the `standards_landscape.mmd` file.
   * **Tooling:** Git commands.
   * **Output:** Committed changes to version control.

## Considerations & Enhancements

* **Matrix Detail:** The richness of the diagram depends on the information in `standards_matrix.md`. Consider how categories and relationships are best represented.
* **Filtering:** The script/agent could be configured to filter out standards with certain statuses (e.g., 'Archived') from the diagram.
* **Complexity Management:** For a very large number of standards, the diagram might become too complex. Consider strategies for summarization or creating multiple focused landscape diagrams if needed.
