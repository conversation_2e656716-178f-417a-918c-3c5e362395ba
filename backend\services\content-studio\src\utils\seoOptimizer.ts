import { logger } from './logger';

export interface SEOOptimizationResult {
  optimizedContent: string;
  improvements: string[];
  seoScore: number;
  keywordAnalysis: {
    primary: string[];
    secondary: string[];
    density: { [keyword: string]: number };
  };
  metaData: {
    title?: string;
    description?: string;
    keywords?: string[];
  };
}

export interface SEOAnalysisResult {
  score: number;
  issues: string[];
  recommendations: string[];
  keywordOptimization: {
    missing: string[];
    overused: string[];
    optimal: string[];
  };
}

export class SEOOptimizer {
  constructor() {
    logger.info('SEOOptimizer initialized');
  }

  async optimize(content: string, targetKeywords: string[]): Promise<SEOOptimizationResult> {
    try {
      logger.info(`Optimizing content for SEO with keywords: ${targetKeywords.join(', ')}`);

      let optimizedContent = content;
      const improvements: string[] = [];
      
      // Analyze current keyword density
      const currentDensity = this.calculateKeywordDensity(content, targetKeywords);
      
      // Optimize keyword placement
      const keywordOptimization = this.optimizeKeywordPlacement(optimizedContent, targetKeywords);
      optimizedContent = keywordOptimization.content;
      improvements.push(...keywordOptimization.improvements);
      
      // Optimize headings
      const headingOptimization = this.optimizeHeadings(optimizedContent, targetKeywords);
      optimizedContent = headingOptimization.content;
      improvements.push(...headingOptimization.improvements);
      
      // Optimize meta elements
      const metaData = this.generateMetaData(optimizedContent, targetKeywords);
      
      // Calculate SEO score
      const seoScore = this.calculateSEOScore(optimizedContent, targetKeywords);
      
      // Analyze keywords
      const keywordAnalysis = this.analyzeKeywords(optimizedContent, targetKeywords);
      
      return {
        optimizedContent,
        improvements,
        seoScore,
        keywordAnalysis,
        metaData,
      };
    } catch (error) {
      logger.error('Error optimizing content for SEO:', error);
      throw new Error(`SEO optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async analyzeSEO(content: string, targetKeywords: string[]): Promise<SEOAnalysisResult> {
    try {
      const score = this.calculateSEOScore(content, targetKeywords);
      const issues = this.identifySEOIssues(content, targetKeywords);
      const recommendations = this.generateSEORecommendations(content, targetKeywords, issues);
      const keywordOptimization = this.analyzeKeywordOptimization(content, targetKeywords);
      
      return {
        score,
        issues,
        recommendations,
        keywordOptimization,
      };
    } catch (error) {
      logger.error('Error analyzing SEO:', error);
      throw new Error(`SEO analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private calculateKeywordDensity(content: string, keywords: string[]): { [keyword: string]: number } {
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const totalWords = words.length;
    const density: { [keyword: string]: number } = {};
    
    for (const keyword of keywords) {
      const keywordLower = keyword.toLowerCase();
      const occurrences = words.filter(word => word === keywordLower).length;
      density[keyword] = totalWords > 0 ? (occurrences / totalWords) * 100 : 0;
    }
    
    return density;
  }

  private optimizeKeywordPlacement(content: string, targetKeywords: string[]): { content: string; improvements: string[] } {
    let optimizedContent = content;
    const improvements: string[] = [];
    
    // Ensure primary keyword appears in first paragraph
    if (targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];
      const paragraphs = content.split('\n\n');
      
      if (paragraphs.length > 0 && !paragraphs[0].toLowerCase().includes(primaryKeyword.toLowerCase())) {
        // Try to naturally integrate the keyword into the first paragraph
        const firstParagraph = paragraphs[0];
        const sentences = firstParagraph.split('. ');
        
        if (sentences.length > 0) {
          // Add keyword to the first sentence if it doesn't already contain it
          if (!sentences[0].toLowerCase().includes(primaryKeyword.toLowerCase())) {
            sentences[0] = this.integrateKeywordNaturally(sentences[0], primaryKeyword);
            paragraphs[0] = sentences.join('. ');
            optimizedContent = paragraphs.join('\n\n');
            improvements.push(`Integrated primary keyword "${primaryKeyword}" into the opening paragraph`);
          }
        }
      }
    }
    
    return { content: optimizedContent, improvements };
  }

  private optimizeHeadings(content: string, targetKeywords: string[]): { content: string; improvements: string[] } {
    let optimizedContent = content;
    const improvements: string[] = [];
    
    // Find headings (simple markdown format)
    const headingRegex = /^(#{1,6})\s+(.+)$/gm;
    const headings = [...content.matchAll(headingRegex)];
    
    if (headings.length > 0 && targetKeywords.length > 0) {
      const primaryKeyword = targetKeywords[0];
      
      // Check if any heading contains the primary keyword
      const hasKeywordInHeading = headings.some(heading => 
        heading[2].toLowerCase().includes(primaryKeyword.toLowerCase())
      );
      
      if (!hasKeywordInHeading && headings.length > 0) {
        // Try to add keyword to the first heading
        const firstHeading = headings[0];
        const optimizedHeadingText = this.integrateKeywordNaturally(firstHeading[2], primaryKeyword);
        optimizedContent = optimizedContent.replace(
          firstHeading[0],
          `${firstHeading[1]} ${optimizedHeadingText}`
        );
        improvements.push(`Added primary keyword "${primaryKeyword}" to main heading`);
      }
    }
    
    return { content: optimizedContent, improvements };
  }

  private generateMetaData(content: string, targetKeywords: string[]): { title?: string; description?: string; keywords?: string[] } {
    const words = content.split(' ');
    const firstSentence = content.split('.')[0];
    
    // Generate title (first 60 characters with primary keyword)
    let title = firstSentence.substring(0, 57);
    if (targetKeywords.length > 0 && !title.toLowerCase().includes(targetKeywords[0].toLowerCase())) {
      title = `${targetKeywords[0]} - ${title}`;
    }
    title = title.substring(0, 60) + (title.length > 60 ? '...' : '');
    
    // Generate description (first 155 characters)
    let description = content.substring(0, 152);
    if (targetKeywords.length > 0 && !description.toLowerCase().includes(targetKeywords[0].toLowerCase())) {
      description = `${targetKeywords[0]} - ${description}`;
    }
    description = description.substring(0, 155) + (description.length > 155 ? '...' : '');
    
    return {
      title,
      description,
      keywords: targetKeywords,
    };
  }

  private calculateSEOScore(content: string, targetKeywords: string[]): number {
    let score = 0;
    const maxScore = 100;
    
    // Content length (20 points)
    const wordCount = (content.match(/\b\w+\b/g) || []).length;
    if (wordCount >= 300) score += 20;
    else if (wordCount >= 150) score += 10;
    
    // Keyword density (30 points)
    if (targetKeywords.length > 0) {
      const density = this.calculateKeywordDensity(content, targetKeywords);
      const primaryDensity = density[targetKeywords[0]] || 0;
      
      if (primaryDensity >= 1 && primaryDensity <= 3) score += 30;
      else if (primaryDensity >= 0.5 && primaryDensity <= 5) score += 20;
      else if (primaryDensity > 0) score += 10;
    }
    
    // Heading optimization (20 points)
    const headings = content.match(/^#{1,6}\s+.+$/gm) || [];
    if (headings.length > 0) {
      score += 10;
      if (targetKeywords.length > 0) {
        const hasKeywordInHeading = headings.some(heading => 
          heading.toLowerCase().includes(targetKeywords[0].toLowerCase())
        );
        if (hasKeywordInHeading) score += 10;
      }
    }
    
    // Readability (15 points)
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgWordsPerSentence = wordCount / sentences.length;
    if (avgWordsPerSentence <= 20) score += 15;
    else if (avgWordsPerSentence <= 25) score += 10;
    else if (avgWordsPerSentence <= 30) score += 5;
    
    // Keyword in first paragraph (15 points)
    if (targetKeywords.length > 0) {
      const firstParagraph = content.split('\n\n')[0] || '';
      if (firstParagraph.toLowerCase().includes(targetKeywords[0].toLowerCase())) {
        score += 15;
      }
    }
    
    return Math.min(score, maxScore);
  }

  private identifySEOIssues(content: string, targetKeywords: string[]): string[] {
    const issues: string[] = [];
    
    const wordCount = (content.match(/\b\w+\b/g) || []).length;
    if (wordCount < 300) {
      issues.push('Content is too short (less than 300 words)');
    }
    
    if (targetKeywords.length > 0) {
      const density = this.calculateKeywordDensity(content, targetKeywords);
      const primaryDensity = density[targetKeywords[0]] || 0;
      
      if (primaryDensity === 0) {
        issues.push(`Primary keyword "${targetKeywords[0]}" not found in content`);
      } else if (primaryDensity > 5) {
        issues.push(`Primary keyword "${targetKeywords[0]}" is overused (${primaryDensity.toFixed(1)}% density)`);
      } else if (primaryDensity < 0.5) {
        issues.push(`Primary keyword "${targetKeywords[0]}" is underused (${primaryDensity.toFixed(1)}% density)`);
      }
    }
    
    const headings = content.match(/^#{1,6}\s+.+$/gm) || [];
    if (headings.length === 0) {
      issues.push('No headings found - add H1, H2, or H3 tags');
    }
    
    return issues;
  }

  private generateSEORecommendations(content: string, targetKeywords: string[], issues: string[]): string[] {
    const recommendations: string[] = [];
    
    if (issues.some(issue => issue.includes('too short'))) {
      recommendations.push('Expand content to at least 300 words for better SEO performance');
    }
    
    if (issues.some(issue => issue.includes('not found'))) {
      recommendations.push('Include primary keyword naturally throughout the content');
    }
    
    if (issues.some(issue => issue.includes('overused'))) {
      recommendations.push('Reduce keyword density by using synonyms and related terms');
    }
    
    if (issues.some(issue => issue.includes('No headings'))) {
      recommendations.push('Add structured headings (H1, H2, H3) to improve content organization');
    }
    
    recommendations.push('Include internal and external links to authoritative sources');
    recommendations.push('Optimize images with alt text containing relevant keywords');
    recommendations.push('Ensure content provides unique value and answers user questions');
    
    return recommendations;
  }

  private analyzeKeywordOptimization(content: string, targetKeywords: string[]): {
    missing: string[];
    overused: string[];
    optimal: string[];
  } {
    const density = this.calculateKeywordDensity(content, targetKeywords);
    const missing: string[] = [];
    const overused: string[] = [];
    const optimal: string[] = [];
    
    for (const keyword of targetKeywords) {
      const keywordDensity = density[keyword] || 0;
      
      if (keywordDensity === 0) {
        missing.push(keyword);
      } else if (keywordDensity > 5) {
        overused.push(keyword);
      } else if (keywordDensity >= 1 && keywordDensity <= 3) {
        optimal.push(keyword);
      }
    }
    
    return { missing, overused, optimal };
  }

  private analyzeKeywords(content: string, targetKeywords: string[]): {
    primary: string[];
    secondary: string[];
    density: { [keyword: string]: number };
  } {
    const density = this.calculateKeywordDensity(content, targetKeywords);
    const primary = targetKeywords.slice(0, 1);
    const secondary = targetKeywords.slice(1);
    
    return {
      primary,
      secondary,
      density,
    };
  }

  private integrateKeywordNaturally(text: string, keyword: string): string {
    // Simple keyword integration - can be enhanced with NLP
    if (text.toLowerCase().includes(keyword.toLowerCase())) {
      return text;
    }
    
    // Try to add keyword at the beginning if it makes sense
    const words = text.split(' ');
    if (words.length > 3) {
      return `${keyword} ${text.toLowerCase()}`;
    }
    
    return `${text} ${keyword}`;
  }
}

export default SEOOptimizer;