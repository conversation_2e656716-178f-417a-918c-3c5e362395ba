# Luxcrafts Enterprise Dokploy Deployment Guide

## Overview

This document outlines the comprehensive enterprise-grade deployment infrastructure for Luxcrafts using <PERSON>kploy, featuring automated VPS provisioning, advanced security measures, CI/CD automation, and production-ready monitoring.

## Architecture Overview

### Infrastructure Components

1. **VPS Infrastructure** (DigitalOcean/Custom VPS)
   - Production environment with load balancing
   - Staging environment for testing
   - Database server with automated backups
   - Monitoring and logging infrastructure

2. **Security Layer**
   - WAF (Web Application Firewall)
   - DDoS protection
   - SSL/TLS encryption
   - Intrusion detection system
   - Vulnerability scanning
   - Security headers and hardening

3. **Deployment Pipeline**
   - GitHub Actions CI/CD
   - Automated testing and quality gates
   - Blue-green deployment strategy
   - Rollback capabilities
   - Environment promotion

4. **Monitoring & Alerting**
   - Application performance monitoring
   - Infrastructure monitoring
   - Log aggregation and analysis
   - Real-time alerting
   - Uptime monitoring

## Prerequisites

### Required Accounts & Services

1. **DigitalOcean Account** (or custom VPS provider)
2. **Cloudflare Account** (for DNS and CDN)
3. **GitHub Repository** with Actions enabled
4. **Domain Name** registered and configured
5. **Email Service** for alerts and notifications

### Required Tools

```bash
# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
sudo mv terraform /usr/local/bin/

# Install Ansible
sudo apt update
sudo apt install ansible -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Dokploy
curl -sSL https://dokploy.com/install.sh | sh
```

## Deployment Process

### Phase 1: Infrastructure Provisioning

#### 1.1 Configure Terraform Variables

Create `terraform.tfvars`:

```hcl
# DigitalOcean Configuration
do_token = "your_digitalocean_token"
do_region = "nyc3"
do_size = "s-2vcpu-4gb"

# Cloudflare Configuration
cloudflare_api_token = "your_cloudflare_token"
cloudflare_zone_id = "your_zone_id"

# Domain Configuration
domain_name = "luxcrafts.com"
staging_domain = "staging.luxcrafts.com"

# SSH Configuration
ssh_public_key = "ssh-rsa AAAAB3NzaC1yc2E..."
ssh_private_key_path = "~/.ssh/id_rsa"

# Application Configuration
app_name = "luxcrafts"
app_user = "luxcrafts"
admin_email = "<EMAIL>"

# Database Configuration
db_name = "luxcrafts_prod"
db_user = "luxcrafts"
db_password = "secure_password_here"

# Environment
environment = "production"
```

#### 1.2 Deploy Infrastructure

```bash
# Initialize Terraform
cd infrastructure/terraform
terraform init

# Plan deployment
terraform plan -var-file="terraform.tfvars"

# Apply infrastructure
terraform apply -var-file="terraform.tfvars"

# Save outputs
terraform output > ../outputs.txt
```

### Phase 2: Server Configuration

#### 2.1 Configure Ansible Inventory

Create `inventory/hosts.yml`:

```yaml
all:
  children:
    production:
      hosts:
        luxcrafts-web-01:
          ansible_host: "{{ web_server_ip }}"
          ansible_user: luxcrafts
          ansible_ssh_private_key_file: ~/.ssh/id_rsa
        luxcrafts-db-01:
          ansible_host: "{{ db_server_ip }}"
          ansible_user: luxcrafts
          ansible_ssh_private_key_file: ~/.ssh/id_rsa
    staging:
      hosts:
        luxcrafts-staging-01:
          ansible_host: "{{ staging_server_ip }}"
          ansible_user: luxcrafts
          ansible_ssh_private_key_file: ~/.ssh/id_rsa
    monitoring:
      hosts:
        luxcrafts-monitor-01:
          ansible_host: "{{ monitoring_server_ip }}"
          ansible_user: luxcrafts
          ansible_ssh_private_key_file: ~/.ssh/id_rsa
```

#### 2.2 Configure Ansible Variables

Create `group_vars/all.yml`:

```yaml
# Application Configuration
app_name: luxcrafts
app_user: luxcrafts
app_dir: /var/www/luxcrafts
domain_name: luxcrafts.com
staging_domain: staging.luxcrafts.com

# Database Configuration
db_host: "{{ hostvars['luxcrafts-db-01']['ansible_host'] }}"
db_name: luxcrafts_prod
db_user: luxcrafts
db_password: "{{ vault_db_password }}"

# Security Configuration
ssl_email: <EMAIL>
fail2ban_bantime: 3600
fail2ban_maxretry: 3

# Monitoring Configuration
alert_email: <EMAIL>
monitoring_interval: 300

# Repository Configuration
repository_url: https://github.com/your-org/luxcrafts.git
deploy_branch: main
```

#### 2.3 Run Ansible Playbook

```bash
# Configure servers
cd infrastructure/ansible
ansible-playbook -i inventory/hosts.yml playbook.yml

# Deploy application
ansible-playbook -i inventory/hosts.yml playbook.yml --tags deploy
```

### Phase 3: Dokploy Configuration

#### 3.1 Access Dokploy Dashboard

```bash
# Get Dokploy admin credentials
sudo dokploy auth show

# Access dashboard
open https://your-server-ip:3000
```

#### 3.2 Configure Dokploy Projects

1. **Create Production Project**
   - Name: `luxcrafts-production`
   - Repository: `https://github.com/your-org/luxcrafts.git`
   - Branch: `main`
   - Build path: `clients/luxcrafts`
   - Port: `3000`

2. **Create Staging Project**
   - Name: `luxcrafts-staging`
   - Repository: `https://github.com/your-org/luxcrafts.git`
   - Branch: `develop`
   - Build path: `clients/luxcrafts`
   - Port: `3002`

#### 3.3 Configure Environment Variables

**Production Environment:**
```env
NODE_ENV=production
PORT=3000
DATABASE_URL=**********************************************/luxcrafts_prod
REDIS_URL=redis://redis-server:6379
JWT_SECRET=your-jwt-secret
API_BASE_URL=https://api.luxcrafts.com
CDN_URL=https://cdn.luxcrafts.com
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=info
```

**Staging Environment:**
```env
NODE_ENV=staging
PORT=3002
DATABASE_URL=**********************************************/luxcrafts_staging
REDIS_URL=redis://redis-server:6379
JWT_SECRET=your-staging-jwt-secret
API_BASE_URL=https://staging-api.luxcrafts.com
CDN_URL=https://staging-cdn.luxcrafts.com
SENTRY_DSN=your-staging-sentry-dsn
LOG_LEVEL=debug
```

### Phase 4: CI/CD Pipeline Setup

#### 4.1 Configure GitHub Secrets

```bash
# Required secrets for GitHub Actions
DOKPLOY_SERVER_HOST=your-server-ip
DOKPLOY_API_TOKEN=your-dokploy-token
DOKPLOY_PROJECT_ID_PROD=production-project-id
DOKPLOY_PROJECT_ID_STAGING=staging-project-id
SSH_PRIVATE_KEY=your-ssh-private-key
SSH_HOST=your-server-ip
SSH_USER=luxcrafts
DATABASE_URL=your-database-url
JWT_SECRET=your-jwt-secret
SENTRY_DSN=your-sentry-dsn
CLOUDFLARE_API_TOKEN=your-cloudflare-token
CLOUDFLARE_ZONE_ID=your-zone-id
SLACK_WEBHOOK_URL=your-slack-webhook
ALERT_EMAIL=<EMAIL>
```

#### 4.2 Workflow Configuration

The CI/CD pipeline (`dokploy-enterprise-cicd.yml`) includes:

1. **Quality Gates**
   - TypeScript compilation
   - ESLint code quality
   - Security vulnerability scan
   - Bundle size analysis
   - Unit and integration tests

2. **Security Analysis**
   - SAST (Static Application Security Testing)
   - Dependency vulnerability scan
   - Container security scan
   - Infrastructure security check

3. **Deployment Strategy**
   - Staging deployment (automatic)
   - Production deployment (manual approval)
   - Blue-green deployment
   - Health checks and rollback

### Phase 5: Monitoring & Alerting

#### 5.1 Application Monitoring

**Metrics Collected:**
- Response times and throughput
- Error rates and exceptions
- Database performance
- Memory and CPU usage
- User sessions and activity

**Alerting Rules:**
```yaml
# High error rate
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
  for: 5m
  annotations:
    summary: "High error rate detected"

# High response time
- alert: HighResponseTime
  expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
  for: 5m
  annotations:
    summary: "High response time detected"

# Database connection issues
- alert: DatabaseDown
  expr: up{job="database"} == 0
  for: 1m
  annotations:
    summary: "Database is down"
```

#### 5.2 Infrastructure Monitoring

**System Metrics:**
- CPU, memory, and disk usage
- Network traffic and latency
- Service availability
- SSL certificate expiration
- Security events and intrusions

#### 5.3 Log Management

**Log Sources:**
- Application logs (structured JSON)
- Nginx access and error logs
- System logs (syslog, auth.log)
- Security logs (fail2ban, audit)
- Database logs

**Log Aggregation:**
```bash
# Configure log shipping
sudo systemctl enable rsyslog
sudo systemctl enable logrotate

# Setup log retention
echo "{{ log_dir }}/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 {{ app_user }} {{ app_user }}
    postrotate
        systemctl reload nginx
    endscript
}" | sudo tee /etc/logrotate.d/luxcrafts
```

### Phase 6: Security Implementation

#### 6.1 SSL/TLS Configuration

```bash
# Install SSL certificates
sudo certbot --nginx -d luxcrafts.com -d www.luxcrafts.com -d staging.luxcrafts.com

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet --nginx" | sudo crontab -
```

#### 6.2 Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3000/tcp  # Dokploy
sudo ufw enable
```

#### 6.3 Intrusion Detection

```bash
# Configure Fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban

# Monitor failed attempts
sudo fail2ban-client status
sudo fail2ban-client status sshd
```

#### 6.4 Security Scanning

```bash
# Run security audit
sudo lynis audit system

# Check for rootkits
sudo rkhunter --check
sudo chkrootkit

# Virus scan
sudo clamscan -r /var/www --log=/var/log/clamav/scan.log
```

## Maintenance & Operations

### Daily Operations

1. **Health Checks**
   ```bash
   # Check application status
   curl -f https://luxcrafts.com/health
   
   # Check system resources
   htop
   df -h
   free -m
   ```

2. **Log Review**
   ```bash
   # Check application logs
   tail -f {{ log_dir }}/app.log
   
   # Check security logs
   sudo tail -f /var/log/auth.log
   sudo fail2ban-client status
   ```

### Weekly Operations

1. **Security Updates**
   ```bash
   # Update system packages
   sudo apt update && sudo apt upgrade -y
   
   # Update Node.js dependencies
   cd {{ app_dir }}/current/clients/luxcrafts
   npm audit fix
   ```

2. **Backup Verification**
   ```bash
   # Verify database backups
   ls -la {{ backup_dir }}/database/
   
   # Test backup restoration
   /usr/local/bin/test-backup-restore
   ```

### Monthly Operations

1. **Performance Review**
   - Analyze response times and throughput
   - Review resource utilization trends
   - Optimize database queries
   - Update monitoring thresholds

2. **Security Audit**
   - Run comprehensive security scan
   - Review access logs and user activity
   - Update security policies
   - Rotate secrets and certificates

3. **Capacity Planning**
   - Review growth trends
   - Plan infrastructure scaling
   - Update disaster recovery procedures
   - Test backup and restore procedures

## Troubleshooting

### Common Issues

#### Application Not Starting
```bash
# Check PM2 status
pm2 status
pm2 logs luxcrafts

# Check system resources
free -m
df -h

# Check port availability
sudo netstat -tlnp | grep :3000
```

#### SSL Certificate Issues
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew --dry-run
sudo certbot renew --force-renewal
```

#### Database Connection Issues
```bash
# Check database status
sudo systemctl status postgresql

# Check connections
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"

# Check logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### High Resource Usage
```bash
# Identify resource-heavy processes
top
htop
iotop

# Check disk usage
du -sh /var/www/luxcrafts/*
du -sh /var/log/*

# Clean up logs
sudo logrotate -f /etc/logrotate.conf
```

### Emergency Procedures

#### Rollback Deployment
```bash
# Rollback via Dokploy
curl -X POST "https://your-server:3000/api/projects/{project-id}/rollback" \
  -H "Authorization: Bearer {api-token}"

# Manual rollback
cd {{ app_dir }}
ln -sfn releases/previous current
pm2 reload ecosystem.config.js
```

#### Scale Resources
```bash
# Scale PM2 instances
pm2 scale luxcrafts +2

# Scale infrastructure (Terraform)
terraform apply -var="droplet_count=3"
```

#### Security Incident Response
```bash
# Block suspicious IP
sudo ufw deny from suspicious.ip.address

# Check for intrusions
sudo aide --check
sudo rkhunter --check

# Review logs
sudo grep "suspicious.ip.address" /var/log/nginx/access.log
sudo journalctl -u fail2ban
```

## Performance Optimization

### Application Optimization

1. **Code Splitting and Lazy Loading**
2. **Image Optimization and CDN**
3. **Database Query Optimization**
4. **Caching Strategy Implementation**
5. **Bundle Size Optimization**

### Infrastructure Optimization

1. **Load Balancer Configuration**
2. **CDN Setup and Configuration**
3. **Database Performance Tuning**
4. **Nginx Optimization**
5. **Resource Scaling Automation**

## Compliance & Security

### Security Standards

- **OWASP Top 10** compliance
- **SOC 2 Type II** controls
- **GDPR** data protection
- **PCI DSS** (if handling payments)
- **ISO 27001** security management

### Audit Trail

- All system access logged
- Application events tracked
- Database changes audited
- Configuration changes versioned
- Security events monitored

## Support & Documentation

### Documentation

- [API Documentation](./docs/api.md)
- [Deployment Runbook](./docs/deployment.md)
- [Monitoring Guide](./docs/monitoring.md)
- [Security Procedures](./docs/security.md)
- [Troubleshooting Guide](./docs/troubleshooting.md)

### Support Contacts

- **Technical Lead**: <EMAIL>
- **DevOps Engineer**: <EMAIL>
- **Security Officer**: <EMAIL>
- **On-call Support**: +1-555-SUPPORT

---

**Last Updated**: {{ ansible_date_time.date }}
**Version**: 1.0.0
**Environment**: Production
**Maintained by**: DevOps Team