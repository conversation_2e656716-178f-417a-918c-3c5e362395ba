# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Snowpack dependency directory (https://snowpack.dev/)
web_modules/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# Comment in the public line in if your project uses Gatsby and not Next.js
# https://nextjs.org/blog/next-9-1#public-directory-support
# public

# vuepress build output
.vuepress/dist

# vuepress v2.x temp and cache directory
.temp
.cache

# Docusaurus cache and generated files
.docusaurus

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Vite
dist/
dist-ssr/
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore
docker-compose.override.yml

# Database
*.sqlite
*.sqlite3
*.db

# Blockchain & Crypto
.secret
*.pem
*.key
wallet.json
mnemonic.txt
private-keys/

# Build artifacts
build/
dist/
out/
.output/
.vercel/
.netlify/

# Testing
/coverage/
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# IDE files
*.swp
*.swo
*~

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Deployment
.deployment
.azure/
.aws/
.gcp/

# Monitoring
prometheus_data/
grafana_data/

# SSL Certificates
*.crt
*.csr
*.pem
*.p12
*.pfx

# Backup files
*.bak
*.backup
*.old

# Local development
.local/
.dev/

# Sensitive configuration
config/production.json
config/staging.json
secrets/

# Smart contract artifacts
artifacts/
cache/
typechain/
typechain-types/

# IPFS
.ipfs/

# Analytics
.analytics/

# Error tracking
.sentry/

# Performance monitoring
.newrelic/

# CDN
.cdn/

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig
kustomization.yaml

# Helm
*.tgz
charts/

# CI/CD
.github/workflows/secrets.yml
.gitlab-ci-local/

# Documentation
/docs/build/
/docs/.docusaurus/

# Storybook
storybook-static/

# Chromatic
build-storybook.log

# Local Netlify folder
.netlify

# Local Vercel folder
.vercel

# Turborepo
.turbo

# Nx
.nx/cache

# Rush
common/temp/

# Lerna
lerna-debug.log

# ESLint
.eslintcache

# Prettier
.prettierignore

# Husky
.husky/_

# Commitizen
.cz-config.js

# Semantic Release
.semanticrc

# Renovate
renovate.json

# Dependabot
.dependabot/

# Security
.snyk

# Performance
.lighthouse/

# Bundle analysis
bundle-analyzer-report.html
stats.json

# PWA
workbox-*.js
sw.js

# Capacitor
android/
ios/

# Expo
.expo/

# React Native
.react-native/

# Metro
.metro-health-check*

# Flipper
.flipper/

# CocoaPods
Pods/
Podfile.lock

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Xcode
*.xcodeproj
*.xcworkspace
DerivedData/

# Android
*.apk
*.aab
*.dex
*.class
bin/
gen/
proguard/

# Gradle
.gradle/
build/

# Maven
target/

# IntelliJ
*.iml
*.ipr
*.iws
.idea/

# Eclipse
.project
.classpath
.settings/

# NetBeans
nbproject/

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates

# Windows
ehthumbs.db
Thumbs.db

# macOS
.DS_Store
.AppleDouble
.LSOverride

# Linux
*~

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# JetBrains
.idea/
*.iml

# Custom project ignores
/backend/uploads/
/frontend/public/uploads/
/logs/
/backups/
/scripts/private/
/config/local.json
/data/
/tmp/