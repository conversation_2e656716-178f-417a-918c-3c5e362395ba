import React, { useState, useEffect } from 'react';
import { Search, Filter, Grid, List, Star, Clock, Users, Zap, Eye, ShoppingCart } from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { ProductSpline } from '../components/3d/SplineScene';

interface Product {
  id: string;
  name: string;
  description: string;
  image: string;
  price: number;
  originalPrice?: number;
  tickets: number;
  maxTickets: number;
  timeLeft: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  tags: string[];
  featured: boolean;
  aiScore: number;
}

interface FilterState {
  category: string;
  priceRange: [number, number];
  rarity: string;
  availability: string;
  sortBy: string;
}

const Catalog: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<FilterState>({
    category: 'all',
    priceRange: [0, 100000],
    rarity: 'all',
    availability: 'all',
    sortBy: 'featured'
  });

  // Mock products data with AI curation
  const mockProducts: Product[] = [
    {
      id: '1',
      name: 'iPhone 15 Pro Max',
      description: 'El smartphone más avanzado con tecnología A17 Pro y cámaras profesionales.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20titanium%20professional%20product%20photography%20studio%20lighting&image_size=square_hd',
      price: 50000,
      originalPrice: 55000,
      tickets: 1250,
      maxTickets: 2000,
      timeLeft: '2d 14h',
      rarity: 'legendary',
      category: 'tecnologia',
      tags: ['smartphone', 'apple', 'premium'],
      featured: true,
      aiScore: 9.8
    },
    {
      id: '2',
      name: 'PlayStation 5',
      description: 'Consola de nueva generación con gráficos 4K y experiencias inmersivas.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=PlayStation%205%20console%20white%20modern%20gaming%20setup%20RGB%20lighting&image_size=square_hd',
      price: 25000,
      tickets: 890,
      maxTickets: 1500,
      timeLeft: '1d 8h',
      rarity: 'epic',
      category: 'gaming',
      tags: ['consola', 'sony', 'gaming'],
      featured: true,
      aiScore: 9.5
    },
    {
      id: '3',
      name: 'MacBook Pro M3',
      description: 'Laptop profesional con chip M3 para creativos y desarrolladores.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=MacBook%20Pro%20M3%20space%20gray%20professional%20workspace%20minimalist%20design&image_size=square_hd',
      price: 75000,
      tickets: 2100,
      maxTickets: 3000,
      timeLeft: '3d 22h',
      rarity: 'legendary',
      category: 'tecnologia',
      tags: ['laptop', 'apple', 'profesional'],
      featured: true,
      aiScore: 9.7
    },
    {
      id: '4',
      name: 'AirPods Pro 2',
      description: 'Auriculares inalámbricos con cancelación activa de ruido.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=AirPods%20Pro%202%20white%20floating%20wireless%20earbuds%20premium%20audio&image_size=square_hd',
      price: 8000,
      tickets: 450,
      maxTickets: 800,
      timeLeft: '5h 30m',
      rarity: 'rare',
      category: 'audio',
      tags: ['auriculares', 'apple', 'inalambrico'],
      featured: false,
      aiScore: 8.9
    },
    {
      id: '5',
      name: 'Tesla Model Y',
      description: 'Vehículo eléctrico de lujo con autopilot y tecnología avanzada.',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Tesla%20Model%20Y%20white%20electric%20car%20futuristic%20design%20clean%20background&image_size=square_hd',
      price: 200000,
      tickets: 5000,
      maxTickets: 10000,
      timeLeft: '7d 12h',
      rarity: 'legendary',
      category: 'vehiculos',
      tags: ['tesla', 'electrico', 'lujo'],
      featured: true,
      aiScore: 9.9
    }
  ];

  const categories = [
    { id: 'all', name: 'Todos', count: mockProducts.length },
    { id: 'tecnologia', name: 'Tecnología', count: mockProducts.filter(p => p.category === 'tecnologia').length },
    { id: 'gaming', name: 'Gaming', count: mockProducts.filter(p => p.category === 'gaming').length },
    { id: 'audio', name: 'Audio', count: mockProducts.filter(p => p.category === 'audio').length },
    { id: 'vehiculos', name: 'Vehículos', count: mockProducts.filter(p => p.category === 'vehiculos').length }
  ];

  const rarityColors = {
    common: 'from-gray-400 to-gray-600',
    rare: 'from-blue-400 to-blue-600',
    epic: 'from-purple-400 to-purple-600',
    legendary: 'from-amber-400 to-amber-600'
  };

  const rarityLabels = {
    common: 'Común',
    rare: 'Raro',
    epic: 'Épico',
    legendary: 'Legendario'
  };

  useEffect(() => {
    setProducts(mockProducts);
    setFilteredProducts(mockProducts);
  }, []);

  useEffect(() => {
    let filtered = products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = filters.category === 'all' || product.category === filters.category;
      const matchesPrice = product.price >= filters.priceRange[0] && product.price <= filters.priceRange[1];
      const matchesRarity = filters.rarity === 'all' || product.rarity === filters.rarity;
      const matchesAvailability = filters.availability === 'all' || 
                                 (filters.availability === 'available' && product.tickets < product.maxTickets) ||
                                 (filters.availability === 'ending-soon' && product.timeLeft.includes('h'));
      
      return matchesSearch && matchesCategory && matchesPrice && matchesRarity && matchesAvailability;
    });

    // Sort products
    switch (filters.sortBy) {
      case 'featured':
        filtered.sort((a, b) => (b.featured ? 1 : 0) - (a.featured ? 1 : 0) || b.aiScore - a.aiScore);
        break;
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'ending-soon':
        filtered.sort((a, b) => a.timeLeft.localeCompare(b.timeLeft));
        break;
      case 'popularity':
        filtered.sort((a, b) => b.tickets - a.tickets);
        break;
      default:
        break;
    }

    setFilteredProducts(filtered);
  }, [products, searchTerm, filters]);

  const getProgressPercentage = (tickets: number, maxTickets: number) => {
    return (tickets / maxTickets) * 100;
  };

  const ProductCard: React.FC<{ product: Product }> = ({ product }) => (
    <Card variant="glass" className="group hover:scale-105 transition-all duration-300 overflow-hidden">
      <div className="relative">
        <img 
          src={product.image} 
          alt={product.name}
          className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
        />
        
        {/* Rarity Badge */}
        <div className={`absolute top-3 right-3 px-2 py-1 rounded-full bg-gradient-to-r ${rarityColors[product.rarity]} text-white text-xs font-medium`}>
          {rarityLabels[product.rarity]}
        </div>
        
        {/* AI Score */}
        <div className="absolute top-3 left-3 bg-black/50 backdrop-blur-md rounded-lg px-2 py-1 flex items-center">
          <Zap className="w-3 h-3 text-amber-400 mr-1" />
          <span className="text-white text-xs font-medium">{product.aiScore}</span>
        </div>
        
        {/* Time Left */}
        <div className="absolute bottom-3 left-3 bg-black/50 backdrop-blur-md rounded-lg px-2 py-1 flex items-center">
          <Clock className="w-3 h-3 text-white mr-1" />
          <span className="text-white text-xs">{product.timeLeft}</span>
        </div>
        
        {/* Featured Badge */}
        {product.featured && (
          <div className="absolute bottom-3 right-3 bg-gradient-to-r from-purple-500 to-amber-500 rounded-lg px-2 py-1">
            <Star className="w-3 h-3 text-white" />
          </div>
        )}
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-bold text-white mb-2 line-clamp-1">{product.name}</h3>
        <p className="text-white/70 text-sm mb-4 line-clamp-2">{product.description}</p>
        
        <div className="flex justify-between items-center mb-3">
          <div>
            <p className="text-white/70 text-xs">Precio del boleto</p>
            <div className="flex items-center space-x-2">
              <p className="text-lg font-bold text-white">${product.price.toLocaleString()}</p>
              {product.originalPrice && (
                <p className="text-sm text-white/50 line-through">${product.originalPrice.toLocaleString()}</p>
              )}
            </div>
          </div>
          <div className="text-right">
            <p className="text-white/70 text-xs">Vendidos</p>
            <p className="text-sm font-semibold text-amber-400">{product.tickets}/{product.maxTickets}</p>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="w-full bg-white/20 rounded-full h-2">
            <div 
              className="bg-gradient-to-r from-purple-500 to-amber-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${getProgressPercentage(product.tickets, product.maxTickets)}%` }}
            ></div>
          </div>
          <p className="text-xs text-white/60 mt-1">
            {getProgressPercentage(product.tickets, product.maxTickets).toFixed(1)}% vendido
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button size="sm" className="flex-1">
            <ShoppingCart className="w-4 h-4 mr-1" />
            Comprar
          </Button>
          <Button variant="outline" size="sm" className="border-white/30 text-white hover:bg-white/10">
            <Eye className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen pt-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Catálogo de <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Premios</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Descubre premios increíbles curados por nuestra IA. Cada producto es seleccionado por su calidad y demanda.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 w-5 h-5" />
              <input
                type="text"
                placeholder="Buscar productos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 backdrop-blur-md border border-white/20 rounded-xl text-white placeholder-white/60 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            {/* View Mode and Filters */}
            <div className="flex items-center space-x-4">
              <div className="flex bg-white/10 backdrop-blur-md rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-all ${viewMode === 'grid' ? 'bg-purple-500 text-white' : 'text-white/60 hover:text-white'}`}
                >
                  <Grid className="w-5 h-5" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-all ${viewMode === 'list' ? 'bg-purple-500 text-white' : 'text-white/60 hover:text-white'}`}
                >
                  <List className="w-5 h-5" />
                </button>
              </div>
              
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
                className="border-white/30 text-white hover:bg-white/10"
              >
                <Filter className="w-4 h-4 mr-2" />
                Filtros
              </Button>
            </div>
          </div>
        </div>

        {/* Categories */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setFilters(prev => ({ ...prev, category: category.id }))}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
                  filters.category === category.id
                    ? 'bg-gradient-to-r from-purple-500 to-amber-500 text-white'
                    : 'bg-white/10 text-white/80 hover:bg-white/20'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <Card variant="glass" className="mb-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Price Range */}
              <div>
                <label className="block text-white font-medium mb-2">Rango de Precio</label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="200000"
                    value={filters.priceRange[1]}
                    onChange={(e) => setFilters(prev => ({ ...prev, priceRange: [0, parseInt(e.target.value)] }))}
                    className="w-full"
                  />
                  <p className="text-white/70 text-sm">Hasta ${filters.priceRange[1].toLocaleString()}</p>
                </div>
              </div>
              
              {/* Rarity */}
              <div>
                <label className="block text-white font-medium mb-2">Rareza</label>
                <select
                  value={filters.rarity}
                  onChange={(e) => setFilters(prev => ({ ...prev, rarity: e.target.value }))}
                  className="w-full p-2 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value="all">Todas</option>
                  <option value="common">Común</option>
                  <option value="rare">Raro</option>
                  <option value="epic">Épico</option>
                  <option value="legendary">Legendario</option>
                </select>
              </div>
              
              {/* Availability */}
              <div>
                <label className="block text-white font-medium mb-2">Disponibilidad</label>
                <select
                  value={filters.availability}
                  onChange={(e) => setFilters(prev => ({ ...prev, availability: e.target.value }))}
                  className="w-full p-2 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value="all">Todos</option>
                  <option value="available">Disponible</option>
                  <option value="ending-soon">Terminando Pronto</option>
                </select>
              </div>
              
              {/* Sort By */}
              <div>
                <label className="block text-white font-medium mb-2">Ordenar por</label>
                <select
                  value={filters.sortBy}
                  onChange={(e) => setFilters(prev => ({ ...prev, sortBy: e.target.value }))}
                  className="w-full p-2 bg-white/10 border border-white/20 rounded-lg text-white"
                >
                  <option value="featured">Destacados</option>
                  <option value="price-low">Precio: Menor a Mayor</option>
                  <option value="price-high">Precio: Mayor a Menor</option>
                  <option value="ending-soon">Terminando Pronto</option>
                  <option value="popularity">Popularidad</option>
                </select>
              </div>
            </div>
          </Card>
        )}

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-white/80">
            Mostrando {filteredProducts.length} de {products.length} productos
          </p>
        </div>

        {/* Products Grid */}
        <div className={`grid gap-6 mb-12 ${
          viewMode === 'grid' 
            ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' 
            : 'grid-cols-1'
        }`}>
          {filteredProducts.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* Empty State */}
        {filteredProducts.length === 0 && (
          <div className="text-center py-20">
            <div className="w-24 h-24 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-6">
              <Search className="w-12 h-12 text-white/60" />
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">No se encontraron productos</h3>
            <p className="text-white/70 mb-6">Intenta ajustar tus filtros o términos de búsqueda.</p>
            <Button onClick={() => {
              setSearchTerm('');
              setFilters({
                category: 'all',
                priceRange: [0, 100000],
                rarity: 'all',
                availability: 'all',
                sortBy: 'featured'
              });
            }}>
              Limpiar Filtros
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};

export default Catalog;