import { v4 as uuidv4 } from 'uuid';
import { promises as fs } from 'fs';
import path from 'path';
import { logger } from '@/utils/logger';
import { environment } from '@/config/environment';

export interface Document {
  id: string;
  organizationId: string;
  clientId?: string;
  rfpId?: string;
  onboardingFlowId?: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  status: 'pending' | 'approved' | 'rejected';
  uploadedBy: string;
  uploadedAt: Date;
  approvedBy?: string;
  approvedAt?: Date;
  rejectionReason?: string;
  metadata: Record<string, any>;
}

export interface UploadDocumentRequest {
  file: {
    filename: string;
    mimetype: string;
    data: Buffer;
  };
  clientId?: string;
  rfpId?: string;
  onboardingFlowId?: string;
  metadata?: Record<string, any>;
}

export class DocumentService {
  private documents: Map<string, Document> = new Map();
  private uploadDir: string;
  private isInitialized = false;

  constructor() {
    this.uploadDir = path.join(process.cwd(), 'uploads', 'documents');
  }

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Document Service...');
      
      // Ensure upload directory exists
      await this.ensureUploadDirectory();
      
      // Generate mock documents for development
      await this.generateMockDocuments();
      
      this.isInitialized = true;
      logger.info('Document Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Document Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Document Service...');
    this.documents.clear();
    this.isInitialized = false;
  }

  async uploadDocument(
    organizationId: string,
    uploadedBy: string,
    data: UploadDocumentRequest
  ): Promise<Document> {
    try {
      const documentId = uuidv4();
      const now = new Date();
      const fileExtension = path.extname(data.file.filename);
      const filename = `${documentId}${fileExtension}`;
      const filePath = path.join(this.uploadDir, filename);

      // Validate file
      await this.validateFile(data.file);

      // Save file to disk
      await fs.writeFile(filePath, data.file.data);

      const document: Document = {
        id: documentId,
        organizationId,
        clientId: data.clientId,
        rfpId: data.rfpId,
        onboardingFlowId: data.onboardingFlowId,
        filename,
        originalName: data.file.filename,
        mimeType: data.file.mimetype,
        size: data.file.data.length,
        url: `/api/documents/${documentId}/download`,
        status: 'pending',
        uploadedBy,
        uploadedAt: now,
        metadata: data.metadata || {}
      };

      this.documents.set(documentId, document);

      logger.info('Document uploaded successfully', {
        documentId,
        organizationId,
        filename: document.originalName,
        size: document.size,
        uploadedBy
      });

      return document;
    } catch (error) {
      logger.error('Failed to upload document', { error, data });
      throw error;
    }
  }

  async getDocument(documentId: string, organizationId: string): Promise<Document | null> {
    const document = this.documents.get(documentId);
    
    if (!document || document.organizationId !== organizationId) {
      return null;
    }

    return document;
  }

  async getDocumentFile(documentId: string, organizationId: string): Promise<{
    filename: string;
    mimeType: string;
    data: Buffer;
  } | null> {
    try {
      const document = await this.getDocument(documentId, organizationId);
      
      if (!document) {
        return null;
      }

      const filePath = path.join(this.uploadDir, document.filename);
      const data = await fs.readFile(filePath);

      return {
        filename: document.originalName,
        mimeType: document.mimeType,
        data
      };
    } catch (error) {
      logger.error('Failed to get document file', { error, documentId });
      throw error;
    }
  }

  async approveDocument(
    documentId: string,
    organizationId: string,
    approvedBy: string
  ): Promise<Document | null> {
    try {
      const document = await this.getDocument(documentId, organizationId);
      
      if (!document) {
        return null;
      }

      if (document.status !== 'pending') {
        throw new Error('Document is not in pending status');
      }

      const updatedDocument: Document = {
        ...document,
        status: 'approved',
        approvedBy,
        approvedAt: new Date()
      };

      this.documents.set(documentId, updatedDocument);

      logger.info('Document approved successfully', {
        documentId,
        organizationId,
        approvedBy
      });

      return updatedDocument;
    } catch (error) {
      logger.error('Failed to approve document', { error, documentId });
      throw error;
    }
  }

  async rejectDocument(
    documentId: string,
    organizationId: string,
    rejectedBy: string,
    reason: string
  ): Promise<Document | null> {
    try {
      const document = await this.getDocument(documentId, organizationId);
      
      if (!document) {
        return null;
      }

      if (document.status !== 'pending') {
        throw new Error('Document is not in pending status');
      }

      const updatedDocument: Document = {
        ...document,
        status: 'rejected',
        rejectionReason: reason
      };

      this.documents.set(documentId, updatedDocument);

      logger.info('Document rejected successfully', {
        documentId,
        organizationId,
        rejectedBy,
        reason
      });

      return updatedDocument;
    } catch (error) {
      logger.error('Failed to reject document', { error, documentId });
      throw error;
    }
  }

  async deleteDocument(documentId: string, organizationId: string): Promise<boolean> {
    try {
      const document = await this.getDocument(documentId, organizationId);
      
      if (!document) {
        return false;
      }

      // Delete file from disk
      const filePath = path.join(this.uploadDir, document.filename);
      try {
        await fs.unlink(filePath);
      } catch (error) {
        logger.warn('Failed to delete file from disk', { error, filePath });
      }

      this.documents.delete(documentId);

      logger.info('Document deleted successfully', {
        documentId,
        organizationId
      });

      return true;
    } catch (error) {
      logger.error('Failed to delete document', { error, documentId });
      throw error;
    }
  }

  async getDocuments(
    organizationId: string,
    filters: {
      clientId?: string;
      rfpId?: string;
      onboardingFlowId?: string;
      status?: Document['status'];
      uploadedBy?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ documents: Document[]; total: number; page: number; limit: number }> {
    try {
      let filteredDocuments = Array.from(this.documents.values())
        .filter(doc => doc.organizationId === organizationId);

      // Apply filters
      if (filters.clientId) {
        filteredDocuments = filteredDocuments.filter(doc => doc.clientId === filters.clientId);
      }

      if (filters.rfpId) {
        filteredDocuments = filteredDocuments.filter(doc => doc.rfpId === filters.rfpId);
      }

      if (filters.onboardingFlowId) {
        filteredDocuments = filteredDocuments.filter(doc => doc.onboardingFlowId === filters.onboardingFlowId);
      }

      if (filters.status) {
        filteredDocuments = filteredDocuments.filter(doc => doc.status === filters.status);
      }

      if (filters.uploadedBy) {
        filteredDocuments = filteredDocuments.filter(doc => doc.uploadedBy === filters.uploadedBy);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'uploadedAt';
      const sortOrder = filters.sortOrder || 'desc';
      
      filteredDocuments.sort((a, b) => {
        let aValue: any = (a as any)[sortBy];
        let bValue: any = (b as any)[sortBy];

        if (aValue instanceof Date) aValue = aValue.getTime();
        if (bValue instanceof Date) bValue = bValue.getTime();

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedDocuments = filteredDocuments.slice(startIndex, endIndex);

      return {
        documents: paginatedDocuments,
        total: filteredDocuments.length,
        page,
        limit
      };
    } catch (error) {
      logger.error('Failed to get documents', { error, filters });
      throw error;
    }
  }

  async getDocumentStats(organizationId: string): Promise<{
    total: number;
    byStatus: Record<Document['status'], number>;
    totalSize: number;
    recentlyUploaded: number;
  }> {
    try {
      const documents = Array.from(this.documents.values())
        .filter(doc => doc.organizationId === organizationId);

      const byStatus: Record<Document['status'], number> = {
        pending: 0,
        approved: 0,
        rejected: 0
      };

      let totalSize = 0;
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      let recentlyUploaded = 0;

      documents.forEach(doc => {
        byStatus[doc.status]++;
        totalSize += doc.size;
        
        if (doc.uploadedAt > sevenDaysAgo) {
          recentlyUploaded++;
        }
      });

      return {
        total: documents.length,
        byStatus,
        totalSize,
        recentlyUploaded
      };
    } catch (error) {
      logger.error('Failed to get document stats', { error, organizationId });
      throw error;
    }
  }

  private async validateFile(file: { filename: string; mimetype: string; data: Buffer }): Promise<void> {
    // Check file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.data.length > maxSize) {
      throw new Error('File size exceeds maximum limit of 10MB');
    }

    // Check allowed file types
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'text/plain',
      'image/jpeg',
      'image/png',
      'image/gif'
    ];

    if (!allowedTypes.includes(file.mimetype)) {
      throw new Error(`File type ${file.mimetype} is not allowed`);
    }

    // Check filename
    if (!file.filename || file.filename.length > 255) {
      throw new Error('Invalid filename');
    }
  }

  private async ensureUploadDirectory(): Promise<void> {
    try {
      await fs.access(this.uploadDir);
    } catch {
      await fs.mkdir(this.uploadDir, { recursive: true });
      logger.info('Created upload directory', { path: this.uploadDir });
    }
  }

  private async generateMockDocuments(): Promise<void> {
    const mockDocuments: Omit<Document, 'id' | 'uploadedAt' | 'approvedAt'>[] = [
      {
        organizationId: 'org-1',
        clientId: 'client-1',
        filename: 'contract-001.pdf',
        originalName: 'Service Agreement - TechCorp.pdf',
        mimeType: 'application/pdf',
        size: 245760,
        url: '/api/documents/doc-1/download',
        status: 'approved',
        uploadedBy: 'user-1',
        approvedBy: 'user-2',
        metadata: {
          category: 'contract',
          version: '1.0'
        }
      },
      {
        organizationId: 'org-1',
        rfpId: 'rfp-1',
        filename: 'requirements-001.docx',
        originalName: 'Project Requirements.docx',
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        size: 156432,
        url: '/api/documents/doc-2/download',
        status: 'pending',
        uploadedBy: 'user-3',
        metadata: {
          category: 'requirements',
          version: '2.1'
        }
      }
    ];

    for (const mockDoc of mockDocuments) {
      const now = new Date();
      const document: Document = {
        ...mockDoc,
        id: uuidv4(),
        uploadedAt: now,
        approvedAt: mockDoc.status === 'approved' ? now : undefined
      };
      
      this.documents.set(document.id, document);
    }

    logger.debug(`Generated ${mockDocuments.length} mock documents`);
  }
}