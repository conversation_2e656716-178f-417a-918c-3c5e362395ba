# Content Studio Service

AI-powered content generation and management service for the ESTRATIX platform.

## 🚀 Features

### AI Content Generation
- **Multi-Model Support**: OpenAI GPT-4, Anthropic Claude, and more
- **Content Types**: Blog posts, social media, emails, ads, and custom formats
- **Campaign Generation**: Multi-piece content campaigns with consistent messaging
- **Context-Aware**: Uses vector similarity search for relevant context retrieval

### Content Management
- **CRUD Operations**: Create, read, update, delete content
- **Version Control**: Track content changes and revisions
- **Publishing Workflow**: Draft, review, publish, archive states
- **Metadata Management**: Tags, categories, SEO data

### Content Optimization
- **SEO Optimization**: Keyword optimization, meta descriptions, readability
- **Engagement Optimization**: A/B testing suggestions, engagement predictions
- **Conversion Optimization**: CTA optimization, conversion rate predictions
- **Accessibility**: WCAG compliance checks and suggestions

### Content Analysis
- **Sentiment Analysis**: Emotional tone and sentiment scoring
- **Readability Analysis**: Flesch-Kincaid, SMOG, and other readability metrics
- **Brand Compliance**: Brand voice and guideline adherence checking
- **Performance Prediction**: AI-powered performance forecasting

### Scheduling & Calendar
- **Content Calendar**: Visual content planning and scheduling
- **Multi-Platform Publishing**: Schedule across different platforms
- **Bulk Operations**: Batch scheduling and management
- **Calendar Analytics**: Performance insights by time periods

### Campaign Management
- **Campaign Creation**: Multi-content campaign planning
- **Content Coordination**: Ensure consistent messaging across pieces
- **Performance Tracking**: Campaign-level analytics and ROI
- **A/B Testing**: Campaign variant testing and optimization

### Analytics & Reporting
- **Performance Metrics**: Reach, engagement, conversions, ROI
- **Trend Analysis**: Historical performance and trend identification
- **Custom Reports**: Configurable reporting with export options
- **Real-time Dashboards**: Live performance monitoring

### Advanced Features
- **Vector Search**: Semantic content similarity and context retrieval
- **Queue Processing**: Asynchronous AI generation with progress tracking
- **Rate Limiting**: API protection and usage management
- **Authentication**: JWT-based security with role-based access
- **File Upload**: Media management with cloud storage support

## 🏗️ Architecture

### Technology Stack
- **Framework**: Fastify (High-performance Node.js web framework)
- **AI Services**: OpenAI GPT-4, Anthropic Claude, Stability AI
- **Vector Database**: Milvus (Semantic search and similarity)
- **Queue System**: Bull + Redis (Async job processing)
- **Database**: MongoDB (Primary data storage)
- **Cache**: Redis (Session and data caching)
- **Authentication**: JWT with role-based access control

### Service Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Content Studio │────│   AI Services   │
│   (Fastify)     │    │    Service      │    │ (OpenAI/Claude) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vector DB     │    │    MongoDB      │    │     Redis       │
│   (Milvus)      │    │   (Primary)     │    │ (Cache/Queue)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Directory Structure
```
src/
├── config/           # Environment and configuration
├── middleware/       # Authentication, error handling
├── routes/          # API route definitions
│   ├── ai.ts        # AI generation endpoints
│   ├── content.ts   # Content management
│   ├── calendar.ts  # Scheduling endpoints
│   ├── campaigns.ts # Campaign management
│   └── analytics.ts # Analytics endpoints
├── services/        # Business logic services
│   ├── aiContentService.ts    # AI content generation
│   └── vectorStoreService.ts  # Vector database operations
├── queues/          # Async job processing
│   └── contentGeneration.ts  # Content generation queue
├── utils/           # Utility functions
│   └── logger.ts    # Logging configuration
└── index.ts         # Application entry point
```

## 🛠️ Setup & Installation

### Prerequisites
- Node.js 18+ and npm/pnpm
- MongoDB 6.0+
- Redis 6.0+
- Milvus 2.4+ (for vector search)

### Installation Steps

1. **Install Dependencies**
```bash
npm install
# or
pnpm install
```

2. **Environment Configuration**
```bash
cp .env.example .env
```

3. **Configure Environment Variables**
Edit `.env` with your configuration:
```env
# Required: AI Service API Keys
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# Required: Database Connections
MONGODB_URI=mongodb://localhost:27017/estratix_content_studio
REDIS_HOST=localhost
MILVUS_HOST=localhost:19530

# Required: JWT Secret
JWT_SECRET=your-super-secret-jwt-key
```

4. **Start Services**

**Development Mode:**
```bash
npm run dev
```

**Production Mode:**
```bash
npm run build
npm start
```

### Docker Setup (Optional)

```bash
# Start supporting services
docker-compose up -d mongodb redis milvus

# Build and run the service
docker build -t content-studio .
docker run -p 3001:3001 --env-file .env content-studio
```

## 📚 API Documentation

### Interactive Documentation
Once running, visit: `http://localhost:3001/docs`

### Authentication
All endpoints require JWT authentication:
```bash
Authorization: Bearer <your-jwt-token>
```

### Key Endpoints

#### Content Generation
```http
POST /api/ai/generate
Content-Type: application/json

{
  "type": "blog_post",
  "topic": "AI in Marketing",
  "audience": "marketing_professionals",
  "tone": "professional",
  "length": "medium"
}
```

#### Content Management
```http
GET    /api/content           # List content
POST   /api/content           # Create content
GET    /api/content/:id       # Get content
PUT    /api/content/:id       # Update content
DELETE /api/content/:id       # Delete content
```

#### Campaign Management
```http
GET    /api/campaigns         # List campaigns
POST   /api/campaigns         # Create campaign
POST   /api/campaigns/:id/launch  # Launch campaign
```

#### Analytics
```http
GET /api/analytics/overview   # Dashboard overview
GET /api/analytics/detailed   # Detailed analytics
GET /api/analytics/content/:id # Content-specific analytics
```

## 🔧 Configuration

### AI Model Configuration
Configure AI models in `src/config/environment.ts`:
```typescript
ai: {
  openai: {
    apiKey: process.env.OPENAI_API_KEY,
    defaultModel: 'gpt-4-turbo-preview',
    maxTokens: 4000,
  },
  anthropic: {
    apiKey: process.env.ANTHROPIC_API_KEY,
    defaultModel: 'claude-3-opus-20240229',
    maxTokens: 4000,
  },
}
```

### Queue Configuration
Adjust queue settings for performance:
```typescript
queue: {
  redis: {
    host: process.env.QUEUE_REDIS_HOST,
    port: parseInt(process.env.QUEUE_REDIS_PORT || '6379'),
  },
  concurrency: {
    singleContent: 5,
    campaign: 2,
    optimization: 3,
    analysis: 5,
  },
}
```

## 🚀 Deployment

### Production Checklist
- [ ] Set `NODE_ENV=production`
- [ ] Configure secure JWT secret
- [ ] Set up SSL/TLS certificates
- [ ] Configure production databases
- [ ] Set up monitoring and logging
- [ ] Configure rate limiting
- [ ] Set up backup strategies

### Environment Variables
See `.env.example` for all available configuration options.

### Health Checks
```http
GET /health
```
Returns service health status including database connections and queue status.

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Format code
npm run format
```

## 📊 Monitoring

### Logging
Structured logging with Pino:
- Request/response logging
- Error tracking
- Performance metrics
- Queue job tracking

### Metrics
- API response times
- Queue processing times
- AI service usage and costs
- Database performance
- Error rates

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is part of the ESTRATIX platform and is proprietary software.

## 🆘 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the health endpoint at `/health`
- Check application logs for errors
- Ensure all required services are running

---

**ESTRATIX Content Studio** - Empowering content creation with AI