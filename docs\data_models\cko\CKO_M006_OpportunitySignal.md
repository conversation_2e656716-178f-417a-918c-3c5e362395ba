# CKO_M006: OpportunitySignal

## 1. Metadata

*   **Data Model ID:** CKO_M006
*   **Data Model Name:** OpportunitySignal
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A004_KnowledgeAnalystAgent

## 2. Purpose

*   This data model represents a concise, actionable signal highlighting a potential opportunity for ESTRATIX. Opportunities could relate to new markets, technologies, service offerings, partnerships, efficiency gains, or competitive advantages. It is a key output of `CKO_F002_KnowledgeAnalysisAndInsightGeneration` and a primary input for `CPO_F001_StrategicOpportunityToProposalDevelopment`.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime, timedelta
from enum import Enum
import uuid

class CKO_OpportunityTypeEnum(str, Enum):
    NEW_MARKET_ENTRY = "New Market Entry"
    NEW_PRODUCT_SERVICE = "New Product/Service Development"
    TECHNOLOGY_ADOPTION = "Technology Adoption/Innovation"
    PARTNERSHIP_ALLIANCE = "Partnership/Alliance"
    PROCESS_IMPROVEMENT = "Process Improvement/Efficiency Gain"
    COMPETITIVE_ADVANTAGE = "Competitive Advantage"
    TALENT_ACQUISITION = "Talent Acquisition"
    INVESTMENT_OPPORTUNITY = "Investment Opportunity"
    REGULATORY_CHANGE_ADVANTAGE = "Regulatory Change Advantage"
    OTHER = "Other"

class CKO_SignalStatusEnum(str, Enum):
    NEW = "New"
    UNDER_EVALUATION = "Under Evaluation (by CPO/CSO)"
    PROPOSAL_IN_DEVELOPMENT = "Proposal in Development"
    ACTIONED = "Actioned (Proposal Submitted/Approved)"
    DEFERRED = "Deferred"
    REJECTED = "Rejected/Not Pursued"
    ARCHIVED = "Archived"

class CKO_UrgencyLevelEnum(str, Enum):
    IMMEDIATE = "Immediate (within 1 month)"
    SHORT_TERM = "Short-term (1-3 months)"
    MEDIUM_TERM = "Medium-term (3-12 months)"
    LONG_TERM = "Long-term (1+ year)"

class CKO_OpportunitySignal(BaseModel):
    signal_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the opportunity signal.")
    title: str = Field(..., description="Concise title summarizing the opportunity.")
    description: str = Field(..., description="Detailed description of the opportunity, its context, and potential benefits for ESTRATIX.")
    opportunity_type: CKO_OpportunityTypeEnum = Field(..., description="Categorization of the opportunity.")
    
    # Supporting Evidence & Context
    source_insight_report_id: Optional[uuid.UUID] = Field(None, description="ID of the CKO_M005_InsightReport from which this signal was primarily derived, if any.")
    source_asset_ids: List[uuid.UUID] = Field(default_factory=list, description="List of key CKO_M003_CuratedKnowledgeAsset IDs supporting this signal.")
    source_graph_node_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="Key CKO_M008_KnowledgeGraphNode IDs related to this opportunity.")
    
    # Assessment & Prioritization
    potential_impact: str = Field(..., description="Qualitative assessment of the potential positive impact on ESTRATIX (e.g., revenue growth, cost savings, market share, strategic positioning).")
    confidence_in_opportunity: float = Field(..., ge=0.0, le=1.0, description="Confidence level (0.0-1.0) that this is a real and viable opportunity.")
    urgency_level: CKO_UrgencyLevelEnum = Field(..., description="Assessed urgency for ESTRATIX to act on this opportunity.")
    preliminary_risks: Optional[List[str]] = Field(default_factory=list, description="Initial thoughts on potential risks or challenges associated with pursuing this opportunity.")
    estimated_time_to_realize: Optional[timedelta] = Field(None, description="Rough estimate of the time it might take to realize benefits if pursued.")
    
    # Origination & Status
    generated_by_agent_id: str = Field(..., description="ID of the ESTRATIX agent (e.g., CKO_A004_KnowledgeAnalystAgent) that generated this signal.")
    generation_date: datetime = Field(default_factory=datetime.utcnow, description="Date the signal was generated.")
    signal_status: CKO_SignalStatusEnum = Field(default=CKO_SignalStatusEnum.NEW, description="Current status of the opportunity signal.")
    status_last_updated: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of the last status update.")
    assigned_to_co_office: Optional[str] = Field(None, description="Command Office primarily responsible for evaluation (e.g., CPO, CSO). Default could be CPO.")
    evaluation_notes: Optional[str] = Field(None, description="Notes from the evaluation process by CPO/CSO or other stakeholders.")
    
    # Link to Action
    related_proposal_id: Optional[uuid.UUID] = Field(None, description="ID of the CPO_F001 proposal developed from this signal, if applicable.")

    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, signal-specific metadata.")

## 4. Field Descriptions

| Field Name                      | Type                             | Description                                                                                                | Required | Example Value(s)                                                              |
|---------------------------------|----------------------------------|------------------------------------------------------------------------------------------------------------|----------|-------------------------------------------------------------------------------|
| `signal_id`                     | `uuid.UUID`                      | Unique identifier for the opportunity signal.                                                              | Yes      | `"abc123ef-456g-hijk-lmno-pqrstuvwxyza"`                                      |
| `title`                         | `str`                            | Concise title summarizing the opportunity.                                                                 | Yes      | `"Opportunity to Enter Asian EdTech Market"`                                    |
| `description`                   | `str`                            | Detailed description of the opportunity, context, and potential benefits.                                  | Yes      | `"Growing demand for online learning platforms in Southeast Asia..."`           |
| `opportunity_type`              | `CKO_OpportunityTypeEnum`        | Categorization of the opportunity.                                                                         | Yes      | `"New Market Entry"`                                                            |
| `source_insight_report_id`      | `Optional[uuid.UUID]`            | ID of the `CKO_M005_InsightReport` if derived from one.                                                    | No       | `"f0e1d2c3-b4a5-6789-0123-456789abcdef"`                                       |
| `source_asset_ids`              | `List[uuid.UUID]`                | List of key `CKO_M003_CuratedKnowledgeAsset` IDs supporting this signal.                                   | Yes      | `["c1d2e3f4-..."]`                                                             |
| `source_graph_node_ids`         | `Optional[List[uuid.UUID]]`      | Key `CKO_M008_KnowledgeGraphNode` IDs related to this opportunity.                                         | No       | `["e1f2a3b4-..."]`                                                             |
| `potential_impact`              | `str`                            | Qualitative assessment of the potential positive impact.                                                   | Yes      | `"Significant revenue growth (est. $XM annually), expands global footprint."` |
| `confidence_in_opportunity`     | `float`                          | Confidence level (0.0-1.0) in the viability of the opportunity.                                            | Yes      | `0.85`                                                                        |
| `urgency_level`                 | `CKO_UrgencyLevelEnum`           | Assessed urgency for ESTRATIX to act.                                                                      | Yes      | `"Medium-term"`                                                               |
| `preliminary_risks`             | `Optional[List[str]]`            | Initial thoughts on potential risks or challenges.                                                         | No       | `["High competition", "Regulatory hurdles"]`                                  |
| `estimated_time_to_realize`     | `Optional[timedelta]`            | Rough estimate of time to realize benefits.                                                                | No       | `timedelta(days=365)`                                                         |
| `generated_by_agent_id`         | `str`                            | ID of the ESTRATIX agent that generated this signal.                                                       | Yes      | `"CKO_A004"`                                                                  |
| `generation_date`               | `datetime`                       | Date the signal was generated.                                                                             | Yes      | `"2025-05-28T14:00:00Z"`                                                      |
| `signal_status`                 | `CKO_SignalStatusEnum`           | Current status of the opportunity signal.                                                                  | Yes      | `"New"`, `"Under Evaluation"`                                                 |
| `status_last_updated`           | `datetime`                       | Timestamp of the last status update.                                                                       | Yes      | `"2025-05-28T14:00:00Z"`                                                      |
| `assigned_to_co_office`         | `Optional[str]`                  | Command Office primarily responsible for evaluation.                                                       | No       | `"CPO"`                                                                       |
| `evaluation_notes`              | `Optional[str]`                  | Notes from the evaluation process.                                                                         | No       | `"CPO initial review: Promising, needs further market sizing."`                 |
| `related_proposal_id`           | `Optional[uuid.UUID]`            | ID of the proposal developed from this signal.                                                             | No       | `"prop123-uuid-goes-here"`                                                    |
| `custom_fields`                 | `Optional[Dict[str, Any]]`       | Flexible dictionary for additional, signal-specific metadata.                                              | No       | `{"target_segment": "K-12"}`                                                  |

## 5. Relationships to Other Data Models

*   **`source_insight_report_id` (references `CKO_M005_InsightReport.report_id`):** The report that may have triggered or heavily informed this signal.
*   **`source_asset_ids` (references `CKO_M003_CuratedKnowledgeAsset.asset_id`):** Supporting evidence from curated knowledge.
*   **`source_graph_node_ids` (references `CKO_M008_KnowledgeGraphNode.node_id`):** Related entities in the knowledge graph.
*   This model is a primary input to `CPO_F001_StrategicOpportunityToProposalDevelopment`.
*   Can lead to the creation of a proposal (linking via `related_proposal_id`).

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (specifically `CKO_P008_IdentifyAndLogOpportunityOrThreat`). Created by `CKO_A004_KnowledgeAnalystAgent`.
*   **Primary Consuming Flow(s)/Process(es):** `CPO_F001_StrategicOpportunityToProposalDevelopment` (specifically `CPO_T001: Intake & Prioritize Strategic Inputs`).
*   **Key Agents Interacting:** `CKO_A004_KnowledgeAnalystAgent` (creates), `CPO_A001_PortfolioAnalystAgent` (reads, updates status), CSO agents (read, evaluate).

## 7. Notes / Future Considerations

*   A scoring system for `potential_impact` might be developed for more quantitative comparison.
*   The `signal_status` lifecycle should be tightly coupled with the CPO/CSO evaluation workflows.
*   Consider adding a field for 'Opportunity Champion' if a specific individual or CO takes ownership early on.
*   Feedback loop: If a signal is rejected or deferred, the reasons should be captured in `evaluation_notes` to inform future signal generation.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                                                                                                                            |
| :------ | :--------- | :--------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M006 to CKO_M006. Updated ID, version, owner, SME, Pydantic enum and class names, and all internal KNO references to CKO (including CKO_M008 and CKO_P008). Updated Last Updated date. Added revision history. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the OpportunitySignal data model.                                                                                                                                                              |
