import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { validateCreateRFP, validateUpdateRFP, ValidationError } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { RFP, CreateRFPRequest, UpdateRFPRequest } from '@/types';

export interface GetRFPsQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  priority?: string;
  clientId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface RFPParams {
  id: string;
}

export interface SubmitRFPBody {
  notes?: string;
}

export interface ReviewRFPBody {
  status: 'approved' | 'rejected';
  reviewNotes?: string;
  estimatedBudget?: number;
  estimatedTimeline?: string;
}

export async function rfpRoutes(fastify: FastifyInstance) {
  // Get all RFPs with filtering and pagination
  fastify.get('/rfps', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get all RFPs with filtering and pagination',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          search: { type: 'string' },
          status: { type: 'string', enum: ['draft', 'pending', 'under_review', 'approved', 'rejected'] },
          priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] },
          clientId: { type: 'string', format: 'uuid' },
          sortBy: { type: 'string', enum: ['title', 'status', 'priority', 'budget', 'submittedAt', 'createdAt'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            rfps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  clientId: { type: 'string' },
                  status: { type: 'string' },
                  priority: { type: 'string' },
                  budget: { type: 'number' },
                  timeline: { type: 'string' },
                  requirements: { type: 'array', items: { type: 'string' } },
                  attachments: { type: 'array', items: { type: 'string' } },
                  submittedAt: { type: 'string' },
                  reviewedAt: { type: 'string' },
                  reviewedBy: { type: 'string' },
                  reviewNotes: { type: 'string' },
                  estimatedBudget: { type: 'number' },
                  estimatedTimeline: { type: 'string' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                pages: { type: 'integer' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetRFPsQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10, search, status, priority, clientId, sortBy = 'createdAt', sortOrder = 'desc' } = request.query;
      
      const result = await fastify.rfpService.getRFPs({
        page,
        limit,
        search,
        status,
        priority,
        clientId,
        sortBy,
        sortOrder
      });

      logger.info('RFPs retrieved', {
        userId: (request as any).user?.id,
        count: result.rfps.length,
        page,
        limit
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to get RFPs', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve RFPs' });
    }
  });

  // Get RFP by ID
  fastify.get('/rfps/:id', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get RFP by ID',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            title: { type: 'string' },
            description: { type: 'string' },
            clientId: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' },
            budget: { type: 'number' },
            timeline: { type: 'string' },
            requirements: { type: 'array', items: { type: 'string' } },
            attachments: { type: 'array', items: { type: 'string' } },
            submittedAt: { type: 'string' },
            reviewedAt: { type: 'string' },
            reviewedBy: { type: 'string' },
            reviewNotes: { type: 'string' },
            estimatedBudget: { type: 'number' },
            estimatedTimeline: { type: 'string' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: RFPParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const rfp = await fastify.rfpService.getRFPById(id);
      
      if (!rfp) {
        return reply.status(404).send({ error: 'RFP not found' });
      }

      logger.info('RFP retrieved', {
        userId: (request as any).user?.id,
        rfpId: id
      });

      return reply.status(200).send(rfp);
    } catch (error) {
      logger.error('Failed to get RFP', { error, rfpId: request.params.id });
      return reply.status(500).send({ error: 'Failed to retrieve RFP' });
    }
  });

  // Create new RFP
  fastify.post('/rfps', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Create a new RFP',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', minLength: 1, maxLength: 200 },
          description: { type: 'string', minLength: 1, maxLength: 5000 },
          clientId: { type: 'string', format: 'uuid' },
          priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'], default: 'medium' },
          budget: { type: 'number', minimum: 0 },
          timeline: { type: 'string', maxLength: 100 },
          requirements: { type: 'array', items: { type: 'string' }, maxItems: 50 },
          attachments: { type: 'array', items: { type: 'string' }, maxItems: 10 }
        },
        required: ['title', 'description', 'clientId']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            title: { type: 'string' },
            description: { type: 'string' },
            clientId: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' },
            budget: { type: 'number' },
            timeline: { type: 'string' },
            requirements: { type: 'array', items: { type: 'string' } },
            attachments: { type: 'array', items: { type: 'string' } },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'string' } }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateRFPRequest }>, reply: FastifyReply) => {
    try {
      // Validate request body
      const validationResult = validateCreateRFP(request.body);
      if (!validationResult.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validationResult.error.errors.map(err => err.message)
        });
      }

      // Verify client exists
      const client = await fastify.clientService.getClientById(request.body.clientId);
      if (!client) {
        return reply.status(400).send({ error: 'Client not found' });
      }

      const rfp = await fastify.rfpService.createRFP(request.body);

      logger.info('RFP created', {
        userId: (request as any).user?.id,
        rfpId: rfp.id,
        clientId: rfp.clientId
      });

      return reply.status(201).send(rfp);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to create RFP', { error, body: request.body });
      return reply.status(500).send({ error: 'Failed to create RFP' });
    }
  });

  // Update RFP
  fastify.put('/rfps/:id', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Update RFP information',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          title: { type: 'string', minLength: 1, maxLength: 200 },
          description: { type: 'string', minLength: 1, maxLength: 5000 },
          priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] },
          budget: { type: 'number', minimum: 0 },
          timeline: { type: 'string', maxLength: 100 },
          requirements: { type: 'array', items: { type: 'string' }, maxItems: 50 },
          attachments: { type: 'array', items: { type: 'string' }, maxItems: 10 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            title: { type: 'string' },
            description: { type: 'string' },
            clientId: { type: 'string' },
            status: { type: 'string' },
            priority: { type: 'string' },
            budget: { type: 'number' },
            timeline: { type: 'string' },
            requirements: { type: 'array', items: { type: 'string' } },
            attachments: { type: 'array', items: { type: 'string' } },
            submittedAt: { type: 'string' },
            reviewedAt: { type: 'string' },
            reviewedBy: { type: 'string' },
            reviewNotes: { type: 'string' },
            estimatedBudget: { type: 'number' },
            estimatedTimeline: { type: 'string' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'string' } }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: RFPParams; Body: UpdateRFPRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      // Validate request body
      const validationResult = validateUpdateRFP(request.body);
      if (!validationResult.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validationResult.error.errors.map(err => err.message)
        });
      }

      // Check if RFP exists
      const existingRFP = await fastify.rfpService.getRFPById(id);
      if (!existingRFP) {
        return reply.status(404).send({ error: 'RFP not found' });
      }

      // Check if RFP can be updated (not submitted or under review)
      if (existingRFP.status === 'pending' || existingRFP.status === 'under_review') {
        return reply.status(400).send({ error: 'Cannot update RFP that is pending or under review' });
      }

      const updatedRFP = await fastify.rfpService.updateRFP(id, request.body);

      logger.info('RFP updated', {
        userId: (request as any).user?.id,
        rfpId: id,
        changes: Object.keys(request.body)
      });

      return reply.status(200).send(updatedRFP);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to update RFP', { error, rfpId: request.params.id, body: request.body });
      return reply.status(500).send({ error: 'Failed to update RFP' });
    }
  });

  // Submit RFP for review
  fastify.post('/rfps/:id/submit', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Submit RFP for review',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          notes: { type: 'string', maxLength: 1000 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            submittedAt: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: RFPParams; Body: SubmitRFPBody }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { notes } = request.body;
      
      const result = await fastify.rfpService.submitRFP(id, notes);
      
      if (!result) {
        return reply.status(404).send({ error: 'RFP not found' });
      }

      // Get client email for notification
      const rfp = await fastify.rfpService.getRFPById(id);
      if (rfp) {
        const client = await fastify.clientService.getClientById(rfp.clientId);
        if (client) {
          try {
            await fastify.emailService.sendRFPSubmissionConfirmation(rfp, client.email);
          } catch (emailError) {
            logger.warn('Failed to send RFP submission confirmation email', { error: emailError, rfpId: id });
          }
        }
      }

      logger.info('RFP submitted', {
        userId: (request as any).user?.id,
        rfpId: id
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to submit RFP', { error, rfpId: request.params.id });
      return reply.status(500).send({ error: 'Failed to submit RFP' });
    }
  });

  // Review RFP (approve/reject)
  fastify.post('/rfps/:id/review', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Review RFP (approve or reject)',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          status: { type: 'string', enum: ['approved', 'rejected'] },
          reviewNotes: { type: 'string', maxLength: 1000 },
          estimatedBudget: { type: 'number', minimum: 0 },
          estimatedTimeline: { type: 'string', maxLength: 100 }
        },
        required: ['status']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            reviewedAt: { type: 'string' },
            reviewedBy: { type: 'string' },
            reviewNotes: { type: 'string' },
            estimatedBudget: { type: 'number' },
            estimatedTimeline: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: RFPParams; Body: ReviewRFPBody }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { status, reviewNotes, estimatedBudget, estimatedTimeline } = request.body;
      const reviewerId = (request as any).user?.id;
      
      const result = await fastify.rfpService.reviewRFP(id, {
        status,
        reviewNotes,
        estimatedBudget,
        estimatedTimeline,
        reviewedBy: reviewerId
      });
      
      if (!result) {
        return reply.status(404).send({ error: 'RFP not found' });
      }

      // Send status update email to client
      const rfp = await fastify.rfpService.getRFPById(id);
      if (rfp) {
        const client = await fastify.clientService.getClientById(rfp.clientId);
        if (client) {
          try {
            await fastify.emailService.sendRFPStatusUpdate(rfp, client.email);
          } catch (emailError) {
            logger.warn('Failed to send RFP status update email', { error: emailError, rfpId: id });
          }
        }
      }

      logger.info('RFP reviewed', {
        userId: reviewerId,
        rfpId: id,
        status,
        reviewNotes: reviewNotes ? 'provided' : 'none'
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to review RFP', { error, rfpId: request.params.id, body: request.body });
      return reply.status(500).send({ error: 'Failed to review RFP' });
    }
  });

  // Delete RFP
  fastify.delete('/rfps/:id', {
    preHandler: [authenticateToken, requireRole(['admin'])],
    schema: {
      description: 'Delete RFP',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        204: {
          type: 'null'
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: RFPParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const deleted = await fastify.rfpService.deleteRFP(id);
      
      if (!deleted) {
        return reply.status(404).send({ error: 'RFP not found' });
      }

      logger.info('RFP deleted', {
        userId: (request as any).user?.id,
        rfpId: id
      });

      return reply.status(204).send();
    } catch (error) {
      logger.error('Failed to delete RFP', { error, rfpId: request.params.id });
      return reply.status(500).send({ error: 'Failed to delete RFP' });
    }
  });

  // Get RFP statistics
  fastify.get('/rfps/stats', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get RFP statistics',
      tags: ['RFPs'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            draft: { type: 'integer' },
            pending: { type: 'integer' },
            underReview: { type: 'integer' },
            approved: { type: 'integer' },
            rejected: { type: 'integer' },
            approvalRate: { type: 'number' },
            averageProcessingTime: { type: 'number' },
            byPriority: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await fastify.rfpService.getRFPStats();

      logger.info('RFP statistics retrieved', {
        userId: (request as any).user?.id,
        total: stats.total
      });

      return reply.status(200).send(stats);
    } catch (error) {
      logger.error('Failed to get RFP statistics', { error });
      return reply.status(500).send({ error: 'Failed to retrieve RFP statistics' });
    }
  });
}