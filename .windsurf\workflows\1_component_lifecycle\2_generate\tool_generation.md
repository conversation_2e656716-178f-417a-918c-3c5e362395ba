---
description: "Guides the agentic generation of runnable code for a defined ESTRATIX Tool, turning a definition into an executable component."
---

# Workflow: Generate ESTRATIX Tool Implementation

**Objective**: To translate a conceptual ESTRATIX Tool definition into a runnable, framework-agnostic Python script or a framework-specific tool class, located in the appropriate `src/` directory.

**Trigger**: A tool is marked as `Defined` in `docs/matrices/tool_matrix.md` and is ready for implementation.

**Responsible Command Office (Lead)**: CTO

**Dependencies**:

- A `Defined` tool in `docs/matrices/tool_matrix.md`.
- The corresponding tool definition file in `docs/tools/`.
- The `estratix_tool_template.md` for structure reference.

**Outputs**:

- A new Python file in `src/domain/tools/` or a framework-specific directory.
- An updated `Status` and `Implementation Path` in `docs/matrices/tool_matrix.md`.

---

## Steps

1.  **Initiation & Definition Retrieval**
    -   **Action**: An agent is given a `Tool_ID` (e.g., `k001`).
    -   **Action**: The agent reads `docs/matrices/tool_matrix.md` to find the row for the `Tool_ID` and retrieve the path to its definition file.
    -   **Action**: The agent reads the definition file to parse all details: name, function signature, dependencies, etc.
    -   **Tool**: `view_file_outline` or `view_line_range`.

2.  **Construct Implementation Paths**
    -   **Action**: Based on the definition, construct the file paths for the new tool and its test.
    -   **Guidance**: Convert the `Tool Name` to `snake_case`. Use the `Owner Office` from the definition to determine the `officer_acronym_lowercase`.
    -   **Implementation Path**: `src/domain/tools/[officer_acronym_lowercase]/[Tool_ID]_[tool_name_snake_case].py`
    -   **Test Path**: `tests/domain/tools/[officer_acronym_lowercase]/test_[Tool_ID]_[tool_name_snake_case].py`

3.  **Generate Tool Code**
    -   **Action**: Create the Python file containing the tool's logic.
    -   **Tool**: `write_to_file`
    -   **Guidance**: Generate a complete, runnable file including necessary imports, the function/class signature with type hints, a comprehensive docstring, and placeholder logic.

4.  **Generate Unit Test**
    -   **Action**: Create the corresponding unit test file.
    -   **Tool**: `write_to_file`
    -   **Guidance**: Generate a test file using the `unittest` framework. Create placeholder test methods based on the tool's definition.

5.  **Update Tool Matrix**
    -   **Action**: Update the status and add the implementation path for the tool in `docs/matrices/tool_matrix.md`.
    -   **Tool**: `replace_file_content`
    -   **Example**:
        ```markdown
        <!-- replace_file_content(
            'docs/matrices/tool_matrix.md',
            ReplacementChunks=[{
                'AllowMultiple': false,
                'TargetContent': '| `k001` | GitHub Issue Creator | ... | CTO | Defined | [Definition](../tools/cto/k001_GitHubIssueCreator.md) | |',
                'ReplacementContent': '| `k001` | GitHub Issue Creator | ... | CTO | Implemented | [Definition](../tools/cto/k001_GitHubIssueCreator.md) | `src/domain/tools/cto/k001_github_issue_creator.py` |'
            }]
        ) -->
        ```

6.  **Finalization & Reporting**
    -   **Action**: Report the successful creation of the code and test files.
    -   **Output**: Provide direct links to the new `.py` and `test_...py` files and suggest running the tests to validate the implementation.