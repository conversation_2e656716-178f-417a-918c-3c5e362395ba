import { apiClient, ApiResponse } from './api.client';
import { User, UserRole, Permission } from '../types/auth';
import { Property } from './property.service';
import { ContentItem } from './content.service';
import { NFT } from './nft.service';
import { Booking } from './booking.service';
import { BaseService, buildSearchParams, paginate } from '../utils/service.utils';

// Admin types
export interface SystemStats {
  users: {
    total: number;
    active: number;
    newToday: number;
    byRole: Record<string, number>;
  };
  properties: {
    total: number;
    available: number;
    occupied: number;
    revenue: number;
  };
  bookings: {
    total: number;
    pending: number;
    confirmed: number;
    revenue: number;
  };
  content: {
    total: number;
    published: number;
    views: number;
    engagement: number;
  };
  nfts: {
    total: number;
    listed: number;
    volume: number;
    transactions: number;
  };
  system: {
    uptime: number;
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
  };
}

export interface ActivityLog {
  id: string;
  userId: string;
  userName: string;
  action: string;
  resource: string;
  resourceId: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  severity: 'info' | 'warning' | 'error' | 'critical';
}

export interface SystemAlert {
  id: string;
  type: 'security' | 'performance' | 'system' | 'business';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  source: string;
  isRead: boolean;
  isResolved: boolean;
  createdAt: Date;
  resolvedAt?: Date;
  resolvedBy?: string;
  metadata: Record<string, any>;
}

export interface ServiceStatus {
  name: string;
  status: 'healthy' | 'degraded' | 'down';
  uptime: number;
  responseTime: number;
  lastCheck: Date;
  endpoint: string;
  dependencies: string[];
  metrics: {
    requests: number;
    errors: number;
    avgResponseTime: number;
  };
}

export interface UserManagement {
  user: User;
  lastLogin: Date;
  loginCount: number;
  isBlocked: boolean;
  blockReason?: string;
  permissions: Permission[];
  activityScore: number;
  riskLevel: 'low' | 'medium' | 'high';
}

export interface ContentModeration {
  content: ContentItem;
  flagCount: number;
  flags: Array<{
    reason: string;
    reportedBy: string;
    timestamp: Date;
  }>;
  moderationStatus: 'pending' | 'approved' | 'rejected' | 'flagged';
  moderatedBy?: string;
  moderatedAt?: Date;
  moderationNotes?: string;
}

export interface FinancialReport {
  period: {
    start: Date;
    end: Date;
  };
  revenue: {
    total: number;
    bySource: Record<string, number>;
    growth: number;
  };
  expenses: {
    total: number;
    byCategory: Record<string, number>;
  };
  profit: {
    gross: number;
    net: number;
    margin: number;
  };
  transactions: {
    count: number;
    volume: number;
    averageValue: number;
  };
  projections: {
    nextMonth: number;
    nextQuarter: number;
    confidence: number;
  };
}

export interface BackupInfo {
  id: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'running' | 'completed' | 'failed';
  size: number;
  duration: number;
  createdAt: Date;
  location: string;
  checksum: string;
  metadata: Record<string, any>;
}

// Mock data for development
const mockSystemStats: SystemStats = {
  users: {
    total: 12450,
    active: 8920,
    newToday: 45,
    byRole: {
      'CONSUMER': 8500,
      'INVESTOR': 2100,
      'SERVICE_PROVIDER': 1200,
      'PROPERTY_MANAGER': 450,
      'ADMIN': 200
    }
  },
  properties: {
    total: 2340,
    available: 1890,
    occupied: 450,
    revenue: 2450000
  },
  bookings: {
    total: 5670,
    pending: 120,
    confirmed: 340,
    revenue: 890000
  },
  content: {
    total: 1250,
    published: 980,
    views: 450000,
    engagement: 12.5
  },
  nfts: {
    total: 890,
    listed: 340,
    volume: 1250000,
    transactions: 2340
  },
  system: {
    uptime: 99.8,
    cpuUsage: 45.2,
    memoryUsage: 67.8,
    diskUsage: 34.5
  }
};

const mockActivityLogs: ActivityLog[] = [
  {
    id: 'log-1',
    userId: 'user-1',
    userName: 'John Admin',
    action: 'USER_LOGIN',
    resource: 'auth',
    resourceId: 'user-1',
    details: { method: 'email', success: true },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    timestamp: new Date('2024-01-20T10:30:00Z'),
    severity: 'info'
  },
  {
    id: 'log-2',
    userId: 'user-2',
    userName: 'Sarah Manager',
    action: 'PROPERTY_UPDATE',
    resource: 'property',
    resourceId: 'prop-1',
    details: { field: 'price', oldValue: 8000, newValue: 8500 },
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    timestamp: new Date('2024-01-20T09:15:00Z'),
    severity: 'info'
  },
  {
    id: 'log-3',
    userId: 'system',
    userName: 'System',
    action: 'SECURITY_ALERT',
    resource: 'security',
    resourceId: 'alert-1',
    details: { type: 'failed_login_attempts', count: 5, ip: '********' },
    ipAddress: '********',
    userAgent: 'Unknown',
    timestamp: new Date('2024-01-20T08:45:00Z'),
    severity: 'warning'
  }
];

const mockSystemAlerts: SystemAlert[] = [
  {
    id: 'alert-1',
    type: 'security',
    severity: 'high',
    title: 'Multiple Failed Login Attempts',
    message: 'Detected 5 failed login attempts from IP ******** in the last 10 minutes',
    source: 'auth-service',
    isRead: false,
    isResolved: false,
    createdAt: new Date('2024-01-20T08:45:00Z'),
    metadata: {
      ip: '********',
      attempts: 5,
      timeWindow: '10m'
    }
  },
  {
    id: 'alert-2',
    type: 'performance',
    severity: 'medium',
    title: 'High Memory Usage',
    message: 'System memory usage has exceeded 80% for the last 15 minutes',
    source: 'monitoring-service',
    isRead: true,
    isResolved: false,
    createdAt: new Date('2024-01-20T07:30:00Z'),
    metadata: {
      memoryUsage: 82.5,
      threshold: 80,
      duration: '15m'
    }
  }
];

const mockServiceStatus: ServiceStatus[] = [
  {
    name: 'API Gateway',
    status: 'healthy',
    uptime: 99.9,
    responseTime: 45,
    lastCheck: new Date(),
    endpoint: '/health',
    dependencies: ['database', 'redis'],
    metrics: {
      requests: 125000,
      errors: 12,
      avgResponseTime: 42
    }
  },
  {
    name: 'Database',
    status: 'healthy',
    uptime: 99.8,
    responseTime: 12,
    lastCheck: new Date(),
    endpoint: '/db/health',
    dependencies: [],
    metrics: {
      requests: 89000,
      errors: 3,
      avgResponseTime: 15
    }
  },
  {
    name: 'Payment Service',
    status: 'degraded',
    uptime: 98.5,
    responseTime: 120,
    lastCheck: new Date(),
    endpoint: '/payments/health',
    dependencies: ['stripe-api', 'database'],
    metrics: {
      requests: 45000,
      errors: 89,
      avgResponseTime: 95
    }
  }
];

class AdminService extends BaseService {
  async getSystemStats(): Promise<SystemStats> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<SystemStats>('/admin/stats');
        return response.data;
      },
      async () => {
        return { ...mockSystemStats };
      },
      800
    );
  }

  async getActivityLogs(
    page: number = 1,
    limit: number = 50,
    filters?: {
      userId?: string;
      action?: string;
      resource?: string;
      severity?: ActivityLog['severity'];
      dateFrom?: Date;
      dateTo?: Date;
    }
  ): Promise<{ logs: ActivityLog[]; total: number }> {
    return this.handleApiCall(
      async () => {
        const params = buildSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          userId: filters?.userId,
          action: filters?.action,
          resource: filters?.resource,
          severity: filters?.severity,
          dateFrom: filters?.dateFrom?.toISOString(),
          dateTo: filters?.dateTo?.toISOString()
        });
        const response = await apiClient.get<{ logs: ActivityLog[]; total: number }>(
          `/admin/activity-logs?${params}`
        );
        return response.data;
      },
      async () => {
        let filteredLogs = [...mockActivityLogs];
        
        if (filters?.userId) {
          filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
        }
        if (filters?.action) {
          filteredLogs = filteredLogs.filter(log => 
            log.action.toLowerCase().includes(filters.action!.toLowerCase())
          );
        }
        if (filters?.resource) {
          filteredLogs = filteredLogs.filter(log => log.resource === filters.resource);
        }
        if (filters?.severity) {
          filteredLogs = filteredLogs.filter(log => log.severity === filters.severity);
        }
        
        const paginatedResult = paginate(filteredLogs, page, limit);
        
        return {
          logs: paginatedResult.items,
          total: filteredLogs.length
        };
      },
      600
    );
  }

  async getSystemAlerts(
    includeResolved: boolean = false
  ): Promise<SystemAlert[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<SystemAlert[]>(
          `/admin/alerts?includeResolved=${includeResolved}`
        );
        return response.data;
      },
      async () => {
        return includeResolved 
          ? [...mockSystemAlerts]
          : mockSystemAlerts.filter(alert => !alert.isResolved);
      },
      400
    );
  }

  async markAlertAsRead(alertId: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.patch(`/admin/alerts/${alertId}/read`);
      },
      async () => {
        const alert = mockSystemAlerts.find(a => a.id === alertId);
        if (alert) {
          alert.isRead = true;
        }
      },
      300
    );
  }

  async resolveAlert(alertId: string, resolution: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.patch(`/admin/alerts/${alertId}/resolve`, { resolution });
      },
      async () => {
        const alert = mockSystemAlerts.find(a => a.id === alertId);
        if (alert) {
          alert.isResolved = true;
          alert.resolvedAt = new Date();
          alert.resolvedBy = 'current-admin';
          alert.metadata.resolution = resolution;
        }
      },
      500
    );
  }

  async getServiceStatus(): Promise<ServiceStatus[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<ServiceStatus[]>('/admin/services/status');
        return response.data;
      },
      async () => [...mockServiceStatus],
      600
    );
  }

  async restartService(serviceName: string): Promise<{ success: boolean; message: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; message: string }>(
          `/admin/services/${serviceName}/restart`
        );
        return response.data;
      },
      async () => {
        const service = mockServiceStatus.find(s => s.name === serviceName);
        if (!service) {
          throw new Error('Service not found');
        }
        
        // Mock service restart
        service.status = 'healthy';
        service.lastCheck = new Date();
        service.responseTime = Math.floor(Math.random() * 50) + 20;
        
        return {
          success: true,
          message: `Service ${serviceName} restarted successfully`
        };
      },
      3000
    );
  }

  async getUserManagement(
    page: number = 1,
    limit: number = 20,
    filters?: {
      role?: string;
      isBlocked?: boolean;
      riskLevel?: UserManagement['riskLevel'];
      search?: string;
    }
  ): Promise<{ users: UserManagement[]; total: number }> {
    return this.handleApiCall(
      async () => {
        const params = buildSearchParams({
          page: page.toString(),
          limit: limit.toString(),
          role: filters?.role,
          isBlocked: filters?.isBlocked?.toString(),
          riskLevel: filters?.riskLevel,
          search: filters?.search
        });
        const response = await apiClient.get<{ users: UserManagement[]; total: number }>(
          `/admin/users?${params}`
        );
        return response.data;
      },
      async () => {
        // Mock user management data
        const mockUsers: UserManagement[] = [
          {
            user: {
              id: 'user-1',
              email: '<EMAIL>',
              name: 'John Doe',
              role: UserRole.CONSUMER,
              permissions: [Permission.BOOK_SERVICES, Permission.MANAGE_BOOKINGS],
              createdAt: new Date('2023-12-01'),
              lastLogin: new Date('2024-01-20T10:30:00Z'),
              isActive: true
            },
            lastLogin: new Date('2024-01-20T10:30:00Z'),
            loginCount: 145,
            isBlocked: false,
            permissions: [Permission.BOOK_SERVICES, Permission.MANAGE_BOOKINGS],
            activityScore: 85,
            riskLevel: 'low'
          },
          {
            user: {
              id: 'user-2',
              email: '<EMAIL>',
              name: 'Sarah Johnson',
              role: UserRole.INVESTOR,
              permissions: [Permission.VIEW_INVESTMENT_OPPORTUNITIES, Permission.MANAGE_PORTFOLIO, Permission.ACCESS_FINANCIAL_DATA],
              createdAt: new Date('2023-11-15'),
              lastLogin: new Date('2024-01-19T16:45:00Z'),
              isActive: true
            },
            lastLogin: new Date('2024-01-19T16:45:00Z'),
            loginCount: 89,
            isBlocked: false,
            permissions: [Permission.VIEW_INVESTMENT_OPPORTUNITIES, Permission.MANAGE_PORTFOLIO, Permission.ACCESS_FINANCIAL_DATA],
            activityScore: 92,
            riskLevel: 'low'
          }
        ];
        
        let filteredUsers = [...mockUsers];
        
        if (filters?.role) {
          filteredUsers = filteredUsers.filter(user => user.user.role === filters.role);
        }
        if (filters?.isBlocked !== undefined) {
          filteredUsers = filteredUsers.filter(user => user.isBlocked === filters.isBlocked);
        }
        if (filters?.riskLevel) {
          filteredUsers = filteredUsers.filter(user => user.riskLevel === filters.riskLevel);
        }
        if (filters?.search) {
          const search = filters.search.toLowerCase();
          filteredUsers = filteredUsers.filter(user => 
            user.user.name.toLowerCase().includes(search) ||
            user.user.email.toLowerCase().includes(search)
          );
        }
        
        const paginatedResult = paginate(filteredUsers, page, limit);
        
        return {
          users: paginatedResult.items,
          total: filteredUsers.length
        };
      },
      700
    );
  }

  async blockUser(userId: string, reason: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.patch(`/admin/users/${userId}/block`, { reason });
      },
      async () => {
        // Mock implementation - no action needed
      },
      500
    );
  }

  async unblockUser(userId: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.patch(`/admin/users/${userId}/unblock`);
      },
      async () => {
        // Mock implementation - no action needed
      },
      500
    );
  }

  async getFinancialReport(
    startDate: Date,
    endDate: Date
  ): Promise<FinancialReport> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<FinancialReport>(
          `/admin/reports/financial?start=${startDate.toISOString()}&end=${endDate.toISOString()}`
        );
        return response.data;
      },
      async () => {
        return {
          period: { start: startDate, end: endDate },
          revenue: {
            total: 2450000,
            bySource: {
              'Property Rentals': 1800000,
              'Service Bookings': 450000,
              'NFT Sales': 150000,
              'Platform Fees': 50000
            },
            growth: 12.5
          },
          expenses: {
            total: 1200000,
            byCategory: {
              'Operations': 600000,
              'Marketing': 300000,
              'Technology': 200000,
              'Legal & Compliance': 100000
            }
          },
          profit: {
            gross: 1250000,
            net: 1050000,
            margin: 42.9
          },
          transactions: {
            count: 15670,
            volume: 2450000,
            averageValue: 156.3
          },
          projections: {
            nextMonth: 2650000,
            nextQuarter: 7800000,
            confidence: 85.2
          }
        };
      },
      1000
    );
  }

  async createBackup(type: BackupInfo['type']): Promise<{ backupId: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ backupId: string }>('/admin/backup', { type });
        return response.data;
      },
      async () => {
        return {
          backupId: `backup-${Date.now()}`
        };
      },
      2000
    );
  }

  async getBackups(): Promise<BackupInfo[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<BackupInfo[]>('/admin/backups');
        return response.data;
      },
      async () => {
        return [
          {
            id: 'backup-1',
            type: 'full',
            status: 'completed',
            size: 2500000000, // 2.5GB
            duration: 1800, // 30 minutes
            createdAt: new Date('2024-01-20T02:00:00Z'),
            location: 's3://backups/full/backup-1.tar.gz',
            checksum: 'sha256:abc123def456...',
            metadata: {
              tables: 45,
              records: 1250000
            }
          },
          {
            id: 'backup-2',
            type: 'incremental',
            status: 'completed',
            size: 150000000, // 150MB
            duration: 120, // 2 minutes
            createdAt: new Date('2024-01-19T02:00:00Z'),
            location: 's3://backups/incremental/backup-2.tar.gz',
            checksum: 'sha256:def456ghi789...',
            metadata: {
              changes: 2340
            }
          }
        ];
      },
      500
    );
  }

  async restoreBackup(backupId: string): Promise<{ success: boolean; message: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; message: string }>(
          `/admin/backups/${backupId}/restore`
        );
        return response.data;
      },
      async () => {
        return {
          success: true,
          message: `Backup ${backupId} restored successfully`
        };
      },
      5000
    );
  }

  async exportData(
    type: 'users' | 'properties' | 'bookings' | 'content' | 'all',
    format: 'csv' | 'json' | 'xlsx'
  ): Promise<{ downloadUrl: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ downloadUrl: string }>('/admin/export', {
          type,
          format
        });
        return response.data;
      },
      async () => {
        return {
          downloadUrl: `https://example.com/exports/${type}-${Date.now()}.${format}`
        };
      },
      3000
    );
  }
}

export const adminService = new AdminService();
export default adminService;