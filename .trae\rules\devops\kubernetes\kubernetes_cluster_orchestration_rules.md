# ESTRATIX Agentic Rules: Kubernetes Cluster Orchestration
# Rule ID: R-DO-003

---

## 1. Core Principles

This document defines the agentic rules for orchestrating and managing Kubernetes clusters within the ESTRATIX ecosystem. These rules are designed to be enforced by DevOps, SecOps, and GitOps agents to ensure security, scalability, resilience, and operational efficiency. All deployments must adhere to these principles.

- **Principle 1: Declarative Configuration (GitOps First)**
  - **Rule:** The state of the Kubernetes cluster (applications, configurations, policies) MUST be defined declaratively in Git repositories as the single source of truth.
  - **Enforcement:** GitOps agents (e.g., ArgoCD, Flux) are responsible for synchronizing the cluster state with the Git repository. Manual changes via `kubectl apply/edit` are prohibited for managed resources.

- **Principle 2: Security by Design**
  - **Rule:** Security MUST be integrated into every stage of the lifecycle. This includes enforcing the principle of least privilege, isolating workloads, and securing network traffic.
  - **Enforcement:** SecOps agents will enforce NetworkPolicies, Pod Security Standards (PSS), and RBAC configurations. CI/CD pipelines will include static analysis of manifests using tools like `kube-linter` and `Checkov`.

- **Principle 3: Scalability and Performance**
  - **Rule:** Applications MUST be designed for horizontal scalability. Resource requests and limits MUST be explicitly defined to ensure predictable performance and efficient cluster utilization.
  - **Enforcement:** DevOps agents will monitor resource utilization and manage Horizontal Pod Autoscalers (HPA) and event-driven scalers like KEDA.

- **Principle 4: Comprehensive Observability**
  - **Rule:** All components MUST expose metrics, logs, and traces in a standardized format.
  - **Enforcement:** LLMOps and MLOps agents will ensure applications are instrumented for Prometheus metrics and structured logging. Observability tools like Grafana, Prometheus, and Langfuse will be used for monitoring and alerting.

---

## 2. Specific Rules

### 2.1. Workload & Resource Management
- **Rule R-DO-003.1 (Resource Requests & Limits):** All Pods and Containers MUST define CPU and memory `requests` and `limits`. Requests should be based on performance testing, and limits should prevent resource starvation.
- **Rule R-DO-003.2 (Namespace Management):** All resources MUST be deployed into specific, non-default namespaces based on environment (e.g., `prod`, `staging`) or application.
- **Rule R-DO-003.3 (Labeling & Annotations):** All resources MUST have a standard set of labels (`app.kubernetes.io/name`, `app.kubernetes.io/instance`, `app.kubernetes.io/version`, `estratix.io/owner-agent`) for filtering and automation.

### 2.2. Security & Networking
- **Rule R-DO-003.4 (Network Policies):** A default-deny network policy SHOULD be applied to all namespaces. Egress and ingress traffic MUST be explicitly allowed.
- **Rule R-DO-003.5 (Pod Security Standards):** Workloads MUST adhere to the `restricted` Pod Security Standard profile unless a documented exception is approved.
- **Rule R-DO-003.6 (Secrets Management):** Sensitive information MUST NOT be stored in environment variables or plain ConfigMaps. Use a secrets management tool like HashiCorp Vault integrated via a sidecar injector.

### 2.3. Health & Resilience
- **Rule R-DO-003.7 (Health Probes):** All Pods MUST implement `livenessProbe`, `readinessProbe`, and `startupProbe` to ensure traffic is only routed to healthy, ready containers.
- **Rule R-DO-003.8 (Autoscaling):** Horizontal Pod Autoscalers (HPA) MUST be configured for all stateless services. For event-driven workloads, KEDA MUST be used.

---

## 3. Enforcement by ESTRATIX Agents

- **DevOps Agents:** Responsible for managing CI/CD pipelines, GitOps sync, and autoscaling configurations.
- **SecOps Agents:** Responsible for enforcing NetworkPolicies, PSS, and scanning for vulnerabilities.
- **GitOps Agents:** Responsible for maintaining the desired state of the cluster based on Git repositories.
- **LLMOps/MLOps Agents:** Responsible for ensuring AI/ML workloads are properly instrumented for observability and resource management.
