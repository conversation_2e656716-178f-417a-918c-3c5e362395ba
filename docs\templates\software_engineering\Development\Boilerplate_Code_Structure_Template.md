# ESTRATIX Boilerplate Code Structure Definition Template

---
**Document Control**
- **Version:** 1.0
- **Status:** Guideline / Meta-Template
- **Author(s):** ESTRATIX CTO Office, `AGENT_Architecture_Framework_Expert` (ID: AGENT_AFE001)
- **Reviewer(s):** ESTRATIX Lead Architects, ESTRATIX Development Leads, `AGENT_DevOps_Lead` (ID: AGENT_DEVOPS001)
- **Approver(s):** ESTRATIX CTO, `AGENT_CTO_Office_Reviewer` (ID: AGENT_CTOR_001)
- **Date Created:** {{YYYY-MM-DD}}
- **Last Updated Date:** {{YYYY-MM-DD}}
- **Security Classification:** ESTRATIX Internal
- **ESTRATIX Document ID:** ESTRATIX-TEMPL-SED-BCS-001
- **Distribution List:** All ESTRATIX Development Teams, All ESTRATIX Agents involved in Code Generation or Project Setup, ESTRATIX CTO Office
---

## 0. Introduction

### 0.1. Purpose of This Meta-Template
This document serves as a **meta-template** and guideline for defining and creating **specific boilerplate code structures** within the ESTRATIX ecosystem. Its purpose is to ensure that all new projects, services, agents, or components start with a standardized, robust, and ESTRATIX-compliant foundation that adheres to our architectural principles, particularly **Hexagonal Architecture** and **Domain-Driven Design (DDD)**.

### 0.2. How to Use This Document
When a new type of boilerplate is required (e.g., for a Python FastAPI microservice, a CrewAI agent, a Pydantic-AI model, a React frontend component), this document should be used as a template to create a **new, specific boilerplate definition document**. That new document will then detail the exact structure and components for that particular technology stack and purpose.

**Agent Prompt:** `AGENT_Boilerplate_Definition_Assistant_BDA001` - Assist in creating a new specific boilerplate definition document using this meta-template, tailored for [Specify Technology Stack, e.g., Python FastAPI] and [Specify Purpose, e.g., REST API Microservice].

### 0.3. Target Audience
Architects, Development Leads, Senior Developers, and ESTRATIX Agents responsible for defining, creating, or utilizing project boilerplates.

### 0.4. Core ESTRATIX Principles to Embody
All boilerplates derived using this guideline must embody:
*   **SOLID Principles**
*   **DRY (Don't Repeat Yourself)**
*   **Test-Driven Development (TDD) / Behavior-Driven Development (BDD)** readiness
*   **Clear Separation of Concerns** (Hexagonal Architecture)
*   **Security by Design**
*   **Observability Hooks** (logging, metrics, tracing)
*   **Configuration Management Best Practices**
*   **Agent-Ready Design** (clear interfaces, well-defined responsibilities)

---

## 1. Boilerplate Definition: `[Name of Specific Boilerplate, e.g., ESTRATIX FastAPI Microservice Boilerplate]`

### 1.1. Boilerplate ID
`[Unique ID for this specific boilerplate, e.g., BP_PY_FASTAPI_V1]`

### 1.2. Purpose and Scope
*Define the specific purpose of **this** boilerplate (e.g., "To provide a standardized starting point for Python-based RESTful microservices using FastAPI, adhering to Hexagonal Architecture."). Detail what's included and what's considered out of scope for this particular boilerplate.*

### 1.3. Target Technology Stack
*List the primary technologies, frameworks, and significant libraries this boilerplate will use.*
*   **Language(s):** `[e.g., Python 3.10+, JavaScript ES2022+]`
*   **Core Framework(s):** `[e.g., FastAPI, NestJS, React, CrewAI, Pydantic-AI]`
*   **Key Libraries:** `[e.g., Pydantic, SQLAlchemy, Celery, Jest]`
*   **Dependency Management:** `[e.g., Poetry, Pipenv, NPM, Yarn]`

### 1.4. Key Architectural Patterns
*Reiterate commitment to Hexagonal Architecture and DDD. Specify any other key patterns to be implemented (e.g., CQRS, Event Sourcing stubs if applicable).*

---

## 2. Standardized Directory Structure (Hexagonal Architecture Focus)
*Define the standard directory layout. The following is a recommended baseline for backend services, adaptable for other types of applications. Justify any significant deviations.*

```
[project_root]/
├── .devcontainer/         # Optional: For VS Code Dev Containers
├── .github/               # GitHub Actions workflows, issue templates
├── .vscode/               # VS Code workspace settings
├── docs/                  # Project documentation (design docs, ADRs, etc.)
├── scripts/               # Utility scripts (build, test, deploy, lint, format)
├── src/                   # Source code
│   ├── domain/            # Core business logic, entities, value objects, domain services
│   │   ├── __init__.py
│   │   ├── models/        # Pydantic models for domain entities/value objects
│   │   ├── repositories/  # Abstract repository interfaces (ports)
│   │   └── services/      # Domain services
│   ├── application/       # Application-specific logic, use cases
│   │   ├── __init__.py
│   │   ├── services/      # Application services / use cases orchestrating domain logic
│   │   ├── ports/         # Interfaces for driving adapters (e.g., API input DTOs) and driven adapters
│   │   └── dto/           # Data Transfer Objects for application layer
│   ├── infrastructure/    # Adapters, configuration, external concerns
│   │   ├── __init__.py
│   │   ├── adapters/      # Concrete implementations of ports
│   │   │   ├── api/       # Driving/Primary Adapter: REST API endpoints (e.g., FastAPI routers)
│   │   │   ├── cli/       # Driving/Primary Adapter: Command-line interface
│   │   │   ├── db/        # Driven/Secondary Adapter: Database interactions (SQLAlchemy models, repository impl.)
│   │   │   └── external/  # Driven/Secondary Adapter: External service integrations
│   │   ├── config/        # Configuration loading and management (e.g., Pydantic settings)
│   │   ├── logging/       # Logging setup and configuration
│   │   └── security/      # Security-related components (e.g., auth middleware stubs)
│   ├── main.py            # Application entry point (e.g., FastAPI app instantiation)
│   └── __init__.py
├── tests/                 # Automated tests
│   ├── __init__.py
│   ├── unit/              # Unit tests (isolated component tests)
│   │   ├── domain/
│   │   ├── application/
│   │   └── infrastructure/
│   ├── integration/       # Integration tests (component interactions, e.g., service with DB)
│   └── e2e/               # End-to-end tests (full system tests via API/UI)
├── .dockerignore
├── .env.example           # Example environment variables
├── .gitignore
├── Dockerfile             # Containerization definition
├── [requirements.txt|pyproject.toml|package.json] # Dependencies
├── README.md              # Project-specific README
└── [LICENSE]              # Project license file
```

*   **Agent Prompt:** `AGENT_Hexagonal_Architect_HA001` - Review and validate the proposed directory structure for adherence to Hexagonal Architecture principles for the `[Boilerplate ID]` boilerplate.

---

## 3. Core Components & Implementation Guidance
*Detail the setup and initial implementation for essential cross-cutting concerns.*

### 3.1. Application Entry Point (`src/main.py`)
*   `[e.g., FastAPI app setup, middleware registration, router inclusion]`

### 3.2. Configuration Management (`src/infrastructure/config/`)
*   Use Pydantic for settings management, loading from environment variables and/or config files.
*   Provide an `.env.example` file.
*   Ensure sensitive information is handled securely (e.g., via secrets manager integration stubs).

### 3.3. Logging (`src/infrastructure/logging/`)
*   Implement structured logging (e.g., JSON format).
*   Configurable log levels.
*   Integration with ESTRATIX standard observability platforms (placeholders/guidance).
*   Correlation IDs for request tracing.

### 3.4. Error Handling
*   Global exception handlers.
*   Standardized error response formats (e.g., JSON for APIs, link to `API_Design_Guidelines_Template.md`).
*   Clear distinction between business exceptions and system errors.

### 3.5. API Design (if applicable, `src/infrastructure/adapters/api/`)
*   Adherence to `API_Design_Guidelines_Template.md`.
*   Versioning strategy stub.
*   Input validation (e.g., using Pydantic in FastAPI).
*   Authentication & Authorization stubs (e.g., OAuth2/OIDC, API Keys).

### 3.6. Data Persistence (if applicable, `src/infrastructure/adapters/db/`)
*   ORM setup (e.g., SQLAlchemy) with migration tool (e.g., Alembic).
*   Abstract repository pattern implementation.
*   Example CRUD operations.
*   Connection pooling and management.

### 3.7. Security Components (`src/infrastructure/security/`)
*   Basic security headers middleware.
*   CSRF protection stubs (for web apps).
*   Rate limiting stubs.
*   Guidance on secrets management.
*   **Agent Prompt:** `AGENT_Security_Best_Practices_SBP001` - Provide initial security best practice recommendations for the `[Boilerplate ID]` boilerplate's technology stack.

---

## 4. Testing Strategy (`tests/`)

### 4.1. Frameworks and Tools
*   `[e.g., Pytest for Python, Jest/Vitest for JavaScript]`
*   Mocking libraries `[e.g., unittest.mock, pytest-mock, Jest mocks]`
*   Coverage tools `[e.g., coverage.py, Istanbul]`

### 4.2. Unit Tests
*   Focus on isolating components in `domain`, `application`, and `infrastructure` layers.
*   Aim for high code coverage.

### 4.3. Integration Tests
*   Test interactions between components (e.g., application service with repository implementation).
*   May require test databases or mocked external services.

### 4.4. End-to-End (E2E) Tests (if applicable)
*   Test the full application flow via its primary interface (e.g., API endpoints, UI).

### 4.5. Test-Driven Development (TDD)
*   Encourage TDD practices. Boilerplate should make it easy to write tests before code.
*   **Agent Prompt:** `AGENT_Test_Generator_TG001` - Generate skeleton test files and example test cases for the core components defined in the `[Boilerplate ID]` boilerplate.

---

## 5. Dockerization & Containerization (`Dockerfile`)

*   Provide a production-ready `Dockerfile`.
*   Use multi-stage builds to minimize image size.
*   Non-root user execution.
*   Secure base images.
*   Configurable via environment variables.

---

## 6. CI/CD Integration

*   Provide example GitHub Actions workflows (or equivalent for other CI/CD systems) in `.github/workflows/` for:
    *   Linting and formatting checks.
    *   Running unit and integration tests.
    *   Building Docker images.
    *   Security scanning (e.g., Snyk, Trivy).
    *   (Optional) Deployment stubs to ESTRATIX standard environments (e.g., Kubernetes).
*   **Agent Prompt:** `AGENT_CICD_Pipeline_Designer_CPD001` - Design a basic CI/CD pipeline structure for the `[Boilerplate ID]` boilerplate, incorporating ESTRATIX standard tools and practices.

---

## 7. Documentation

### 7.1. `README.md`
*   The boilerplate must include a comprehensive `README.md` template covering:
    *   Project Overview (to be filled by the user).
    *   Prerequisites.
    *   Getting Started (setup, installation).
    *   Running the Application.
    *   Running Tests.
    *   Directory Structure Overview.
    *   Key Configuration Options.
    *   Deployment Instructions (basic).
    *   How to Contribute (if applicable).

### 7.2. Code Documentation
*   Enforce use of docstrings (e.g., Google/NumPy style for Python, JSDoc for JavaScript).
*   Comments for complex logic.

---

## 8. How to Use This Boilerplate (For End Users)
*Provide clear, step-by-step instructions for a developer to start a new project using **this specific boilerplate**.*
1.  `[e.g., Clone the boilerplate repository / Use a CLI tool to generate from template]`
2.  `[e.g., Install dependencies: poetry install / npm install]`
3.  `[e.g., Set up environment variables: cp .env.example .env && nano .env]`
4.  `[e.g., Run database migrations: alembic upgrade head]`
5.  `[e.g., Start the development server: uvicorn src.main:app --reload / npm run dev]`
*   **Agent Prompt:** `AGENT_Boilerplate_Generator_BG001` - Generate a new project instance named `[New Project Name]` using the `[Boilerplate ID]` boilerplate, and initialize a Git repository.

---

## 9. Customization and Evolution
*Provide guidance on how developers can customize and extend the boilerplate for their specific project needs while maintaining ESTRATIX alignment. Also, outline the process for updating and versioning this boilerplate itself.*

---

**ESTRATIX Controlled Deliverable**
*This document defines a standard for creating ESTRATIX boilerplates. It is subject to ESTRATIX document management and version control policies. Unauthorized distribution or modification is prohibited.*
*Ensure this definition and the resulting boilerplate code are maintained in the ESTRATIX central knowledge repository (e.g., Git, Milvus for metadata) and linked appropriately.*
*Consult the ESTRATIX CTO office for any queries regarding boilerplate standards and evolution.*
