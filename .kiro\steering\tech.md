# ESTRATIX Technology Stack

## Build System & Package Management
- **Primary**: `uv` for virtual environments and dependency management
- **Build Backend**: setuptools
- **Python Version**: 3.10+ (supports 3.10, 3.11, 3.12)
- **Package Name**: estratix_v3

## Core Framework Stack
- **Web Framework**: FastAPI with uvicorn[standard]
- **Data Validation**: Pydantic v2.5+
- **Async Support**: Full async/await with aiohttp, aiofiles, aioredis

## AI & Agent Frameworks
- **Primary Agent Framework**: CrewAI (v0.36.0+)
- **LLM Integration**: 
  - OpenAI API
  - Google Generative AI
  - LangChain ecosystem (core, openai, community)
  - Pydantic AI
  - LangGraph for complex workflows

## Database & Storage
- **Vector Database**: Milvus (production), ChromaDB, Faiss, Pinecone, Weaviate
- **Document Database**: MongoDB (motor for async)
- **Graph Database**: Neo4j
- **Caching**: Redis with hiredis
- **Embeddings**: sentence-transformers, fastembed

## Development Tools
- **Linting**: <PERSON>uff (replaces flake8, black, isort)
- **Type Checking**: MyPy
- **Testing**: pytest with pytest-asyncio
- **Pre-commit**: Enabled with hooks

## Common Commands

### Environment Setup
```bash
# Create and activate virtual environment
uv venv .venv
source .venv/bin/activate  # Linux/macOS
.\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
uv pip install -e ".[dev]"
```

### Development
```bash
# Run tests
pytest

# Type checking
mypy src/

# Linting and formatting
ruff check src/
ruff format src/

# Run development server
uvicorn src.main:app --reload
```

### Production
```bash
# Install production dependencies only
uv pip install -e .

# Production server (when implemented)
uvicorn src.main:app --host 0.0.0.0 --port 8000
```

## Architecture Patterns
- **Hexagonal Architecture**: Ports & Adapters pattern
- **Domain-Driven Design**: Core business logic in domain layer
- **Async-First**: All I/O operations use async/await
- **Agent Orchestration**: Multi-agent coordination patterns
- **Matrix-Driven**: Configuration and discovery through markdown matrices