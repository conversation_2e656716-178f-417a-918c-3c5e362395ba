# Sorteo Estelar - Web3 Gaming Ecosystem
# Makefile for development, testing, and deployment automation

.PHONY: help install dev build test lint format docker deploy clean

# Default target
.DEFAULT_GOAL := help

# Variables
APP_NAME := sorteo-estelar
VERSION := $(shell node -p "require('./package.json').version")
DOCKER_IMAGE := $(APP_NAME):$(VERSION)
VPS_HOST := admin@**************
VPS_PORT := 2222
DEPLOY_PATH := /opt/apps/$(APP_NAME)

# Colors for output
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

## Help
help: ## Show this help message
	@echo "$(BLUE)Sorteo Estelar - Available Commands$(NC)"
	@echo ""
	@awk 'BEGIN {FS = ":.*##"; printf "Usage: make $(BLUE)<target>$(NC)\n\n"} /^[a-zA-Z_-]+:.*?##/ { printf "  $(BLUE)%-15s$(NC) %s\n", $$1, $$2 } /^##@/ { printf "\n$(YELLOW)%s$(NC)\n", substr($$0, 5) } ' $(MAKEFILE_LIST)

##@ Development
install: ## Install dependencies
	@echo "$(BLUE)Installing dependencies...$(NC)"
	npm install
	@echo "$(GREEN)Dependencies installed successfully$(NC)"

dev: ## Start development server
	@echo "$(BLUE)Starting development server...$(NC)"
	npm run dev

build: ## Build for production
	@echo "$(BLUE)Building for production...$(NC)"
	npm run build
	@echo "$(GREEN)Build completed successfully$(NC)"

preview: ## Preview production build
	@echo "$(BLUE)Starting preview server...$(NC)"
	npm run preview

clean: ## Clean build artifacts and dependencies
	@echo "$(BLUE)Cleaning build artifacts...$(NC)"
	rm -rf dist node_modules .vite .turbo
	npm cache clean --force
	@echo "$(GREEN)Clean completed$(NC)"

##@ Code Quality
lint: ## Run linter
	@echo "$(BLUE)Running linter...$(NC)"
	npm run lint

lint-fix: ## Fix linting issues
	@echo "$(BLUE)Fixing linting issues...$(NC)"
	npm run lint:fix
	@echo "$(GREEN)Linting issues fixed$(NC)"

format: ## Format code
	@echo "$(BLUE)Formatting code...$(NC)"
	npm run format
	@echo "$(GREEN)Code formatted$(NC)"

format-check: ## Check code formatting
	@echo "$(BLUE)Checking code formatting...$(NC)"
	npm run format:check

type-check: ## Run TypeScript type checking
	@echo "$(BLUE)Running TypeScript type checking...$(NC)"
	npm run type-check
	@echo "$(GREEN)Type checking completed$(NC)"

quality: lint type-check format-check ## Run all code quality checks
	@echo "$(GREEN)All quality checks passed$(NC)"

##@ Testing
test: ## Run unit tests
	@echo "$(BLUE)Running unit tests...$(NC)"
	npm run test

test-ui: ## Run tests with UI
	@echo "$(BLUE)Running tests with UI...$(NC)"
	npm run test:ui

test-coverage: ## Run tests with coverage
	@echo "$(BLUE)Running tests with coverage...$(NC)"
	npm run test:coverage

test-e2e: ## Run end-to-end tests
	@echo "$(BLUE)Running end-to-end tests...$(NC)"
	npm run test:e2e

test-all: test test-e2e ## Run all tests
	@echo "$(GREEN)All tests completed$(NC)"

##@ Docker
docker-build: ## Build Docker image
	@echo "$(BLUE)Building Docker image...$(NC)"
	docker build -t $(DOCKER_IMAGE) .
	@echo "$(GREEN)Docker image built: $(DOCKER_IMAGE)$(NC)"

docker-run: ## Run Docker container
	@echo "$(BLUE)Running Docker container...$(NC)"
	docker run -p 3000:80 --name $(APP_NAME) $(DOCKER_IMAGE)

docker-stop: ## Stop Docker container
	@echo "$(BLUE)Stopping Docker container...$(NC)"
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

docker-compose-up: ## Start services with Docker Compose
	@echo "$(BLUE)Starting services with Docker Compose...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)Services started$(NC)"

docker-compose-down: ## Stop services with Docker Compose
	@echo "$(BLUE)Stopping services with Docker Compose...$(NC)"
	docker-compose down
	@echo "$(GREEN)Services stopped$(NC)"

docker-compose-logs: ## View Docker Compose logs
	@echo "$(BLUE)Viewing Docker Compose logs...$(NC)"
	docker-compose logs -f

docker-clean: ## Clean Docker images and containers
	@echo "$(BLUE)Cleaning Docker images and containers...$(NC)"
	docker system prune -f
	docker image prune -f
	@echo "$(GREEN)Docker cleanup completed$(NC)"

##@ Database
db-setup: ## Setup database
	@echo "$(BLUE)Setting up database...$(NC)"
	docker-compose up -d postgres redis
	sleep 10
	npm run db:migrate
	npm run db:seed
	@echo "$(GREEN)Database setup completed$(NC)"

db-migrate: ## Run database migrations
	@echo "$(BLUE)Running database migrations...$(NC)"
	npm run db:migrate
	@echo "$(GREEN)Database migrations completed$(NC)"

db-seed: ## Seed database with test data
	@echo "$(BLUE)Seeding database...$(NC)"
	npm run db:seed
	@echo "$(GREEN)Database seeded$(NC)"

db-reset: ## Reset database
	@echo "$(BLUE)Resetting database...$(NC)"
	npm run db:reset
	@echo "$(GREEN)Database reset completed$(NC)"

##@ Deployment
deploy-check: ## Check deployment prerequisites
	@echo "$(BLUE)Checking deployment prerequisites...$(NC)"
	@command -v ssh >/dev/null 2>&1 || { echo "$(RED)SSH is required but not installed$(NC)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)Docker is required but not installed$(NC)"; exit 1; }
	@ssh -p $(VPS_PORT) -o ConnectTimeout=10 -o BatchMode=yes $(VPS_HOST) exit 2>/dev/null || { echo "$(RED)SSH connection to VPS failed$(NC)"; exit 1; }
	@echo "$(GREEN)Deployment prerequisites check passed$(NC)"

deploy-staging: deploy-check ## Deploy to staging environment
	@echo "$(BLUE)Deploying to staging...$(NC)"
	./deploy.sh deploy staging
	@echo "$(GREEN)Staging deployment completed$(NC)"

deploy-production: deploy-check ## Deploy to production environment
	@echo "$(BLUE)Deploying to production...$(NC)"
	./deploy.sh deploy production
	@echo "$(GREEN)Production deployment completed$(NC)"

deploy: deploy-production ## Deploy to production (alias)

deploy-health: ## Check deployment health
	@echo "$(BLUE)Checking deployment health...$(NC)"
	./deploy.sh health

deploy-logs: ## View deployment logs
	@echo "$(BLUE)Viewing deployment logs...$(NC)"
	./deploy.sh logs

deploy-restart: ## Restart deployed services
	@echo "$(BLUE)Restarting deployed services...$(NC)"
	./deploy.sh restart
	@echo "$(GREEN)Services restarted$(NC)"

deploy-rollback: ## Rollback deployment
	@echo "$(BLUE)Rolling back deployment...$(NC)"
	ssh -p $(VPS_PORT) $(VPS_HOST) "cd $(DEPLOY_PATH) && git checkout HEAD~1 && docker-compose up -d --build"
	@echo "$(GREEN)Rollback completed$(NC)"

##@ Monitoring
monitor-status: ## Check application status
	@echo "$(BLUE)Checking application status...$(NC)"
	curl -f http://localhost:3000/health || echo "$(RED)Application not responding$(NC)"
	curl -f http://localhost:3001/api/health || echo "$(RED)API not responding$(NC)"

monitor-logs: ## View application logs
	@echo "$(BLUE)Viewing application logs...$(NC)"
	docker-compose logs -f --tail=100

monitor-metrics: ## View application metrics
	@echo "$(BLUE)Opening metrics dashboard...$(NC)"
	open http://localhost:3002 # Grafana

monitor-performance: ## Run performance tests
	@echo "$(BLUE)Running performance tests...$(NC)"
	npm run test:performance

##@ Security
security-audit: ## Run security audit
	@echo "$(BLUE)Running security audit...$(NC)"
	npm audit
	npm audit fix
	@echo "$(GREEN)Security audit completed$(NC)"

security-scan: ## Scan for vulnerabilities
	@echo "$(BLUE)Scanning for vulnerabilities...$(NC)"
	docker run --rm -v $(PWD):/app securecodewarrior/docker-security-scanner /app

security-check: security-audit security-scan ## Run all security checks
	@echo "$(GREEN)Security checks completed$(NC)"

##@ Git Operations
git-setup: ## Setup Git hooks and configuration
	@echo "$(BLUE)Setting up Git hooks...$(NC)"
	npm run prepare
	git config core.hooksPath .husky
	@echo "$(GREEN)Git setup completed$(NC)"

git-status: ## Show Git status
	@echo "$(BLUE)Git Status:$(NC)"
	git status --short --branch

git-clean: ## Clean Git repository
	@echo "$(BLUE)Cleaning Git repository...$(NC)"
	git clean -fd
	git gc --prune=now
	@echo "$(GREEN)Git cleanup completed$(NC)"

##@ Utilities
info: ## Show project information
	@echo "$(BLUE)Project Information:$(NC)"
	@echo "Name: $(APP_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Docker Image: $(DOCKER_IMAGE)"
	@echo "VPS Host: $(VPS_HOST):$(VPS_PORT)"
	@echo "Deploy Path: $(DEPLOY_PATH)"

env-check: ## Check environment variables
	@echo "$(BLUE)Checking environment variables...$(NC)"
	@test -f .env || { echo "$(RED).env file not found$(NC)"; exit 1; }
	@echo "$(GREEN)Environment variables check passed$(NC)"

ports-check: ## Check if required ports are available
	@echo "$(BLUE)Checking required ports...$(NC)"
	@lsof -i :3000 >/dev/null 2>&1 && echo "$(YELLOW)Port 3000 is in use$(NC)" || echo "$(GREEN)Port 3000 is available$(NC)"
	@lsof -i :3001 >/dev/null 2>&1 && echo "$(YELLOW)Port 3001 is in use$(NC)" || echo "$(GREEN)Port 3001 is available$(NC)"
	@lsof -i :5432 >/dev/null 2>&1 && echo "$(YELLOW)Port 5432 is in use$(NC)" || echo "$(GREEN)Port 5432 is available$(NC)"
	@lsof -i :6379 >/dev/null 2>&1 && echo "$(YELLOW)Port 6379 is in use$(NC)" || echo "$(GREEN)Port 6379 is available$(NC)"

backup: ## Create project backup
	@echo "$(BLUE)Creating project backup...$(NC)"
	tar -czf backup-$(shell date +%Y%m%d-%H%M%S).tar.gz --exclude=node_modules --exclude=dist --exclude=.git .
	@echo "$(GREEN)Backup created$(NC)"

##@ Complete Workflows
setup: install env-check db-setup git-setup ## Complete project setup
	@echo "$(GREEN)Project setup completed successfully!$(NC)"
	@echo "$(BLUE)Run 'make dev' to start development$(NC)"

ci: quality test-all security-audit ## Run CI pipeline
	@echo "$(GREEN)CI pipeline completed successfully$(NC)"

cd: build docker-build deploy ## Run CD pipeline
	@echo "$(GREEN)CD pipeline completed successfully$(NC)"

full-deploy: ci cd ## Run full CI/CD pipeline
	@echo "$(GREEN)Full deployment pipeline completed successfully$(NC)"

dev-reset: clean install db-reset dev ## Reset development environment
	@echo "$(GREEN)Development environment reset completed$(NC)"

##@ Documentation
docs-serve: ## Serve documentation locally
	@echo "$(BLUE)Serving documentation...$(NC)"
	npx docsify serve docs

docs-build: ## Build documentation
	@echo "$(BLUE)Building documentation...$(NC)"
	npx typedoc --out docs/api src
	@echo "$(GREEN)Documentation built$(NC)"

# Include local Makefile if it exists
-include Makefile.local