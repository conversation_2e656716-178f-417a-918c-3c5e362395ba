{"clientName": "Zurux", "brand": {"brandName": "Zurux", "logoUrl": "https://cdn.example.com/logos/zurux.png", "primaryColor": "#0A5F8F", "secondaryColor": "#F2C94C"}, "projectObjective": "Migrate legacy WooCommerce site to a modern, scalable, headless ecommerce platform with multi-framework frontend prototypes and advanced content generation/SEO capabilities.", "requiredFeatures": ["category navigation", "product pages", "dynamic QR/blockchain payments", "manual payment confirmation", "content/blog integration", "SEO optimization"], "preferredBackend": "medusajs", "preferredFrontendFrameworks": ["react", "vue", "svelte", "alpine"], "paymentMethods": ["qr_blockchain", "manual_confirmation"], "contentGeneration": true, "seoBoost": true, "customizationVariables": {"currency": "USD", "defaultLanguage": "en", "timezone": "America/New_York"}, "notes": "Client wants to test multiple frontend UIs and integrate with social/blog content workflows."}