import { apiClient, ApiResponse } from './api.client';
import { BaseService } from './base.service';
import { buildSearchParams, applyFilters, paginate, sortItems } from './service.utils';

// Property types
export interface Property {
  id: string;
  title: string;
  description: string;
  type: 'apartment' | 'house' | 'condo' | 'villa' | 'penthouse' | 'loft';
  status: 'available' | 'occupied' | 'maintenance' | 'sold';
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  price: number;
  currency: string;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  amenities: string[];
  images: string[];
  virtualTourUrl?: string;
  ownerId: string;
  managerId?: string;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  isFeatured: boolean;
  availableFrom?: Date;
  availableTo?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface PropertyFilters {
  type?: Property['type'][];
  status?: Property['status'][];
  city?: string;
  state?: string;
  priceMin?: number;
  priceMax?: number;
  bedrooms?: number;
  bathrooms?: number;
  amenities?: string[];
  isVerified?: boolean;
  isFeatured?: boolean;
  availableFrom?: Date;
  availableTo?: Date;
}

export interface PropertySearchParams extends PropertyFilters {
  query?: string;
  sortBy?: 'price' | 'rating' | 'created' | 'updated';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface PropertyStats {
  total: number;
  available: number;
  occupied: number;
  averagePrice: number;
  averageRating: number;
  totalRevenue: number;
}

export interface PropertyCreateRequest {
  title: string;
  description: string;
  type: Property['type'];
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  price: number;
  currency: string;
  bedrooms: number;
  bathrooms: number;
  squareFeet: number;
  amenities: string[];
  images?: string[];
  virtualTourUrl?: string;
  availableFrom?: Date;
  availableTo?: Date;
}

// Mock data for development
const mockProperties: Property[] = [
  {
    id: 'prop-1',
    title: 'Luxury Penthouse in Back Bay',
    description: 'Stunning penthouse with panoramic city views, modern amenities, and premium finishes.',
    type: 'penthouse',
    status: 'available',
    address: '123 Newbury Street',
    city: 'Boston',
    state: 'MA',
    zipCode: '02116',
    country: 'USA',
    coordinates: { lat: 42.3505, lng: -71.0759 },
    price: 8500,
    currency: 'USD',
    bedrooms: 3,
    bathrooms: 2,
    squareFeet: 2200,
    amenities: ['Pool', 'Gym', 'Concierge', 'Parking', 'Balcony'],
    images: [
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20boston%20back%20bay%20modern%20interior&image_size=landscape_16_9'
    ],
    ownerId: 'owner-1',
    rating: 4.8,
    reviewCount: 24,
    isVerified: true,
    isFeatured: true,
    availableFrom: new Date('2024-02-01'),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-10')
  },
  {
    id: 'prop-2',
    title: 'Historic Brownstone in Beacon Hill',
    description: 'Charming historic brownstone with original details and modern updates.',
    type: 'house',
    status: 'available',
    address: '456 Beacon Street',
    city: 'Boston',
    state: 'MA',
    zipCode: '02108',
    country: 'USA',
    coordinates: { lat: 42.3584, lng: -71.0598 },
    price: 6500,
    currency: 'USD',
    bedrooms: 4,
    bathrooms: 3,
    squareFeet: 2800,
    amenities: ['Fireplace', 'Garden', 'Parking', 'Historic Details'],
    images: [
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=historic%20brownstone%20beacon%20hill%20boston%20luxury%20exterior&image_size=landscape_16_9'
    ],
    ownerId: 'owner-2',
    rating: 4.6,
    reviewCount: 18,
    isVerified: true,
    isFeatured: false,
    availableFrom: new Date('2024-01-15'),
    createdAt: new Date('2023-12-15'),
    updatedAt: new Date('2024-01-05')
  },
  {
    id: 'prop-3',
    title: 'Modern Waterfront Condo',
    description: 'Contemporary condo with harbor views and luxury amenities.',
    type: 'condo',
    status: 'occupied',
    address: '789 Harbor Drive',
    city: 'Cambridge',
    state: 'MA',
    zipCode: '02141',
    country: 'USA',
    coordinates: { lat: 42.3656, lng: -71.0275 },
    price: 4200,
    currency: 'USD',
    bedrooms: 2,
    bathrooms: 2,
    squareFeet: 1400,
    amenities: ['Harbor View', 'Gym', 'Pool', 'Concierge'],
    images: [
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=modern%20waterfront%20condo%20cambridge%20harbor%20view&image_size=landscape_16_9'
    ],
    ownerId: 'owner-3',
    rating: 4.7,
    reviewCount: 31,
    isVerified: true,
    isFeatured: true,
    createdAt: new Date('2023-11-20'),
    updatedAt: new Date('2024-01-08')
  }
];

class PropertyService extends BaseService {
  async getProperties(params?: PropertySearchParams): Promise<{ properties: Property[]; total: number }> {
    return this.handleApiCall(
      async () => {
        const searchParams = buildSearchParams({
          query: params?.query,
          type: params?.type?.join(','),
          status: params?.status?.join(','),
          city: params?.city,
          state: params?.state,
          priceMin: params?.priceMin?.toString(),
          priceMax: params?.priceMax?.toString(),
          bedrooms: params?.bedrooms?.toString(),
          bathrooms: params?.bathrooms?.toString(),
          isVerified: params?.isVerified?.toString(),
          isFeatured: params?.isFeatured?.toString(),
          sortBy: params?.sortBy,
          sortOrder: params?.sortOrder,
          page: params?.page?.toString(),
          limit: params?.limit?.toString()
        });
        const response = await apiClient.get<{ properties: Property[]; total: number }>(
          `/properties?${searchParams.toString()}`
        );
        return response.data;
      },
      () => {
        let filteredProperties = [...mockProperties];
        
        // Apply filters
         if (params?.type) {
           filteredProperties = filteredProperties.filter(p => params.type!.includes(p.type));
         }
         
         if (params?.status) {
           filteredProperties = filteredProperties.filter(p => params.status!.includes(p.status));
         }
         
         if (params?.city) {
           filteredProperties = filteredProperties.filter(p => 
             p.city.toLowerCase().includes(params.city!.toLowerCase())
           );
         }
         
         if (params?.priceMin) {
           filteredProperties = filteredProperties.filter(p => p.price >= params.priceMin!);
         }
         
         if (params?.priceMax) {
           filteredProperties = filteredProperties.filter(p => p.price <= params.priceMax!);
         }
         
         if (params?.bedrooms) {
           filteredProperties = filteredProperties.filter(p => p.bedrooms >= params.bedrooms!);
         }
         
         if (params?.bathrooms) {
           filteredProperties = filteredProperties.filter(p => p.bathrooms >= params.bathrooms!);
         }
         
         if (params?.isVerified !== undefined) {
           filteredProperties = filteredProperties.filter(p => p.isVerified === params.isVerified);
         }
         
         if (params?.isFeatured !== undefined) {
           filteredProperties = filteredProperties.filter(p => p.isFeatured === params.isFeatured);
         }
         
         if (params?.query) {
           const query = params.query.toLowerCase();
           filteredProperties = filteredProperties.filter(p => 
             p.title.toLowerCase().includes(query) ||
             p.description.toLowerCase().includes(query) ||
             p.address.toLowerCase().includes(query)
           );
         }
        
        // Apply sorting
        filteredProperties = sortItems(filteredProperties, params?.sortBy, params?.sortOrder);
        
        // Apply pagination
        const paginatedResult = paginate(filteredProperties, params?.page, params?.limit);
        
        return {
          properties: paginatedResult.items,
          total: paginatedResult.total
        };
      },
      500
    );
  }

  async getPropertyById(id: string): Promise<Property> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Property>(`/properties/${id}`);
        return response.data;
      },
      async () => {
        const property = mockProperties.find(p => p.id === id);
        if (!property) {
          throw new Error('Property not found');
        }
        return property;
      },
      300
    );
  }

  async createProperty(data: PropertyCreateRequest): Promise<Property> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<Property>('/properties', data);
        return response.data;
      },
      async () => {
        const newProperty: Property = {
          id: `prop-${Date.now()}`,
          ...data,
          status: 'available',
          coordinates: { lat: 42.3601, lng: -71.0589 }, // Mock coordinates
          images: data.images || [],
          ownerId: 'current-user', // Would come from auth context
          rating: 0,
          reviewCount: 0,
          isVerified: false,
          isFeatured: false,
          createdAt: new Date(),
          updatedAt: new Date()
        };
        
        mockProperties.push(newProperty);
        return newProperty;
      },
      1000
    );
  }

  async updateProperty(id: string, updates: Partial<PropertyCreateRequest>): Promise<Property> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.patch<Property>(`/properties/${id}`, updates);
        return response.data;
      },
      async () => {
        const propertyIndex = mockProperties.findIndex(p => p.id === id);
        if (propertyIndex === -1) {
          throw new Error('Property not found');
        }
        
        mockProperties[propertyIndex] = {
          ...mockProperties[propertyIndex],
          ...updates,
          updatedAt: new Date()
        };
        
        return mockProperties[propertyIndex];
      },
      800
    );
  }

  async deleteProperty(id: string): Promise<void> {
    return this.handleApiCall(
      async () => {
        await apiClient.delete(`/properties/${id}`);
      },
      async () => {
        const propertyIndex = mockProperties.findIndex(p => p.id === id);
        if (propertyIndex === -1) {
          throw new Error('Property not found');
        }
        
        mockProperties.splice(propertyIndex, 1);
      },
      500
    );
  }

  async getPropertyStats(filters?: PropertyFilters): Promise<PropertyStats> {
    return this.handleApiCall(
      async () => {
        const params = buildSearchParams({
          type: filters?.type?.join(','),
          status: filters?.status?.join(','),
          city: filters?.city,
          state: filters?.state
        });
        const response = await apiClient.get<PropertyStats>(`/properties/stats?${params.toString()}`);
        return response.data;
      },
      async () => {
        const { properties } = await this.getProperties(filters);
        
        return {
          total: properties.length,
          available: properties.filter(p => p.status === 'available').length,
          occupied: properties.filter(p => p.status === 'occupied').length,
          averagePrice: properties.reduce((sum, p) => sum + p.price, 0) / properties.length || 0,
          averageRating: properties.reduce((sum, p) => sum + p.rating, 0) / properties.length || 0,
          totalRevenue: properties.filter(p => p.status === 'occupied').reduce((sum, p) => sum + p.price, 0)
        };
      },
      300
    );
  }

  async uploadPropertyImages(propertyId: string, files: File[]): Promise<string[]> {
    return this.handleApiCall(
      async () => {
        const uploadPromises = files.map(file => 
          apiClient.uploadFile<{ url: string }>(`/properties/${propertyId}/images`, file)
        );
        
        const responses = await Promise.all(uploadPromises);
        return responses.map(response => response.data.url);
      },
      () => files.map((_, index) => 
        `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20property%20interior%20${index}&image_size=landscape_16_9`
      ),
      2000
    );
  }

  async getFeaturedProperties(limit: number = 6): Promise<Property[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Property[]>(`/properties/featured?limit=${limit}`);
        return response.data;
      },
      () => mockProperties
        .filter(p => p.isFeatured && p.status === 'available')
        .slice(0, limit),
      300
    );
  }

  async searchNearby(lat: number, lng: number, radius: number = 10): Promise<Property[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<Property[]>(
          `/properties/nearby?lat=${lat}&lng=${lng}&radius=${radius}`
        );
        return response.data;
      },
      () => mockProperties.filter(p => p.status === 'available'),
      500
    );
  }
}

export const propertyService = new PropertyService();
export default propertyService;