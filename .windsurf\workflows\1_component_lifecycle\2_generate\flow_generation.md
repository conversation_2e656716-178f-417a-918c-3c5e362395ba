---
description: Guides the generation of a framework-specific implementation for a pre-defined ESTRATIX Flow, producing runnable code in `src/frameworks/[framework]/flows/[Flow_ID]_[DescriptiveName_PascalCase]/`.
---

# ESTRATIX Workflow: Generate Flow Implementation

**Objective:** To generate a runnable, framework-specific implementation for a pre-defined ESTRATIX Flow, placing it in the correct directory structure and updating its registration in the `flow_matrix.md`.

**Agent Persona:** `AGENT_Developer_Expert`

## Prerequisites

- A `Flow_ID` for an existing, defined flow.
- The target `Agentic_Framework` (e.g., `crewAI`, `pydantic-ai`, `google-adk`).

## Workflow Steps

1. **Identify Target Flow & Framework**
   - **Action**: Obtain the `Flow_ID` and target `Agentic_Framework` from the user.
   - **Output**: `Flow_ID`, `Agentic_Framework`.

2. **Retrieve Flow Details from Matrix**
   - **Action**: Read `docs/matrices/flow_matrix.md` to find the target flow's details.
   - **Tool**: `view_file_outline`
   - **Example**: `<!-- view_file_outline('docs/matrices/flow_matrix.md') -->`
   - **Output**: The full, exact line content for the target flow, including `Flow_Name`, `Owner_Office_Code`, and the path to its definition file.

3. **Analyze Flow Definition to Identify Processes**
   - **Action**: Read the flow's definition file (e.g., `docs/flows/.../[Flow_ID]..._Definition.md`) to identify the list of `Orchestrated_Process_IDs`.
   - **Tool**: `view_file_outline`
   - **Example**: `<!-- view_file_outline('docs/flows/[Owner_Office_Code]/[Flow_ID]_[Flow_Name_PascalCase]/[Flow_ID]_[Flow_Name_PascalCase]_Definition.md') -->`
   - **Output**: A list of `Process_ID`s that this flow must orchestrate.

4. **Create Implementation Directory**
   - **Action**: Create the directory for the new framework-specific flow implementation.
   - **Tool**: `run_command`
   - **Example**: `<!-- run_command('mkdir -p src/frameworks/[Agentic_Framework]/flows/[Flow_ID]_[Flow_Name_PascalCase]/tests') -->`

5. **Scaffold Flow Implementation with Process Orchestration**
   - **Action**: Create the `flow.py` file with boilerplate code that imports and orchestrates the required processes.
   - **Tool**: `write_to_file`
   - **Guidance**: The generated code must dynamically import the process classes from their canonical locations (e.g., `src.frameworks.[Agentic_Framework].processes.[Process_ID]...`).
   - **Example (`crewAI`)**:

     ```python
     # src/frameworks/crewAI/flows/F001_ExampleFlow/flow.py

     # Import necessary Process classes from their canonical locations
     from src.frameworks.crewAI.processes.P001_ProcessOne.process import ProcessOne
     from src.frameworks.crewAI.processes.P002_ProcessTwo.process import ProcessTwo
     # ... import other processes identified in Step 3

     class ExampleFlow:
         """
         Orchestrates the sequence of processes for the Example Flow.
         """
         def run(self):
             """
             Executes the full flow by running its constituent processes in order.
             """
             print("--- Starting ExampleFlow ---")

             # Instantiate and run the first process
             process_one = ProcessOne()
             result_one = process_one.run()
             print("--- ProcessOne Completed ---")

             # Instantiate and run the second process
             process_two = ProcessTwo()
             result_two = process_two.run()
             print("--- ProcessTwo Completed ---")

             print("--- ExampleFlow Finished ---")
             return "Flow completed successfully."

     if __name__ == "__main__":
         flow = ExampleFlow()
         flow.run()
     ```

6. **Scaffold Tests and Documentation**
   - **Action**: Create boilerplate files for tests (`test_flow.py`) and documentation (`README.md`).
   - **Tool**: `write_to_file`

7. **Update the Flow Matrix**
   - **Action**: Update the `flow_matrix.md` to link to the new implementation.
   - **Tool**: `replace_file_content`
   - **Guidance**: Use the full line content gathered in Step 2 as the `TargetContent`.
   - **Example**:

     ```markdown
     <!-- replace_file_content(...) -->
     ```

8. **Confirmation**
   - **Action**: Confirm to the user that the flow's implementation skeleton has been successfully generated.

## Guidance for Use

- This workflow is designed to be executed by a specialized "Builder" agent with expertise in the target framework.
- **Hexagonal & Process-Centric Architecture**: The agent must adhere to a strict separation of concerns:
  - The **Flow** (`flow.py`) is the top-level orchestrator. Its sole responsibility is to instantiate and run one or more ESTRATIX Processes in the correct sequence.
  - The **Process** is where the actual work is defined. The Process component sources the necessary Agents, defines the Tasks for those agents, and equips them with the required Tools.
  - This pattern (`Flow -> Process -> [Agents, Tasks, Tools]`) is fundamental to ESTRATIX. It ensures that logic is modular, reusable, and easy to trace.
