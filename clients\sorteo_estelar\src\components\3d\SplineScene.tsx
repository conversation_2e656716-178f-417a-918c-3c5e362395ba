import React, { Suspense } from 'react';

// Spline Scene URLs - These would be actual Spline scene URLs in production
const SPLINE_SCENES = {
  HERO_STARFIELD: 'https://prod.spline.design/6Wq8RnbwWGhgI3Xs/scene.splinecode',
  PRODUCT_SHOWCASE: 'https://prod.spline.design/llK8rXlPWgHgI2Ys/scene.splinecode',
  NFT_GALLERY: 'https://prod.spline.design/8Kq7RnbwWGhgI4Zs/scene.splinecode',
  DEFI_HUB: 'https://prod.spline.design/9Lq6RnbwWGhgI5As/scene.splinecode'
};

interface SplineSceneProps {
  scene: keyof typeof SPLINE_SCENES;
  className?: string;
  fallback?: React.ReactNode;
}

// Fallback component for when Spline is loading or unavailable
const SplineFallback: React.FC<{ className?: string }> = ({ className }) => (
  <div className={`bg-gradient-to-br from-purple-900/50 to-blue-900/50 rounded-lg flex items-center justify-center ${className || ''}`}>
    <div className="text-center text-white/60">
      <div className="animate-spin w-8 h-8 border-2 border-white/30 border-t-white/60 rounded-full mx-auto mb-4"></div>
      <p className="text-sm">Loading 3D Scene...</p>
    </div>
  </div>
);

// Mock Spline component since we don't have @splinetool/react-spline installed
const MockSpline: React.FC<{ scene: string; className?: string }> = ({ scene, className }) => {
  return (
    <div className={`bg-gradient-to-br from-purple-900/30 to-blue-900/30 rounded-lg flex items-center justify-center ${className || ''}`}>
      <div className="text-center text-white/80">
        <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full mx-auto mb-4 animate-pulse"></div>
        <p className="text-sm font-medium">3D Scene</p>
        <p className="text-xs text-white/60 mt-1">Spline Integration Ready</p>
      </div>
    </div>
  );
};

export const SplineScene: React.FC<SplineSceneProps> = ({ 
  scene, 
  className, 
  fallback 
}) => {
  const sceneUrl = SPLINE_SCENES[scene];
  
  return (
    <Suspense fallback={fallback || <SplineFallback className={className} />}>
      <MockSpline scene={sceneUrl} className={className} />
    </Suspense>
  );
};

// Predefined scene components for easy use
export const HeroSpline: React.FC<{ className?: string }> = ({ className }) => (
  <SplineScene scene="HERO_STARFIELD" className={className} />
);

export const ProductSpline: React.FC<{ className?: string }> = ({ className }) => (
  <SplineScene scene="PRODUCT_SHOWCASE" className={className} />
);

export const NFTSpline: React.FC<{ className?: string }> = ({ className }) => (
  <SplineScene scene="NFT_GALLERY" className={className} />
);

export const DeFiSpline: React.FC<{ className?: string }> = ({ className }) => (
  <SplineScene scene="DEFI_HUB" className={className} />
);

export default SplineScene;