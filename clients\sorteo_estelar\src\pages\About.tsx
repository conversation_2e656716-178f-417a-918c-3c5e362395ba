import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Users, Target, Award, Shield, Globe, Zap,
  Heart, Star, TrendingUp, CheckCircle, 
  Linkedin, Twitter, Github, Mail,
  Calendar, MapPin, Phone, ExternalLink
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  avatar: string;
  social: {
    linkedin?: string;
    twitter?: string;
    github?: string;
    email?: string;
  };
  expertise: string[];
}

interface Milestone {
  id: string;
  year: string;
  title: string;
  description: string;
  icon: React.ReactNode;
}

interface Value {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}

const About: React.FC = () => {
  const { t } = useTranslation();
  const [activeSection, setActiveSection] = useState<'mission' | 'team' | 'history' | 'values'>('mission');

  const teamMembers: TeamMember[] = [
    {
      id: '1',
      name: 'Dr. <PERSON>',
      role: 'CEO & Fundadora',
      bio: 'Pionera en la aplicación de blockchain a sistemas de lotería. Con más de 15 años de experiencia en fintech y criptomonedas.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20CEO%20woman%20Elena%20Vasquez%20blockchain%20expert&image_size=square',
      social: {
        linkedin: 'https://linkedin.com/in/elena-vasquez',
        twitter: 'https://twitter.com/elena_vasquez',
        email: '<EMAIL>'
      },
      expertise: ['Blockchain', 'Fintech', 'Liderazgo', 'Estrategia']
    },
    {
      id: '2',
      name: 'Carlos Mendoza',
      role: 'CTO',
      bio: 'Arquitecto de software especializado en sistemas distribuidos y smart contracts. Ex-ingeniero senior en Google y Ethereum Foundation.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20CTO%20man%20Carlos%20Mendoza%20software%20architect&image_size=square',
      social: {
        linkedin: 'https://linkedin.com/in/carlos-mendoza',
        github: 'https://github.com/carlosmendoza',
        email: '<EMAIL>'
      },
      expertise: ['Smart Contracts', 'Arquitectura', 'Solidity', 'DevOps']
    },
    {
      id: '3',
      name: 'María González',
      role: 'Head of Product',
      bio: 'Diseñadora de experiencias digitales con enfoque en gamificación y engagement. Anteriormente en Spotify y Airbnb.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20product%20manager%20woman%20Maria%20Gonzalez%20UX%20designer&image_size=square',
      social: {
        linkedin: 'https://linkedin.com/in/maria-gonzalez',
        twitter: 'https://twitter.com/maria_ux',
        email: '<EMAIL>'
      },
      expertise: ['UX/UI', 'Product Strategy', 'Gamificación', 'Research']
    },
    {
      id: '4',
      name: 'Roberto Silva',
      role: 'Head of Security',
      bio: 'Experto en ciberseguridad y auditoría de smart contracts. Certificado en múltiples frameworks de seguridad blockchain.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20security%20expert%20man%20Roberto%20Silva%20cybersecurity&image_size=square',
      social: {
        linkedin: 'https://linkedin.com/in/roberto-silva',
        github: 'https://github.com/robertosilva',
        email: '<EMAIL>'
      },
      expertise: ['Cybersecurity', 'Smart Contract Auditing', 'Penetration Testing', 'Compliance']
    },
    {
      id: '5',
      name: 'Ana Martínez',
      role: 'Head of Marketing',
      bio: 'Especialista en marketing digital y growth hacking para startups tecnológicas. MBA en Marketing Digital.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20marketing%20manager%20woman%20Ana%20Martinez%20digital%20marketing&image_size=square',
      social: {
        linkedin: 'https://linkedin.com/in/ana-martinez',
        twitter: 'https://twitter.com/ana_marketing',
        email: '<EMAIL>'
      },
      expertise: ['Digital Marketing', 'Growth Hacking', 'Content Strategy', 'Analytics']
    },
    {
      id: '6',
      name: 'Luis Fernández',
      role: 'Lead Developer',
      bio: 'Desarrollador full-stack con especialización en React, Node.js y tecnologías blockchain. Contribuidor activo en proyectos open source.',
      avatar: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20lead%20developer%20man%20Luis%20Fernandez%20software%20engineer&image_size=square',
      social: {
        github: 'https://github.com/luisfernandez',
        linkedin: 'https://linkedin.com/in/luis-fernandez',
        email: '<EMAIL>'
      },
      expertise: ['React', 'Node.js', 'Web3', 'TypeScript']
    }
  ];

  const milestones: Milestone[] = [
    {
      id: '1',
      year: '2022',
      title: 'Fundación de Sorteo Estelar',
      description: 'Inicio del proyecto con la visión de revolucionar las loterías mediante blockchain.',
      icon: <Star className="w-6 h-6" />
    },
    {
      id: '2',
      year: '2023',
      title: 'Lanzamiento del MVP',
      description: 'Primera versión de la plataforma con sorteos básicos y pagos tradicionales.',
      icon: <Zap className="w-6 h-6" />
    },
    {
      id: '3',
      year: '2023',
      title: 'Integración Web3',
      description: 'Implementación de pagos con criptomonedas y smart contracts para transparencia.',
      icon: <Shield className="w-6 h-6" />
    },
    {
      id: '4',
      year: '2024',
      title: 'Expansión DeFi',
      description: 'Lanzamiento de funcionalidades DeFi: staking, yield farming y NFT marketplace.',
      icon: <TrendingUp className="w-6 h-6" />
    },
    {
      id: '5',
      year: '2024',
      title: 'Alcance Global',
      description: 'Expansión internacional con soporte multi-moneda y cumplimiento regulatorio.',
      icon: <Globe className="w-6 h-6" />
    }
  ];

  const values: Value[] = [
    {
      id: '1',
      title: 'Transparencia',
      description: 'Todos nuestros sorteos son verificables en blockchain, garantizando total transparencia en cada proceso.',
      icon: <Shield className="w-8 h-8" />,
      color: 'blue'
    },
    {
      id: '2',
      title: 'Innovación',
      description: 'Constantemente exploramos nuevas tecnologías para mejorar la experiencia de nuestros usuarios.',
      icon: <Zap className="w-8 h-8" />,
      color: 'purple'
    },
    {
      id: '3',
      title: 'Seguridad',
      description: 'La protección de los datos y fondos de nuestros usuarios es nuestra máxima prioridad.',
      icon: <Award className="w-8 h-8" />,
      color: 'green'
    },
    {
      id: '4',
      title: 'Inclusión',
      description: 'Creemos en hacer accesibles las oportunidades para personas de todo el mundo.',
      icon: <Heart className="w-8 h-8" />,
      color: 'red'
    },
    {
      id: '5',
      title: 'Excelencia',
      description: 'Nos esforzamos por ofrecer la mejor experiencia posible en cada interacción.',
      icon: <Star className="w-8 h-8" />,
      color: 'amber'
    },
    {
      id: '6',
      title: 'Comunidad',
      description: 'Construimos junto a nuestra comunidad, escuchando y respondiendo a sus necesidades.',
      icon: <Users className="w-8 h-8" />,
      color: 'indigo'
    }
  ];

  const stats = [
    { label: 'Usuarios Activos', value: '50,000+', icon: <Users className="w-6 h-6" /> },
    { label: 'Sorteos Realizados', value: '1,200+', icon: <Target className="w-6 h-6" /> },
    { label: 'Premios Entregados', value: '$2.5M+', icon: <Award className="w-6 h-6" /> },
    { label: 'Países Atendidos', value: '25+', icon: <Globe className="w-6 h-6" /> }
  ];

  const getValueColor = (color: string) => {
    switch (color) {
      case 'blue': return 'text-blue-400';
      case 'purple': return 'text-purple-400';
      case 'green': return 'text-green-400';
      case 'red': return 'text-red-400';
      case 'amber': return 'text-amber-400';
      case 'indigo': return 'text-indigo-400';
      default: return 'text-white';
    }
  };

  const getValueBg = (color: string) => {
    switch (color) {
      case 'blue': return 'bg-blue-500/20';
      case 'purple': return 'bg-purple-500/20';
      case 'green': return 'bg-green-500/20';
      case 'red': return 'bg-red-500/20';
      case 'amber': return 'bg-amber-500/20';
      case 'indigo': return 'bg-indigo-500/20';
      default: return 'bg-white/20';
    }
  };

  const TeamMemberCard: React.FC<{ member: TeamMember }> = ({ member }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="text-center space-y-4">
        <img 
          src={member.avatar} 
          alt={member.name}
          className="w-24 h-24 rounded-full mx-auto object-cover"
        />
        
        <div>
          <h3 className="text-white font-semibold text-lg">{member.name}</h3>
          <p className="text-purple-400 font-medium">{member.role}</p>
        </div>
        
        <p className="text-white/80 text-sm">{member.bio}</p>
        
        <div className="flex flex-wrap gap-2 justify-center">
          {member.expertise.map(skill => (
            <span 
              key={skill}
              className="px-2 py-1 bg-white/10 text-white/70 rounded-full text-xs"
            >
              {skill}
            </span>
          ))}
        </div>
        
        <div className="flex justify-center space-x-3 pt-2">
          {member.social.linkedin && (
            <a 
              href={member.social.linkedin}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-white/70 hover:text-blue-400 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Linkedin className="w-4 h-4" />
            </a>
          )}
          {member.social.twitter && (
            <a 
              href={member.social.twitter}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-white/70 hover:text-blue-400 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Twitter className="w-4 h-4" />
            </a>
          )}
          {member.social.github && (
            <a 
              href={member.social.github}
              target="_blank"
              rel="noopener noreferrer"
              className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
            >
              <Github className="w-4 h-4" />
            </a>
          )}
          {member.social.email && (
            <a 
              href={`mailto:${member.social.email}`}
              className="p-2 text-white/70 hover:text-green-400 hover:bg-white/10 rounded-lg transition-colors"
            >
              <Mail className="w-4 h-4" />
            </a>
          )}
        </div>
      </div>
    </Card>
  );

  const ValueCard: React.FC<{ value: Value }> = ({ value }) => (
    <Card variant="glass" className="hover:scale-105 transition-all duration-300">
      <div className="text-center space-y-4">
        <div className={`w-16 h-16 rounded-full mx-auto flex items-center justify-center ${getValueBg(value.color)}`}>
          <div className={getValueColor(value.color)}>
            {value.icon}
          </div>
        </div>
        
        <h3 className="text-white font-semibold text-lg">{value.title}</h3>
        <p className="text-white/80 text-sm">{value.description}</p>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Sobre <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Sorteo Estelar</span>
          </h1>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Revolucionando el mundo de las loterías con tecnología blockchain, 
            transparencia total y oportunidades globales para todos.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {stats.map((stat, index) => (
            <Card key={index} variant="glass">
              <div className="text-center">
                <div className="text-purple-400 mb-2 flex justify-center">
                  {stat.icon}
                </div>
                <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
                <div className="text-white/70 text-sm">{stat.label}</div>
              </div>
            </Card>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {[
            { id: 'mission', label: 'Misión & Visión', icon: Target },
            { id: 'team', label: 'Nuestro Equipo', icon: Users },
            { id: 'history', label: 'Historia', icon: Calendar },
            { id: 'values', label: 'Valores', icon: Heart }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveSection(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeSection === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium hidden sm:block">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content Sections */}
        <div className="space-y-8">
          {activeSection === 'mission' && (
            <div className="space-y-8">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
                        <Target className="w-6 h-6 text-purple-400" />
                      </div>
                      <h2 className="text-2xl font-bold text-white">Nuestra Misión</h2>
                    </div>
                    <p className="text-white/80 text-lg leading-relaxed">
                      Democratizar el acceso a oportunidades de ganar premios extraordinarios 
                      mediante una plataforma transparente, segura y justa que utiliza la 
                      tecnología blockchain para garantizar la integridad de cada sorteo.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="text-white/80">Transparencia total en todos los procesos</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="text-white/80">Acceso global sin restricciones</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="w-5 h-5 text-green-400" />
                        <span className="text-white/80">Seguridad de nivel empresarial</span>
                      </div>
                    </div>
                  </div>
                </Card>
                
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-amber-500/20 rounded-full flex items-center justify-center">
                        <Star className="w-6 h-6 text-amber-400" />
                      </div>
                      <h2 className="text-2xl font-bold text-white">Nuestra Visión</h2>
                    </div>
                    <p className="text-white/80 text-lg leading-relaxed">
                      Convertirnos en la plataforma de loterías digitales líder mundial, 
                      estableciendo el estándar de oro para la transparencia, innovación 
                      y experiencia del usuario en el ecosistema Web3.
                    </p>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Star className="w-5 h-5 text-amber-400" />
                        <span className="text-white/80">Liderazgo en innovación blockchain</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Star className="w-5 h-5 text-amber-400" />
                        <span className="text-white/80">Experiencia de usuario excepcional</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Star className="w-5 h-5 text-amber-400" />
                        <span className="text-white/80">Impacto positivo en la comunidad</span>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
              
              <Card variant="glass">
                <div className="text-center space-y-6">
                  <h2 className="text-2xl font-bold text-white">¿Por qué Sorteo Estelar?</h2>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="space-y-3">
                      <div className="w-16 h-16 bg-blue-500/20 rounded-full mx-auto flex items-center justify-center">
                        <Shield className="w-8 h-8 text-blue-400" />
                      </div>
                      <h3 className="text-white font-semibold">Tecnología Avanzada</h3>
                      <p className="text-white/70 text-sm">
                        Utilizamos smart contracts auditados y tecnología blockchain 
                        de última generación para garantizar la máxima seguridad.
                      </p>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="w-16 h-16 bg-green-500/20 rounded-full mx-auto flex items-center justify-center">
                        <Globe className="w-8 h-8 text-green-400" />
                      </div>
                      <h3 className="text-white font-semibold">Alcance Global</h3>
                      <p className="text-white/70 text-sm">
                        Operamos en múltiples países con soporte para diversas 
                        monedas y métodos de pago tradicionales y digitales.
                      </p>
                    </div>
                    
                    <div className="space-y-3">
                      <div className="w-16 h-16 bg-purple-500/20 rounded-full mx-auto flex items-center justify-center">
                        <Users className="w-8 h-8 text-purple-400" />
                      </div>
                      <h3 className="text-white font-semibold">Comunidad Activa</h3>
                      <p className="text-white/70 text-sm">
                        Más de 50,000 usuarios confían en nosotros y participan 
                        activamente en nuestro ecosistema DeFi.
                      </p>
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeSection === 'team' && (
            <div className="space-y-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-white mb-4">Conoce a Nuestro Equipo</h2>
                <p className="text-white/80 max-w-2xl mx-auto">
                  Un grupo diverso de expertos apasionados por la innovación y 
                  comprometidos con la excelencia en cada aspecto de nuestra plataforma.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {teamMembers.map(member => (
                  <TeamMemberCard key={member.id} member={member} />
                ))}
              </div>
              
              <Card variant="glass">
                <div className="text-center space-y-4">
                  <h3 className="text-xl font-bold text-white">¿Quieres unirte a nuestro equipo?</h3>
                  <p className="text-white/80">
                    Estamos siempre buscando talento excepcional para unirse a nuestra misión 
                    de revolucionar el mundo de las loterías digitales.
                  </p>
                  <Button variant="primary">
                    <Mail className="w-4 h-4 mr-2" />
                    Ver Oportunidades
                  </Button>
                </div>
              </Card>
            </div>
          )}

          {activeSection === 'history' && (
            <div className="space-y-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-white mb-4">Nuestra Historia</h2>
                <p className="text-white/80 max-w-2xl mx-auto">
                  Desde nuestros humildes comienzos hasta convertirnos en una plataforma 
                  líder en loterías blockchain, cada paso ha sido guiado por la innovación.
                </p>
              </div>
              
              <div className="relative">
                <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-purple-500 to-amber-500 rounded-full"></div>
                
                <div className="space-y-8">
                  {milestones.map((milestone, index) => (
                    <div key={milestone.id} className={`flex items-center ${index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'}`}>
                      <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8 text-right' : 'pl-8 text-left'}`}>
                        <Card variant="glass">
                          <div className="space-y-3">
                            <div className={`flex items-center space-x-3 ${index % 2 === 0 ? 'justify-end' : 'justify-start'}`}>
                              <div className="text-purple-400">
                                {milestone.icon}
                              </div>
                              <span className="text-2xl font-bold text-white">{milestone.year}</span>
                            </div>
                            <h3 className="text-white font-semibold text-lg">{milestone.title}</h3>
                            <p className="text-white/80">{milestone.description}</p>
                          </div>
                        </Card>
                      </div>
                      
                      <div className="relative z-10">
                        <div className="w-4 h-4 bg-white rounded-full border-4 border-purple-500"></div>
                      </div>
                      
                      <div className="w-1/2"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeSection === 'values' && (
            <div className="space-y-8">
              <div className="text-center">
                <h2 className="text-3xl font-bold text-white mb-4">Nuestros Valores</h2>
                <p className="text-white/80 max-w-2xl mx-auto">
                  Los principios fundamentales que guían cada decisión y acción 
                  en Sorteo Estelar, definiendo quiénes somos y hacia dónde vamos.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {values.map(value => (
                  <ValueCard key={value.id} value={value} />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Contact Section */}
        <Card variant="glass" className="mt-12">
          <div className="text-center space-y-6">
            <h2 className="text-2xl font-bold text-white">¿Tienes preguntas?</h2>
            <p className="text-white/80 max-w-2xl mx-auto">
              Estamos aquí para ayudarte. Contáctanos y nuestro equipo te responderá 
              lo antes posible.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
              <div className="flex items-center justify-center space-x-3">
                <Mail className="w-5 h-5 text-purple-400" />
                <div className="text-left">
                  <div className="text-white font-medium">Email</div>
                  <div className="text-white/70 text-sm"><EMAIL></div>
                </div>
              </div>
              
              <div className="flex items-center justify-center space-x-3">
                <Phone className="w-5 h-5 text-green-400" />
                <div className="text-left">
                  <div className="text-white font-medium">Teléfono</div>
                  <div className="text-white/70 text-sm">+****************</div>
                </div>
              </div>
              
              <div className="flex items-center justify-center space-x-3">
                <MapPin className="w-5 h-5 text-blue-400" />
                <div className="text-left">
                  <div className="text-white font-medium">Ubicación</div>
                  <div className="text-white/70 text-sm">San Francisco, CA</div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-center space-x-4">
              <Button variant="primary">
                <Mail className="w-4 h-4 mr-2" />
                Contáctanos
              </Button>
              <Button variant="outline">
                <ExternalLink className="w-4 h-4 mr-2" />
                Centro de Ayuda
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default About;