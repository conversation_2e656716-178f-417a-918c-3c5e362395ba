| API ID      | API Name                     | Responsible Command Office | Version | Status      | Link to Definition                                  | Link to Diagram/Spec                               | Related Services | Related Flows | Notes                                             |
| :---------- | :--------------------------- | :------------------------- | :------ | :---------- | :-------------------------------------------------- | :------------------------------------------------- | :--------------- | :------------ | :------------------------------------------------ |
| `CTO_API001` | `ExampleInternalAuthAPI`     | `CTO`                      | `0.1`   | `Proposed`  | `../api/cto/CTO_API001_ExampleInternalAuthAPI.md` | `../api/cto/CTO_API001_ExampleInternalAuthAPI.yaml` | `CSOL_S001`      | `CTO_F001`    | Initial proposal for internal authentication service. |
