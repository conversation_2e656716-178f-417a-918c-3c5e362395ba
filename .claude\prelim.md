Understand project context recursively and iteratively thoroughly in order to have a clear roadmap and visioning about master project bootstrap on key missing components for engine starting and autonomous agency agentic frameworks based in charge of managing and building infrastructure as DDD hexagonal structure; so with this context on hand and with most advanced engineering and project management techniques and tool calling in order to build streamlining agentic development rules @/.gemini/GEMINI.md and bootstrapping commands @/.claude/commands.md https://www.anthropic.com/engineering/claude-code-best-practices linking these files and providing instructions to generate CLAUDE.md files for each client project @/clients as code development rules in alignment in properly engineered sequence for PRD document generation with some PRDs already in @/.trae/documents for the agency project and in general in alignment with agency project management architecture starting with client onboarding or research and scouting content curation from each command headquarter delegate team into CKO headquarter deriving requests for proposal generation as RFP for internal operational headquarters as CPrO and CPO and COO and CPOO and CEO and CAO and CSO strategic Project Proposal PP generation and curation from strategic experts analysis and approval fo systemic project initiation, producing a thorough PRD document with recursive @docs\templates\project_management @docs\templates\software_engineering @docs\templates\client-onboarding templates application and generative completion for each client project or according Master Project and Subprojects Architecture @docs\projects as for Agency Main Master Project @docs\projects\estratix_master_project, leveraging agentic-based LLM queries using deep-thinking and deep-reasoning tools, also deep-research and PlanRAG @archive/guides/2406.12430v1.pdf, implementing the below prompt engineering, Meta-prompting and agentic workflows orchestration patterns shared in PDF articles for effective agents communication implementation with remote SSE MCP tools or with A2A protocol in effective agentic workflows orchestration with multiple agentic processes with shared context and agents' communication leveraging multiple MCP tools recursively for effective PDCA process-based project-task execution of project-tasks from effective WBS tasks description and AI assistant context priming assigned to a process composed by tasks assigned to agents with tools unlocking generative and recursive engagement for effective implementation of hybrid functional-matrix organizational structure with Project Management Architecture integrated with Agentic Frameworks teams composability and conformation assembling bigger command units as we achieve the main CEO and Command Officers Headquarters Bootstrap in charge of managing each of all agentic frameworks in properly orchestrated workflows sequences in parallel for leveraging effectively army field forces with full organizational command headquarters implementation achieved recursively through the main executive command officers and agents as a master builder agent or tasks executor agent leveraging generative LLM models for code generation and full organizational structure bootstrap recursively and progressively through steady and straightforward processes and flows and patterns and Command Offices Headquarters building based on effective agency patterns implementation composed of flows and processes and agents, and tools, and tasks implemented on each of the agentic frameworks force powerful agency self-observability and observability implementation for continuous improvement, thanks to effective management of PDCA cycles on agents definition and process-based tasks execution for recursive full command headquarters implementation and powerful agentic workflows orchestration in multiple parallelization levels, as tool calling and agents calling and workflows execution and agentic frameworks invocation applied to approved supported by CKO processes and knowledge research and scouting workflows for knowledge ingestion as agentic frameworks documentation from web research and crawling as main inflow of data from this pipeline base for Deep Learning and Deep Reinforcement Learning processes for agents training and alternative methologies as GEPA @archive/guides/2507.19457v1.pdf or AgentFly @archive/guides/2507.14897v1.pdf or AIME @archive/guides/2410.03131v3.pdf or ExpertPromting @archive/guides/2305.14688v2.pdf or STOP @archive/guides/2310.02304v3.pdf or Conversational Prompt Engineering @archive/guides/2408.04560v1.pdf or PromptAgent @archive/guides/2310.16427v2.pdf or Chain-of-Thought Prompting @archive/guides/2201.11903v6.pdf in order to leveraging effectively Claude-Code SubAgents using /agent command https://docs.anthropic.com/en/docs/claude-code/sub-agents#quick-start this way we can boostrap command offices headquarters defining key strategic executive and management and operational roles for key core command offices headquarters in alignment with ageny patterns' models @docs\models @docs\models\model_matrix.md for agency key data structures and models objects data schemas implementation in direct relation with command headquarters per their one-to-many HQ-Models for effective data structures full systemic management moving from models matrices in .md to fully systemic API persistent data structures state management by agentic frameworks also in charge of managing and register DDD hexagonal structures functions as tools; maintain low entropy in the system; apply least action principle; find gaps for improvement, ask if any questions
List of Articles Paths:
@archive/guides/2411.13768v2.pdf
@archive/guides/2406.12430v1.pdf
@archive/guides/2502.13569v1.pdf
@archive/guides/2507.18638v1.pdf
@archive/guides/2507.14897v1.pdf
@archive/guides/2507.14241v3.pdf
@archive/guides/2507.09839v1.pdf
@archive/guides/2507.19457v1.pdf
@archive/guides/2503.17603v1.pdf
@archive/guides/2507.11988v2.pdf
@archive/guides/2506.03401v1.pdf
@archive/guides/2211.01910v2.pdf
@archive/guides/2312.06562v3.pdf
@archive/guides/2305.14688v2.pdf
@archive/guides/2210.03629v3.pdf
@archive/guides/2311.05661v3.pdf
@archive/guides/2310.02304v3.pdf
@archive/guides/2408.04560v1.pdf
@archive/guides/2310.16427v2.pdf
@archive/guides/2201.11903v6.pdf
@archive/guides/2409.15199v1.pdf
@archive/guides/2307.05300v4.pdf
@archive/guides/2405.10467v4.pdf
@archive/guides/2411.05285v2.pdf
@archive/guides/2406.06608v6.pdf
@archive/guides/2411.13768v2.pdf
