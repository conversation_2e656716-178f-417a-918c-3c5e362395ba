export class ContractError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly txHash?: string
  ) {
    super(message);
    this.name = 'ContractError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public readonly field?: string,
    public readonly value?: any
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class NetworkError extends Error {
  constructor(
    message: string,
    public readonly chainId?: number,
    public readonly network?: string
  ) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class InsufficientFundsError extends Error {
  constructor(
    message: string,
    public readonly required?: string,
    public readonly available?: string
  ) {
    super(message);
    this.name = 'InsufficientFundsError';
  }
}

export class UnauthorizedError extends Error {
  constructor(
    message: string,
    public readonly requiredRole?: string,
    public readonly userRole?: string
  ) {
    super(message);
    this.name = 'UnauthorizedError';
  }
}