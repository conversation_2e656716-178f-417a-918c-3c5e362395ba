# ESTRATIX Library Matrix

---

## 1. Overview

This matrix serves as the central registry for all third-party libraries, frameworks, and technologies used or evaluated within the ESTRATIX ecosystem. Its purpose is to enforce a standardized, secure, and well-documented technology stack, facilitating informed architectural decisions and streamlining development.

---

## 2. Library Inventory

| Library Name | Category (e.g., Agentic, Backend, Frontend, Data, DevOps, Testing) | Version (from pyproject.toml) | License | Link (Repo/Docs) | Status (e.g., Evaluating, Approved, Restricted, Deprecated) | ESTRATIX Decision & Rationale | Integration Notes & Patterns |
|---|---|---|---|---|---|---|---|
| `crewai` | Agentic | >=0.36.0 | MIT | [crewAI Docs](https://github.com/crewAIInc/crewAI) | Approved | Selected as the primary framework for multi-agent collaboration due to its robust role-based design and process orchestration capabilities. | Implement via `CtoIngestionAgents` factory pattern. Use `Flow` for orchestration. |
| `crewai-tools` | Agentic | >=0.1.6 | MIT | [crewAI-tools Docs](https://github.com/crewAIInc/crewAI-tools) | Approved | Provides essential, pre-built tools that accelerate agent development and integration with external services. | Import specific tools as needed (e.g., `ScrapeWebsiteTool`). |
| `pydantic` | Backend, Data | * | MIT | [Pydantic Docs](https://github.com/pydantic/pydantic) | Approved | Core library for data validation, settings management, and creating structured data models across the entire project. | Used for all data models in `src/domain/models` and for agent/tool definitions. |
| `fastapi` | Backend | * | MIT | [FastAPI Docs](https://github.com/tiangolo/fastapi) | Approved | Primary framework for building high-performance APIs. Aligns with our Python-based backend strategy and Pydantic integration. | All new services should use the approved FastAPI template (`R-BE-002`). |
| `uvicorn` | DevOps | * | BSD-3-Clause | [Uvicorn Docs](https://www.uvicorn.org/) | Approved | High-performance ASGI server, required to run FastAPI applications. | Used as the standard server for running FastAPI services. |
| `uv` | DevOps | * | MIT / Apache 2.0 | [uv Docs](https://github.com/astral-sh/uv) | Approved | Adopted as the unified project and virtual environment manager for its speed and modern features, replacing pip and requirements.txt. | Use `uv pip compile` and `uv pip sync` for dependency management. |
| `python-dotenv` | DevOps | >=1.0.1 | BSD-3-Clause | [python-dotenv Docs](https://github.com/theskumar/python-dotenv) | Approved | Standard method for managing environment variables in local development. Ensures secure handling of credentials. | Load via `load_dotenv()` in main application entry points. Use `.env.example` for templates. |
| `langchain-openai` | Agentic | >=0.1.8 | MIT | [LangChain Docs](https://python.langchain.com/docs/integrations/llms/openai) | Approved | Provides LangChain integrations for OpenAI models, including chat models and embeddings. | Used for LLM and embedding model access within agentic components. |
| `openai` | Agentic | * | Apache-2.0 | [OpenAI Python](https://github.com/openai/openai-python) | Approved | Official Python library for the OpenAI API. | Direct interaction with OpenAI services where LangChain is not required. |
| `pydantic-ai` | Agentic | * | MIT | [Pydantic-AI](https://github.com/pydantic/pydantic-ai) | Evaluating | Framework for building structured AI applications using Pydantic models. | To be evaluated as an alternative or complement to CrewAI for specific use cases. |
| `google-generativeai` | Agentic | * | Apache-2.0 | [Google AI Python](https://github.com/google/generative-ai-python) | Evaluating | Python SDK for Google's generative AI models (e.g., Gemini). | To be evaluated for multi-cloud LLM strategy. |
| `langgraph` | Agentic | * | MIT | [LangGraph Docs](https://langchain-ai.github.io/langgraph/) | Evaluating | Library for building stateful, multi-agent applications with LLMs. | To be evaluated for complex, cyclical agentic workflows. |
| `pocketflow` | Agentic | * | MIT | [PocketFlow](https://github.com/pocketflow/pocketflow) | Evaluating | Lightweight, modular workflow orchestration framework. | To be evaluated as a potential alternative for process and flow management. |
| `pymilvus` | Data | >=2.3.0,<2.4.0 | Apache-2.0 | [PyMilvus Docs](https://milvus.io/docs/pymilvus.md) | Approved | Python client for Milvus. Current primary vector database for knowledge ingestion and RAG capabilities. | See `R-DA-001` for standards. To be evaluated against alternatives. |
| `qdrant-client` | Data | * | Apache-2.0 | [Qdrant Docs](https://github.com/qdrant/qdrant) | Evaluating | Python client for Qdrant. Candidate for vector database. To be evaluated for performance and features. | See task `R&D-VECDB-EVAL`. |
| `weaviate` | Data | - | BSD-3-Clause | [Weaviate Docs](https://github.com/weaviate/weaviate) | Evaluating | Candidate for vector database. To be evaluated. | See task `R&D-VECDB-EVAL`. |
| `pinecone` | Data | - | Proprietary | [Pinecone Website](https://www.pinecone.io/) | Evaluating | Candidate for vector database (managed service). To be evaluated. | See task `R&D-VECDB-EVAL`. |
| `fastembed` | Data | * | Apache-2.0 | [FastEmbed Docs](https://github.com/qdrant/fastembed) | Approved | Lightweight, fast, and accurate embedding generation library. | Used for local embedding generation, especially with Qdrant. |
| `sentence-transformers` | Data | >=2.6.0,<3.0.0 | Apache-2.0 | [Sentence-Transformers](https://www.sbert.net/) | Approved | Provides easy methods to compute dense vector representations for sentences, paragraphs, and images. | Core component for generating embeddings for various models. |
| `pymongo` | Data | * | Apache-2.0 | [PyMongo Docs](https://pymongo.readthedocs.io/) | Approved | Official Python driver for MongoDB. | Used for interacting with MongoDB for metadata storage or other non-vector data needs. |
| `tiktoken` | Data | * | MIT | [TikToken Repo](https://github.com/openai/tiktoken) | Approved | Fast BPE tokenizer used by OpenAI models. | Essential for accurately counting tokens for context management and cost estimation. |
| `ruff` | DevOps, Testing | * | MIT | [Ruff Docs](https://docs.astral.sh/ruff/) | Approved | Extremely fast Python linter and code formatter. | Primary tool for enforcing code style and quality. Integrated into pre-commit hooks. |
| `mypy` | DevOps, Testing | * | MIT | [MyPy Docs](http://mypy-lang.org/) | Approved | Static type checker for Python. | Used to enforce type safety and catch type-related errors. |
| `pytest` | Testing | * | MIT | [Pytest Docs](https://docs.pytest.org/) | Approved | The standard framework for writing and running tests in the project. | All new code should be accompanied by corresponding pytest tests. |
| `pytest-asyncio` | Testing | * | Apache-2.0 | [Pytest-Asyncio Repo](https://github.com/pytest-dev/pytest-asyncio) | Approved | Pytest plugin for testing asyncio code. | Required for testing asynchronous components, such as FastAPI endpoints. |
| `pre-commit` | DevOps | * | MIT | [Pre-commit Docs](https://pre-commit.com/) | Approved | Framework for managing and maintaining multi-language pre-commit hooks. | Used to automate code formatting and linting before commits. |
| `chainlit` | Frontend | * | Apache-2.0 | [Chainlit Docs](https://docs.chainlit.io/get-started/overview) | Approved (Dev) | Framework for creating conversational AI interfaces. | Used for rapid prototyping and debugging of agentic flows in the dev environment. |
| `litellm` | Agentic, DevOps | * | MIT | [LiteLLM Docs](https://github.com/BerriAI/litellm) | Approved | Provides a unified interface for calling 100+ LLMs, simplifying model management and provider switching. Used to abstract the connection to OpenRouter. | Integrated into the LLM configuration layer to handle API calls to different providers seamlessly. |
| `DeepSeek` | Agentic (Model) | N/A | Custom | [DeepSeek AI](https://www.deepseek.com/) | Approved | Default free model accessed via OpenRouter, chosen for its strong performance-to-cost ratio for general agentic tasks. | Accessed via OpenRouter using the model string `openrouter/deepseek/deepseek-r1-0528-qwen3-8b:free`. Not a direct library dependency. |
| `PyPDF2` | Data, PDF Processing | 3.0.1 | BSD-3-Clause | [PyPDF2](https://github.com/py-pdf/PyPDF2) | Approved | Basic PDF text extraction for simple document processing tasks. | Standard PDF reader for simple text extraction in k004_pdf_processor_tool. |
| `pdfplumber` | Data, PDF Processing | 0.9.0 | MIT | [pdfplumber](https://github.com/jsvine/pdfplumber) | Approved | Advanced PDF table extraction and layout analysis capabilities. | Enhanced table and layout analysis in k033_enhanced_pdf_processor_tool. |
| `PyMuPDF` | Data, PDF Processing | 1.23.0 | AGPL-3.0 | [PyMuPDF](https://github.com/pymupdf/PyMuPDF) | Approved | Comprehensive PDF processing with OCR, image extraction, and manipulation. | Advanced PDF manipulation, OCR, image extraction in k034_advanced_pdf_research_tool. |
| `pdfminer` | Data, PDF Processing | 20231228 | MIT | [pdfminer](https://github.com/pdfminer/pdfminer.six) | Approved | Deep PDF text analysis and low-level PDF parsing capabilities. | Low-level PDF parsing and text extraction, used by MarkItDown converter. |
| `Camelot` | Data, PDF Processing | 0.11.0 | MIT | [Camelot](https://github.com/camelot-dev/camelot) | Approved | Specialized PDF table extraction with high accuracy for tabular data. | Specialized table extraction from PDFs in advanced processing workflows. |
| `Tabula` | Data, PDF Processing | 2.9.0 | MIT | [Tabula](https://github.com/chezou/tabula-py) | Approved | Java-based robust table extraction from PDF documents. | Robust table extraction using Java backend for complex table structures. |
| `MonkeyOCR` | Data, OCR, AI | 1.0.0 | Apache-2.0 | [MonkeyOCR](https://github.com/echo840/MonkeyOCR) | Evaluating | Advanced OCR with AI for complex document recognition and structure analysis. | AI-powered OCR for complex documents, under evaluation for integration. |
| `MarkItDown` | Data, Document Processing | 0.0.1a2 | MIT | [MarkItDown](https://github.com/microsoft/markitdown) | Approved | Multi-format document conversion to Markdown, including PDF processing. | Microsoft's document-to-markdown converter, integrated for document ingestion workflows. |
| `magika` | Data, File Analysis | * | Apache-2.0 | [Magika](https://github.com/google/magika) | Approved | AI-powered file type detection for accurate content type identification. | Used by MarkItDown for stream information guessing and mimetype detection. |
| `networkx` | Data, Graph Processing | * | BSD-3-Clause | [NetworkX](https://github.com/networkx/networkx) | Approved | Graph analysis and network algorithms for workflow dependency management. | Used in k037_master_orchestration_workflow for dependency graph management. |
| `pandas` | Data, Analytics | * | BSD-3-Clause | [Pandas](https://github.com/pandas-dev/pandas) | Approved | Data manipulation and analysis library for structured data processing. | Used across analytics workflows and data processing pipelines. |

---

## 3. Maintenance

This matrix must be updated whenever a new library is proposed for use or when the status of an existing library changes. All architectural decisions regarding the technology stack must be reflected here.
