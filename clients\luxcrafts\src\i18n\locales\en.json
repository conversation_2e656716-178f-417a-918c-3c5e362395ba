{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "connect": "Connect", "disconnect": "Disconnect", "wallet": "Wallet", "balance": "Balance", "transaction": "Transaction", "transactions": "Transactions", "address": "Address", "amount": "Amount", "fee": "Fee", "total": "Total", "status": "Status", "pending": "Pending", "completed": "Completed", "failed": "Failed", "approved": "Approved", "rejected": "Rejected", "date": "Date", "time": "Time", "name": "Name", "email": "Email", "phone": "Phone", "location": "Location", "price": "Price", "rating": "Rating", "reviews": "Reviews", "availability": "Availability", "book": "Book", "booked": "Booked", "booking": "Booking", "bookings": "Bookings", "service": "Service", "services": "Services", "provider": "Provider", "providers": "Providers", "property": "Property", "properties": "Properties", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "help": "Help", "support": "Support", "contact": "Contact", "about": "About", "terms": "Terms of Service", "privacy": "Privacy Policy", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register", "signup": "Sign Up", "signin": "Sign In"}, "navigation": {"home": "Home", "marketplace": "Marketplace", "services": "Services", "dashboard": "Dashboard", "tokens": "Tokens", "nft": "NFT", "defi": "<PERSON><PERSON><PERSON>", "admin": "Admin", "provider": "Provider Portal", "content": "Content Studio", "tokenization": "Asset Tokenization", "analytics": "Analytics"}, "home": {"hero": {"title": "Luxury Property Services", "subtitle": "Powered by Web3", "description": "Revolutionary platform connecting property owners with premium service providers through blockchain technology, AI-powered matching, and tokenized rewards.", "cta_primary": "Book Services Now", "cta_secondary": "Explore Marketplace"}, "stats": {"properties_managed": "Properties Managed", "service_providers": "Service Providers", "lux_tokens_staked": "LUX Tokens Staked", "customer_satisfaction": "Customer Satisfaction"}, "services": {"title": "Premium Property Services", "description": "Discover our comprehensive suite of luxury property services, each powered by cutting-edge technology and delivered by certified professionals.", "property_management": {"title": "Property Management", "description": "Comprehensive property oversight with AI-powered maintenance scheduling"}, "luxury_cleaning": {"title": "<PERSON><PERSON><PERSON> Cleaning", "description": "Premium cleaning services with eco-friendly products and white-glove treatment"}, "landscaping": {"title": "Landscaping & Design", "description": "Professional landscaping with sustainable design and smart irrigation"}, "remediation": {"title": "Property Remediation", "description": "Expert restoration services for water damage, mold, and structural issues"}, "analytics": {"title": "Investment Analytics", "description": "AI-powered property valuation and investment opportunity analysis"}, "crypto_payments": {"title": "Crypto Payments", "description": "Seamless Web3 payments with LUX token rewards and NFT certificates"}, "learn_more": "Learn More"}, "web3": {"title": "Web3-Powered Property Ecosystem", "description": "Experience the future of property services with blockchain technology, tokenized rewards, and decentralized finance integration.", "lux_rewards": {"title": "LUX Token Rewards", "description": "Earn LUX tokens for every service booking, property improvement, and platform participation. Stake tokens for premium benefits."}, "tokenization": {"title": "Property Tokenization", "description": "Tokenize property assets for fractional ownership, transparent rental income distribution, and liquid real estate investment."}, "nft_certificates": {"title": "NFT Certificates", "description": "Receive verifiable NFT certificates for property improvements, service completions, and membership tiers."}, "dashboard_preview": {"title": "Token Dashboard Preview", "lux_balance": "LUX Balance", "staked_amount": "Staked Amount", "rewards_earned": "Rewards Earned", "nfts_owned": "NFTs Owned", "access_dashboard": "Access Token Dashboard"}}, "cta": {"title": "Ready to Transform Your Property Experience?", "description": "Join thousands of property owners who trust Luxcrafts for premium services and innovative Web3 rewards.", "start_booking": "Start Booking Services", "become_provider": "Become a Provider"}}, "payment": {"web2": {"title": "Traditional Banking", "description": "Pay with credit cards, bank transfers, and ACH payments", "available_in": "Available in United States", "methods": {"credit_card": "Credit Card", "bank_transfer": "Bank Transfer", "ach": "ACH Payment", "paypal": "PayPal"}}, "web3": {"title": "Cryptocurrency Payments", "description": "Pay with Bitcoin, Ethereum, stablecoins, and LUX tokens", "available_globally": "Available Globally", "methods": {"bitcoin": "Bitcoin", "ethereum": "Ethereum", "usdc": "USDC", "usdt": "USDT", "lux_token": "LUX Token"}}, "toggle": {"web2": "Traditional Banking", "web3": "Crypto Payments", "switch_to_web2": "Switch to Traditional Banking", "switch_to_web3": "Switch to Crypto Payments"}}, "accessibility": {"skip_to_content": "Skip to main content", "menu": "<PERSON><PERSON>", "close_menu": "Close menu", "open_menu": "Open menu", "language_selector": "Language selector", "current_language": "Current language", "change_language": "Change language", "high_contrast": "High contrast mode", "normal_contrast": "Normal contrast mode", "increase_font_size": "Increase font size", "decrease_font_size": "Decrease font size", "reset_font_size": "Reset font size", "screen_reader_content": "Screen reader content", "keyboard_navigation": "Keyboard navigation available", "alternative_text": "Alternative text", "loading_content": "Loading content, please wait", "error_occurred": "An error occurred", "success_message": "Action completed successfully", "required_field": "Required field", "invalid_input": "Invalid input", "form_validation_error": "Please correct the errors in the form", "page_navigation": "Page navigation", "breadcrumb_navigation": "Breadcrumb navigation", "main_navigation": "Main navigation", "footer_navigation": "Footer navigation", "search_results": "Search results", "no_results_found": "No results found", "results_count": "{{count}} results found", "page_of_total": "Page {{current}} of {{total}}", "sort_by": "Sort by", "filter_by": "Filter by", "clear_filters": "Clear all filters", "apply_filters": "Apply filters", "expand_section": "Expand section", "collapse_section": "Collapse section", "show_more": "Show more", "show_less": "Show less", "external_link": "External link", "opens_in_new_window": "Opens in new window", "download_file": "Download file", "file_size": "File size: {{size}}", "file_type": "File type: {{type}}", "image_description": "Image: {{description}}", "video_description": "Video: {{description}}", "audio_description": "Audio: {{description}}", "chart_description": "Chart: {{description}}", "table_description": "Table: {{description}}", "interactive_element": "Interactive element", "button_action": "Button: {{action}}", "link_destination": "Link to: {{destination}}", "form_field": "Form field: {{label}}", "required_field_indicator": "Required field indicator", "optional_field_indicator": "Optional field", "character_count": "{{current}} of {{max}} characters", "password_requirements": "Password requirements", "password_strength": "Password strength: {{strength}}", "captcha_challenge": "CAPTCHA challenge", "security_verification": "Security verification required", "two_factor_auth": "Two-factor authentication", "biometric_auth": "Biometric authentication", "session_timeout": "Session timeout warning", "auto_save": "Auto-save enabled", "unsaved_changes": "You have unsaved changes", "confirm_navigation": "Confirm navigation away from page", "data_loading": "Data is loading", "data_error": "Error loading data", "retry_action": "Retry action", "refresh_page": "Refresh page", "contact_support": "Contact support for assistance"}, "errors": {"generic": "An unexpected error occurred. Please try again.", "network": "Network error. Please check your connection.", "validation": "Please check your input and try again.", "authentication": "Authentication failed. Please log in again.", "authorization": "You don't have permission to perform this action.", "not_found": "The requested resource was not found.", "server_error": "Server error. Please try again later.", "wallet_connection": "Failed to connect wallet. Please try again.", "transaction_failed": "Transaction failed. Please try again.", "insufficient_funds": "Insufficient funds for this transaction.", "gas_estimation_failed": "Failed to estimate gas fees.", "contract_interaction_failed": "Smart contract interaction failed.", "token_approval_failed": "Token approval failed.", "nft_mint_failed": "NFT minting failed.", "staking_failed": "Staking operation failed.", "unstaking_failed": "Unstaking operation failed.", "swap_failed": "Token swap failed.", "liquidity_failed": "Liquidity operation failed.", "payment_processing_failed": "Payment processing failed.", "service_booking_failed": "Service booking failed.", "file_upload_failed": "File upload failed.", "image_processing_failed": "Image processing failed.", "video_processing_failed": "Video processing failed.", "audio_processing_failed": "Audio processing failed.", "content_generation_failed": "Content generation failed.", "ai_processing_failed": "AI processing failed.", "blockchain_sync_failed": "Blockchain synchronization failed.", "ipfs_upload_failed": "IPFS upload failed.", "metadata_update_failed": "Metadata update failed.", "smart_contract_deployment_failed": "Smart contract deployment failed.", "oracle_data_failed": "Oracle data retrieval failed.", "cross_chain_failed": "Cross-chain operation failed.", "defi_protocol_failed": "DeFi protocol interaction failed.", "yield_farming_failed": "Yield farming operation failed.", "governance_vote_failed": "Governance vote failed.", "proposal_creation_failed": "Proposal creation failed.", "delegation_failed": "Delegation operation failed.", "reward_claim_failed": "Reward claim failed.", "vesting_failed": "Vesting operation failed.", "bridge_failed": "Bridge operation failed.", "layer2_failed": "Layer 2 operation failed.", "sidechain_failed": "Sidechain operation failed.", "rollup_failed": "Rollup operation failed.", "state_channel_failed": "State channel operation failed.", "plasma_failed": "Plasma operation failed.", "sharding_failed": "Database sharding failed.", "consensus_failed": "Consensus mechanism failed.", "validator_failed": "Validator operation failed.", "slashing_occurred": "Slashing event occurred.", "fork_detected": "Blockchain fork detected.", "reorg_detected": "Blockchain reorganization detected.", "mempool_congestion": "Network congestion detected.", "gas_price_high": "Gas prices are currently high.", "block_confirmation_timeout": "Block confirmation timeout.", "transaction_reverted": "Transaction was reverted.", "contract_paused": "Smart contract is currently paused.", "contract_deprecated": "Smart contract is deprecated.", "upgrade_required": "Contract upgrade required.", "maintenance_mode": "System is in maintenance mode.", "rate_limit_exceeded": "Rate limit exceeded. Please try again later.", "quota_exceeded": "Usage quota exceeded.", "subscription_expired": "Subscription has expired.", "feature_not_available": "Feature not available in your region.", "kyc_required": "KYC verification required.", "aml_check_failed": "AML check failed.", "compliance_violation": "Compliance violation detected.", "regulatory_restriction": "Regulatory restriction applies.", "jurisdiction_blocked": "Service not available in your jurisdiction.", "sanctions_check_failed": "Sanctions screening failed.", "risk_assessment_failed": "Risk assessment failed.", "fraud_detection_triggered": "Fraud detection system triggered.", "security_breach_detected": "Security breach detected.", "suspicious_activity": "Suspicious activity detected.", "account_locked": "Account has been locked.", "account_suspended": "Account has been suspended.", "account_terminated": "Account has been terminated.", "password_expired": "Password has expired.", "session_expired": "Session has expired.", "token_expired": "Access token has expired.", "refresh_token_invalid": "Refresh token is invalid.", "api_key_invalid": "API key is invalid.", "signature_invalid": "Digital signature is invalid.", "certificate_expired": "Certificate has expired.", "ssl_verification_failed": "SSL verification failed.", "encryption_failed": "Encryption operation failed.", "decryption_failed": "Decryption operation failed.", "hash_verification_failed": "Hash verification failed.", "merkle_proof_invalid": "<PERSON><PERSON><PERSON> proof is invalid.", "zero_knowledge_proof_failed": "Zero-knowledge proof failed.", "multi_sig_threshold_not_met": "Multi-signature threshold not met.", "timelock_not_expired": "Timelock has not expired yet.", "oracle_price_stale": "Oracle price data is stale.", "slippage_too_high": "Slippage tolerance exceeded.", "deadline_exceeded": "Transaction deadline exceeded.", "nonce_too_low": "Transaction nonce is too low.", "nonce_too_high": "Transaction nonce is too high.", "gas_limit_exceeded": "Gas limit exceeded.", "block_gas_limit_exceeded": "Block gas limit exceeded.", "base_fee_too_high": "Base fee is too high.", "priority_fee_too_low": "Priority fee is too low.", "max_fee_too_low": "Maximum fee is too low.", "eip1559_not_supported": "EIP-1559 not supported on this network.", "legacy_transaction_not_supported": "Legacy transactions not supported.", "access_list_invalid": "Access list is invalid.", "blob_transaction_failed": "Blob transaction failed.", "proto_danksharding_failed": "Proto-danksharding operation failed.", "verkle_tree_failed": "Verkle tree operation failed.", "state_expiry_failed": "State expiry operation failed.", "account_abstraction_failed": "Account abstraction failed.", "social_recovery_failed": "Social recovery failed.", "guardian_approval_failed": "Guardian approval failed.", "recovery_delay_not_met": "Recovery delay period not met.", "backup_not_found": "Backup not found.", "seed_phrase_invalid": "Seed phrase is invalid.", "private_key_invalid": "Private key is invalid.", "public_key_invalid": "Public key is invalid.", "address_invalid": "Address format is invalid.", "checksum_mismatch": "Address checksum mismatch.", "ens_resolution_failed": "ENS name resolution failed.", "reverse_ens_failed": "Reverse ENS lookup failed.", "ipfs_hash_invalid": "IPFS hash is invalid.", "content_hash_mismatch": "Content hash mismatch.", "metadata_invalid": "Metadata format is invalid.", "json_parse_error": "JSON parsing error.", "xml_parse_error": "XML parsing error.", "csv_parse_error": "CSV parsing error.", "binary_data_corrupted": "Binary data is corrupted.", "file_format_unsupported": "File format is not supported.", "file_size_too_large": "File size exceeds limit.", "file_size_too_small": "File size is too small.", "image_resolution_too_low": "Image resolution is too low.", "image_resolution_too_high": "Image resolution is too high.", "video_duration_too_long": "Video duration exceeds limit.", "video_duration_too_short": "Video duration is too short.", "audio_quality_too_low": "Audio quality is too low.", "audio_format_unsupported": "Audio format is not supported.", "codec_not_supported": "Codec is not supported.", "container_format_invalid": "Container format is invalid.", "stream_corrupted": "Media stream is corrupted.", "drm_protection_failed": "DRM protection failed.", "watermark_detection_failed": "Watermark detection failed.", "content_moderation_failed": "Content moderation failed.", "ai_model_unavailable": "AI model is unavailable.", "ai_inference_failed": "AI inference failed.", "machine_learning_error": "Machine learning error.", "neural_network_error": "Neural network error.", "training_data_insufficient": "Training data is insufficient.", "model_accuracy_too_low": "Model accuracy is too low.", "prediction_confidence_low": "Prediction confidence is low.", "feature_extraction_failed": "Feature extraction failed.", "dimensionality_reduction_failed": "Dimensionality reduction failed.", "clustering_failed": "Clustering operation failed.", "classification_failed": "Classification failed.", "regression_failed": "Regression analysis failed.", "anomaly_detection_failed": "Anomaly detection failed.", "pattern_recognition_failed": "Pattern recognition failed.", "natural_language_processing_failed": "Natural language processing failed.", "sentiment_analysis_failed": "Sentiment analysis failed.", "text_summarization_failed": "Text summarization failed.", "translation_failed": "Translation failed.", "speech_recognition_failed": "Speech recognition failed.", "speech_synthesis_failed": "Speech synthesis failed.", "computer_vision_failed": "Computer vision failed.", "object_detection_failed": "Object detection failed.", "face_recognition_failed": "Face recognition failed.", "ocr_failed": "Optical character recognition failed.", "barcode_scanning_failed": "Barcode scanning failed.", "qr_code_scanning_failed": "QR code scanning failed.", "augmented_reality_failed": "Augmented reality failed.", "virtual_reality_failed": "Virtual reality failed.", "mixed_reality_failed": "Mixed reality failed.", "3d_rendering_failed": "3D rendering failed.", "ray_tracing_failed": "Ray tracing failed.", "physics_simulation_failed": "Physics simulation failed.", "collision_detection_failed": "Collision detection failed.", "pathfinding_failed": "Pathfinding failed.", "ai_agent_failed": "AI agent failed.", "reinforcement_learning_failed": "Reinforcement learning failed.", "genetic_algorithm_failed": "Genetic algorithm failed.", "swarm_intelligence_failed": "Swarm intelligence failed.", "expert_system_failed": "Expert system failed.", "knowledge_graph_failed": "Knowledge graph failed.", "ontology_reasoning_failed": "Ontology reasoning failed.", "semantic_web_failed": "Semantic web operation failed.", "linked_data_failed": "Linked data operation failed.", "rdf_processing_failed": "RDF processing failed.", "sparql_query_failed": "SPARQL query failed.", "graph_database_failed": "Graph database operation failed.", "nosql_operation_failed": "NoSQL operation failed.", "sql_query_failed": "SQL query failed.", "database_connection_failed": "Database connection failed.", "database_timeout": "Database operation timed out.", "database_lock_timeout": "Database lock timeout.", "transaction_rollback": "Database transaction was rolled back.", "constraint_violation": "Database constraint violation.", "foreign_key_violation": "Foreign key constraint violation.", "unique_constraint_violation": "Unique constraint violation.", "check_constraint_violation": "Check constraint violation.", "not_null_violation": "Not null constraint violation.", "data_type_mismatch": "Data type mismatch.", "column_not_found": "Database column not found.", "table_not_found": "Database table not found.", "index_not_found": "Database index not found.", "view_not_found": "Database view not found.", "procedure_not_found": "Stored procedure not found.", "function_not_found": "Database function not found.", "trigger_failed": "Database trigger failed.", "backup_failed": "Database backup failed.", "restore_failed": "Database restore failed.", "migration_failed": "Database migration failed.", "replication_failed": "Database replication failed.", "partitioning_failed": "Database partitioning failed.", "indexing_failed": "Database indexing failed.", "optimization_failed": "Database optimization failed.", "vacuum_failed": "Database vacuum failed.", "analyze_failed": "Database analyze failed.", "statistics_update_failed": "Database statistics update failed.", "cache_miss": "Cache miss occurred.", "cache_invalidation_failed": "Cache invalidation failed.", "cache_eviction_failed": "Cache eviction failed.", "cache_warming_failed": "Cache warming failed.", "distributed_cache_failed": "Distributed cache operation failed.", "redis_connection_failed": "Redis connection failed.", "memcached_connection_failed": "Memcached connection failed.", "elasticsearch_failed": "Elasticsearch operation failed.", "solr_failed": "Solr operation failed.", "search_index_failed": "Search index operation failed.", "full_text_search_failed": "Full-text search failed.", "faceted_search_failed": "Faceted search failed.", "autocomplete_failed": "Autocomplete failed.", "spell_check_failed": "Spell check failed.", "synonym_expansion_failed": "Synonym expansion failed.", "stemming_failed": "Stemming operation failed.", "tokenization_failed": "Tokenization failed.", "language_detection_failed": "Language detection failed.", "encoding_detection_failed": "Encoding detection failed.", "character_encoding_error": "Character encoding error.", "unicode_normalization_failed": "Unicode normalization failed.", "regex_compilation_failed": "Regular expression compilation failed.", "regex_match_failed": "Regular expression match failed.", "xpath_evaluation_failed": "XPath evaluation failed.", "css_selector_failed": "CSS selector failed.", "dom_manipulation_failed": "DOM manipulation failed.", "html_parsing_failed": "HTML parsing failed.", "css_parsing_failed": "CSS parsing failed.", "javascript_execution_failed": "JavaScript execution failed.", "typescript_compilation_failed": "TypeScript compilation failed.", "babel_transformation_failed": "Babel transformation failed.", "webpack_build_failed": "Webpack build failed.", "rollup_build_failed": "Rollup build failed.", "vite_build_failed": "Vite build failed.", "parcel_build_failed": "Parcel build failed.", "esbuild_failed": "ESBuild failed.", "swc_compilation_failed": "SWC compilation failed.", "terser_minification_failed": "Terser minification failed.", "uglify_minification_failed": "UglifyJS minification failed.", "postcss_processing_failed": "PostCSS processing failed.", "sass_compilation_failed": "Sass compilation failed.", "less_compilation_failed": "Less compilation failed.", "stylus_compilation_failed": "Stylus compilation failed.", "autoprefixer_failed": "Autoprefixer failed.", "cssnano_optimization_failed": "CSSNano optimization failed.", "purgecss_failed": "PurgeCSS failed.", "tailwind_build_failed": "Tailwind CSS build failed.", "bootstrap_compilation_failed": "Bootstrap compilation failed.", "material_ui_theme_failed": "Material-UI theme failed.", "styled_components_failed": "Styled Components failed.", "emotion_styling_failed": "Emotion styling failed.", "chakra_ui_failed": "Chakra UI failed.", "ant_design_failed": "Ant Design failed.", "react_rendering_failed": "React rendering failed.", "vue_rendering_failed": "Vue rendering failed.", "angular_rendering_failed": "Angular rendering failed.", "svelte_compilation_failed": "Svelte compilation failed.", "solid_js_failed": "SolidJS failed.", "preact_failed": "Preact failed.", "lit_element_failed": "LitElement failed.", "stencil_compilation_failed": "Stencil compilation failed.", "web_components_failed": "Web Components failed.", "custom_elements_failed": "Custom Elements failed.", "shadow_dom_failed": "Shadow DOM failed.", "service_worker_failed": "Service Worker failed.", "web_worker_failed": "Web Worker failed.", "shared_worker_failed": "Shared Worker failed.", "workbox_failed": "Workbox failed.", "pwa_installation_failed": "PWA installation failed.", "manifest_parsing_failed": "Web App Manifest parsing failed.", "push_notification_failed": "Push notification failed.", "background_sync_failed": "Background sync failed.", "indexeddb_failed": "IndexedDB operation failed.", "websql_failed": "WebSQL operation failed.", "localstorage_failed": "LocalStorage operation failed.", "sessionstorage_failed": "SessionStorage operation failed.", "cookies_disabled": "Cookies are disabled.", "third_party_cookies_blocked": "Third-party cookies are blocked.", "cors_error": "CORS error occurred.", "csp_violation": "Content Security Policy violation.", "mixed_content_error": "Mixed content error.", "insecure_context_error": "Insecure context error.", "permissions_api_failed": "Permissions API failed.", "geolocation_failed": "Geolocation failed.", "camera_access_denied": "Camera access denied.", "microphone_access_denied": "Microphone access denied.", "screen_sharing_failed": "Screen sharing failed.", "clipboard_access_denied": "Clipboard access denied.", "fullscreen_failed": "Fullscreen request failed.", "pointer_lock_failed": "Pointer lock failed.", "device_orientation_failed": "Device orientation failed.", "device_motion_failed": "<PERSON><PERSON> motion failed.", "battery_api_failed": "Battery API failed.", "network_information_failed": "Network Information API failed.", "payment_request_failed": "Payment Request API failed.", "web_authentication_failed": "Web Authentication failed.", "credential_management_failed": "Credential Management failed.", "web_share_failed": "Web Share API failed.", "contact_picker_failed": "Contact Picker API failed.", "file_system_access_failed": "File System Access API failed.", "web_locks_failed": "Web Locks API failed.", "broadcast_channel_failed": "Broadcast Channel failed.", "message_channel_failed": "Message Channel failed.", "websocket_connection_failed": "WebSocket connection failed.", "websocket_message_failed": "WebSocket message failed.", "sse_connection_failed": "Server-Sent Events connection failed.", "webrtc_connection_failed": "WebRTC connection failed.", "peer_connection_failed": "Peer connection failed.", "data_channel_failed": "Data channel failed.", "media_stream_failed": "Media stream failed.", "media_recorder_failed": "Media recorder failed.", "audio_context_failed": "Audio context failed.", "web_audio_failed": "Web Audio API failed.", "midi_access_failed": "MIDI access failed.", "gamepad_api_failed": "Gamepad API failed.", "vibration_api_failed": "Vibration API failed.", "ambient_light_failed": "Ambient Light Sensor failed.", "proximity_sensor_failed": "Proximity sensor failed.", "accelerometer_failed": "Accelerometer failed.", "gyroscope_failed": "Gyroscope failed.", "magnetometer_failed": "Magnetometer failed.", "orientation_sensor_failed": "Orientation sensor failed.", "generic_sensor_failed": "Generic Sensor API failed.", "web_usb_failed": "WebUSB failed.", "web_bluetooth_failed": "Web Bluetooth failed.", "web_nfc_failed": "Web NFC failed.", "web_serial_failed": "Web Serial failed.", "web_hid_failed": "WebHID failed.", "web_codecs_failed": "WebCodecs failed.", "web_transport_failed": "WebTransport failed.", "web_streams_failed": "Web Streams failed.", "compression_streams_failed": "Compression Streams failed.", "transform_streams_failed": "Transform Streams failed.", "readable_streams_failed": "Readable Streams failed.", "writable_streams_failed": "Writable Streams failed.", "abort_controller_failed": "AbortController failed.", "abort_signal_failed": "AbortSignal failed.", "intersection_observer_failed": "Intersection Observer failed.", "mutation_observer_failed": "Mutation Observer failed.", "resize_observer_failed": "Resize Observer failed.", "performance_observer_failed": "Performance Observer failed.", "reporting_observer_failed": "Reporting Observer failed.", "web_vitals_failed": "Web Vitals measurement failed.", "performance_measurement_failed": "Performance measurement failed.", "memory_api_failed": "Memory API failed.", "navigation_timing_failed": "Navigation Timing failed.", "resource_timing_failed": "Resource Timing failed.", "user_timing_failed": "User Timing failed.", "paint_timing_failed": "<PERSON><PERSON> failed.", "layout_instability_failed": "Layout Instability failed.", "largest_contentful_paint_failed": "Largest Contentful Paint failed.", "first_input_delay_failed": "First Input Delay failed.", "cumulative_layout_shift_failed": "Cumulative Layout Shift failed.", "time_to_interactive_failed": "Time to Interactive failed.", "total_blocking_time_failed": "Total Blocking Time failed.", "speed_index_failed": "Speed Index failed.", "lighthouse_audit_failed": "Lighthouse audit failed.", "pagespeed_insights_failed": "PageSpeed Insights failed.", "web_page_test_failed": "WebPageTest failed.", "gtmetrix_failed": "GTmetrix failed.", "pingdom_failed": "<PERSON><PERSON> failed.", "uptime_robot_failed": "UptimeRobot failed.", "new_relic_failed": "New Relic failed.", "datadog_failed": "Datadog failed.", "sentry_failed": "<PERSON><PERSON> failed.", "bugsnag_failed": "Bugsnag failed.", "rollbar_failed": "Roll<PERSON> failed.", "honeybadger_failed": "Honeybadger failed.", "airbrake_failed": "Airbrake failed.", "raygun_failed": "<PERSON><PERSON> failed.", "logrocket_failed": "LogRocket failed.", "fullstory_failed": "FullStory failed.", "hotjar_failed": "<PERSON><PERSON> failed.", "crazy_egg_failed": "Crazy Egg failed.", "google_analytics_failed": "Google Analytics failed.", "google_tag_manager_failed": "Google Tag Manager failed.", "facebook_pixel_failed": "Facebook Pixel failed.", "twitter_pixel_failed": "Twitter Pixel failed.", "linkedin_insight_failed": "LinkedIn Insight Tag failed.", "pinterest_tag_failed": "Pinterest Tag failed.", "snapchat_pixel_failed": "Snapchat Pixel failed.", "tiktok_pixel_failed": "TikTok Pixel failed.", "reddit_pixel_failed": "Reddit Pixel failed.", "quora_pixel_failed": "Quora Pixel failed.", "bing_ads_failed": "Bing Ads failed.", "amazon_dsp_failed": "Amazon DSP failed.", "the_trade_desk_failed": "The Trade Desk failed.", "adobe_analytics_failed": "Adobe Analytics failed.", "mixpanel_failed": "Mixpanel failed.", "amplitude_failed": "Amplitude failed.", "segment_failed": "Segment failed.", "heap_analytics_failed": "Heap Analytics failed.", "kissmetrics_failed": "Kissmetrics failed.", "chartbeat_failed": "Chartbeat failed.", "parse_ly_failed": "Parse.ly failed.", "comscore_failed": "comScore failed.", "nielsen_failed": "<PERSON> failed.", "quantcast_failed": "Quantcast failed.", "alexa_failed": "<PERSON><PERSON> failed.", "similarweb_failed": "SimilarWeb failed.", "compete_failed": "Compete failed.", "statcounter_failed": "StatCounter failed.", "clicky_failed": "<PERSON><PERSON><PERSON> failed.", "woopra_failed": "<PERSON><PERSON><PERSON> failed.", "piwik_failed": "<PERSON><PERSON><PERSON> failed.", "matomo_failed": "<PERSON><PERSON> failed.", "open_web_analytics_failed": "Open Web Analytics failed.", "yandex_metrica_failed": "Yandex Metrica failed.", "baidu_analytics_failed": "Baidu Analytics failed.", "cnzz_failed": "CNZZ failed.", "51_la_failed": "51.LA failed.", "webtrends_failed": "Webtrends failed.", "coremetrics_failed": "Coremetrics failed.", "omniture_failed": "Omniture failed.", "google_optimize_failed": "Google Optimize failed.", "optimizely_failed": "Optimizely failed.", "vwo_failed": "VWO failed.", "ab_tasty_failed": "AB Tasty failed.", "unbounce_failed": "<PERSON>boun<PERSON> failed.", "leadpages_failed": "Leadpages failed.", "instapage_failed": "Instapage failed.", "clickfunnels_failed": "ClickFunnels failed.", "mailchimp_failed": "Mailchimp failed.", "constant_contact_failed": "Constant Contact failed.", "aweber_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "getresponse_failed": "GetResponse failed.", "convertkit_failed": "ConvertKit failed.", "drip_failed": "<PERSON><PERSON> failed.", "activecampaign_failed": "ActiveCampaign failed.", "hubspot_failed": "HubSpot failed.", "marketo_failed": "Marketo failed.", "pardot_failed": "<PERSON><PERSON><PERSON> failed.", "eloqua_failed": "Eloqua failed.", "salesforce_failed": "Salesforce failed.", "pipedrive_failed": "Pipedrive failed.", "zoho_failed": "<PERSON><PERSON><PERSON> failed.", "freshworks_failed": "Freshworks failed.", "intercom_failed": "Intercom failed.", "zendesk_failed": "Zendesk failed.", "freshdesk_failed": "Freshdesk failed.", "helpscout_failed": "Help Scout failed.", "drift_failed": "Drift failed.", "crisp_failed": "<PERSON><PERSON><PERSON> failed.", "tawk_to_failed": "Tawk.to failed.", "livechat_failed": "LiveChat failed.", "olark_failed": "Olark failed.", "zopim_failed": "<PERSON><PERSON><PERSON> failed.", "uservoice_failed": "UserVoice failed.", "feedback_failed": "Feedback system failed.", "survey_failed": "Survey system failed.", "typeform_failed": "Typeform failed.", "google_forms_failed": "Google Forms failed.", "surveymonkey_failed": "SurveyMonkey failed.", "qualtrics_failed": "Qualtrics failed.", "formstack_failed": "Formstack failed.", "wufoo_failed": "<PERSON><PERSON><PERSON> failed.", "jotform_failed": "JotForm failed.", "gravity_forms_failed": "Gravity Forms failed.", "ninja_forms_failed": "Ninja Forms failed.", "contact_form_7_failed": "Contact Form 7 failed.", "wpforms_failed": "WPForms failed.", "formidable_forms_failed": "Formidable Forms failed.", "caldera_forms_failed": "Caldera Forms failed.", "elementor_forms_failed": "Elementor Forms failed.", "divi_forms_failed": "Divi Forms failed.", "beaver_builder_failed": "Beaver Builder failed.", "visual_composer_failed": "Visual Composer failed.", "gutenberg_failed": "<PERSON><PERSON><PERSON> failed.", "wordpress_failed": "WordPress failed.", "drupal_failed": "<PERSON><PERSON><PERSON> failed.", "joomla_failed": "<PERSON><PERSON><PERSON> failed.", "magento_failed": "Magento failed.", "shopify_failed": "Shopify failed.", "woocommerce_failed": "WooCommerce failed.", "bigcommerce_failed": "BigCommerce failed.", "prestashop_failed": "PrestaShop failed.", "opencart_failed": "OpenCart failed.", "oscommerce_failed": "osCommerce failed.", "zen_cart_failed": "<PERSON>t failed.", "x_cart_failed": "X-Cart failed.", "cs_cart_failed": "CS-Cart failed.", "loaded_commerce_failed": "Loaded Commerce failed.", "cubecart_failed": "CubeCart failed.", "ecwid_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "square_online_failed": "Square Online failed.", "weebly_failed": "<PERSON><PERSON><PERSON> failed.", "wix_failed": "Wix failed.", "squarespace_failed": "Squarespace failed.", "webflow_failed": "Webflow failed.", "bubble_failed": "Bubble failed.", "adalo_failed": "<PERSON><PERSON> failed.", "glide_failed": "Glide failed.", "thunkable_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "appgyver_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON> failed.", "outsystems_failed": "OutSystems failed.", "mendix_failed": "<PERSON><PERSON><PERSON> failed.", "powerapps_failed": "PowerApps failed.", "appsheet_failed": "AppSheet failed.", "retool_failed": "Retool failed.", "zapier_failed": "Zapier failed.", "integromat_failed": "Integromat failed.", "make_failed": "Make failed.", "ifttt_failed": "IFTTT failed.", "microsoft_flow_failed": "Microsoft Flow failed.", "power_automate_failed": "Power Automate failed.", "azure_logic_apps_failed": "Azure Logic Apps failed.", "aws_lambda_failed": "AWS Lambda failed.", "google_cloud_functions_failed": "Google Cloud Functions failed.", "azure_functions_failed": "Azure Functions failed.", "cloudflare_workers_failed": "Cloudflare Workers failed.", "vercel_functions_failed": "Vercel Functions failed.", "netlify_functions_failed": "Netlify Functions failed.", "firebase_functions_failed": "Firebase Functions failed.", "supabase_functions_failed": "Supabase Functions failed.", "planetscale_failed": "PlanetScale failed.", "railway_failed": "Railway failed.", "render_failed": "<PERSON>der failed.", "fly_io_failed": "Fly.io failed.", "digital_ocean_failed": "DigitalOcean failed.", "linode_failed": "<PERSON><PERSON> failed.", "vultr_failed": "<PERSON><PERSON><PERSON> failed.", "hetzner_failed": "<PERSON><PERSON><PERSON> failed.", "ovh_failed": "OVH failed.", "scaleway_failed": "Scaleway failed.", "upcloud_failed": "UpCloud failed.", "aws_failed": "AWS failed.", "google_cloud_failed": "Google Cloud failed.", "azure_failed": "Microsoft Azure failed.", "ibm_cloud_failed": "IBM Cloud failed.", "oracle_cloud_failed": "Oracle Cloud failed.", "alibaba_cloud_failed": "Alibaba Cloud failed.", "tencent_cloud_failed": "Tencent Cloud failed.", "baidu_cloud_failed": "Baidu Cloud failed.", "huawei_cloud_failed": "Huawei Cloud failed.", "naver_cloud_failed": "Naver Cloud failed.", "nhn_failed": "NHN failed.", "kakao_cloud_failed": "Kakao Cloud failed.", "line_cloud_failed": "LINE Cloud failed.", "softbank_cloud_failed": "SoftBank Cloud failed.", "ntt_communications_failed": "NTT Communications failed.", "kddi_failed": "KDDI failed.", "rakuten_cloud_failed": "<PERSON><PERSON><PERSON> Cloud failed.", "gmo_cloud_failed": "GMO Cloud failed.", "sakura_internet_failed": "Sakura Internet failed.", "conoha_failed": "ConoHa failed.", "xserver_failed": "Xserver failed.", "lolipop_failed": "<PERSON><PERSON><PERSON> failed.", "heteml_failed": "he<PERSON><PERSON> failed.", "coreserver_failed": "CoreServer failed.", "value_server_failed": "Value Server failed.", "kagoya_failed": "KAGOYA failed.", "wadax_failed": "Wadax failed.", "cpi_failed": "CPI failed.", "shared_server_failed": "Shared server failed.", "vps_failed": "VPS failed.", "dedicated_server_failed": "Dedicated server failed.", "cloud_server_failed": "Cloud server failed.", "container_failed": "Container failed.", "kubernetes_failed": "Kubernetes failed.", "docker_failed": "<PERSON><PERSON> failed.", "podman_failed": "<PERSON><PERSON> failed.", "containerd_failed": "containerd failed.", "cri_o_failed": "CRI-O failed.", "rkt_failed": "rkt failed.", "lxc_failed": "LXC failed.", "lxd_failed": "LXD failed.", "systemd_nspawn_failed": "systemd-nspawn failed.", "chroot_failed": "chroot failed.", "jail_failed": "FreeBSD jail failed.", "zone_failed": "Solaris zone failed.", "wpar_failed": "AIX WPAR failed.", "ldom_failed": "Oracle LDOM failed.", "vmware_failed": "VMware failed.", "virtualbox_failed": "VirtualBox failed.", "qemu_failed": "QEMU failed.", "kvm_failed": "KVM failed.", "xen_failed": "Xen failed.", "hyper_v_failed": "Hyper-V failed.", "parallels_failed": "<PERSON><PERSON><PERSON> failed.", "vagrant_failed": "Vagrant failed.", "terraform_failed": "Terraform failed.", "ansible_failed": "Ansible failed.", "puppet_failed": "<PERSON><PERSON><PERSON> failed.", "chef_failed": "Chef failed.", "saltstack_failed": "SaltStack failed.", "fabric_failed": "<PERSON><PERSON><PERSON> failed.", "capistrano_failed": "Capistrano failed.", "deployer_failed": "Deployer failed.", "jenkins_failed": "<PERSON> failed.", "gitlab_ci_failed": "GitLab CI failed.", "github_actions_failed": "GitHub Actions failed.", "azure_devops_failed": "Azure DevOps failed.", "bitbucket_pipelines_failed": "Bitbucket Pipelines failed.", "circleci_failed": "CircleCI failed.", "travis_ci_failed": "Travis CI failed.", "appveyor_failed": "<PERSON>pp<PERSON><PERSON><PERSON> failed.", "teamcity_failed": "TeamCity failed.", "bamboo_failed": "Bamboo failed.", "octopus_deploy_failed": "Octopus Deploy failed.", "spinnaker_failed": "Spinnaker failed.", "argo_cd_failed": "Argo CD failed.", "flux_failed": "Flux failed.", "tekton_failed": "Tekton failed.", "concourse_failed": "Concourse failed.", "drone_failed": "<PERSON><PERSON> failed.", "buildkite_failed": "Buildkite failed.", "semaphore_failed": "Se<PERSON><PERSON><PERSON> failed.", "codeship_failed": "Codeship failed.", "wercker_failed": "<PERSON><PERSON><PERSON> failed.", "shippable_failed": "Shippable failed.", "vsts_failed": "VSTS failed.", "tfs_failed": "TFS failed.", "git_failed": "Git failed.", "svn_failed": "Subversion failed.", "mercurial_failed": "Mercurial failed.", "bazaar_failed": "<PERSON><PERSON><PERSON> failed.", "cvs_failed": "CVS failed.", "perforce_failed": "Perforce failed.", "clearcase_failed": "ClearCase failed.", "vss_failed": "Visual SourceSafe failed.", "tfs_version_control_failed": "TFS Version Control failed.", "plastic_scm_failed": "Plastic SCM failed.", "fossil_failed": "Fossil failed.", "darcs_failed": "<PERSON><PERSON> failed.", "monotone_failed": "Monotone failed.", "arch_failed": "GNU Arch failed.", "bitkeeper_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "rcs_failed": "RCS failed.", "sccs_failed": "SCCS failed.", "github_failed": "GitHub failed.", "gitlab_failed": "GitLab failed.", "bitbucket_failed": "Bitbucket failed.", "azure_repos_failed": "Azure Repos failed.", "codecommit_failed": "AWS CodeCommit failed.", "sourceforge_failed": "SourceForge failed.", "codeberg_failed": "<PERSON><PERSON> failed.", "gitea_failed": "<PERSON><PERSON><PERSON> failed.", "gogs_failed": "Gogs failed.", "forgejo_failed": "Forgejo failed.", "gitiles_failed": "<PERSON><PERSON><PERSON> failed.", "cgit_failed": "cgit failed.", "gitweb_failed": "GitWeb failed.", "viewvc_failed": "ViewVC failed.", "trac_failed": "Trac failed.", "redmine_failed": "Redmine failed.", "mantis_failed": "MantisBT failed.", "bugzilla_failed": "Bug<PERSON> failed.", "jira_failed": "<PERSON><PERSON> failed.", "youtrack_failed": "YouTrack failed.", "linear_failed": "Linear failed.", "asana_failed": "<PERSON><PERSON> failed.", "trello_failed": "Trello failed.", "notion_failed": "Notion failed.", "monday_failed": "Monday.com failed.", "clickup_failed": "ClickUp failed.", "airtable_failed": "Airtable failed.", "smartsheet_failed": "Smartsheet failed.", "basecamp_failed": "Basecamp failed.", "slack_failed": "Slack failed.", "discord_failed": "Discord failed.", "teams_failed": "Microsoft Teams failed.", "zoom_failed": "Zoom failed.", "google_meet_failed": "Google Meet failed.", "webex_failed": "Webex failed.", "gotomeeting_failed": "GoToMeeting failed.", "skype_failed": "Skype failed.", "telegram_failed": "Telegram failed.", "whatsapp_failed": "WhatsApp failed.", "signal_failed": "Signal failed.", "wire_failed": "Wire failed.", "element_failed": "Element failed.", "riot_failed": "Riot failed.", "matrix_failed": "Matrix failed.", "irc_failed": "IRC failed.", "xmpp_failed": "XMPP failed.", "email_failed": "Email failed.", "smtp_failed": "SMTP failed.", "imap_failed": "IMAP failed.", "pop3_failed": "POP3 failed.", "exchange_failed": "Exchange failed.", "outlook_failed": "Outlook failed.", "gmail_failed": "Gmail failed.", "yahoo_mail_failed": "Yahoo Mail failed.", "protonmail_failed": "ProtonMail failed.", "tutanota_failed": "<PERSON><PERSON><PERSON> failed.", "fastmail_failed": "FastMail failed.", "mailgun_failed": "Mailgun failed.", "sendgrid_failed": "SendGrid failed.", "ses_failed": "Amazon SES failed.", "postmark_failed": "Postmark failed.", "mandrill_failed": "Mandrill failed.", "sparkpost_failed": "SparkPost failed.", "mailjet_failed": "Mailjet failed.", "campaign_monitor_failed": "Campaign Monitor failed.", "emma_failed": "<PERSON> failed.", "vertical_response_failed": "VerticalResponse failed.", "benchmark_email_failed": "Benchmark Email failed.", "icontact_failed": "iContact failed.", "mad_mimi_failed": "<PERSON> failed.", "tinyletter_failed": "TinyLetter failed.", "substack_failed": "Substack failed.", "revue_failed": "Revue failed.", "buttondown_failed": "Buttondown failed.", "ghost_failed": "<PERSON> failed.", "medium_failed": "Medium failed.", "dev_to_failed": "<PERSON>.to failed.", "hashnode_failed": "<PERSON><PERSON><PERSON> failed.", "hackernoon_failed": "HackerNoon failed.", "reddit_failed": "Reddit failed.", "hacker_news_failed": "Hacker News failed.", "product_hunt_failed": "Product Hunt failed.", "indie_hackers_failed": "Indie Hackers failed.", "betalist_failed": "BetaList failed.", "launching_next_failed": "Launching Next failed.", "startup_grind_failed": "Startup Grind failed.", "angellist_failed": "AngelList failed.", "crunchbase_failed": "Crunchbase failed.", "pitchbook_failed": "PitchBook failed.", "cb_insights_failed": "CB Insights failed.", "dealroom_failed": "Dealroom failed.", "tracxn_failed": "Tracxn failed.", "owler_failed": "<PERSON><PERSON><PERSON> failed.", "zoominfo_failed": "ZoomInfo failed.", "apollo_failed": "Apollo failed.", "outreach_failed": "Outreach failed.", "salesloft_failed": "SalesLoft failed.", "gong_failed": "<PERSON> failed.", "chorus_failed": "Chorus failed.", "conversica_failed": "Conversica failed.", "drift_sales_failed": "Drift Sales failed.", "qualified_failed": "Qualified failed.", "chili_piper_failed": "<PERSON><PERSON> failed.", "calendly_failed": "<PERSON><PERSON><PERSON> failed.", "acuity_scheduling_failed": "Acuity Scheduling failed.", "bookings_failed": "Microsoft Bookings failed.", "doodle_failed": "<PERSON><PERSON> failed.", "when2meet_failed": "When2meet failed.", "findtime_failed": "FindTime failed.", "x_ai_failed": "x.ai failed.", "clara_failed": "<PERSON> failed.", "julie_desk_failed": "<PERSON> failed.", "reclaim_ai_failed": "Reclaim.ai failed.", "clockify_failed": "Clockify failed.", "toggl_failed": "<PERSON><PERSON><PERSON> failed.", "harvest_failed": "Harvest failed.", "freshbooks_failed": "FreshBooks failed.", "quickbooks_failed": "QuickBooks failed.", "xero_failed": "Xero failed.", "wave_failed": "Wave failed.", "zoho_books_failed": "Zoho Books failed.", "sage_failed": "<PERSON> failed.", "netsuite_failed": "NetSuite failed.", "sap_failed": "SAP failed.", "oracle_erp_failed": "Oracle ERP failed.", "microsoft_dynamics_failed": "Microsoft Dynamics failed.", "workday_failed": "Workday failed.", "successfactors_failed": "SuccessFactors failed.", "bamboohr_failed": "BambooHR failed.", "namely_failed": "Namely failed.", "zenefits_failed": "Zenefits failed.", "gusto_failed": "<PERSON><PERSON> failed.", "adp_failed": "ADP failed.", "paychex_failed": "Paychex failed.", "paylocity_failed": "Paylocity failed.", "paycom_failed": "Paycom failed.", "ultimate_software_failed": "Ultimate Software failed.", "ceridian_failed": "Cerid<PERSON> failed.", "kronos_failed": "Kronos failed.", "deputy_failed": "Deputy failed.", "when_i_work_failed": "When I Work failed.", "homebase_failed": "Homebase failed.", "humanity_failed": "Humanity failed.", "shiftboard_failed": "Shiftboard failed.", "findmyshift_failed": "FindMyShift failed.", "planday_failed": "Planday failed.", "rotacloud_failed": "RotaCloud failed.", "zipschedules_failed": "ZipSchedules failed.", "hotschedules_failed": "HotSchedules failed.", "restaurant365_failed": "Restaurant365 failed.", "toast_failed": "Toast failed.", "square_failed": "Square failed.", "clover_failed": "C<PERSON> failed.", "lightspeed_failed": "Lightspeed failed.", "shopkeep_failed": "ShopKeep failed.", "vend_failed": "V<PERSON> failed.", "loyverse_failed": "Loyverse failed.", "epos_now_failed": "Epos Now failed.", "revel_failed": "Revel failed.", "touchbistro_failed": "TouchBistro failed.", "upserve_failed": "Upserve failed.", "breadcrumb_failed": "Breadcrumb failed.", "talech_failed": "<PERSON><PERSON> failed.", "erply_failed": "ERPLY failed.", "cin7_failed": "Cin7 failed.", "tradegecko_failed": "TradeGecko failed.", "unleashed_failed": "Unleashed failed.", "fishbowl_failed": "Fishbowl failed.", "inflow_failed": "inFlow failed.", "sortly_failed": "Sortly failed.", "stockpile_failed": "Stockpile failed.", "ordoro_failed": "Ordoro failed.", "skubana_failed": "S<PERSON>bana failed.", "sellbrite_failed": "Sellbrite failed.", "channelape_failed": "ChannelApe failed.", "linnworks_failed": "Linnworks failed.", "brightpearl_failed": "<PERSON><PERSON><PERSON> failed.", "stitch_labs_failed": "Stitch Labs failed.", "extensiv_failed": "Extensiv failed.", "shipstation_failed": "ShipStation failed.", "shipbob_failed": "ShipBob failed.", "fulfillment_by_amazon_failed": "Fulfillment by Amazon failed.", "red_stag_fulfillment_failed": "Red Stag Fulfillment failed.", "whiplash_failed": "Whiplash failed.", "shipwire_failed": "Shipwire failed.", "rakuten_super_logistics_failed": "Rakuten Super Logistics failed.", "deliverr_failed": "Deliverr failed.", "flexport_failed": "Flexport failed.", "freightos_failed": "Freightos failed.", "convoy_failed": "Convoy failed.", "uber_freight_failed": "<PERSON><PERSON> Freight failed.", "transfix_failed": "Transfix failed.", "loadsmart_failed": "Loadsmart failed.", "project44_failed": "project44 failed.", "fourkites_failed": "FourKites failed.", "macropoint_failed": "MacroPoint failed.", "trucker_path_failed": "Trucker Path failed.", "keeptruckin_failed": "KeepTruckin failed.", "samsara_failed": "<PERSON><PERSON><PERSON> failed.", "geotab_failed": "Geotab failed.", "verizon_connect_failed": "Verizon Connect failed.", "fleet_complete_failed": "Fleet Complete failed.", "teletrac_navman_failed": "Teletrac Navman failed.", "omnitracs_failed": "Omnitracs failed.", "trimble_failed": "Trimble failed.", "spireon_failed": "<PERSON><PERSON><PERSON> failed.", "calamp_failed": "CalAmp failed.", "mix_telematics_failed": "MiX Telematics failed.", "webfleet_failed": "Webfleet failed.", "masternaut_failed": "Masternaut failed.", "microlise_failed": "Microlise failed.", "quartix_failed": "Quartix failed.", "ram_tracking_failed": "RAM Tracking failed.", "crystal_ball_failed": "Crystal Ball failed.", "bigchange_failed": "BigChange failed.", "paragon_failed": "Paragon failed.", "descartes_failed": "<PERSON><PERSON><PERSON> failed.", "manhattan_associates_failed": "Manhattan Associates failed.", "jda_failed": "JDA failed.", "blue_yonder_failed": "<PERSON>nder failed.", "oracle_wms_failed": "Oracle WMS failed.", "sap_ewm_failed": "SAP EWM failed.", "infor_wms_failed": "Infor WMS failed.", "highjump_failed": "HighJump failed.", "korber_failed": "<PERSON><PERSON><PERSON> failed.", "tecsys_failed": "<PERSON><PERSON><PERSON> failed.", "generix_failed": "Generix failed.", "logfire_failed": "Logfire failed.", "snapfulfil_failed": "SnapFulfil failed.", "deposco_failed": "<PERSON><PERSON><PERSON> failed.", "3pl_central_failed": "3PL Central failed.", "extensiv_3pl_warehouse_manager_failed": "Extensiv 3PL Warehouse Manager failed.", "fishbowl_warehouse_failed": "Fishbowl Warehouse failed.", "logiwa_failed": "Logiwa failed.", "warehouse_edge_failed": "Warehouse Edge failed.", "accellos_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "made4net_failed": "Made4net failed.", "softeon_failed": "Softeon failed.", "epicor_failed": "<PERSON><PERSON> failed.", "ifs_failed": "IFS failed.", "syspro_failed": "SYSPRO failed.", "abas_failed": "abas failed.", "exact_failed": "Exact failed.", "unit4_failed": "Unit4 failed.", "deltek_failed": "Deltek failed.", "acumatica_failed": "Acumatica failed.", "priority_failed": "Priority failed.", "iqms_failed": "IQMS failed.", "plex_failed": "Plex failed.", "global_shop_solutions_failed": "Global Shop Solutions failed.", "e2_shop_system_failed": "E2 Shop System failed.", "shoptech_failed": "Shoptech failed.", "prodsmart_failed": "ProdSmart failed.", "mestec_failed": "MESTEC failed.", "forcam_failed": "FORCAM failed.", "tulip_failed": "<PERSON><PERSON> failed.", "sight_machine_failed": "Sight Machine failed.", "uptake_failed": "Uptake failed.", "predix_failed": "Predix failed.", "mindsphere_failed": "MindSphere failed.", "azure_iot_failed": "Azure IoT failed.", "aws_iot_failed": "AWS IoT failed.", "google_cloud_iot_failed": "Google Cloud IoT failed.", "ibm_watson_iot_failed": "IBM Watson IoT failed.", "cisco_iot_failed": "Cisco IoT failed.", "intel_iot_failed": "Intel IoT failed.", "qualcomm_iot_failed": "Qualcomm IoT failed.", "arm_iot_failed": "ARM IoT failed.", "nvidia_iot_failed": "NVIDIA IoT failed.", "raspberry_pi_failed": "Raspberry Pi failed.", "arduino_failed": "<PERSON><PERSON><PERSON><PERSON> failed.", "esp32_failed": "ESP32 failed.", "esp8266_failed": "ESP8266 failed.", "particle_failed": "Particle failed.", "adafruit_failed": "Adafruit failed.", "sparkfun_failed": "SparkFun failed.", "seeed_studio_failed": "Seeed Studio failed.", "dfrobot_failed": "DFRobot failed.", "grove_failed": "Grove failed.", "micro_bit_failed": "micro:bit failed.", "calliope_mini_failed": "Calliope mini failed.", "makecode_failed": "MakeCode failed.", "scratch_failed": "<PERSON><PERSON><PERSON> failed.", "blockly_failed": "<PERSON><PERSON> failed.", "app_inventor_failed": "App Inventor failed.", "mit_app_inventor_failed": "MIT App Inventor failed.", "kodular_failed": "<PERSON><PERSON><PERSON> failed.", "thunkable_x_failed": "Thunkable X failed.", "applab_failed": "AppLab failed.", "gamelab_failed": "GameLab failed.", "weblab_failed": "WebLab failed.", "code_org_failed": "Code.org failed.", "codecademy_failed": "Codecademy failed.", "freecodecamp_failed": "freeCodeCamp failed.", "khan_academy_failed": "Khan Academy failed.", "coursera_failed": "<PERSON><PERSON> failed.", "edx_failed": "edX failed.", "udacity_failed": "Udacity failed.", "udemy_failed": "U<PERSON><PERSON> failed.", "pluralsight_failed": "Pluralsight failed.", "lynda_failed": "<PERSON><PERSON><PERSON> failed.", "linkedin_learning_failed": "LinkedIn Learning failed.", "skillshare_failed": "Skillshare failed.", "masterclass_failed": "MasterClass failed.", "brilliant_failed": "Brilliant failed.", "datacamp_failed": "DataCamp failed.", "kaggle_learn_failed": "<PERSON><PERSON> failed.", "fast_ai_failed": "Fast.ai failed.", "deeplearning_ai_failed": "DeepLearning.AI failed.", "andrew_ng_failed": "<PERSON> failed.", "stanford_cs229_failed": "Stanford CS229 failed.", "mit_6_034_failed": "MIT 6.034 failed.", "berkeley_cs188_failed": "Berkeley CS188 failed.", "cmu_10_601_failed": "CMU 10-601 failed.", "georgia_tech_cs7641_failed": "Georgia Tech CS7641 failed.", "university_of_washington_cse_446_failed": "University of Washington CSE 446 failed.", "caltech_cs156_failed": "Caltech CS156 failed.", "cornell_cs4780_failed": "Cornell CS4780 failed.", "harvard_cs109_failed": "Harvard CS109 failed.", "yale_cpsc_340_failed": "Yale CPSC 340 failed.", "princeton_cos_324_failed": "Princeton COS 324 failed.", "columbia_coms_4771_failed": "Columbia COMS 4771 failed.", "nyu_ds_ga_1003_failed": "NYU DS-GA 1003 failed.", "usc_csci_567_failed": "USC CSCI 567 failed.", "ucla_cs_260_failed": "UCLA CS 260 failed.", "ucsd_cse_250b_failed": "UCSD CSE 250B failed.", "uci_cs_178_failed": "UCI CS 178 failed.", "uc_davis_ecs_171_failed": "UC Davis ECS 171 failed.", "uc_santa_barbara_cs_165b_failed": "UC Santa Barbara CS 165B failed.", "university_of_michigan_eecs_445_failed": "University of Michigan EECS 445 failed.", "university_of_illinois_cs_446_failed": "University of Illinois CS 446 failed.", "university_of_wisconsin_cs_540_failed": "University of Wisconsin CS 540 failed.", "university_of_texas_cs_391l_failed": "University of Texas CS 391L failed.", "university_of_toronto_csc_411_failed": "University of Toronto CSC 411 failed.", "university_of_british_columbia_cpsc_340_failed": "University of British Columbia CPSC 340 failed.", "mcgill_comp_551_failed": "McGill COMP 551 failed.", "university_of_waterloo_cs_480_failed": "University of Waterloo CS 480 failed.", "university_of_alberta_cmput_466_failed": "University of Alberta CMPUT 466 failed.", "simon_fraser_university_cmpt_726_failed": "Simon <PERSON> University CMPT 726 failed.", "university_of_montreal_ift_6390_failed": "University of Montreal IFT 6390 failed.", "university_of_ottawa_csi_5387_failed": "University of Ottawa CSI 5387 failed.", "carleton_university_comp_4107_failed": "Carleton University COMP 4107 failed.", "queens_university_cisc_352_failed": "Queen's University CISC 352 failed.", "university_of_western_ontario_cs_4442_failed": "University of Western Ontario CS 4442 failed.", "mcmaster_university_cs_4ml3_failed": "McMaster University CS 4ML3 failed.", "york_university_eecs_4404_failed": "York University EECS 4404 failed.", "ryerson_university_cps_721_failed": "Ryerson University CPS 721 failed.", "concordia_university_comp_474_failed": "Concordia University COMP 474 failed.", "university_of_calgary_cpsc_526_failed": "University of Calgary CPSC 526 failed.", "university_of_saskatchewan_cmpt_317_failed": "University of Saskatchewan CMPT 317 failed.", "university_of_manitoba_comp_4190_failed": "University of Manitoba COMP 4190 failed.", "memorial_university_comp_6912_failed": "Memorial University COMP 6912 failed.", "university_of_new_brunswick_cs_6735_failed": "University of New Brunswick CS 6735 failed.", "dalhousie_university_csci_6509_failed": "Dalhousie University CSCI 6509 failed.", "acadia_university_comp_4983_failed": "Acadia University COMP 4983 failed.", "cape_breton_university_csci_4117_failed": "Cape Breton University CSCI 4117 failed.", "st_francis_xavier_university_csci_315_failed": "St<PERSON> <PERSON> Xavier University CSCI 315 failed.", "mount_allison_university_comp_3721_failed": "Mount Allison University COMP 3721 failed.", "university_of_prince_edward_island_cs_4910_failed": "University of Prince Edward Island CS 4910 failed.", "oxford_university_failed": "Oxford University failed.", "cambridge_university_failed": "Cambridge University failed.", "imperial_college_london_failed": "Imperial College London failed.", "university_college_london_failed": "University College London failed.", "kings_college_london_failed": "King's College London failed.", "london_school_of_economics_failed": "London School of Economics failed.", "university_of_edinburgh_failed": "University of Edinburgh failed.", "university_of_glasgow_failed": "University of Glasgow failed.", "university_of_st_andrews_failed": "University of St Andrews failed.", "university_of_warwick_failed": "University of Warwick failed.", "university_of_bristol_failed": "University of Bristol failed.", "university_of_manchester_failed": "University of Manchester failed.", "university_of_birmingham_failed": "University of Birmingham failed.", "university_of_leeds_failed": "University of Leeds failed.", "university_of_sheffield_failed": "University of Sheffield failed.", "university_of_nottingham_failed": "University of Nottingham failed.", "university_of_southampton_failed": "University of Southampton failed.", "university_of_york_failed": "University of York failed.", "university_of_exeter_failed": "University of Exeter failed.", "university_of_bath_failed": "University of Bath failed.", "university_of_surrey_failed": "University of Surrey failed.", "university_of_sussex_failed": "University of Sussex failed.", "university_of_east_anglia_failed": "University of East Anglia failed.", "university_of_kent_failed": "University of Kent failed.", "university_of_essex_failed": "University of Essex failed.", "university_of_reading_failed": "University of Reading failed.", "university_of_hertfordshire_failed": "University of Hertfordshire failed.", "brunel_university_failed": "Brunel University failed.", "city_university_of_london_failed": "City University of London failed.", "goldsmiths_university_of_london_failed": "Goldsmiths University of London failed.", "queen_mary_university_of_london_failed": "Queen Mary University of London failed.", "royal_holloway_university_of_london_failed": "Royal Holloway University of London failed.", "birkbeck_university_of_london_failed": "Birkbeck University of London failed.", "soas_university_of_london_failed": "SOAS University of London failed.", "london_business_school_failed": "London Business School failed.", "cranfield_university_failed": "Cranfield University failed.", "open_university_failed": "Open University failed.", "university_of_buckingham_failed": "University of Buckingham failed.", "university_of_chichester_failed": "University of Chichester failed.", "university_of_winchester_failed": "University of Winchester failed.", "university_of_portsmouth_failed": "University of Portsmouth failed.", "university_of_plymouth_failed": "University of Plymouth failed.", "university_of_the_west_of_england_failed": "University of the West of England failed.", "university_of_gloucestershire_failed": "University of Gloucestershire failed.", "university_of_worcester_failed": "University of Worcester failed.", "university_of_derby_failed": "University of Derby failed.", "university_of_lincoln_failed": "University of Lincoln failed.", "university_of_hull_failed": "University of Hull failed.", "university_of_bradford_failed": "University of Bradford failed.", "university_of_huddersfield_failed": "University of Huddersfield failed.", "university_of_salford_failed": "University of Salford failed.", "university_of_bolton_failed": "University of Bolton failed.", "university_of_chester_failed": "University of Chester failed.", "university_of_cumbria_failed": "University of Cumbria failed.", "university_of_central_lancashire_failed": "University of Central Lancashire failed.", "lancaster_university_failed": "Lancaster University failed.", "university_of_liverpool_failed": "University of Liverpool failed.", "liverpool_john_moores_university_failed": "Liverpool John Moores University failed.", "liverpool_hope_university_failed": "Liverpool Hope University failed.", "edge_hill_university_failed": "Edge Hill University failed.", "bangor_university_failed": "Bangor University failed.", "cardiff_university_failed": "Cardiff University failed.", "swansea_university_failed": "Swansea University failed.", "aberystwyth_university_failed": "Aberystwyth University failed.", "university_of_south_wales_failed": "University of South Wales failed.", "cardiff_metropolitan_university_failed": "Cardiff Metropolitan University failed.", "university_of_wales_trinity_saint_david_failed": "University of Wales Trinity Saint David failed.", "wrexham_glyndwr_university_failed": "Wrexham Glyndwr University failed.", "university_of_stirling_failed": "University of Stirling failed.", "heriot_watt_university_failed": "Heriot-Watt University failed.", "university_of_strathclyde_failed": "University of Strathclyde failed.", "glasgow_caledonian_university_failed": "Glasgow Caledonian University failed.", "university_of_the_west_of_scotland_failed": "University of the West of Scotland failed.", "university_of_abertay_dundee_failed": "University of Abertay Dundee failed.", "university_of_dundee_failed": "University of Dundee failed.", "robert_gordon_university_failed": "Robert <PERSON> failed.", "university_of_aberdeen_failed": "University of Aberdeen failed.", "university_of_the_highlands_and_islands_failed": "University of the Highlands and Islands failed.", "queen_margaret_university_failed": "Queen Margaret University failed.", "edinburgh_napier_university_failed": "Edinburgh Napier University failed.", "university_of_ulster_failed": "University of Ulster failed.", "queens_university_belfast_failed": "Queen's University Belfast failed.", "stranmillis_university_college_failed": "Stranmillis University College failed.", "st_marys_university_college_failed": "St Mary's University College failed."}}