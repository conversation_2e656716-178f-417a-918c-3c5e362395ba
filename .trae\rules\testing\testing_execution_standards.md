---
trigger: always_on
---

# ESTRATIX Testing Execution Standards - High Momentum TDD

## 1. Core Testing Philosophy

### 1.1. High-Velocity Test-Driven Development

**ESTRATIX operates on RAPID, CONTINUOUS testing cycles:**

- **Test-First Approach**: Write tests before implementation
- **UV-Powered Testing**: All test execution uses `uv run pytest`
- **Continuous Validation**: Tests run on every code change
- **Fast Feedback Loops**: Test results in < 30 seconds
- **Quality Gates**: No code ships without passing tests

### 1.2. Testing Velocity Metrics

```bash
# Test execution speed tracking
uv run pytest --durations=10 --tb=short

# Test coverage velocity
uv run pytest --cov=src --cov-report=term-missing

# Test reliability metrics
uv run python scripts/test_reliability.py --period daily
```

## 2. UV-Powered Testing Workflow

### 2.1. Rapid Test Setup

```bash
# Initialize testing environment (< 10 seconds)
uv add --dev pytest pytest-cov pytest-mock pytest-asyncio
uv add --dev pytest-xdist pytest-benchmark pytest-html
uv add --dev factory-boy faker hypothesis

# Configure pytest
cat > pytest.ini << EOF
[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    fast: Fast running tests
    critical: Critical path tests
EOF
```

### 2.2. Daily Testing Cycle

```bash
# Morning test validation (< 30 seconds)
uv run pytest -m "unit and fast" --maxfail=3
uv run pytest -m "critical" --maxfail=1

# Development testing loop
uv run pytest --lf --tb=short              # Last failed tests
uv run pytest tests/test_current.py -v     # Current development
uv run pytest --cov=src/module --cov-report=term

# Pre-commit testing (< 60 seconds)
uv run pytest -m "unit" --cov=src --cov-fail-under=80
uv run pytest -m "integration" --maxfail=5
```

### 2.3. Continuous Integration Testing

```bash
# CI test pipeline
uv run pytest --cov=src --cov-report=xml --cov-fail-under=80
uv run pytest -m "integration" --junitxml=integration-results.xml
uv run pytest -m "e2e" --html=e2e-report.html --self-contained-html

# Performance testing
uv run pytest tests/benchmarks/ --benchmark-json=benchmark.json

# Security testing
uv run pytest tests/security/ --tb=short
```

## 3. High-Performance Testing Patterns

### 3.1. Parallel Test Execution

```bash
# Multi-core test execution
uv run pytest -n auto --dist=worksteal

# Distributed testing across environments
uv run pytest --dist=each --tx=popen//python=python3.11

# Load-balanced test execution
uv run pytest -n 4 --dist=loadscope
```

### 3.2. Smart Test Selection

```bash
# Test only changed code
uv run pytest --testmon

# Test based on git changes
uv run pytest --picked=first

# Test critical paths first
uv run pytest -m "critical" --maxfail=1 && uv run pytest -m "unit"
```

### 3.3. Fast Feedback Testing

```bash
# Watch mode for instant feedback
uv run ptw --runner "pytest -x -vs"

# Continuous testing with coverage
uv run ptw --runner "pytest --cov=src --cov-report=term-missing"

# Real-time test results
uv run pytest --live-log --log-cli-level=INFO
```

## 4. Agent Testing Framework

### 4.1. Agent Unit Testing

```bash
# Test agent initialization
uv run pytest tests/agents/test_agent_init.py -v

# Test agent tools
uv run pytest tests/agents/test_agent_tools.py --cov=src/agents

# Test agent workflows
uv run pytest tests/agents/test_workflows.py -m "unit"
```

### 4.2. Agent Integration Testing

```bash
# Test agent communication
uv run pytest tests/agents/test_communication.py -m "integration"

# Test multi-agent coordination
uv run pytest tests/agents/test_coordination.py --timeout=300

# Test agent-tool integration
uv run pytest tests/agents/test_tool_integration.py -v
```

### 4.3. Agent Performance Testing

```bash
# Agent response time testing
uv run pytest tests/agents/test_performance.py --benchmark-only

# Agent memory usage testing
uv run pytest tests/agents/test_memory.py --memray

# Agent scalability testing
uv run pytest tests/agents/test_scalability.py --stress
```

## 5. Tool Testing Standards

### 5.1. Tool Validation Testing

```bash
# Test tool interfaces
uv run pytest tests/tools/test_interfaces.py --strict

# Test tool functionality
uv run pytest tests/tools/test_functionality.py --cov=src/tools

# Test tool error handling
uv run pytest tests/tools/test_error_handling.py -v
```

### 5.2. Tool Integration Testing

```bash
# Test tool-agent integration
uv run pytest tests/tools/test_agent_integration.py -m "integration"

# Test tool chaining
uv run pytest tests/tools/test_chaining.py --timeout=120

# Test tool dependencies
uv run pytest tests/tools/test_dependencies.py -v
```

### 5.3. Tool Performance Testing

```bash
# Tool execution speed
uv run pytest tests/tools/test_speed.py --benchmark-compare

# Tool resource usage
uv run pytest tests/tools/test_resources.py --profile

# Tool concurrency
uv run pytest tests/tools/test_concurrency.py --asyncio-mode=auto
```

## 6. Data Testing Framework

### 6.1. Data Validation Testing

```bash
# Test data schemas
uv run pytest tests/data/test_schemas.py --strict

# Test data transformations
uv run pytest tests/data/test_transformations.py --cov=src/data

# Test data quality
uv run pytest tests/data/test_quality.py -v
```

### 6.2. Database Testing

```bash
# Test database operations
uv run pytest tests/db/test_operations.py --db-url=sqlite:///:memory:

# Test database migrations
uv run pytest tests/db/test_migrations.py --migrations

# Test database performance
uv run pytest tests/db/test_performance.py --benchmark-only
```

### 6.3. Vector Database Testing

```bash
# Test vector operations
uv run pytest tests/vector/test_operations.py --vector-db=test

# Test embedding generation
uv run pytest tests/vector/test_embeddings.py --timeout=300

# Test similarity search
uv run pytest tests/vector/test_search.py --benchmark-only
```

## 7. API Testing Standards

### 7.1. REST API Testing

```bash
# Test API endpoints
uv run pytest tests/api/test_endpoints.py --api-url=http://localhost:8000

# Test API authentication
uv run pytest tests/api/test_auth.py --auth-token=test

# Test API rate limiting
uv run pytest tests/api/test_rate_limiting.py --stress
```

### 7.2. GraphQL API Testing

```bash
# Test GraphQL queries
uv run pytest tests/graphql/test_queries.py --graphql-url=http://localhost:8000/graphql

# Test GraphQL mutations
uv run pytest tests/graphql/test_mutations.py -v

# Test GraphQL subscriptions
uv run pytest tests/graphql/test_subscriptions.py --asyncio-mode=auto
```

### 7.3. WebSocket Testing

```bash
# Test WebSocket connections
uv run pytest tests/websocket/test_connections.py --ws-url=ws://localhost:8000/ws

# Test real-time messaging
uv run pytest tests/websocket/test_messaging.py --timeout=60

# Test WebSocket performance
uv run pytest tests/websocket/test_performance.py --benchmark-only
```

## 8. Security Testing Framework

### 8.1. Authentication Testing

```bash
# Test authentication flows
uv run pytest tests/security/test_auth.py --security

# Test authorization checks
uv run pytest tests/security/test_authz.py --strict

# Test session management
uv run pytest tests/security/test_sessions.py -v
```

### 8.2. Input Validation Testing

```bash
# Test input sanitization
uv run pytest tests/security/test_sanitization.py --fuzz

# Test SQL injection prevention
uv run pytest tests/security/test_sql_injection.py --security

# Test XSS prevention
uv run pytest tests/security/test_xss.py --web-security
```

### 8.3. Vulnerability Testing

```bash
# Test for known vulnerabilities
uv run pytest tests/security/test_vulnerabilities.py --cve-check

# Test dependency security
uv run pytest tests/security/test_dependencies.py --audit

# Test secrets exposure
uv run pytest tests/security/test_secrets.py --secrets-scan
```

## 9. Performance Testing Standards

### 9.1. Load Testing

```bash
# Test application load
uv run pytest tests/performance/test_load.py --load-users=100

# Test concurrent operations
uv run pytest tests/performance/test_concurrency.py --concurrent=50

# Test throughput
uv run pytest tests/performance/test_throughput.py --benchmark-only
```

### 9.2. Stress Testing

```bash
# Test system limits
uv run pytest tests/performance/test_stress.py --stress-level=high

# Test memory limits
uv run pytest tests/performance/test_memory_stress.py --memory-limit=1GB

# Test CPU limits
uv run pytest tests/performance/test_cpu_stress.py --cpu-cores=4
```

### 9.3. Endurance Testing

```bash
# Test long-running operations
uv run pytest tests/performance/test_endurance.py --duration=3600

# Test memory leaks
uv run pytest tests/performance/test_memory_leaks.py --memray

# Test resource cleanup
uv run pytest tests/performance/test_cleanup.py --gc-check
```

## 10. Test Data Management

### 10.1. Test Data Generation

```bash
# Generate test data
uv run python scripts/generate_test_data.py --size=1000

# Create test fixtures
uv run pytest tests/fixtures/create_fixtures.py --output=tests/data/

# Mock external services
uv run pytest tests/mocks/setup_mocks.py --services=all
```

### 10.2. Test Data Cleanup

```bash
# Clean test databases
uv run python scripts/clean_test_db.py --confirm

# Reset test environment
uv run pytest tests/setup/reset_environment.py --force

# Clear test caches
uv run python scripts/clear_test_cache.py --all
```

### 10.3. Test Data Validation

```bash
# Validate test data integrity
uv run pytest tests/data/test_integrity.py --strict

# Check test data consistency
uv run pytest tests/data/test_consistency.py -v

# Verify test data coverage
uv run pytest tests/data/test_coverage.py --coverage-report
```

## 11. Test Reporting & Analytics

### 11.1. Test Result Reporting

```bash
# Generate HTML test report
uv run pytest --html=reports/test_report.html --self-contained-html

# Generate JUnit XML report
uv run pytest --junitxml=reports/junit.xml

# Generate coverage report
uv run pytest --cov=src --cov-report=html:reports/coverage
```

### 11.2. Test Analytics

```bash
# Analyze test trends
uv run python scripts/test_analytics.py --period=30days

# Test performance trends
uv run python scripts/test_performance_trends.py --benchmark-history

# Test reliability analysis
uv run python scripts/test_reliability.py --flaky-detection
```

### 11.3. Test Metrics Dashboard

```bash
# Generate test metrics dashboard
uv run python scripts/test_dashboard.py --output=html

# Real-time test monitoring
uv run python scripts/test_monitor.py --realtime

# Test quality metrics
uv run python scripts/test_quality_metrics.py --export=json
```

## 12. Test Environment Management

### 12.1. Environment Setup

```bash
# Setup test environment
uv run python scripts/setup_test_env.py --env=local

# Configure test databases
uv run python scripts/setup_test_db.py --db=postgresql

# Initialize test services
uv run python scripts/init_test_services.py --services=redis,elasticsearch
```

### 12.2. Environment Isolation

```bash
# Create isolated test environment
uv run python scripts/create_test_isolation.py --container=docker

# Setup test networking
uv run python scripts/setup_test_network.py --isolated

# Configure test security
uv run python scripts/setup_test_security.py --strict
```

### 12.3. Environment Cleanup

```bash
# Cleanup test environment
uv run python scripts/cleanup_test_env.py --force

# Remove test containers
uv run python scripts/remove_test_containers.py --all

# Reset test configuration
uv run python scripts/reset_test_config.py --default
```

## 13. Test Automation & CI/CD

### 13.1. Automated Test Execution

```bash
# Pre-commit test automation
uv run python scripts/pre_commit_tests.py --fast

# Post-commit test automation
uv run python scripts/post_commit_tests.py --full

# Scheduled test automation
uv run python scripts/scheduled_tests.py --cron="0 2 * * *"
```

### 13.2. Test Pipeline Integration

```bash
# GitHub Actions integration
uv run python scripts/github_actions_tests.py --workflow=ci

# GitLab CI integration
uv run python scripts/gitlab_ci_tests.py --pipeline=test

# Jenkins integration
uv run python scripts/jenkins_tests.py --job=test-pipeline
```

### 13.3. Test Deployment Validation

```bash
# Validate deployment tests
uv run pytest tests/deployment/test_validation.py --env=staging

# Smoke tests for production
uv run pytest tests/smoke/ --env=production --critical-only

# Health check tests
uv run pytest tests/health/ --timeout=30 --maxfail=1
```

## 14. Test Quality Assurance

### 14.1. Test Code Quality

```bash
# Lint test code
uv run ruff check tests/ --fix

# Format test code
uv run black tests/

# Type check test code
uv run mypy tests/ --strict
```

### 14.2. Test Coverage Analysis

```bash
# Detailed coverage analysis
uv run pytest --cov=src --cov-report=term-missing --cov-branch

# Coverage diff analysis
uv run coverage diff --compare-branch=main

# Coverage trend analysis
uv run python scripts/coverage_trends.py --period=30days
```

### 14.3. Test Maintenance

```bash
# Identify flaky tests
uv run python scripts/identify_flaky_tests.py --runs=100

# Update test dependencies
uv run python scripts/update_test_deps.py --check-security

# Refactor test code
uv run python scripts/refactor_tests.py --auto-fix
```

## 15. Emergency Testing Procedures

### 15.1. Critical Path Testing

```bash
# Emergency critical path validation
uv run pytest -m "critical" --maxfail=1 --tb=short

# Production hotfix testing
uv run pytest tests/hotfix/ --env=production-like --strict

# Rollback validation testing
uv run pytest tests/rollback/ --version=previous --fast
```

### 15.2. Incident Response Testing

```bash
# Test incident response procedures
uv run pytest tests/incident/ --scenario=database-failure

# Test disaster recovery
uv run pytest tests/disaster/ --recovery-time-objective=1hour

# Test backup restoration
uv run pytest tests/backup/ --restore-point=latest
```

### 15.3. Security Incident Testing

```bash
# Test security incident response
uv run pytest tests/security/incident/ --severity=critical

# Test breach containment
uv run pytest tests/security/containment/ --isolation-mode=strict

# Test forensic procedures
uv run pytest tests/security/forensics/ --evidence-preservation
```

## 16. Testing Best Practices

### 16.1. Test Design Principles

- **FAST**: Tests should run in < 30 seconds
- **INDEPENDENT**: Tests should not depend on each other
- **REPEATABLE**: Tests should produce consistent results
- **SELF-VALIDATING**: Tests should have clear pass/fail criteria
- **TIMELY**: Tests should be written before or with the code

### 16.2. Test Organization

```
tests/
├── unit/           # Fast, isolated unit tests
├── integration/    # Component integration tests
├── e2e/           # End-to-end system tests
├── performance/   # Performance and load tests
├── security/      # Security and vulnerability tests
├── fixtures/      # Test data and fixtures
├── mocks/         # Mock objects and services
└── utils/         # Test utilities and helpers
```

### 16.3. Test Naming Conventions

```python
# Test file naming
test_[module_name].py
test_[feature_name].py
test_[component_name]_integration.py

# Test function naming
def test_[action]_[expected_result]():
def test_[action]_when_[condition]_then_[expected_result]():
def test_[action]_should_[expected_behavior]():
```

## 17. Testing Metrics & KPIs

### 17.1. Test Quality Metrics

- **Test Coverage**: > 80% line coverage, > 70% branch coverage
- **Test Execution Time**: < 30 seconds for unit tests, < 5 minutes for full suite
- **Test Reliability**: < 1% flaky test rate
- **Test Maintenance**: < 10% test code to production code ratio

### 17.2. Testing Velocity Metrics

```bash
# Track testing velocity
uv run python scripts/test_velocity_metrics.py --daily

# Generate testing dashboard
uv run python scripts/testing_dashboard.py --realtime

# Benchmark testing performance
uv run python scripts/test_performance_benchmark.py
```

## 18. Compliance & Standards

### 18.1. Testing Compliance Checklist

- [ ] All tests use `uv run pytest`
- [ ] Test coverage > 80%
- [ ] All critical paths tested
- [ ] Security tests included
- [ ] Performance tests included
- [ ] Integration tests included
- [ ] Test documentation updated

### 18.2. Quality Gates

```bash
# Quality gate validation
uv run python scripts/quality_gates.py --strict

# Compliance verification
uv run python scripts/test_compliance.py --standard=estratix

# Audit test practices
uv run python scripts/audit_test_practices.py --report=detailed
```

---

**ESTRATIX High-Velocity Testing - Powered by UV**
**Test-Driven • High-Coverage • Continuous Validation**
© 2025 ESTRATIX - Testing Excellence Framework