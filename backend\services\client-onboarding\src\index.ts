import fastify, { FastifyInstance } from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import multipart from '@fastify/multipart';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';
import { requestLogging, responseLogging, errorHandler } from '@/middleware/logging';
import { authenticateToken } from '@/middleware/auth';

// Types are automatically loaded via tsconfig.json

// Import routes
import { healthRoutes } from '@/routes/health';
import { clientRoutes } from '@/routes/clients';
import { rfpRoutes } from '@/routes/rfp';
import { onboardingRoutes } from '@/routes/onboarding';
import { documentRoutes } from '@/routes/documents';
import { analyticsRoutes } from '@/routes/analytics';

// Import services
import { ClientService } from '@/services/clientService';
import { RFPService } from '@/services/rfpService';
import { OnboardingService } from '@/services/onboardingService';
import { DocumentService } from '@/services/documentService';
import { EmailService } from '@/services/emailService';
import { AnalyticsService } from '@/services/analyticsService';

// Create Fastify instance
const server = fastify({
  logger: logger,
  trustProxy: true,
  bodyLimit: environment.upload.maxSize,
  keepAliveTimeout: 30000,
  connectionTimeout: 30000
});

// Global error handler
server.setErrorHandler(async (error, request, reply) => {
  server.log.error(error, 'Unhandled error');
  
  const statusCode = error.statusCode || 500;
  const message = environment.nodeEnv === 'production' 
    ? 'Internal Server Error' 
    : error.message;
  
  return reply.status(statusCode).send({
    error: true,
    message,
    statusCode,
    timestamp: new Date().toISOString(),
    path: request.url
  });
});

// Register plugins
async function registerPlugins() {
  // Security plugins
  await server.register(helmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  });
  
  await server.register(cors, {
    origin: environment.cors.origin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
  });
  
  // JWT plugin
  await server.register(jwt, {
    secret: environment.jwt.secret,
    sign: {
      expiresIn: environment.jwt.expiresIn
    }
  });
  
  // Multipart plugin for file uploads
  await server.register(multipart, {
    limits: {
      fileSize: environment.upload.maxSize,
      files: 10
    }
  });
  
  // Swagger documentation
  await server.register(swagger, {
    openapi: {
      openapi: '3.0.0',
      info: {
        title: 'ESTRATIX Client Onboarding API',
        description: 'API for automated client intake, RFP processing, and onboarding workflows',
        version: '1.0.0',
        contact: {
          name: 'ESTRATIX Team',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: `http://${environment.host}:${environment.port}`,
          description: 'Development server'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      },
      security: [
        {
          bearerAuth: []
        }
      ]
    }
  });
  
  await server.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    },
    staticCSP: true,
    transformStaticCSP: (header) => header
  });
}

// Register middleware
async function registerMiddleware() {
  // Add request/response logging hooks
  server.addHook('preHandler', requestLogging);
  server.addHook('preHandler', responseLogging);
  
  // Add error handler
  server.setErrorHandler(errorHandler);
  
  // Add authentication decorator
  server.decorate('authenticate', authenticateToken);
}

// Register routes
async function registerRoutes() {
  await server.register(healthRoutes, { prefix: '/health' });
  await server.register(clientRoutes, { prefix: '/api/v1/clients' });
  await server.register(rfpRoutes, { prefix: '/api/v1/rfp' });
  await server.register(onboardingRoutes, { prefix: '/api/v1/onboarding' });
  await server.register(documentRoutes, { prefix: '/api/v1/documents' });
  await server.register(analyticsRoutes, { prefix: '/api/v1/analytics' });
}

// Initialize services
async function initializeServices() {
  try {
    server.log.info('Initializing Client Onboarding services...');
    
    // Initialize services
  const clientService = new ClientService();
  const rfpService = new RFPService();
  const onboardingService = new OnboardingService();
  const documentService = new DocumentService();
  const emailService = new EmailService();
  const analyticsService = new AnalyticsService();
    
    // Initialize services
    await clientService.initialize();
    await rfpService.initialize();
    await onboardingService.initialize();
    await documentService.initialize();
    await emailService.initialize();
    await analyticsService.initialize();
    
    // Decorate server with services
    (server as any).clientService = clientService;
    (server as any).rfpService = rfpService;
    (server as any).onboardingService = onboardingService;
    (server as any).documentService = documentService;
    (server as any).emailService = emailService;
    (server as any).analyticsService = analyticsService;
    
    server.log.info('All services initialized successfully');
  } catch (error) {
    server.log.error(error, 'Failed to initialize services');
    throw error;
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  server.log.info('SIGTERM received, shutting down gracefully');
  
  try {
    // Cleanup services
    const serverAny = server as any;
    if (serverAny.clientService) await serverAny.clientService.cleanup();
    if (serverAny.rfpService) await serverAny.rfpService.cleanup();
    if (serverAny.onboardingService) await serverAny.onboardingService.cleanup();
    if (serverAny.documentService) await serverAny.documentService.cleanup();
    if (serverAny.emailService) await serverAny.emailService.cleanup();
    if (serverAny.analyticsService) await serverAny.analyticsService.cleanup();
    
    await server.close();
    server.log.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    server.log.error(error, 'Error during shutdown');
    process.exit(1);
  }
});

// Start server
async function start() {
  try {
    server.log.info('Client Onboarding service configuration loaded for development environment');
    server.log.info(`Environment: ${environment.nodeEnv}`);
    server.log.info(`Server will start on ${environment.host}:${environment.port}`);
    
    // Register everything
    await registerPlugins();
    await registerMiddleware();
    await initializeServices();
    await registerRoutes();
    
    // Start the server
    await server.listen({
      port: environment.port,
      host: environment.host
    });
    
    server.log.info(`🚀 Client Onboarding service is running on http://${environment.host}:${environment.port}`);
    server.log.info(`📚 API Documentation available at http://${environment.host}:${environment.port}/docs`);
    
    if (environment.rfp.autoProcessing) {
      server.log.info('🤖 RFP Auto-processing is enabled');
    }
    
    if (environment.onboarding.workflowEnabled) {
      server.log.info('📋 Onboarding workflows are enabled');
    }
    
  } catch (error) {
    server.log.error(error, 'Failed to start server');
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  server.log.fatal(error, 'Uncaught exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  server.log.fatal({ reason, promise }, 'Unhandled rejection');
  process.exit(1);
});

// Start the application
start();

// Export for testing
export { server };
export default server;