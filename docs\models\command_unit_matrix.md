# ESTRATIX Command Unit Matrix

**Version:** 1.0
**Status:** Proposed
**Last Updated:** 2025-07-18

## 1. Introduction

This matrix defines the hierarchical command structure of the ESTRATIX agency, modeling operational units from the individual squad up to field armies. This structure enables clear command lines, recursive delegation, and scalable agentic operations, integrating seamlessly with the project, process, and agent matrices.

## 2. Unit Hierarchy

The hierarchy follows standard field army command structures:

1.  **Squad** (Basic unit)
2.  **Platoon**
3.  **Company**
4.  **Battalion**
5.  **Brigade**
6.  **Division**
7.  **Corps**
8.  **Field Army** (Highest tactical unit)

## 3. Matrix Schema

| Unit ID | Unit Name                    | Unit Type | Parent Unit ID | Lead Agent ID | Associated Process IDs | Associated Project ID | Status   |
| :------ | :--------------------------- | :-------- | :------------- | :------------ | :--------------------- | :-------------------- | :------- |
| `cu001` | `Alpha Squad`                | `Squad`   | `cu002`        | `a001`        | `p001, p002`           | `proj001`             | `Active` |
| `cu002` | `First Platoon`              | `Platoon` | `cu003`        | `a005`        | `p003`                 | `proj001`             | `Active` |
| `cu003` | `Bravo Company`              | `Company` | `cu004`        | `a010`        | `f001`                 | `proj001`             | `Active` |
| `cu004` | `Project-X Battalion`        | `Battalion`| `null`         | `a020`        | `f001`                 | `proj001`             | `Active` |
| `...`   | `...`                        | `...`     | `...`          | `...`         | `...`                  | `...`                 | `...`    |

### Column Definitions

- **Unit ID (`cu###`)**: The globally unique identifier for the command unit. The `cu` prefix is proposed.
- **Unit Name**: A descriptive, human-readable name for the unit.
- **Unit Type**: The unit's level in the command hierarchy (e.g., Squad, Platoon).
- **Parent Unit ID**: The ID of the direct parent unit. `null` for top-level units like Battalions assigned directly to a Command Office or high-level projects.
- **Lead Agent ID**: The unique ID of the agent designated as the leader of this unit.
- **Associated Process IDs**: A comma-separated list of Process or Flow IDs that the unit is primarily responsible for executing.
- **Associated Project ID**: The unique ID of the project the unit is assigned to.
- **Status**: The current operational status of the unit (e.g., `Active`, `Inactive`, `Forming`).