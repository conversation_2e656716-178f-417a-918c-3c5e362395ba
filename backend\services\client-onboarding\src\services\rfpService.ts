import { v4 as uuidv4 } from 'uuid';
import { RFP, RFPRequirement, RFPMilestone, CreateRFPRequest } from '@/types';
import { logger } from '@/utils/logger';
import { sanitizeString } from '@/utils/validation';

export class RFPService {
  private rfps: Map<string, RFP> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing RFP Service...');
      
      // Generate mock RFPs for development
      await this.generateMockRFPs();
      
      this.isInitialized = true;
      logger.info('RFP Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize RFP Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up RFP Service...');
    this.rfps.clear();
    this.isInitialized = false;
  }

  async createRFP(organizationId: string, data: CreateRFPRequest): Promise<RFP> {
    try {
      const rfpId = uuidv4();
      const now = new Date();

      // Process requirements
      const requirements: RFPRequirement[] = data.requirements.map(req => ({
        id: uuidv4(),
        category: sanitizeString(req.category, 50),
        description: sanitizeString(req.description, 500),
        priority: req.priority,
        criteria: req.criteria.map(c => sanitizeString(c, 200))
      }));

      // Process milestones
      const milestones: RFPMilestone[] = data.timeline.milestones.map(milestone => ({
        id: uuidv4(),
        name: sanitizeString(milestone.name, 100),
        description: sanitizeString(milestone.description, 500),
        dueDate: new Date(milestone.dueDate),
        deliverables: milestone.deliverables.map(d => sanitizeString(d, 200))
      }));

      const rfp: RFP = {
        id: rfpId,
        clientId: data.clientId,
        organizationId,
        title: sanitizeString(data.title, 200),
        description: sanitizeString(data.description, 2000),
        requirements,
        budget: data.budget,
        timeline: {
          startDate: new Date(data.timeline.startDate),
          endDate: new Date(data.timeline.endDate),
          milestones
        },
        status: 'draft',
        priority: data.priority || 'medium',
        attachments: [],
        responses: [],
        createdAt: now,
        updatedAt: now
      };

      this.rfps.set(rfpId, rfp);

      logger.info('RFP created successfully', {
        rfpId,
        clientId: data.clientId,
        organizationId,
        title: rfp.title
      });

      return rfp;
    } catch (error) {
      logger.error('Failed to create RFP', { error, data });
      throw error;
    }
  }

  async getRFP(rfpId: string, organizationId: string): Promise<RFP | null> {
    const rfp = this.rfps.get(rfpId);
    
    if (!rfp || rfp.organizationId !== organizationId) {
      return null;
    }

    return rfp;
  }

  async updateRFP(
    rfpId: string,
    organizationId: string,
    updates: Partial<Pick<RFP, 'title' | 'description' | 'priority' | 'status'>>
  ): Promise<RFP | null> {
    try {
      const rfp = await this.getRFP(rfpId, organizationId);
      
      if (!rfp) {
        return null;
      }

      const updatedRFP: RFP = {
        ...rfp,
        title: updates.title ? sanitizeString(updates.title, 200) : rfp.title,
        description: updates.description ? sanitizeString(updates.description, 2000) : rfp.description,
        priority: updates.priority || rfp.priority,
        status: updates.status || rfp.status,
        updatedAt: new Date()
      };

      // Update timestamps based on status changes
      if (updates.status === 'submitted' && rfp.status !== 'submitted') {
        updatedRFP.submittedAt = new Date();
      }
      
      if (updates.status && ['approved', 'rejected'].includes(updates.status) && !rfp.reviewedAt) {
        updatedRFP.reviewedAt = new Date();
      }

      this.rfps.set(rfpId, updatedRFP);

      logger.info('RFP updated successfully', {
        rfpId,
        organizationId,
        changes: Object.keys(updates)
      });

      return updatedRFP;
    } catch (error) {
      logger.error('Failed to update RFP', { error, rfpId, updates });
      throw error;
    }
  }

  async deleteRFP(rfpId: string, organizationId: string): Promise<boolean> {
    try {
      const rfp = await this.getRFP(rfpId, organizationId);
      
      if (!rfp) {
        return false;
      }

      // Only allow deletion of draft RFPs
      if (rfp.status !== 'draft') {
        throw new Error('Cannot delete RFP that has been submitted');
      }

      this.rfps.delete(rfpId);

      logger.info('RFP deleted successfully', {
        rfpId,
        organizationId
      });

      return true;
    } catch (error) {
      logger.error('Failed to delete RFP', { error, rfpId });
      throw error;
    }
  }

  async getRFPs(
    organizationId: string,
    filters: {
      status?: RFP['status'];
      priority?: RFP['priority'];
      clientId?: string;
      search?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ rfps: RFP[]; total: number; page: number; limit: number }> {
    try {
      let filteredRFPs = Array.from(this.rfps.values())
        .filter(rfp => rfp.organizationId === organizationId);

      // Apply filters
      if (filters.status) {
        filteredRFPs = filteredRFPs.filter(rfp => rfp.status === filters.status);
      }

      if (filters.priority) {
        filteredRFPs = filteredRFPs.filter(rfp => rfp.priority === filters.priority);
      }

      if (filters.clientId) {
        filteredRFPs = filteredRFPs.filter(rfp => rfp.clientId === filters.clientId);
      }

      if (filters.search) {
        const searchTerm = filters.search.toLowerCase();
        filteredRFPs = filteredRFPs.filter(rfp => 
          rfp.title.toLowerCase().includes(searchTerm) ||
          rfp.description.toLowerCase().includes(searchTerm)
        );
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'desc';
      
      filteredRFPs.sort((a, b) => {
        let aValue: any = (a as any)[sortBy];
        let bValue: any = (b as any)[sortBy];

        if (aValue instanceof Date) aValue = aValue.getTime();
        if (bValue instanceof Date) bValue = bValue.getTime();

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedRFPs = filteredRFPs.slice(startIndex, endIndex);

      return {
        rfps: paginatedRFPs,
        total: filteredRFPs.length,
        page,
        limit
      };
    } catch (error) {
      logger.error('Failed to get RFPs', { error, filters });
      throw error;
    }
  }

  async submitRFP(rfpId: string, organizationId: string): Promise<RFP | null> {
    try {
      const rfp = await this.getRFP(rfpId, organizationId);
      
      if (!rfp) {
        return null;
      }

      if (rfp.status !== 'draft') {
        throw new Error('RFP can only be submitted from draft status');
      }

      // Validate RFP completeness
      if (!rfp.requirements.length) {
        throw new Error('RFP must have at least one requirement');
      }

      if (rfp.timeline.startDate >= rfp.timeline.endDate) {
        throw new Error('RFP end date must be after start date');
      }

      const updatedRFP: RFP = {
        ...rfp,
        status: 'submitted',
        submittedAt: new Date(),
        updatedAt: new Date()
      };

      this.rfps.set(rfpId, updatedRFP);

      logger.info('RFP submitted successfully', {
        rfpId,
        organizationId,
        title: rfp.title
      });

      return updatedRFP;
    } catch (error) {
      logger.error('Failed to submit RFP', { error, rfpId });
      throw error;
    }
  }

  async getRFPStats(organizationId: string): Promise<{
    total: number;
    byStatus: Record<RFP['status'], number>;
    byPriority: Record<RFP['priority'], number>;
    recentlyCreated: number;
  }> {
    try {
      const rfps = Array.from(this.rfps.values())
        .filter(rfp => rfp.organizationId === organizationId);

      const byStatus: Record<RFP['status'], number> = {
        draft: 0,
        submitted: 0,
        under_review: 0,
        approved: 0,
        rejected: 0,
        cancelled: 0
      };

      const byPriority: Record<RFP['priority'], number> = {
        low: 0,
        medium: 0,
        high: 0,
        urgent: 0
      };

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      let recentlyCreated = 0;

      rfps.forEach(rfp => {
        byStatus[rfp.status]++;
        byPriority[rfp.priority]++;
        
        if (rfp.createdAt > thirtyDaysAgo) {
          recentlyCreated++;
        }
      });

      return {
        total: rfps.length,
        byStatus,
        byPriority,
        recentlyCreated
      };
    } catch (error) {
      logger.error('Failed to get RFP stats', { error, organizationId });
      throw error;
    }
  }

  private async generateMockRFPs(): Promise<void> {
    const mockRFPs: Omit<RFP, 'id' | 'createdAt' | 'updatedAt' | 'submittedAt' | 'reviewedAt'>[] = [
      {
        clientId: 'client-1',
        organizationId: 'org-1',
        title: 'E-commerce Platform Development',
        description: 'We need a comprehensive e-commerce platform with modern features including payment processing, inventory management, and customer analytics.',
        requirements: [
          {
            id: uuidv4(),
            category: 'Frontend',
            description: 'Responsive web application with modern UI/UX',
            priority: 'must_have',
            criteria: ['Mobile responsive', 'Cross-browser compatibility', 'Accessibility compliance']
          },
          {
            id: uuidv4(),
            category: 'Backend',
            description: 'Scalable API with secure authentication',
            priority: 'must_have',
            criteria: ['RESTful API', 'JWT authentication', 'Rate limiting']
          }
        ],
        budget: {
          min: 50000,
          max: 100000,
          currency: 'USD'
        },
        timeline: {
          startDate: new Date('2024-02-01'),
          endDate: new Date('2024-08-01'),
          milestones: [
            {
              id: uuidv4(),
              name: 'Design Phase',
              description: 'Complete UI/UX design and prototyping',
              dueDate: new Date('2024-03-15'),
              deliverables: ['Wireframes', 'Design mockups', 'Interactive prototype']
            }
          ]
        },
        status: 'submitted',
        priority: 'high',
        attachments: [],
        responses: []
      }
    ];

    for (const mockRFP of mockRFPs) {
      const now = new Date();
      const rfp: RFP = {
        ...mockRFP,
        id: uuidv4(),
        createdAt: now,
        updatedAt: now
      };
      
      this.rfps.set(rfp.id, rfp);
    }

    logger.debug(`Generated ${mockRFPs.length} mock RFPs`);
  }
}