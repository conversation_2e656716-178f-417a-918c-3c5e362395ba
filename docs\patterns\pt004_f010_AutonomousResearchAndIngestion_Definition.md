# ESTRATIX Pattern Definition: Autonomous Research and Ingestion

- **Pattern ID**: `pt004`
- **Status**: `Defined`
- **Version**: `1.0.0`
- **Lead Command Office**: `CIO`
- **Last Updated**: `2025-07-05`

---

## 1. Description

This pattern defines a standardized, autonomous workflow for researching a topic, gathering information from various web sources, processing the collected data, and ingesting it into the ESTRATIX knowledge base. It orchestrates a suite of specialized tools to create a robust and repeatable research capability for agentic crews.

## 2. Pattern Flow

1. **Tasking**: An agent (e.g., `CIO_A002_ResearchAnalyst`) receives a research task with a specific topic or question.
2. **Initial Search**: The agent uses the `BraveSearch` MCP (`TL_MCP_BraveSearch`) to identify a list of relevant URLs.
3. **Content Scraping**: For each URL, the agent selects the most appropriate scraping tool based on the perceived complexity of the target site:
   - **Simple Static Sites**: `Web Scraper` (`p006_t001`)
   - **Complex/JS-Heavy Sites**: `Firecrawl Scraper` (`p006_t005`) or `Crawl4AI Scraper` (`p006_t006`) for robust, LLM-friendly markdown extraction.
   - **Interactive Navigation**: `BrowserUseTool` (`p006_t007`) if the task requires navigating through a site or interacting with elements before extraction.
4. **Content Processing**: The raw scraped content (HTML, markdown, etc.) is passed to the `Content Processor` (`p006_t003`) to be cleaned, normalized, and split into manageable chunks for embedding.
5. **Vectorization & Ingestion**: The processed chunks are then vectorized and ingested into the knowledge base (e.g., Neo4j), making them available for retrieval.
6. **Synthesis & Reporting**: The agent uses the `VectorSearchTool` (`CIO_T001`) to query the newly ingested information, synthesize an answer to the original research task, and generate a report.

## 3. Core Components

- **Flows**: `CIO_F010_AutonomousResearchAndIngestionFlow`
- **Processes**: `CIO_P005_AutonomousResearch`
- **Agents**: `CIO_A002_ResearchAnalyst`
- **Tools**:
  - `TL_MCP_BraveSearch`
  - `p006_t001_web_scraper`
  - `p006_t005_firecrawl_scraper`
  - `p006_t006_crawl4ai_scraper`
  - `p006_t007_browser_use_tool`
  - `p006_t003_content_processor`
  - `CIO_T001_VectorSearchTool`

## 4. Dependencies

- A configured and accessible vector database.
- API keys for services like Firecrawl, if used.

## 5. Revision History

| Version | Date | Author | Changes |
| :--- | :--- | :--- | :--- |
| 1.0.0 | 2025-07-05 | Cascade | Initial definition of the autonomous research pattern. |

---
