# Task Definition: Define Agent

- **Task ID:** `CHRO_T001`
- **Task Name:** `Define Agent`
- **Owner Command Office:** `CHRO`
- **Parent Process ID:** `CHRO_P001`
- **Version:** `1.0`

## 1. Description

This task takes an approved agent proposal and creates the formal, standardized `agent_definition.md` file. This definition serves as the canonical source of truth for the agent's purpose, capabilities, and operational parameters, which is used by the `MasterBuilderAgent` for code generation.

## 2. Inputs

| Input | Data Model ID | Source | Description |
|---|---|---|---|
| Approved Agent Proposal | `MDL-PRO-001` | `CHRO_P001` | The formal request to create a new agent, containing its high-level goals and requirements. |

## 3. Expected Output

| Output | Data Model ID | Destination | Description |
|---|---|---|---|
| Agent Definition File | `MDL-AGN-001` | `docs/agents/` | A new `[Agent_ID]_definition.md` file, populated according to the standard agent definition template. |

## 4. Required Tools

| Tool ID | Tool Name | Purpose |
|---|---|---|
| `TOL-SYS-001` | FileSystemWriter | To create and write the new definition file to the correct directory. |
| `TOL-TPL-001` | TemplateApplier | To populate the new file with the standard agent definition template. |

## 5. Acceptance Criteria

- A new file named `[Agent_ID]_definition.md` is created in the `docs/agents/` directory.
- The file content successfully validates against the standard agent definition template schema.
- The file contains all necessary sections, including role, goal, backstory, tools, and operational parameters.

## 6. Exception Handling

| Condition | Action | Notes |
|---|---|---|
| Invalid Proposal Input | Raise `InvalidInputError` | The input proposal is missing required fields or is improperly formatted. |
| File Write Failure | Raise `ToolFailureError` | The `FileSystemWriter` tool fails to create the file due to permissions or other issues. |
