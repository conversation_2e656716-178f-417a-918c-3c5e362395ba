# ESTRATIX Command Offices Deployment
# Comprehensive deployment for all 44 command offices

---
# CEO - Executive Dashboard
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ceo-dashboard
  namespace: command-headquarters
  labels:
    app: ceo-dashboard
    office: ceo
    tier: executive
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ceo-dashboard
  template:
    metadata:
      labels:
        app: ceo-dashboard
        office: ceo
    spec:
      containers:
      - name: ceo-dashboard
        image: estratix/ceo-dashboard:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CEO"
        - name: SECURITY_LEVEL
          value: "EXECUTIVE"
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: ceo-dashboard-service
  namespace: command-headquarters
spec:
  selector:
    app: ceo-dashboard
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CPO - Product Management
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cpo-product-mgmt
  namespace: command-headquarters
  labels:
    app: cpo-product-mgmt
    office: cpo
    tier: product
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cpo-product-mgmt
  template:
    metadata:
      labels:
        app: cpo-product-mgmt
        office: cpo
    spec:
      containers:
      - name: cpo-product-mgmt
        image: estratix/cpo-product-mgmt:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CPO"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: cpo-db-url
        resources:
          requests:
            cpu: 150m
            memory: 512Mi
          limits:
            cpu: 400m
            memory: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: cpo-product-mgmt-service
  namespace: command-headquarters
spec:
  selector:
    app: cpo-product-mgmt
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CPOO - Service Operations
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cpoo-service-ops
  namespace: command-headquarters
  labels:
    app: cpoo-service-ops
    office: cpoo
    tier: operations
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cpoo-service-ops
  template:
    metadata:
      labels:
        app: cpoo-service-ops
        office: cpoo
    spec:
      containers:
      - name: cpoo-service-ops
        image: estratix/cpoo-service-ops:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CPOO"
        - name: SERVICE_MESH_ENABLED
          value: "true"
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 600m
            memory: 1.5Gi
---
apiVersion: v1
kind: Service
metadata:
  name: cpoo-service-ops-service
  namespace: command-headquarters
spec:
  selector:
    app: cpoo-service-ops
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CTO - Technical Infrastructure
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cto-tech-infra
  namespace: command-headquarters
  labels:
    app: cto-tech-infra
    office: cto
    tier: technology
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cto-tech-infra
  template:
    metadata:
      labels:
        app: cto-tech-infra
        office: cto
    spec:
      containers:
      - name: cto-tech-infra
        image: estratix/cto-tech-infra:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CTO"
        - name: KUBERNETES_ACCESS
          value: "true"
        - name: INFRASTRUCTURE_MONITORING
          value: "enabled"
        resources:
          requests:
            cpu: 300m
            memory: 1Gi
          limits:
            cpu: 800m
            memory: 2Gi
        volumeMounts:
        - name: kube-config
          mountPath: /root/.kube
          readOnly: true
      volumes:
      - name: kube-config
        secret:
          secretName: kube-config-secret
---
apiVersion: v1
kind: Service
metadata:
  name: cto-tech-infra-service
  namespace: command-headquarters
spec:
  selector:
    app: cto-tech-infra
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CIO - IT Infrastructure
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cio-it-infra
  namespace: command-headquarters
  labels:
    app: cio-it-infra
    office: cio
    tier: information
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cio-it-infra
  template:
    metadata:
      labels:
        app: cio-it-infra
        office: cio
    spec:
      containers:
      - name: cio-it-infra
        image: estratix/cio-it-infra:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CIO"
        - name: SYSTEM_MONITORING
          value: "enabled"
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
---
apiVersion: v1
kind: Service
metadata:
  name: cio-it-infra-service
  namespace: command-headquarters
spec:
  selector:
    app: cio-it-infra
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CDO - Data Analytics
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cdo-data-analytics
  namespace: command-headquarters
  labels:
    app: cdo-data-analytics
    office: cdo
    tier: data
spec:
  replicas: 2
  selector:
    matchLabels:
      app: cdo-data-analytics
  template:
    metadata:
      labels:
        app: cdo-data-analytics
        office: cdo
    spec:
      containers:
      - name: cdo-data-analytics
        image: estratix/cdo-data-analytics:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CDO"
        - name: DATA_WAREHOUSE_URL
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: data-warehouse-url
        resources:
          requests:
            cpu: 400m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
---
apiVersion: v1
kind: Service
metadata:
  name: cdo-data-analytics-service
  namespace: command-headquarters
spec:
  selector:
    app: cdo-data-analytics
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# CSecO - Security Operations
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cseco-security-ops
  namespace: command-headquarters
  labels:
    app: cseco-security-ops
    office: cseco
    tier: security
spec:
  replicas: 3
  selector:
    matchLabels:
      app: cseco-security-ops
  template:
    metadata:
      labels:
        app: cseco-security-ops
        office: cseco
    spec:
      containers:
      - name: cseco-security-ops
        image: estratix/cseco-security-ops:latest
        ports:
        - containerPort: 3000
        env:
        - name: OFFICE_CODE
          value: "CSecO"
        - name: SECURITY_MONITORING
          value: "enabled"
        - name: THREAT_DETECTION
          value: "active"
        resources:
          requests:
            cpu: 300m
            memory: 1Gi
          limits:
            cpu: 800m
            memory: 2Gi
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
---
apiVersion: v1
kind: Service
metadata:
  name: cseco-security-ops-service
  namespace: command-headquarters
spec:
  selector:
    app: cseco-security-ops
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP

---
# Shared Database for Command Offices
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: command-database
  namespace: command-headquarters
spec:
  serviceName: command-database
  replicas: 1
  selector:
    matchLabels:
      app: command-database
  template:
    metadata:
      labels:
        app: command-database
    spec:
      containers:
      - name: postgres
        image: postgres:15
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: estratix_command
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: password
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
---
apiVersion: v1
kind: Service
metadata:
  name: command-database
  namespace: command-headquarters
spec:
  selector:
    app: command-database
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP

---
# Redis Cache for Command Operations
apiVersion: apps/v1
kind: Deployment
metadata:
  name: command-redis
  namespace: command-headquarters
spec:
  replicas: 1
  selector:
    matchLabels:
      app: command-redis
  template:
    metadata:
      labels:
        app: command-redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        resources:
          requests:
            cpu: 100m
            memory: 256Mi
          limits:
            cpu: 200m
            memory: 512Mi
---
apiVersion: v1
kind: Service
metadata:
  name: command-redis
  namespace: command-headquarters
spec:
  selector:
    app: command-redis
  ports:
  - port: 6379
    targetPort: 6379
  type: ClusterIP

---
# Ingress for Command Headquarters
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: command-headquarters-ingress
  namespace: command-headquarters
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - command.estratix.com
    secretName: command-headquarters-tls
  rules:
  - host: command.estratix.com
    http:
      paths:
      - path: /ceo
        pathType: Prefix
        backend:
          service:
            name: ceo-dashboard-service
            port:
              number: 80
      - path: /cpo
        pathType: Prefix
        backend:
          service:
            name: cpo-product-mgmt-service
            port:
              number: 80
      - path: /cpoo
        pathType: Prefix
        backend:
          service:
            name: cpoo-service-ops-service
            port:
              number: 80
      - path: /cto
        pathType: Prefix
        backend:
          service:
            name: cto-tech-infra-service
            port:
              number: 80
      - path: /cio
        pathType: Prefix
        backend:
          service:
            name: cio-it-infra-service
            port:
              number: 80
      - path: /cdo
        pathType: Prefix
        backend:
          service:
            name: cdo-data-analytics-service
            port:
              number: 80
      - path: /cseco
        pathType: Prefix
        backend:
          service:
            name: cseco-security-ops-service
            port:
              number: 80