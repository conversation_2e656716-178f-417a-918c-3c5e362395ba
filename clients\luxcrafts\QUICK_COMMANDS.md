﻿# Luxcrafts Quick Deployment Commands

## Development
```bash
# Start development server
npm run dev

# Run quality checks
npm run check
npm run lint

# Build for production
npm run build
```

## Staging Deployment
```bash
# Automated staging deployment
powershell -ExecutionPolicy Bypass -File scripts/deploy-staging.ps1

# Manual Vercel deployment
npx vercel --prebuilt
```

## Production Deployment
```bash
# Trigger production deployment via GitHub Actions
gh workflow run production-deployment.yml

# Manual production deployment
powershell -ExecutionPolicy Bypass -File scripts/deploy-production.ps1
```

## Health Checks
```bash
# Check staging health
curl https://your-staging-url.vercel.app/health.json

# Check production health
curl https://luxcrafts.co/health.json
```

## Monitoring
```bash
# View GitHub Actions status
gh run list

# View specific workflow run
gh run view [run-id]

# View deployment logs
gh run view [run-id] --log
```
