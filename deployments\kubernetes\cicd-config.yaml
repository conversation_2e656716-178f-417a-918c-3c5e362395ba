# ESTRATIX CI/CD Pipeline Configuration
# GitLab Runner and ArgoCD setup for automated deployments

---
# Namespace for CI/CD
apiVersion: v1
kind: Namespace
metadata:
  name: cicd
  labels:
    name: cicd
    purpose: continuous-integration-deployment

---
# GitLab Runner Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: gitlab-runner-config
  namespace: cicd
data:
  config.toml: |
    concurrent = 4
    check_interval = 0
    
    [session_server]
      session_timeout = 1800
    
    [[runners]]
      name = "estratix-k8s-runner"
      url = "https://gitlab.com/"
      token = "GITLAB_RUNNER_TOKEN_PLACEHOLDER"
      executor = "kubernetes"
      [runners.kubernetes]
        host = ""
        namespace = "cicd"
        privileged = true
        image = "ubuntu:20.04"
        [[runners.kubernetes.volumes.empty_dir]]
          name = "docker-certs"
          mount_path = "/certs/client"
          medium = "Memory"
        [[runners.kubernetes.volumes.host_path]]
          name = "docker-sock"
          mount_path = "/var/run/docker.sock"
          host_path = "/var/run/docker.sock"
      [runners.cache]
        Type = "s3"
        Shared = true
        [runners.cache.s3]
          ServerAddress = "minio.cicd:9000"
          BucketName = "gitlab-runner-cache"
          BucketLocation = "us-east-1"
          Insecure = true

---
# GitLab Runner Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gitlab-runner
  namespace: cicd
spec:
  replicas: 2
  selector:
    matchLabels:
      app: gitlab-runner
  template:
    metadata:
      labels:
        app: gitlab-runner
    spec:
      serviceAccountName: gitlab-runner
      containers:
      - name: gitlab-runner
        image: gitlab/gitlab-runner:latest
        command: ["/bin/bash"]
        args: ["-c", "gitlab-runner register --non-interactive && gitlab-runner run"]
        env:
        - name: CI_SERVER_URL
          value: "https://gitlab.com/"
        - name: REGISTRATION_TOKEN
          valueFrom:
            secretKeyRef:
              name: gitlab-runner-secret
              key: registration-token
        - name: RUNNER_NAME
          value: "estratix-k8s-runner"
        - name: RUNNER_EXECUTOR
          value: "kubernetes"
        - name: KUBERNETES_NAMESPACE
          value: "cicd"
        - name: KUBERNETES_PRIVILEGED
          value: "true"
        volumeMounts:
        - name: gitlab-runner-config
          mountPath: /etc/gitlab-runner
        - name: docker-sock
          mountPath: /var/run/docker.sock
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
      volumes:
      - name: gitlab-runner-config
        configMap:
          name: gitlab-runner-config
      - name: docker-sock
        hostPath:
          path: /var/run/docker.sock

---
# GitLab Runner Service Account
apiVersion: v1
kind: ServiceAccount
metadata:
  name: gitlab-runner
  namespace: cicd

---
# GitLab Runner RBAC
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: gitlab-runner
rules:
- apiGroups: [""]
  resources: ["pods", "pods/exec", "pods/log", "services", "secrets", "configmaps"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["extensions"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: gitlab-runner
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: gitlab-runner
subjects:
- kind: ServiceAccount
  name: gitlab-runner
  namespace: cicd

---
# GitLab Runner Secret
apiVersion: v1
kind: Secret
metadata:
  name: gitlab-runner-secret
  namespace: cicd
type: Opaque
data:
  registration-token: R0lUTEFCX1JVTk5FUl9UT0tFTl9QTEFDRUIPTERSX1JFUE9SVEVSRQ==  # Placeholder token

---
# ArgoCD Installation
apiVersion: v1
kind: Namespace
metadata:
  name: argocd
  labels:
    name: argocd
    purpose: gitops-deployment

---
# ArgoCD Server Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-server-config
  namespace: argocd
data:
  url: "https://argocd.estratix.com"
  application.instanceLabelKey: argocd.argoproj.io/instance
  server.rbac.log.enforce.enable: "true"
  exec.enabled: "true"
  admin.enabled: "true"
  timeout.reconciliation: "180s"
  oidc.config: |
    name: ESTRATIX SSO
    issuer: https://auth.estratix.com
    clientId: argocd
    clientSecret: $oidc.estratix.clientSecret
    requestedScopes: ["openid", "profile", "email", "groups"]
    requestedIDTokenClaims: {"groups": {"essential": true}}

---
# ArgoCD Repository Configuration
apiVersion: v1
kind: Secret
metadata:
  name: estratix-repo
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
type: Opaque
stringData:
  type: git
  url: https://gitlab.com/estratix/infrastructure
  username: estratix-bot
  password: GITLAB_ACCESS_TOKEN_PLACEHOLDER

---
# ArgoCD Application for Command Headquarters
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: command-headquarters
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://gitlab.com/estratix/infrastructure
    targetRevision: main
    path: deployments/kubernetes
  destination:
    server: https://kubernetes.default.svc
    namespace: command-headquarters
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m

---
# ArgoCD Application for Monitoring
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: monitoring-stack
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://gitlab.com/estratix/infrastructure
    targetRevision: main
    path: deployments/monitoring
  destination:
    server: https://kubernetes.default.svc
    namespace: monitoring
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
    - CreateNamespace=true

---
# Docker Registry for CI/CD
apiVersion: apps/v1
kind: Deployment
metadata:
  name: docker-registry
  namespace: cicd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: docker-registry
  template:
    metadata:
      labels:
        app: docker-registry
    spec:
      containers:
      - name: registry
        image: registry:2
        ports:
        - containerPort: 5000
        env:
        - name: REGISTRY_STORAGE_FILESYSTEM_ROOTDIRECTORY
          value: /var/lib/registry
        - name: REGISTRY_AUTH
          value: htpasswd
        - name: REGISTRY_AUTH_HTPASSWD_REALM
          value: Registry Realm
        - name: REGISTRY_AUTH_HTPASSWD_PATH
          value: /auth/htpasswd
        volumeMounts:
        - name: registry-storage
          mountPath: /var/lib/registry
        - name: registry-auth
          mountPath: /auth
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
      volumes:
      - name: registry-storage
        persistentVolumeClaim:
          claimName: registry-storage
      - name: registry-auth
        secret:
          secretName: registry-auth

---
# Docker Registry Service
apiVersion: v1
kind: Service
metadata:
  name: docker-registry
  namespace: cicd
spec:
  selector:
    app: docker-registry
  ports:
  - port: 5000
    targetPort: 5000
  type: ClusterIP

---
# Docker Registry PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: registry-storage
  namespace: cicd
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi

---
# Docker Registry Auth Secret
apiVersion: v1
kind: Secret
metadata:
  name: registry-auth
  namespace: cicd
type: Opaque
data:
  htpasswd: ZXN0cmF0aXg6JDJ5JDA1JE5vVGhpbmdIZXJlSnVzdEZha2VIYXNoRm9yRXhhbXBsZQ==  # estratix:password

---
# MinIO for CI/CD Cache
apiVersion: apps/v1
kind: Deployment
metadata:
  name: minio
  namespace: cicd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      containers:
      - name: minio
        image: minio/minio:latest
        args:
        - server
        - /data
        - --console-address
        - ":9001"
        ports:
        - containerPort: 9000
        - containerPort: 9001
        env:
        - name: MINIO_ROOT_USER
          value: estratix
        - name: MINIO_ROOT_PASSWORD
          value: estratix2024
        volumeMounts:
        - name: minio-storage
          mountPath: /data
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
      volumes:
      - name: minio-storage
        persistentVolumeClaim:
          claimName: minio-storage

---
# MinIO Service
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: cicd
spec:
  selector:
    app: minio
  ports:
  - name: api
    port: 9000
    targetPort: 9000
  - name: console
    port: 9001
    targetPort: 9001
  type: ClusterIP

---
# MinIO PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: minio-storage
  namespace: cicd
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi

---
# CI/CD Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: cicd-ingress
  namespace: cicd
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - registry.estratix.com
    - minio.estratix.com
    secretName: cicd-tls
  rules:
  - host: registry.estratix.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: docker-registry
            port:
              number: 5000
  - host: minio.estratix.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: minio
            port:
              number: 9001

---
# ArgoCD Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: argocd-ingress
  namespace: argocd
  annotations:
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/backend-protocol: GRPC
spec:
  tls:
  - hosts:
    - argocd.estratix.com
    secretName: argocd-tls
  rules:
  - host: argocd.estratix.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: argocd-server
            port:
              number: 80