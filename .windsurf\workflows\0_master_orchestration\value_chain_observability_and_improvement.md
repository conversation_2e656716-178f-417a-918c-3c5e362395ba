---
description: (MASTER WORKFLOW) Manages the ongoing observability, alignment, and continuous improvement of the ESTRATIX value chain. It orchestrates the lifecycle of domain components and their implementations, ensuring the digital twin (docs) aligns with the physical, framework-agnostic implementation twin (src/domain).
---

# Master Workflow: Value Chain Observability & Improvement

**Objective**: To establish and maintain a comprehensive, agent-driven system for the continuous observability, alignment, and improvement of all ESTRATIX components. This workflow operationalizes the PDCA (Plan-Do-Check-Act) cycle, ensuring the **Digital Twin** (`docs/`) accurately reflects and informs the **Physical Twin** (`src/`), which is in turn consumed by agentic frameworks.

**Governing Rules**: `/.windsurf/rules/project_rules.md`

**Agent Persona**: `AGENT_SystemArchitect_Expert`, `AGENT_ProcessImprovement_Expert`

**Instructions**: This is a cyclical, continuous improvement workflow. Start at Phase 1 and loop as needed to drive operational excellence.

---

## Phase 1: CHECK - Audit the Digital & Physical Twins

**Objective**: To programmatically assess the current state of the ESTRATIX ecosystem, identify drift between the digital and physical twins, and flag opportunities for improvement.

1. **Audit Component Matrices**:
   - **Action**: An agent systematically scans all component matrices in `docs/models/` (e.g., `process_matrix.md`, `tool_matrix.md`) to build a complete inventory of all defined components.
   - **Goal**: Identify components that are `Pending Definition`, `Defined` but not implemented, or marked for `Deprecation`.

2. **Verify Twin-to-Twin Traceability**:
   - **Action**: For each `Implemented` component, an agent verifies that the link in the matrix correctly points to an existing implementation directory in `src/infrastructure/frameworks/` or `src/domain/`.
   - **Goal**: Ensure every documented component has a corresponding physical implementation.

3. **Detect Code-Level Drift**:
   - **Action**: A specialized linter agent compares the function/class signatures in a component's `src/` implementation against its definition in `docs/`. For example, it checks if a tool's parameters in code match the documentation.
   - **Goal**: Identify discrepancies between the intended design and the actual code.

4. **Propose Corrective Actions or Improvements**:
   - **Action**: Based on the audit, an architect agent generates proposals to address any identified gaps, drift, or opportunities.
   - **Workflow**: Execute the `/1_component_lifecycle/1_define/proposal_definition.md` workflow.

---

## Phase 2: PLAN - Define the Change in the Digital Twin

**Objective**: To take an approved proposal and create a detailed, actionable plan by first defining the change in the Digital Twin (`docs/`). This ensures design precedes implementation.

1. **Select Approved Proposal**: Choose a proposal from `docs/proposals/` with the status `Approved`.

2. **Define Component in the Digital Twin (Atomic Registration)**:
   - **Action**: Use the appropriate workflow to create or update the component's markdown definition file. This models the change before it is built.
   - **Workflow Triggers**: Execute the relevant workflow from `/1_component_lifecycle/1_define/` (e.g., `process_definition.md`, `tool_definition.md`).
   - **Critical Step**: The definition workflow **must** handle atomic ID generation and registration, updating the relevant matrix and setting the component's status to `Defined`.

---

## Phase 3: DO - Implement the Change in the Physical Twin

**Objective**: To translate the Digital Twin definition into runnable, tested code, following the process-centric orchestration model.

**Guiding Principle**: Generation starts at the highest level of orchestration (Flow or Process) and recursively triggers the generation of its dependencies (Agents, Tasks). This ensures a top-down, cohesive implementation.

1. **Generate Domain-Level Tools & Services**:
   - **Action**: If the change involves new, reusable, framework-agnostic logic, generate these components first.
   - **Location**: `src/domain/tools/` or `src/domain/services/`
   - **Workflows**: `/tool_generation`, `/service_generation`

2. **Generate Framework-Specific Processes & Flows**:
   - **Action**: Generate the primary orchestrators (Processes) and their higher-level coordinators (Flows) within the target agentic framework.
   - **Location**: `src/infrastructure/frameworks/[framework_name]/crews/` or `src/infrastructure/frameworks/[framework_name]/flows/`
   - **Workflows**: `/process_generation`, `/flow_generation`
   - **Recursive Generation**: These workflows are responsible for orchestrating the generation of their constituent parts:
     - The `/process_generation` workflow will trigger `/agent_generation` and `/task_generation` for all required components.
     - The `/flow_generation` workflow ensures all required `Process` components are available and implemented.

3. **Develop, Test, and Document Code**:
   - **Action**: A developer agent fleshes out the generated boilerplate with the required business logic, following TDD principles and ensuring all code is documented with docstrings.
   - **Goal**: A fully functional and tested component that perfectly matches its specification in the Digital Twin.

4. **Update Matrix Status**:
   - **Action**: The generation workflow (e.g., `/process_generation`) is responsible for updating the component's status in its matrix to `Implemented` and adding a link to the `src/` implementation directory upon successful completion.

---

## Phase 4: ACT - Verify, Standardize, and Iterate

**Objective**: To confirm the implemented solution meets its objectives, standardize any learnings, and feed insights back into the ecosystem.

1. **Monitor & Validate**: Return to Phase 1 (CHECK) to monitor KPIs and metrics to ensure the change had the desired positive impact.

2. **Update Landscape Diagrams**:
   - **Action**: If the change affects the overall architecture, an agent updates the master landscape diagrams.
   - **Workflow**: Execute `/2_operational_tasks/documentation/update_master_process_landscape.md` or other relevant diagram workflows.

3. **Standardize Learnings as New Rules or Patterns**:
   - **Action**: If the project revealed a new best practice or reusable pattern, an architect agent formalizes it.
   - **Workflows**:
     - Execute `/1_component_lifecycle/1_define/pattern_definition.md`.
     - Execute `/1_component_lifecycle/1_define/rule_definition.md` to add a new rule to `.windsurf/rules/`.

4. **Commit All Changes**:
   - **Action**: Commit all new and modified files to the repository with a standardized message.
   - **Tool**: `run_command`
   - **Example**:

     ```markdown
     <!-- run_command('git commit -am "feat(scope): Describe the change and its impact"') -->
     ```
