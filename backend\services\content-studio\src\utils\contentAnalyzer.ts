import { logger } from './logger';

export interface ContentAnalysisResult {
  readabilityScore: number;
  sentimentScore: number;
  keywordDensity: { [keyword: string]: number };
  wordCount: number;
  readingTime: number;
  tone: string;
  complexity: string;
  suggestions: string[];
}

export interface CompetitorAnalysisResult {
  similarities: string[];
  differences: string[];
  uniquenessScore: number;
  recommendations: string[];
}

export class ContentAnalyzer {
  constructor() {
    logger.info('ContentAnalyzer initialized');
  }

  async analyzeContent(content: string): Promise<ContentAnalysisResult> {
    try {
      const wordCount = this.getWordCount(content);
      const readingTime = this.calculateReadingTime(wordCount);
      const readabilityScore = this.calculateReadabilityScore(content);
      const sentimentScore = this.analyzeSentiment(content);
      const keywordDensity = this.calculateKeywordDensity(content);
      const tone = this.analyzeTone(content);
      const complexity = this.analyzeComplexity(content);
      const suggestions = this.generateSuggestions(content, readabilityScore, sentimentScore);

      return {
        readabilityScore,
        sentimentScore,
        keywordDensity,
        wordCount,
        readingTime,
        tone,
        complexity,
        suggestions,
      };
    } catch (error) {
      logger.error('Error analyzing content:', error);
      throw new Error(`Content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async compareWithCompetitors(content: string, competitorContent: string[]): Promise<CompetitorAnalysisResult> {
    try {
      const similarities: string[] = [];
      const differences: string[] = [];
      let uniquenessScore = 100;

      // Simple comparison logic - can be enhanced with more sophisticated algorithms
      for (const competitor of competitorContent) {
        const similarity = this.calculateSimilarity(content, competitor);
        if (similarity > 0.7) {
          similarities.push(`High similarity detected with competitor content (${Math.round(similarity * 100)}% match)`);
          uniquenessScore -= similarity * 20;
        }
      }

      if (similarities.length === 0) {
        differences.push('Content appears to be unique compared to analyzed competitors');
      }

      const recommendations = this.generateCompetitorRecommendations(uniquenessScore, similarities);

      return {
        similarities,
        differences,
        uniquenessScore: Math.max(0, uniquenessScore),
        recommendations,
      };
    } catch (error) {
      logger.error('Error comparing with competitors:', error);
      throw new Error(`Competitor analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getWordCount(content: string): number {
    return content.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private calculateReadingTime(wordCount: number): number {
    // Average reading speed: 200 words per minute
    return Math.ceil(wordCount / 200);
  }

  private calculateReadabilityScore(content: string): number {
    // Simplified Flesch Reading Ease calculation
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
    const words = this.getWordCount(content);
    const syllables = this.countSyllables(content);

    if (sentences === 0 || words === 0) return 0;

    const score = 206.835 - (1.015 * (words / sentences)) - (84.6 * (syllables / words));
    return Math.max(0, Math.min(100, score));
  }

  private countSyllables(content: string): number {
    // Simple syllable counting - can be improved
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    let syllableCount = 0;

    for (const word of words) {
      const vowels = word.match(/[aeiouy]+/g);
      syllableCount += vowels ? vowels.length : 1;
    }

    return syllableCount;
  }

  private analyzeSentiment(content: string): number {
    // Simple sentiment analysis - can be enhanced with ML models
    const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'best', 'perfect', 'awesome'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'worst', 'horrible', 'disappointing', 'poor', 'failed', 'wrong'];

    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    let positiveCount = 0;
    let negativeCount = 0;

    for (const word of words) {
      if (positiveWords.includes(word)) positiveCount++;
      if (negativeWords.includes(word)) negativeCount++;
    }

    const totalSentimentWords = positiveCount + negativeCount;
    if (totalSentimentWords === 0) return 0; // Neutral

    return ((positiveCount - negativeCount) / totalSentimentWords) * 100;
  }

  private calculateKeywordDensity(content: string): { [keyword: string]: number } {
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const wordCount = words.length;
    const frequency: { [word: string]: number } = {};

    for (const word of words) {
      if (word.length > 3) { // Only consider words longer than 3 characters
        frequency[word] = (frequency[word] || 0) + 1;
      }
    }

    const density: { [keyword: string]: number } = {};
    for (const [word, count] of Object.entries(frequency)) {
      density[word] = (count / wordCount) * 100;
    }

    // Return top 10 keywords by density
    return Object.fromEntries(
      Object.entries(density)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
    );
  }

  private analyzeTone(content: string): string {
    const sentimentScore = this.analyzeSentiment(content);
    const formalWords = ['therefore', 'furthermore', 'consequently', 'moreover', 'nevertheless'];
    const casualWords = ['hey', 'awesome', 'cool', 'yeah', 'totally', 'super'];
    
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    const formalCount = words.filter(word => formalWords.includes(word)).length;
    const casualCount = words.filter(word => casualWords.includes(word)).length;

    if (sentimentScore > 20) return 'Positive';
    if (sentimentScore < -20) return 'Negative';
    if (formalCount > casualCount) return 'Formal';
    if (casualCount > formalCount) return 'Casual';
    return 'Neutral';
  }

  private analyzeComplexity(content: string): string {
    const readabilityScore = this.calculateReadabilityScore(content);
    
    if (readabilityScore >= 80) return 'Very Easy';
    if (readabilityScore >= 70) return 'Easy';
    if (readabilityScore >= 60) return 'Fairly Easy';
    if (readabilityScore >= 50) return 'Standard';
    if (readabilityScore >= 30) return 'Fairly Difficult';
    if (readabilityScore >= 10) return 'Difficult';
    return 'Very Difficult';
  }

  private generateSuggestions(content: string, readabilityScore: number, sentimentScore: number): string[] {
    const suggestions: string[] = [];

    if (readabilityScore < 50) {
      suggestions.push('Consider using shorter sentences to improve readability');
      suggestions.push('Replace complex words with simpler alternatives');
    }

    if (sentimentScore < -10) {
      suggestions.push('Consider adding more positive language to improve tone');
    }

    if (this.getWordCount(content) < 100) {
      suggestions.push('Content might be too short - consider expanding with more details');
    }

    if (this.getWordCount(content) > 2000) {
      suggestions.push('Content might be too long - consider breaking into smaller sections');
    }

    const exclamationCount = (content.match(/!/g) || []).length;
    if (exclamationCount > 5) {
      suggestions.push('Reduce the number of exclamation marks for a more professional tone');
    }

    return suggestions;
  }

  private calculateSimilarity(content1: string, content2: string): number {
    // Simple Jaccard similarity - can be enhanced with more sophisticated algorithms
    const words1 = new Set(content1.toLowerCase().match(/\b\w+\b/g) || []);
    const words2 = new Set(content2.toLowerCase().match(/\b\w+\b/g) || []);
    
    const intersection = new Set([...words1].filter(word => words2.has(word)));
    const union = new Set([...words1, ...words2]);
    
    return intersection.size / union.size;
  }

  private generateCompetitorRecommendations(uniquenessScore: number, similarities: string[]): string[] {
    const recommendations: string[] = [];

    if (uniquenessScore < 50) {
      recommendations.push('Content has high similarity to competitors - consider adding unique perspectives');
      recommendations.push('Include original research or case studies to differentiate');
    }

    if (similarities.length > 2) {
      recommendations.push('Multiple similarities detected - review competitor content and identify gaps');
    }

    if (uniquenessScore > 80) {
      recommendations.push('Content appears highly unique - great job on differentiation!');
    }

    return recommendations;
  }
}

export default ContentAnalyzer;