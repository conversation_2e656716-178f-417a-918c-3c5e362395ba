# CKO_M003: CuratedKnowledgeAsset

## 1. Metadata

*   **Data Model ID:** CKO_M003
*   **Data Model Name:** CuratedKnowledgeAsset
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A003_ContentCurationAgent, CKO_A004_KnowledgeAnalystAgent

## 2. Purpose

*   This data model represents a discrete unit of curated knowledge that has been processed, cleaned, structured, enriched, and validated. It is ready for storage in the ESTRATIX Vector Database and for use in analysis flows like `CKO_F002_KnowledgeAnalysisAndInsightGeneration`.

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime
from enum import Enum
import uuid

class AssetTypeEnum(str, Enum):
    ARTICLE = "Article"
    REPORT = "Report"
    RESEARCH_PAPER = "Research Paper"
    NEWS_ITEM = "News Item"
    BLOG_POST = "Blog Post"
    DATASET_SUMMARY = "Dataset Summary"
    PATENT_ABSTRACT = "Patent Abstract"
    REGULATORY_UPDATE = "Regulatory Update"
    FORUM_POST_SUMMARY = "Forum Post Summary"
    SOCIAL_MEDIA_TREND = "Social Media Trend"
    OTHER = "Other"

class SensitivityLevelEnum(str, Enum):
    PUBLIC = "Public"
    INTERNAL = "Internal"
    CONFIDENTIAL = "Confidential"
    STRICTLY_CONFIDENTIAL = "Strictly Confidential"

class CuratedKnowledgeAsset(BaseModel):
    asset_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the curated knowledge asset.")
    raw_content_cache_id: Optional[uuid.UUID] = Field(None, description="Identifier of the original RawContentCache (CKO_M002) from which this asset was derived, if applicable.")
    source_registry_id: uuid.UUID = Field(..., description="Identifier of the ultimate knowledge source from CKO_M001_KnowledgeSourceRegistry.")
    original_source_url: Union[HttpUrl, str] = Field(..., description="The most direct URL to the original content.")
    
    title: str = Field(..., description="Concise and descriptive title of the knowledge asset.")
    summary: str = Field(..., description="A brief summary (e.g., 2-3 sentences) of the key information in the asset.")
    full_text_cleaned: Optional[str] = Field(None, description="The cleaned and structured full text of the asset, if applicable (e.g., for articles, reports). May be omitted if asset is primarily structured data or a summary of a larger non-textual piece.")
    
    # Enrichment & Structuring
    asset_type: AssetTypeEnum = Field(..., description="Type of the knowledge asset.")
    keywords: List[str] = Field(default_factory=list, description="Key terms and concepts extracted from or assigned to the asset.")
    entities_extracted: Optional[Dict[str, List[str]]] = Field(None, description="Named entities extracted (e.g., {'PERSON': ['John Doe'], 'ORG': ['Acme Corp']}).")
    topics_assigned: List[str] = Field(default_factory=list, description="Assigned topics or categories relevant to ESTRATIX.")
    sentiment_score: Optional[float] = Field(None, description="Overall sentiment score (-1.0 to 1.0) if applicable and analyzed.")
    structured_data: Optional[Dict[str, Any]] = Field(None, description="Any structured data extracted or derived from the asset (e.g., key figures, tables as JSON).")
    
    # Provenance & Quality
    publication_date: Optional[datetime] = Field(None, description="Original publication date of the content, if available.")
    ingestion_date: datetime = Field(default_factory=datetime.utcnow, description="Date the asset was ingested and curated into ESTRATIX.")
    curation_agent_id: Optional[str] = Field(None, description="ID of the ESTRATIX agent (e.g., CKO_A003) responsible for curation.")
    quality_assessment: Optional[str] = Field(None, description="Notes on the quality, reliability, or any biases of the content.")
    confidence_score: float = Field(default=1.0, description="Confidence in the accuracy and relevance of the curated asset (0.0-1.0).")
    
    # Access & Usage
    sensitivity_level: SensitivityLevelEnum = Field(default=SensitivityLevelEnum.INTERNAL, description="Sensitivity level of the asset.")
    access_permissions: Optional[List[str]] = Field(None, description="Specific roles or agent groups permitted to access this asset (if more granular than sensitivity level).")
    usage_count: int = Field(default=0, description="How many times this asset has been accessed or used in analyses.")
    last_accessed_date: Optional[datetime] = Field(None, description="Timestamp of the last access.")
    
    # Vector Database Info
    vector_embedding_model: Optional[str] = Field(None, description="Name of the model used to generate the vector embedding for this asset.")
    # vector_id: Optional[str] = Field(None, description="ID of the vector in the vector database, if stored separately.") # Often asset_id itself is used.

    # Relationships
    related_asset_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="IDs of other CuratedKnowledgeAssets that are related to this one.")

    # Custom metadata for specific needs
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, asset-type-specific metadata.")

```

## 4. Field Descriptions

| Field Name                 | Type                             | Description                                                                                                | Required | Example Value(s)                                      |
|----------------------------|----------------------------------|------------------------------------------------------------------------------------------------------------|----------|-------------------------------------------------------|
| `asset_id`                 | `uuid.UUID`                      | Unique identifier for the curated knowledge asset.                                                         | Yes      | `"c1d2e3f4-a5b6-7890-1234-567890abcdef"`              |
| `raw_content_cache_id`     | `Optional[uuid.UUID]`            | Identifier of the original `CKO_M002` instance.                                                            | No       | `"a1b2c3d4-e5f6-7890-1234-567890abcdef"`              |
| `source_registry_id`       | `uuid.UUID`                      | Identifier of the ultimate knowledge source from `CKO_M001`.                                               | Yes      | `"123e4567-e89b-12d3-a456-************"`              |
| `original_source_url`      | `Union[HttpUrl, str]`            | The most direct URL to the original content.                                                               | Yes      | `"https://example.com/article/123"`                   |
| `title`                    | `str`                            | Concise and descriptive title of the knowledge asset.                                                      | Yes      | `"Breakthrough in AI Achieved"`                         |
| `summary`                  | `str`                            | A brief summary of the key information.                                                                    | Yes      | `"Researchers at XYZ University announced..."`          |
| `full_text_cleaned`        | `Optional[str]`                  | Cleaned and structured full text.                                                                          | No       | `"The full article text after removing ads..."`         |
| `asset_type`               | `AssetTypeEnum`                  | Type of the knowledge asset.                                                                               | Yes      | `"Article"`, `"Research Paper"`                       |
| `keywords`                 | `List[str]`                      | Key terms and concepts.                                                                                    | Yes      | `["AI", "neural networks", "deep learning"]`        |
| `entities_extracted`       | `Optional[Dict[str, List[str]]]` | Named entities extracted.                                                                                  | No       | `{"ORG": ["XYZ University"], "TECH": ["AI"]}`       |
| `topics_assigned`          | `List[str]`                      | Assigned topics or categories.                                                                             | Yes      | `["Technology/AI", "Academic Research"]`              |
| `sentiment_score`          | `Optional[float]`                | Overall sentiment score (-1.0 to 1.0).                                                                     | No       | `0.75`                                                |
| `structured_data`          | `Optional[Dict[str, Any]]`       | Any structured data extracted or derived.                                                                  | No       | `{"citation_count": 150, "key_finding": "..."}`    |
| `publication_date`         | `Optional[datetime]`             | Original publication date of the content.                                                                  | No       | `"2025-05-20T10:00:00Z"`                              |
| `ingestion_date`           | `datetime`                       | Date the asset was ingested and curated.                                                                   | Yes      | `"2025-05-26T20:00:00Z"`                              |
| `curation_agent_id`        | `Optional[str]`                  | ID of the ESTRATIX agent (e.g., `CKO_A003`) responsible for curation.                              | No       | `"CKO_A003_ContentCurationAgent"`                     |
| `quality_assessment`       | `Optional[str]`                  | Notes on the quality, reliability, or biases.                                                              | No       | `"Source is peer-reviewed and highly reputable."`       |
| `confidence_score`         | `float`                          | Confidence in the accuracy and relevance (0.0-1.0).                                                        | Yes      | `0.95`                                                |
| `sensitivity_level`        | `SensitivityLevelEnum`           | Sensitivity level of the asset.                                                                            | Yes      | `"Internal"`                                          |
| `access_permissions`       | `Optional[List[str]]`            | Specific roles or agent groups permitted access.                                                           | No       | `["CKO_Analysts", "CSO_Team"]`                      |
| `usage_count`              | `int`                            | How many times this asset has been accessed.                                                               | Yes      | `0`, `15`                                             |
| `last_accessed_date`       | `Optional[datetime]`             | Timestamp of the last access.                                                                              | No       | `"2025-05-26T21:00:00Z"`                              |
| `vector_embedding_model`   | `Optional[str]`                  | Name of the model used for vector embedding.                                                               | No       | `"text-embedding-ada-002"`                            |
| `related_asset_ids`        | `Optional[List[uuid.UUID]]`      | IDs of other related `CuratedKnowledgeAssets`.                                                             | No       | `["d4e5f6a7-..."]`                                   |
| `custom_fields`            | `Optional[Dict[str, Any]]`       | Flexible dictionary for additional metadata.                                                               | No       | `{"project_code": "ESTRATIX_AI_INIT"}`              |

## 5. Relationships to Other Data Models

*   **`raw_content_cache_id` (links to `CKO_M002_RawContentCache.cache_id`):** Indicates the raw content source for this curated asset.
*   **`source_registry_id` (links to `CKO_M001_KnowledgeSourceRegistry.source_id`):** Points to the ultimate origin of the information.
*   This is the primary data model for storage in the ESTRATIX Vector Database.
*   Serves as a key input for `CKO_M004_KnowledgeGraphNode`, `CKO_M005_InsightReport`, `CKO_M006_OpportunitySignal`, and `CKO_M007_ThreatAlert`.

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F001_ExternalKnowledgeIngestionAndCuration` (specifically `CKO_P006_ValidateAndStoreKnowledgeAsset`). Created by `CKO_A003_ContentCurationAgent`.
*   **Primary Consuming Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (multiple processes within this flow), `CKO_F00X_KnowledgeGraphConstruction`, Vector Database Indexing Process.
*   **Key Agents Interacting:** `CKO_A003_ContentCurationAgent` (creates), `CKO_A004_KnowledgeAnalystAgent` (reads, uses for analysis), various research/analytical agents from other COs (read).

## 7. Notes / Future Considerations

*   The `full_text_cleaned` field might be very large. Consider chunking strategies for vectorization if the full text is stored directly, or storing it in a document store and linking to it.
*   The `vector_embedding_model` field is crucial for consistency if embeddings are generated by different processes or updated over time.
*   Versioning of curated assets could be a future enhancement (e.g., if an article is updated and re-curated).
*   A more granular permission model might be needed beyond `sensitivity_level` and `access_permissions` for highly specific data access control.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                               |
| :------ | :--------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------ |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M003 to CKO_M003. Updated ID, version, owner, SME, and all internal KNO references to CKO. Updated Last Updated date. |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the CuratedKnowledgeAsset data model.                                                                        |
