# ESTRATIX Process Definition: Create Website Wireframes & UI Design Specification

## 1. Process Metadata

*   **Process ID:** `DES001`
*   **Process Name:** `Create Website Wireframes & UI Design Specification`
*   **Version:** `1.0`
*   **Creation Date:** `2025-05-10`
*   **Last Updated:** `2025-05-10`
*   **Owner/Maintainer:** `Design Team / UI-UX Lead`
*   **Status:** `Draft`
*   **Related ESTRATIX Global Rule(s):** `UI/UX Quality Guidelines, Design System Standards (ref: MEMORY[user_global])`

## 2. Purpose & Goal

*   **Purpose:** To meticulously translate the approved `Website Plan & Sitemap (PLN001)` into a detailed visual and functional blueprint for the website. This involves creating comprehensive wireframes, high-fidelity mockups, optionally interactive prototypes, and a complete UI Design Specification document. This process ensures a clear, buildable design that aligns with strategic goals and UX pillars defined in `PLN001`, preparing a solid foundation for the development phase (`DEV00X`).
*   **Goal(s):**
    *   **Visual Blueprint:** Produce clear, annotated wireframes for all key pages, templates, and user interface states, demonstrating information hierarchy and core functionality.
    *   **Aesthetic Realization:** Develop high-fidelity mockups that accurately represent the intended visual style, branding application, typography, color palettes, and overall aesthetic of the website, adhering to the UX pillars from `PLN001`.
    *   **Interactive Validation (Optional):** Create interactive Figma prototypes for key user flows to validate navigation, interaction patterns, and user experience concepts before development.
    *   **Developer Handoff Excellence:** Compile a comprehensive `UI Design Specification Document` detailing all visual assets, style guides (or references to existing design systems), component specifications, interaction behaviors, responsive design considerations, and accessibility guidelines to enable efficient and accurate development.
    *   **Tool Proficiency & Innovation:** Leverage Figma extensively (including the `Framelink Figma MCP` for asset management and data extraction) and explore opportunities for AI-assisted design tools (e.g., `@magicuidesign/mcp`) to enhance efficiency and creativity.
    *   **Stakeholder Alignment & Approval:** Secure client and internal stakeholder approval on the final UI design and specification document before proceeding to development.

## 3. Scope

*   **In Scope:**
    *   Interpretation and translation of `PLN001` outputs (Website Plan, Sitemap, Design Brief section).
    *   Detailed user flow mapping and refinement for key website interactions.
    *   Creation of low to mid-fidelity wireframes for all essential pages and UI states.
    *   Development of high-fidelity mockups reflecting the final visual design, including application of branding, color schemes, typography, and imagery placeholders.
    *   (Optional) Creation of interactive prototypes using Figma for critical user journeys.
    *   Defining responsive design breakpoints and specific guidelines for various screen sizes.
    *   Establishing accessibility guidelines and annotations (e.g., WCAG compliance considerations).
    *   Detailed specification of UI components, their states, and interaction patterns.
    *   Creation of the `UI Design Specification Document`.
    *   Systematic use of **Figma**, leveraging its features for design, prototyping, and collaboration, including **`Framelink Figma MCP` (`mcp2_get_figma_data`, `mcp2_download_figma_images`)**.
    *   Exploration and potential use of **`@magicuidesign/mcp`** or other generative AI tools for design ideation, component generation, or style exploration.
    *   Preparation of design assets for developer handoff (e.g., exportable icons, images, design tokens if applicable).
*   **Out of Scope:**
    *   Website strategy development, sitemap creation, or initial market research (covered in `PLN001`).
    *   Final content writing, copywriting, or sourcing of final imagery and video assets (covered in `CON001`).
    *   Full brand identity or logo design (assumed as an input or separate branding process).
    *   Website development, coding, or backend implementation (covered in `DEV00X`).
    *   Detailed usability testing of fully developed features (typically post-development or in later stages).
    *   Generation of full, runnable website code (HTML, CSS, JS project structure).

## 4. Triggers

*   **Approved `PLN001` Deliverables:** Formal sign-off and receipt of the complete `Website Strategy & Plan Document` (including the Design Brief section) and `Finalized Sitemap` from the `PLN001` process.
*   **Official Project Phase Transition:** Formal hand-off to the Design Team lead, and assignment of design resources within the ESTRATIX project management system.

## 5. Inputs

*   **Input Name:** `Approved Website Strategy & Plan Document (from PLN001)`
    *   **Type:** `Digital Document (Markdown, PDF)`
    *   **Source:** `PLN001 Output, ESTRATIX Project Management System`
    *   **Format/Constraints:** Must include the Design Brief section, UX Pillars, target audience, and strategic objectives.
*   **Input Name:** `Finalized Sitemap (Visual & Textual) (from PLN001)`
    *   **Type:** `Image File (PNG, SVG), Structured Text/XML, Figma Link`
    *   **Source:** `PLN001 Output, ESTRATIX Project Management System`
    *   **Format/Constraints:** Clearly defines all pages and navigation structure.
*   **Input Name:** `High-Level Content Structure Outline (from PLN001)`
    *   **Type:** `Digital Document / Spreadsheet`
    *   **Source:** `PLN001 Output`
    *   **Format/Constraints:** Outlines key content blocks for each page.
*   **Input Name:** `Client Onboarding Package / Brand Brief / Style Guides`
    *   **Type:** `Digital Documents, Image Files, Font Files, Figma Links`
    *   **Source:** `Client, PLN001 Inputs, ESTRATIX Brand Asset Library`
    *   **Format/Constraints:** Contains brand guidelines, logos, color palettes, typography, existing style guides.
*   **Input Name:** `(Optional) Initial Visual Concept/Mood Board (from PLN001)`
    *   **Type:** `Link to Figma file or Exported Image`
    *   **Source:** `PLN001 Output`
    *   **Format/Constraints:** Provides initial visual direction.
*   **Input Name:** `ESTRATIX Design Principles & Quality Standards`
    *   **Type:** `Links to ESTRATIX Documentation, Checklists`
    *   **Source:** `ESTRATIX Internal Knowledge Base`
    *   **Format/Constraints:** To ensure alignment with ESTRATIX quality benchmarks.
*   **Input Name:** `Feedback from PLN001 Review (if any)`
    *   **Type:** `Meeting Notes, Email Summaries`
    *   **Source:** `Client/Stakeholder Feedback during PLN001`

## 6. Outputs / Deliverables

*   **Output Name:** `Annotated Wireframes`
    *   **Type:** `Figma File Link, Exported PDFs`
    *   **Destination/Storage:** `ESTRATIX Project Management System, Figma Project File, DEV00X Inputs`
    *   **Format/Quality Criteria:** Clear, detailed, covers all key pages and states, includes annotations for functionality and content areas.
*   **Output Name:** `High-Fidelity Mockups`
    *   **Type:** `Figma File Link, Exported PNGs/JPGs`
    *   **Destination/Storage:** `ESTRATIX Project Management System, Figma Project File, Client Review Portal, DEV00X Inputs`
    *   **Format/Quality Criteria:** Visually polished, pixel-perfect representation, consistent branding, responsive views demonstrated.
*   **Output Name:** `(Optional) Interactive Prototype`
    *   **Type:** `Figma Prototype Link`
    *   **Destination/Storage:** `ESTRATIX Project Management System, Client Review Portal, DEV00X Inputs (for reference)`
    *   **Format/Quality Criteria:** Demonstrates key user flows and interactions smoothly.
*   **Output Name:** `UI Design Specification Document`
    *   **Type:** `Comprehensive Digital Document (Markdown, PDF, Confluence/Notion Page, or directly annotated Figma file sections)`
    *   **Destination/Storage:** `ESTRATIX Project Management System, DEV00X Inputs`
    *   **Format/Quality Criteria:** Details typography, color palettes, component specs, interaction guidelines, responsive behavior, accessibility notes, asset export guidelines.
*   **Output Name:** `Design Assets & Resources`
    *   **Type:** `Exportable SVGs, Icons, Image Placeholders, Font Information, (Potentially) Design Tokens (JSON/CSS)`
    *   **Destination/Storage:** `Shared Asset Library, DEV00X Inputs`
    *   **Format/Quality Criteria:** Well-organized, correctly formatted, and ready for development use. May include AI-generated code snippets for components as an accelerator for `DEV00X`.
*   **Output Name:** `Client/Stakeholder Approval Record for UI Design`
    *   **Type:** `Email Confirmation, Signed Document, Project Management System Update`
    *   **Destination/Storage:** `ESTRATIX Project Management System`
    *   **Format/Quality Criteria:** Explicit agreement on the final design deliverables.

## 7. High-Level Steps / Phases

    1.  **Initiation & Input Assimilation:** Receive and thoroughly review all inputs from `PLN001`, clarify any ambiguities. Set up Figma project.
    2.  **User Flow Analysis & Wireframing:** Detail key user flows. Create low/mid-fidelity wireframes for all pages and states. Conduct internal review and iteration on wireframes.
    3.  **Mood Boarding & Visual Style Definition (If not fully covered in PLN001):** Explore visual directions, create/refine mood boards. Select typography, color palettes. Present to client for early alignment if needed.
    4.  **High-Fidelity Mockup Design:** Translate approved wireframes into high-fidelity mockups in Figma. Design UI components, apply branding, and ensure visual consistency.
    5.  **(Optional) Interactive Prototyping:** Develop interactive prototypes in Figma for key user flows to demonstrate and validate interactions.
    6.  **Responsive Design & Accessibility Specifications:** Define responsive breakpoints and design adaptations for different screen sizes. Annotate designs with accessibility considerations (e.g., ARIA roles, contrast ratios).
    7.  **UI Design Specification Document Compilation:** Consolidate all design information (styles, components, interactions, assets, responsive, accessibility) into the comprehensive UI Design Specification Document.
    8.  **Internal Review & Quality Assurance:** Conduct a thorough internal review of all design deliverables for completeness, consistency, and adherence to standards.
    9.  **Client/Stakeholder Presentation, Feedback & Iteration:** Present final designs and specification document. Gather feedback, make necessary revisions.
    10. **Final Approval & Handoff Preparation:** Obtain formal sign-off. Organize and prepare all assets and documentation for handoff to `DEV00X` and `CON001`.

## 8. Key Tools, Technologies, & MCPs Involved

*   **Primary Design & Prototyping:** **Figma**
    *   **`Framelink Figma MCP`**: `mcp2_get_figma_data` (to analyze Figma file structure/components), `mcp2_download_figma_images` (to extract assets).
*   **AI-Assisted Design (Exploratory/Supportive):**
    *   **`@magicuidesign/mcp`**: For generating design ideas, UI components, or style variations.
    *   Other generative AI tools for image generation, pattern creation, etc.
*   **Collaboration & Documentation:** Notion, Confluence, Google Workspace (Docs, Slides, Sheets), Markdown Editors.
*   **Project Management:** ESTRATIX PM System, Linear.
*   **Communication:** Email, Slack (via `slack` MCP), Video Conferencing.
*   **Asset Management:** Shared Cloud Storage (e.g., Google Drive, Dropbox), Figma Asset Libraries.
*   **Windsurf MCPs/Tools (Conceptual for Agentic Execution):** `read_file_content` (for inputs), `write_to_file` (for spec doc), `mcp2_get_figma_data`, `mcp2_download_figma_images`, `@magicuidesign/mcp`, `create_memory`, `suggested_responses`.

## 9. Roles & Responsibilities

*   **Role:** `UI/UX Design Lead (Agent or Human)`
    *   **Responsibilities:** Overseeing the DES001 process, ensuring design quality and consistency, team coordination, primary client-facing designer.
*   **Role:** `UI/UX Designer(s) (Agent or Human)`
    *   **Responsibilities:** Executing design tasks (wireframing, mockups, prototyping), creating specifications, collaborating with the lead and other stakeholders.
*   **Role:** `Client / Stakeholder`
    *   **Responsibilities:** Providing brand assets, participating in design reviews, providing timely feedback, approving final designs.
*   **Role:** `(Consulted) Lead Developer / Tech Lead`
    *   **Responsibilities:** Providing feedback on technical feasibility of designs, clarifying requirements for handoff.
*   **Role:** `(Consulted) Content Strategist (from CON001)`
    *   **Responsibilities:** Providing input on how content will fit into designs, ensuring designs accommodate content needs.

## 10. Metrics & KPIs

*   **Design Approval Rate:** (Number of designs approved on first/second review / Total designs submitted).
*   **Time to Design Completion:** (From process initiation to final client approval).
*   **Specification Clarity & Completeness Score:** (Developer feedback rating or internal QA score).
*   **Number of Design Iterations:** (Per project or major feature).
*   **Adherence to `PLN001` UX Pillars:** (Internal review against defined pillars).

## 11. Dependencies

*   **Upstream Processes:** `PLN001_DefineWebsitePlan&Sitemap` (critical for all inputs).
*   **Downstream Processes:** `DEV00X_ApplicationDevelopment` (primary consumer), `CON001_DevelopWebsiteContentStrategy` (designs inform final content needs and vice-versa).
*   **External Systems/Services:** Client's brand asset repositories, ESTRATIX PM & documentation tools, Figma platform.

## 12. Agentic Framework Mapping

    ### A. Windsurf Workflow Mapping:
    *   **Associated Windsurf Workflow File:** `docs/processes/workflows/DES001_CreateUIDesign_WF.md` (To be created)
    *   **Key Parameters / User Inputs:** Approved PLN001 documentation, Figma project link.
    *   **Primary Windsurf Tools Used:** `read_file_content`, `write_to_file`, `mcp2_get_figma_data`, `mcp2_download_figma_images`, `@magicuidesign/mcp`, `create_memory`.

    ### B. CrewAI Mapping (Conceptual):
    *   **Potential Crew(s):** `UIDesignCrew`, `FigmaAutomationCrew`
    *   **Agent Role(s) involved:** `WireframeDesignerAgent`, `MockupArtistAgent`, `FigmaPrototypeAgent`, `AccessibilityCheckerAgent`, `DesignSpecWriterAgent`, `FigmaAssetExporterAgent` (using MCPs).
    *   **Agent Tools:** Figma interaction tools (wrapping MCPs), `@magicuidesign/mcp`, documentation tools, design validation tools.

    ### C. Pydantic-AI Mapping (Conceptual):
    *   **Relevant Pydantic Models:** `DesignInputBrief`, `WireframeModel`, `ComponentSpec`, `StyleGuideModel`, `UIDesignOutput`.
    *   **Core Logic Function Signature:** `async def generate_ui_design_specification(plan: DesignInputBrief, figma_project_url: str) -> UIDesignOutput:`

    ### D. PocketFlow Mapping (Conceptual):
    *   **Flow Definition:** Sequence of states: `InputReview`, `Wireframing`, `MockupDesign`, `Prototyping`, `SpecCompilation`, `ReviewLoop`, `FinalApproval`.
    *   **Transitions:** Driven by task completion, tool outputs (e.g., Figma export ready), stakeholder feedback.

    ### E. Aider Integration Points:
    *   `/edit Create a basic wireframe structure in markdown for a homepage with [header, hero, 3-features, footer].`
    *   `/ask Based on this Figma layer structure (pasted), suggest a CSS class naming convention.`
    *   `/edit Document the color palette used in this Figma screenshot: [screenshot path or description].`
    *   `/ask What are key accessibility considerations for a navigation menu?`

    ### F. Multi-Framework Agent Training Strategy (ESTRATIX Guiding Principle):
    *   Specialized "Design Agents" trained on Figma best practices, UI/UX principles, and usage of `@magicuidesign/mcp` and `Framelink Figma MCP` tools. Training data to include successful design projects and specification documents.

## 13. Exception Handling & Error Conditions

*   **Ambiguous or Incomplete Inputs from `PLN001`:** Halt process, revert to `PLN001` owner for clarification or revision.
*   **Client Disagreement on Visual Direction:** Facilitate additional mood boarding/style exploration. Document differing opinions and iterate. Escalate if impasse.
*   **Technical Feasibility Issues with Design:** Collaborate with development lead (from `DEV00X`) early and often. Adjust design as needed.
*   **Scope Creep during Design Reviews:** Refer back to approved `PLN001` scope. Manage changes via a formal change request process if significant.
*   **Tooling Issues (Figma, MCPs):** Report issues to tool providers/internal support. Have contingency plans for manual workarounds if possible.

## 14. PDCA (Plan-Do-Check-Act) & Continuous Improvement

*   **Review Cycle:** After every major website design project, or quarterly.
*   **Improvement Log:**
    *   **Date:** `2025-05-10` | **Issue/Observation:** `Initial version.` | **Proposed Change:** `N/A` | **Action Taken:** `N/A` | **Result:** `N/A`
    *   Track feedback from development teams on the clarity and usability of design specifications.
    *   Evaluate the effectiveness and efficiency gains from using AI-assisted design tools and MCPs.
    *   Update design templates, checklists, and component libraries based on lessons learned.

## 15. Diagram Reference

*   **Mermaid Diagram:** `[docs/processes/diagrams/DES001_CreateWebsiteWireframes&UIDesignSpecification.mmd](./../diagrams/DES001_CreateWebsiteWireframes&UIDesignSpecification.mmd)` (To be created)

## 16. Notes & Assumptions

*   Assumes necessary brand guidelines and assets are available and clearly defined.
*   The depth of prototyping and specification can be scaled based on project complexity and budget.
*   Close collaboration with `CON001` (Content) and `DEV00X` (Development) is essential throughout this process.
*   Assumes access to and proficiency with Figma and other listed design tools.
