{"name": "@estratix/agent-orchestration", "version": "1.0.0", "description": "ESTRATIX Agent Orchestration Service - Coordinates agentic workflows and multi-agent collaboration", "main": "dist/index.js", "scripts": {"dev": "ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit"}, "keywords": ["estratix", "agent-orchestration", "multi-agent", "workflow", "coordination", "agentic"], "author": "ESTRATIX Team", "license": "MIT", "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/multipart": "^8.3.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.1.0", "axios": "^1.7.7", "bull": "^4.16.3", "dotenv": "^16.4.5", "fastify": "^4.28.1", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.3.5", "openai": "^4.67.3", "pino": "^9.4.0", "pino-pretty": "^11.2.2", "uuid": "^10.0.0", "ws": "^8.18.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.7.4", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^9.11.1", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.6.2"}, "engines": {"node": ">=18.0.0"}}