import fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import multipart from '@fastify/multipart';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';
import '@/types/fastify';
import { requestLogging, responseLogging } from '@/middleware/logging';
import { authenticateToken } from '@/middleware/auth';

// Import routes
import healthRoutes from '@/routes/health';
import agentsRoutes from '@/routes/agents';
import { workflowRoutes } from '@/routes/workflows';
import { orchestrationRoutes } from '@/routes/orchestration';
import { analyticsRoutes } from '@/routes/analytics';
import { websocketRoutes } from '@/routes/websocket';

// Import services
import { AgentService } from '@/services/agentService';
import { WorkflowService } from '@/services/workflowService';
import { OrchestrationService } from '@/services/orchestrationService';
import { WebSocketService } from '@/services/webSocketService';
import { AnalyticsService } from '@/services/analyticsService';

// Create Fastify instance
const server = fastify({
  logger: logger,
  trustProxy: true,
  bodyLimit: 10485760, // 10MB
  keepAliveTimeout: 30000,
  connectionTimeout: 30000
});

// Global error handler
server.setErrorHandler(async (error, request, reply) => {
  server.log.error(error, 'Unhandled error');
  
  const statusCode = error.statusCode || 500;
  const message = environment.nodeEnv === 'production' 
    ? 'Internal Server Error' 
    : error.message;
  
  return reply.status(statusCode).send({
    error: true,
    message,
    statusCode,
    timestamp: new Date().toISOString(),
    path: request.url
  });
});

// Register plugins
async function registerPlugins() {
  // Security plugins
  await server.register(helmet, {
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  });
  
  await server.register(cors, {
    origin: environment.cors.origin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
  });
  
  // JWT plugin
  await server.register(jwt, {
    secret: environment.jwt.secret,
    sign: {
      expiresIn: environment.jwt.expiresIn
    }
  });
  
  // Multipart plugin for file uploads
  await server.register(multipart, {
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 5
    }
  });
  
  // Swagger documentation
  await server.register(swagger, {
    openapi: {
      openapi: '3.0.0',
      info: {
        title: 'ESTRATIX Agent Orchestration API',
        description: 'API for coordinating agentic workflows and multi-agent collaboration',
        version: '1.0.0',
        contact: {
          name: 'ESTRATIX Team',
          email: '<EMAIL>'
        }
      },
      servers: [
        {
          url: `http://${environment.host}:${environment.port}`,
          description: 'Development server'
        }
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT'
          }
        }
      },
      security: [
        {
          bearerAuth: []
        }
      ]
    }
  });
  
  await server.register(swaggerUi, {
    routePrefix: '/docs',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false
    },
    staticCSP: true,
    transformStaticCSP: (header) => header
  });
}

// Register middleware
async function registerMiddleware() {
  await server.register(requestLogging);
  await server.register(responseLogging);
  
  // Add authentication decorator
  server.decorate('authenticate', authenticateToken);
}

// Register routes
async function registerRoutes() {
  await server.register(healthRoutes, { prefix: '/health' });
  await server.register(agentsRoutes, { prefix: '/agents' });
  await server.register(workflowRoutes, { prefix: '/workflows' });
  await server.register(orchestrationRoutes, { prefix: '/orchestration' });
  await server.register(analyticsRoutes, { prefix: '/analytics' });
  await server.register(websocketRoutes, { prefix: '/ws' });
}

// Initialize services
async function initializeServices() {
  try {
    server.log.info('Initializing Agent Orchestration services...');
    
    // Initialize core services
    const agentService = new AgentService();
    const workflowService = new WorkflowService();
    const orchestrationService = new OrchestrationService(agentService, workflowService);
    const webSocketService = new WebSocketService();
    const analyticsService = new AnalyticsService();
    
    // Initialize services
    await agentService.initialize();
    await workflowService.initialize();
    await orchestrationService.initialize();
    await webSocketService.initialize();
    await analyticsService.initialize();
    
    // Decorate server with services
    server.decorate('agentService', agentService);
    server.decorate('workflowService', workflowService);
    server.decorate('orchestrationService', orchestrationService);
    server.decorate('webSocketService', webSocketService);
    server.decorate('analyticsService', analyticsService);
    
    server.log.info('All services initialized successfully');
  } catch (error) {
    server.log.error(error, 'Failed to initialize services');
    throw error;
  }
}

// Graceful shutdown
process.on('SIGTERM', async () => {
  server.log.info('SIGTERM received, shutting down gracefully');
  
  try {
    // Cleanup services
    if (server.agentService) await server.agentService.cleanup();
    if (server.workflowService) await server.workflowService.cleanup();
    if (server.orchestrationService) await server.orchestrationService.cleanup();
    if (server.webSocketService) await server.webSocketService.cleanup();
    if (server.analyticsService) await server.analyticsService.cleanup();
    
    await server.close();
    server.log.info('Server closed successfully');
    process.exit(0);
  } catch (error) {
    server.log.error(error, 'Error during shutdown');
    process.exit(1);
  }
});

// Start server
async function start() {
  try {
    server.log.info('Agent Orchestration service configuration loaded for development environment');
    server.log.info(`Environment: ${environment.nodeEnv}`);
    server.log.info(`Server will start on ${environment.host}:${environment.port}`);
    
    // Register everything
    await registerPlugins();
    await registerMiddleware();
    await initializeServices();
    await registerRoutes();
    
    // Start the server
    await server.listen({
      port: environment.port,
      host: environment.host
    });
    
    server.log.info(`🚀 Agent Orchestration service is running on http://${environment.host}:${environment.port}`);
    server.log.info(`📚 API Documentation available at http://${environment.host}:${environment.port}/docs`);
    
    if (environment.websocket.enabled) {
      server.log.info(`🔌 WebSocket server running on port ${environment.websocket.port}`);
    }
    
  } catch (error) {
    server.log.error(error, 'Failed to start server');
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  server.log.fatal(error, 'Uncaught exception');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  server.log.fatal({ reason, promise }, 'Unhandled rejection');
  process.exit(1);
});

// Start the application
start();

// Export for testing
export { server };
export default server;