# ESTRATIX Backend Services

## Overview

This backend infrastructure supports the ESTRATIX agency's productized services and client project management through a comprehensive suite of microservices aligned with our Command Officer organizational structure.

## Architecture

### Core Services
- **Content Studio API** - AI-powered content generation and management
- **Smart Contracts Service** - Blockchain integration and contract management
- **Project Management API** - Systematic project lifecycle management
- **Agent Orchestration Service** - Agentic workflow coordination
- **Client Onboarding API** - Automated client intake and RFP processing

### Command Officer Integration
- **CEO Service** - Strategic oversight and decision coordination
- **CTO Service** - Technical architecture and development management
- **CPrO Service** - Project management and delivery coordination
- **COO Service** - Operational workflow orchestration
- **CKO Service** - Knowledge management and research integration
- **CPOO Service** - Product operations and service delivery

## Technology Stack

- **Runtime**: Node.js with TypeScript
- **Framework**: Express.js with Fastify for high-performance APIs
- **Database**: PostgreSQL with Prisma ORM
- **Vector Database**: Milvus for embeddings and knowledge management
- **Message Queue**: Redis with <PERSON> for job processing
- **Blockchain**: Ethereum/Polygon integration with ethers.js
- **AI/ML**: OpenAI GPT-4, Anthropic Claude, and local LLM integration
- **Authentication**: JWT with role-based access control
- **Monitoring**: Prometheus with Grafana dashboards

## Service Architecture

```
backend/
├── services/
│   ├── content-studio/          # AI content generation
│   ├── smart-contracts/         # Blockchain services
│   ├── project-management/      # Project lifecycle
│   ├── agent-orchestration/     # Agentic workflows
│   ├── client-onboarding/       # RFP and intake
│   └── command-officers/        # Executive services
├── shared/
│   ├── database/               # Shared DB models
│   ├── auth/                   # Authentication
│   ├── utils/                  # Common utilities
│   └── types/                  # TypeScript definitions
└── infrastructure/
    ├── docker/                 # Container configs
    ├── k8s/                    # Kubernetes manifests
    └── monitoring/             # Observability
```

## Getting Started

1. **Environment Setup**
   ```bash
   cd backend
   npm install
   cp .env.example .env
   ```

2. **Database Setup**
   ```bash
   npm run db:setup
   npm run db:migrate
   npm run db:seed
   ```

3. **Start Services**
   ```bash
   npm run dev:all
   ```

## API Documentation

Each service exposes OpenAPI 3.0 documentation at `/docs` endpoint.

- Content Studio: `http://localhost:3001/docs`
- Smart Contracts: `http://localhost:3002/docs`
- Project Management: `http://localhost:3003/docs`
- Agent Orchestration: `http://localhost:3004/docs`
- Client Onboarding: `http://localhost:3005/docs`

## Development Guidelines

- Follow Command Officer organizational structure
- Implement matrix organizational patterns
- Use agentic workflow orchestration
- Maintain comprehensive documentation
- Apply systematic project management templates

## Deployment

Services are containerized and deployed using Kubernetes with GitOps workflows.

```bash
npm run deploy:staging
npm run deploy:production
```