# ESTRATIX Source Definition: [Source Name] ([ID])

## 1. Metadata

* **ID:** [SRC_ID] (e.g., SRC001)
* **Source Name:** [e.g., CrewAI GitHub Repo]
* **Version:** 1.0
* **Status:** (Active | Monitored | Archived)
* **Owner (Command Office):** CIO
* **Manager (Lead Agent/Role):** [e.g., AGENT_SourceScout_Lead]
* **Date Created:** YYYY-MM-DD
* **Last Updated:** YYYY-MM-DD

## 2. Overview

* **Purpose:** [Describe the type of information this source provides.]

## 3. Details

* **Type:** [e.g., GitHub Repository, Website, API, RSS Feed]
* **URL/Endpoint:** [Link to the source]
* **Update Frequency:** [e.g., Daily, Weekly, On-demand]

## 4. Monitoring

* **Monitoring Agent:** [e.g., AGENT_SourceMonitor]
* **Last Checked:** YYYY-MM-DD
* **Change Detected:** (Yes | No)

## 5. Associated Documents

* [List documents that have been ingested from this source.]
  * [Link to Document Definition 1]

## 6. Revision History

| Version | Date       | Author        | Changes                                     |
| :------ | :--------- | :------------ | :------------------------------------------ |
| 1.0     | YYYY-MM-DD | [Author Name] | Initial Definition                          |
