# Python Backend Coding Practices

This document outlines the core coding standards and practices for Python backend development within the ESTRATIX framework. Adherence to these rules ensures consistency, maintainability, and quality across the project.

## 1. Core Principles

- **SOLID:** All code should adhere to the SOLID principles of object-oriented design.
- **DRY (Don't Repeat Yourself):** Avoid code duplication. Abstract and reuse common logic and components.
- **KISS (Keep It Simple, Stupid):** Prefer simple, clear, and straightforward solutions over complex ones.

## 2. Naming Conventions

- **Files, Functions, and Variables:** Use snake_case (e.g., my_variable, calculate_value()).
- **Classes:** Use PascalCase (e.g., AgentService, DataModel).
- **Constants:** Use UPPER_CASE_SNAKE_CASE (e.g., MAX_RETRIES, API_ENDPOINT).
- **Implementations:** Suffix implementations with their type or framework (e.g., FastAPIRouter, QdrantKnowledgeStore).

## 3. Type Hinting

- **Mandatory:** All function signatures, class attributes, and variables must include type hints as per PEP 484.
- **Clarity:** Use clear and specific types (e.g., list[str] instead of list).

## 4. Modularity and Single Responsibility

- **Atomic Components:** Design functions and classes to have a single, well-defined responsibility.
- **File Organization:** Keep files focused. If a file grows too large or handles too many concerns, refactor it into smaller, more specialized modules.
- **Public Interfaces:** Use __init__.py files to define the public API of a package, exporting only the necessary classes and functions.
