import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { validateCreateOnboardingFlow, ValidationError } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { OnboardingFlow, CreateOnboardingFlowRequest } from '@/types';

export interface GetOnboardingFlowsQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  type?: string;
  clientId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface OnboardingFlowParams {
  id: string;
}

export interface OnboardingStepParams {
  id: string;
  stepId: string;
}

export interface CompleteStepBody {
  data?: Record<string, any>;
  notes?: string;
}

export interface SkipStepBody {
  reason: string;
}

export async function onboardingRoutes(fastify: FastifyInstance) {
  // Get all onboarding flows with filtering and pagination
  fastify.get('/onboarding', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get all onboarding flows with filtering and pagination',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          search: { type: 'string' },
          status: { type: 'string', enum: ['not_started', 'in_progress', 'completed', 'paused'] },
          type: { type: 'string', enum: ['basic', 'premium', 'enterprise', 'custom'] },
          clientId: { type: 'string', format: 'uuid' },
          sortBy: { type: 'string', enum: ['type', 'status', 'progress', 'startedAt', 'completedAt', 'createdAt'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            flows: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  clientId: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  totalSteps: { type: 'integer' },
                  completedSteps: { type: 'integer' },
                  currentStep: { type: 'integer' },
                  startedAt: { type: 'string' },
                  completedAt: { type: 'string' },
                  estimatedDuration: { type: 'integer' },
                  actualDuration: { type: 'integer' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                pages: { type: 'integer' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetOnboardingFlowsQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10, search, status, type, clientId, sortBy = 'createdAt', sortOrder = 'desc' } = request.query;
      
      const result = await fastify.onboardingService.getOnboardingFlows({
        page,
        limit,
        search,
        status,
        type,
        clientId,
        sortBy,
        sortOrder
      });

      logger.info('Onboarding flows retrieved', {
        userId: (request as any).user?.id,
        count: result.flows.length,
        page,
        limit
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to get onboarding flows', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve onboarding flows' });
    }
  });

  // Get onboarding flow by ID
  fastify.get('/onboarding/:id', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get onboarding flow by ID',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            clientId: { type: 'string' },
            type: { type: 'string' },
            status: { type: 'string' },
            totalSteps: { type: 'integer' },
            completedSteps: { type: 'integer' },
            currentStep: { type: 'integer' },
            startedAt: { type: 'string' },
            completedAt: { type: 'string' },
            estimatedDuration: { type: 'integer' },
            actualDuration: { type: 'integer' },
            steps: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  title: { type: 'string' },
                  description: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  order: { type: 'integer' },
                  required: { type: 'boolean' },
                  estimatedDuration: { type: 'integer' },
                  actualDuration: { type: 'integer' },
                  startedAt: { type: 'string' },
                  completedAt: { type: 'string' },
                  skippedAt: { type: 'string' },
                  skipReason: { type: 'string' },
                  data: { type: 'object' },
                  notes: { type: 'string' },
                  formFields: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        id: { type: 'string' },
                        label: { type: 'string' },
                        type: { type: 'string' },
                        required: { type: 'boolean' },
                        options: { type: 'array', items: { type: 'string' } },
                        validation: { type: 'object' },
                        value: {},
                        placeholder: { type: 'string' },
                        helpText: { type: 'string' }
                      }
                    }
                  }
                }
              }
            },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: OnboardingFlowParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const flow = await fastify.onboardingService.getOnboardingFlowById(id);
      
      if (!flow) {
        return reply.status(404).send({ error: 'Onboarding flow not found' });
      }

      logger.info('Onboarding flow retrieved', {
        userId: (request as any).user?.id,
        flowId: id
      });

      return reply.status(200).send(flow);
    } catch (error) {
      logger.error('Failed to get onboarding flow', { error, flowId: request.params.id });
      return reply.status(500).send({ error: 'Failed to retrieve onboarding flow' });
    }
  });

  // Create new onboarding flow
  fastify.post('/onboarding', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Create a new onboarding flow',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          clientId: { type: 'string', format: 'uuid' },
          type: { type: 'string', enum: ['basic', 'premium', 'enterprise', 'custom'] },
          customSteps: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                title: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                required: { type: 'boolean' },
                estimatedDuration: { type: 'integer' },
                formFields: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      label: { type: 'string' },
                      type: { type: 'string' },
                      required: { type: 'boolean' },
                      options: { type: 'array', items: { type: 'string' } },
                      validation: { type: 'object' },
                      placeholder: { type: 'string' },
                      helpText: { type: 'string' }
                    }
                  }
                }
              }
            }
          }
        },
        required: ['clientId', 'type']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            clientId: { type: 'string' },
            type: { type: 'string' },
            status: { type: 'string' },
            totalSteps: { type: 'integer' },
            completedSteps: { type: 'integer' },
            currentStep: { type: 'integer' },
            estimatedDuration: { type: 'integer' },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'string' } }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateOnboardingFlowRequest }>, reply: FastifyReply) => {
    try {
      // Validate request body
      const validationResult = validateCreateOnboardingFlow(request.body);
      if (!validationResult.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validationResult.error.errors.map(err => err.message)
        });
      }

      // Verify client exists
      const client = await fastify.clientService.getClientById(request.body.clientId);
      if (!client) {
        return reply.status(400).send({ error: 'Client not found' });
      }

      const flow = await fastify.onboardingService.createOnboardingFlow(request.body);

      // Send onboarding started email
      try {
        await fastify.emailService.sendOnboardingStarted(flow, client.email);
      } catch (emailError) {
        logger.warn('Failed to send onboarding started email', { error: emailError, flowId: flow.id });
      }

      logger.info('Onboarding flow created', {
        userId: (request as any).user?.id,
        flowId: flow.id,
        clientId: flow.clientId,
        type: flow.type
      });

      return reply.status(201).send(flow);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to create onboarding flow', { error, body: request.body });
      return reply.status(500).send({ error: 'Failed to create onboarding flow' });
    }
  });

  // Start onboarding flow
  fastify.post('/onboarding/:id/start', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Start an onboarding flow',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            startedAt: { type: 'string' },
            currentStep: { type: 'integer' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: OnboardingFlowParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const result = await fastify.onboardingService.startOnboardingFlow(id);
      
      if (!result) {
        return reply.status(404).send({ error: 'Onboarding flow not found' });
      }

      logger.info('Onboarding flow started', {
        userId: (request as any).user?.id,
        flowId: id
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to start onboarding flow', { error, flowId: request.params.id });
      return reply.status(500).send({ error: 'Failed to start onboarding flow' });
    }
  });

  // Complete onboarding step
  fastify.post('/onboarding/:id/steps/:stepId/complete', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Complete an onboarding step',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          stepId: { type: 'string' }
        },
        required: ['id', 'stepId']
      },
      body: {
        type: 'object',
        properties: {
          data: { type: 'object' },
          notes: { type: 'string', maxLength: 1000 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            stepId: { type: 'string' },
            status: { type: 'string' },
            completedAt: { type: 'string' },
            flowProgress: {
              type: 'object',
              properties: {
                completedSteps: { type: 'integer' },
                totalSteps: { type: 'integer' },
                currentStep: { type: 'integer' },
                isCompleted: { type: 'boolean' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: OnboardingStepParams; Body: CompleteStepBody }>, reply: FastifyReply) => {
    try {
      const { id, stepId } = request.params;
      const { data, notes } = request.body;
      
      const result = await fastify.onboardingService.completeStep(id, stepId, data, notes);
      
      if (!result) {
        return reply.status(404).send({ error: 'Onboarding flow or step not found' });
      }

      // Check if onboarding is now complete
      if (result.flowProgress.isCompleted) {
        const flow = await fastify.onboardingService.getOnboardingFlowById(id);
        if (flow) {
          const client = await fastify.clientService.getClientById(flow.clientId);
          if (client) {
            try {
              await fastify.emailService.sendOnboardingCompleted(flow, client.email);
            } catch (emailError) {
              logger.warn('Failed to send onboarding completed email', { error: emailError, flowId: id });
            }
          }
        }
      }

      logger.info('Onboarding step completed', {
        userId: (request as any).user?.id,
        flowId: id,
        stepId,
        isFlowCompleted: result.flowProgress.isCompleted
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to complete onboarding step', { error, flowId: request.params.id, stepId: request.params.stepId });
      return reply.status(500).send({ error: 'Failed to complete onboarding step' });
    }
  });

  // Skip onboarding step
  fastify.post('/onboarding/:id/steps/:stepId/skip', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Skip an onboarding step',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          stepId: { type: 'string' }
        },
        required: ['id', 'stepId']
      },
      body: {
        type: 'object',
        properties: {
          reason: { type: 'string', minLength: 1, maxLength: 500 }
        },
        required: ['reason']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            stepId: { type: 'string' },
            status: { type: 'string' },
            skippedAt: { type: 'string' },
            skipReason: { type: 'string' },
            flowProgress: {
              type: 'object',
              properties: {
                completedSteps: { type: 'integer' },
                totalSteps: { type: 'integer' },
                currentStep: { type: 'integer' },
                isCompleted: { type: 'boolean' }
              }
            }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: OnboardingStepParams; Body: SkipStepBody }>, reply: FastifyReply) => {
    try {
      const { id, stepId } = request.params;
      const { reason } = request.body;
      
      const result = await fastify.onboardingService.skipStep(id, stepId, reason);
      
      if (!result) {
        return reply.status(404).send({ error: 'Onboarding flow or step not found' });
      }

      logger.info('Onboarding step skipped', {
        userId: (request as any).user?.id,
        flowId: id,
        stepId,
        reason
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to skip onboarding step', { error, flowId: request.params.id, stepId: request.params.stepId });
      return reply.status(500).send({ error: 'Failed to skip onboarding step' });
    }
  });

  // Get onboarding templates
  fastify.get('/onboarding/templates', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get available onboarding templates',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            templates: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  type: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  estimatedDuration: { type: 'integer' },
                  stepCount: { type: 'integer' },
                  steps: {
                    type: 'array',
                    items: {
                      type: 'object',
                      properties: {
                        title: { type: 'string' },
                        description: { type: 'string' },
                        type: { type: 'string' },
                        required: { type: 'boolean' },
                        estimatedDuration: { type: 'integer' }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const templates = await fastify.onboardingService.getOnboardingTemplates();

      logger.info('Onboarding templates retrieved', {
        userId: (request as any).user?.id,
        templateCount: templates.length
      });

      return reply.status(200).send({ templates });
    } catch (error) {
      logger.error('Failed to get onboarding templates', { error });
      return reply.status(500).send({ error: 'Failed to retrieve onboarding templates' });
    }
  });

  // Get onboarding statistics
  fastify.get('/onboarding/stats', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get onboarding statistics',
      tags: ['Onboarding'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            notStarted: { type: 'integer' },
            inProgress: { type: 'integer' },
            completed: { type: 'integer' },
            paused: { type: 'integer' },
            completionRate: { type: 'number' },
            averageCompletionTime: { type: 'number' },
            byType: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await fastify.onboardingService.getOnboardingStats();

      logger.info('Onboarding statistics retrieved', {
        userId: (request as any).user?.id,
        total: stats.total
      });

      return reply.status(200).send(stats);
    } catch (error) {
      logger.error('Failed to get onboarding statistics', { error });
      return reply.status(500).send({ error: 'Failed to retrieve onboarding statistics' });
    }
  });
}