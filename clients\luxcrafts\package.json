{"name": "luxcrafts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node --max-old-space-size=32768 --max-semi-space-size=1024 node_modules/vite/bin/vite.js build", "build:tsc": "tsc -b && npm run build", "lint": "eslint .", "preview": "vite preview", "check": "tsc -b --noEmit"}, "dependencies": {"@headlessui/react": "^2.0.4", "@heroicons/react": "^2.1.5", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@rainbow-me/rainbowkit": "^2.1.6", "@react-three/drei": "^9.114.3", "@react-three/fiber": "^8.17.10", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.28", "@tanstack/react-query": "^5.59.0", "@wagmi/core": "^2.13.8", "@web3modal/wagmi": "^5.1.11", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "ethers": "^6.13.4", "framer-motion": "^11.11.17", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "i18next-http-backend": "^2.6.1", "lucide-react": "^0.511.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^6.0.0", "react-hook-form": "^7.53.2", "react-i18next": "^15.0.2", "react-router-dom": "^7.3.0", "recharts": "^2.12.7", "sonner": "^2.0.2", "stripe": "^17.3.1", "tailwind-merge": "^3.3.1", "three": "^0.170.0", "viem": "^2.21.19", "wagmi": "^2.12.17", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "babel-plugin-react-dev-locator": "^1.0.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-plugin-trae-solo-badge": "^1.0.0", "vite-tsconfig-paths": "^5.1.4"}}