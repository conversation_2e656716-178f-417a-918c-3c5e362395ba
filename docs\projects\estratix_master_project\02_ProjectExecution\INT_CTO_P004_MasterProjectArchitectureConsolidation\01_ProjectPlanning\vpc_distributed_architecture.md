# VPC Distributed Systems Architecture

## Multi-VPS Kubernetes Cluster Configuration

This document outlines the comprehensive architecture for deploying a distributed VPC system with multiple VPS instances, Kubernetes clusters, and automated CI/CD workflows using Coolify and other modern DevOps tools.

## 1. Architecture Overview

### 1.1 Infrastructure Topology

```
┌─────────────────────────────────────────────────────────────────┐
│                        VPC Network (10.0.0.0/16)               │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Control Plane │  │   Worker Node 1 │  │   Worker Node 2 │  │
│  │   (Master VPS)  │  │     (VPS-1)     │  │     (VPS-2)     │  │
│  │  *********      │  │   *********     │  │   *********     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│           │                     │                     │          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Monitoring    │  │   CI/CD Server  │  │   Load Balancer │  │
│  │   (Grafana)     │  │   (Coolify)     │  │   (Traefik)     │  │
│  │  10.0.2.10      │  │   10.0.2.11     │  │   10.0.2.12     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 Component Distribution

- **Control Plane VPS**: Kubernetes master, etcd, API server
- **Worker VPS Nodes**: Application workloads, container runtime
- **Monitoring VPS**: Prometheus, Grafana, AlertManager
- **CI/CD VPS**: Coolify, GitLab Runner, Docker Registry
- **Load Balancer VPS**: Traefik, Nginx, SSL termination

## 2. VPS Configuration Matrix

### 2.1 Hardware Specifications

| Role | CPU | RAM | Storage | Network | OS |
|------|-----|-----|---------|---------|----|
| Control Plane | 4 vCPU | 8GB | 100GB SSD | 1Gbps | Ubuntu 22.04 LTS |
| Worker Node | 8 vCPU | 16GB | 200GB SSD | 1Gbps | Ubuntu 22.04 LTS |
| Monitoring | 4 vCPU | 8GB | 500GB SSD | 1Gbps | Ubuntu 22.04 LTS |
| CI/CD | 6 vCPU | 12GB | 300GB SSD | 1Gbps | Ubuntu 22.04 LTS |
| Load Balancer | 2 vCPU | 4GB | 50GB SSD | 1Gbps | Ubuntu 22.04 LTS |

### 2.2 Network Configuration

```yaml
vpc_config:
  cidr: "10.0.0.0/16"
  subnets:
    control_plane: "********/24"
    workers: "********/24"
    services: "10.0.2.0/24"
    monitoring: "10.0.3.0/24"
  
  security_groups:
    kubernetes:
      ingress:
        - port: 6443  # API Server
        - port: 2379-2380  # etcd
        - port: 10250  # kubelet
        - port: 10251  # kube-scheduler
        - port: 10252  # kube-controller-manager
    
    applications:
      ingress:
        - port: 80    # HTTP
        - port: 443   # HTTPS
        - port: 30000-32767  # NodePort range
    
    monitoring:
      ingress:
        - port: 9090  # Prometheus
        - port: 3000  # Grafana
        - port: 9093  # AlertManager
```

## 3. Kubernetes Multi-Cluster Setup

### 3.1 Cluster Initialization Script

```bash
#!/bin/bash
# kubernetes_multi_cluster_init.sh

set -e

# Configuration
CLUSTER_NAME="estratix-cluster"
POD_CIDR="***********/16"
SERVICE_CIDR="*********/12"
API_SERVER_ENDPOINT="*********:6443"

# Initialize control plane
init_control_plane() {
    echo "Initializing Kubernetes control plane..."
    
    kubeadm init \
        --cluster-name="$CLUSTER_NAME" \
        --pod-network-cidr="$POD_CIDR" \
        --service-cidr="$SERVICE_CIDR" \
        --apiserver-advertise-address="*********" \
        --apiserver-cert-extra-sans="*********,estratix.local" \
        --node-name="control-plane-1"
    
    # Configure kubectl for root
    mkdir -p $HOME/.kube
    cp -i /etc/kubernetes/admin.conf $HOME/.kube/config
    chown $(id -u):$(id -g) $HOME/.kube/config
    
    # Install Calico CNI
    kubectl apply -f https://raw.githubusercontent.com/projectcalico/calico/v3.26.1/manifests/calico.yaml
    
    # Generate join command
    kubeadm token create --print-join-command > /tmp/join-command.sh
    chmod +x /tmp/join-command.sh
    
    echo "Control plane initialized successfully!"
}

# Join worker node
join_worker() {
    local JOIN_COMMAND="$1"
    echo "Joining worker node to cluster..."
    
    eval "$JOIN_COMMAND"
    
    echo "Worker node joined successfully!"
}

# Install cluster addons
install_addons() {
    echo "Installing cluster addons..."
    
    # Metrics Server
    kubectl apply -f https://github.com/kubernetes-sigs/metrics-server/releases/latest/download/components.yaml
    
    # Ingress Controller (Traefik)
    helm repo add traefik https://traefik.github.io/charts
    helm repo update
    helm install traefik traefik/traefik \
        --namespace traefik-system \
        --create-namespace \
        --set service.type=LoadBalancer
    
    # ArgoCD for GitOps
    kubectl create namespace argocd
    kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
    
    # Prometheus Operator
    helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
    helm install prometheus prometheus-community/kube-prometheus-stack \
        --namespace monitoring \
        --create-namespace
    
    echo "Addons installed successfully!"
}

# Main execution
case "$1" in
    "control-plane")
        init_control_plane
        install_addons
        ;;
    "worker")
        if [ -z "$2" ]; then
            echo "Usage: $0 worker <join-command>"
            exit 1
        fi
        join_worker "$2"
        ;;
    *)
        echo "Usage: $0 {control-plane|worker <join-command>}"
        exit 1
        ;;
esac
```

### 3.2 Multi-Cluster Management

```yaml
# cluster-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: cluster-config
  namespace: kube-system
data:
  clusters.yaml: |
    clusters:
      - name: estratix-prod
        endpoint: https://*********:6443
        region: us-east-1
        environment: production
        
      - name: estratix-staging
        endpoint: https://*********:6443
        region: us-east-1
        environment: staging
        
      - name: estratix-dev
        endpoint: https://*********:6443
        region: us-east-1
        environment: development
```

## 4. Coolify Installation and Configuration

### 4.1 Coolify Bootstrap Script

```bash
#!/bin/bash
# coolify_bootstrap.sh

set -e

COOLIFY_VERSION="4.0"
COOLIFY_DOMAIN="coolify.estratix.local"
COOLIFY_EMAIL="<EMAIL>"

echo "Installing Coolify v$COOLIFY_VERSION..."

# Prerequisites
apt update && apt upgrade -y
apt install -y curl wget git jq

# Install Docker
curl -fsSL https://get.docker.com | sh
systemctl enable docker
systemctl start docker
usermod -aG docker $USER

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Create Coolify directory
mkdir -p /opt/coolify
cd /opt/coolify

# Download Coolify
curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash

# Configure environment
cat > .env << EOF
APP_NAME=Coolify
APP_ID=$(openssl rand -hex 16)
APP_KEY=base64:$(openssl rand -base64 32)
DB_PASSWORD=$(openssl rand -base64 32)
REDIS_PASSWORD=$(openssl rand -base64 32)
PUSHER_APP_ID=$(openssl rand -hex 8)
PUSHER_APP_KEY=$(openssl rand -hex 16)
PUSHER_APP_SECRET=$(openssl rand -hex 16)
APP_URL=https://$COOLIFY_DOMAIN
APP_ENV=production
APP_DEBUG=false
LOG_CHANNEL=stderr
DB_CONNECTION=sqlite
DB_DATABASE=/data/coolify.sqlite
QUEUE_CONNECTION=redis
REDIS_HOST=redis
REDIS_PORT=6379
EOF

# Start Coolify
docker-compose up -d

echo "Coolify installed successfully!"
echo "Access URL: https://$COOLIFY_DOMAIN"
echo "Default credentials will be displayed on first access"
```

### 4.2 Coolify Project Configuration

```yaml
# coolify-projects.yaml
projects:
  - name: "estratix-frontend"
    description: "ESTRATIX Frontend Applications"
    environment: "production"
    git_repository: "https://github.com/estratix/frontend"
    build_command: "npm run build"
    start_command: "npm start"
    port: 3000
    domains:
      - "app.estratix.com"
      - "www.estratix.com"
    environment_variables:
      NODE_ENV: "production"
      API_URL: "https://api.estratix.com"
      
  - name: "estratix-api"
    description: "ESTRATIX Backend API"
    environment: "production"
    git_repository: "https://github.com/estratix/api"
    dockerfile: "Dockerfile"
    port: 8000
    domains:
      - "api.estratix.com"
    environment_variables:
      DATABASE_URL: "******************************/estratix"
      REDIS_URL: "redis://redis:6379"
      
  - name: "estratix-workers"
    description: "ESTRATIX Background Workers"
    environment: "production"
    git_repository: "https://github.com/estratix/workers"
    dockerfile: "Dockerfile.worker"
    replicas: 3
    environment_variables:
      QUEUE_URL: "redis://redis:6379"
      DATABASE_URL: "******************************/estratix"
```

### 4.3 Coolify Server Management

```bash
#!/bin/bash
# coolify_server_management.sh

# Add new server to Coolify
add_server() {
    local SERVER_NAME="$1"
    local SERVER_IP="$2"
    local SSH_KEY_PATH="$3"
    
    echo "Adding server $SERVER_NAME ($SERVER_IP) to Coolify..."
    
    # API call to add server
    curl -X POST "https://coolify.estratix.local/api/servers" \
        -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$SERVER_NAME\",
            \"ip\": \"$SERVER_IP\",
            \"user\": \"root\",
            \"private_key\": \"$(cat $SSH_KEY_PATH | base64 -w 0)\"
        }"
}

# Deploy application
deploy_application() {
    local PROJECT_NAME="$1"
    local SERVER_NAME="$2"
    
    echo "Deploying $PROJECT_NAME to $SERVER_NAME..."
    
    curl -X POST "https://coolify.estratix.local/api/projects/$PROJECT_NAME/deploy" \
        -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"server\": \"$SERVER_NAME\",
            \"force\": true
        }"
}

# Monitor deployment
monitor_deployment() {
    local DEPLOYMENT_ID="$1"
    
    while true; do
        STATUS=$(curl -s "https://coolify.estratix.local/api/deployments/$DEPLOYMENT_ID" \
            -H "Authorization: Bearer $COOLIFY_API_TOKEN" | jq -r '.status')
        
        echo "Deployment status: $STATUS"
        
        if [ "$STATUS" = "success" ] || [ "$STATUS" = "failed" ]; then
            break
        fi
        
        sleep 10
    done
}

# Usage examples
case "$1" in
    "add-server")
        add_server "$2" "$3" "$4"
        ;;
    "deploy")
        deploy_application "$2" "$3"
        ;;
    "monitor")
        monitor_deployment "$2"
        ;;
    *)
        echo "Usage: $0 {add-server|deploy|monitor} [args...]"
        exit 1
        ;;
esac
```

## 5. CI/CD Pipeline Configuration

### 5.1 GitLab CI Configuration

```yaml
# .gitlab-ci.yml
stages:
  - validate
  - build
  - test
  - security
  - deploy-staging
  - deploy-production

variables:
  DOCKER_REGISTRY: "registry.estratix.com"
  KUBERNETES_NAMESPACE: "estratix"
  COOLIFY_API_URL: "https://coolify.estratix.local/api"

# Validation stage
validate-code:
  stage: validate
  image: python:3.11
  script:
    - pip install pre-commit
    - pre-commit run --all-files
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Build stage
build-image:
  stage: build
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build -t $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA .
    - docker push $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
    - docker tag $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA $CI_REGISTRY_IMAGE:latest
    - docker push $CI_REGISTRY_IMAGE:latest
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Test stage
run-tests:
  stage: test
  image: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  script:
    - python -m pytest tests/ --cov=src/ --cov-report=xml
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Security scanning
security-scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 1 --severity HIGH,CRITICAL $CI_REGISTRY_IMAGE:$CI_COMMIT_SHA
  allow_failure: true
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Staging deployment
deploy-staging:
  stage: deploy-staging
  image: alpine/k8s:1.28.2
  script:
    - kubectl config use-context estratix-staging
    - envsubst < k8s/deployment.yaml | kubectl apply -f -
    - kubectl rollout status deployment/$CI_PROJECT_NAME -n $KUBERNETES_NAMESPACE
  environment:
    name: staging
    url: https://staging.estratix.com
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# Production deployment via Coolify
deploy-production:
  stage: deploy-production
  image: alpine/curl
  script:
    - |
      curl -X POST "$COOLIFY_API_URL/projects/$CI_PROJECT_NAME/deploy" \
        -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
          \"image\": \"$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA\",
          \"environment\": \"production\"
        }"
  environment:
    name: production
    url: https://estratix.com
  when: manual
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
```

### 5.2 GitHub Actions Workflow

```yaml
# .github/workflows/deploy.yml
name: Deploy to Kubernetes and Coolify

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      - name: Install dependencies
        run: |
          pip install pre-commit
          pre-commit install
      - name: Run pre-commit
        run: pre-commit run --all-files

  build:
    needs: validate
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image: ${{ steps.image.outputs.image }}
    steps:
      - uses: actions/checkout@v4
      
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
      
      - name: Output image
        id: image
        run: echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}" >> $GITHUB_OUTPUT

  test:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Run tests
        run: |
          docker run --rm ${{ needs.build.outputs.image }} python -m pytest

  deploy-staging:
    needs: [build, test]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging
    steps:
      - uses: actions/checkout@v4
      
      - name: Configure kubectl
        run: |
          mkdir -p $HOME/.kube
          echo "${{ secrets.KUBE_CONFIG_STAGING }}" | base64 -d > $HOME/.kube/config
      
      - name: Deploy to staging
        run: |
          envsubst < k8s/deployment.yaml | kubectl apply -f -
          kubectl rollout status deployment/${{ github.event.repository.name }} -n estratix
        env:
          IMAGE: ${{ needs.build.outputs.image }}
          ENVIRONMENT: staging

  deploy-production:
    needs: [build, test, deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
      - name: Deploy via Coolify
        run: |
          curl -X POST "${{ secrets.COOLIFY_API_URL }}/projects/${{ github.event.repository.name }}/deploy" \
            -H "Authorization: Bearer ${{ secrets.COOLIFY_API_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d "{
              \"image\": \"${{ needs.build.outputs.image }}\",
              \"environment\": \"production\"
            }"
```

## 6. Monitoring and Observability

### 6.1 Prometheus Configuration

```yaml
# prometheus-config.yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "estratix-rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
    - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
      action: keep
      regex: default;kubernetes;https

  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
    - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
    - action: labelmap
      regex: __meta_kubernetes_node_label_(.+)

  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
    - role: pod
    relabel_configs:
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)

  - job_name: 'coolify-metrics'
    static_configs:
    - targets: ['coolify.estratix.local:9090']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'estratix-applications'
    kubernetes_sd_configs:
    - role: service
    relabel_configs:
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
      action: keep
      regex: true
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
      action: replace
      target_label: __scheme__
      regex: (https?)
    - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
      action: replace
      target_label: __metrics_path__
      regex: (.+)
    - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
      action: replace
      regex: ([^:]+)(?::\d+)?;(\d+)
      replacement: $1:$2
      target_label: __address__
```

### 6.2 Grafana Dashboard Configuration

```json
{
  "dashboard": {
    "id": null,
    "title": "ESTRATIX VPC Infrastructure Overview",
    "tags": ["estratix", "kubernetes", "vpc"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Cluster Health Overview",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"kubernetes-nodes\"}",
            "legendFormat": "Nodes Up"
          },
          {
            "expr": "kube_pod_status_ready{condition=\"true\"}",
            "legendFormat": "Pods Ready"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}
      },
      {
        "id": 2,
        "title": "Resource Utilization",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg(irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)",
            "legendFormat": "CPU Usage %"
          },
          {
            "expr": "(1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100",
            "legendFormat": "Memory Usage %"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
      },
      {
        "id": 3,
        "title": "Application Deployments",
        "type": "table",
        "targets": [
          {
            "expr": "kube_deployment_status_replicas_available",
            "format": "table",
            "instant": true
          }
        ],
        "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}
      },
      {
        "id": 4,
        "title": "Network Traffic",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(node_network_receive_bytes_total[5m])",
            "legendFormat": "Inbound {{device}}"
          },
          {
            "expr": "rate(node_network_transmit_bytes_total[5m])",
            "legendFormat": "Outbound {{device}}"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}
      },
      {
        "id": 5,
        "title": "Coolify Deployments Status",
        "type": "stat",
        "targets": [
          {
            "expr": "coolify_deployments_total",
            "legendFormat": "Total Deployments"
          },
          {
            "expr": "coolify_deployments_success_total",
            "legendFormat": "Successful Deployments"
          },
          {
            "expr": "coolify_deployments_failed_total",
            "legendFormat": "Failed Deployments"
          }
        ],
        "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "30s"
  }
}
```

### 6.3 AlertManager Configuration

```yaml
# alertmanager-config.yaml
global:
  smtp_smarthost: 'smtp.estratix.com:587'
  smtp_from: '<EMAIL>'
  slack_api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
  - match:
      severity: warning
    receiver: 'warning-alerts'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://127.0.0.1:5001/'

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: 'CRITICAL: {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      Alert: {{ .Annotations.summary }}
      Description: {{ .Annotations.description }}
      {{ end }}
  slack_configs:
  - channel: '#alerts-critical'
    title: 'CRITICAL Alert'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

- name: 'warning-alerts'
  slack_configs:
  - channel: '#alerts-warning'
    title: 'Warning Alert'
    text: '{{ range .Alerts }}{{ .Annotations.summary }}{{ end }}'

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
```

## 7. Central Admin Dashboard

### 7.1 Dashboard Architecture

```typescript
// dashboard-config.ts
export interface DashboardConfig {
  clusters: ClusterConfig[];
  applications: ApplicationConfig[];
  monitoring: MonitoringConfig;
  coolify: CoolifyConfig;
}

export interface ClusterConfig {
  name: string;
  endpoint: string;
  region: string;
  environment: 'production' | 'staging' | 'development';
  nodes: NodeConfig[];
  status: 'healthy' | 'degraded' | 'unhealthy';
}

export interface ApplicationConfig {
  name: string;
  namespace: string;
  cluster: string;
  replicas: number;
  status: 'running' | 'pending' | 'failed';
  healthCheck: string;
  metrics: MetricsConfig;
}

export interface CoolifyConfig {
  url: string;
  projects: CoolifyProject[];
  servers: CoolifyServer[];
}

export const dashboardConfig: DashboardConfig = {
  clusters: [
    {
      name: 'estratix-prod',
      endpoint: 'https://*********:6443',
      region: 'us-east-1',
      environment: 'production',
      nodes: [
        { name: 'control-plane-1', ip: '*********', role: 'master' },
        { name: 'worker-1', ip: '*********', role: 'worker' },
        { name: 'worker-2', ip: '*********', role: 'worker' }
      ],
      status: 'healthy'
    }
  ],
  applications: [
    {
      name: 'estratix-frontend',
      namespace: 'estratix',
      cluster: 'estratix-prod',
      replicas: 3,
      status: 'running',
      healthCheck: '/health',
      metrics: {
        cpu: 'container_cpu_usage_seconds_total',
        memory: 'container_memory_usage_bytes',
        requests: 'http_requests_total'
      }
    }
  ],
  monitoring: {
    prometheus: 'https://prometheus.estratix.local',
    grafana: 'https://grafana.estratix.local',
    alertmanager: 'https://alertmanager.estratix.local'
  },
  coolify: {
    url: 'https://coolify.estratix.local',
    projects: [],
    servers: []
  }
};
```

### 7.2 Dashboard Components

```tsx
// Dashboard.tsx
import React, { useState, useEffect } from 'react';
import { ClusterOverview } from './components/ClusterOverview';
import { ApplicationStatus } from './components/ApplicationStatus';
import { CoolifyIntegration } from './components/CoolifyIntegration';
import { MonitoringDashboard } from './components/MonitoringDashboard';

export const Dashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [clusterData, setClusterData] = useState(null);
  const [applicationData, setApplicationData] = useState(null);

  useEffect(() => {
    // Fetch real-time data
    const fetchData = async () => {
      try {
        const [clusters, applications] = await Promise.all([
          fetch('/api/clusters').then(r => r.json()),
          fetch('/api/applications').then(r => r.json())
        ]);
        
        setClusterData(clusters);
        setApplicationData(applications);
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      }
    };

    fetchData();
    const interval = setInterval(fetchData, 30000); // Refresh every 30s

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1>ESTRATIX Infrastructure Dashboard</h1>
        <nav className="dashboard-nav">
          <button 
            className={activeTab === 'overview' ? 'active' : ''}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button 
            className={activeTab === 'clusters' ? 'active' : ''}
            onClick={() => setActiveTab('clusters')}
          >
            Clusters
          </button>
          <button 
            className={activeTab === 'applications' ? 'active' : ''}
            onClick={() => setActiveTab('applications')}
          >
            Applications
          </button>
          <button 
            className={activeTab === 'coolify' ? 'active' : ''}
            onClick={() => setActiveTab('coolify')}
          >
            Coolify
          </button>
          <button 
            className={activeTab === 'monitoring' ? 'active' : ''}
            onClick={() => setActiveTab('monitoring')}
          >
            Monitoring
          </button>
        </nav>
      </header>

      <main className="dashboard-content">
        {activeTab === 'overview' && (
          <div className="overview-grid">
            <ClusterOverview data={clusterData} />
            <ApplicationStatus data={applicationData} />
            <MonitoringDashboard />
          </div>
        )}
        
        {activeTab === 'clusters' && (
          <ClusterOverview data={clusterData} detailed={true} />
        )}
        
        {activeTab === 'applications' && (
          <ApplicationStatus data={applicationData} detailed={true} />
        )}
        
        {activeTab === 'coolify' && (
          <CoolifyIntegration />
        )}
        
        {activeTab === 'monitoring' && (
          <MonitoringDashboard detailed={true} />
        )}
      </main>
    </div>
  );
};
```

## 8. Automation Scripts

### 8.1 Infrastructure Provisioning

```bash
#!/bin/bash
# provision_infrastructure.sh

set -e

# Configuration
VPC_CIDR="10.0.0.0/16"
REGION="us-east-1"
KEY_NAME="estratix-key"
INSTANCE_TYPE="t3.large"

# Create VPC
create_vpc() {
    echo "Creating VPC..."
    
    VPC_ID=$(aws ec2 create-vpc \
        --cidr-block $VPC_CIDR \
        --query 'Vpc.VpcId' \
        --output text)
    
    aws ec2 create-tags \
        --resources $VPC_ID \
        --tags Key=Name,Value=estratix-vpc
    
    echo "VPC created: $VPC_ID"
}

# Create subnets
create_subnets() {
    echo "Creating subnets..."
    
    # Public subnet for load balancers
    PUBLIC_SUBNET_ID=$(aws ec2 create-subnet \
        --vpc-id $VPC_ID \
        --cidr-block ********/24 \
        --availability-zone ${REGION}a \
        --query 'Subnet.SubnetId' \
        --output text)
    
    # Private subnet for applications
    PRIVATE_SUBNET_ID=$(aws ec2 create-subnet \
        --vpc-id $VPC_ID \
        --cidr-block 10.0.2.0/24 \
        --availability-zone ${REGION}b \
        --query 'Subnet.SubnetId' \
        --output text)
    
    aws ec2 create-tags \
        --resources $PUBLIC_SUBNET_ID \
        --tags Key=Name,Value=estratix-public-subnet
    
    aws ec2 create-tags \
        --resources $PRIVATE_SUBNET_ID \
        --tags Key=Name,Value=estratix-private-subnet
    
    echo "Subnets created: $PUBLIC_SUBNET_ID, $PRIVATE_SUBNET_ID"
}

# Launch instances
launch_instances() {
    echo "Launching instances..."
    
    # Control plane
    CONTROL_PLANE_ID=$(aws ec2 run-instances \
        --image-id ami-0c02fb55956c7d316 \
        --count 1 \
        --instance-type $INSTANCE_TYPE \
        --key-name $KEY_NAME \
        --subnet-id $PRIVATE_SUBNET_ID \
        --security-group-ids $SECURITY_GROUP_ID \
        --user-data file://user-data-control-plane.sh \
        --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=estratix-control-plane}]' \
        --query 'Instances[0].InstanceId' \
        --output text)
    
    # Worker nodes
    for i in {1..2}; do
        WORKER_ID=$(aws ec2 run-instances \
            --image-id ami-0c02fb55956c7d316 \
            --count 1 \
            --instance-type $INSTANCE_TYPE \
            --key-name $KEY_NAME \
            --subnet-id $PRIVATE_SUBNET_ID \
            --security-group-ids $SECURITY_GROUP_ID \
            --user-data file://user-data-worker.sh \
            --tag-specifications "ResourceType=instance,Tags=[{Key=Name,Value=estratix-worker-$i}]" \
            --query 'Instances[0].InstanceId' \
            --output text)
        
        echo "Worker node $i created: $WORKER_ID"
    done
    
    echo "Control plane created: $CONTROL_PLANE_ID"
}

# Main execution
main() {
    create_vpc
    create_subnets
    launch_instances
    
    echo "Infrastructure provisioning completed!"
    echo "VPC ID: $VPC_ID"
    echo "Control Plane: $CONTROL_PLANE_ID"
}

main "$@"
```

### 8.2 Application Deployment Automation

```bash
#!/bin/bash
# deploy_application.sh

set -e

APP_NAME="$1"
GIT_REPO="$2"
ENVIRONMENT="$3"
DOMAIN="$4"

if [ $# -ne 4 ]; then
    echo "Usage: $0 <app-name> <git-repo> <environment> <domain>"
    exit 1
fi

echo "Deploying $APP_NAME from $GIT_REPO to $ENVIRONMENT environment..."

# Deploy to Kubernetes
deploy_to_kubernetes() {
    echo "Deploying to Kubernetes..."
    
    # Generate deployment manifest
    cat > k8s-deployment.yaml << EOF
apiVersion: apps/v1
kind: Deployment
metadata:
  name: $APP_NAME
  namespace: estratix
spec:
  replicas: 3
  selector:
    matchLabels:
      app: $APP_NAME
  template:
    metadata:
      labels:
        app: $APP_NAME
    spec:
      containers:
      - name: $APP_NAME
        image: ghcr.io/estratix/$APP_NAME:latest
        ports:
        - containerPort: 8080
        env:
        - name: ENVIRONMENT
          value: "$ENVIRONMENT"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
---
apiVersion: v1
kind: Service
metadata:
  name: $APP_NAME-service
  namespace: estratix
spec:
  selector:
    app: $APP_NAME
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: $APP_NAME-ingress
  namespace: estratix
  annotations:
    kubernetes.io/ingress.class: traefik
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  tls:
  - hosts:
    - $DOMAIN
    secretName: $APP_NAME-tls
  rules:
  - host: $DOMAIN
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: $APP_NAME-service
            port:
              number: 80
EOF
    
    kubectl apply -f k8s-deployment.yaml
    kubectl rollout status deployment/$APP_NAME -n estratix
    
    echo "Kubernetes deployment completed!"
}

# Deploy via Coolify
deploy_to_coolify() {
    echo "Deploying via Coolify..."
    
    # Create Coolify project
    curl -X POST "https://coolify.estratix.local/api/projects" \
        -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"name\": \"$APP_NAME\",
            \"description\": \"Auto-deployed $APP_NAME\",
            \"git_repository\": \"$GIT_REPO\",
            \"environment\": \"$ENVIRONMENT\",
            \"domains\": [\"$DOMAIN\"]
        }"
    
    # Trigger deployment
    curl -X POST "https://coolify.estratix.local/api/projects/$APP_NAME/deploy" \
        -H "Authorization: Bearer $COOLIFY_API_TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"force\": true
        }"
    
    echo "Coolify deployment triggered!"
}

# Monitor deployment
monitor_deployment() {
    echo "Monitoring deployment..."
    
    for i in {1..30}; do
        if kubectl get pods -n estratix -l app=$APP_NAME | grep -q "Running"; then
            echo "Deployment successful!"
            return 0
        fi
        
        echo "Waiting for pods to be ready... ($i/30)"
        sleep 10
    done
    
    echo "Deployment timeout!"
    return 1
}

# Main execution
case "$ENVIRONMENT" in
    "production")
        deploy_to_coolify
        ;;
    "staging")
        deploy_to_kubernetes
        monitor_deployment
        ;;
    "development")
        deploy_to_kubernetes
        ;;
    *)
        echo "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

echo "Deployment of $APP_NAME completed successfully!"
echo "Access URL: https://$DOMAIN"
```

## 9. Security and Compliance

### 9.1 Security Hardening Checklist

- [x] SSH key-based authentication
- [x] Firewall configuration (UFW/iptables)
- [x] Fail2Ban for intrusion prevention
- [x] Regular security updates
- [x] Container image scanning
- [x] Network segmentation
- [x] RBAC for Kubernetes
- [x] Secrets management
- [x] TLS/SSL encryption
- [x] Audit logging

### 9.2 Compliance Monitoring

```yaml
# compliance-policy.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: compliance-policy
  namespace: kube-system
data:
  policy.yaml: |
    policies:
      - name: "container-security"
        rules:
          - no_root_containers
          - read_only_filesystem
          - no_privileged_containers
          - resource_limits_required
      
      - name: "network-security"
        rules:
          - network_policies_required
          - ingress_tls_required
          - service_mesh_enabled
      
      - name: "data-protection"
        rules:
          - encryption_at_rest
          - encryption_in_transit
          - backup_retention_policy
```

## 10. Troubleshooting and Maintenance

### 10.1 Common Issues and Solutions

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Pod CrashLoopBackOff | Pods continuously restarting | Check logs, resource limits, health checks |
| Service Unavailable | 503 errors from ingress | Verify service endpoints, pod readiness |
| High Memory Usage | OOMKilled events | Increase memory limits, optimize application |
| Network Connectivity | Timeouts, DNS resolution failures | Check network policies, CNI configuration |
| Storage Issues | PVC pending, mount failures | Verify storage class, node capacity |

### 10.2 Maintenance Scripts

```bash
#!/bin/bash
# maintenance.sh

# Cleanup unused resources
cleanup_resources() {
    echo "Cleaning up unused resources..."
    
    # Remove unused Docker images
    docker system prune -af
    
    # Clean up completed pods
    kubectl delete pods --field-selector=status.phase=Succeeded -A
    
    # Remove unused PVCs
    kubectl get pvc -A --no-headers | awk '$2=="Available" {print $1, $2}' | xargs -n2 kubectl delete pvc -n
}

# Update system packages
update_system() {
    echo "Updating system packages..."
    
    apt update && apt upgrade -y
    apt autoremove -y
    apt autoclean
}

# Backup critical data
backup_data() {
    echo "Backing up critical data..."
    
    # Backup etcd
    ETCDCTL_API=3 etcdctl snapshot save /backup/etcd-$(date +%Y%m%d-%H%M%S).db
    
    # Backup application data
    kubectl get all -A -o yaml > /backup/k8s-resources-$(date +%Y%m%d-%H%M%S).yaml
}

# Main execution
case "$1" in
    "cleanup")
        cleanup_resources
        ;;
    "update")
        update_system
        ;;
    "backup")
        backup_data
        ;;
    "all")
        cleanup_resources
        update_system
        backup_data
        ;;
    *)
        echo "Usage: $0 {cleanup|update|backup|all}"
        exit 1
        ;;
esac
```

## 11. Next Steps and Improvements

### 11.1 Identified Gaps

1. **Service Mesh Integration**: Implement Istio for advanced traffic management
2. **GitOps Automation**: Enhance ArgoCD configuration for multi-environment deployments
3. **Disaster Recovery**: Implement cross-region backup and failover strategies
4. **Cost Optimization**: Add resource usage analytics and cost monitoring
5. **Security Scanning**: Integrate vulnerability scanning in CI/CD pipeline
6. **Performance Testing**: Automated load testing and performance benchmarking

### 11.2 Optimization Opportunities

- **Resource Allocation**: Implement Vertical Pod Autoscaler (VPA)
- **Network Optimization**: Use Cilium for eBPF-based networking
- **Storage Optimization**: Implement CSI drivers for cloud storage
- **Monitoring Enhancement**: Add distributed tracing with Jaeger
- **Automation**: Implement Chaos Engineering with Chaos Monkey

### 11.3 Scaling Considerations

- **Multi-Region Deployment**: Expand to multiple geographic regions
- **Edge Computing**: Deploy edge nodes for reduced latency
- **Microservices Architecture**: Break down monolithic applications
- **Event-Driven Architecture**: Implement message queues and event streaming
- **API Gateway**: Centralized API management and rate limiting

This comprehensive architecture provides a solid foundation for a distributed VPC system with multiple Kubernetes clusters, automated CI/CD workflows, and centralized monitoring. The configuration is designed to be scalable, secure, and maintainable while following best practices for modern cloud-native deployments.