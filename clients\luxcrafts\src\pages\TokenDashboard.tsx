import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  BanknotesIcon,
  GiftIcon,
  LockClosedIcon,
  ClockIcon,
  FireIcon,
  UserGroupIcon,
  TrophyIcon,
  SparklesIcon,
  ArrowPathIcon,
  PlusIcon,
  MinusIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, AreaChart, Area } from 'recharts';

const tokenMetrics = {
  price: 2.47,
  change24h: 8.5,
  marketCap: ********,
  volume24h: 1250000,
  totalSupply: ********,
  circulatingSupply: 7500000,
  holders: 15420,
  stakingAPY: 12.5,
  totalStaked: 3200000,
  stakingRatio: 42.7,
};

const userPortfolio = {
  balance: 1250.75,
  staked: 850.25,
  rewards: 45.80,
  totalValue: 5234.67,
  stakingRewards: 156.32,
  referralRewards: 23.45,
};

const priceHistory = [
  { time: '00:00', price: 2.35 },
  { time: '04:00', price: 2.42 },
  { time: '08:00', price: 2.38 },
  { time: '12:00', price: 2.45 },
  { time: '16:00', price: 2.51 },
  { time: '20:00', price: 2.47 },
];

const stakingPools = [
  {
    id: 1,
    name: 'Flexible Staking',
    apy: 8.5,
    lockPeriod: 0,
    minStake: 10,
    totalStaked: 1200000,
    userStaked: 250.5,
    rewards: 12.35,
    description: 'Stake and unstake anytime with competitive rewards',
  },
  {
    id: 2,
    name: '30-Day Lock',
    apy: 12.5,
    lockPeriod: 30,
    minStake: 50,
    totalStaked: 800000,
    userStaked: 400.0,
    rewards: 25.80,
    description: 'Higher rewards for 30-day commitment',
  },
  {
    id: 3,
    name: '90-Day Lock',
    apy: 18.0,
    lockPeriod: 90,
    minStake: 100,
    totalStaked: 600000,
    userStaked: 199.75,
    rewards: 18.17,
    description: 'Maximum rewards for long-term staking',
  },
  {
    id: 4,
    name: 'Property Yield Pool',
    apy: 15.2,
    lockPeriod: 60,
    minStake: 200,
    totalStaked: 600000,
    userStaked: 0,
    rewards: 0,
    description: 'Earn from real estate rental yields',
  },
];

const recentTransactions = [
  {
    id: 1,
    type: 'stake',
    amount: 100,
    timestamp: '2024-01-16 14:30',
    status: 'completed',
    hash: '0x1234...5678',
  },
  {
    id: 2,
    type: 'reward',
    amount: 5.25,
    timestamp: '2024-01-16 12:00',
    status: 'completed',
    hash: '0x2345...6789',
  },
  {
    id: 3,
    type: 'unstake',
    amount: 50,
    timestamp: '2024-01-15 16:45',
    status: 'completed',
    hash: '0x3456...7890',
  },
];

const tokenDistribution = [
  { name: 'Circulating Supply', value: 7500000, color: '#3b82f6' },
  { name: 'Staking Rewards', value: 1500000, color: '#10b981' },
  { name: 'Team & Advisors', value: 500000, color: '#f59e0b' },
  { name: 'Treasury', value: 500000, color: '#8b5cf6' },
];

const defiFeatures = [
  {
    id: 'lending',
    name: 'Lending Pool',
    icon: BanknotesIcon,
    apy: '6.8%',
    tvl: '$2.1M',
    description: 'Lend LUX tokens and earn interest',
    status: 'active',
  },
  {
    id: 'borrowing',
    name: 'Collateral Borrowing',
    icon: CurrencyDollarIcon,
    apy: '4.2%',
    tvl: '$1.8M',
    description: 'Borrow against your property NFTs',
    status: 'active',
  },
  {
    id: 'liquidity',
    name: 'Liquidity Mining',
    icon: ArrowPathIcon,
    apy: '24.5%',
    tvl: '$850K',
    description: 'Provide liquidity and earn rewards',
    status: 'coming_soon',
  },
  {
    id: 'yield',
    name: 'Yield Farming',
    icon: SparklesIcon,
    apy: '32.1%',
    tvl: '$650K',
    description: 'Farm yields across multiple protocols',
    status: 'coming_soon',
  },
];

export default function TokenDashboard() {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedPool, setSelectedPool] = useState(null);
  const [stakeAmount, setStakeAmount] = useState('');
  const [unstakeAmount, setUnstakeAmount] = useState('');
  const [showStakeModal, setShowStakeModal] = useState(false);
  const [showUnstakeModal, setShowUnstakeModal] = useState(false);

  const tabs = [
    { id: 'overview', name: 'Overview' },
    { id: 'staking', name: 'Staking' },
    { id: 'defi', name: 'DeFi' },
    { id: 'governance', name: 'Governance' },
    { id: 'analytics', name: 'Analytics' },
  ];

  const handleStake = () => {
    // Implement staking logic
    console.log('Staking:', stakeAmount, 'LUX');
    setShowStakeModal(false);
    setStakeAmount('');
  };

  const handleUnstake = () => {
    // Implement unstaking logic
    console.log('Unstaking:', unstakeAmount, 'LUX');
    setShowUnstakeModal(false);
    setUnstakeAmount('');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white font-bold text-lg">LUX</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">LUX Token Dashboard</h1>
              <p className="text-xl text-gray-600">Manage your LUX tokens, staking, and DeFi activities</p>
            </div>
          </div>
          
          {/* Token Price Banner */}
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl p-6 text-white">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <p className="text-blue-100 text-sm">LUX Price</p>
                <div className="flex items-center space-x-2">
                  <span className="text-3xl font-bold">${tokenMetrics.price}</span>
                  <span className={`flex items-center text-sm ${
                    tokenMetrics.change24h >= 0 ? 'text-green-300' : 'text-red-300'
                  }`}>
                    {tokenMetrics.change24h >= 0 ? (
                      <ArrowTrendingUpIcon className="w-4 h-4 mr-1" />
                    ) : (
                      <ArrowTrendingDownIcon className="w-4 h-4 mr-1" />
                    )}
                    {Math.abs(tokenMetrics.change24h)}%
                  </span>
                </div>
              </div>
              <div>
                <p className="text-blue-100 text-sm">Market Cap</p>
                <p className="text-2xl font-bold">${(tokenMetrics.marketCap / 1000000).toFixed(1)}M</p>
              </div>
              <div>
                <p className="text-blue-100 text-sm">24h Volume</p>
                <p className="text-2xl font-bold">${(tokenMetrics.volume24h / 1000000).toFixed(1)}M</p>
              </div>
              <div>
                <p className="text-blue-100 text-sm">Staking APY</p>
                <p className="text-2xl font-bold">{tokenMetrics.stakingAPY}%</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Portfolio Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Balance</p>
                    <p className="text-2xl font-bold text-gray-900">{userPortfolio.balance.toFixed(2)} LUX</p>
                    <p className="text-sm text-gray-500">${(userPortfolio.balance * tokenMetrics.price).toFixed(2)}</p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Staked Amount</p>
                    <p className="text-2xl font-bold text-gray-900">{userPortfolio.staked.toFixed(2)} LUX</p>
                    <p className="text-sm text-green-600">Earning {tokenMetrics.stakingAPY}% APY</p>
                  </div>
                  <LockClosedIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending Rewards</p>
                    <p className="text-2xl font-bold text-gray-900">{userPortfolio.rewards.toFixed(2)} LUX</p>
                    <p className="text-sm text-gray-500">${(userPortfolio.rewards * tokenMetrics.price).toFixed(2)}</p>
                  </div>
                  <GiftIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Value</p>
                    <p className="text-2xl font-bold text-gray-900">${userPortfolio.totalValue.toFixed(2)}</p>
                    <p className="text-sm text-green-600">+12.5% this month</p>
                  </div>
                  <TrophyIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
            </div>

            {/* Price Chart */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">LUX Price Chart (24h)</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={priceHistory}>
                    <defs>
                      <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={['dataMin - 0.05', 'dataMax + 0.05']} />
                    <Tooltip />
                    <Area
                      type="monotone"
                      dataKey="price"
                      stroke="#3b82f6"
                      fillOpacity={1}
                      fill="url(#colorPrice)"
                      strokeWidth={3}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Recent Transactions */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Transactions</h3>
              <div className="space-y-4">
                {recentTransactions.map((tx) => (
                  <div key={tx.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        tx.type === 'stake' ? 'bg-green-100' :
                        tx.type === 'unstake' ? 'bg-red-100' :
                        'bg-blue-100'
                      }`}>
                        {tx.type === 'stake' ? (
                          <PlusIcon className="w-5 h-5 text-green-600" />
                        ) : tx.type === 'unstake' ? (
                          <MinusIcon className="w-5 h-5 text-red-600" />
                        ) : (
                          <GiftIcon className="w-5 h-5 text-blue-600" />
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 capitalize">{tx.type}</p>
                        <p className="text-sm text-gray-500">{tx.timestamp}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className={`font-bold ${
                        tx.type === 'reward' ? 'text-green-600' :
                        tx.type === 'unstake' ? 'text-red-600' :
                        'text-gray-900'
                      }`}>
                        {tx.type === 'reward' ? '+' : tx.type === 'unstake' ? '-' : ''}{tx.amount} LUX
                      </p>
                      <p className="text-sm text-gray-500">{tx.hash}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Staking Tab */}
        {activeTab === 'staking' && (
          <div className="space-y-8">
            {/* Staking Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Staked</p>
                    <p className="text-2xl font-bold text-gray-900">{(tokenMetrics.totalStaked / 1000000).toFixed(1)}M LUX</p>
                    <p className="text-sm text-gray-500">{tokenMetrics.stakingRatio}% of supply</p>
                  </div>
                  <LockClosedIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Your Staked</p>
                    <p className="text-2xl font-bold text-gray-900">{userPortfolio.staked.toFixed(2)} LUX</p>
                    <p className="text-sm text-green-600">${(userPortfolio.staked * tokenMetrics.price).toFixed(2)}</p>
                  </div>
                  <UserGroupIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Rewards Earned</p>
                    <p className="text-2xl font-bold text-gray-900">{userPortfolio.stakingRewards.toFixed(2)} LUX</p>
                    <p className="text-sm text-purple-600">All time rewards</p>
                  </div>
                  <TrophyIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>

            {/* Staking Pools */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {stakingPools.map((pool) => (
                <motion.div
                  key={pool.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-xl font-bold text-gray-900">{pool.name}</h4>
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                      {pool.apy}% APY
                    </span>
                  </div>
                  
                  <p className="text-gray-600 mb-4">{pool.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div>
                      <p className="text-sm text-gray-500">Lock Period</p>
                      <p className="font-semibold">{pool.lockPeriod === 0 ? 'Flexible' : `${pool.lockPeriod} days`}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Min Stake</p>
                      <p className="font-semibold">{pool.minStake} LUX</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Total Staked</p>
                      <p className="font-semibold">{(pool.totalStaked / 1000000).toFixed(1)}M LUX</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500">Your Stake</p>
                      <p className="font-semibold">{pool.userStaked} LUX</p>
                    </div>
                  </div>
                  
                  {pool.userStaked > 0 && (
                    <div className="bg-green-50 rounded-lg p-4 mb-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-green-700">Pending Rewards</span>
                        <span className="font-bold text-green-800">{pool.rewards} LUX</span>
                      </div>
                    </div>
                  )}
                  
                  <div className="flex space-x-3">
                    <button
                      onClick={() => {
                        setSelectedPool(pool);
                        setShowStakeModal(true);
                      }}
                      className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                    >
                      Stake
                    </button>
                    {pool.userStaked > 0 && (
                      <button
                        onClick={() => {
                          setSelectedPool(pool);
                          setShowUnstakeModal(true);
                        }}
                        className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                      >
                        Unstake
                      </button>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* DeFi Tab */}
        {activeTab === 'defi' && (
          <div className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {defiFeatures.map((feature) => (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow"
                >
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                      <feature.icon className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="text-xl font-bold text-gray-900">{feature.name}</h4>
                      <div className="flex items-center space-x-4">
                        <span className="text-sm text-green-600 font-medium">APY: {feature.apy}</span>
                        <span className="text-sm text-gray-500">TVL: {feature.tvl}</span>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-gray-600 mb-6">{feature.description}</p>
                  
                  <button
                    disabled={feature.status === 'coming_soon'}
                    className={`w-full px-4 py-3 rounded-lg font-medium transition-colors ${
                      feature.status === 'active'
                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {feature.status === 'active' ? 'Enter Pool' : 'Coming Soon'}
                  </button>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            {/* Token Distribution */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Token Distribution</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={tokenDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {tokenDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [(Number(value) / 1000000).toFixed(1) + 'M', 'Tokens']} />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
                
                <div className="space-y-4">
                  {tokenDistribution.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div
                          className="w-4 h-4 rounded-full"
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="font-medium text-gray-900">{item.name}</span>
                      </div>
                      <span className="text-gray-600">{(item.value / 1000000).toFixed(1)}M LUX</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Holders</p>
                    <p className="text-2xl font-bold text-gray-900">{tokenMetrics.holders.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+5.2% this week</p>
                  </div>
                  <UserGroupIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Burn Rate</p>
                    <p className="text-2xl font-bold text-gray-900">2.1%</p>
                    <p className="text-sm text-red-600">Deflationary</p>
                  </div>
                  <FireIcon className="w-8 h-8 text-red-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Hold Time</p>
                    <p className="text-2xl font-bold text-gray-900">45 days</p>
                    <p className="text-sm text-green-600">Strong hands</p>
                  </div>
                  <ClockIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Velocity</p>
                    <p className="text-2xl font-bold text-gray-900">0.8x</p>
                    <p className="text-sm text-blue-600">Healthy circulation</p>
                  </div>
                  <ArrowPathIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Stake Modal */}
        {showStakeModal && selectedPool && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Stake LUX Tokens</h3>
              <p className="text-gray-600 mb-4">Pool: {selectedPool.name}</p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount to Stake
                  </label>
                  <input
                    type="number"
                    value={stakeAmount}
                    onChange={(e) => setStakeAmount(e.target.value)}
                    placeholder="Enter amount"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Available: {userPortfolio.balance.toFixed(2)} LUX
                  </p>
                </div>
                
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <InformationCircleIcon className="w-5 h-5 text-blue-500" />
                    <span className="text-sm font-medium text-blue-900">Staking Details</span>
                  </div>
                  <div className="space-y-1 text-sm text-blue-800">
                    <p>APY: {selectedPool.apy}%</p>
                    <p>Lock Period: {selectedPool.lockPeriod === 0 ? 'Flexible' : `${selectedPool.lockPeriod} days`}</p>
                    <p>Minimum Stake: {selectedPool.minStake} LUX</p>
                  </div>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowStakeModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleStake}
                    disabled={!stakeAmount || parseFloat(stakeAmount) < selectedPool.minStake}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Stake
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Unstake Modal */}
        {showUnstakeModal && selectedPool && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Unstake LUX Tokens</h3>
              <p className="text-gray-600 mb-4">Pool: {selectedPool.name}</p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Amount to Unstake
                  </label>
                  <input
                    type="number"
                    value={unstakeAmount}
                    onChange={(e) => setUnstakeAmount(e.target.value)}
                    placeholder="Enter amount"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Staked: {selectedPool.userStaked} LUX
                  </p>
                </div>
                
                {selectedPool.lockPeriod > 0 && (
                  <div className="bg-yellow-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <ClockIcon className="w-5 h-5 text-yellow-500" />
                      <span className="text-sm font-medium text-yellow-900">Lock Period Notice</span>
                    </div>
                    <p className="text-sm text-yellow-800">
                      This pool has a {selectedPool.lockPeriod}-day lock period. Early unstaking may incur penalties.
                    </p>
                  </div>
                )}
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowUnstakeModal(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleUnstake}
                    disabled={!unstakeAmount || parseFloat(unstakeAmount) > selectedPool.userStaked}
                    className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Unstake
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}