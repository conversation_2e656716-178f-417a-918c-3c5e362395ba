---
description: Guides the generation of a new ESTRATIX FastAPI microservice based on the Hexagonal Architecture boilerplate.
---

# ESTRATIX Workflow: Bootstrap FastAPI Microservice

**Workflow ID:** `WF_COO_BOOTSTRAP_FASTAPI_V1`

**Agent Persona:** `AGENT_Boilerplate_Generator_BG001`

## 1. Objective

To automate the creation of a new, production-ready Python FastAPI microservice project that fully adheres to the ESTRATIX Hexagonal Architecture boilerplate (`BP_PY_FASTAPI_HEX_V1`).

## 2. Prerequisites

- The name of the new service must be provided (e.g., `user-management-service`).
- The target parent directory for the new service must be specified (e.g., `src/services/`).

## 3. Workflow Steps

### Step 1: Define Project Name and Path

1. **Input:** Obtain the `service_name` (e.g., `user-management-service`).
2. **Input:** Obtain the `parent_directory` (e.g., `src/domain/services`).
3. **Action:** Define the `project_root` path as `{{parent_directory}}/{{service_name}}`.

### Step 2: Create Root Directory

- **Action:** Create the main project directory.
- **Tool:** `run_command`
- **Example Command:** `<!-- run_command('mkdir -p {{project_root}}') -->`

### Step 3: Scaffold Hexagonal Directory Structure

- **Action:** Create the complete directory structure as defined in the ESTRATIX Hexagonal Architecture Boilerplate (`BP_PY_FASTAPI_HEX_V1`).
- **Tool:** `run_command`
- **Guidance:** Commands are broken down by layer for clarity.

- **Top-Level Directories:**

  ```markdown
  <!-- run_command('mkdir -p .github/workflows docs scripts src tests', Cwd='{{project_root}}') -->
  ```

- **Source (`src`) Directories:**

  ```markdown
  <!-- run_command('mkdir -p src/application src/domain src/infrastructure', Cwd='{{project_root}}') -->
  ```

- **Domain Layer:**

  ```markdown
  <!-- run_command('mkdir -p src/domain/models src/domain/repositories src/domain/services', Cwd='{{project_root}}') -->
  ```

- **Application Layer:**

  ```markdown
  <!-- run_command('mkdir -p src/application/services src/application/ports src/application/dto', Cwd='{{project_root}}') -->
  ```

- **Infrastructure Layer:**

  ```markdown
  <!-- run_command('mkdir -p src/infrastructure/adapters/api src/infrastructure/adapters/db src/infrastructure/adapters/external src/infrastructure/config src/infrastructure/logging src/infrastructure/security', Cwd='{{project_root}}') -->
  ```

- **Tests Directory:**

  ```markdown
  <!-- run_command('mkdir -p tests/unit tests/integration tests/e2e', Cwd='{{project_root}}') -->
  ```

### Step 4: Create Core Boilerplate Files

- **Action:** Create essential empty or placeholder files to make directories importable Python packages and to provide entry points.
- **Tool:** `write_to_file`
- **Guidance:** Create `__init__.py` in all necessary directories and populate key configuration files.

  ```markdown
  <!-- write_to_file('{{project_root}}/.dockerignore', '') -->
  <!-- write_to_file('{{project_root}}/.env.example', 'DATABASE_URL=postgresql://user:password@host:port/database') -->
  <!-- write_to_file('{{project_root}}/.gitignore', '# Byte-compiled / optimized / DLL files\n__pycache__/\n*.py[cod]\n*$py.class\n\n# C extensions\n*.so\n\n# Environments\n.env\n.venv\nenv/\nvenv/\n\n# IDEs\n.idea/\n.vscode/\n') -->
  <!-- write_to_file('{{project_root}}/Dockerfile', 'FROM python:3.11-slim\n\nWORKDIR /app\n\n# Install uv
RUN pip install uv

# Copy dependency definitions
COPY pyproject.toml ./

# Install dependencies
RUN uv pip install --system -e . 

# Copy the application source code
COPY src/ /app/src

# Expose the port the app runs on
EXPOSE 8000

# Run the application
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"]') -->
  <!-- write_to_file('{{project_root}}/alembic.ini', '') -->
  <!-- write_to_file('{{project_root}}/src/main.py', 'from fastapi import FastAPI\n\napp = FastAPI(title="{{service_name}}")\n\<EMAIL>("/")\ndef read_root():\n    return {"message": "Welcome to the {{service_name}} API"}\n') -->
  <!-- write_to_file('{{project_root}}/src/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/domain/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/domain/models/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/domain/repositories/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/domain/services/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/application/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/application/services/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/application/ports/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/application/dto/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/adapters/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/adapters/api/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/adapters/db/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/adapters/external/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/config/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/logging/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/src/infrastructure/security/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/tests/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/tests/unit/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/tests/integration/__init__.py', '') -->
  <!-- write_to_file('{{project_root}}/tests/e2e/__init__.py', '') -->
  ```

### Step 5: Populate `pyproject.toml`

-   **Action:** Create and populate the `pyproject.toml` file with standard dependencies for a FastAPI service.
-   **Tool:** `write_to_file`
-   **Example Command:**

    ```markdown
    <!-- write_to_file('{{project_root}}/pyproject.toml', `
    [tool.poetry]
    name = "{{service_name}}"
    version = "0.1.0"
    description = "{{service_name}} - An ESTRATIX microservice."
    authors = ["ESTRATIX Development <<EMAIL>>"]

    [tool.poetry.dependencies]
    python = "^3.10"
    fastapi = "^0.109.0"
    uvicorn = {extras = ["standard"], version = "^0.27.0"}
    pydantic = {extras = ["email"], version = "^2.5.0"}
    pydantic-settings = "^2.1.0"
    sqlalchemy = "^2.0.25"
    alembic = "^1.13.1"
    python-jose = {extras = ["cryptography"], version = "^3.3.0"}
    passlib = {extras = ["bcrypt"], version = "^1.7.4"}

    [tool.poetry.group.dev.dependencies]
    pytest = "^8.0.0"
    pytest-mock = "^3.12.0"
    httpx = "^0.26.0" # For TestClient
    black = "^24.1.1"
    flake8 = "^7.0.0"

    [build-system]
    requires = ["poetry-core"]
    build-backend = "poetry.core.masonry.api"
    `) -->
    ```

### Step 6: Populate `README.md`

-   **Action:** Create a placeholder `README.md` with the service name.
-   **Tool:** `write_to_file`
-   **Example Command:** `<!-- write_to_file('{{project_root}}/README.md', '# {{service_name}}\n\nThis is the main README for the {{service_name}} microservice.') -->`

### Step 7: Initialize Git Repository

-   **Action:** Initialize a new Git repository in the project's root directory.
-   **Tool:** `run_command`
-   **Example Commands:**

    ```markdown
    <!-- run_command('git init', Cwd='{{project_root}}') -->
    <!-- run_command('git add .', Cwd='{{project_root}}') -->
    <!-- run_command('git commit -m "Initial commit: Bootstrap ESTRATIX FastAPI service `{{service_name}}`"', Cwd='{{project_root}}') -->
    ```

## 4. Completion

The workflow is complete when the directory structure and all boilerplate files have been created and a Git repository has been initialized. The new service is now ready for development, following TDD principles.
