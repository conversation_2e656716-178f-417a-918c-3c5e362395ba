{"name": "@estratix/backend-services", "version": "1.0.0", "description": "ESTRATIX Agency Backend Infrastructure - Comprehensive microservices for productized AI services and client project management", "main": "dist/index.js", "scripts": {"build": "tsc && npm run build:services", "build:services": "concurrently \"npm run build:content-studio\" \"npm run build:smart-contracts\" \"npm run build:project-mgmt\" \"npm run build:agent-orchestration\" \"npm run build:client-onboarding\"", "build:content-studio": "cd services/content-studio && npm run build", "build:smart-contracts": "cd services/smart-contracts && npm run build", "build:project-mgmt": "cd services/project-management && npm run build", "build:agent-orchestration": "cd services/agent-orchestration && npm run build", "build:client-onboarding": "cd services/client-onboarding && npm run build", "dev": "concurrently \"npm run dev:content-studio\" \"npm run dev:smart-contracts\" \"npm run dev:project-mgmt\" \"npm run dev:agent-orchestration\" \"npm run dev:client-onboarding\"", "dev:content-studio": "cd services/content-studio && npm run dev", "dev:smart-contracts": "cd services/smart-contracts && npm run dev", "dev:project-mgmt": "cd services/project-management && npm run dev", "dev:agent-orchestration": "cd services/agent-orchestration && npm run dev", "dev:client-onboarding": "cd services/client-onboarding && npm run dev", "dev:all": "npm run db:setup && npm run dev", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "db:setup": "npm run db:generate && npm run db:migrate", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:seed": "tsx shared/database/seed.ts", "db:studio": "prisma studio", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "k8s:deploy": "kubectl apply -f infrastructure/k8s/", "deploy:staging": "npm run build && npm run docker:build && npm run k8s:deploy", "deploy:production": "npm run test && npm run build && npm run docker:build && npm run k8s:deploy", "docs:generate": "swagger-jsdoc -d swaggerDef.js -o docs/swagger.json src/**/*.ts", "monitor:start": "docker-compose -f infrastructure/monitoring/docker-compose.yml up -d"}, "keywords": ["estratix", "agency", "ai", "content-studio", "smart-contracts", "project-management", "agentic-workflows", "microservices", "typescript", "blockchain"], "author": "ESTRATIX Agency", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.0", "@fastify/multipart": "^8.3.0", "@fastify/rate-limit": "^9.1.0", "@fastify/swagger": "^8.14.0", "@fastify/swagger-ui": "^4.0.0", "@prisma/client": "^5.15.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.6", "axios": "^1.7.2", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "dotenv": "^16.4.5", "ethers": "^6.13.1", "express": "^4.19.2", "fastify": "^4.28.1", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "langchain": "^0.2.8", "openai": "^4.52.7", "pino": "^9.2.0", "pino-pretty": "^11.2.1", "prisma": "^5.15.0", "redis": "^4.6.14", "uuid": "^10.0.0", "ws": "^8.17.1", "zod": "^3.23.8"}, "devDependencies": {"@types/bull": "^4.10.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/node": "^20.14.9", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^7.13.1", "@typescript-eslint/parser": "^7.13.1", "concurrently": "^8.2.2", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "nodemon": "^3.1.4", "prettier": "^3.3.2", "swagger-jsdoc": "^6.2.8", "ts-jest": "^29.1.5", "ts-node": "^10.9.2", "tsx": "^4.15.7", "typescript": "^5.5.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/estratix/backend-services.git"}, "workspaces": ["services/*", "shared/*"]}