# ESTRATIX VPC Server Definition: [Server Name] ([ID])

## 1. Metadata

* **ID:** [VPC_ID] (e.g., VPC001)
* **Server Name:** [Hostname or Friendly Name]
* **Version:** 1.0
* **Status:** (Planning | Provisioned | Active | Decommissioned)
* **Owner (Command Office):** CTO
* **Manager (Lead Agent/Role):** [e.g., AGENT_InfrastructureManager_Lead]
* **Date Created:** YYYY-MM-DD
* **Last Updated:** YYYY-MM-DD

## 2. Overview

* **Purpose:** [Describe the role of this server in the infrastructure, e.g., 'Hosts the primary application backend', 'Runs the staging database'].

## 3. Instance Configuration

* **Provider:** [e.g., AWS, Azure, Google Cloud, Linode]
* **Instance Type/Size:** [e.g., t3.medium, Standard_D2s_v3]
* **Operating System:** [e.g., Ubuntu 22.04 LTS]
* **Storage:** [e.g., 50GB GP3 SSD]
* **Region:** [e.g., us-east-1]

## 4. Networking

* **VPC/VNet:** [Link to VPC/VNet Definition]
* **Subnet:** [e.g., private-app-subnet-1a]
* **Static IP / Elastic IP:** [IP Address, if applicable]
* **DNS Records:** [List associated domain names]

## 5. Security

* **Security Group / NSG:** [Link to Security Group Definition]
* **Key Pair:** [Name of the SSH key pair for access]
* **IAM Role:** [Name of the IAM role attached to the instance]

## 6. Monitoring & Logging

* **Monitoring Agent:** [e.g., Datadog, CloudWatch Agent]
* **Key Metrics to Monitor:** [CPU Utilization, Memory Usage, Disk I/O, Network In/Out]
* **Log Aggregation:** [e.g., CloudWatch Logs, Splunk]

## 7. Dependencies

* **Dependent Services:** [Services running on or relying on this server.]
* **Dependent Systems:** [e.g., S3 Buckets, Databases, Load Balancers.]

## 8. Revision History

| Version | Date       | Author        | Changes                                     |
| :------ | :--------- | :------------ | :------------------------------------------ |
| 1.0     | YYYY-MM-DD | [Author Name] | Initial Definition                          |
