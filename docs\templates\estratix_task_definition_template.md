# ESTRATIX Task Definition: [Task Name] ([Task_ID])

## 1. Metadata

*   **ID:** [Task_ID] (e.g., T0001)
*   **Task Name:** [Full Name of the Task]
*   **Version:** 1.0
*   **Status:** (Draft | Under Review | Active | Deprecated)
*   **Owner Office:** [Acronym of the Command Office owning the process, e.g., CPO, CTO]
*   **Security Classification:** (Public | Internal | Confidential | Secret)
*   **Date Created:** YYYY-MM-DD
*   **Last Updated:** YYYY-MM-DD

## 2. Relationships & Dependencies

*   **Parent Process(ID):** [ID of the parent ESTRATIX Process, e.g., P001]
*   **Parent Flow(ID):** [ID of the ESTRATIX Flow this task is part of, e.g., F001]
*   **Parent Service(ID):** [ID of the ESTRATIX Service this task supports, e.g., S001]
*   **Task Dependencies (IDs):** (List of Task IDs that must be completed before this one can start, e.g., T0001, T0002)

## 3. Project Management Context

*   **Associated Project(ID):** [ID of the ESTRATIX Project this task is associated with, e.g., EP001]
*   **Work Item ID(s):** (Link to external tracking systems like Jira, Azure DevOps, or the Master Task List, e.g., ESTRATIX-123)

## 4. Purpose & Goal

*   **Purpose:** (Concisely describe why this task exists. What is its primary function and contribution to its parent components?)
*   **Goal:** (What is the specific, measurable, achievable, relevant, and time-bound (SMART) objective this task aims to achieve?)

## 5. Execution Details

*   **Triggers:** (What specific events, conditions, or preceding task completions initiate this task?)
*   **Inputs:** (What specific information, documents, data models, or resources are required? Specify source and format.)
    *   Input 1:
        *   Description:
        *   Source/Format:
        *   Data Model (Pydantic):
*   **Outputs:** (What are the tangible deliverables, results, or state changes produced? Specify destination and format.)
    *   Output 1:
        *   Description:
        *   Destination/Format:
        *   Data Model (Pydantic):
*   **Key Steps / Activities:** (Outline the major activities performed by the responsible agent(s) to complete this task.)
    1.  Activity 1: [Description]
    2.  Activity 2: [Description]

## 6. Agentic & System Integration

*   **Executing Agent(s):** (Primary agent(s) responsible for performing the task.)
    *   Agent ID(s): [e.g., CTO_A005_DeploymentOrchestratorAgent]
    *   Required Capabilities: (List specific skills or knowledge the agent must possess)
*   **Supporting Agent(s):** (Other agents involved, e.g., for monitoring, reporting, or error handling.)
    *   Agent ID(s): [e.g., CSO_A001_SecurityMonitorAgent]
*   **Tools & Systems Used:** (List specific software libraries, external APIs, MCPs, or ESTRATIX tools.)
    *   Tool 1: [e.g., `qdrant-client`, `docker-mcp`, `git`]

## 7. Quality & Performance

*   **Success Criteria / Acceptance Criteria:** (How is successful completion verified? What conditions must be met?)
    *   [ ] Criterion 1:
    *   [ ] Criterion 2:
*   **Key Performance Indicators (KPIs):** (What metrics measure the performance and efficiency of this task?)
    *   KPI 1: (e.g., Completion Time)
    *   KPI 2: (e.g., Error Rate)
*   **Error Handling:** (Describe known potential errors and how they should be handled.)
    *   **Issue 1:** [Description]
    *   **Handling:** [Action to take]

## 8. Framework-Specific Implementation

*(Provide specific details relevant to implementing this task within different agentic frameworks or for specific tools like Aider.)*

### 8.1 General Agent Instructions:
*   (e.g., "Agent should expect Input 1 as a Pydantic model `SourceConfigData`. Agent must call `Tool_UpdateScheduler` upon successful completion.")

### 8.2 Aider Task Specification:
*   **Task Description for Aider:** (A concise, imperative instruction formatted for an `aider` prompt.)
*   **Relevant Files for Context:** (List files `aider` should be aware of.)

### 8.3 CrewAI Specifics:
*   **Agent Tool(s) Required:** (List specific tools for the CrewAI agent's `tools` array)
*   **Task Parameters Example:** (Illustrative parameters for the `.Task()` object)

### 8.4 Pydantic-AI Specifics:
*   **Relevant Pydantic Models:** (List Pydantic models used for input/output or state)
*   **Function Call Structure (Conceptual):** (How this task might map to a Pydantic-AI runnable/graph component)

## 9. Revision History

| Version | Date       | Author        | Changes                                     |
| :------ | :--------- | :------------ | :------------------------------------------ |
| 1.0     | YYYY-MM-DD | [Author Name] | Initial enhanced version based on new requirements. |
