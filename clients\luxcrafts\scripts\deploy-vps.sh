#!/bin/bash

# Luxcrafts VPS Deployment Script
# Usage: ./deploy-vps.sh [staging|production] [deploy|rollback]

set -e  # Exit on any error

# Configuration
APP_NAME="luxcrafts"
REPO_URL="https://github.com/your-org/luxcrafts.git"
NODE_VERSION="18"
PM2_APP_NAME="luxcrafts-app"

# Environment-specific configurations
if [ "$1" = "staging" ]; then
    SERVER_HOST="staging.luxcrafts.com"
    SERVER_USER="deploy"
    SERVER_PORT="22"
    DEPLOY_PATH="/var/www/staging.luxcrafts.com"
    DOMAIN="staging.luxcrafts.com"
    SSL_EMAIL="<EMAIL>"
    NODE_ENV="staging"
elif [ "$1" = "production" ]; then
    SERVER_HOST="luxcrafts.com"
    SERVER_USER="deploy"
    SERVER_PORT="22"
    DEPLOY_PATH="/var/www/luxcrafts.com"
    DOMAIN="luxcrafts.com"
    SSL_EMAIL="<EMAIL>"
    NODE_ENV="production"
else
    echo "Usage: $0 [staging|production] [deploy|rollback]"
    echo "Environment must be specified: staging or production"
    exit 1
fi

# Action (deploy or rollback)
ACTION=${2:-deploy}

echo "🚀 Starting $ACTION for $1 environment..."
echo "Server: $SERVER_HOST"
echo "Deploy Path: $DEPLOY_PATH"
echo "Domain: $DOMAIN"

# Function to run commands on remote server
run_remote() {
    ssh -p $SERVER_PORT $SERVER_USER@$SERVER_HOST "$1"
}

# Function to copy files to remote server
copy_to_remote() {
    scp -P $SERVER_PORT -r "$1" $SERVER_USER@$SERVER_HOST:"$2"
}

# Function to setup server dependencies
setup_server() {
    echo "📦 Setting up server dependencies..."
    
    run_remote "
        # Update system packages
        sudo apt update && sudo apt upgrade -y
        
        # Install Node.js
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
        
        # Install PM2 globally
        sudo npm install -g pm2
        
        # Install Nginx
        sudo apt install -y nginx
        
        # Install Certbot for SSL
        sudo apt install -y certbot python3-certbot-nginx
        
        # Create deploy directory
        sudo mkdir -p $DEPLOY_PATH
        sudo chown -R $SERVER_USER:$SERVER_USER $DEPLOY_PATH
        
        # Create logs directory
        mkdir -p $DEPLOY_PATH/logs
    "
}

# Function to deploy application
deploy_app() {
    echo "🔄 Deploying application..."
    
    # Build application locally
    echo "🏗️  Building application locally..."
    npm install
    npm run build
    
    # Create deployment package
    echo "📦 Creating deployment package..."
    tar -czf deploy.tar.gz dist package.json package-lock.json
    
    # Copy to server
    echo "📤 Uploading to server..."
    copy_to_remote "deploy.tar.gz" "/tmp/"
    
    # Extract and setup on server
    run_remote "
        cd $DEPLOY_PATH
        
        # Backup current deployment
        if [ -d 'current' ]; then
            sudo rm -rf backup
            sudo mv current backup
        fi
        
        # Create new deployment directory
        mkdir -p current
        cd current
        
        # Extract new deployment
        tar -xzf /tmp/deploy.tar.gz
        
        # Install production dependencies
        npm ci --only=production
        
        # Create environment file
        cat > .env << EOF
NODE_ENV=$NODE_ENV
PORT=3000
VITE_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
VITE_ALCHEMY_API_KEY=your_alchemy_api_key
VITE_INFURA_PROJECT_ID=your_infura_project_id
VITE_API_BASE_URL=https://api.$DOMAIN
VITE_WS_URL=wss://ws.$DOMAIN
VITE_IPFS_GATEWAY=https://ipfs.io/ipfs/
VITE_ENVIRONMENT=$NODE_ENV
EOF
        
        # Set permissions
        sudo chown -R $SERVER_USER:$SERVER_USER $DEPLOY_PATH
        
        # Clean up
        rm /tmp/deploy.tar.gz
    "
    
    # Clean up local files
    rm deploy.tar.gz
}

# Function to configure Nginx
setup_nginx() {
    echo "🌐 Configuring Nginx..."
    
    # Create Nginx configuration
    cat > nginx.conf << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN;
    
    # SSL Configuration (will be managed by Certbot)
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # Main application
    location / {
        root $DEPLOY_PATH/current/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API proxy (if needed)
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # WebSocket proxy (if needed)
    location /ws/ {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}

# Subdomain configurations for role-based access
server {
    listen 443 ssl http2;
    server_name admin.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    location / {
        root $DEPLOY_PATH/current/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Add admin-specific headers
        add_header X-Subdomain "admin" always;
    }
}

server {
    listen 443 ssl http2;
    server_name dev.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    location / {
        root $DEPLOY_PATH/current/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Add dev-specific headers
        add_header X-Subdomain "dev" always;
    }
}

server {
    listen 443 ssl http2;
    server_name ai.$DOMAIN;
    
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    
    location / {
        root $DEPLOY_PATH/current/dist;
        try_files \$uri \$uri/ /index.html;
        
        # Add AI-specific headers
        add_header X-Subdomain "ai" always;
    }
}
EOF
    
    # Copy Nginx configuration to server
    copy_to_remote "nginx.conf" "/tmp/"
    
    run_remote "
        # Install Nginx configuration
        sudo mv /tmp/nginx.conf /etc/nginx/sites-available/$DOMAIN
        sudo ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/
        
        # Remove default site
        sudo rm -f /etc/nginx/sites-enabled/default
        
        # Test Nginx configuration
        sudo nginx -t
        
        # Restart Nginx
        sudo systemctl restart nginx
        sudo systemctl enable nginx
    "
    
    # Clean up local file
    rm nginx.conf
}

# Function to setup SSL certificates
setup_ssl() {
    echo "🔒 Setting up SSL certificates..."
    
    run_remote "
        # Get SSL certificate for main domain and subdomains
        sudo certbot --nginx -d $DOMAIN -d admin.$DOMAIN -d dev.$DOMAIN -d ai.$DOMAIN --email $SSL_EMAIL --agree-tos --non-interactive
        
        # Setup auto-renewal
        sudo systemctl enable certbot.timer
        sudo systemctl start certbot.timer
    "
}

# Function to start application with PM2
start_app() {
    echo "🚀 Starting application with PM2..."
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: 'serve',
    args: '-s dist -l 3000',
    cwd: '$DEPLOY_PATH/current',
    env: {
      NODE_ENV: '$NODE_ENV',
      PORT: 3000
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G',
    error_file: '$DEPLOY_PATH/logs/error.log',
    out_file: '$DEPLOY_PATH/logs/out.log',
    log_file: '$DEPLOY_PATH/logs/combined.log',
    time: true
  }]
};
EOF
    
    # Copy PM2 config to server
    copy_to_remote "ecosystem.config.js" "$DEPLOY_PATH/"
    
    run_remote "
        cd $DEPLOY_PATH
        
        # Install serve globally if not exists
        sudo npm install -g serve
        
        # Stop existing PM2 processes
        pm2 stop $PM2_APP_NAME || true
        pm2 delete $PM2_APP_NAME || true
        
        # Start application
        pm2 start ecosystem.config.js
        
        # Save PM2 configuration
        pm2 save
        
        # Setup PM2 startup
        sudo env PATH=\$PATH:/usr/bin pm2 startup systemd -u $SERVER_USER --hp /home/<USER>
    "
    
    # Clean up local file
    rm ecosystem.config.js
}

# Function to rollback deployment
rollback_app() {
    echo "🔄 Rolling back deployment..."
    
    run_remote "
        cd $DEPLOY_PATH
        
        if [ ! -d 'backup' ]; then
            echo 'No backup found for rollback!'
            exit 1
        fi
        
        # Stop current application
        pm2 stop $PM2_APP_NAME || true
        
        # Swap current and backup
        mv current failed
        mv backup current
        
        # Restart application
        pm2 start ecosystem.config.js
        
        echo 'Rollback completed successfully!'
    "
}

# Function to check deployment health
health_check() {
    echo "🏥 Performing health check..."
    
    # Wait for application to start
    sleep 10
    
    # Check if application is responding
    if curl -f -s "https://$DOMAIN" > /dev/null; then
        echo "✅ Application is healthy and responding"
        return 0
    else
        echo "❌ Application health check failed"
        return 1
    fi
}

# Function to send deployment notification
send_notification() {
    local status=$1
    local message=$2
    
    echo "📢 Deployment $status: $message"
    
    # You can integrate with Slack, Discord, or other notification services here
    # Example Slack webhook:
    # curl -X POST -H 'Content-type: application/json' \
    #   --data "{\"text\":\"Luxcrafts $1 Deployment $status: $message\"}" \
    #   $SLACK_WEBHOOK_URL
}

# Main deployment flow
main() {
    case $ACTION in
        "deploy")
            echo "🚀 Starting deployment process..."
            
            # Setup server if first deployment
            if [ "$3" = "--setup" ]; then
                setup_server
                setup_nginx
                setup_ssl
            fi
            
            # Deploy application
            deploy_app
            start_app
            
            # Health check
            if health_check; then
                send_notification "SUCCESS" "Deployment completed successfully to $1 environment"
                echo "✅ Deployment completed successfully!"
                echo "🌐 Application is available at: https://$DOMAIN"
                echo "🔧 Admin panel: https://admin.$DOMAIN"
                echo "💻 Developer tools: https://dev.$DOMAIN"
                echo "🤖 AI agents: https://ai.$DOMAIN"
            else
                send_notification "FAILED" "Deployment health check failed"
                echo "❌ Deployment failed health check"
                exit 1
            fi
            ;;
        "rollback")
            echo "🔄 Starting rollback process..."
            rollback_app
            
            if health_check; then
                send_notification "SUCCESS" "Rollback completed successfully"
                echo "✅ Rollback completed successfully!"
            else
                send_notification "FAILED" "Rollback health check failed"
                echo "❌ Rollback failed health check"
                exit 1
            fi
            ;;
        *)
            echo "Usage: $0 [staging|production] [deploy|rollback] [--setup]"
            echo "Actions:"
            echo "  deploy   - Deploy the application"
            echo "  rollback - Rollback to previous version"
            echo "Options:"
            echo "  --setup  - Setup server dependencies (first deployment only)"
            exit 1
            ;;
    esac
}

# Run main function
main "$@"

echo "🎉 Deployment script completed!"