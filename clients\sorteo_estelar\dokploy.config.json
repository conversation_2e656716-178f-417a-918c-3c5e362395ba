{"version": "1.0", "project": {"name": "sorteo-estelar", "description": "Web3 NFT-based lottery and gaming ecosystem", "repository": "https://github.com/estratix/sorteo-estelar.git", "branch": "main"}, "services": {"frontend": {"type": "application", "dockerfile": "Dockerfile", "buildContext": ".", "port": 80, "domain": "www.sorteoestelar.com", "ssl": {"enabled": true, "provider": "letsencrypt", "email": "<EMAIL>"}, "environment": {"NODE_ENV": "production", "VITE_API_URL": "https://api.sorteoestelar.com", "VITE_WS_URL": "wss://api.sorteoestelar.com", "VITE_CHAIN_ID": "1", "VITE_INFURA_PROJECT_ID": "${INFURA_PROJECT_ID}", "VITE_WALLETCONNECT_PROJECT_ID": "${WALLETCONNECT_PROJECT_ID}"}, "healthCheck": {"path": "/health", "interval": 30, "timeout": 10, "retries": 3}, "resources": {"memory": "512Mi", "cpu": "0.5"}, "scaling": {"min": 1, "max": 3, "targetCPU": 70}}, "backend": {"type": "application", "dockerfile": "backend/Dockerfile", "buildContext": "./backend", "port": 3001, "domain": "api.sorteoestelar.com", "ssl": {"enabled": true, "provider": "letsencrypt", "email": "<EMAIL>"}, "environment": {"NODE_ENV": "production", "DATABASE_URL": "${DATABASE_URL}", "REDIS_URL": "${REDIS_URL}", "JWT_SECRET": "${JWT_SECRET}", "BINANCE_API_KEY": "${BINANCE_API_KEY}", "BINANCE_SECRET_KEY": "${BINANCE_SECRET_KEY}", "XRPL_NETWORK": "mainnet", "STRIPE_SECRET_KEY": "${STRIPE_SECRET_KEY}", "PAYPAL_CLIENT_ID": "${PAYPAL_CLIENT_ID}", "PAYPAL_CLIENT_SECRET": "${PAYPAL_CLIENT_SECRET}", "SMTP_HOST": "${SMTP_HOST}", "SMTP_PORT": "${SMTP_PORT}", "SMTP_USER": "${SMTP_USER}", "SMTP_PASS": "${SMTP_PASS}"}, "healthCheck": {"path": "/api/health", "interval": 30, "timeout": 10, "retries": 3}, "resources": {"memory": "1Gi", "cpu": "1"}, "scaling": {"min": 2, "max": 5, "targetCPU": 70}}, "database": {"type": "postgres", "version": "15", "storage": "20Gi", "environment": {"POSTGRES_DB": "sorteo_estelar", "POSTGRES_USER": "${DB_USER}", "POSTGRES_PASSWORD": "${DB_PASSWORD}"}, "backup": {"enabled": true, "schedule": "0 2 * * *", "retention": "30d"}}, "redis": {"type": "redis", "version": "7", "storage": "5Gi", "persistence": true}}, "deployment": {"strategy": "rolling", "maxUnavailable": "25%", "maxSurge": "25%", "progressDeadlineSeconds": 600}, "monitoring": {"enabled": true, "prometheus": {"enabled": true, "scrapeInterval": "30s"}, "grafana": {"enabled": true, "domain": "monitoring.sorteoestelar.com"}, "alerts": {"enabled": true, "webhook": "${ALERT_WEBHOOK_URL}"}}, "networking": {"ingress": {"enabled": true, "class": "nginx", "annotations": {"nginx.ingress.kubernetes.io/ssl-redirect": "true", "nginx.ingress.kubernetes.io/force-ssl-redirect": "true", "cert-manager.io/cluster-issuer": "letsencrypt-prod"}}, "loadBalancer": {"enabled": true, "type": "nginx", "sticky": false}}, "security": {"networkPolicies": true, "podSecurityStandards": "restricted", "secrets": {"encryption": "aes-256", "rotation": "90d"}}, "ci": {"enabled": true, "triggers": {"push": ["main", "develop"], "pullRequest": true}, "stages": {"test": {"enabled": true, "commands": ["npm ci", "npm run test", "npm run lint", "npm run type-check"]}, "security": {"enabled": true, "commands": ["npm audit", "docker scan"]}, "build": {"enabled": true, "cache": true}, "deploy": {"enabled": true, "environments": {"staging": {"branch": "develop", "domain": "staging.sorteoestelar.com"}, "production": {"branch": "main", "domain": "www.sorteoestelar.com", "approval": true}}}}}}