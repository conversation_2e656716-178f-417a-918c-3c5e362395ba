# ESTRATIX Security Configuration
# Comprehensive security setup for VPS infrastructure

---
# Database Credentials Secret
apiVersion: v1
kind: Secret
metadata:
  name: database-credentials
  namespace: command-headquarters
type: Opaque
data:
  username: ZXN0cmF0aXg=  # estratix (base64)
  password: RXN0cmF0aXgyMDI0IQ==  # Estratix2024! (base64)
  cpo-db-url: ****************************************************************************************************
  data-warehouse-url: ****************************************************************************************************

---
# Kubernetes Config Secret for CTO Access
apiVersion: v1
kind: Secret
metadata:
  name: kube-config-secret
  namespace: command-headquarters
type: Opaque
data:
  config: LS0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQojIFBsYWNlaG9sZGVyIGZvciBhY3R1YWwga3ViZWNvbmZpZyBjb250ZW50CiMgVGhpcyBzaG91bGQgYmUgcmVwbGFjZWQgd2l0aCBhY3R1YWwgY29uZmlnCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0=

---
# Service Account for Command Operations
apiVersion: v1
kind: ServiceAccount
metadata:
  name: command-operations
  namespace: command-headquarters
automountServiceAccountToken: true

---
# RBAC Role for Command Operations
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: command-headquarters
  name: command-operations-role
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
# RBAC RoleBinding for Command Operations
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: command-operations-binding
  namespace: command-headquarters
subjects:
- kind: ServiceAccount
  name: command-operations
  namespace: command-headquarters
roleRef:
  kind: Role
  name: command-operations-role
  apiGroup: rbac.authorization.k8s.io

---
# Cluster Role for CTO Infrastructure Access
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cto-infrastructure-access
rules:
- apiGroups: [""]
  resources: ["nodes", "namespaces", "persistentvolumes"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "daemonsets", "statefulsets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["networking.k8s.io"]
  resources: ["networkpolicies", "ingresses"]
  verbs: ["get", "list", "watch", "create", "update", "patch"]
- apiGroups: ["metrics.k8s.io"]
  resources: ["nodes", "pods"]
  verbs: ["get", "list"]

---
# Service Account for CTO
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cto-service-account
  namespace: command-headquarters

---
# Cluster RoleBinding for CTO
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cto-infrastructure-binding
subjects:
- kind: ServiceAccount
  name: cto-service-account
  namespace: command-headquarters
roleRef:
  kind: ClusterRole
  name: cto-infrastructure-access
  apiGroup: rbac.authorization.k8s.io

---
# Pod Security Policy for Command Headquarters
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: command-headquarters-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'

---
# Security Context Constraints (OpenShift compatible)
apiVersion: security.openshift.io/v1
kind: SecurityContextConstraints
metadata:
  name: command-headquarters-scc
allowHostDirVolumePlugin: false
allowHostIPC: false
allowHostNetwork: false
allowHostPID: false
allowHostPorts: false
allowPrivilegedContainer: false
allowedCapabilities: null
defaultAddCapabilities: null
fsGroup:
  type: RunAsAny
readOnlyRootFilesystem: false
requiredDropCapabilities:
- ALL
runAsUser:
  type: MustRunAsNonRoot
seLinuxContext:
  type: RunAsAny
supplementalGroups:
  type: RunAsAny
volumes:
- configMap
- downwardAPI
- emptyDir
- persistentVolumeClaim
- projected
- secret

---
# Network Policy for Database Access
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: database-access-policy
  namespace: command-headquarters
spec:
  podSelector:
    matchLabels:
      app: command-database
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          office: ceo
    - podSelector:
        matchLabels:
          office: cpo
    - podSelector:
        matchLabels:
          office: cpoo
    - podSelector:
        matchLabels:
          office: cto
    - podSelector:
        matchLabels:
          office: cio
    - podSelector:
        matchLabels:
          office: cdo
    - podSelector:
        matchLabels:
          office: cseco
    ports:
    - protocol: TCP
      port: 5432

---
# Network Policy for Redis Access
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: redis-access-policy
  namespace: command-headquarters
spec:
  podSelector:
    matchLabels:
      app: command-redis
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector: {}
    ports:
    - protocol: TCP
      port: 6379

---
# TLS Certificate for Command Headquarters
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: command-headquarters-cert
  namespace: command-headquarters
spec:
  secretName: command-headquarters-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - command.estratix.com
  - "*.command.estratix.com"

---
# Cluster Issuer for Let's Encrypt
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: traefik

---
# ConfigMap for Security Policies
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-policies
  namespace: command-headquarters
data:
  password-policy.json: |
    {
      "minLength": 12,
      "requireUppercase": true,
      "requireLowercase": true,
      "requireNumbers": true,
      "requireSpecialChars": true,
      "maxAge": 90,
      "historyCount": 12
    }
  access-policy.json: |
    {
      "sessionTimeout": 3600,
      "maxFailedAttempts": 3,
      "lockoutDuration": 900,
      "requireMFA": true,
      "allowedIPs": ["************/22"]
    }
  encryption-policy.json: |
    {
      "algorithm": "AES-256-GCM",
      "keyRotationDays": 30,
      "encryptAtRest": true,
      "encryptInTransit": true
    }

---
# Secret for JWT Tokens
apiVersion: v1
kind: Secret
metadata:
  name: jwt-secret
  namespace: command-headquarters
type: Opaque
data:
  secret: RXN0cmF0aXhKV1RTZWNyZXQyMDI0IUAjJA==  # EstrategixJWTSecret2024!@#$ (base64)
  
---
# Secret for API Keys
apiVersion: v1
kind: Secret
metadata:
  name: api-keys
  namespace: command-headquarters
type: Opaque
data:
  prometheus-api-key: cHJvbWV0aGV1c19hcGlfa2V5XzIwMjQ=
  grafana-api-key: Z3JhZmFuYV9hcGlfa2V5XzIwMjQ=
  coolify-api-key: Y29vbGlmeV9hcGlfa2V5XzIwMjQ=