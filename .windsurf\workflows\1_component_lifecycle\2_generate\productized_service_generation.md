---
description: "Guides the agentic implementation of a defined ESTRATIX Productized Service, orchestrating the generation of its constituent projects, flows, and microservices."
---

# ESTRATIX Generative Workflow: Productized Service Generation

## Objective

To automate the scaffolding and initial implementation of a new ESTRATIX Productized Service based on a completed and approved definition document. This workflow orchestrates other generative workflows to create the necessary sub-project, microservices, and agentic flows required to deliver the service.

## Pre-requisites

* A completed and approved Productized Service Definition document, located in its correct directory under `docs/services/productized_services/`.
* The service must be registered in the `productized_services_matrix.md`.

## Workflow Steps

### 1. Initiation

- **Action:** The user or a `AGENT_ProductManager_Lead` triggers this workflow, providing the `Service_ID` of the productized service to be generated.

### 2. Parse Service Definition

- **Action:** Read the corresponding service definition file to extract key requirements.
- **Tool:** `view_file_outline`
- **Example Command:** `<!-- view_file_outline('docs/services/productized_services/[Service_ID]_[ServiceName_PascalCase]_Definition.md') -->`
- **Guidance:** Extract constituent processes, flows, and technology stack requirements.

### 3. Create Dedicated Sub-Project

- **Action:** Follow the `estratix_project_definition.md` and `estratix_project_generation.md` workflows to create a formal sub-project for this service.
- **Guidance:** This is a conceptual step. The agent must read and execute the steps in the referenced workflows.

### 4. Scaffold Core Service Infrastructure

- **Action:** Based on the technology stack, scaffold the required infrastructure.
- **Guidance:** If a new FastAPI microservice is needed, follow the `bootstrap_fastapi_service.md` workflow.

### 5. Generate Agentic Implementation Layer

- **Action:** Generate the agentic layer that orchestrates the underlying processes.
- **Guidance:** Use the `flow_generation.md` and `agents_generation_from_processes-[framework].md` workflows to create the necessary crews, agents, and tasks.

### 6. Link Components

- **Action:** Update definition files to ensure all generated components are linked.
- **Tool:** `replace_file_content`
- **Guidance:** This is an illustrative example. The agent must find the correct target content to append the links.
- **Example Command:**

  ```markdown
  <!-- replace_file_content(
      'docs/services/productized_services/[Service_ID]_[ServiceName_PascalCase]_Definition.md',
      replacement_chunks=[{
          'target_content': '## Related Documents',
          'replacement_content': '## Related Documents\n- [Sub-Project Definition](path/to/subproject_definition.md)\n- [Implementation](path/to/implementation_readme.md)'
      }]
  ) -->
  ```

### 7. Finalization & Reporting

- **Action:** Update the `productized_services_matrix.md` to reflect the new status.
- **Tool:** `replace_file_content`
- **Example Command:**

  ```markdown
  <!-- replace_file_content(
      'docs/matrices/productized_services_matrix.md',
      replacement_chunks=[{
          'target_content': '| [Service_ID] | ... | Defined |',
          'replacement_content': '| [Service_ID] | ... | In Development |'
      }]
  ) -->
  ```

- **Action:** Report the successful creation of all artifacts and provide direct links to the key generated components.

## Outputs

* A new sub-project for managing the productized service.
* A fully scaffolded microservice (if required) with a hexagonal architecture.
* A set of generated agentic flows, crews, and tasks for service delivery.
* Updated matrices and definition files with cross-references.
