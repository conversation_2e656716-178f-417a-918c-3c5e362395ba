import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { environment } from '@/config/environment';

export default async function healthRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Basic health check
  fastify.get('/', {
    schema: {
      description: 'Basic health check',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            service: { type: 'string' },
            version: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'agent-orchestration',
      version: '1.0.0'
    };
  });

  // Detailed health check
  fastify.get('/detailed', {
    schema: {
      description: 'Detailed health check with service dependencies',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            timestamp: { type: 'string' },
            service: { type: 'string' },
            version: { type: 'string' },
            environment: { type: 'string' },
            uptime: { type: 'number' },
            memory: {
              type: 'object',
              properties: {
                used: { type: 'number' },
                total: { type: 'number' },
                percentage: { type: 'number' }
              }
            },
            services: {
              type: 'object',
              properties: {
                database: { type: 'string' },
                redis: { type: 'string' },
                agentService: { type: 'string' },
                workflowService: { type: 'string' },
                orchestrationService: { type: 'string' },
                websocketService: { type: 'string' },
                analyticsService: { type: 'string' }
              }
            },
            externalServices: {
              type: 'object',
              properties: {
                projectManagement: { type: 'string' },
                smartContracts: { type: 'string' },
                contentStudio: { type: 'string' },
                clientOnboarding: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const memoryUsage = process.memoryUsage();
    const memoryUsedMB = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const memoryTotalMB = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    const memoryPercentage = Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100);

    // Check service statuses
    const serviceStatuses = {
      database: 'unknown',
      redis: 'unknown',
      agentService: 'unknown',
      workflowService: 'unknown',
      orchestrationService: 'unknown',
      websocketService: 'unknown',
      analyticsService: 'unknown'
    };

    try {
      if (fastify.agentService) {
        serviceStatuses.agentService = await fastify.agentService.getStatus();
      }
      if (fastify.workflowService) {
        serviceStatuses.workflowService = await fastify.workflowService.getStatus();
      }
      if (fastify.orchestrationService) {
        serviceStatuses.orchestrationService = await fastify.orchestrationService.getStatus();
      }
      if (fastify.websocketService) {
        serviceStatuses.websocketService = await fastify.websocketService.getStatus();
      }
      if (fastify.analyticsService) {
        serviceStatuses.analyticsService = await fastify.analyticsService.getStatus();
      }
    } catch (error) {
      fastify.log.warn(error, 'Error checking service statuses');
    }

    // Check external services
    const externalServiceStatuses = {
      projectManagement: 'unknown',
      smartContracts: 'unknown',
      contentStudio: 'unknown',
      clientOnboarding: 'unknown'
    };

    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'agent-orchestration',
      version: '1.0.0',
      environment: environment.nodeEnv,
      uptime: process.uptime(),
      memory: {
        used: memoryUsedMB,
        total: memoryTotalMB,
        percentage: memoryPercentage
      },
      services: serviceStatuses,
      externalServices: externalServiceStatuses
    };
  });

  // Readiness probe
  fastify.get('/ready', {
    schema: {
      description: 'Readiness probe for Kubernetes',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            ready: { type: 'boolean' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    let ready = true;
    
    try {
      // Check if core services are ready
      if (fastify.agentService) {
        const status = await fastify.agentService.getStatus();
        if (status !== 'healthy') ready = false;
      }
      
      if (fastify.orchestrationService) {
        const status = await fastify.orchestrationService.getStatus();
        if (status !== 'healthy') ready = false;
      }
    } catch (error) {
      ready = false;
    }
    
    if (!ready) {
      reply.status(503);
    }
    
    return {
      ready,
      timestamp: new Date().toISOString()
    };
  });

  // Liveness probe
  fastify.get('/live', {
    schema: {
      description: 'Liveness probe for Kubernetes',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            alive: { type: 'boolean' },
            timestamp: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    return {
      alive: true,
      timestamp: new Date().toISOString()
    };
  });

  // Metrics endpoint
  fastify.get('/metrics', {
    schema: {
      description: 'Service metrics',
      tags: ['Health'],
      response: {
        200: {
          type: 'object',
          properties: {
            timestamp: { type: 'string' },
            metrics: {
              type: 'object',
              properties: {
                activeAgents: { type: 'number' },
                runningWorkflows: { type: 'number' },
                completedWorkflows: { type: 'number' },
                failedWorkflows: { type: 'number' },
                queuedTasks: { type: 'number' },
                averageExecutionTime: { type: 'number' },
                memoryUsage: { type: 'number' },
                cpuUsage: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const metrics = {
      activeAgents: 0,
      runningWorkflows: 0,
      completedWorkflows: 0,
      failedWorkflows: 0,
      queuedTasks: 0,
      averageExecutionTime: 0,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      cpuUsage: 0
    };

    try {
      if (fastify.agentService) {
        const agentMetrics = await fastify.agentService.getMetrics();
        metrics.activeAgents = agentMetrics.activeAgents || 0;
      }
      
      if (fastify.workflowService) {
        const workflowMetrics = await fastify.workflowService.getMetrics();
        metrics.runningWorkflows = workflowMetrics.running || 0;
        metrics.completedWorkflows = workflowMetrics.completed || 0;
        metrics.failedWorkflows = workflowMetrics.failed || 0;
        metrics.averageExecutionTime = workflowMetrics.averageExecutionTime || 0;
      }
      
      if (fastify.orchestrationService) {
        const orchestrationMetrics = await fastify.orchestrationService.getMetrics();
        metrics.queuedTasks = orchestrationMetrics.queuedTasks || 0;
      }
    } catch (error) {
      fastify.log.warn(error, 'Error collecting metrics');
    }

    return {
      timestamp: new Date().toISOString(),
      metrics
    };
  });
}