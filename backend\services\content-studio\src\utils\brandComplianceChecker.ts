import { logger } from './logger';

export interface BrandGuidelines {
  brandName: string;
  brandVoice: string;
  brandValues: string[];
  doNotUse?: string[];
  preferredTerms?: { [key: string]: string };
  toneGuidelines?: {
    formal: boolean;
    friendly: boolean;
    professional: boolean;
    casual: boolean;
  };
  colorPalette?: string[];
  typography?: {
    primaryFont: string;
    secondaryFont: string;
  };
}

export interface ComplianceCheckResult {
  isCompliant: boolean;
  score: number;
  violations: ComplianceViolation[];
  suggestions: string[];
  brandAlignment: {
    voice: number;
    values: number;
    terminology: number;
    tone: number;
  };
}

export interface ComplianceViolation {
  type: 'terminology' | 'tone' | 'values' | 'voice' | 'prohibited';
  severity: 'low' | 'medium' | 'high';
  description: string;
  suggestion: string;
  location?: {
    start: number;
    end: number;
  };
}

export class BrandComplianceChecker {
  private defaultGuidelines: BrandGuidelines;

  constructor() {
    this.defaultGuidelines = {
      brandName: 'ESTRATIX',
      brandVoice: 'Professional, innovative, and client-focused',
      brandValues: ['Innovation', 'Excellence', 'Integrity', 'Client Success', 'Collaboration'],
      doNotUse: ['cheap', 'basic', 'simple', 'amateur', 'low-quality'],
      preferredTerms: {
        'customers': 'clients',
        'users': 'clients',
        'cheap': 'cost-effective',
        'basic': 'essential',
        'simple': 'streamlined'
      },
      toneGuidelines: {
        formal: true,
        friendly: true,
        professional: true,
        casual: false
      }
    };
    
    logger.info('BrandComplianceChecker initialized');
  }

  async checkCompliance(content: string, guidelines?: BrandGuidelines): Promise<ComplianceCheckResult> {
    try {
      const brandGuidelines = guidelines || this.defaultGuidelines;
      logger.info(`Checking brand compliance for: ${brandGuidelines.brandName}`);

      const violations: ComplianceViolation[] = [];
      const suggestions: string[] = [];

      // Check for prohibited terms
      const prohibitedViolations = this.checkProhibitedTerms(content, brandGuidelines);
      violations.push(...prohibitedViolations);

      // Check terminology compliance
      const terminologyViolations = this.checkTerminology(content, brandGuidelines);
      violations.push(...terminologyViolations);

      // Check tone compliance
      const toneViolations = this.checkToneCompliance(content, brandGuidelines);
      violations.push(...toneViolations);

      // Check brand values alignment
      const valuesViolations = this.checkBrandValues(content, brandGuidelines);
      violations.push(...valuesViolations);

      // Check brand voice consistency
      const voiceViolations = this.checkBrandVoice(content, brandGuidelines);
      violations.push(...voiceViolations);

      // Calculate compliance score
      const score = this.calculateComplianceScore(violations);
      const isCompliant = score >= 80;

      // Generate suggestions
      suggestions.push(...this.generateComplianceSuggestions(violations, brandGuidelines));

      // Calculate brand alignment scores
      const brandAlignment = this.calculateBrandAlignment(content, brandGuidelines, violations);

      return {
        isCompliant,
        score,
        violations,
        suggestions,
        brandAlignment
      };
    } catch (error) {
      logger.error('Error checking brand compliance:', error);
      throw new Error(`Brand compliance check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async suggestBrandAlignedContent(content: string, guidelines?: BrandGuidelines): Promise<string> {
    try {
      const brandGuidelines = guidelines || this.defaultGuidelines;
      let alignedContent = content;

      // Replace prohibited terms with preferred alternatives
      if (brandGuidelines.preferredTerms) {
        for (const [prohibited, preferred] of Object.entries(brandGuidelines.preferredTerms)) {
          const regex = new RegExp(`\\b${prohibited}\\b`, 'gi');
          alignedContent = alignedContent.replace(regex, preferred);
        }
      }

      // Remove or replace prohibited words
      if (brandGuidelines.doNotUse) {
        for (const prohibitedWord of brandGuidelines.doNotUse) {
          const regex = new RegExp(`\\b${prohibitedWord}\\b`, 'gi');
          alignedContent = alignedContent.replace(regex, '[NEEDS_REPLACEMENT]');
        }
      }

      return alignedContent;
    } catch (error) {
      logger.error('Error suggesting brand-aligned content:', error);
      throw new Error(`Brand alignment suggestion failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private checkProhibitedTerms(content: string, guidelines: BrandGuidelines): ComplianceViolation[] {
    const violations: ComplianceViolation[] = [];
    const contentLower = content.toLowerCase();

    if (guidelines.doNotUse) {
      for (const prohibitedTerm of guidelines.doNotUse) {
        const regex = new RegExp(`\\b${prohibitedTerm.toLowerCase()}\\b`, 'g');
        const matches = [...contentLower.matchAll(regex)];
        
        for (const match of matches) {
          violations.push({
            type: 'prohibited',
            severity: 'high',
            description: `Prohibited term "${prohibitedTerm}" found in content`,
            suggestion: guidelines.preferredTerms?.[prohibitedTerm] 
              ? `Replace with "${guidelines.preferredTerms[prohibitedTerm]}"`
              : `Remove or replace "${prohibitedTerm}" with brand-appropriate alternative`,
            location: {
              start: match.index || 0,
              end: (match.index || 0) + prohibitedTerm.length
            }
          });
        }
      }
    }

    return violations;
  }

  private checkTerminology(content: string, guidelines: BrandGuidelines): ComplianceViolation[] {
    const violations: ComplianceViolation[] = [];
    const contentLower = content.toLowerCase();

    if (guidelines.preferredTerms) {
      for (const [discouraged, preferred] of Object.entries(guidelines.preferredTerms)) {
        const regex = new RegExp(`\\b${discouraged.toLowerCase()}\\b`, 'g');
        const matches = [...contentLower.matchAll(regex)];
        
        for (const match of matches) {
          violations.push({
            type: 'terminology',
            severity: 'medium',
            description: `Non-preferred term "${discouraged}" found`,
            suggestion: `Consider using "${preferred}" instead of "${discouraged}"`,
            location: {
              start: match.index || 0,
              end: (match.index || 0) + discouraged.length
            }
          });
        }
      }
    }

    return violations;
  }

  private checkToneCompliance(content: string, guidelines: BrandGuidelines): ComplianceViolation[] {
    const violations: ComplianceViolation[] = [];
    
    if (!guidelines.toneGuidelines) return violations;

    const toneAnalysis = this.analyzeTone(content);
    
    // Check if content tone aligns with brand guidelines
    if (!guidelines.toneGuidelines.casual && toneAnalysis.isCasual) {
      violations.push({
        type: 'tone',
        severity: 'medium',
        description: 'Content tone is too casual for brand guidelines',
        suggestion: 'Use more professional language and formal structure'
      });
    }
    
    if (!guidelines.toneGuidelines.formal && toneAnalysis.isFormal) {
      violations.push({
        type: 'tone',
        severity: 'low',
        description: 'Content tone is too formal for brand guidelines',
        suggestion: 'Use more conversational and approachable language'
      });
    }

    return violations;
  }

  private checkBrandValues(content: string, guidelines: BrandGuidelines): ComplianceViolation[] {
    const violations: ComplianceViolation[] = [];
    const contentLower = content.toLowerCase();
    
    // Check if brand values are reflected in content
    const valuesFound = guidelines.brandValues.filter(value => 
      contentLower.includes(value.toLowerCase())
    );
    
    if (valuesFound.length === 0 && guidelines.brandValues.length > 0) {
      violations.push({
        type: 'values',
        severity: 'medium',
        description: 'Content does not reflect any brand values',
        suggestion: `Consider incorporating brand values: ${guidelines.brandValues.join(', ')}`
      });
    }

    return violations;
  }

  private checkBrandVoice(content: string, guidelines: BrandGuidelines): ComplianceViolation[] {
    const violations: ComplianceViolation[] = [];
    
    // Simple brand voice analysis based on keywords
    const voiceKeywords = guidelines.brandVoice.toLowerCase().split(/[,\s]+/);
    const contentLower = content.toLowerCase();
    
    const voiceAlignment = voiceKeywords.filter(keyword => 
      contentLower.includes(keyword)
    ).length / voiceKeywords.length;
    
    if (voiceAlignment < 0.3) {
      violations.push({
        type: 'voice',
        severity: 'medium',
        description: 'Content does not align with brand voice',
        suggestion: `Ensure content reflects brand voice: ${guidelines.brandVoice}`
      });
    }

    return violations;
  }

  private calculateComplianceScore(violations: ComplianceViolation[]): number {
    let score = 100;
    
    for (const violation of violations) {
      switch (violation.severity) {
        case 'high':
          score -= 15;
          break;
        case 'medium':
          score -= 10;
          break;
        case 'low':
          score -= 5;
          break;
      }
    }
    
    return Math.max(0, score);
  }

  private generateComplianceSuggestions(violations: ComplianceViolation[], guidelines: BrandGuidelines): string[] {
    const suggestions: string[] = [];
    
    const violationTypes = new Set(violations.map(v => v.type));
    
    if (violationTypes.has('prohibited')) {
      suggestions.push('Remove or replace prohibited terms with brand-appropriate alternatives');
    }
    
    if (violationTypes.has('terminology')) {
      suggestions.push('Use preferred brand terminology consistently throughout content');
    }
    
    if (violationTypes.has('tone')) {
      suggestions.push(`Adjust content tone to match brand guidelines: ${JSON.stringify(guidelines.toneGuidelines)}`);
    }
    
    if (violationTypes.has('values')) {
      suggestions.push(`Incorporate brand values into content: ${guidelines.brandValues.join(', ')}`);
    }
    
    if (violationTypes.has('voice')) {
      suggestions.push(`Align content with brand voice: ${guidelines.brandVoice}`);
    }
    
    // General suggestions
    suggestions.push('Review brand guidelines before content creation');
    suggestions.push('Use brand-approved language and messaging');
    
    return suggestions;
  }

  private calculateBrandAlignment(content: string, guidelines: BrandGuidelines, violations: ComplianceViolation[]): {
    voice: number;
    values: number;
    terminology: number;
    tone: number;
  } {
    const voiceViolations = violations.filter(v => v.type === 'voice').length;
    const valuesViolations = violations.filter(v => v.type === 'values').length;
    const terminologyViolations = violations.filter(v => v.type === 'terminology').length;
    const toneViolations = violations.filter(v => v.type === 'tone').length;
    
    return {
      voice: Math.max(0, 100 - (voiceViolations * 20)),
      values: Math.max(0, 100 - (valuesViolations * 25)),
      terminology: Math.max(0, 100 - (terminologyViolations * 15)),
      tone: Math.max(0, 100 - (toneViolations * 20))
    };
  }

  private analyzeTone(content: string): { isCasual: boolean; isFormal: boolean } {
    const casualIndicators = ['hey', 'awesome', 'cool', 'yeah', 'totally', 'super', 'gonna', 'wanna'];
    const formalIndicators = ['therefore', 'furthermore', 'consequently', 'moreover', 'nevertheless', 'however'];
    
    const words = content.toLowerCase().match(/\b\w+\b/g) || [];
    
    const casualCount = words.filter(word => casualIndicators.includes(word)).length;
    const formalCount = words.filter(word => formalIndicators.includes(word)).length;
    
    return {
      isCasual: casualCount > formalCount,
      isFormal: formalCount > casualCount
    };
  }
}

export default BrandComplianceChecker;