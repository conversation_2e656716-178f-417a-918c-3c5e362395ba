---
trigger: always_on
---

# ESTRATIX Development Workflow Standards - High Momentum Operations

## 1. Core Development Philosophy

### 1.1. High Impulse & Momentum Principles

**ESTRATIX operates on HIGH MOMENTUM, RESULTS-DRIVEN development cycles:**

- **Velocity First**: Every development action must contribute to measurable progress
- **UV-Powered Execution**: All Python operations use `uv run` for maximum efficiency
- **Continuous Delivery**: Ship working code daily, iterate rapidly
- **Autonomous Operations**: Minimize manual intervention, maximize automation
- **Strategic Focus**: Every task aligns with quarterly objectives

### 1.2. Development Velocity Metrics

```bash
# Daily velocity tracking
uv run python scripts/track_velocity.py --date $(date +%Y-%m-%d)

# Sprint momentum analysis
uv run python scripts/sprint_analysis.py --sprint current

# Productivity dashboard
uv run python scripts/productivity_dashboard.py --realtime
```

## 2. UV-Powered Development Workflow

### 2.1. Project Initialization (High Speed)

```bash
# Rapid project bootstrap
uv init estratix-[component-name]
cd estratix-[component-name]

# Install core dependencies (fast)
uv add fastapi pydantic pydantic-ai crewai
uv add --dev pytest ruff mypy black

# Generate project structure
uv run python -m cookiecutter gh:estratix/project-template

# Initialize git and commit
git init && git add . && git commit -m "feat: bootstrap [component-name]"
```

### 2.2. Daily Development Cycle

```bash
# Morning startup (< 30 seconds)
uv sync --dev                           # Sync dependencies
uv run pytest -m "unit" --maxfail=1    # Quick health check
uv run ruff check src/                  # Code quality check

# Development loop
uv run --dev src/main.py               # Start dev server
uv run pytest --lf                     # Test last failed
uv run mypy src/                       # Type checking

# End of day (< 60 seconds)
uv run pytest --cov=src               # Full test suite
uv run ruff format src/                # Format code
git add . && git commit -m "feat: [description]"
git push origin main
```

### 2.3. Continuous Integration Workflow

```bash
# Pre-commit validation (< 10 seconds)
uv run pytest -m "unit" --maxfail=3
uv run ruff check --fix src/
uv run mypy src/

# CI pipeline execution
uv run pytest --cov=src --cov-fail-under=80
uv run ruff check src/ tests/
uv run mypy src/ tests/
uv run bandit -r src/
```

## 3. High-Performance Development Patterns

### 3.1. Rapid Prototyping

```bash
# Quick prototype creation
uv run python -c "
import sys
sys.path.append('src')
from prototype import QuickTest
QuickTest().run()
"

# Interactive development
uv run ipython --profile=estratix

# Jupyter notebook development
uv run jupyter lab --port=8888
```

### 3.2. Agent Development Acceleration

```bash
# Generate agent scaffold
uv run python scripts/generate_agent.py --name CTO --type command_office

# Test agent immediately
uv run pytest tests/agents/test_cto.py -v

# Deploy agent to development
uv run python scripts/deploy_agent.py --agent CTO --env dev
```

### 3.3. Tool Development Speed

```bash
# Create tool from template
uv run python scripts/create_tool.py --name DocumentProcessor --category processing

# Validate tool interface
uv run python scripts/validate_tool.py --tool DocumentProcessor

# Integration test
uv run pytest tests/tools/test_document_processor.py --integration
```

## 4. Automated Quality Assurance

### 4.1. Real-Time Code Quality

```bash
# Watch mode for continuous quality
uv run watchdog --patterns="*.py" --command="uv run ruff check {watch_src_path}"

# Auto-format on save
uv run black --watch src/

# Continuous type checking
uv run mypy --follow-imports=silent --watch src/
```

### 4.2. Performance Monitoring

```bash
# Profile application performance
uv run python -m cProfile -o profile.stats src/main.py

# Memory usage tracking
uv run python -m memory_profiler src/main.py

# Benchmark critical paths
uv run pytest tests/benchmarks/ --benchmark-only
```

## 5. Strategic Task Execution

### 5.1. Task Prioritization Matrix

```python
# High Priority (Execute Immediately)
HIGH_PRIORITY_TASKS = [
    "uv run pytest tests/critical/",
    "uv run python scripts/deploy_production.py",
    "uv run python scripts/security_scan.py",
]

# Medium Priority (Execute Daily)
MEDIUM_PRIORITY_TASKS = [
    "uv run pytest --cov=src",
    "uv run ruff check src/",
    "uv run mypy src/",
]

# Low Priority (Execute Weekly)
LOW_PRIORITY_TASKS = [
    "uv run python scripts/dependency_audit.py",
    "uv run python scripts/documentation_update.py",
    "uv run python scripts/performance_analysis.py",
]
```

### 5.2. Automated Task Execution

```bash
# Execute high-priority tasks
uv run python scripts/execute_priority_tasks.py --level high

# Daily automation
uv run python scripts/daily_automation.py

# Weekly maintenance
uv run python scripts/weekly_maintenance.py
```

## 6. Agentic Development Acceleration

### 6.1. Multi-LLM Orchestration

```bash
# Initialize multi-LLM framework
uv run python scripts/init_multi_llm.py --providers openai,anthropic,google

# Test LLM routing
uv run pytest tests/llm/test_routing.py

# Deploy LLM orchestration
uv run python scripts/deploy_llm_orchestration.py
```

### 6.2. Agent Registration & Management

```bash
# Register new agent
uv run python scripts/register_agent.py --name DocumentProcessor --type service

# Update agent capabilities
uv run python scripts/update_agent.py --agent DocumentProcessor --capabilities "pdf,docx,txt"

# Monitor agent performance
uv run python scripts/monitor_agents.py --realtime
```

### 6.3. Command Office Operations

```bash
# Bootstrap command office
uv run python scripts/bootstrap_command_office.py --office CTO

# Deploy command office
uv run python scripts/deploy_command_office.py --office CTO --env production

# Monitor command office health
uv run python scripts/monitor_command_office.py --office CTO
```

## 7. Performance Optimization

### 7.1. Code Performance

```bash
# Profile critical paths
uv run python -m cProfile -s cumulative src/main.py

# Memory optimization
uv run python -m memory_profiler src/main.py

# Async performance testing
uv run pytest tests/performance/ --benchmark-only
```

### 7.2. Database Performance

```bash
# Vector database optimization
uv run python scripts/optimize_vector_db.py --database milvus

# MongoDB performance tuning
uv run python scripts/optimize_mongodb.py --collection documents

# Neo4j graph optimization
uv run python scripts/optimize_neo4j.py --indexes all
```

### 7.3. Infrastructure Performance

```bash
# Kubernetes resource optimization
uv run python scripts/optimize_k8s_resources.py

# Load balancer configuration
uv run python scripts/configure_load_balancer.py

# Monitoring and alerting setup
uv run python scripts/setup_monitoring.py
```

## 8. Security & Compliance

### 8.1. Security Scanning

```bash
# Security vulnerability scan
uv run bandit -r src/ -f json -o security_report.json

# Dependency security audit
uv run safety check --json

# SAST analysis
uv run semgrep --config=auto src/
```

### 8.2. Compliance Validation

```bash
# Code quality compliance
uv run ruff check src/ --format=json

# Type safety compliance
uv run mypy src/ --strict

# Documentation compliance
uv run python scripts/validate_documentation.py
```

## 9. Deployment & Operations

### 9.1. Containerized Deployment

```bash
# Build optimized container
uv run python scripts/build_container.py --optimize

# Deploy to Kubernetes
uv run python scripts/deploy_k8s.py --namespace estratix

# Health check deployment
uv run python scripts/health_check.py --service all
```

### 9.2. Monitoring & Observability

```bash
# Setup observability stack
uv run python scripts/setup_observability.py

# Configure alerts
uv run python scripts/configure_alerts.py

# Generate performance reports
uv run python scripts/generate_performance_report.py
```

## 10. Continuous Improvement

### 10.1. Metrics Collection

```bash
# Collect development metrics
uv run python scripts/collect_dev_metrics.py

# Analyze productivity trends
uv run python scripts/analyze_productivity.py

# Generate improvement recommendations
uv run python scripts/generate_improvements.py
```

### 10.2. Process Optimization

```bash
# Optimize development workflows
uv run python scripts/optimize_workflows.py

# Update automation scripts
uv run python scripts/update_automation.py

# Validate process improvements
uv run python scripts/validate_improvements.py
```

---

## Enforcement & Compliance

- **Automated Enforcement**: All standards are enforced through CI/CD pipelines
- **Real-time Monitoring**: Development velocity and quality metrics are tracked continuously
- **Continuous Improvement**: Standards are updated based on performance data and team feedback
- **Strategic Alignment**: All development activities align with quarterly objectives and business goals

---

**Last Updated**: 2025-01-27  
**Version**: 2.0  
**Status**: Active - High Momentum Operations