# 🚀 ESTRATIX Agency VPS Deployment Guide

## Overview

This comprehensive guide outlines the enhanced SSH-based deployment workflow for deploying Luxcrafts applications to client VPS infrastructure with custom domain pointing. This solution addresses enterprise-grade requirements for agency deployments.

## 📋 Table of Contents

1. [Current State Analysis](#current-state-analysis)
2. [Identified Gaps & Improvements](#identified-gaps--improvements)
3. [Enhanced Deployment Architecture](#enhanced-deployment-architecture)
4. [Prerequisites](#prerequisites)
5. [Quick Start Guide](#quick-start-guide)
6. [Detailed Deployment Process](#detailed-deployment-process)
7. [Security Hardening](#security-hardening)
8. [Monitoring & Alerting](#monitoring--alerting)
9. [Backup & Recovery](#backup--recovery)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)
12. [Questions & Considerations](#questions--considerations)

## 🔍 Current State Analysis

### Existing Infrastructure

✅ **Strengths:**
- Dokploy-based deployment system
- Docker containerization
- SSL/TLS configuration
- Basic monitoring setup
- Nginx reverse proxy
- Automated backup system

❌ **Limitations:**
- Limited SSH automation
- Manual domain configuration
- Basic security hardening
- Minimal monitoring coverage
- No multi-environment support
- Limited error handling
- No rollback mechanisms

## 🎯 Identified Gaps & Improvements

### 1. **SSH Automation & Security**

**Current Gaps:**
- Manual SSH key management
- No SSH connection validation
- Limited error handling
- No connection retry logic

**Improvements:**
- ✅ Automated SSH key validation
- ✅ Connection testing and retry mechanisms
- ✅ Secure SSH configuration
- ✅ Multi-key support for team access

### 2. **Domain Management**

**Current Gaps:**
- Manual DNS configuration
- No domain validation
- Limited multi-domain support
- No subdomain automation

**Improvements:**
- ✅ Automated DNS validation
- ✅ Multi-domain SSL certificates
- ✅ Subdomain routing configuration
- ✅ Domain health checks

### 3. **Security Hardening**

**Current Gaps:**
- Basic firewall rules
- Limited intrusion detection
- No security headers
- Manual security updates

**Improvements:**
- ✅ Advanced firewall configuration
- ✅ Fail2Ban integration
- ✅ Security headers implementation
- ✅ Automated security updates
- ✅ Rate limiting
- ✅ DDoS protection

### 4. **Monitoring & Alerting**

**Current Gaps:**
- Basic health checks
- No performance monitoring
- Limited alerting channels
- No predictive monitoring

**Improvements:**
- ✅ Comprehensive system monitoring
- ✅ Performance metrics collection
- ✅ Multi-channel alerting (Slack, email, SMS)
- ✅ Predictive threshold monitoring
- ✅ Custom dashboard creation

### 5. **Deployment Automation**

**Current Gaps:**
- Manual deployment steps
- No rollback mechanisms
- Limited environment support
- No deployment validation

**Improvements:**
- ✅ Fully automated deployment pipeline
- ✅ Blue-green deployment support
- ✅ Automatic rollback on failure
- ✅ Multi-environment configuration
- ✅ Deployment validation and testing

## 🏗️ Enhanced Deployment Architecture

```mermaid
graph TB
    A[Agency Developer] --> B[SSH Deploy Script]
    B --> C[VPS Validation]
    C --> D[System Preparation]
    D --> E[Security Hardening]
    E --> F[Application Deployment]
    F --> G[SSL Configuration]
    G --> H[Monitoring Setup]
    H --> I[Backup Configuration]
    I --> J[Health Validation]
    J --> K[Client Domain Live]
    
    subgraph "Security Layer"
        E1[Firewall Rules]
        E2[Fail2Ban]
        E3[Security Headers]
        E4[Rate Limiting]
    end
    
    subgraph "Monitoring Layer"
        H1[System Metrics]
        H2[Application Health]
        H3[Performance Monitoring]
        H4[Alert Management]
    end
    
    subgraph "Backup Layer"
        I1[Application Backup]
        I2[Database Backup]
        I3[Configuration Backup]
        I4[Offsite Storage]
    end
```

## 📋 Prerequisites

### Local Environment

```bash
# Required tools
sudo apt-get install -y openssh-client curl jq git

# Optional tools for enhanced functionality
sudo apt-get install -y awscli ansible terraform
```

### VPS Requirements

- **Minimum Specifications:**
  - 2 GB RAM
  - 1 CPU core
  - 20 GB SSD storage
  - Ubuntu 20.04+ or Debian 11+

- **Recommended Specifications:**
  - 4 GB RAM
  - 2 CPU cores
  - 50 GB SSD storage
  - Ubuntu 22.04 LTS

### Access Requirements

- SSH access to VPS (root or sudo user)
- Domain DNS management access
- SSL certificate email access

## 🚀 Quick Start Guide

### 1. **Setup Configuration**

```bash
# Clone the repository
git clone https://github.com/estratix/luxcrafts.git
cd luxcrafts/scripts

# Copy and customize configuration
cp ssh-deploy-config.env luxcrafts-production.env

# Edit configuration
nano luxcrafts-production.env
```

### 2. **Configure Environment Variables**

```bash
# Essential configuration
export VPS_HOST="your-vps-ip-or-hostname"
export CLIENT_DOMAIN="www.luxcrafts.co"
export CLIENT_EMAIL="<EMAIL>"
export SSH_KEY_PATH="~/.ssh/id_rsa"
```

### 3. **Deploy Application**

```bash
# Load configuration
source luxcrafts-production.env

# Validate configuration
./deploy-agency-vps.sh --help

# Deploy to production
./deploy-agency-vps.sh deploy
```

## 📖 Detailed Deployment Process

### Phase 1: Pre-Deployment Validation

```bash
# 1. Configuration validation
./deploy-agency-vps.sh --config luxcrafts-production.env status

# 2. SSH connectivity test
ssh -i ~/.ssh/id_rsa root@your-vps-ip "echo 'Connection successful'"

# 3. DNS validation
dig +short www.luxcrafts.co
```

### Phase 2: System Preparation

```bash
# Automated system preparation includes:
# - System package updates
# - Docker installation
# - Node.js installation
# - Nginx installation
# - Security tools installation
```

### Phase 3: Security Hardening

```bash
# Automated security configuration:
# - UFW firewall setup
# - Fail2Ban configuration
# - SSH hardening
# - Security headers
# - Rate limiting
```

### Phase 4: Application Deployment

```bash
# Application deployment process:
# - Repository cloning
# - Dependency installation
# - Application building
# - Nginx configuration
# - Service startup
```

### Phase 5: SSL & Domain Configuration

```bash
# SSL certificate setup:
# - Let's Encrypt certificate
# - Automatic renewal
# - HSTS configuration
# - Security headers
```

### Phase 6: Monitoring & Backup Setup

```bash
# Monitoring configuration:
# - Health check scripts
# - Performance monitoring
# - Alert configuration
# - Backup automation
```

## 🔒 Security Hardening

### Firewall Configuration

```bash
# UFW rules automatically configured:
ufw default deny incoming
ufw default allow outgoing
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw enable
```

### Fail2Ban Protection

```bash
# Fail2Ban jails configured:
# - SSH brute force protection
# - Nginx HTTP auth protection
# - Rate limiting protection
```

### Security Headers

```nginx
# Nginx security headers:
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload";
add_header Referrer-Policy strict-origin-when-cross-origin;
```

## 📊 Monitoring & Alerting

### System Monitoring

```bash
# Automated monitoring includes:
# - CPU usage monitoring
# - Memory usage monitoring
# - Disk space monitoring
# - Network connectivity
# - Application health checks
```

### Alert Configuration

```bash
# Alert channels supported:
# - Slack webhooks
# - Discord webhooks
# - Email notifications
# - SMS alerts (via Twilio)
# - Custom webhook endpoints
```

### Performance Metrics

```bash
# Performance monitoring:
# - Response time tracking
# - Throughput measurement
# - Error rate monitoring
# - Resource utilization
```

## 💾 Backup & Recovery

### Automated Backup System

```bash
# Daily backup includes:
# - Application files
# - Nginx configuration
# - SSL certificates
# - Database (if applicable)
# - System configuration
```

### Offsite Backup Storage

```bash
# Supported storage providers:
# - AWS S3
# - DigitalOcean Spaces
# - Google Cloud Storage
# - Custom FTP/SFTP
```

### Recovery Procedures

```bash
# Recovery options:
./deploy-agency-vps.sh backup          # Manual backup
./deploy-agency-vps.sh --rollback      # Rollback deployment
./deploy-agency-vps.sh --restore DATE  # Restore from backup
```

## 🔧 Troubleshooting

### Common Issues

#### SSH Connection Issues

```bash
# Debug SSH connection
ssh -vvv -i ~/.ssh/id_rsa root@your-vps-ip

# Check SSH key permissions
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
```

#### DNS Configuration Issues

```bash
# Verify DNS propagation
dig +trace www.luxcrafts.co
nslookup www.luxcrafts.co

# Check DNS from different locations
curl "https://dns.google/resolve?name=www.luxcrafts.co&type=A"
```

#### SSL Certificate Issues

```bash
# Check certificate status
openssl s509 -in /etc/letsencrypt/live/www.luxcrafts.co/fullchain.pem -text -noout

# Renew certificate manually
certbot renew --dry-run
```

#### Application Issues

```bash
# Check application logs
tail -f /var/log/luxcrafts/application.log

# Check Nginx logs
tail -f /var/log/nginx/error.log

# Check system resources
htop
df -h
free -m
```

### Debug Mode

```bash
# Enable verbose logging
./deploy-agency-vps.sh --verbose deploy

# Check deployment logs
tail -f deployment-$(date +%Y%m%d).log
```

## 📚 Best Practices

### 1. **Environment Management**

```bash
# Use separate configurations for each environment
luxcrafts-production.env
luxcrafts-staging.env
luxcrafts-development.env
```

### 2. **Security Best Practices**

- Use dedicated SSH keys for each client
- Implement key rotation policies
- Enable two-factor authentication
- Regular security audits
- Automated security updates

### 3. **Monitoring Best Practices**

- Set up proactive monitoring
- Configure meaningful alerts
- Regular performance reviews
- Capacity planning
- Incident response procedures

### 4. **Backup Best Practices**

- Test backup restoration regularly
- Implement 3-2-1 backup strategy
- Encrypt sensitive backups
- Document recovery procedures
- Regular backup verification

### 5. **Deployment Best Practices**

- Use blue-green deployments for zero downtime
- Implement automated testing
- Maintain deployment documentation
- Version control all configurations
- Regular deployment rehearsals

## ❓ Questions & Considerations

### Technical Questions

1. **VPS Provider Preferences:**
   - Do you have a preferred VPS provider (DigitalOcean, Linode, AWS, etc.)?
   - Any specific regional requirements for server location?

2. **Domain Management:**
   - Who manages the client's DNS records?
   - Are there any existing DNS configurations to preserve?
   - Do you need wildcard SSL certificates?

3. **Security Requirements:**
   - Are there specific compliance requirements (GDPR, HIPAA, etc.)?
   - Do you need additional security measures (VPN, IP whitelisting)?
   - What's the preferred method for security incident notifications?

4. **Monitoring & Alerting:**
   - What monitoring tools do you currently use?
   - Preferred alerting channels (Slack, email, SMS)?
   - Required uptime SLA for clients?

5. **Backup & Recovery:**
   - Preferred backup storage location?
   - Required backup retention period?
   - Recovery time objectives (RTO) and recovery point objectives (RPO)?

### Operational Questions

1. **Team Access:**
   - How many team members need VPS access?
   - Do you need role-based access control?
   - Preferred method for SSH key management?

2. **Client Communication:**
   - How do you handle client notifications during deployments?
   - Do clients need access to monitoring dashboards?
   - What level of technical detail do clients expect?

3. **Maintenance Windows:**
   - Preferred maintenance windows for updates?
   - How do you handle emergency deployments?
   - Client approval process for major updates?

### Business Questions

1. **Service Level Agreements:**
   - What uptime guarantees do you provide?
   - Response time commitments for issues?
   - Escalation procedures for critical issues?

2. **Scaling Requirements:**
   - Expected traffic growth for clients?
   - Auto-scaling requirements?
   - Load balancing needs?

3. **Cost Management:**
   - Budget considerations for VPS resources?
   - Cost allocation between clients?
   - Monitoring and alerting for resource usage?

## 🎯 Next Steps

### Immediate Actions

1. **Review and customize the deployment configuration**
2. **Test the deployment script in a staging environment**
3. **Set up monitoring and alerting channels**
4. **Configure backup storage**
5. **Document client-specific requirements**

### Medium-term Improvements

1. **Implement CI/CD integration**
2. **Add automated testing pipelines**
3. **Enhance monitoring dashboards**
4. **Implement infrastructure as code**
5. **Add performance optimization tools**

### Long-term Enhancements

1. **Multi-region deployment support**
2. **Advanced security scanning**
3. **Predictive scaling**
4. **Cost optimization automation**
5. **Client self-service portals**

## 📞 Support & Contact

For questions, issues, or enhancements related to this deployment system:

- **Technical Support:** <EMAIL>
- **Emergency Contact:** +1-XXX-XXX-XXXX
- **Documentation:** https://docs.estratix.agency
- **Issue Tracking:** https://github.com/estratix/luxcrafts/issues

---

**Last Updated:** January 2025  
**Version:** 2.0  
**Maintained by:** ESTRATIX Infrastructure Team