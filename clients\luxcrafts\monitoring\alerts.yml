# Luxcrafts Monitoring and Alerting Configuration
# This file defines monitoring rules and alert conditions for the Luxcrafts platform

apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: luxcrafts-alerts
  namespace: luxcrafts-production
  labels:
    app: luxcrafts
    environment: production
    team: devops

spec:
  groups:
    # Application Health Alerts
    - name: luxcrafts.application
      interval: 30s
      rules:
        - alert: ApplicationDown
          expr: up{job="luxcrafts-frontend"} == 0
          for: 1m
          labels:
            severity: critical
            service: frontend
            team: devops
          annotations:
            summary: "Luxcrafts application is down"
            description: "The Luxcrafts frontend application has been down for more than 1 minute."
            runbook_url: "https://docs.luxcrafts.co/runbooks/application-down"
            dashboard_url: "https://grafana.luxcrafts.co/d/luxcrafts-overview"

        - alert: HealthCheckFailing
          expr: probe_success{instance="https://luxcrafts.co/health"} == 0
          for: 2m
          labels:
            severity: critical
            service: frontend
            team: devops
          annotations:
            summary: "Health check endpoint is failing"
            description: "The health check endpoint has been failing for more than 2 minutes."
            runbook_url: "https://docs.luxcrafts.co/runbooks/health-check-failing"

        - alert: HighErrorRate
          expr: |
            (
              rate(http_requests_total{job="luxcrafts-frontend",code=~"5.."}[5m]) /
              rate(http_requests_total{job="luxcrafts-frontend"}[5m])
            ) * 100 > 5
          for: 5m
          labels:
            severity: warning
            service: frontend
            team: devops
          annotations:
            summary: "High error rate detected"
            description: "Error rate is {{ $value }}% for the last 5 minutes."
            runbook_url: "https://docs.luxcrafts.co/runbooks/high-error-rate"

        - alert: SlowResponseTime
          expr: |
            histogram_quantile(0.95,
              rate(http_request_duration_seconds_bucket{job="luxcrafts-frontend"}[5m])
            ) > 2
          for: 10m
          labels:
            severity: warning
            service: frontend
            team: devops
          annotations:
            summary: "Slow response times detected"
            description: "95th percentile response time is {{ $value }}s for the last 10 minutes."
            runbook_url: "https://docs.luxcrafts.co/runbooks/slow-response-time"

    # Infrastructure Alerts
    - name: luxcrafts.infrastructure
      interval: 30s
      rules:
        - alert: HighCPUUsage
          expr: |
            (
              100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)
            ) > 80
          for: 10m
          labels:
            severity: warning
            service: infrastructure
            team: devops
          annotations:
            summary: "High CPU usage detected"
            description: "CPU usage is {{ $value }}% on instance {{ $labels.instance }}."
            runbook_url: "https://docs.luxcrafts.co/runbooks/high-cpu-usage"

        - alert: HighMemoryUsage
          expr: |
            (
              (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) /
              node_memory_MemTotal_bytes
            ) * 100 > 85
          for: 10m
          labels:
            severity: warning
            service: infrastructure
            team: devops
          annotations:
            summary: "High memory usage detected"
            description: "Memory usage is {{ $value }}% on instance {{ $labels.instance }}."
            runbook_url: "https://docs.luxcrafts.co/runbooks/high-memory-usage"

        - alert: DiskSpaceLow
          expr: |
            (
              (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) /
              node_filesystem_size_bytes{fstype!="tmpfs"}
            ) * 100 > 85
          for: 5m
          labels:
            severity: warning
            service: infrastructure
            team: devops
          annotations:
            summary: "Disk space is running low"
            description: "Disk usage is {{ $value }}% on {{ $labels.device }} ({{ $labels.instance }})."
            runbook_url: "https://docs.luxcrafts.co/runbooks/disk-space-low"

        - alert: DiskSpaceCritical
          expr: |
            (
              (node_filesystem_size_bytes{fstype!="tmpfs"} - node_filesystem_free_bytes{fstype!="tmpfs"}) /
              node_filesystem_size_bytes{fstype!="tmpfs"}
            ) * 100 > 95
          for: 1m
          labels:
            severity: critical
            service: infrastructure
            team: devops
          annotations:
            summary: "Disk space is critically low"
            description: "Disk usage is {{ $value }}% on {{ $labels.device }} ({{ $labels.instance }})."
            runbook_url: "https://docs.luxcrafts.co/runbooks/disk-space-critical"

    # SSL Certificate Alerts
    - name: luxcrafts.ssl
      interval: 1h
      rules:
        - alert: SSLCertificateExpiringSoon
          expr: probe_ssl_earliest_cert_expiry - time() < 86400 * 30
          for: 1h
          labels:
            severity: warning
            service: ssl
            team: devops
          annotations:
            summary: "SSL certificate expiring soon"
            description: "SSL certificate for {{ $labels.instance }} expires in {{ $value | humanizeDuration }}."
            runbook_url: "https://docs.luxcrafts.co/runbooks/ssl-certificate-expiring"

        - alert: SSLCertificateExpired
          expr: probe_ssl_earliest_cert_expiry - time() <= 0
          for: 1m
          labels:
            severity: critical
            service: ssl
            team: devops
          annotations:
            summary: "SSL certificate has expired"
            description: "SSL certificate for {{ $labels.instance }} has expired."
            runbook_url: "https://docs.luxcrafts.co/runbooks/ssl-certificate-expired"

    # Database Alerts
    - name: luxcrafts.database
      interval: 30s
      rules:
        - alert: DatabaseDown
          expr: up{job="luxcrafts-database"} == 0
          for: 1m
          labels:
            severity: critical
            service: database
            team: devops
          annotations:
            summary: "Database is down"
            description: "The Luxcrafts database has been down for more than 1 minute."
            runbook_url: "https://docs.luxcrafts.co/runbooks/database-down"

        - alert: DatabaseConnectionsHigh
          expr: |
            (
              pg_stat_activity_count{datname="luxcrafts_production"} /
              pg_settings_max_connections
            ) * 100 > 80
          for: 5m
          labels:
            severity: warning
            service: database
            team: devops
          annotations:
            summary: "High number of database connections"
            description: "Database connection usage is {{ $value }}%."
            runbook_url: "https://docs.luxcrafts.co/runbooks/database-connections-high"

        - alert: DatabaseSlowQueries
          expr: |
            rate(pg_stat_activity_max_tx_duration{datname="luxcrafts_production"}[5m]) > 30
          for: 10m
          labels:
            severity: warning
            service: database
            team: devops
          annotations:
            summary: "Slow database queries detected"
            description: "Average query duration is {{ $value }}s."
            runbook_url: "https://docs.luxcrafts.co/runbooks/database-slow-queries"

    # Blockchain Alerts
    - name: luxcrafts.blockchain
      interval: 1m
      rules:
        - alert: BlockchainConnectionDown
          expr: ethereum_node_up == 0
          for: 2m
          labels:
            severity: critical
            service: blockchain
            team: devops
          annotations:
            summary: "Blockchain connection is down"
            description: "Connection to Ethereum node has been down for more than 2 minutes."
            runbook_url: "https://docs.luxcrafts.co/runbooks/blockchain-connection-down"

        - alert: BlockchainSyncLagging
          expr: |
            (
              ethereum_latest_block_number - ethereum_synced_block_number
            ) > 10
          for: 5m
          labels:
            severity: warning
            service: blockchain
            team: devops
          annotations:
            summary: "Blockchain sync is lagging"
            description: "Blockchain sync is {{ $value }} blocks behind."
            runbook_url: "https://docs.luxcrafts.co/runbooks/blockchain-sync-lagging"

    # Security Alerts
    - name: luxcrafts.security
      interval: 1m
      rules:
        - alert: UnusualTrafficPattern
          expr: |
            rate(http_requests_total{job="luxcrafts-frontend"}[5m]) >
            (avg_over_time(rate(http_requests_total{job="luxcrafts-frontend"}[5m])[1h:5m]) * 3)
          for: 5m
          labels:
            severity: warning
            service: security
            team: security
          annotations:
            summary: "Unusual traffic pattern detected"
            description: "Traffic rate is {{ $value }} requests/second, which is 3x higher than normal."
            runbook_url: "https://docs.luxcrafts.co/runbooks/unusual-traffic-pattern"

        - alert: HighFailedLoginAttempts
          expr: |
            rate(auth_failed_attempts_total[5m]) > 10
          for: 2m
          labels:
            severity: warning
            service: security
            team: security
          annotations:
            summary: "High number of failed login attempts"
            description: "Failed login rate is {{ $value }} attempts/second."
            runbook_url: "https://docs.luxcrafts.co/runbooks/high-failed-login-attempts"

    # Business Metrics Alerts
    - name: luxcrafts.business
      interval: 5m
      rules:
        - alert: LowTransactionVolume
          expr: |
            rate(nft_transactions_total[1h]) < 1
          for: 2h
          labels:
            severity: warning
            service: business
            team: product
          annotations:
            summary: "Low NFT transaction volume"
            description: "NFT transaction rate is {{ $value }} transactions/hour."
            runbook_url: "https://docs.luxcrafts.co/runbooks/low-transaction-volume"

        - alert: HighTransactionFailureRate
          expr: |
            (
              rate(nft_transactions_failed_total[5m]) /
              rate(nft_transactions_total[5m])
            ) * 100 > 10
          for: 10m
          labels:
            severity: warning
            service: business
            team: product
          annotations:
            summary: "High transaction failure rate"
            description: "Transaction failure rate is {{ $value }}%."
            runbook_url: "https://docs.luxcrafts.co/runbooks/high-transaction-failure-rate"

---
# Alertmanager Configuration
apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: luxcrafts-production
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.luxcrafts.co:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: '${SMTP_PASSWORD}'
      slack_api_url: '${SLACK_WEBHOOK_URL}'

    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
        - match:
            severity: critical
          receiver: 'critical-alerts'
          group_wait: 5s
          repeat_interval: 5m
        - match:
            team: security
          receiver: 'security-team'
        - match:
            team: devops
          receiver: 'devops-team'
        - match:
            team: product
          receiver: 'product-team'

    receivers:
      - name: 'default'
        slack_configs:
          - channel: '#alerts-luxcrafts'
            title: 'Luxcrafts Alert'
            text: |
              {{ range .Alerts }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Service:* {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
              {{ if .Annotations.dashboard_url }}*Dashboard:* {{ .Annotations.dashboard_url }}{{ end }}
              {{ end }}

      - name: 'critical-alerts'
        slack_configs:
          - channel: '#critical-alerts'
            title: '🚨 CRITICAL: Luxcrafts Alert'
            text: |
              {{ range .Alerts }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Service:* {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
              {{ if .Annotations.dashboard_url }}*Dashboard:* {{ .Annotations.dashboard_url }}{{ end }}
              {{ end }}
        email_configs:
          - to: '<EMAIL>'
            subject: '🚨 CRITICAL: {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Service: {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}Runbook: {{ .Annotations.runbook_url }}{{ end }}
              {{ if .Annotations.dashboard_url }}Dashboard: {{ .Annotations.dashboard_url }}{{ end }}
              {{ end }}
        pagerduty_configs:
          - routing_key: '${PAGERDUTY_INTEGRATION_KEY}'
            description: '{{ .GroupLabels.alertname }}'

      - name: 'security-team'
        slack_configs:
          - channel: '#security-alerts'
            title: '🔒 Security Alert: Luxcrafts'
            text: |
              {{ range .Alerts }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Service:* {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
              {{ end }}
        email_configs:
          - to: '<EMAIL>'
            subject: '🔒 Security Alert: {{ .GroupLabels.alertname }}'

      - name: 'devops-team'
        slack_configs:
          - channel: '#devops-alerts'
            title: '⚙️ DevOps Alert: Luxcrafts'
            text: |
              {{ range .Alerts }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Service:* {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
              {{ if .Annotations.dashboard_url }}*Dashboard:* {{ .Annotations.dashboard_url }}{{ end }}
              {{ end }}

      - name: 'product-team'
        slack_configs:
          - channel: '#product-alerts'
            title: '📊 Product Alert: Luxcrafts'
            text: |
              {{ range .Alerts }}
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Service:* {{ .Labels.service }}
              {{ if .Annotations.runbook_url }}*Runbook:* {{ .Annotations.runbook_url }}{{ end }}
              {{ end }}

    inhibit_rules:
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'cluster', 'service']