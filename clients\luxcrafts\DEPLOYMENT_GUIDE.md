# Luxcrafts CI/CD Deployment Guide

## Overview

This guide covers the complete CI/CD pipeline for Luxcrafts, including staging deployment to Vercel and production deployment to luxcrafts.co using Dokploy.

## Architecture

```
Developer Push â†’ GitHub Actions â†’ Build & Test â†’ Deploy
     â†“                â†“              â†“           â†“
Feature Branch â†’ Quality Checks â†’ Artifacts â†’ Staging (Vercel)
     â†“                â†“              â†“           â†“
Develop Branch â†’ Security Scan â†’ Container â†’ Preview URL
     â†“                â†“              â†“           â†“
 Main Branch   â†’ Performance â†’ Registry â†’ Production (luxcrafts.co)
```

## Deployment Environments

### Staging (Vercel)
- **Trigger**: Push to `develop` or `feature/*` branches
- **URL**: Auto-generated Vercel preview URL
- **Purpose**: Integration testing and preview
- **Environment**: Goerli testnet

### Production (Dokploy)
- **Trigger**: Push to `main` branch
- **URL**: https://luxcrafts.co
- **Purpose**: Live production environment
- **Environment**: Ethereum mainnet

## Required GitHub Secrets

### Vercel Secrets
- `VERCEL_TOKEN`: Vercel deployment token
- `VERCEL_ORG_ID`: Vercel organization ID
- `VERCEL_PROJECT_ID`: Vercel project ID

### Production Secrets
- `PRODUCTION_HOST`: VPS server IP address
- `PRODUCTION_USERNAME`: SSH username
- `PRODUCTION_SSH_KEY`: SSH private key
- `PRODUCTION_PORT`: SSH port (default: 22)

### Environment Variables
- `VITE_WALLETCONNECT_PROJECT_ID`: WalletConnect project ID
- `VITE_ALCHEMY_API_KEY_STAGING`: Alchemy API key for staging
- `VITE_ALCHEMY_API_KEY_PRODUCTION`: Alchemy API key for production
- `VITE_INFURA_API_KEY_STAGING`: Infura API key for staging
- `VITE_INFURA_API_KEY_PRODUCTION`: Infura API key for production
- `VITE_CONTRACT_ADDRESS_STAGING`: Smart contract address for staging
- `VITE_CONTRACT_ADDRESS_PRODUCTION`: Smart contract address for production
- `VITE_LUX_TOKEN_ADDRESS_STAGING`: LUX token address for staging
- `VITE_LUX_TOKEN_ADDRESS_PRODUCTION`: LUX token address for production

## Deployment Process

### Staging Deployment
1. Push code to `develop` or feature branch
2. GitHub Actions triggers staging workflow
3. Quality checks (TypeScript, ESLint, tests)
4. Build application with staging environment
5. Deploy to Vercel
6. Run health checks
7. Comment PR with preview URL

### Production Deployment
1. Merge to `main` branch
2. GitHub Actions triggers production workflow
3. Comprehensive pre-deployment checks
4. Build application with production environment
5. Create deployment package
6. Deploy to VPS using Dokploy
7. Health checks and SSL verification
8. Cleanup old releases

## Manual Deployment

### Staging
```bash
# Run staging deployment script
powershell -ExecutionPolicy Bypass -File scripts/deploy-staging.ps1
```

### Production
```bash
# Trigger production deployment
gh workflow run production-deployment.yml
```

## Health Checks

### Staging
- URL: `{preview-url}/health.json`
- Checks: Frontend status, build verification

### Production
- URL: `https://luxcrafts.co/health.json`
- Checks: Frontend, SSL, domain configuration

## Rollback Procedures

### Staging
- Automatic rollback on health check failure
- Manual rollback via Vercel dashboard

### Production
- Automatic rollback on deployment failure
- Manual rollback via SSH to VPS
- Previous releases kept for 5 deployments

## Monitoring

### Deployment Monitoring
- GitHub Actions workflow status
- Vercel deployment logs
- VPS server logs

### Application Monitoring
- Health check endpoints
- SSL certificate monitoring
- Performance metrics

## Troubleshooting

### Common Issues
1. **Build failures**: Check Node.js version and dependencies
2. **Deployment timeouts**: Increase timeout values
3. **Health check failures**: Verify application startup
4. **SSL issues**: Check certificate renewal

### Support
- GitHub Issues for bug reports
- Team Slack for urgent issues
- Documentation updates via PR

## Security

### Best Practices
- All secrets stored in GitHub Secrets
- SSH key authentication for VPS
- SSL/TLS encryption for all traffic
- Security headers configured
- Regular dependency updates

### Compliance
- Audit logs for all deployments
- Access control via GitHub teams
- Backup and recovery procedures
- Incident response plan
