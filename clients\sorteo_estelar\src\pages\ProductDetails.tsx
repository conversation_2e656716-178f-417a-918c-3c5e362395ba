import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { 
  ShoppingCart, Heart, Share2, Clock, Users, Star, Zap, 
  CreditCard, Wallet, QrCode, Upload, Check, X, 
  ArrowLeft, TrendingUp, Award, Shield, Info
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';
import { useCartStore, useUserStore, useDeFiStore } from '../store';

interface Product {
  id: string;
  name: string;
  description: string;
  longDescription: string;
  images: string[];
  price: number;
  originalPrice?: number;
  tickets: number;
  maxTickets: number;
  timeLeft: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  tags: string[];
  featured: boolean;
  aiScore: number;
  specifications: { [key: string]: string };
  numberType: 'lottery-linked' | 'random';
  numberLength: number;
  drawDate: string;
  topBuyers: Array<{
    address: string;
    tickets: number;
    percentage: number;
  }>;
}

interface PaymentMethod {
  id: string;
  name: string;
  type: 'web2' | 'web3';
  icon: string;
  description: string;
  fees: number;
  processingTime: string;
  supported: boolean;
}

const ProductDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { addItem } = useCartStore();
  const { user } = useUserStore();
  const { walletConnected } = useDeFiStore();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [selectedNumbers, setSelectedNumbers] = useState<string[]>([]);
  const [showPayment, setShowPayment] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<string | null>(null);
  const [paymentStep, setPaymentStep] = useState<'method' | 'details' | 'confirmation'>('method');
  const [uploadedReceipt, setUploadedReceipt] = useState<File | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  // Mock product data
  const mockProduct: Product = {
    id: '1',
    name: 'iPhone 15 Pro Max',
    description: 'El smartphone más avanzado con tecnología A17 Pro y cámaras profesionales.',
    longDescription: 'El iPhone 15 Pro Max representa la cúspide de la innovación móvil. Con su revolucionario chip A17 Pro, sistema de cámaras profesionales y diseño en titanio, este dispositivo redefine lo que es posible en un smartphone. Perfecto para profesionales creativos, gamers y entusiastas de la tecnología.',
    images: [
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20titanium%20professional%20product%20photography%20studio%20lighting&image_size=landscape_16_9',
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20camera%20system%20close%20up%20professional%20photography&image_size=landscape_16_9',
      'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20display%20screen%20vibrant%20colors%20technology&image_size=landscape_16_9'
    ],
    price: 50000,
    originalPrice: 55000,
    tickets: 1250,
    maxTickets: 2000,
    timeLeft: '2d 14h 30m',
    rarity: 'legendary',
    category: 'tecnologia',
    tags: ['smartphone', 'apple', 'premium', 'profesional'],
    featured: true,
    aiScore: 9.8,
    specifications: {
      'Pantalla': '6.7" Super Retina XDR',
      'Chip': 'A17 Pro',
      'Cámara': 'Sistema Pro de 48MP',
      'Almacenamiento': '256GB',
      'Batería': 'Hasta 29 horas de video',
      'Material': 'Titanio grado aeroespacial'
    },
    numberType: 'lottery-linked',
    numberLength: 4,
    drawDate: '2024-01-20',
    topBuyers: [
      { address: '0x1234...5678', tickets: 45, percentage: 2.25 },
      { address: '0x8765...4321', tickets: 38, percentage: 1.9 },
      { address: '0x9876...1234', tickets: 32, percentage: 1.6 }
    ]
  };

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'nequi',
      name: 'Nequi',
      type: 'web2',
      icon: '💳',
      description: 'Pago instantáneo con Nequi',
      fees: 0,
      processingTime: 'Inmediato',
      supported: user?.role === 'colombian'
    },
    {
      id: 'daviplata',
      name: 'Daviplata',
      type: 'web2',
      icon: '🏦',
      description: 'Transferencia con Daviplata',
      fees: 0,
      processingTime: 'Inmediato',
      supported: user?.role === 'colombian'
    },
    {
      id: 'wise',
      name: 'Wise',
      type: 'web2',
      icon: '🌍',
      description: 'Transferencia internacional',
      fees: 2.5,
      processingTime: '1-3 días',
      supported: true
    },
    {
      id: 'zelle',
      name: 'Zelle',
      type: 'web2',
      icon: '💰',
      description: 'Transferencia rápida en USD',
      fees: 0,
      processingTime: 'Inmediato',
      supported: user?.role === 'international'
    },
    {
      id: 'metamask',
      name: 'MetaMask',
      type: 'web3',
      icon: '🦊',
      description: 'Pago con criptomonedas',
      fees: 1,
      processingTime: '1-5 minutos',
      supported: walletConnected
    },
    {
      id: 'coinbase',
      name: 'Coinbase Wallet',
      type: 'web3',
      icon: '🔷',
      description: 'Pago seguro con Coinbase',
      fees: 1.5,
      processingTime: '1-5 minutos',
      supported: true
    }
  ];

  useEffect(() => {
    // Simulate API call
    setProduct(mockProduct);
  }, [id]);

  const generateRandomNumbers = () => {
    if (!product) return;
    
    const numbers: string[] = [];
    for (let i = 0; i < quantity; i++) {
      let number = '';
      for (let j = 0; j < product.numberLength; j++) {
        number += Math.floor(Math.random() * 10).toString();
      }
      numbers.push(number);
    }
    setSelectedNumbers(numbers);
  };

  const handlePurchase = () => {
    if (!product) return;
    
    if (selectedNumbers.length === 0) {
      generateRandomNumbers();
    }
    
    setShowPayment(true);
  };

  const handlePaymentConfirm = () => {
    if (!product) return;
    
    addItem({
      productId: product.id,
      productName: product.name,
      price: product.price,
      quantity,
      numbers: selectedNumbers,
      image: product.images[0]
    });
    
    setShowPayment(false);
    navigate('/my-numbers');
  };

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'common': return 'from-gray-400 to-gray-600';
      case 'rare': return 'from-blue-400 to-blue-600';
      case 'epic': return 'from-purple-400 to-purple-600';
      case 'legendary': return 'from-amber-400 to-amber-600';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  const getProgressPercentage = () => {
    if (!product) return 0;
    return (product.tickets / product.maxTickets) * 100;
  };

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-white text-xl">Cargando producto...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="flex items-center space-x-2 text-white/70 hover:text-white mb-8 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Volver al catálogo</span>
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-video rounded-2xl overflow-hidden">
              <img 
                src={product.images[selectedImage]} 
                alt={product.name}
                className="w-full h-full object-cover"
              />
              <div className="absolute top-4 right-4 flex space-x-2">
                <button
                  onClick={() => setIsLiked(!isLiked)}
                  className={`p-3 rounded-full backdrop-blur-md transition-all ${
                    isLiked ? 'bg-red-500/20 text-red-400' : 'bg-white/10 text-white/70 hover:text-white'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${isLiked ? 'fill-current' : ''}`} />
                </button>
                <button className="p-3 rounded-full bg-white/10 backdrop-blur-md text-white/70 hover:text-white transition-colors">
                  <Share2 className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${
                    selectedImage === index ? 'border-purple-500' : 'border-white/20 hover:border-white/40'
                  }`}
                >
                  <img src={image} alt={`${product.name} ${index + 1}`} className="w-full h-full object-cover" />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            {/* Header */}
            <div>
              <div className="flex items-center space-x-3 mb-2">
                <span className={`px-3 py-1 rounded-full text-sm font-medium bg-gradient-to-r ${getRarityColor(product.rarity)} text-white`}>
                  {product.rarity.charAt(0).toUpperCase() + product.rarity.slice(1)}
                </span>
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-amber-400 fill-current" />
                  <span className="text-white font-semibold">{product.aiScore}</span>
                  <span className="text-white/60 text-sm">AI Score</span>
                </div>
              </div>
              
              <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">{product.name}</h1>
              <p className="text-white/80 text-lg leading-relaxed">{product.description}</p>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              <div className="text-3xl font-bold text-white">${product.price.toLocaleString()}</div>
              {product.originalPrice && (
                <div className="text-xl text-white/60 line-through">${product.originalPrice.toLocaleString()}</div>
              )}
              {product.originalPrice && (
                <div className="px-2 py-1 bg-green-500/20 text-green-400 rounded-lg text-sm font-medium">
                  -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                </div>
              )}
            </div>

            {/* Progress */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-white/80">Progreso del sorteo</span>
                <span className="text-white font-semibold">{product.tickets}/{product.maxTickets} tickets</span>
              </div>
              <div className="w-full bg-white/10 rounded-full h-3">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-amber-500 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${getProgressPercentage()}%` }}
                ></div>
              </div>
              <div className="flex items-center justify-between mt-2 text-sm text-white/70">
                <span>{getProgressPercentage().toFixed(1)}% completado</span>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{product.timeLeft} restante</span>
                </div>
              </div>
            </div>

            {/* Number Selection */}
            <Card variant="glass">
              <h3 className="text-white font-semibold mb-4">Selección de Números</h3>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-white/80">Cantidad de tickets:</span>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="w-8 h-8 rounded-lg bg-white/10 text-white hover:bg-white/20 transition-colors"
                    >
                      -
                    </button>
                    <span className="text-white font-semibold w-8 text-center">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="w-8 h-8 rounded-lg bg-white/10 text-white hover:bg-white/20 transition-colors"
                    >
                      +
                    </button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-white/80">Tipo de números:</span>
                  <span className="text-white capitalize">{product.numberType.replace('-', ' ')}</span>
                </div>
                
                {selectedNumbers.length > 0 && (
                  <div>
                    <span className="text-white/80 block mb-2">Tus números:</span>
                    <div className="flex flex-wrap gap-2">
                      {selectedNumbers.map((number, index) => (
                        <span 
                          key={index}
                          className="px-3 py-1 bg-gradient-to-r from-purple-500 to-amber-500 text-white rounded-lg font-mono font-bold"
                        >
                          {number}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={generateRandomNumbers}
                  className="w-full"
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Generar números aleatorios
                </Button>
              </div>
            </Card>

            {/* Purchase Button */}
            <div className="space-y-4">
              <Button 
                size="xl" 
                onClick={handlePurchase}
                className="w-full"
              >
                <ShoppingCart className="w-5 h-5 mr-2" />
                Comprar por ${(product.price * quantity).toLocaleString()}
              </Button>
              
              <div className="text-center text-white/60 text-sm">
                Total: {quantity} ticket{quantity > 1 ? 's' : ''} × ${product.price.toLocaleString()} = ${(product.price * quantity).toLocaleString()}
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info Tabs */}
        <div className="mt-16">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Specifications */}
            <Card variant="glass">
              <h3 className="text-xl font-bold text-white mb-6">Especificaciones</h3>
              <div className="space-y-3">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-white/70">{key}:</span>
                    <span className="text-white font-medium">{value}</span>
                  </div>
                ))}
              </div>
            </Card>

            {/* Top Buyers */}
            <Card variant="glass">
              <h3 className="text-xl font-bold text-white mb-6">Top Compradores</h3>
              <div className="space-y-3">
                {product.topBuyers.map((buyer, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-amber-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                        {index + 1}
                      </div>
                      <span className="text-white/80 font-mono text-sm">{buyer.address}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-white font-semibold">{buyer.tickets}</div>
                      <div className="text-white/60 text-xs">{buyer.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>

            {/* Security Info */}
            <Card variant="glass">
              <h3 className="text-xl font-bold text-white mb-6">Seguridad</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Shield className="w-5 h-5 text-green-400" />
                  <span className="text-white/80">Sorteo verificado</span>
                </div>
                <div className="flex items-center space-x-3">
                  <Award className="w-5 h-5 text-amber-400" />
                  <span className="text-white/80">Producto auténtico</span>
                </div>
                <div className="flex items-center space-x-3">
                  <TrendingUp className="w-5 h-5 text-blue-400" />
                  <span className="text-white/80">Transparencia total</span>
                </div>
                <p className="text-white/60 text-sm mt-4">
                  Todos los sorteos son verificados por smart contracts y auditados por terceros.
                </p>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPayment && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <Card variant="glass" className="max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-white">Completar Compra</h2>
              <button
                onClick={() => setShowPayment(false)}
                className="text-white/70 hover:text-white p-2 rounded-lg hover:bg-white/10"
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {paymentStep === 'method' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-2">Selecciona método de pago</h3>
                  <p className="text-white/70">Elige tu método de pago preferido</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {paymentMethods.filter(method => method.supported).map(method => (
                    <button
                      key={method.id}
                      onClick={() => {
                        setSelectedPayment(method.id);
                        setPaymentStep('details');
                      }}
                      className={`p-4 rounded-xl border-2 transition-all text-left ${
                        method.type === 'web2' 
                          ? 'border-blue-500/30 bg-blue-500/10 hover:bg-blue-500/20'
                          : 'border-purple-500/30 bg-purple-500/10 hover:bg-purple-500/20'
                      }`}
                    >
                      <div className="flex items-center space-x-3 mb-2">
                        <span className="text-2xl">{method.icon}</span>
                        <div>
                          <div className="text-white font-semibold">{method.name}</div>
                          <div className="text-white/60 text-sm">{method.description}</div>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-white/70">Comisión: {method.fees}%</span>
                        <span className="text-white/70">{method.processingTime}</span>
                      </div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {paymentStep === 'details' && selectedPayment && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-2">Detalles del pago</h3>
                  <p className="text-white/70">Completa la información requerida</p>
                </div>

                {/* Payment Details Form */}
                <div className="space-y-4">
                  {selectedPayment === 'nequi' && (
                    <div>
                      <label className="block text-white/80 mb-2">Número de teléfono Nequi</label>
                      <input 
                        type="tel" 
                        placeholder="3001234567"
                        className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    </div>
                  )}
                  
                  {selectedPayment === 'wise' && (
                    <div className="space-y-4">
                      <div>
                        <label className="block text-white/80 mb-2">Email de Wise</label>
                        <input 
                          type="email" 
                          placeholder="<EMAIL>"
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      <div>
                        <label className="block text-white/80 mb-2">Subir comprobante</label>
                        <div className="border-2 border-dashed border-white/30 rounded-xl p-6 text-center">
                          <Upload className="w-8 h-8 text-white/50 mx-auto mb-2" />
                          <p className="text-white/70">Arrastra tu comprobante aquí o haz clic para seleccionar</p>
                          <input type="file" className="hidden" accept="image/*,.pdf" />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  {selectedPayment === 'metamask' && (
                    <div className="text-center py-8">
                      <Wallet className="w-16 h-16 text-purple-400 mx-auto mb-4" />
                      <p className="text-white/80 mb-4">Conecta tu wallet MetaMask para continuar</p>
                      <Button>Conectar MetaMask</Button>
                    </div>
                  )}
                </div>

                <div className="flex space-x-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setPaymentStep('method')}
                    className="flex-1"
                  >
                    Volver
                  </Button>
                  <Button 
                    onClick={() => setPaymentStep('confirmation')}
                    className="flex-1"
                  >
                    Continuar
                  </Button>
                </div>
              </div>
            )}

            {paymentStep === 'confirmation' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-xl font-semibold text-white mb-2">Confirmar compra</h3>
                  <p className="text-white/70">Revisa los detalles antes de confirmar</p>
                </div>

                <Card variant="bordered" className="bg-white/5">
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-white/70">Producto:</span>
                      <span className="text-white font-semibold">{product.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Cantidad:</span>
                      <span className="text-white font-semibold">{quantity} ticket{quantity > 1 ? 's' : ''}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-white/70">Números:</span>
                      <div className="flex space-x-1">
                        {selectedNumbers.map((number, index) => (
                          <span key={index} className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs font-mono">
                            {number}
                          </span>
                        ))}
                      </div>
                    </div>
                    <hr className="border-white/20" />
                    <div className="flex justify-between text-lg">
                      <span className="text-white font-semibold">Total:</span>
                      <span className="text-white font-bold">${(product.price * quantity).toLocaleString()}</span>
                    </div>
                  </div>
                </Card>

                <div className="flex space-x-4">
                  <Button 
                    variant="outline" 
                    onClick={() => setPaymentStep('details')}
                    className="flex-1"
                  >
                    Volver
                  </Button>
                  <Button 
                    onClick={handlePaymentConfirm}
                    className="flex-1"
                  >
                    <Check className="w-4 h-4 mr-2" />
                    Confirmar Compra
                  </Button>
                </div>
              </div>
            )}
          </Card>
        </div>
      )}
    </div>
  );
};

export default ProductDetails;