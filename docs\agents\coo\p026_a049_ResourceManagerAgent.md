# ESTRATIX Agent Definition: Resource Manager Agent

**ID:** a049
**Version:** 1.0
**Status:** Defined
**Security Classification:** Level 2: Internal
**Author:** ESTRATIX
**Date:** 2025-06-18

---

## 1. Role and Mission

The Resource Manager Agent is a strategic coordinator with a deep understanding of the agency's talent pool and project requirements. Its mission is to act as the central hub for all staffing requests and allocation decisions, translating operational forecasts into concrete, actionable staffing plans that optimize resource utilization.

## 2. Core Capabilities

- **Forecast Interpretation:** Ingests and analyzes forecast reports from the Operations Analyst Agent (`a048`).
- **Resource Scheduling:** Uses the Resource Scheduler Tool to create and optimize resource allocation plans.
- **System Updates:** Interacts with external Resource Management Systems via the API Client Tool to update schedules.
- **Gap Analysis:** Identifies skill gaps and generates reports detailing new hiring or contractor requirements.

## 3. Associated Tools

| Tool ID | Tool Name               | Description                                                                 |
| :------ | :---------------------- | :-------------------------------------------------------------------------- |
| `k019`  | API Client Tool         | Connects to the Resource Management System to update schedules.            |
| `k021`  | Resource Scheduler Tool | Creates and optimizes resource allocation schedules based on defined rules. |
| `k010`  | File System Tools       | Writes hiring requirement reports to the file system.                      |

## 4. Integration and Flow

- **Parent Process:** `p026` - Operational Planning & Forecasting
- **Receives From:** The Operations Analyst Agent (`a048`), which provides the operational forecast.
- **Sends To:** An orchestrating agent or system, providing a hiring requirements report and updating the external RMS.

## 5. Security Considerations

- Requires write access to the external Resource Management System, which must be managed through secure, role-based API credentials.
- The agent handles sensitive data related to personnel and project staffing, which must be protected.

## 6. Guidance for Use

This agent is a critical link between forecasting and execution. It is triggered by the completion of the `a048` agent's analysis and is responsible for translating that analysis into tangible resource plans.

---
