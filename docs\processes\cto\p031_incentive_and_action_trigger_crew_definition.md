# ESTRATIX Process Definition: p031 - Incentive & Action Trigger Crew

## 1. Process Overview

- **Process ID:** p031
- **Process Name:** Incentive & Action Trigger Crew
- **Responsible Command Office:** CTO
- **Version:** 1.0
- **Status:** Defined

## 2. Process Description

This is the final process in the performance evaluation flow. It acts on the results of the evaluation, triggering downstream workflows for rewards, promotions, or remedial actions. It closes the loop on the performance cycle by translating evaluation outcomes into tangible actions.

## 3. Crew Composition (Conceptual)

| Agent Role | Agent ID | Key Responsibilities | Tools |
|---|---|---|---|
| Action Orchestrator | CTO_AXXX | Determines the appropriate downstream action based on the performance outcome and triggers the relevant workflow. | `t_cto_p031_workflow_dispatcher` |

## 4. Process Workflow

1.  **Initiation:** Triggered by `f014` upon successful completion of `p030`.
2.  **Decision Making:** The orchestrator analyzes the `Calculated Performance Score Record` against predefined thresholds.
3.  **Workflow Dispatch:** Based on the analysis, the orchestrator triggers one of the following:
    - **High Performance:** Initiates a reward workflow (e.g., assignment to a high-priority Project Battalion, access to new tools).
    - **Standard Performance:** Logs the result for trend analysis.
    - **Low Performance:** Initiates a remedial training or review workflow.
4.  **Output:** The process outputs a confirmation of the action taken.

## 5. Inputs & Outputs

- **Primary Input:** `Calculated Performance Score Record`, `Agent ID`
- **Primary Output:** `Action Trigger Confirmation`

## 6. Dependencies

- **Flow:** `f014 - Agent Performance Evaluation Flow`
- **Process:** `p030 - Agent Rank & State Update Crew`
- **Workflows:** Reward, Remedial Training, and other downstream workflows.
- **Tools:** `t_cto_p031_workflow_dispatcher`.
