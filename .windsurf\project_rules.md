# ESTRATIX Agentic Engine: Workflows & Rules

This directory contains the core components of the ESTRATIX agentic engine, which automates and governs all development and operational processes. It is comprised of two key subdirectories: `workflows` and `rules`.

---

## 1. Overview

The ESTRATIX project leverages a powerful combination of **Workflows** and **Rules** to achieve a high degree of automation, consistency, and intelligent operation. 

- **Workflows (`/workflows`)**: Define the **"How"** — the step-by-step procedures for executing complex tasks, from bootstrapping a new client project to generating individual code components.
- **Rules (`/rules`)**: Define the **"What"** and **"Why"** — the guiding principles, constraints, and standards that all agents must adhere to during execution. 

Together, they form a robust system where workflows orchestrate actions, and rules ensure those actions are performed correctly and in alignment with the project's architectural and strategic goals.

---

## 2. Core Components

### 2.1. Workflows

Workflows are machine-readable markdown files that outline sequences of tasks for agents to follow. They are the primary mechanism for process automation in ESTRATIX.

- **Purpose**: To codify best practices and standard operating procedures into executable scripts for agents.
- **Structure**: Organized by function into `0_master_orchestration`, `1_component_lifecycle`, and `2_operational_tasks`.

For a complete index and detailed explanation of all available workflows, please see the [Workflows README](./workflows/README.md).

### 2.2. Rules

Rules are foundational prompts that provide context, constraints, and directives to the agents. They act as a dynamic 'constitution' for the agentic workforce.

- **Purpose**: To ensure quality, consistency, and adherence to architectural standards across all agentic activities.
- **Activation**: Rules can be activated globally (`always_on`), triggered by specific workflows, or loaded dynamically by agents for specialized tasks.

For a comprehensive overview of the global project rules and their management, please see the [Project Rules](./rules/project_rules.md).

---

## 3. The Digital Twin Symbiosis

The `.windsurf` directory is the engine that maintains the synchronization between the project's **Digital Twin** (`/docs`) and its **Physical Twin** (`/src`).

- **Workflows** actively build and modify both twins.
- **Rules** ensure that all modifications maintain the integrity and alignment of the entire system.

The master workflow `value_chain_observability_and_improvement.md` is specifically designed to orchestrate this symbiosis, using agents to continuously monitor and correct any drift between the documented architecture and the implemented code.
