---
trigger: always_on
---

# Enhanced Project Rules & Multi-Assistant Coordination Framework
## Git MCP Tool Integration for Agentic Development

## Overview

This document summarizes the comprehensive enhancements made to the ESTRATIX project rules to support high-velocity, multi-assistant development with systematic GitOps and DevOps workflows, specifically leveraging Git MCP tools for trunk-based development and parallel LLM coordination through git worktree management.

## Key Enhancements Implemented

### 1. Multi-Assistant Coordination Protocol

**New Rule Activation Mode:**
- Added "Multi-Assistant Coordination" mode for collaborative development scenarios
- Enables dynamic rule activation based on team composition and task complexity

**Coordination Principles:**
- **Shared Context**: All assistants maintain synchronized understanding of project state
- **Task Handoff**: Structured protocols for seamless task transitions
- **PDCA Integration**: Plan-Do-Check-Act cycles embedded in all workflows
- **Conflict Resolution**: Clear escalation paths and resolution mechanisms

### 2. Git MCP Tool Integration & Trunk-Based Development

**MCP Git Tool Leveraging:**
- `git_status`, `git_diff_unstaged`, `git_diff_staged` for real-time state awareness
- `git_add`, `git_commit` for atomic change management
- `git_create_branch`, `git_checkout` for parallel worktree orchestration
- `git_log`, `git_show` for historical context and change tracking

**Trunk-Based Development with Git Worktrees:**
- Main branch protection with MCP-assisted reviews
- Parallel git worktrees for simultaneous LLM assistant work
- Short-lived feature branches (max 3 days) with automated cleanup
- Structured naming: `estratix-[assistant-id]-[task-type]-[task-id]`
- MCP-driven branch lifecycle management

**Multi-Assistant Git Worktree Strategy:**
- Each coding assistant operates in isolated git worktrees
- Shared main worktree for integration and coordination
- MCP tools enable seamless branch switching and merging
- Automated conflict detection and resolution workflows

**Agile Testing & Validation:**
- Multi-tier testing pipeline (Unit → Integration → Agent → E2E)
- Automated validation gates for quality, security, and performance
- Documentation completeness verification

**CI/CD Pipeline:**
- Pre-commit → Build → Test → Security → Deploy
- Blue-green deployment strategy with health checks
- Automated staging deployments for feature branches

### 3. Workflow Orchestration Patterns

**Iterative Cycle Management:**
- 2-week sprints with clear objectives and deliverables
- Async daily standups via coordination worksheet
- Sprint reviews with feature demonstrations
- Retrospectives for continuous improvement

**Parallel LLM Call Coordination via Git MCP:**
- Task partitioning mapped to git worktree boundaries
- MCP-assisted resource allocation and domain ownership tracking
- Real-time dependency management through `git_status` monitoring
- Proactive merge conflict prevention via MCP diff analysis
- Automated ownership boundary enforcement through git hooks
- Cross-assistant synchronization via MCP git operations

**Command Office Patterns:**
- Single Command Office for critical path decisions
- Multiple Command Offices with distributed ownership
- Cross-office coordination protocols
- Clear escalation paths for conflicts

### 4. Recursive MCP Tool Leveraging & High-Momentum Execution

**Recursive Simplicity Patterns:**
- MCP tools as first-class citizens in all development workflows
- Recursive application: MCP tools managing MCP tool configurations
- Self-bootstrapping project initialization via MCP automation
- Compound tool effects: Git MCP + Context7 MCP + Stripe MCP integration

**High-Momentum Execution Strategies:**
- Zero-friction task initiation through MCP tool chains
- Automated project scaffolding via recursive MCP calls
- Continuous momentum maintenance through MCP-driven workflows
- Instant context switching via Git MCP worktree management

**Simplicity-First MCP Integration:**
- Single MCP call chains for complex multi-step operations
- Minimal cognitive overhead through standardized MCP patterns
- Self-documenting workflows via MCP tool introspection
- Fail-fast feedback loops through MCP status monitoring

**Project Bootstrapping Acceleration:**
- Template-driven initialization via MCP automation
- Recursive dependency resolution through MCP tool discovery
- Auto-configuration of development environments
- Instant project state synchronization across assistants

### 5. PDCA Feedback Management

**Structured Feedback Loops:**
- **Plan**: Clear objectives, resources, timelines, risk factors
- **Do**: Continuous monitoring, progress documentation, lesson capture
- **Check**: Validation against criteria, quality reviews, stakeholder feedback
- **Act**: Improvement implementation, process updates, knowledge sharing

**Multi-Level Integration:**
- Real-time feedback for immediate course correction
- Sprint feedback for planning adjustments
- Release feedback for post-deployment monitoring
- Strategic feedback for quarterly reviews

### 6. Technology Integration & Automation

**Development Environment Standards:**
- Git with conventional commits
- uv for Python dependency management
- Comprehensive code quality tools (black, isort, mypy)
- pytest with coverage reporting
- Sphinx documentation with auto-generation

**Automation Framework:**
- Template-based component scaffolding
- Auto-sync between code and documentation
- Automated testing on code changes
- Automated staging deployments
- Health checks and performance monitoring

**Performance & Scalability:**
- API response times < 200ms (95th percentile)
- Horizontal scaling capability
- Stateless service design
- Database optimization and sharding strategies

### 7. Enhanced Digital Twin Implementation

**Synchronization Rules:**
- 1:1 correspondence between `docs/` and `src/`
- Automated matrix file updates
- Real-time architecture diagram synchronization
- Auto-generated API documentation

**Continuous Monitoring:**
- Automated drift detection between digital and physical twins
- Orchestrated agent scanning for synchronization
- Immediate flagging and correction of inconsistencies

### 8. Compliance & Security Framework

**Security Standards:**
- No hardcoded secrets or credentials
- Comprehensive input validation
- Secure authentication and authorization
- Regular security dependency updates

**Compliance Requirements:**
- OpenAPI specifications for all public APIs
- Security documentation for all components
- Data flow diagrams for compliance audits
- Regular compliance reviews and training

## MCP-Driven Implementation Roadmap

### ✅ Completed (MCP-Enhanced)
- Enhanced project rules with Git MCP integration patterns
- Multi-assistant coordination via MCP tool orchestration
- Git worktree management framework for parallel LLM operations
- Recursive MCP tool leveraging strategies
- High-momentum execution patterns documentation
- MCP-driven project bootstrapping specifications

### 🔄 In Progress (MCP-Accelerated)
- CTO Command Office bootstrap via recursive MCP automation
- Git MCP worktree setup for parallel assistant coordination
- Context7 MCP integration for documentation synchronization
- Stripe MCP integration for business logic automation

### 📋 Next Steps (MCP-First)
- Automated Git MCP workflow implementation
- Recursive MCP tool chain validation
- Cross-MCP server coordination protocols
- MCP-driven performance monitoring
- Self-healing MCP workflow deployment

## Git MCP Workflow Examples

### Multi-Assistant Parallel Development
```bash
# Assistant 1: Feature development
git_create_branch("estratix-trae-feature-auth")
git_checkout("estratix-trae-feature-auth")
# Work in isolated worktree
git_status() # Monitor changes
git_add(["src/auth/"]) 
git_commit("feat: implement OAuth integration")

# Assistant 2: Concurrent bug fix
git_create_branch("estratix-windsurf-fix-validation")
git_checkout("estratix-windsurf-fix-validation")
# Work in parallel worktree
git_diff_unstaged() # Review changes
git_add(["src/validation/"])
git_commit("fix: resolve input validation edge case")

# Coordination Assistant: Integration
git_checkout("main")
git_diff("estratix-trae-feature-auth") # Review changes
git_diff("estratix-windsurf-fix-validation") # Check conflicts
# Merge coordination via MCP orchestration
```

### Recursive MCP Bootstrapping
```python
# Self-bootstrapping project initialization
mcp_chain = [
    git_init(repo_path),
    git_create_branch("estratix-bootstrap-initial"),
    context7_resolve_library("fastapi"),
    context7_get_docs(library_id, "project-structure"),
    git_add(["."])
    git_commit("bootstrap: initial project structure")
]
# Execute chain with automatic rollback on failure
```

## MCP-Enhanced Benefits Achieved

1. **Zero-Friction Coordination**: MCP-driven multi-assistant collaboration with instant context sharing
2. **Recursive Simplicity**: Complex workflows reduced to simple MCP tool chains
3. **High-Momentum Execution**: Continuous task flow through MCP automation
4. **Parallel Development**: Git worktree management enabling simultaneous LLM operations
5. **Self-Healing Workflows**: MCP tools monitoring and correcting workflow drift
6. **Compound Tool Effects**: Synergistic integration across Git, Context7, and Stripe MCP servers
7. **Instant Bootstrapping**: Project initialization and scaling through recursive MCP patterns
8. **Cognitive Load Reduction**: Standardized MCP patterns minimizing decision overhead
9. **Real-Time Synchronization**: Live project state coordination across all assistants
10. **Fail-Fast Feedback**: Immediate error detection and correction via MCP monitoring

## Architecture Alignment

The MCP-enhanced rules are fully aligned with:
- ESTRATIX Master Project Architecture with MCP-first integration
- Hexagonal Architecture principles via MCP boundary management
- Domain-Driven Design patterns through MCP domain orchestration
- Agent-First development approach with native MCP tool integration
- Digital Twin implementation via MCP synchronization protocols

## Practical Implementation Guidelines

### Immediate Adoption Steps
1. **Initialize Git MCP Workflow**: Set up parallel worktrees for each active assistant
2. **Establish MCP Tool Chains**: Create standardized sequences for common operations
3. **Configure Recursive Patterns**: Implement self-bootstrapping project templates
4. **Deploy Cross-MCP Integration**: Connect Git, Context7, and Stripe MCP servers
5. **Activate High-Momentum Protocols**: Enable zero-friction task switching

### Daily Operation Patterns
- **Morning Sync**: `git_status()` across all worktrees + MCP health check
- **Task Initiation**: Recursive MCP chain execution for new features
- **Continuous Integration**: Real-time `git_diff()` monitoring between assistants
- **Evening Consolidation**: MCP-orchestrated merge and synchronization

### Emergency Protocols
- **Conflict Resolution**: Automated MCP-driven conflict detection and resolution
- **Rollback Procedures**: Git MCP + Context7 MCP coordinated state restoration
- **Recovery Patterns**: Self-healing workflow reactivation via MCP monitoring

## Conclusion

The MCP-enhanced project rules establish a revolutionary framework for high-velocity, multi-assistant development that leverages recursive simplicity and compound tool effects. By treating MCP tools as first-class citizens in all workflows, the ESTRATIX project achieves unprecedented coordination efficiency, development momentum, and quality assurance.

This framework enables:
- **Instant Context Switching** via Git MCP worktree management
- **Zero-Friction Collaboration** through standardized MCP patterns
- **Self-Healing Operations** via recursive MCP monitoring
- **Compound Acceleration** through cross-MCP server integration

The result is a development environment that maintains high momentum while ensuring architectural consistency, quality standards, and seamless multi-assistant coordination.

---

**Document Version**: 2.0 (MCP-Enhanced)  
**Last Updated**: Current Session  
**Next Review**: After MCP workflow implementation  
**Maintained By**: MCP-Coordinated Multi-Assistant Team  
**MCP Integration Level**: Full Recursive Implementation