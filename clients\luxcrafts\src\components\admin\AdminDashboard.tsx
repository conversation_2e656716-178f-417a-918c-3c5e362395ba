import React from 'react';
import { useAuthStore } from '../../stores/authStore';
import { UserRole, Permission } from '../../types/auth';
import { 
  Users, 
  Settings, 
  BarChart3, 
  Building, 
  Bot, 
  FileText, 
  DollarSign, 
  Briefcase,
  Home,
  Shield,
  Database,
  Cpu,
  TrendingUp,
  Calendar,
  MessageSquare
} from 'lucide-react';

interface DashboardCard {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  permission?: Permission;
  roles?: UserRole[];
}

const AdminDashboard: React.FC = () => {
  const { user, hasPermission, hasRole } = useAuthStore();

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600">Please log in to access the admin dashboard.</p>
        </div>
      </div>
    );
  }

  const dashboardCards: DashboardCard[] = [
    // Super Admin Cards
    {
      title: 'Infrastructure Management',
      description: 'Manage servers, databases, and system infrastructure',
      icon: <Database className="h-8 w-8" />,
      href: '/admin/infrastructure',
      permission: Permission.MANAGE_INFRASTRUCTURE
    },
    {
      title: 'Client Management',
      description: 'Manage all client accounts and configurations',
      icon: <Users className="h-8 w-8" />,
      href: '/admin/clients',
      permission: Permission.MANAGE_ALL_CLIENTS
    },
    
    // Agency Admin Cards
    {
      title: 'Client Accounts',
      description: 'Manage client accounts and team members',
      icon: <Briefcase className="h-8 w-8" />,
      href: '/admin/client-accounts',
      permission: Permission.MANAGE_CLIENT_ACCOUNTS
    },
    {
      title: 'Analytics Overview',
      description: 'View comprehensive analytics across all clients',
      icon: <BarChart3 className="h-8 w-8" />,
      href: '/admin/analytics',
      permission: Permission.VIEW_ALL_ANALYTICS
    },
    {
      title: 'Developer Management',
      description: 'Manage developer access and permissions',
      icon: <Cpu className="h-8 w-8" />,
      href: '/admin/developers',
      permission: Permission.MANAGE_DEVELOPERS
    },
    
    // Client Admin Cards
    {
      title: 'Property Management',
      description: 'Manage properties, listings, and availability',
      icon: <Building className="h-8 w-8" />,
      href: '/admin/properties',
      permission: Permission.MANAGE_PROPERTIES
    },
    {
      title: 'Team Management',
      description: 'Manage team members and their roles',
      icon: <Users className="h-8 w-8" />,
      href: '/admin/team',
      permission: Permission.MANAGE_TEAM
    },
    {
      title: 'Client Analytics',
      description: 'View analytics for your organization',
      icon: <TrendingUp className="h-8 w-8" />,
      href: '/admin/client-analytics',
      permission: Permission.VIEW_CLIENT_ANALYTICS
    },
    
    // Developer Cards
    {
      title: 'Development Tools',
      description: 'Access development tools and environments',
      icon: <Settings className="h-8 w-8" />,
      href: '/admin/dev-tools',
      permission: Permission.ACCESS_DEV_TOOLS
    },
    {
      title: 'Deployment Management',
      description: 'Deploy and manage applications',
      icon: <Shield className="h-8 w-8" />,
      href: '/admin/deployments',
      permission: Permission.DEPLOY_APPLICATIONS
    },
    {
      title: 'Integrations',
      description: 'Manage API integrations and webhooks',
      icon: <Database className="h-8 w-8" />,
      href: '/admin/integrations',
      permission: Permission.MANAGE_INTEGRATIONS
    },
    
    // Property Manager Cards
    {
      title: 'Listings Management',
      description: 'Manage property listings and details',
      icon: <Home className="h-8 w-8" />,
      href: '/admin/listings',
      permission: Permission.MANAGE_LISTINGS
    },
    {
      title: 'Booking Management',
      description: 'Handle bookings and reservations',
      icon: <Calendar className="h-8 w-8" />,
      href: '/admin/bookings',
      permission: Permission.HANDLE_BOOKINGS
    },
    {
      title: 'Maintenance',
      description: 'Manage property maintenance and repairs',
      icon: <Settings className="h-8 w-8" />,
      href: '/admin/maintenance',
      permission: Permission.MANAGE_MAINTENANCE
    },
    
    // AI Agent Operator Cards
    {
      title: 'AI Agents',
      description: 'Manage and monitor AI agents',
      icon: <Bot className="h-8 w-8" />,
      href: '/admin/ai-agents',
      permission: Permission.MANAGE_AI_AGENTS
    },
    {
      title: 'Model Training',
      description: 'Train and optimize AI models',
      icon: <Cpu className="h-8 w-8" />,
      href: '/admin/model-training',
      permission: Permission.TRAIN_MODELS
    },
    {
      title: 'AI Analytics',
      description: 'View AI performance analytics',
      icon: <BarChart3 className="h-8 w-8" />,
      href: '/admin/ai-analytics',
      permission: Permission.VIEW_AI_ANALYTICS
    },
    
    // Content Manager Cards
    {
      title: 'Content Management',
      description: 'Manage website content and pages',
      icon: <FileText className="h-8 w-8" />,
      href: '/admin/content',
      permission: Permission.MANAGE_CONTENT
    },
    {
      title: 'Media Library',
      description: 'Manage images, videos, and documents',
      icon: <FileText className="h-8 w-8" />,
      href: '/admin/media',
      permission: Permission.MANAGE_MEDIA
    },
    
    // Investor Cards
    {
      title: 'Investment Opportunities',
      description: 'Browse available investment opportunities',
      icon: <TrendingUp className="h-8 w-8" />,
      href: '/admin/investments',
      permission: Permission.VIEW_INVESTMENT_OPPORTUNITIES
    },
    {
      title: 'Portfolio Management',
      description: 'Manage your investment portfolio',
      icon: <DollarSign className="h-8 w-8" />,
      href: '/admin/portfolio',
      permission: Permission.MANAGE_PORTFOLIO
    },
    {
      title: 'Financial Data',
      description: 'Access detailed financial reports',
      icon: <BarChart3 className="h-8 w-8" />,
      href: '/admin/financial-data',
      permission: Permission.ACCESS_FINANCIAL_DATA
    },
    
    // Service Provider Cards
    {
      title: 'Service Management',
      description: 'Manage your services and offerings',
      icon: <Briefcase className="h-8 w-8" />,
      href: '/admin/services',
      permission: Permission.MANAGE_SERVICES
    },
    {
      title: 'Service Bookings',
      description: 'Handle service bookings and schedules',
      icon: <Calendar className="h-8 w-8" />,
      href: '/admin/service-bookings',
      permission: Permission.HANDLE_SERVICE_BOOKINGS
    },
    {
      title: 'Earnings',
      description: 'View earnings and payment history',
      icon: <DollarSign className="h-8 w-8" />,
      href: '/admin/earnings',
      permission: Permission.VIEW_EARNINGS
    }
  ];

  const availableCards = dashboardCards.filter(card => {
    if (card.permission) {
      return hasPermission(card.permission);
    }
    if (card.roles) {
      return card.roles.some(role => hasRole(role));
    }
    return true;
  });

  const getRoleDisplayName = (role: UserRole): string => {
    return role.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
    ).join(' ');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600 mt-1">
                Welcome back, {user.name} ({getRoleDisplayName(user.role)})
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm text-gray-500">Subdomain</p>
                <p className="font-medium">{user.subdomain || 'main'}</p>
              </div>
              <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-medium">
                  {user.name.charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Dashboard Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Users</p>
                <p className="text-2xl font-bold text-gray-900">1,234</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Revenue</p>
                <p className="text-2xl font-bold text-gray-900">$45.2K</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Calendar className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Bookings</p>
                <p className="text-2xl font-bold text-gray-900">89</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <MessageSquare className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Messages</p>
                <p className="text-2xl font-bold text-gray-900">12</p>
              </div>
            </div>
          </div>
        </div>

        {/* Dashboard Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {availableCards.map((card, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow hover:shadow-md transition-shadow cursor-pointer"
              onClick={() => window.location.href = card.href}
            >
              <div className="p-6">
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
                    {card.icon}
                  </div>
                  <h3 className="ml-3 text-lg font-semibold text-gray-900">
                    {card.title}
                  </h3>
                </div>
                <p className="text-gray-600 text-sm">
                  {card.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {availableCards.length === 0 && (
          <div className="text-center py-12">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Dashboard Access
            </h3>
            <p className="text-gray-600">
              You don't have permission to access any admin features.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;