# ESTRATIX Frontend Technology Matrix

---

## 1. Overview

This matrix catalogs all approved and evaluated frontend technologies, including JavaScript frameworks, UI component libraries, and state management solutions. It provides a standardized toolkit for building consistent, high-quality user interfaces across all ESTRATIX projects and is essential for streamlining the frontend development lifecycle.

---

## 2. Technology Inventory

| Category | Technology | License | ESTRATIX Status | Primary Use Case | Integration Notes & Patterns | Traceability/Project Link |
|---|---|---|---|---|---|---|
| **Framework** | React | MIT | **Approved** | Building complex, interactive user interfaces with a component-based architecture. | Use with state management like Redux or Zustand. Pair with Next.js for SSR. | `LIB-REACT` |
| **Framework** | Vue.js | MIT | **Approved** | Approachable and versatile framework for building UIs. | Core for SPAs. Use with **Nuxt.js** for SSR and static site generation. | `LIB-VUE` |
| **Meta-Framework** | Nuxt.js | MIT | **Approved** | Building server-side rendered (SSR) and static sites with Vue.js. | Provides a convention-over-configuration setup for Vue, including routing and state management. | `LIB-NUXT` |
| **Framework** | Angular | MIT | **Evaluating** | Comprehensive platform for building large-scale enterprise applications. | Opinionated structure, includes routing, state management, and more out-of-the-box. | `R&D-FRONTEND-EVAL` |
| **Framework** | Svelte | MIT | **Approved** | Compiler-based framework for building highly performant, lightweight UIs. | No virtual DOM. Use with **SvelteKit** for full-featured applications. | `LIB-SVELTE` |
| **Meta-Framework** | SvelteKit | MIT | **Approved** | The official application framework for Svelte, for building robust, scalable web apps. | Handles routing, server-side rendering, and build optimizations. | `LIB-SVELTEKIT` |
| **Lightweight Framework** | Alpine.js | MIT | **Approved** | A rugged, minimal framework for composing behavior directly in your markup. | Ideal for adding JavaScript behavior to server-rendered HTML, similar to HTMX but client-side focused. | `LIB-ALPINEJS` |
| **Framework** | HTMX | BSD-2-Clause | **Approved** | Extends HTML for modern UI patterns without complex JavaScript. | Ideal for server-side rendered applications needing dynamic updates. | `LIB-HTMX` |
| **UI Library** | Bootstrap | MIT | **Approved** | Foundational CSS framework for responsive, mobile-first design. | Core of ESTRATIX UI standard; provides base components and grid system. | `STD-UI-CSS` |
| **UI Library** | Tailwind CSS | MIT | **Evaluating** | Utility-first CSS framework for rapid custom UI development. | Alternative to component-based CSS for highly bespoke designs. | `R&D-CSS-EVAL` |
| **UI Library** | Material-UI (MUI)| MIT | **Evaluating** | React components for faster and easier web development, based on Google's Material Design. | For projects requiring a consistent Material Design look and feel. | `R&D-UI-LIB-EVAL` |
| **State Management**| Redux | MIT | **Approved** | Predictable state container for JavaScript apps. | Standard for large-scale React applications with complex state. | `LIB-REDUX` |
| **State Management**| Zustand | MIT | **Evaluating** | Small, fast, and scalable state-management solution for React. | Simpler alternative to Redux for less complex state needs. | `R&D-STATE-MGT-EVAL` |

---

## 3. Maintenance

This matrix must be updated when new frontend technologies are considered or when their status changes. Selections must be linked to the `project_matrix.md` to ensure clear traceability for client and internal projects.
