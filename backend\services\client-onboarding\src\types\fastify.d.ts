import { FastifyInstance } from 'fastify';
import { ClientService } from '@/services/clientService';
import { RFPService } from '@/services/rfpService';
import { OnboardingService } from '@/services/onboardingService';
import { DocumentService } from '@/services/documentService';
import { EmailService } from '@/services/emailService';
import { AnalyticsService } from '@/services/analyticsService';

declare module 'fastify' {
  interface FastifyInstance {
    clientService: ClientService;
    rfpService: RFPService;
    onboardingService: OnboardingService;
    documentService: DocumentService;
    emailService: EmailService;
    analyticsService: AnalyticsService;
  }
}