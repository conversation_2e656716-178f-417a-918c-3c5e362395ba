import Bull, { Job, Queue } from 'bull';
import { logger } from '../utils/logger';
import { config } from '../config/environment';
import { AIContentService } from '../services/aiContentService';
import { vectorStoreService } from '../services/vectorStoreService';

export interface ContentGenerationJobData {
  id: string;
  userId: string;
  type: 'single_content' | 'campaign' | 'optimization' | 'analysis';
  parameters: any;
  priority?: number;
  retryAttempts?: number;
  createdAt: string;
}

export interface JobProgress {
  percentage: number;
  stage: string;
  message: string;
  estimatedTimeRemaining?: number;
}

export interface JobResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    processingTime: number;
    tokensUsed?: number;
    modelUsed?: string;
    cost?: number;
  };
}

class ContentGenerationQueue {
  private queue: Queue<ContentGenerationJobData>;
  private aiContentService: AIContentService;
  private isInitialized = false;

  constructor() {
    try {
      this.queue = new Bull('content-generation', {
        redis: {
          host: config.queue.redis.host,
          port: config.queue.redis.port,
          password: config.queue.redis.password,
          db: config.queue.redis.db,
          connectTimeout: 5000,
          lazyConnect: true,
          retryDelayOnFailover: 100,
          maxRetriesPerRequest: 3,
        },
        defaultJobOptions: {
          removeOnComplete: 100, // Keep last 100 completed jobs
          removeOnFail: 50, // Keep last 50 failed jobs
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      // Handle Redis connection errors
      this.queue.on('error', (error) => {
        if (config.nodeEnv === 'development') {
          logger.warn('Redis connection error in development mode:', error.message);
        } else {
          logger.error('Redis connection error:', error);
        }
      });

      // Handle Redis connection events to prevent unhandled rejections
      this.queue.on('failed', (job, error) => {
        logger.debug('Job failed:', { jobId: job?.id, error: error.message });
      });

      this.queue.on('stalled', (job) => {
        logger.debug('Job stalled:', { jobId: job?.id });
      });

      // Add error handling for the Redis client itself
      if (this.queue.client) {
        this.queue.client.on('error', (error) => {
          if (config.nodeEnv === 'development') {
            logger.debug('Redis client error in development mode:', error.message);
          } else {
            logger.error('Redis client error:', error);
          }
        });
      }

      this.aiContentService = new AIContentService();
    } catch (error) {
      logger.error('Failed to create content generation queue:', error);
      if (config.nodeEnv === 'production') {
        throw error;
      }
    }
  }

  async initialize(): Promise<void> {
    try {
      // In development mode, make queue optional if Redis is not available
      if (config.nodeEnv === 'development') {
        try {
          // Test Redis connection with timeout
          const connectionPromise = this.queue.isReady();
          const timeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Redis connection timeout')), 5000)
          );
          
          await Promise.race([connectionPromise, timeoutPromise]);
        } catch (redisError) {
          logger.warn('Redis not available, content generation queue disabled in development mode:', redisError);
          this.isInitialized = false;
          return;
        }
      }
      
      // Set up job processors
      this.setupProcessors();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Clean up old jobs
      await this.cleanupOldJobs();
      
      this.isInitialized = true;
      logger.info('Content generation queue initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize content generation queue:', error);
      if (config.nodeEnv === 'production') {
        throw error;
      } else {
        logger.warn('Content generation queue disabled due to initialization failure in development');
        this.isInitialized = false;
      }
    }
  }

  private setupProcessors(): void {
    // Single content generation processor
    this.queue.process('single_content', 5, async (job: Job<ContentGenerationJobData>) => {
      return this.processSingleContent(job);
    });

    // Campaign generation processor
    this.queue.process('campaign', 2, async (job: Job<ContentGenerationJobData>) => {
      return this.processCampaign(job);
    });

    // Content optimization processor
    this.queue.process('optimization', 3, async (job: Job<ContentGenerationJobData>) => {
      return this.processOptimization(job);
    });

    // Content analysis processor
    this.queue.process('analysis', 5, async (job: Job<ContentGenerationJobData>) => {
      return this.processAnalysis(job);
    });
  }

  private setupEventListeners(): void {
    this.queue.on('completed', (job: Job, result: JobResult) => {
      logger.info(`Job ${job.id} completed successfully`, {
        jobId: job.id,
        type: job.data.type,
        userId: job.data.userId,
        processingTime: result.metadata?.processingTime,
      });
    });

    this.queue.on('failed', (job: Job, error: Error) => {
      logger.error(`Job ${job.id} failed`, {
        jobId: job.id,
        type: job.data.type,
        userId: job.data.userId,
        error: error.message,
        attempts: job.attemptsMade,
      });
    });

    this.queue.on('stalled', (job: Job) => {
      logger.warn(`Job ${job.id} stalled`, {
        jobId: job.id,
        type: job.data.type,
        userId: job.data.userId,
      });
    });

    this.queue.on('progress', (job: Job, progress: JobProgress) => {
      logger.debug(`Job ${job.id} progress: ${progress.percentage}%`, {
        jobId: job.id,
        stage: progress.stage,
        message: progress.message,
      });
    });
  }

  private async processSingleContent(job: Job<ContentGenerationJobData>): Promise<JobResult> {
    const startTime = Date.now();
    
    try {
      await job.progress({
        percentage: 10,
        stage: 'initialization',
        message: 'Starting content generation...',
      });

      const { parameters } = job.data;
      
      await job.progress({
        percentage: 30,
        stage: 'context_retrieval',
        message: 'Retrieving relevant context...',
      });

      // Generate content using AI service
      const result = await this.aiContentService.generateContent(parameters);
      
      await job.progress({
        percentage: 80,
        stage: 'post_processing',
        message: 'Processing and optimizing content...',
      });

      // Store in vector database for future context
      if (result.content) {
        try {
          await this.storeGeneratedContent(result, job.data.userId);
        } catch (error) {
          logger.warn('Failed to store content in vector database:', error);
        }
      }

      await job.progress({
        percentage: 100,
        stage: 'completed',
        message: 'Content generation completed successfully',
      });

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result,
        metadata: {
          processingTime,
          tokensUsed: result.metadata?.tokensUsed,
          modelUsed: result.metadata?.modelUsed,
          cost: result.metadata?.cost,
        },
      };
    } catch (error) {
      logger.error('Single content generation failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  private async processCampaign(job: Job<ContentGenerationJobData>): Promise<JobResult> {
    const startTime = Date.now();
    
    try {
      await job.progress({
        percentage: 5,
        stage: 'initialization',
        message: 'Starting campaign generation...',
      });

      const { parameters } = job.data;
      const contentCount = parameters.contentCount || 5;
      
      await job.progress({
        percentage: 15,
        stage: 'planning',
        message: 'Planning campaign structure...',
      });

      // Generate campaign using AI service
      const result = await this.aiContentService.generateCampaign(parameters);
      
      // Process each piece of content
      const processedContent = [];
      for (let i = 0; i < result.content.length; i++) {
        const progressPercentage = 20 + (i / result.content.length) * 60;
        
        await job.progress({
          percentage: progressPercentage,
          stage: 'content_generation',
          message: `Generating content ${i + 1} of ${result.content.length}...`,
        });

        const contentItem = result.content[i];
        processedContent.push(contentItem);

        // Store each piece in vector database
        try {
          await this.storeGeneratedContent(contentItem, job.data.userId);
        } catch (error) {
          logger.warn(`Failed to store content item ${i + 1} in vector database:`, error);
        }
      }

      await job.progress({
        percentage: 90,
        stage: 'finalization',
        message: 'Finalizing campaign...',
      });

      await job.progress({
        percentage: 100,
        stage: 'completed',
        message: 'Campaign generation completed successfully',
      });

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        data: {
          ...result,
          content: processedContent,
        },
        metadata: {
          processingTime,
          tokensUsed: result.metadata?.tokensUsed,
          modelUsed: result.metadata?.modelUsed,
          cost: result.metadata?.cost,
        },
      };
    } catch (error) {
      logger.error('Campaign generation failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  private async processOptimization(job: Job<ContentGenerationJobData>): Promise<JobResult> {
    const startTime = Date.now();
    
    try {
      await job.progress({
        percentage: 10,
        stage: 'initialization',
        message: 'Starting content optimization...',
      });

      const { parameters } = job.data;
      
      await job.progress({
        percentage: 30,
        stage: 'analysis',
        message: 'Analyzing current content...',
      });

      // Optimize content using AI service
      const result = await this.aiContentService.optimizeContent(
        parameters.content,
        parameters.optimizationType,
        parameters.options
      );
      
      await job.progress({
        percentage: 80,
        stage: 'optimization',
        message: 'Applying optimizations...',
      });

      // Store optimized content
      if (result.optimizedContent) {
        try {
          await this.storeGeneratedContent({
            content: result.optimizedContent,
            metadata: {
              ...result.metadata,
              originalContentId: parameters.contentId,
              optimizationType: parameters.optimizationType,
            },
          }, job.data.userId);
        } catch (error) {
          logger.warn('Failed to store optimized content in vector database:', error);
        }
      }

      await job.progress({
        percentage: 100,
        stage: 'completed',
        message: 'Content optimization completed successfully',
      });

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result,
        metadata: {
          processingTime,
          tokensUsed: result.metadata?.tokensUsed,
          modelUsed: result.metadata?.modelUsed,
          cost: result.metadata?.cost,
        },
      };
    } catch (error) {
      logger.error('Content optimization failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  private async processAnalysis(job: Job<ContentGenerationJobData>): Promise<JobResult> {
    const startTime = Date.now();
    
    try {
      await job.progress({
        percentage: 10,
        stage: 'initialization',
        message: 'Starting content analysis...',
      });

      const { parameters } = job.data;
      
      await job.progress({
        percentage: 50,
        stage: 'analysis',
        message: 'Analyzing content...',
      });

      // Analyze content using AI service
      const result = await this.aiContentService.analyzeContent(
        parameters.content,
        parameters.analysisType,
        parameters.options
      );
      
      await job.progress({
        percentage: 90,
        stage: 'processing',
        message: 'Processing analysis results...',
      });

      await job.progress({
        percentage: 100,
        stage: 'completed',
        message: 'Content analysis completed successfully',
      });

      const processingTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result,
        metadata: {
          processingTime,
          tokensUsed: result.metadata?.tokensUsed,
          modelUsed: result.metadata?.modelUsed,
          cost: result.metadata?.cost,
        },
      };
    } catch (error) {
      logger.error('Content analysis failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        metadata: {
          processingTime: Date.now() - startTime,
        },
      };
    }
  }

  private async storeGeneratedContent(content: any, userId: string): Promise<void> {
    try {
      // Generate embedding for the content
      const embedding = await this.aiContentService.generateEmbedding(content.content || content.text);
      
      const document = {
        id: `generated_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: content.content || content.text,
        embedding,
        metadata: {
          type: 'content' as const,
          title: content.title || content.headline,
          category: content.category,
          platform: content.platform,
          language: content.language || 'en',
          userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          tags: content.tags || [],
          generated: true,
          ...content.metadata,
        },
      };

      await vectorStoreService.addDocument(document);
    } catch (error) {
      logger.error('Failed to store generated content:', error);
      throw error;
    }
  }

  async addJob(
    type: ContentGenerationJobData['type'],
    parameters: any,
    userId: string,
    options: {
      priority?: number;
      delay?: number;
      retryAttempts?: number;
    } = {}
  ): Promise<string> {
    try {
      const jobData: ContentGenerationJobData = {
        id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type,
        parameters,
        priority: options.priority || 0,
        retryAttempts: options.retryAttempts || 3,
        createdAt: new Date().toISOString(),
      };

      const job = await this.queue.add(type, jobData, {
        priority: options.priority || 0,
        delay: options.delay || 0,
        attempts: options.retryAttempts || 3,
      });

      logger.info(`Added job to queue: ${job.id}`, {
        jobId: job.id,
        type,
        userId,
        priority: options.priority,
      });

      return job.id.toString();
    } catch (error) {
      logger.error('Failed to add job to queue:', error);
      throw error;
    }
  }

  async getJobStatus(jobId: string): Promise<{
    id: string;
    status: string;
    progress?: JobProgress;
    result?: JobResult;
    error?: string;
    createdAt: Date;
    processedAt?: Date;
    finishedAt?: Date;
  } | null> {
    try {
      const job = await this.queue.getJob(jobId);
      
      if (!job) {
        return null;
      }

      const state = await job.getState();
      
      return {
        id: job.id.toString(),
        status: state,
        progress: job.progress() as JobProgress,
        result: job.returnvalue as JobResult,
        error: job.failedReason,
        createdAt: new Date(job.timestamp),
        processedAt: job.processedOn ? new Date(job.processedOn) : undefined,
        finishedAt: job.finishedOn ? new Date(job.finishedOn) : undefined,
      };
    } catch (error) {
      logger.error('Failed to get job status:', error);
      throw error;
    }
  }

  async cancelJob(jobId: string): Promise<boolean> {
    try {
      const job = await this.queue.getJob(jobId);
      
      if (!job) {
        return false;
      }

      await job.remove();
      
      logger.info(`Cancelled job: ${jobId}`);
      return true;
    } catch (error) {
      logger.error('Failed to cancel job:', error);
      throw error;
    }
  }

  async getQueueStats(): Promise<{
    waiting: number;
    active: number;
    completed: number;
    failed: number;
    delayed: number;
    paused: number;
  }> {
    try {
      // Return empty stats if queue is not initialized
      if (!this.isInitialized) {
        return {
          waiting: 0,
          active: 0,
          completed: 0,
          failed: 0,
          delayed: 0,
          paused: 0,
        };
      }
      
      const [waiting, active, completed, failed, delayed, paused] = await Promise.all([
        this.queue.getWaiting(),
        this.queue.getActive(),
        this.queue.getCompleted(),
        this.queue.getFailed(),
        this.queue.getDelayed(),
        this.queue.getPaused(),
      ]);

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        delayed: delayed.length,
        paused: paused.length,
      };
    } catch (error) {
      logger.error('Failed to get queue stats:', error);
      throw error;
    }
  }

  async pauseQueue(): Promise<void> {
    try {
      await this.queue.pause();
      logger.info('Content generation queue paused');
    } catch (error) {
      logger.error('Failed to pause queue:', error);
      throw error;
    }
  }

  async resumeQueue(): Promise<void> {
    try {
      await this.queue.resume();
      logger.info('Content generation queue resumed');
    } catch (error) {
      logger.error('Failed to resume queue:', error);
      throw error;
    }
  }

  private async cleanupOldJobs(): Promise<void> {
    try {
      // Clean jobs older than 7 days
      const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
      
      await this.queue.clean(sevenDaysAgo, 'completed');
      await this.queue.clean(sevenDaysAgo, 'failed');
      
      logger.info('Cleaned up old jobs from queue');
    } catch (error) {
      logger.error('Failed to cleanup old jobs:', error);
    }
  }

  async shutdown(): Promise<void> {
    try {
      if (this.isInitialized) {
        await this.queue.close();
        logger.info('Content generation queue shut down');
      } else {
        logger.info('Content generation queue was not initialized');
      }
    } catch (error) {
      logger.error('Failed to shutdown queue:', error);
    }
  }
}

// Export singleton instance
export const contentGenerationQueue = new ContentGenerationQueue();