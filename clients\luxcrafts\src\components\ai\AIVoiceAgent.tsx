import React, { useState, useRef, useEffect } from 'react';

// Extend Window interface for speech recognition
declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

interface SpeechRecognition {
  continuous: boolean;
  interimResults: boolean;
  onresult: (event: any) => void;
  start: () => void;
  stop: () => void;
}
import { 
  Mic, 
  MicOff, 
  Phone, 
  PhoneOff, 
  Volume2, 
  VolumeX, 
  Settings, 
  Play, 
  Pause, 
  RotateCcw,
  MessageSquare,
  TrendingUp,
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface AIVoiceAgentProps {
  agentType: 'acquisition' | 'disposition';
  propertyId?: string;
  contactId?: string;
  onCallComplete?: (result: CallResult) => void;
}

interface CallResult {
  success: boolean;
  duration: number;
  outcome: string;
  nextSteps: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  keyPoints: string[];
  followUpDate?: Date;
}

interface NegotiationScript {
  id: string;
  name: string;
  type: 'acquisition' | 'disposition';
  steps: NegotiationStep[];
}

interface NegotiationStep {
  id: string;
  title: string;
  script: string;
  expectedResponses: string[];
  nextSteps: { [key: string]: string };
  fallbackScript?: string;
}

const AIVoiceAgent: React.FC<AIVoiceAgentProps> = ({ 
  agentType, 
  propertyId, 
  contactId, 
  onCallComplete 
}) => {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [callDuration, setCallDuration] = useState(0);
  const [transcript, setTranscript] = useState<string[]>([]);
  const [agentResponse, setAgentResponse] = useState('');
  const [callStatus, setCallStatus] = useState<'idle' | 'connecting' | 'active' | 'completed'>('idle');
  const [selectedScript, setSelectedScript] = useState<string>('');
  
  const callTimer = useRef<NodeJS.Timeout | null>(null);
  const recognition = useRef<SpeechRecognition | null>(null);
  const synthesis = useRef<SpeechSynthesis | null>(null);

  // Mock negotiation scripts
  const negotiationScripts: NegotiationScript[] = [
    {
      id: 'acquisition-cold-call',
      name: 'Property Acquisition - Cold Call',
      type: 'acquisition',
      steps: [
        {
          id: 'intro',
          title: 'Introduction',
          script: 'Hello, this is Sarah from Luxcrafts Properties. I hope I\'m not catching you at a bad time. I\'m calling because we\'re actively looking to purchase properties in your area, and I noticed your beautiful property at [ADDRESS]. Would you be open to discussing a potential sale?',
          expectedResponses: ['interested', 'not_interested', 'maybe', 'more_info'],
          nextSteps: {
            'interested': 'value_proposition',
            'not_interested': 'objection_handling',
            'maybe': 'value_proposition',
            'more_info': 'company_info'
          }
        },
        {
          id: 'value_proposition',
          title: 'Value Proposition',
          script: 'That\'s wonderful! We specialize in quick, hassle-free purchases. We can close in as little as 7 days, pay all cash, and handle all the paperwork. There are no realtor fees, no repairs needed, and no showings. We buy properties as-is. Based on recent sales in your area, we could potentially offer between $[MIN_PRICE] and $[MAX_PRICE]. Would you like me to schedule a quick property assessment?',
          expectedResponses: ['schedule', 'price_question', 'timeline_question', 'skeptical'],
          nextSteps: {
            'schedule': 'scheduling',
            'price_question': 'pricing_explanation',
            'timeline_question': 'timeline_explanation',
            'skeptical': 'credibility_building'
          }
        },
        {
          id: 'objection_handling',
          title: 'Objection Handling',
          script: 'I completely understand. Many homeowners aren\'t actively thinking about selling. However, the market is quite favorable right now, and we\'ve been able to help families in situations where they needed to relocate quickly, settle estates, or simply wanted to avoid the traditional selling process. Is there any scenario where you might consider selling in the next 6-12 months?',
          expectedResponses: ['maybe_future', 'firm_no', 'circumstances'],
          nextSteps: {
            'maybe_future': 'future_follow_up',
            'firm_no': 'polite_close',
            'circumstances': 'situation_exploration'
          }
        }
      ]
    },
    {
      id: 'disposition-investor-pitch',
      name: 'Property Disposition - Investor Pitch',
      type: 'disposition',
      steps: [
        {
          id: 'intro',
          title: 'Introduction',
          script: 'Hello [INVESTOR_NAME], this is Michael from Luxcrafts Investment Opportunities. I hope you\'re doing well. I\'m reaching out because we have an exclusive investment opportunity that matches your investment criteria. Do you have a few minutes to discuss a potential high-yield property investment?',
          expectedResponses: ['interested', 'busy', 'criteria_question'],
          nextSteps: {
            'interested': 'opportunity_overview',
            'busy': 'reschedule',
            'criteria_question': 'criteria_confirmation'
          }
        },
        {
          id: 'opportunity_overview',
          title: 'Investment Opportunity',
          script: 'Excellent! We have a luxury property in [LOCATION] that we\'re offering to select investors. It\'s a [PROPERTY_TYPE] with [DETAILS]. The property is currently generating $[MONTHLY_RENT] in monthly rental income with a cap rate of [CAP_RATE]%. We\'re offering it at $[PRICE], which is [DISCOUNT]% below market value. The property comes with existing tenants and a proven rental history. Are you interested in reviewing the full investment package?',
          expectedResponses: ['send_package', 'more_details', 'financing_question', 'timeline_question'],
          nextSteps: {
            'send_package': 'package_delivery',
            'more_details': 'detailed_explanation',
            'financing_question': 'financing_options',
            'timeline_question': 'timeline_explanation'
          }
        }
      ]
    }
  ];

  const currentScript = negotiationScripts.find(s => s.id === selectedScript);
  const currentStepData = currentScript?.steps[currentStep];

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize speech recognition
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
        recognition.current = new SpeechRecognition();
        recognition.current.continuous = true;
        recognition.current.interimResults = true;
        
        recognition.current.onresult = (event: any) => {
          const transcript = Array.from(event.results)
            .map((result: any) => result[0].transcript)
            .join('');
          
          if (event.results[event.results.length - 1].isFinal) {
            setTranscript(prev => [...prev, `User: ${transcript}`]);
            handleUserResponse(transcript);
          }
        };
      }
      
      // Initialize speech synthesis
      synthesis.current = window.speechSynthesis;
    }
    
    return () => {
      if (callTimer.current) {
        clearInterval(callTimer.current);
      }
    };
  }, []);

  const startCall = () => {
    if (!selectedScript) {
      alert('Please select a negotiation script first.');
      return;
    }
    
    setCallStatus('connecting');
    setIsCallActive(true);
    setCallDuration(0);
    setCurrentStep(0);
    setTranscript([]);
    
    // Simulate connection delay
    setTimeout(() => {
      setCallStatus('active');
      speakScript(currentScript?.steps[0].script || '');
      
      // Start call timer
      callTimer.current = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    }, 2000);
  };

  const endCall = () => {
    setIsCallActive(false);
    setCallStatus('completed');
    setIsListening(false);
    
    if (callTimer.current) {
      clearInterval(callTimer.current);
    }
    
    if (recognition.current) {
      recognition.current.stop();
    }
    
    // Generate call result
    const result: CallResult = {
      success: currentStep >= (currentScript?.steps.length || 0) - 1,
      duration: callDuration,
      outcome: currentStep >= (currentScript?.steps.length || 0) - 1 ? 'Completed successfully' : 'Ended early',
      nextSteps: ['Send follow-up email', 'Schedule property visit', 'Prepare contract'],
      sentiment: 'positive',
      keyPoints: transcript.filter(t => t.startsWith('User:')),
      followUpDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
    };
    
    onCallComplete?.(result);
  };

  const speakScript = (text: string) => {
    if (synthesis.current && !isMuted) {
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1;
      utterance.volume = 0.8;
      
      utterance.onend = () => {
        setIsListening(true);
        if (recognition.current) {
          recognition.current.start();
        }
      };
      
      synthesis.current.speak(utterance);
      setTranscript(prev => [...prev, `Agent: ${text}`]);
      setAgentResponse(text);
    }
  };

  const handleUserResponse = (response: string) => {
    setIsListening(false);
    if (recognition.current) {
      recognition.current.stop();
    }
    
    // Simple response classification (in real implementation, use NLP)
    const responseType = classifyResponse(response);
    const nextStepId = currentStepData?.nextSteps[responseType];
    
    if (nextStepId && currentScript) {
      const nextStepIndex = currentScript.steps.findIndex(step => step.id === nextStepId);
      if (nextStepIndex !== -1) {
        setCurrentStep(nextStepIndex);
        setTimeout(() => {
          speakScript(currentScript.steps[nextStepIndex].script);
        }, 1000);
      }
    } else {
      // Use fallback or end call
      if (currentStepData?.fallbackScript) {
        setTimeout(() => {
          speakScript(currentStepData.fallbackScript!);
        }, 1000);
      }
    }
  };

  const classifyResponse = (response: string): string => {
    const lowerResponse = response.toLowerCase();
    
    if (lowerResponse.includes('yes') || lowerResponse.includes('interested') || lowerResponse.includes('sure')) {
      return 'interested';
    }
    if (lowerResponse.includes('no') || lowerResponse.includes('not interested')) {
      return 'not_interested';
    }
    if (lowerResponse.includes('maybe') || lowerResponse.includes('think about')) {
      return 'maybe';
    }
    if (lowerResponse.includes('price') || lowerResponse.includes('cost') || lowerResponse.includes('money')) {
      return 'price_question';
    }
    if (lowerResponse.includes('when') || lowerResponse.includes('time')) {
      return 'timeline_question';
    }
    
    return 'more_info';
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            AI Voice Agent - {agentType === 'acquisition' ? 'Property Acquisition' : 'Property Disposition'}
          </h2>
          <p className="text-gray-600 mt-1">
            Automated negotiation and lead qualification
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${
            callStatus === 'idle' ? 'bg-gray-100 text-gray-800' :
            callStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
            callStatus === 'active' ? 'bg-green-100 text-green-800' :
            'bg-blue-100 text-blue-800'
          }`}>
            {callStatus.charAt(0).toUpperCase() + callStatus.slice(1)}
          </span>
        </div>
      </div>

      {/* Script Selection */}
      {!isCallActive && (
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Negotiation Script
          </label>
          <select
            value={selectedScript}
            onChange={(e) => setSelectedScript(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Choose a script...</option>
            {negotiationScripts
              .filter(script => script.type === agentType)
              .map(script => (
                <option key={script.id} value={script.id}>
                  {script.name}
                </option>
              ))
            }
          </select>
        </div>
      )}

      {/* Call Controls */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        {!isCallActive ? (
          <button
            onClick={startCall}
            disabled={!selectedScript}
            className="flex items-center space-x-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-lg font-medium transition-colors"
          >
            <Phone className="h-5 w-5" />
            <span>Start Call</span>
          </button>
        ) : (
          <>
            <button
              onClick={endCall}
              className="flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
            >
              <PhoneOff className="h-5 w-5" />
              <span>End Call</span>
            </button>
            
            <button
              onClick={() => setIsMuted(!isMuted)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                isMuted ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'
              }`}
            >
              {isMuted ? <VolumeX className="h-5 w-5" /> : <Volume2 className="h-5 w-5" />}
              <span>{isMuted ? 'Unmute' : 'Mute'}</span>
            </button>
            
            <div className="flex items-center space-x-2 text-gray-600">
              <Clock className="h-5 w-5" />
              <span className="font-mono">{formatDuration(callDuration)}</span>
            </div>
          </>
        )}
      </div>

      {/* Call Progress */}
      {isCallActive && currentScript && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Step {currentStep + 1} of {currentScript.steps.length}: {currentStepData?.title}
            </span>
            <span className={`flex items-center space-x-1 text-sm ${
              isListening ? 'text-green-600' : 'text-gray-500'
            }`}>
              {isListening ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
              <span>{isListening ? 'Listening...' : 'Speaking'}</span>
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / currentScript.steps.length) * 100}%` }}
            />
          </div>
        </div>
      )}

      {/* Current Agent Response */}
      {agentResponse && (
        <div className="mb-6 p-4 bg-blue-50 rounded-lg">
          <div className="flex items-start space-x-2">
            <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-blue-900 mb-1">AI Agent:</p>
              <p className="text-blue-800">{agentResponse}</p>
            </div>
          </div>
        </div>
      )}

      {/* Call Transcript */}
      {transcript.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Call Transcript</h3>
          <div className="bg-gray-50 rounded-lg p-4 max-h-64 overflow-y-auto">
            {transcript.map((line, index) => (
              <div key={index} className={`mb-2 ${
                line.startsWith('Agent:') ? 'text-blue-700' : 'text-gray-700'
              }`}>
                <span className="font-medium">
                  {line.split(':')[0]}:
                </span>
                <span className="ml-2">
                  {line.split(':').slice(1).join(':')}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Call Analytics */}
      {callStatus === 'completed' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="font-medium text-green-900">Call Completed</span>
            </div>
            <p className="text-green-700 text-sm mt-1">
              Duration: {formatDuration(callDuration)}
            </p>
          </div>
          
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <span className="font-medium text-blue-900">Progress</span>
            </div>
            <p className="text-blue-700 text-sm mt-1">
              {currentStep + 1}/{currentScript?.steps.length} steps completed
            </p>
          </div>
          
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <span className="font-medium text-yellow-900">Next Steps</span>
            </div>
            <p className="text-yellow-700 text-sm mt-1">
              Follow-up scheduled
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default AIVoiceAgent;