name: SSH & VPS Management

on:
  workflow_dispatch:
    inputs:
      action:
        description: 'VPS Management Action'
        required: true
        type: choice
        options:
        - setup-ssh-keys
        - rotate-ssh-keys
        - security-audit
        - system-update
        - backup-system
        - monitor-resources
        - configure-firewall
        - install-dokploy
        - ssl-renewal
      backup_type:
        description: 'Backup type (for backup-system action)'
        required: false
        type: choice
        options:
        - full
        - incremental
        - database-only
        default: 'incremental'
  schedule:
    # Daily security audit at 3 AM UTC
    - cron: '0 3 * * *'
    # Weekly system update on Sundays at 4 AM UTC
    - cron: '0 4 * * 0'

env:
  VPS_HOST: '**************'
  VPS_PORT: '2222'
  VPS_USER: 'admin'
  DOMAIN: 'www.sorteoestelar.com'
  STAGING_DOMAIN: 'staging.sorteoestelar.com'
  MONITORING_DOMAIN: 'monitoring.sorteoestelar.com'
  DOKPLOY_PORT: '3000'

jobs:
  setup-ssh-connection:
    runs-on: ubuntu-latest
    outputs:
      connection-status: ${{ steps.test.outputs.status }}
      server-info: ${{ steps.info.outputs.info }}
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Test SSH connection
        id: test
        run: |
          if ssh -o ConnectTimeout=10 -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} 'echo "Connection successful"'; then
            echo "status=success" >> $GITHUB_OUTPUT
          else
            echo "status=failed" >> $GITHUB_OUTPUT
            exit 1
          fi

      - name: Gather server information
        id: info
        run: |
          INFO=$(ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "OS: $(lsb_release -d | cut -f2)"
            echo "Kernel: $(uname -r)"
            echo "Uptime: $(uptime -p)"
            echo "CPU: $(nproc) cores"
            echo "Memory: $(free -h | grep Mem | awk "{print \$2}")"
            echo "Disk: $(df -h / | tail -1 | awk "{print \$2}")"
          ')
          echo "info<<EOF" >> $GITHUB_OUTPUT
          echo "$INFO" >> $GITHUB_OUTPUT
          echo "EOF" >> $GITHUB_OUTPUT

  ssh-key-management:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'setup-ssh-keys' || github.event.inputs.action == 'rotate-ssh-keys'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Configure SSH security
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Configuring SSH Security ==="
            
            # Backup current SSH config
            sudo cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup.$(date +%Y%m%d-%H%M%S)
            
            # Configure SSH hardening
            sudo tee /etc/ssh/sshd_config.d/99-security.conf > /dev/null <<EOF
            # SSH Security Configuration for Sorteo Estelar
            Port ${{ env.VPS_PORT }}
            Protocol 2
            
            # Authentication
            PermitRootLogin no
            PasswordAuthentication no
            PubkeyAuthentication yes
            AuthorizedKeysFile .ssh/authorized_keys
            
            # Security settings
            PermitEmptyPasswords no
            ChallengeResponseAuthentication no
            UsePAM yes
            X11Forwarding no
            PrintMotd no
            
            # Connection settings
            ClientAliveInterval 300
            ClientAliveCountMax 2
            MaxAuthTries 3
            MaxSessions 2
            LoginGraceTime 60
            
            # Allowed users
            AllowUsers admin
            
            # Logging
            SyslogFacility AUTH
            LogLevel VERBOSE
            EOF
            
            # Test SSH configuration
            sudo sshd -t
            
            if [ $? -eq 0 ]; then
              echo "✅ SSH configuration is valid"
              sudo systemctl reload sshd
              echo "✅ SSH service reloaded"
            else
              echo "❌ SSH configuration is invalid"
              exit 1
            fi
          '

      - name: Setup SSH key rotation
        if: github.event.inputs.action == 'rotate-ssh-keys'
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Rotating SSH Keys ==="
            
            # Backup current authorized_keys
            cp ~/.ssh/authorized_keys ~/.ssh/authorized_keys.backup.$(date +%Y%m%d-%H%M%S)
            
            # Add new key (this should be updated in GitHub secrets)
            echo "${{ secrets.VPS_SSH_PUBLIC_KEY_NEW }}" >> ~/.ssh/authorized_keys
            
            # Remove duplicate entries
            sort ~/.ssh/authorized_keys | uniq > ~/.ssh/authorized_keys.tmp
            mv ~/.ssh/authorized_keys.tmp ~/.ssh/authorized_keys
            
            # Set proper permissions
            chmod 600 ~/.ssh/authorized_keys
            
            echo "✅ SSH keys rotated successfully"
            echo "📝 Remember to update GitHub secrets with the new private key"
          '

  security-audit:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'security-audit' || github.event_name == 'schedule'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Perform security audit
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Security Audit Report ==="
            echo "Timestamp: $(date -Iseconds)"
            echo ""
            
            echo "🔐 SSH Security:"
            echo "   SSH Port: $(sudo ss -tlnp | grep sshd | awk "{print \$4}" | cut -d: -f2)"
            echo "   Failed login attempts (last 24h): $(sudo journalctl -u ssh --since "24 hours ago" | grep "Failed password" | wc -l)"
            echo "   Active SSH sessions: $(who | wc -l)"
            echo ""
            
            echo "🔥 Firewall Status:"
            sudo ufw status verbose
            echo ""
            
            echo "🚪 Open Ports:"
            sudo ss -tlnp | grep LISTEN
            echo ""
            
            echo "👥 User Accounts:"
            echo "   Total users: $(cat /etc/passwd | wc -l)"
            echo "   Users with shell access: $(grep -E "/bin/(bash|sh|zsh)" /etc/passwd | wc -l)"
            echo "   Sudo users: $(getent group sudo | cut -d: -f4)"
            echo ""
            
            echo "📦 System Updates:"
            sudo apt list --upgradable 2>/dev/null | grep -v "WARNING" | wc -l | xargs echo "   Available updates:"
            echo ""
            
            echo "🐳 Docker Security:"
            if command -v docker >/dev/null 2>&1; then
              echo "   Docker version: $(docker --version | cut -d" " -f3 | cut -d"," -f1)"
              echo "   Running containers: $(docker ps -q | wc -l)"
              echo "   Docker daemon accessible: $(ls -la /var/run/docker.sock)"
            else
              echo "   Docker not installed"
            fi
            echo ""
            
            echo "📊 Resource Usage:"
            echo "   CPU usage: $(top -bn1 | grep "Cpu(s)" | awk "{print \$2}" | cut -d"%" -f1)%"
            echo "   Memory usage: $(free | grep Mem | awk "{printf \"%.1f%%\", \$3/\$2 * 100.0}")"
            echo "   Disk usage: $(df / | tail -1 | awk "{print \$5}")"
            echo ""
            
            echo "🔍 Security Scan Results:"
            # Check for suspicious processes
            SUSPICIOUS_PROCS=$(ps aux | grep -E "(nc|netcat|nmap|tcpdump)" | grep -v grep | wc -l)
            echo "   Suspicious processes: $SUSPICIOUS_PROCS"
            
            # Check for unusual network connections
            EXTERNAL_CONNECTIONS=$(ss -tn | grep ESTAB | grep -v "127.0.0.1\|::1" | wc -l)
            echo "   External connections: $EXTERNAL_CONNECTIONS"
            
            # Check system integrity
            if command -v aide >/dev/null 2>&1; then
              echo "   AIDE integrity checker: Installed"
            else
              echo "   AIDE integrity checker: Not installed"
            fi
          '

  system-maintenance:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'system-update' || (github.event_name == 'schedule' && github.event.schedule == '0 4 * * 0')
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Perform system updates
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== System Maintenance ==="
            
            # Update package lists
            echo "📦 Updating package lists..."
            sudo apt update
            
            # Show available updates
            echo "📋 Available updates:"
            apt list --upgradable 2>/dev/null | grep -v "WARNING"
            
            # Perform security updates
            echo "🔒 Installing security updates..."
            sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y
            
            # Clean up
            echo "🧹 Cleaning up..."
            sudo apt autoremove -y
            sudo apt autoclean
            
            # Update Docker if installed
            if command -v docker >/dev/null 2>&1; then
              echo "🐳 Updating Docker images..."
              docker system prune -f
            fi
            
            # Check if reboot is required
            if [ -f /var/run/reboot-required ]; then
              echo "⚠️  System reboot required after updates"
              echo "reboot-required=true" >> $GITHUB_OUTPUT
            else
              echo "✅ No reboot required"
              echo "reboot-required=false" >> $GITHUB_OUTPUT
            fi
            
            echo "✅ System maintenance completed"
          '

  backup-system:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'backup-system'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Create system backup
        env:
          BACKUP_TYPE: ${{ github.event.inputs.backup_type || 'incremental' }}
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Creating System Backup ==="
            echo "Backup type: '$BACKUP_TYPE'"
            
            # Create backup directory
            BACKUP_DIR="/opt/sorteo-estelar/backups/$(date +%Y%m%d-%H%M%S)"
            sudo mkdir -p "$BACKUP_DIR"
            
            case "'$BACKUP_TYPE'" in
              "full")
                echo "📦 Creating full system backup..."
                
                # Backup application data
                sudo tar -czf "$BACKUP_DIR/application.tar.gz" /opt/sorteo-estelar --exclude="/opt/sorteo-estelar/backups"
                
                # Backup system configuration
                sudo tar -czf "$BACKUP_DIR/system-config.tar.gz" /etc/nginx /etc/ssl /etc/ssh
                
                # Backup Docker data
                if command -v docker >/dev/null 2>&1; then
                  sudo tar -czf "$BACKUP_DIR/docker-data.tar.gz" /var/lib/docker/volumes
                fi
                ;;
                
              "database-only")
                echo "🗄️  Creating database backup..."
                
                # Backup PostgreSQL
                if docker ps | grep -q postgres; then
                  docker exec sorteo-estelar-postgres pg_dumpall -U postgres > "$BACKUP_DIR/postgres-dump.sql"
                  gzip "$BACKUP_DIR/postgres-dump.sql"
                fi
                
                # Backup Redis
                if docker ps | grep -q redis; then
                  docker exec sorteo-estelar-redis redis-cli BGSAVE
                  docker cp sorteo-estelar-redis:/data/dump.rdb "$BACKUP_DIR/redis-dump.rdb"
                fi
                ;;
                
              "incremental")
                echo "📈 Creating incremental backup..."
                
                # Find last backup for incremental
                LAST_BACKUP=$(find /opt/sorteo-estelar/backups -name "*.tar.gz" -type f -printf "%T@ %p\n" | sort -n | tail -1 | cut -d" " -f2)
                
                if [ -n "$LAST_BACKUP" ]; then
                  echo "Last backup: $LAST_BACKUP"
                  sudo tar -czf "$BACKUP_DIR/incremental.tar.gz" --newer-mtime="$(stat -c %y "$LAST_BACKUP")" /opt/sorteo-estelar --exclude="/opt/sorteo-estelar/backups"
                else
                  echo "No previous backup found, creating full backup"
                  sudo tar -czf "$BACKUP_DIR/full.tar.gz" /opt/sorteo-estelar --exclude="/opt/sorteo-estelar/backups"
                fi
                ;;
            esac
            
            # Create backup manifest
            cat > "$BACKUP_DIR/manifest.txt" <<EOF
            Backup Information
            ==================
            Date: $(date -Iseconds)
            Type: '$BACKUP_TYPE'
            Host: $(hostname)
            User: $(whoami)
            
            Files:
            $(ls -la "$BACKUP_DIR")
            
            System Info:
            OS: $(lsb_release -d | cut -f2)
            Kernel: $(uname -r)
            Uptime: $(uptime -p)
            EOF
            
            # Set permissions
            sudo chown -R admin:admin "$BACKUP_DIR"
            
            # Calculate backup size
            BACKUP_SIZE=$(du -sh "$BACKUP_DIR" | cut -f1)
            echo "✅ Backup completed: $BACKUP_SIZE"
            echo "📁 Backup location: $BACKUP_DIR"
            
            # Cleanup old backups (keep last 7 days)
            find /opt/sorteo-estelar/backups -type d -mtime +7 -exec rm -rf {} + 2>/dev/null || true
            
            echo "🧹 Old backups cleaned up"
          '

  dokploy-management:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'install-dokploy'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Install and configure Dokploy
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Installing Dokploy ==="
            
            # Install Docker if not present
            if ! command -v docker >/dev/null 2>&1; then
              echo "🐳 Installing Docker..."
              curl -fsSL https://get.docker.com -o get-docker.sh
              sudo sh get-docker.sh
              sudo usermod -aG docker admin
              sudo systemctl enable docker
              sudo systemctl start docker
            fi
            
            # Install Docker Compose if not present
            if ! command -v docker-compose >/dev/null 2>&1; then
              echo "🔧 Installing Docker Compose..."
              sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
              sudo chmod +x /usr/local/bin/docker-compose
            fi
            
            # Create Dokploy directory
            sudo mkdir -p /opt/dokploy
            cd /opt/dokploy
            
            # Download Dokploy
            echo "📥 Downloading Dokploy..."
            curl -sSL https://dokploy.com/install.sh | sh
            
            # Configure Dokploy
            echo "⚙️  Configuring Dokploy..."
            
            # Create Dokploy configuration
            cat > docker-compose.yml <<EOF
            version: "3.8"
            services:
              dokploy:
                image: dokploy/dokploy:latest
                restart: unless-stopped
                ports:
                  - "${{ env.DOKPLOY_PORT }}:3000"
                volumes:
                  - /var/run/docker.sock:/var/run/docker.sock
                  - dokploy_data:/app/data
                environment:
                  - NODE_ENV=production
                  - DATABASE_URL=sqlite:///app/data/dokploy.db
                  - SECRET_KEY=${{ secrets.DOKPLOY_SECRET_KEY }}
                  - DOMAIN=${{ env.DOMAIN }}
                networks:
                  - dokploy
            
            volumes:
              dokploy_data:
            
            networks:
              dokploy:
                driver: bridge
            EOF
            
            # Start Dokploy
            echo "🚀 Starting Dokploy..."
            docker-compose up -d
            
            # Wait for Dokploy to start
            echo "⏳ Waiting for Dokploy to start..."
            sleep 30
            
            # Check Dokploy status
            if curl -f http://localhost:${{ env.DOKPLOY_PORT }}/health >/dev/null 2>&1; then
              echo "✅ Dokploy is running successfully"
              echo "🌐 Access Dokploy at: http://${{ env.VPS_HOST }}:${{ env.DOKPLOY_PORT }}"
            else
              echo "❌ Dokploy failed to start"
              docker-compose logs
              exit 1
            fi
            
            # Configure firewall for Dokploy
            sudo ufw allow ${{ env.DOKPLOY_PORT }}/tcp comment "Dokploy"
            
            echo "✅ Dokploy installation completed"
          '

  ssl-management:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'ssl-renewal'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Manage SSL certificates
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== SSL Certificate Management ==="
            
            # Install Certbot if not present
            if ! command -v certbot >/dev/null 2>&1; then
              echo "📜 Installing Certbot..."
              sudo apt update
              sudo apt install -y certbot python3-certbot-nginx
            fi
            
            # Check current certificates
            echo "📋 Current certificates:"
            sudo certbot certificates
            
            # Renew certificates
            echo "🔄 Renewing SSL certificates..."
            sudo certbot renew --dry-run
            
            if [ $? -eq 0 ]; then
              echo "✅ SSL certificate renewal test successful"
              sudo certbot renew
            else
              echo "❌ SSL certificate renewal test failed"
              exit 1
            fi
            
            # Setup auto-renewal
            echo "⚙️  Setting up auto-renewal..."
            (crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -
            
            echo "✅ SSL management completed"
          '

  monitoring-setup:
    runs-on: ubuntu-latest
    needs: setup-ssh-connection
    if: github.event.inputs.action == 'monitor-resources'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Monitor system resources
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== System Resource Monitoring ==="
            echo "Timestamp: $(date -Iseconds)"
            echo ""
            
            echo "💻 CPU Information:"
            lscpu | grep -E "Model name|CPU\(s\):|Thread|Core"
            echo "Current load: $(uptime | awk -F"load average:" "{print \$2}")"
            echo ""
            
            echo "🧠 Memory Usage:"
            free -h
            echo ""
            
            echo "💾 Disk Usage:"
            df -h
            echo ""
            
            echo "🌐 Network Statistics:"
            ss -tuln | head -10
            echo ""
            
            echo "🔥 Top Processes:"
            ps aux --sort=-%cpu | head -10
            echo ""
            
            echo "🐳 Docker Resources:"
            if command -v docker >/dev/null 2>&1; then
              docker system df
              echo ""
              docker stats --no-stream
            else
              echo "Docker not installed"
            fi
            
            echo ""
            echo "📊 System Health Summary:"
            
            # CPU usage
            CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk "{print \$2}" | cut -d"%" -f1)
            echo "CPU Usage: ${CPU_USAGE}%"
            
            # Memory usage
            MEM_USAGE=$(free | grep Mem | awk "{printf \"%.1f\", \$3/\$2 * 100.0}")
            echo "Memory Usage: ${MEM_USAGE}%"
            
            # Disk usage
            DISK_USAGE=$(df / | tail -1 | awk "{print \$5}" | cut -d"%" -f1)
            echo "Disk Usage: ${DISK_USAGE}%"
            
            # Load average
            LOAD_AVG=$(uptime | awk -F"load average:" "{print \$2}" | awk "{print \$1}" | cut -d"," -f1)
            echo "Load Average: ${LOAD_AVG}"
            
            # Health check
            if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
              echo "⚠️  High CPU usage detected!"
            fi
            
            if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
              echo "⚠️  High memory usage detected!"
            fi
            
            if [ "$DISK_USAGE" -gt 80 ]; then
              echo "⚠️  High disk usage detected!"
            fi
            
            echo "✅ Resource monitoring completed"
          '

  report-status:
    runs-on: ubuntu-latest
    needs: [setup-ssh-connection, security-audit, system-maintenance, backup-system, dokploy-management, ssl-management, monitoring-setup]
    if: always()
    steps:
      - name: Generate status report
        run: |
          echo "=== VPS Management Status Report ==="
          echo "Timestamp: $(date -Iseconds)"
          echo "Triggered by: ${{ github.actor }}"
          echo "Action: ${{ github.event.inputs.action || github.event_name }}"
          echo ""
          echo "📊 Job Results:"
          echo "   SSH Connection: ${{ needs.setup-ssh-connection.result }}"
          echo "   Security Audit: ${{ needs.security-audit.result || 'skipped' }}"
          echo "   System Maintenance: ${{ needs.system-maintenance.result || 'skipped' }}"
          echo "   Backup System: ${{ needs.backup-system.result || 'skipped' }}"
          echo "   Dokploy Management: ${{ needs.dokploy-management.result || 'skipped' }}"
          echo "   SSL Management: ${{ needs.ssl-management.result || 'skipped' }}"
          echo "   Resource Monitoring: ${{ needs.monitoring-setup.result || 'skipped' }}"
          echo ""
          echo "🖥️  Server Information:"
          echo "${{ needs.setup-ssh-connection.outputs.server-info }}"
          
          # Determine overall status
          if [ "${{ needs.setup-ssh-connection.result }}" == "success" ]; then
            echo "✅ VPS management operations completed successfully"
          else
            echo "❌ VPS management operations failed"
            exit 1
          fi