import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { authenticateToken, requirePermission } from '@/middleware/auth';
import { WorkflowService, Workflow } from '@/services/workflowService';

// Request/Response Schemas
const createWorkflowSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  type: z.enum(['sequential', 'parallel', 'conditional', 'loop']).default('sequential'),
  steps: z.array(z.object({
    name: z.string(),
    type: z.enum(['agent_task', 'condition', 'loop', 'parallel', 'webhook', 'delay']),
    agentId: z.string().optional(),
    taskDefinition: z.any().optional(),
    condition: z.string().optional(),
    dependencies: z.array(z.string()).default([])
  })).default([]),
  variables: z.record(z.any()).default({}),
  metadata: z.record(z.any()).optional()
});

const updateWorkflowSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  type: z.enum(['sequential', 'parallel', 'conditional', 'loop']).optional(),
  steps: z.array(z.object({
    id: z.string().optional(),
    name: z.string(),
    type: z.enum(['agent_task', 'condition', 'loop', 'parallel', 'webhook', 'delay']),
    agentId: z.string().optional(),
    taskDefinition: z.any().optional(),
    condition: z.string().optional(),
    dependencies: z.array(z.string()).default([])
  })).optional(),
  variables: z.record(z.any()).optional(),
  metadata: z.record(z.any()).optional(),
  status: z.enum(['draft', 'active', 'running', 'completed', 'failed', 'paused']).optional()
});

const executeWorkflowSchema = z.object({
  variables: z.record(z.any()).optional()
});

const workflowQuerySchema = z.object({
  status: z.enum(['draft', 'active', 'running', 'completed', 'failed', 'paused']).optional(),
  type: z.enum(['sequential', 'parallel', 'conditional', 'loop']).optional(),
  createdBy: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0)
});

interface WorkflowRouteContext {
  workflowService: WorkflowService;
}

export async function workflowRoutes(fastify: FastifyInstance) {
  const { workflowService } = fastify as any as WorkflowRouteContext;

  // Get all workflows
  fastify.get('/', {
    preHandler: [authenticateToken, requirePermission('workflows:read')],
    schema: {
      querystring: workflowQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  steps: { type: 'array' },
                  variables: { type: 'object' },
                  metadata: { type: 'object' },
                  createdBy: { type: 'string' },
                  organizationId: { type: 'string' },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = workflowQuerySchema.parse(request.query);
      const user = (request as any).user;
      
      const workflows = await workflowService.getWorkflows(user.organizationId);
      
      // Apply filters
      let filteredWorkflows = workflows;
      
      if (query.status) {
        filteredWorkflows = filteredWorkflows.filter(w => w.status === query.status);
      }
      
      if (query.type) {
        filteredWorkflows = filteredWorkflows.filter(w => w.type === query.type);
      }
      
      if (query.createdBy) {
        filteredWorkflows = filteredWorkflows.filter(w => w.createdBy === query.createdBy);
      }
      
      // Apply pagination
      const total = filteredWorkflows.length;
      const paginatedWorkflows = filteredWorkflows.slice(query.offset, query.offset + query.limit);
      
      return reply.send({
        success: true,
        data: paginatedWorkflows,
        pagination: {
          total,
          limit: query.limit,
          offset: query.offset
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve workflows'
      });
    }
  });

  // Get workflow by ID
  fastify.get('/:id', {
    preHandler: [authenticateToken, requirePermission('workflows:read')],
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                steps: { type: 'array' },
                variables: { type: 'object' },
                metadata: { type: 'object' },
                createdBy: { type: 'string' },
                organizationId: { type: 'string' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
              }
            }
          }
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const user = (request as any).user;
      
      const workflow = await workflowService.getWorkflow(id, user.organizationId);
      
      if (!workflow) {
        return reply.status(404).send({
          success: false,
          error: 'Workflow not found'
        });
      }
      
      return reply.send({
        success: true,
        data: workflow
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve workflow'
      });
    }
  });

  // Create new workflow
  fastify.post('/', {
    preHandler: [authenticateToken, requirePermission('workflows:create')],
    schema: {
      body: createWorkflowSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                steps: { type: 'array' },
                variables: { type: 'object' },
                createdBy: { type: 'string' },
                organizationId: { type: 'string' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const workflowData = createWorkflowSchema.parse(request.body);
      const user = (request as any).user;
      
      const workflow = await workflowService.createWorkflow({
        ...workflowData,
        createdBy: user.id,
        organizationId: user.organizationId
      });
      
      return reply.status(201).send({
        success: true,
        data: workflow
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create workflow'
      });
    }
  });

  // Execute workflow
  fastify.post('/:id/execute', {
    preHandler: [authenticateToken, requirePermission('workflows:execute')],
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      body: executeWorkflowSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                workflowId: { type: 'string' },
                status: { type: 'string' },
                variables: { type: 'object' },
                results: { type: 'object' },
                startedAt: { type: 'string' }
              }
            }
          }
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const { variables } = executeWorkflowSchema.parse(request.body);
      const user = (request as any).user;
      
      // Check if workflow exists and user has access
      const workflow = await workflowService.getWorkflow(id, user.organizationId);
      if (!workflow) {
        return reply.status(404).send({
          success: false,
          error: 'Workflow not found'
        });
      }
      
      const execution = await workflowService.executeWorkflow(id, variables);
      
      return reply.send({
        success: true,
        data: execution
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to execute workflow'
      });
    }
  });

  // Get workflow metrics
  fastify.get('/metrics', {
    preHandler: [authenticateToken, requirePermission('workflows:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                running: { type: 'number' },
                completed: { type: 'number' },
                failed: { type: 'number' },
                averageExecutionTime: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metrics = await workflowService.getMetrics();
      
      return reply.send({
        success: true,
        data: metrics
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve workflow metrics'
      });
    }
  });
}