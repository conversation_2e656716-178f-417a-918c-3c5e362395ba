import { v4 as uuidv4 } from 'uuid';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';

export interface Agent {
  id: string;
  name: string;
  type: 'task_executor' | 'workflow_coordinator' | 'data_processor' | 'decision_maker' | 'monitor';
  role: string;
  goal: string;
  backstory?: string;
  capabilities: string[];
  tools: string[];
  status: 'active' | 'inactive' | 'maintenance' | 'busy' | 'idle';
  maxConcurrentTasks: number;
  currentTasks: string[];
  timeoutMs: number;
  retryAttempts: number;
  priority: number;
  metrics: AgentMetrics;
  metadata?: Record<string, any>;
  createdBy: string;
  organizationId?: string;
  createdAt: Date;
  updatedAt: Date;
  lastActive?: Date;
}

export interface CreateAgentData {
  name: string;
  type: Agent['type'];
  role: string;
  goal: string;
  backstory?: string;
  capabilities?: string[];
  tools?: string[];
  maxConcurrentTasks?: number;
  timeoutMs?: number;
  retryAttempts?: number;
  priority?: number;
  metadata?: Record<string, any>;
  createdBy: string;
  organizationId?: string;
}

export interface UpdateAgentData {
  name?: string;
  role?: string;
  goal?: string;
  backstory?: string;
  capabilities?: string[];
  tools?: string[];
  maxConcurrentTasks?: number;
  timeoutMs?: number;
  retryAttempts?: number;
  priority?: number;
  status?: Agent['status'];
  metadata?: Record<string, any>;
  updatedBy: string;
  organizationId?: string;
}

export interface AgentQuery {
  page?: number;
  limit?: number;
  type?: string;
  status?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  organizationId?: string;
}

export interface AgentMetrics {
  tasksCompleted: number;
  tasksFailed: number;
  averageExecutionTime: number;
  successRate: number;
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
  lastUpdated: Date;
}

export interface AgentLog {
  id: string;
  agentId: string;
  level: 'error' | 'warn' | 'info' | 'debug';
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export class AgentService {
  private agents: Map<string, Agent> = new Map();
  private agentLogs: Map<string, AgentLog[]> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Agent Service...');
      
      // Generate mock agents for development
      if (environment.nodeEnv === 'development') {
        await this.generateMockAgents();
      }
      
      this.isInitialized = true;
      logger.info('Agent Service initialized successfully');
    } catch (error) {
      logger.error(error, 'Failed to initialize Agent Service');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up Agent Service...');
      
      // Stop all active agents
      for (const agent of this.agents.values()) {
        if (agent.status === 'active' || agent.status === 'busy') {
          await this.stopAgent(agent.id);
        }
      }
      
      this.agents.clear();
      this.agentLogs.clear();
      this.isInitialized = false;
      
      logger.info('Agent Service cleanup completed');
    } catch (error) {
      logger.error(error, 'Error during Agent Service cleanup');
      throw error;
    }
  }

  async getStatus(): Promise<string> {
    if (!this.isInitialized) {
      return 'initializing';
    }
    
    const activeAgents = Array.from(this.agents.values()).filter(a => a.status === 'active').length;
    const totalAgents = this.agents.size;
    
    if (totalAgents === 0) {
      return 'healthy';
    }
    
    return activeAgents > 0 ? 'healthy' : 'degraded';
  }

  async getMetrics(): Promise<{ activeAgents: number; totalAgents: number; busyAgents: number }> {
    const agents = Array.from(this.agents.values());
    
    return {
      activeAgents: agents.filter(a => a.status === 'active').length,
      totalAgents: agents.length,
      busyAgents: agents.filter(a => a.status === 'busy').length
    };
  }

  async createAgent(data: CreateAgentData): Promise<Agent> {
    const agent: Agent = {
      id: uuidv4(),
      name: data.name,
      type: data.type,
      role: data.role,
      goal: data.goal,
      backstory: data.backstory,
      capabilities: data.capabilities || [],
      tools: data.tools || [],
      status: 'inactive',
      maxConcurrentTasks: data.maxConcurrentTasks || 3,
      currentTasks: [],
      timeoutMs: data.timeoutMs || environment.orchestration.agentTimeoutMs,
      retryAttempts: data.retryAttempts || 3,
      priority: data.priority || 5,
      metrics: {
        tasksCompleted: 0,
        tasksFailed: 0,
        averageExecutionTime: 0,
        successRate: 0,
        uptime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        lastUpdated: new Date()
      },
      metadata: data.metadata,
      createdBy: data.createdBy,
      organizationId: data.organizationId,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.agents.set(agent.id, agent);
    this.agentLogs.set(agent.id, []);
    
    this.logAgentEvent(agent.id, 'info', `Agent '${agent.name}' created`);
    
    logger.info(`Agent created: ${agent.id} (${agent.name})`);
    return agent;
  }

  async getAgents(query: AgentQuery): Promise<{ agents: Agent[]; pagination: any }> {
    let agents = Array.from(this.agents.values());
    
    // Filter by organization
    if (query.organizationId) {
      agents = agents.filter(a => a.organizationId === query.organizationId);
    }
    
    // Filter by type
    if (query.type) {
      agents = agents.filter(a => a.type === query.type);
    }
    
    // Filter by status
    if (query.status) {
      agents = agents.filter(a => a.status === query.status);
    }
    
    // Search filter
    if (query.search) {
      const searchLower = query.search.toLowerCase();
      agents = agents.filter(a => 
        a.name.toLowerCase().includes(searchLower) ||
        a.role.toLowerCase().includes(searchLower) ||
        a.goal.toLowerCase().includes(searchLower)
      );
    }
    
    // Sort
    const sortBy = query.sortBy || 'createdAt';
    const sortOrder = query.sortOrder || 'desc';
    
    agents.sort((a, b) => {
      let aVal: any, bVal: any;
      
      switch (sortBy) {
        case 'name':
          aVal = a.name;
          bVal = b.name;
          break;
        case 'type':
          aVal = a.type;
          bVal = b.type;
          break;
        case 'lastActive':
          aVal = a.lastActive || new Date(0);
          bVal = b.lastActive || new Date(0);
          break;
        case 'priority':
          aVal = a.priority;
          bVal = b.priority;
          break;
        default:
          aVal = a.createdAt;
          bVal = b.createdAt;
      }
      
      if (sortOrder === 'asc') {
        return aVal > bVal ? 1 : -1;
      } else {
        return aVal < bVal ? 1 : -1;
      }
    });
    
    // Pagination
    const page = query.page || 1;
    const limit = query.limit || 20;
    const offset = (page - 1) * limit;
    const total = agents.length;
    const pages = Math.ceil(total / limit);
    
    const paginatedAgents = agents.slice(offset, offset + limit);
    
    return {
      agents: paginatedAgents.map(agent => ({
        id: agent.id,
        name: agent.name,
        type: agent.type,
        status: agent.status,
        currentTasks: agent.currentTasks.length,
        totalTasksCompleted: agent.metrics.tasksCompleted,
        lastActive: agent.lastActive,
        createdAt: agent.createdAt
      })) as any,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    };
  }

  async getAgent(id: string, organizationId?: string): Promise<Agent | null> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      return null;
    }
    
    if (organizationId && agent.organizationId !== organizationId) {
      return null;
    }
    
    return agent;
  }

  async updateAgent(id: string, data: UpdateAgentData): Promise<Agent> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (data.organizationId && agent.organizationId !== data.organizationId) {
      throw new Error('Agent not found');
    }
    
    // Update agent properties
    Object.assign(agent, {
      ...data,
      updatedAt: new Date()
    });
    
    this.agents.set(id, agent);
    this.logAgentEvent(id, 'info', `Agent '${agent.name}' updated`);
    
    logger.info(`Agent updated: ${id} (${agent.name})`);
    return agent;
  }

  async deleteAgent(id: string, organizationId?: string): Promise<void> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (organizationId && agent.organizationId !== organizationId) {
      throw new Error('Agent not found');
    }
    
    // Stop agent if active
    if (agent.status === 'active' || agent.status === 'busy') {
      await this.stopAgent(id);
    }
    
    this.agents.delete(id);
    this.agentLogs.delete(id);
    
    logger.info(`Agent deleted: ${id} (${agent.name})`);
  }

  async startAgent(id: string, organizationId?: string): Promise<void> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (organizationId && agent.organizationId !== organizationId) {
      throw new Error('Agent not found');
    }
    
    if (agent.status === 'active') {
      throw new Error('Agent is already active');
    }
    
    agent.status = 'active';
    agent.lastActive = new Date();
    agent.updatedAt = new Date();
    
    this.agents.set(id, agent);
    this.logAgentEvent(id, 'info', `Agent '${agent.name}' started`);
    
    logger.info(`Agent started: ${id} (${agent.name})`);
  }

  async stopAgent(id: string, organizationId?: string): Promise<void> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (organizationId && agent.organizationId !== organizationId) {
      throw new Error('Agent not found');
    }
    
    if (agent.status === 'inactive') {
      throw new Error('Agent is already inactive');
    }
    
    agent.status = 'inactive';
    agent.currentTasks = [];
    agent.updatedAt = new Date();
    
    this.agents.set(id, agent);
    this.logAgentEvent(id, 'info', `Agent '${agent.name}' stopped`);
    
    logger.info(`Agent stopped: ${id} (${agent.name})`);
  }

  async getAgentMetrics(id: string, options: { timeRange?: string; organizationId?: string }): Promise<AgentMetrics> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (options.organizationId && agent.organizationId !== options.organizationId) {
      throw new Error('Agent not found');
    }
    
    // In a real implementation, this would query time-series data
    // For now, return current metrics
    return agent.metrics;
  }

  async getAgentLogs(id: string, options: any): Promise<{ logs: AgentLog[]; pagination: any }> {
    const agent = this.agents.get(id);
    
    if (!agent) {
      throw new Error('Agent not found');
    }
    
    if (options.organizationId && agent.organizationId !== options.organizationId) {
      throw new Error('Agent not found');
    }
    
    let logs = this.agentLogs.get(id) || [];
    
    // Filter by level
    if (options.level) {
      logs = logs.filter(log => log.level === options.level);
    }
    
    // Filter by date range
    if (options.startDate) {
      const startDate = new Date(options.startDate);
      logs = logs.filter(log => log.timestamp >= startDate);
    }
    
    if (options.endDate) {
      const endDate = new Date(options.endDate);
      logs = logs.filter(log => log.timestamp <= endDate);
    }
    
    // Sort by timestamp (newest first)
    logs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    
    // Pagination
    const page = options.page || 1;
    const limit = options.limit || 50;
    const offset = (page - 1) * limit;
    const total = logs.length;
    const pages = Math.ceil(total / limit);
    
    const paginatedLogs = logs.slice(offset, offset + limit);
    
    return {
      logs: paginatedLogs,
      pagination: {
        page,
        limit,
        total,
        pages
      }
    };
  }

  private async generateMockAgents(): Promise<void> {
    const mockAgents: CreateAgentData[] = [
      {
        name: 'Task Executor Alpha',
        type: 'task_executor',
        role: 'Primary task execution agent',
        goal: 'Execute assigned tasks efficiently and accurately',
        backstory: 'Specialized in handling various computational tasks with high reliability',
        capabilities: ['task_execution', 'data_processing', 'error_handling'],
        tools: ['file_processor', 'api_client', 'data_validator'],
        maxConcurrentTasks: 5,
        priority: 8,
        createdBy: 'system'
      },
      {
        name: 'Workflow Coordinator Beta',
        type: 'workflow_coordinator',
        role: 'Workflow orchestration and coordination',
        goal: 'Coordinate complex workflows and manage agent interactions',
        backstory: 'Expert in managing multi-agent workflows and ensuring smooth coordination',
        capabilities: ['workflow_management', 'agent_coordination', 'resource_allocation'],
        tools: ['workflow_engine', 'agent_manager', 'resource_monitor'],
        maxConcurrentTasks: 3,
        priority: 9,
        createdBy: 'system'
      },
      {
        name: 'Data Processor Gamma',
        type: 'data_processor',
        role: 'Data analysis and processing specialist',
        goal: 'Process and analyze large datasets with high accuracy',
        backstory: 'Specialized in data transformation, analysis, and insight generation',
        capabilities: ['data_analysis', 'statistical_processing', 'pattern_recognition'],
        tools: ['data_analyzer', 'statistical_engine', 'visualization_tool'],
        maxConcurrentTasks: 4,
        priority: 7,
        createdBy: 'system'
      }
    ];

    for (const agentData of mockAgents) {
      await this.createAgent(agentData);
    }

    logger.info(`Generated ${mockAgents.length} mock agents`);
  }

  private logAgentEvent(agentId: string, level: AgentLog['level'], message: string, metadata?: Record<string, any>): void {
    const log: AgentLog = {
      id: uuidv4(),
      agentId,
      level,
      message,
      timestamp: new Date(),
      metadata
    };

    const logs = this.agentLogs.get(agentId) || [];
    logs.push(log);
    
    // Keep only last 1000 logs per agent
    if (logs.length > 1000) {
      logs.splice(0, logs.length - 1000);
    }
    
    this.agentLogs.set(agentId, logs);
  }
}