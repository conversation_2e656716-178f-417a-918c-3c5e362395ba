import { toast } from 'sonner';

export enum ErrorType {
  RUNTIME = 'runtime',
  COMPILATION = 'compilation',
  NETWORK = 'network',
  AUTHENTICATION = 'authentication',
  VALIDATION = 'validation',
  BLOCKCHAIN = 'blockchain',
  API = 'api'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorReport {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  stack?: string;
  timestamp: Date;
  url: string;
  userAgent: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export interface HealthMetrics {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  recentErrors: ErrorReport[];
  uptime: number;
  lastErrorTime?: Date;
}

class ErrorMonitoringService {
  private errors: ErrorReport[] = [];
  private maxErrors = 1000;
  private startTime = Date.now();
  private webhookUrl?: string;

  constructor() {
    this.setupGlobalErrorHandlers();
    this.webhookUrl = import.meta.env.VITE_ERROR_WEBHOOK_URL;
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled JavaScript errors
    window.addEventListener('error', (event) => {
      this.captureError({
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.HIGH,
        message: event.message,
        stack: event.error?.stack,
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      });
    });

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError({
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.HIGH,
        message: `Unhandled Promise Rejection: ${event.reason}`,
        stack: event.reason?.stack,
        metadata: {
          reason: event.reason
        }
      });
    });

    // Handle network errors
    window.addEventListener('offline', () => {
      this.captureError({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Network connection lost',
        metadata: {
          online: navigator.onLine
        }
      });
    });
  }

  captureError(errorData: Omit<ErrorReport, 'id' | 'timestamp' | 'url' | 'userAgent'>) {
    const error: ErrorReport = {
      id: this.generateErrorId(),
      timestamp: new Date(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      ...errorData
    };

    this.errors.unshift(error);
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Show user notification for critical errors
    if (error.severity === ErrorSeverity.CRITICAL) {
      toast.error(`Critical Error: ${error.message}`, {
        duration: 10000,
        action: {
          label: 'Report',
          onClick: () => this.reportError(error)
        }
      });
    } else if (error.severity === ErrorSeverity.HIGH) {
      toast.error(`Error: ${error.message}`, {
        duration: 5000
      });
    }

    // Send to monitoring service
    this.sendToMonitoring(error);

    console.error('Error captured:', error);
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private async sendToMonitoring(error: ErrorReport) {
    if (!this.webhookUrl) return;

    try {
      await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: `🚨 Error Alert - ${error.severity.toUpperCase()}`,
          attachments: [{
            color: this.getSeverityColor(error.severity),
            fields: [
              { title: 'Type', value: error.type, short: true },
              { title: 'Severity', value: error.severity, short: true },
              { title: 'Message', value: error.message, short: false },
              { title: 'URL', value: error.url, short: false },
              { title: 'Timestamp', value: error.timestamp.toISOString(), short: true },
              { title: 'User Agent', value: error.userAgent, short: false }
            ]
          }]
        })
      });
    } catch (err) {
      console.error('Failed to send error to monitoring service:', err);
    }
  }

  private getSeverityColor(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return '#ff0000';
      case ErrorSeverity.HIGH: return '#ff6600';
      case ErrorSeverity.MEDIUM: return '#ffcc00';
      case ErrorSeverity.LOW: return '#00ff00';
      default: return '#cccccc';
    }
  }

  private reportError(error: ErrorReport) {
    // Open a modal or redirect to error reporting page
    const reportUrl = `mailto:<EMAIL>?subject=Error Report: ${error.id}&body=${encodeURIComponent(
      `Error ID: ${error.id}\n` +
      `Type: ${error.type}\n` +
      `Severity: ${error.severity}\n` +
      `Message: ${error.message}\n` +
      `URL: ${error.url}\n` +
      `Timestamp: ${error.timestamp.toISOString()}\n` +
      `Stack: ${error.stack || 'N/A'}\n` +
      `Metadata: ${JSON.stringify(error.metadata, null, 2)}`
    )}`;
    
    window.open(reportUrl, '_blank');
  }

  getHealthReport(): HealthMetrics {
    const now = Date.now();
    const uptime = Math.floor((now - this.startTime) / 1000);
    
    const errorsByType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = this.errors.filter(e => e.type === type).length;
      return acc;
    }, {} as Record<ErrorType, number>);

    const errorsBySeverity = Object.values(ErrorSeverity).reduce((acc, severity) => {
      acc[severity] = this.errors.filter(e => e.severity === severity).length;
      return acc;
    }, {} as Record<ErrorSeverity, number>);

    return {
      totalErrors: this.errors.length,
      errorsByType,
      errorsBySeverity,
      recentErrors: this.errors.slice(0, 10),
      uptime,
      lastErrorTime: this.errors[0]?.timestamp
    };
  }

  getErrors(filter?: { type?: ErrorType; severity?: ErrorSeverity; limit?: number }): ErrorReport[] {
    let filteredErrors = this.errors;

    if (filter?.type) {
      filteredErrors = filteredErrors.filter(e => e.type === filter.type);
    }

    if (filter?.severity) {
      filteredErrors = filteredErrors.filter(e => e.severity === filter.severity);
    }

    if (filter?.limit) {
      filteredErrors = filteredErrors.slice(0, filter.limit);
    }

    return filteredErrors;
  }

  clearErrors() {
    this.errors = [];
  }

  // Network monitoring
  async checkEndpointHealth(url: string): Promise<{ healthy: boolean; responseTime: number; error?: string }> {
    const startTime = performance.now();
    
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const responseTime = performance.now() - startTime;
      
      if (!response.ok) {
        this.captureError({
          type: ErrorType.API,
          severity: ErrorSeverity.MEDIUM,
          message: `Endpoint health check failed: ${url}`,
          metadata: {
            status: response.status,
            statusText: response.statusText,
            responseTime
          }
        });
        
        return {
          healthy: false,
          responseTime,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
      
      return {
        healthy: true,
        responseTime
      };
    } catch (error) {
      const responseTime = performance.now() - startTime;
      
      this.captureError({
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.HIGH,
        message: `Network error checking endpoint: ${url}`,
        metadata: {
          error: error instanceof Error ? error.message : String(error),
          responseTime
        }
      });
      
      return {
        healthy: false,
        responseTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  // Performance monitoring
  measurePerformance(name: string, fn: () => Promise<any> | any) {
    const startTime = performance.now();
    
    try {
      const result = fn();
      
      if (result instanceof Promise) {
        return result.finally(() => {
          const duration = performance.now() - startTime;
          console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
          
          if (duration > 5000) { // Alert for operations taking more than 5 seconds
            this.captureError({
              type: ErrorType.RUNTIME,
              severity: ErrorSeverity.MEDIUM,
              message: `Slow operation detected: ${name}`,
              metadata: {
                duration,
                operation: name
              }
            });
          }
        });
      } else {
        const duration = performance.now() - startTime;
        console.log(`Performance: ${name} took ${duration.toFixed(2)}ms`);
        return result;
      }
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.captureError({
        type: ErrorType.RUNTIME,
        severity: ErrorSeverity.HIGH,
        message: `Operation failed: ${name}`,
        stack: error instanceof Error ? error.stack : undefined,
        metadata: {
          duration,
          operation: name,
          error: error instanceof Error ? error.message : String(error)
        }
      });
      
      throw error;
    }
  }
}

export const errorMonitoring = new ErrorMonitoringService();

// Export utility functions
export const captureError = (errorData: Omit<ErrorReport, 'id' | 'timestamp' | 'url' | 'userAgent'>) => {
  errorMonitoring.captureError(errorData);
};

export const measurePerformance = (name: string, fn: () => any) => {
  return errorMonitoring.measurePerformance(name, fn);
};

export const checkEndpointHealth = (url: string) => {
  return errorMonitoring.checkEndpointHealth(url);
};

export default errorMonitoring;