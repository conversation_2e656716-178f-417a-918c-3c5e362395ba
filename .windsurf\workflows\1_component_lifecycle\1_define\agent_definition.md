---
description: Guides the generation of agent definitions from ESTRATIX value chain processes to build the agentic layer of the digital twin.
---

# Workflow: Define ESTRATIX Agent

## Objective

To produce a comprehensive and standardized definition document for a new ESTRATIX Agent, ensuring it is atomically registered in the `agent_matrix.md` and stored in the correct directory: `docs/agents/[Owner_Office_Code]/`.

## Agent Persona

`AGENT_Architect_Expert`

## Steps

1. **Initiation & Information Gathering**
   - **Action**: An agent or user identifies the need for a new agent.
   - **Input**: `Agent Name`, `Owner_Office_Code` (e.g., "cto"), `Role`, `Goal`, `Backstory`, and a list of required tools.
   - **Verification**: Check `docs/models/agent_matrix.md` to ensure a similar agent does not already exist.

2. **Register Agent & Reserve ID (Atomic Operation)**
   - **Action (Automated)**: An agent programmatically reserves a unique ID and registers the agent.
   - **Logic**:
     1. Read `docs/models/agent_matrix.md`.
     2. Find the last entry with the same office prefix (e.g., `cto_a005`).
     3. Increment the numeric part to generate the new `Agent_ID` (e.g., `cto_a006`).
     4. Add a new row to the matrix with the new `Agent_ID`, `Agent Name`, `Owner_Office_Code`, a placeholder `Role`, and set the `Status` to `Pending Definition`.
     5. Save the updated `docs/models/agent_matrix.md`.
   - **Output**: The newly reserved `Agent_ID` (e.g., `cto_a006`).

3. **Create Agent Definition File from Template**
   - **Action (Automated)**:
     1. Create the target directory: `docs/agents/[Owner_Office_Code]/[Agent_ID]_[Agent_Name_PascalCase]/`.
     2. Copy the template `docs/templates/estratix_agent_definition_template.md` to the target file: `docs/agents/[Owner_Office_Code]/[Agent_ID]_[Agent_Name_PascalCase]/[Agent_ID]_[Agent_Name_PascalCase]_Definition.md`.
     3. Replace placeholders like `[Agent_ID]` and `[AgentName]` in the new file.
   - **Tooling**: File system operations, text replacement.

4. **Populate Agent Definition**
   - **Action (User/Agent)**: Open the newly created definition file and populate all sections (Role, Goal, Backstory, Tools, etc.).

5. **Review and Finalize**
   - **Action**: Review the populated definition for clarity, completeness, and alignment with the agent's intended purpose.
   - **Input**: Feedback from the `Owner_Office_Code` Command Officer.
   - **Output**: A finalized definition document.

6. **Update Matrix with Definition Link**
   - **Action (Automated)**: Upon finalization, an agent updates the agent's entry in the matrix.
   - **Logic**:
     1. Open `docs/models/agent_matrix.md`.
     2. Locate the row corresponding to the `Agent_ID`.
     3. Update the `Role` and other relevant fields from the final definition.
     4. Set the `Definition Link` to point to the new definition file.
     5. Update the `Status` from `Pending Definition` to `Defined`.
   - **Tooling**: Markdown table manipulation script.

7. **Confirmation & Next Steps**
   - **Action**: Confirm that the agent has been defined and registered.
   - **Output**: Provide a direct link to the new definition file and suggest running the `/agent_generation` workflow.
