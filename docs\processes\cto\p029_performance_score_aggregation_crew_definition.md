# ESTRATIX Process Definition: p029 - Performance Score Aggregation Crew

## 1. Process Overview

- **Process ID:** p029
- **Process Name:** Performance Score Aggregation Crew
- **Responsible Command Office:** CTO
- **Version:** 1.0
- **Status:** Defined

## 2. Process Description

This process takes the raw performance data collected by `p028` and transforms it into meaningful Key Performance Indicators (KPIs) and a final, weighted performance score. It is the analytical core of the performance evaluation flow, applying the business logic defined in the incentive framework.

## 3. Crew Composition (Conceptual)

| Agent Role | Agent ID | Key Responsibilities | Tools |
|---|---|---|---|
| Score Aggregation Orchestrator | CTO_AXXX | Manages the calculation workflow and applies the final weighting formula. | `t_cto_p029_kpi_calculator`, `t_cto_p029_weighting_engine` |
| KPI Calculation Agent | CTO_AXXX | Executes the specific formulas for each KPI category (Operational, Strategic, Technical, Learning). | `t_cto_p029_kpi_calculator` |

## 4. Process Workflow

1.  **Initiation:** Triggered by `f014` upon successful completion of `p028`, receiving the `Raw Performance Data Record`.
2.  **KPI Calculation:** The KPI Calculation Agent processes the raw data, calculating individual scores for each KPI defined in the framework.
3.  **Score Weighting:** The orchestrator applies the predefined weights to each KPI score.
4.  **Final Score Aggregation:** A final, overall performance score is computed.
5.  **Output:** The process outputs a `Calculated Performance Score Record`, including the overall score and the breakdown by KPI.

## 5. Inputs & Outputs

- **Primary Input:** `Raw Performance Data Record`
- **Primary Output:** `Calculated Performance Score Record`

## 6. Dependencies

- **Flow:** `f014 - Agent Performance Evaluation Flow`
- **Process:** `p028 - KPI Data Collection Crew`
- **Standard:** `ESTRATIX Agent Performance & Incentive Framework`
- **Tools:** `t_cto_p029_*` series of calculation tools.
