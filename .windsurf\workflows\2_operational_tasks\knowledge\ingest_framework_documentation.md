---
description: "Defines the standard ESTRATIX workflow for ingesting framework documentation into a vector database to build a knowledge base for expert agents."
---

# Workflow: Ingest Framework Documentation

**Purpose:** This workflow outlines the standard, high-level procedure for ingesting external documentation into a designated vector database. It serves as the master operational guide and is implemented by the `CIO_F001_DocumentationIngestion` flow.

**Governing Process:** `CIO_P001_KnowledgeIngestion`

**Orchestrating Flow:** `CIO_F001_DocumentationIngestion`

## Overview

The ingestion process is a fully agent-driven pipeline orchestrated by the `CIO_F001` flow. The flow is triggered by new entries in the `docs/matrices/source_matrix.md` and leverages a crew of specialist agents to execute the steps defined in the `CIO_P001` process.

## High-Level Steps

The detailed, step-by-step execution logic is encapsulated within the `CIO_F001_DocumentationIngestion` flow. The key phases are:

1. **Source Identification & Triggering:** A user or agent identifies a documentation source and adds it to the `source_matrix.md`. This action triggers the `CIO_F001` flow.
2. **Content Acquisition:** The flow delegates to the appropriate acquisition agent (`CTO_A002` for web, `CTO_A003` for PDF) to fetch the raw content.
3. **Content Processing:** The `CTO_A004_ContentProcessingSpecialist` cleans and chunks the raw content.
4. **Embedding Generation:** The `CIO_A001_EmbeddingAgent` converts the processed text chunks into vector embeddings.
5. **Vector Database Loading:** The `CIO_A002_VectorDBLoaderAgent` loads the embeddings and metadata into the target vector database (specified in `vector_db_matrix.md`).
6. **Logging & Completion:** The flow updates the `source_matrix.md` and `ingestion_log_matrix.md` to reflect the outcome of the operation.

## Key Components

- **Process Definition:** `docs/processes/cio/CIO_P001_KnowledgeIngestion_Definition.md`
- **Flow Definition:** `docs/flows/cio/CIO_F001_DocumentationIngestion_Definition.md`
- **Agent Definitions:**
  - `docs/agents/cto/CTO_A002_WebScrapingSpecialist.md`
  - `docs/agents/cto/CTO_A003_PDFProcessingSpecialist.md`
  - `docs/agents/cto/CTO_A004_ContentProcessingSpecialist.md`
  - `docs/agents/cio/CIO_A001_EmbeddingAgent_Definition.md`
  - `docs/agents/cio/CIO_A002_VectorDBLoaderAgent_Definition.md`

## Guidance for Use

- To initiate this workflow, add a new entry to the `docs/matrices/source_matrix.md`.
- The workflow is designed to be automated. Monitor the `ingestion_log_matrix.md` for status updates and results.
- Configuration for target databases and embedding models is managed through their respective matrices (`vector_db_matrix.md`, `llm_matrix.md`).
