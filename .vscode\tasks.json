{"version": "2.0.0", "tasks": [{"type": "func", "label": "func: host start", "command": "host start", "problemMatcher": "$func-node-watch", "isBackground": true, "dependsOn": "npm watch (functions)", "options": {"cwd": "${workspaceFolder}/src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions"}}, {"type": "shell", "label": "npm build (functions)", "command": "npm run build", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc", "options": {"cwd": "${workspaceFolder}/src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions"}}, {"type": "shell", "label": "npm watch (functions)", "command": "npm run watch", "dependsOn": "npm install (functions)", "problemMatcher": "$tsc-watch", "group": {"kind": "build", "isDefault": true}, "isBackground": true, "options": {"cwd": "${workspaceFolder}/src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions"}}, {"type": "shell", "label": "npm install (functions)", "command": "npm install", "options": {"cwd": "${workspaceFolder}/src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions"}}, {"type": "shell", "label": "npm prune (functions)", "command": "npm prune --production", "dependsOn": "npm build (functions)", "problemMatcher": [], "options": {"cwd": "${workspaceFolder}/src\\domain\\server\\typescript-puppeteer-real-browser-azure-functions"}}]}