import { getDefaultConfig } from '@rainbow-me/rainbowkit';
import { mainnet, polygon, optimism, arbitrum, base, sepolia, polygonMumbai, type Chain } from 'wagmi/chains';
import { createPublicClient, createWalletClient, custom, http } from 'viem';

// Environment variables with fallbacks
const projectId = import.meta.env.VITE_WALLETCONNECT_PROJECT_ID || 'luxcrafts-default-project-id';
const alchemyApiKey = import.meta.env.VITE_ALCHEMY_API_KEY;
const infuraApiKey = import.meta.env.VITE_INFURA_API_KEY;

// Ensure we have a valid project ID for production
if (!projectId || projectId === 'your_walletconnect_project_id_here') {
  console.warn('WalletConnect Project ID not configured properly');
}

// Chains configuration
const chains: readonly [Chain, ...Chain[]] = [
  mainnet,
  optimism,
  arbitrum,
  polygon,
  base,
  sepolia,
  polygonMumbai
] as const;

// Wagmi configuration
export const config = getDefaultConfig({
  appName: 'Luxcrafts Property Services Platform',
  projectId,
  chains,
  ssr: false,
});

// Contract addresses
export const CONTRACT_ADDRESSES = {
  LUX_TOKEN: import.meta.env.VITE_LUX_TOKEN_ADDRESS || '0x...',
  PROPERTY_NFT: import.meta.env.VITE_PROPERTY_NFT_ADDRESS || '0x...',
  STAKING_CONTRACT: import.meta.env.VITE_STAKING_CONTRACT_ADDRESS || '0x...',
  MARKETPLACE: import.meta.env.VITE_MARKETPLACE_CONTRACT_ADDRESS || '0x...',
  GOVERNANCE: import.meta.env.VITE_GOVERNANCE_CONTRACT_ADDRESS || '0x...',
  LENDING_POOL: import.meta.env.VITE_LENDING_POOL_ADDRESS || '0x...',
  LIQUIDITY_POOL: import.meta.env.VITE_LIQUIDITY_POOL_ADDRESS || '0x...',
  PROPERTY_TOKENIZATION: import.meta.env.VITE_PROPERTY_TOKENIZATION_ADDRESS || '0x...',
  YIELD_FARMING: import.meta.env.VITE_YIELD_FARMING_ADDRESS || '0x...',
  INSURANCE_POOL: import.meta.env.VITE_INSURANCE_POOL_ADDRESS || '0x...',
} as const;

// Token configuration
export const TOKEN_CONFIG = {
  LUX: {
    symbol: 'LUX',
    name: 'Luxcrafts Token',
    decimals: 18,
    totalSupply: BigInt(import.meta.env.VITE_LUX_INITIAL_SUPPLY || '10000000') * BigInt(10 ** 18),
    maxSupply: BigInt(import.meta.env.VITE_LUX_MAX_SUPPLY || '100000000') * BigInt(10 ** 18),
  },
  STAKING_POOLS: [
    {
      id: 'flexible',
      name: 'Flexible Staking',
      apy: 8.5,
      lockPeriod: 0,
      minStake: BigInt(10) * BigInt(10 ** 18),
      maxStake: BigInt(1000000) * BigInt(10 ** 18),
    },
    {
      id: 'lock30',
      name: '30-Day Lock',
      apy: 12.5,
      lockPeriod: 30 * 24 * 60 * 60, // 30 days in seconds
      minStake: BigInt(50) * BigInt(10 ** 18),
      maxStake: BigInt(1000000) * BigInt(10 ** 18),
    },
    {
      id: 'lock90',
      name: '90-Day Lock',
      apy: 18.0,
      lockPeriod: 90 * 24 * 60 * 60, // 90 days in seconds
      minStake: BigInt(100) * BigInt(10 ** 18),
      maxStake: BigInt(1000000) * BigInt(10 ** 18),
    },
    {
      id: 'property_yield',
      name: 'Property Yield Pool',
      apy: 15.2,
      lockPeriod: 60 * 24 * 60 * 60, // 60 days in seconds
      minStake: BigInt(200) * BigInt(10 ** 18),
      maxStake: BigInt(1000000) * BigInt(10 ** 18),
    },
  ],
} as const;

// DeFi protocol configuration
export const DEFI_CONFIG = {
  LENDING: {
    maxLTV: 0.75, // 75% loan-to-value ratio
    liquidationThreshold: 0.85,
    interestRateModel: 'compound',
    supportedCollateral: ['ETH', 'WBTC', 'USDC', 'LUX', 'PROPERTY_NFT'],
  },
  LIQUIDITY_MINING: {
    pools: [
      { pair: 'LUX/ETH', weight: 40, apy: 24.5 },
      { pair: 'LUX/USDC', weight: 30, apy: 18.2 },
      { pair: 'LUX/WBTC', weight: 20, apy: 21.8 },
      { pair: 'PROPERTY/ETH', weight: 10, apy: 32.1 },
    ],
  },
  YIELD_FARMING: {
    strategies: [
      { name: 'Conservative', risk: 'low', expectedAPY: 8.5 },
      { name: 'Balanced', risk: 'medium', expectedAPY: 15.2 },
      { name: 'Aggressive', risk: 'high', expectedAPY: 32.1 },
    ],
  },
} as const;

// Property tokenization configuration
export const PROPERTY_CONFIG = {
  TOKENIZATION: {
    minPropertyValue: BigInt(100000) * BigInt(10 ** 6), // $100k in USDC
    maxPropertyValue: BigInt(50000000) * BigInt(10 ** 6), // $50M in USDC
    fractionSize: BigInt(1000), // 1000 fractions per property
    managementFee: 0.02, // 2% annual management fee
    performanceFee: 0.20, // 20% of profits
  },
  RENTAL_YIELD: {
    distributionFrequency: 30 * 24 * 60 * 60, // Monthly in seconds
    minimumYield: 0.03, // 3% annual yield
    maximumYield: 0.12, // 12% annual yield
  },
} as const;

// Governance configuration
export const GOVERNANCE_CONFIG = {
  PROPOSAL_THRESHOLD: BigInt(100000) * BigInt(10 ** 18), // 100k LUX tokens
  VOTING_PERIOD: 7 * 24 * 60 * 60, // 7 days in seconds
  EXECUTION_DELAY: 2 * 24 * 60 * 60, // 2 days in seconds
  QUORUM: 0.04, // 4% of total supply
  PROPOSAL_TYPES: [
    'PARAMETER_CHANGE',
    'TREASURY_ALLOCATION',
    'PROTOCOL_UPGRADE',
    'EMERGENCY_ACTION',
    'PARTNERSHIP_APPROVAL',
  ],
} as const;

// Public client for reading blockchain data
export const publicClient = createPublicClient({
  chain: mainnet,
  transport: http(alchemyApiKey 
    ? `https://eth-mainnet.g.alchemy.com/v2/${alchemyApiKey}`
    : 'https://ethereum.publicnode.com'
  ),
});

// Utility functions
export const formatTokenAmount = (amount: bigint, decimals: number = 18): string => {
  return (Number(amount) / Math.pow(10, decimals)).toFixed(4);
};

export const parseTokenAmount = (amount: string, decimals: number = 18): bigint => {
  return BigInt(Math.floor(parseFloat(amount) * Math.pow(10, decimals)));
};

export const getChainConfig = (chainId: number) => {
  return chains.find(chain => chain.id === chainId) || mainnet;
};

export const isTestnet = (chainId: number): boolean => {
  const testnetIds: number[] = [sepolia.id, polygonMumbai.id];
  return testnetIds.includes(chainId);
};