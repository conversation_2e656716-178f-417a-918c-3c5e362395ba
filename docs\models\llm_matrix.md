# ESTRATIX LLM Model Matrix

---

## 1. Overview

This matrix inventories and categorizes the Large Language Models (LLMs) available for use within the ESTRATIX ecosystem. It serves as a central registry for model capabilities, access methods, and recommended use cases.

## 2. LLM Model Matrix

| Model ID | Model Name | Provider/Source | Modality | Key Capabilities | Access Point (API/SDK) | Status | Notes |
|---|---|---|---|---|---|---|---|
| LLM-GPT-004 | GPT-4 | OpenAI | Text, Vision | Advanced reasoning, code generation, image analysis | `openai.ChatCompletion` | Active | Primary model for complex tasks. |
| LLM-CLA-003 | Claude 3 Opus | Anthropic | Text, Vision | Long context, high accuracy, document analysis | `anthropic.messages` | Active | Excels at summarization and Q&A over large documents. |
| | | | | | | | |

## 3. Guidance for Use

- **Selection**: Agents and developers should consult this matrix to select the most appropriate model for a given task based on its capabilities, cost, and performance.
- **Integration**: Use the specified access points for integration. Refer to the relevant tool or SDK documentation for implementation details.
- **Updates**: This matrix should be updated whenever a new model is onboarded or an existing model's status changes.
