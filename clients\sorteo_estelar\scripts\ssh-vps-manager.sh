#!/bin/bash

# SSH and VPS Management Script for Sorteo Estelar
# Handles SSH key management, VPS administration, and security

set -euo pipefail

# Configuration
VPS_HOST="**************"
VPS_PORT="2222"
VPS_USER="admin"
PROJECT_NAME="sorteo-estelar"
SSH_KEY_PATH="$HOME/.ssh/sorteo_estelar_rsa"
SSH_CONFIG_PATH="$HOME/.ssh/config"
BACKUP_DIR="/opt/sorteo-estelar/backups"
LOG_DIR="/opt/sorteo-estelar/logs"
MONITORING_DIR="/opt/sorteo-estelar/monitoring"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    logger -t "ssh-vps-manager" "INFO: $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    logger -t "ssh-vps-manager" "SUCCESS: $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    logger -t "ssh-vps-manager" "WARNING: $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    logger -t "ssh-vps-manager" "ERROR: $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
    logger -t "ssh-vps-manager" "DEBUG: $1"
}

# Generate SSH key pair
generate_ssh_key() {
    local key_comment="${1:-sorteo-estelar-$(date +%Y%m%d)}"
    
    log_info "Generating SSH key pair..."
    
    # Create .ssh directory if it doesn't exist
    mkdir -p "$(dirname "$SSH_KEY_PATH")"
    
    # Generate key pair
    ssh-keygen -t rsa -b 4096 -f "$SSH_KEY_PATH" -C "$key_comment" -N ""
    
    # Set proper permissions
    chmod 600 "$SSH_KEY_PATH"
    chmod 644 "${SSH_KEY_PATH}.pub"
    
    log_success "SSH key pair generated: $SSH_KEY_PATH"
    
    # Display public key
    echo -e "\n${CYAN}Public Key:${NC}"
    cat "${SSH_KEY_PATH}.pub"
    echo
}

# Setup SSH configuration
setup_ssh_config() {
    log_info "Setting up SSH configuration..."
    
    # Backup existing config
    if [ -f "$SSH_CONFIG_PATH" ]; then
        cp "$SSH_CONFIG_PATH" "${SSH_CONFIG_PATH}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Create SSH config entry
    cat >> "$SSH_CONFIG_PATH" <<EOF

# Sorteo Estelar VPS Configuration
Host sorteo-estelar-vps
    HostName $VPS_HOST
    Port $VPS_PORT
    User $VPS_USER
    IdentityFile $SSH_KEY_PATH
    IdentitiesOnly yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10
    StrictHostKeyChecking yes
    UserKnownHostsFile ~/.ssh/known_hosts
    LogLevel INFO
    
# Sorteo Estelar VPS with Agent Forwarding
Host sorteo-estelar-vps-agent
    HostName $VPS_HOST
    Port $VPS_PORT
    User $VPS_USER
    IdentityFile $SSH_KEY_PATH
    IdentitiesOnly yes
    ForwardAgent yes
    ServerAliveInterval 60
    ServerAliveCountMax 3
    ConnectTimeout 10
    StrictHostKeyChecking yes
    UserKnownHostsFile ~/.ssh/known_hosts
    LogLevel INFO
EOF
    
    # Set proper permissions
    chmod 600 "$SSH_CONFIG_PATH"
    
    log_success "SSH configuration updated"
}

# Install public key on VPS
install_public_key() {
    log_info "Installing public key on VPS..."
    
    if [ ! -f "${SSH_KEY_PATH}.pub" ]; then
        log_error "Public key not found: ${SSH_KEY_PATH}.pub"
        log_info "Please generate SSH key first using: $0 generate-key"
        return 1
    fi
    
    # Copy public key to VPS
    ssh-copy-id -i "${SSH_KEY_PATH}.pub" -p "$VPS_PORT" "$VPS_USER@$VPS_HOST"
    
    log_success "Public key installed on VPS"
}

# Test SSH connection
test_ssh_connection() {
    log_info "Testing SSH connection to VPS..."
    
    if ssh -o ConnectTimeout=10 -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" 'echo "SSH connection successful"'; then
        log_success "SSH connection test passed"
        return 0
    else
        log_error "SSH connection test failed"
        return 1
    fi
}

# Rotate SSH keys
rotate_ssh_keys() {
    log_info "Rotating SSH keys..."
    
    # Backup current keys
    local backup_suffix=".backup.$(date +%Y%m%d_%H%M%S)"
    if [ -f "$SSH_KEY_PATH" ]; then
        cp "$SSH_KEY_PATH" "${SSH_KEY_PATH}${backup_suffix}"
        cp "${SSH_KEY_PATH}.pub" "${SSH_KEY_PATH}.pub${backup_suffix}"
        log_info "Current keys backed up"
    fi
    
    # Generate new keys
    generate_ssh_key "sorteo-estelar-rotated-$(date +%Y%m%d)"
    
    # Install new public key
    install_public_key
    
    # Test new connection
    if test_ssh_connection; then
        log_success "SSH key rotation completed successfully"
        
        # Remove old keys from VPS
        log_info "Cleaning up old keys on VPS..."
        ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
            # Remove old keys from authorized_keys
            if [ -f ~/.ssh/authorized_keys ]; then
                # Keep only the last 2 keys (current and previous)
                tail -n 2 ~/.ssh/authorized_keys > ~/.ssh/authorized_keys.tmp
                mv ~/.ssh/authorized_keys.tmp ~/.ssh/authorized_keys
                chmod 600 ~/.ssh/authorized_keys
                echo "Old SSH keys cleaned up"
            fi
EOF
        
    else
        log_error "SSH key rotation failed, restoring backup"
        if [ -f "${SSH_KEY_PATH}${backup_suffix}" ]; then
            mv "${SSH_KEY_PATH}${backup_suffix}" "$SSH_KEY_PATH"
            mv "${SSH_KEY_PATH}.pub${backup_suffix}" "${SSH_KEY_PATH}.pub"
            log_info "Backup keys restored"
        fi
        return 1
    fi
}

# Perform security audit
security_audit() {
    log_info "Performing security audit on VPS..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== Security Audit Report ==="
        echo "Date: $(date)"
        echo "Hostname: $(hostname)"
        echo "Uptime: $(uptime)"
        echo
        
        echo "=== System Information ==="
        echo "OS: $(lsb_release -d | cut -f2)"
        echo "Kernel: $(uname -r)"
        echo "Architecture: $(uname -m)"
        echo
        
        echo "=== User Accounts ==="
        echo "Active users:"
        who
        echo
        echo "Users with shell access:"
        grep -E '/bin/(bash|sh|zsh)$' /etc/passwd | cut -d: -f1
        echo
        
        echo "=== SSH Configuration ==="
        echo "SSH service status:"
        systemctl is-active ssh || systemctl is-active sshd
        echo
        echo "SSH configuration check:"
        sshd -T | grep -E '^(port|permitrootlogin|passwordauthentication|pubkeyauthentication)'
        echo
        
        echo "=== Firewall Status ==="
        if command -v ufw &> /dev/null; then
            echo "UFW status:"
            sudo ufw status verbose
        elif command -v iptables &> /dev/null; then
            echo "iptables rules:"
            sudo iptables -L -n
        fi
        echo
        
        echo "=== Network Connections ==="
        echo "Listening ports:"
        ss -tlnp | head -20
        echo
        
        echo "=== Failed Login Attempts ==="
        echo "Recent failed SSH attempts:"
        grep "Failed password" /var/log/auth.log | tail -10 || echo "No recent failed attempts"
        echo
        
        echo "=== System Updates ==="
        echo "Available updates:"
        apt list --upgradable 2>/dev/null | wc -l
        echo
        
        echo "=== Disk Usage ==="
        df -h
        echo
        
        echo "=== Memory Usage ==="
        free -h
        echo
        
        echo "=== Running Processes ==="
        echo "Top 10 processes by CPU:"
        ps aux --sort=-%cpu | head -11
        echo
        
        echo "=== Docker Status ==="
        if command -v docker &> /dev/null; then
            echo "Docker service status:"
            systemctl is-active docker
            echo "Running containers:"
            docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        else
            echo "Docker not installed"
        fi
        echo
        
        echo "=== SSL Certificates ==="
        if [ -d "/etc/letsencrypt/live" ]; then
            echo "SSL certificates:"
            for cert_dir in /etc/letsencrypt/live/*/; do
                if [ -d "$cert_dir" ]; then
                    domain=$(basename "$cert_dir")
                    if [ -f "$cert_dir/cert.pem" ]; then
                        expiry=$(openssl x509 -enddate -noout -in "$cert_dir/cert.pem" | cut -d= -f2)
                        echo "  $domain: expires $expiry"
                    fi
                fi
            done
        else
            echo "No SSL certificates found"
        fi
        echo
        
        echo "=== Security Audit Complete ==="
EOF
    
    log_success "Security audit completed"
}

# Update VPS system
update_vps_system() {
    log_info "Updating VPS system..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== System Update Started ==="
        echo "Date: $(date)"
        
        # Update package lists
        echo "Updating package lists..."
        sudo apt update
        
        # Show available upgrades
        echo "Available upgrades:"
        apt list --upgradable
        
        # Perform upgrade
        echo "Performing system upgrade..."
        sudo DEBIAN_FRONTEND=noninteractive apt upgrade -y
        
        # Clean up
        echo "Cleaning up packages..."
        sudo apt autoremove -y
        sudo apt autoclean
        
        # Update Docker if installed
        if command -v docker &> /dev/null; then
            echo "Updating Docker images..."
            docker system prune -f
        fi
        
        # Check if reboot is required
        if [ -f /var/run/reboot-required ]; then
            echo "⚠️  REBOOT REQUIRED"
            echo "Packages requiring reboot:"
            cat /var/run/reboot-required.pkgs 2>/dev/null || echo "Unknown packages"
        else
            echo "✅ No reboot required"
        fi
        
        echo "=== System Update Complete ==="
EOF
    
    log_success "VPS system update completed"
}

# Create system backup
create_backup() {
    local backup_type="${1:-full}"
    
    log_info "Creating $backup_type backup..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << EOF
        set -euo pipefail
        
        BACKUP_DATE=\$(date +"%Y%m%d_%H%M%S")
        BACKUP_NAME="sorteo-estelar-\${backup_type}-\${BACKUP_DATE}"
        BACKUP_PATH="$BACKUP_DIR/\${BACKUP_NAME}"
        
        echo "=== Creating Backup: \$BACKUP_NAME ==="
        
        # Create backup directory
        sudo mkdir -p "$BACKUP_DIR"
        sudo mkdir -p "\$BACKUP_PATH"
        
        # Backup application data
        if [ "$backup_type" = "full" ] || [ "$backup_type" = "data" ]; then
            echo "Backing up application data..."
            if [ -d "/opt/sorteo-estelar" ]; then
                sudo tar -czf "\${BACKUP_PATH}/application-data.tar.gz" -C /opt sorteo-estelar --exclude='*.log' --exclude='node_modules' --exclude='.git'
            fi
        fi
        
        # Backup databases
        if [ "$backup_type" = "full" ] || [ "$backup_type" = "database" ]; then
            echo "Backing up databases..."
            
            # PostgreSQL backup
            if docker ps | grep -q "sorteo-estelar-postgres"; then
                echo "Backing up PostgreSQL..."
                docker exec sorteo-estelar-postgres pg_dumpall -U postgres > "\${BACKUP_PATH}/postgres-backup.sql"
                gzip "\${BACKUP_PATH}/postgres-backup.sql"
            fi
            
            # Redis backup
            if docker ps | grep -q "sorteo-estelar-redis"; then
                echo "Backing up Redis..."
                docker exec sorteo-estelar-redis redis-cli BGSAVE
                sleep 5
                docker cp sorteo-estelar-redis:/data/dump.rdb "\${BACKUP_PATH}/redis-backup.rdb"
            fi
        fi
        
        # Backup system configuration
        if [ "$backup_type" = "full" ] || [ "$backup_type" = "config" ]; then
            echo "Backing up system configuration..."
            sudo tar -czf "\${BACKUP_PATH}/system-config.tar.gz" /etc/nginx /etc/ssl /etc/letsencrypt 2>/dev/null || true
        fi
        
        # Backup Docker volumes
        if [ "$backup_type" = "full" ] || [ "$backup_type" = "volumes" ]; then
            echo "Backing up Docker volumes..."
            if command -v docker &> /dev/null; then
                docker run --rm -v sorteo-estelar_postgres_data:/data -v "\${BACKUP_PATH}:/backup" alpine tar -czf /backup/postgres-volume.tar.gz -C /data .
                docker run --rm -v sorteo-estelar_redis_data:/data -v "\${BACKUP_PATH}:/backup" alpine tar -czf /backup/redis-volume.tar.gz -C /data .
            fi
        fi
        
        # Create backup manifest
        cat > "\${BACKUP_PATH}/manifest.txt" <<MANIFEST
Backup Information
==================
Backup Name: \$BACKUP_NAME
Backup Type: $backup_type
Created: \$(date)
Hostname: \$(hostname)
Files:
MANIFEST
        
        ls -la "\${BACKUP_PATH}/" >> "\${BACKUP_PATH}/manifest.txt"
        
        # Calculate backup size
        BACKUP_SIZE=\$(du -sh "\${BACKUP_PATH}" | cut -f1)
        echo "Backup Size: \$BACKUP_SIZE" >> "\${BACKUP_PATH}/manifest.txt"
        
        # Set permissions
        sudo chown -R admin:admin "\${BACKUP_PATH}"
        
        echo "✅ Backup created: \${BACKUP_PATH}"
        echo "📦 Backup size: \$BACKUP_SIZE"
        
        # Clean up old backups (keep last 7 days)
        echo "Cleaning up old backups..."
        find "$BACKUP_DIR" -type d -name "sorteo-estelar-*" -mtime +7 -exec rm -rf {} + 2>/dev/null || true
        
        echo "=== Backup Complete ==="
EOF
    
    log_success "Backup created successfully"
}

# Monitor VPS resources
monitor_resources() {
    log_info "Monitoring VPS resources..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== Resource Monitoring Report ==="
        echo "Date: $(date)"
        echo "Hostname: $(hostname)"
        echo
        
        # CPU Usage
        echo "=== CPU Usage ==="
        echo "Load Average: $(uptime | awk -F'load average:' '{print $2}')"
        echo "CPU Info:"
        lscpu | grep -E '^CPU\(s\)|^Model name|^CPU MHz'
        echo
        
        # Memory Usage
        echo "=== Memory Usage ==="
        free -h
        echo
        echo "Memory usage percentage:"
        free | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'
        echo
        
        # Disk Usage
        echo "=== Disk Usage ==="
        df -h
        echo
        echo "Disk usage alerts (>80%):"
        df -h | awk '$5 > 80 {print $0}'
        echo
        
        # Network Usage
        echo "=== Network Usage ==="
        echo "Network interfaces:"
        ip -s link show | grep -E '^[0-9]+:|RX:|TX:'
        echo
        
        # Process Information
        echo "=== Top Processes ==="
        echo "By CPU usage:"
        ps aux --sort=-%cpu | head -6
        echo
        echo "By Memory usage:"
        ps aux --sort=-%mem | head -6
        echo
        
        # Docker Resources
        if command -v docker &> /dev/null; then
            echo "=== Docker Resources ==="
            echo "Container status:"
            docker ps --format "table {{.Names}}\t{{.Status}}\t{{.CPUPerc}}\t{{.MemUsage}}"
            echo
            echo "Docker system info:"
            docker system df
            echo
        fi
        
        # Service Status
        echo "=== Service Status ==="
        for service in nginx docker ssh postgresql redis; do
            if systemctl list-unit-files | grep -q "^$service"; then
                status=$(systemctl is-active $service 2>/dev/null || echo "inactive")
                echo "$service: $status"
            fi
        done
        echo
        
        # Log Analysis
        echo "=== Recent Errors ==="
        echo "System errors (last 10):"
        journalctl --since "1 hour ago" --priority=err --no-pager | tail -10 || echo "No recent errors"
        echo
        
        # Security Events
        echo "=== Security Events ==="
        echo "Recent SSH logins:"
        grep "Accepted" /var/log/auth.log | tail -5 || echo "No recent logins"
        echo
        
        echo "=== Monitoring Complete ==="
EOF
    
    log_success "Resource monitoring completed"
}

# Install Dokploy
install_dokploy() {
    log_info "Installing Dokploy on VPS..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== Installing Dokploy ==="
        
        # Check if Docker is installed
        if ! command -v docker &> /dev/null; then
            echo "Installing Docker..."
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
            sudo systemctl enable docker
            sudo systemctl start docker
            rm get-docker.sh
        fi
        
        # Check if Docker Compose is installed
        if ! command -v docker-compose &> /dev/null; then
            echo "Installing Docker Compose..."
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        fi
        
        # Create Dokploy directory
        sudo mkdir -p /opt/dokploy
        cd /opt/dokploy
        
        # Download Dokploy
        echo "Downloading Dokploy..."
        curl -sSL https://dokploy.com/install.sh | sh
        
        # Wait for Dokploy to start
        echo "Waiting for Dokploy to start..."
        sleep 30
        
        # Check if Dokploy is running
        if curl -f http://localhost:3000/health &> /dev/null; then
            echo "✅ Dokploy installed and running successfully"
            echo "🌐 Access Dokploy at: http://$(curl -s ifconfig.me):3000"
        else
            echo "❌ Dokploy installation failed"
            docker logs dokploy || true
        fi
        
        echo "=== Dokploy Installation Complete ==="
EOF
    
    log_success "Dokploy installation completed"
}

# Renew SSL certificates
renew_ssl_certificates() {
    log_info "Renewing SSL certificates..."
    
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        set -euo pipefail
        
        echo "=== SSL Certificate Renewal ==="
        
        if command -v certbot &> /dev/null; then
            echo "Checking certificate status..."
            sudo certbot certificates
            
            echo "Attempting certificate renewal..."
            sudo certbot renew --dry-run
            
            if [ $? -eq 0 ]; then
                echo "Dry run successful, performing actual renewal..."
                sudo certbot renew
                
                # Reload nginx if certificates were renewed
                if sudo nginx -t; then
                    sudo systemctl reload nginx
                    echo "✅ Nginx reloaded with new certificates"
                else
                    echo "❌ Nginx configuration test failed"
                fi
            else
                echo "❌ Certificate renewal dry run failed"
            fi
        else
            echo "❌ Certbot not installed"
        fi
        
        echo "=== SSL Certificate Renewal Complete ==="
EOF
    
    log_success "SSL certificate renewal completed"
}

# Show VPS status
show_vps_status() {
    log_info "Checking VPS status..."
    
    echo -e "\n${CYAN}=== VPS Connection Status ===${NC}"
    if test_ssh_connection; then
        echo -e "${GREEN}✅ SSH Connection: OK${NC}"
    else
        echo -e "${RED}❌ SSH Connection: FAILED${NC}"
        return 1
    fi
    
    echo -e "\n${CYAN}=== VPS Information ===${NC}"
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        echo "Hostname: $(hostname)"
        echo "OS: $(lsb_release -d | cut -f2 2>/dev/null || echo "Unknown")"
        echo "Kernel: $(uname -r)"
        echo "Uptime: $(uptime -p)"
        echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
        echo "Memory: $(free -h | awk 'NR==2{printf "%s/%s (%.2f%%)\n", $3,$2,$3*100/$2}')"
        echo "Disk: $(df -h / | awk 'NR==2{printf "%s/%s (%s)\n", $3,$2,$5}')"
EOF
    
    echo -e "\n${CYAN}=== Service Status ===${NC}"
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        services=("nginx" "docker" "ssh")
        for service in "${services[@]}"; do
            if systemctl list-unit-files | grep -q "^$service"; then
                status=$(systemctl is-active $service 2>/dev/null || echo "inactive")
                if [ "$status" = "active" ]; then
                    echo "✅ $service: $status"
                else
                    echo "❌ $service: $status"
                fi
            fi
        done
EOF
    
    echo -e "\n${CYAN}=== Docker Containers ===${NC}"
    ssh -p "$VPS_PORT" -i "$SSH_KEY_PATH" "$VPS_USER@$VPS_HOST" << 'EOF'
        if command -v docker &> /dev/null; then
            docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>/dev/null || echo "No containers running"
        else
            echo "Docker not installed"
        fi
EOF
}

# Show help
show_help() {
    cat <<EOF
SSH and VPS Management Script for Sorteo Estelar

Usage: $0 <command> [arguments]

SSH Management:
  generate-key [comment]        Generate new SSH key pair
  setup-config                  Setup SSH configuration
  install-key                   Install public key on VPS
  test-connection              Test SSH connection
  rotate-keys                  Rotate SSH keys

VPS Administration:
  security-audit               Perform security audit
  update-system                Update VPS system packages
  monitor-resources            Monitor VPS resources
  status                       Show VPS status

Backup Management:
  backup [type]                Create backup (full|data|database|config|volumes)

Service Management:
  install-dokploy              Install Dokploy
  renew-ssl                    Renew SSL certificates

Configuration:
  VPS Host: $VPS_HOST
  VPS Port: $VPS_PORT
  VPS User: $VPS_USER
  SSH Key: $SSH_KEY_PATH

Examples:
  $0 generate-key "my-sorteo-key"
  $0 setup-config
  $0 install-key
  $0 test-connection
  $0 security-audit
  $0 backup full
  $0 monitor-resources
  $0 status
EOF
}

# Main function
main() {
    local command="${1:-}"
    
    case "$command" in
        "generate-key")
            generate_ssh_key "${2:-}"
            ;;
        "setup-config")
            setup_ssh_config
            ;;
        "install-key")
            install_public_key
            ;;
        "test-connection")
            test_ssh_connection
            ;;
        "rotate-keys")
            rotate_ssh_keys
            ;;
        "security-audit")
            security_audit
            ;;
        "update-system")
            update_vps_system
            ;;
        "monitor-resources")
            monitor_resources
            ;;
        "backup")
            create_backup "${2:-full}"
            ;;
        "install-dokploy")
            install_dokploy
            ;;
        "renew-ssl")
            renew_ssl_certificates
            ;;
        "status")
            show_vps_status
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            log_error "No command specified"
            show_help
            exit 1
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Create necessary directories
mkdir -p "$(dirname "$SSH_KEY_PATH")"

# Run main function with all arguments
main "$@"