import React, { useState, useEffect } from 'react';
import { Camera, Video, Music, Image, Calendar, Zap, Sparkles, Download, Share2, Eye, Heart, MessageCircle, TrendingUp, Palette, Wand2, Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface ContentItem {
  id: string;
  type: 'image' | 'video' | 'audio' | 'carousel' | '3d_scene';
  title: string;
  description: string;
  status: 'generating' | 'ready' | 'published' | 'scheduled';
  thumbnail: string;
  url?: string;
  metadata: {
    dimensions?: string;
    duration?: number;
    fileSize?: string;
    format?: string;
  };
  engagement: {
    views: number;
    likes: number;
    comments: number;
    shares: number;
  };
  scheduledDate?: string;
  platforms: string[];
  tags: string[];
  aiPrompt: string;
  generationTime: number;
}

interface ContentCalendar {
  date: string;
  content: ContentItem[];
  theme: string;
  targetAudience: string;
}

interface GenerationRequest {
  type: 'image' | 'video' | 'audio' | 'carousel' | '3d_scene';
  prompt: string;
  style: string;
  dimensions: string;
  duration?: number;
  mood: string;
  brand: string;
  platforms: string[];
  scheduledDate?: string;
}

const ContentGenerationStudio: React.FC = () => {
  const [contentLibrary, setContentLibrary] = useState<ContentItem[]>([]);
  const [contentCalendar, setContentCalendar] = useState<ContentCalendar[]>([]);
  const [activeTab, setActiveTab] = useState<'generate' | 'library' | 'calendar' | 'analytics'>('generate');
  const [generationRequest, setGenerationRequest] = useState<GenerationRequest>({
    type: 'image',
    prompt: '',
    style: 'photorealistic',
    dimensions: 'square_hd',
    mood: 'professional',
    brand: 'luxcrafts',
    platforms: ['instagram'],
    scheduledDate: ''
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedContent, setSelectedContent] = useState<ContentItem | null>(null);
  const [audioPlaying, setAudioPlaying] = useState<string | null>(null);

  // Mock data initialization
  useEffect(() => {
    const mockContent: ContentItem[] = [
      {
        id: '1',
        type: 'image',
        title: 'Luxury Property Showcase',
        description: 'Stunning aerial view of a modern luxury estate with infinity pool',
        status: 'published',
        thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20modern%20estate%20aerial%20view%20infinity%20pool%20sunset&image_size=square_hd',
        url: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20modern%20estate%20aerial%20view%20infinity%20pool%20sunset&image_size=landscape_16_9',
        metadata: {
          dimensions: '1920x1080',
          fileSize: '2.4 MB',
          format: 'PNG'
        },
        engagement: {
          views: 15420,
          likes: 892,
          comments: 67,
          shares: 134
        },
        platforms: ['instagram', 'facebook', 'linkedin'],
        tags: ['luxury', 'property', 'real-estate', 'modern', 'architecture'],
        aiPrompt: 'Create a stunning aerial photograph of a modern luxury estate featuring an infinity pool overlooking the ocean at golden hour',
        generationTime: 12
      },
      {
        id: '2',
        type: 'video',
        title: 'Property Tour Animation',
        description: '3D animated walkthrough of luxury penthouse',
        status: 'ready',
        thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20interior%203d%20animation%20modern%20design&image_size=square_hd',
        metadata: {
          duration: 45,
          fileSize: '28.7 MB',
          format: 'MP4'
        },
        engagement: {
          views: 8930,
          likes: 456,
          comments: 23,
          shares: 78
        },
        platforms: ['youtube', 'instagram', 'tiktok'],
        tags: ['3d', 'animation', 'penthouse', 'tour', 'luxury'],
        aiPrompt: 'Generate a cinematic 3D animated walkthrough of a luxury penthouse with floor-to-ceiling windows and modern furnishings',
        generationTime: 180
      },
      {
        id: '3',
        type: 'audio',
        title: 'Ambient Property Music',
        description: 'Sophisticated background music for property videos',
        status: 'published',
        thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=abstract%20sound%20waves%20luxury%20gold%20elegant%20music&image_size=square_hd',
        metadata: {
          duration: 120,
          fileSize: '4.2 MB',
          format: 'MP3'
        },
        engagement: {
          views: 3240,
          likes: 187,
          comments: 12,
          shares: 34
        },
        platforms: ['youtube', 'instagram'],
        tags: ['music', 'ambient', 'luxury', 'background'],
        aiPrompt: 'Compose elegant ambient music with piano and strings suitable for luxury real estate presentations',
        generationTime: 45
      }
    ];

    const mockCalendar: ContentCalendar[] = [
      {
        date: '2024-02-25',
        content: [mockContent[0]],
        theme: 'Luxury Lifestyle',
        targetAudience: 'High-net-worth individuals'
      },
      {
        date: '2024-02-26',
        content: [mockContent[1]],
        theme: 'Property Innovation',
        targetAudience: 'Real estate investors'
      }
    ];

    setContentLibrary(mockContent);
    setContentCalendar(mockCalendar);
  }, []);

  const generateContent = async () => {
    setIsGenerating(true);
    
    // Simulate AI content generation
    const newContent: ContentItem = {
      id: Date.now().toString(),
      type: generationRequest.type,
      title: `Generated ${generationRequest.type} - ${generationRequest.style}`,
      description: generationRequest.prompt,
      status: 'generating',
      thumbnail: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=generating%20content%20ai%20loading&image_size=square',
      metadata: {
        dimensions: generationRequest.dimensions,
        duration: generationRequest.duration,
        format: generationRequest.type === 'image' ? 'PNG' : generationRequest.type === 'video' ? 'MP4' : 'MP3'
      },
      engagement: {
        views: 0,
        likes: 0,
        comments: 0,
        shares: 0
      },
      platforms: generationRequest.platforms,
      tags: generationRequest.prompt.split(' ').slice(0, 5),
      aiPrompt: generationRequest.prompt,
      generationTime: 0
    };

    setContentLibrary(prev => [newContent, ...prev]);

    // Simulate generation time
    const generationTime = generationRequest.type === 'video' ? 120 : generationRequest.type === 'audio' ? 60 : 15;
    
    setTimeout(() => {
      setContentLibrary(prev => prev.map(item => 
        item.id === newContent.id 
          ? {
              ...item,
              status: 'ready',
              thumbnail: `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=${encodeURIComponent(generationRequest.prompt)}&image_size=${generationRequest.dimensions}`,
              url: `https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=${encodeURIComponent(generationRequest.prompt)}&image_size=${generationRequest.dimensions}`,
              generationTime
            }
          : item
      ));
      setIsGenerating(false);
    }, generationTime * 100); // Simulate real-time generation
  };

  const publishContent = (contentId: string) => {
    setContentLibrary(prev => prev.map(item => 
      item.id === contentId 
        ? { ...item, status: 'published' }
        : item
    ));
  };

  const scheduleContent = (contentId: string, date: string) => {
    setContentLibrary(prev => prev.map(item => 
      item.id === contentId 
        ? { ...item, status: 'scheduled', scheduledDate: date }
        : item
    ));
  };

  const toggleAudio = (contentId: string) => {
    setAudioPlaying(prev => prev === contentId ? null : contentId);
  };

  const getStatusColor = (status: ContentItem['status']) => {
    switch (status) {
      case 'generating': return 'bg-yellow-100 text-yellow-800';
      case 'ready': return 'bg-blue-100 text-blue-800';
      case 'published': return 'bg-green-100 text-green-800';
      case 'scheduled': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: ContentItem['type']) => {
    switch (type) {
      case 'image': return <Image className="w-4 h-4" />;
      case 'video': return <Video className="w-4 h-4" />;
      case 'audio': return <Music className="w-4 h-4" />;
      case 'carousel': return <Camera className="w-4 h-4" />;
      case '3d_scene': return <Sparkles className="w-4 h-4" />;
      default: return <Image className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-pink-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Content Generation Studio</h1>
          <p className="text-gray-600">AI-powered multi-modal content creation for marketing and branding</p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex space-x-1 bg-white rounded-xl p-1 mb-8 shadow-lg">
          {[
            { id: 'generate', label: 'Generate', icon: <Wand2 className="w-4 h-4" /> },
            { id: 'library', label: 'Library', icon: <Image className="w-4 h-4" /> },
            { id: 'calendar', label: 'Calendar', icon: <Calendar className="w-4 h-4" /> },
            { id: 'analytics', label: 'Analytics', icon: <TrendingUp className="w-4 h-4" /> }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all ${
                activeTab === tab.id
                  ? 'bg-purple-600 text-white shadow-md'
                  : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
              }`}
            >
              {tab.icon}
              {tab.label}
            </button>
          ))}
        </div>

        {/* Generate Tab */}
        {activeTab === 'generate' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-lg p-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Generate New Content</h2>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Generation Form */}
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Content Type</label>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { value: 'image', label: 'Image', icon: <Image className="w-4 h-4" /> },
                      { value: 'video', label: 'Video', icon: <Video className="w-4 h-4" /> },
                      { value: 'audio', label: 'Audio', icon: <Music className="w-4 h-4" /> },
                      { value: '3d_scene', label: '3D Scene', icon: <Sparkles className="w-4 h-4" /> }
                    ].map((type) => (
                      <button
                        key={type.value}
                        onClick={() => setGenerationRequest(prev => ({ ...prev, type: type.value as any }))}
                        className={`flex items-center gap-2 p-3 rounded-lg border-2 transition-all ${
                          generationRequest.type === type.value
                            ? 'border-purple-500 bg-purple-50 text-purple-700'
                            : 'border-gray-200 hover:border-purple-300'
                        }`}
                      >
                        {type.icon}
                        {type.label}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">AI Prompt</label>
                  <textarea
                    value={generationRequest.prompt}
                    onChange={(e) => setGenerationRequest(prev => ({ ...prev, prompt: e.target.value }))}
                    placeholder="Describe the content you want to generate..."
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Style</label>
                    <select
                      value={generationRequest.style}
                      onChange={(e) => setGenerationRequest(prev => ({ ...prev, style: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="photorealistic">Photorealistic</option>
                      <option value="artistic">Artistic</option>
                      <option value="minimalist">Minimalist</option>
                      <option value="luxury">Luxury</option>
                      <option value="modern">Modern</option>
                      <option value="vintage">Vintage</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Mood</label>
                    <select
                      value={generationRequest.mood}
                      onChange={(e) => setGenerationRequest(prev => ({ ...prev, mood: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    >
                      <option value="professional">Professional</option>
                      <option value="elegant">Elegant</option>
                      <option value="energetic">Energetic</option>
                      <option value="calm">Calm</option>
                      <option value="dramatic">Dramatic</option>
                      <option value="warm">Warm</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Target Platforms</label>
                  <div className="grid grid-cols-3 gap-2">
                    {['instagram', 'facebook', 'linkedin', 'youtube', 'tiktok', 'twitter'].map((platform) => (
                      <label key={platform} className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={generationRequest.platforms.includes(platform)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setGenerationRequest(prev => ({
                                ...prev,
                                platforms: [...prev.platforms, platform]
                              }));
                            } else {
                              setGenerationRequest(prev => ({
                                ...prev,
                                platforms: prev.platforms.filter(p => p !== platform)
                              }));
                            }
                          }}
                          className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                        />
                        <span className="text-sm text-gray-700 capitalize">{platform}</span>
                      </label>
                    ))}
                  </div>
                </div>

                <button
                  onClick={generateContent}
                  disabled={isGenerating || !generationRequest.prompt}
                  className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 disabled:from-gray-400 disabled:to-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-all flex items-center justify-center gap-2"
                >
                  {isGenerating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4" />
                      Generate Content
                    </>
                  )}
                </button>
              </div>

              {/* Preview Area */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Preview</h3>
                <div className="aspect-square bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                  {generationRequest.prompt ? (
                    <div className="text-center p-4">
                      <Palette className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-600 mb-2">Preview will appear here</p>
                      <p className="text-xs text-gray-500">{generationRequest.prompt}</p>
                    </div>
                  ) : (
                    <div className="text-center">
                      <Image className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">Enter a prompt to see preview</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Library Tab */}
        {activeTab === 'library' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-bold text-gray-900">Content Library</h2>
              <div className="flex gap-2">
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  Filter
                </button>
                <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                  Sort
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {contentLibrary.map((content) => (
                <motion.div
                  key={content.id}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow cursor-pointer"
                  onClick={() => setSelectedContent(content)}
                >
                  <div className="relative">
                    <img
                      src={content.thumbnail}
                      alt={content.title}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute top-3 left-3">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(content.status)}`}>
                        {getTypeIcon(content.type)}
                        {content.status.toUpperCase()}
                      </span>
                    </div>
                    {content.type === 'audio' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleAudio(content.id);
                        }}
                        className="absolute top-3 right-3 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                      >
                        {audioPlaying === content.id ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                      </button>
                    )}
                  </div>
                  
                  <div className="p-4">
                    <h3 className="font-semibold text-gray-900 mb-1">{content.title}</h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">{content.description}</p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                      <span>{content.metadata.format}</span>
                      <span>{content.metadata.fileSize}</span>
                      {content.metadata.duration && <span>{content.metadata.duration}s</span>}
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          {content.engagement.views.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <Heart className="w-3 h-3" />
                          {content.engagement.likes}
                        </span>
                      </div>
                      
                      <div className="flex gap-1">
                        {content.status === 'ready' && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              publishContent(content.id);
                            }}
                            className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-xs transition-colors"
                          >
                            Publish
                          </button>
                        )}
                        <button className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors flex items-center gap-1">
                          <Share2 className="w-3 h-3" />
                          Share
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Calendar Tab */}
        {activeTab === 'calendar' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-lg p-8"
          >
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Content Calendar</h2>
            
            <div className="space-y-4">
              {contentCalendar.map((day, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-gray-900">{new Date(day.date).toLocaleDateString()}</h3>
                    <div className="text-sm text-gray-600">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded mr-2">{day.theme}</span>
                      <span className="bg-green-100 text-green-800 px-2 py-1 rounded">{day.targetAudience}</span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {day.content.map((content) => (
                      <div key={content.id} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                        <img src={content.thumbnail} alt={content.title} className="w-12 h-12 rounded object-cover" />
                        <div className="flex-1">
                          <p className="font-medium text-sm text-gray-900">{content.title}</p>
                          <p className="text-xs text-gray-600">{content.platforms.join(', ')}</p>
                        </div>
                        <span className={`px-2 py-1 rounded text-xs ${getStatusColor(content.status)}`}>
                          {content.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <h2 className="text-2xl font-bold text-gray-900">Content Analytics</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[
                { label: 'Total Views', value: '127.5K', change: '+12.3%', color: 'blue' },
                { label: 'Engagement Rate', value: '8.7%', change: '+2.1%', color: 'green' },
                { label: 'Content Generated', value: '342', change: '+45', color: 'purple' },
                { label: 'Avg. Generation Time', value: '23s', change: '-5s', color: 'orange' }
              ].map((stat, index) => (
                <div key={index} className="bg-white rounded-xl shadow-lg p-6">
                  <h3 className="text-sm font-medium text-gray-600 mb-2">{stat.label}</h3>
                  <p className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</p>
                  <p className={`text-sm font-medium ${
                    stat.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change} from last month
                  </p>
                </div>
              ))}
            </div>
            
            <div className="bg-white rounded-xl shadow-lg p-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Performance by Content Type</h3>
              <div className="space-y-4">
                {[
                  { type: 'Image', count: 156, engagement: 8.2, color: 'bg-blue-500' },
                  { type: 'Video', count: 89, engagement: 12.7, color: 'bg-green-500' },
                  { type: 'Audio', count: 67, engagement: 6.1, color: 'bg-purple-500' },
                  { type: '3D Scene', count: 30, engagement: 15.3, color: 'bg-orange-500' }
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-4">
                    <div className={`w-4 h-4 rounded ${item.color}`} />
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="font-medium text-gray-900">{item.type}</span>
                        <span className="text-sm text-gray-600">{item.count} pieces</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${item.color}`}
                          style={{ width: `${(item.engagement / 20) * 100}%` }}
                        />
                      </div>
                    </div>
                    <span className="text-sm font-medium text-gray-900">{item.engagement}%</span>
                  </div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </div>

      {/* Content Detail Modal */}
      <AnimatePresence>
        {selectedContent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
            onClick={() => setSelectedContent(null)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900">{selectedContent.title}</h2>
                  <button
                    onClick={() => setSelectedContent(null)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    ✕
                  </button>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <img
                      src={selectedContent.url || selectedContent.thumbnail}
                      alt={selectedContent.title}
                      className="w-full rounded-lg"
                    />
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                      <p className="text-gray-600">{selectedContent.description}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">AI Prompt</h3>
                      <p className="text-gray-600 bg-gray-50 p-3 rounded-lg">{selectedContent.aiPrompt}</p>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Metadata</h3>
                      <div className="grid grid-cols-2 gap-2 text-sm">
                        <div>Format: {selectedContent.metadata.format}</div>
                        <div>Size: {selectedContent.metadata.fileSize}</div>
                        {selectedContent.metadata.duration && <div>Duration: {selectedContent.metadata.duration}s</div>}
                        <div>Generated in: {selectedContent.generationTime}s</div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Engagement</h3>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="flex items-center gap-2">
                          <Eye className="w-4 h-4 text-gray-500" />
                          <span>{selectedContent.engagement.views.toLocaleString()} views</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Heart className="w-4 h-4 text-gray-500" />
                          <span>{selectedContent.engagement.likes} likes</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MessageCircle className="w-4 h-4 text-gray-500" />
                          <span>{selectedContent.engagement.comments} comments</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Share2 className="w-4 h-4 text-gray-500" />
                          <span>{selectedContent.engagement.shares} shares</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-2 pt-4">
                      <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center gap-2">
                        <Download className="w-4 h-4" />
                        Download
                      </button>
                      <button className="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center justify-center gap-2">
                        <Share2 className="w-4 h-4" />
                        Share
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ContentGenerationStudio;