#!/bin/bash

# Sorteo Estelar Deployment Script
# VPS: admin@**************:2222
# Domain: www.sorteoestelar.com

set -e  # Exit on any error

# Configuration
VPS_HOST="**************"
VPS_PORT="2222"
VPS_USER="admin"
APP_NAME="sorteo-estelar"
DOMAIN="www.sorteoestelar.com"
REPO_URL="https://github.com/estratix/sorteo-estelar.git"
DEPLOY_PATH="/opt/apps/${APP_NAME}"
DOKPLOY_API="http://localhost:3000/api"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if SSH key exists
    if [ ! -f ~/.ssh/id_rsa ]; then
        error "SSH key not found. Please generate one with: ssh-keygen -t rsa -b 4096"
        exit 1
    fi
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if Git is installed
    if ! command -v git &> /dev/null; then
        error "Git is not installed. Please install Git first."
        exit 1
    fi
    
    success "Prerequisites check passed"
}

# Test SSH connection
test_ssh_connection() {
    log "Testing SSH connection to ${VPS_USER}@${VPS_HOST}:${VPS_PORT}..."
    
    if ssh -p ${VPS_PORT} -o ConnectTimeout=10 -o BatchMode=yes ${VPS_USER}@${VPS_HOST} exit 2>/dev/null; then
        success "SSH connection successful"
    else
        error "SSH connection failed. Please check your SSH key and VPS access."
        echo "Try: ssh-copy-id -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST}"
        exit 1
    fi
}

# Setup VPS environment
setup_vps_environment() {
    log "Setting up VPS environment..."
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << 'EOF'
        # Update system
        sudo apt update && sudo apt upgrade -y
        
        # Install Docker if not present
        if ! command -v docker &> /dev/null; then
            curl -fsSL https://get.docker.com -o get-docker.sh
            sudo sh get-docker.sh
            sudo usermod -aG docker $USER
        fi
        
        # Install Docker Compose
        if ! command -v docker-compose &> /dev/null; then
            sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
            sudo chmod +x /usr/local/bin/docker-compose
        fi
        
        # Install Node.js and npm
        if ! command -v node &> /dev/null; then
            curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
            sudo apt-get install -y nodejs
        fi
        
        # Install Git if not present
        if ! command -v git &> /dev/null; then
            sudo apt install -y git
        fi
        
        # Create application directory
        sudo mkdir -p /opt/apps
        sudo chown -R $USER:$USER /opt/apps
        
        echo "VPS environment setup completed"
EOF
    
    success "VPS environment setup completed"
}

# Install Dokploy
install_dokploy() {
    log "Installing Dokploy on VPS..."
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << 'EOF'
        # Install Dokploy
        if ! command -v dokploy &> /dev/null; then
            curl -sSL https://dokploy.com/install.sh | sh
        fi
        
        # Start Dokploy service
        sudo systemctl enable dokploy
        sudo systemctl start dokploy
        
        # Wait for Dokploy to be ready
        sleep 30
        
        echo "Dokploy installation completed"
EOF
    
    success "Dokploy installed and started"
}

# Deploy application
deploy_application() {
    log "Deploying Sorteo Estelar application..."
    
    # Copy deployment files to VPS
    scp -P ${VPS_PORT} -r . ${VPS_USER}@${VPS_HOST}:${DEPLOY_PATH}/
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << EOF
        cd ${DEPLOY_PATH}
        
        # Build and deploy with Docker Compose
        docker-compose down --remove-orphans
        docker-compose build --no-cache
        docker-compose up -d
        
        # Wait for services to be ready
        sleep 60
        
        # Check service health
        docker-compose ps
        
        echo "Application deployment completed"
EOF
    
    success "Application deployed successfully"
}

# Configure domain and SSL
configure_domain() {
    log "Configuring domain and SSL for ${DOMAIN}..."
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << EOF
        # Install Certbot for SSL
        sudo apt install -y certbot python3-certbot-nginx
        
        # Generate SSL certificate
        sudo certbot --nginx -d ${DOMAIN} --non-interactive --agree-tos --email <EMAIL>
        
        # Setup auto-renewal
        sudo crontab -l | { cat; echo "0 12 * * * /usr/bin/certbot renew --quiet"; } | sudo crontab -
        
        echo "Domain and SSL configuration completed"
EOF
    
    success "Domain and SSL configured for ${DOMAIN}"
}

# Setup monitoring
setup_monitoring() {
    log "Setting up monitoring and logging..."
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << EOF
        cd ${DEPLOY_PATH}
        
        # Start monitoring services
        docker-compose up -d prometheus grafana
        
        # Configure log rotation
        sudo tee /etc/logrotate.d/sorteo-estelar > /dev/null <<EOL
/var/log/sorteo-estelar/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 root root
    postrotate
        docker-compose restart nginx
    endscript
}
EOL
        
        echo "Monitoring setup completed"
EOF
    
    success "Monitoring and logging configured"
}

# Setup Git hooks for CI/CD
setup_git_hooks() {
    log "Setting up Git hooks for automated deployment..."
    
    ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} << EOF
        cd ${DEPLOY_PATH}
        
        # Initialize Git repository if not exists
        if [ ! -d .git ]; then
            git init
            git remote add origin ${REPO_URL}
        fi
        
        # Create post-receive hook
        mkdir -p .git/hooks
        cat > .git/hooks/post-receive << 'HOOK'
#!/bin/bash
cd ${DEPLOY_PATH}
git --git-dir=${DEPLOY_PATH}/.git --work-tree=${DEPLOY_PATH} checkout -f
docker-compose build --no-cache
docker-compose up -d
echo "Deployment completed via Git hook"
HOOK
        
        chmod +x .git/hooks/post-receive
        
        echo "Git hooks setup completed"
EOF
    
    success "Git hooks configured for automated deployment"
}

# Health check
health_check() {
    log "Performing health check..."
    
    # Check if application is responding
    if curl -f -s "http://${VPS_HOST}:3000/health" > /dev/null; then
        success "Application is healthy and responding"
    else
        warn "Application health check failed, but deployment may still be in progress"
    fi
    
    # Display deployment summary
    echo ""
    echo "=== DEPLOYMENT SUMMARY ==="
    echo "Application: ${APP_NAME}"
    echo "Domain: ${DOMAIN}"
    echo "VPS: ${VPS_USER}@${VPS_HOST}:${VPS_PORT}"
    echo "Deploy Path: ${DEPLOY_PATH}"
    echo "Status: Deployed"
    echo ""
    echo "Access your application at: https://${DOMAIN}"
    echo "Monitoring dashboard: http://${VPS_HOST}:3002"
    echo ""
}

# Main deployment function
main() {
    log "Starting Sorteo Estelar deployment process..."
    
    check_prerequisites
    test_ssh_connection
    setup_vps_environment
    install_dokploy
    deploy_application
    configure_domain
    setup_monitoring
    setup_git_hooks
    health_check
    
    success "Deployment completed successfully!"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "health")
        health_check
        ;;
    "logs")
        ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} "cd ${DEPLOY_PATH} && docker-compose logs -f"
        ;;
    "restart")
        ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} "cd ${DEPLOY_PATH} && docker-compose restart"
        ;;
    "stop")
        ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} "cd ${DEPLOY_PATH} && docker-compose down"
        ;;
    "start")
        ssh -p ${VPS_PORT} ${VPS_USER}@${VPS_HOST} "cd ${DEPLOY_PATH} && docker-compose up -d"
        ;;
    *)
        echo "Usage: $0 {deploy|health|logs|restart|stop|start}"
        exit 1
        ;;
esac