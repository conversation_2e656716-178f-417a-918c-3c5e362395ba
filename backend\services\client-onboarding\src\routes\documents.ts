import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { ValidationError } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { OnboardingDocument } from '@/types';
import { MultipartFile } from '@fastify/multipart';

export interface GetDocumentsQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  type?: string;
  clientId?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface DocumentParams {
  id: string;
}

export interface UploadDocumentBody {
  clientId: string;
  type: string;
  description?: string;
  required?: boolean;
}

export interface ApproveDocumentBody {
  notes?: string;
}

export interface RejectDocumentBody {
  reason: string;
  notes?: string;
}

export async function documentRoutes(fastify: FastifyInstance) {
  // Get all documents with filtering and pagination
  fastify.get('/documents', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get all documents with filtering and pagination',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          search: { type: 'string' },
          status: { type: 'string', enum: ['pending', 'approved', 'rejected'] },
          type: { type: 'string', enum: ['contract', 'identity', 'financial', 'legal', 'technical', 'other'] },
          clientId: { type: 'string', format: 'uuid' },
          sortBy: { type: 'string', enum: ['name', 'type', 'status', 'size', 'uploadedAt', 'reviewedAt'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            documents: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  originalName: { type: 'string' },
                  clientId: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  size: { type: 'integer' },
                  mimeType: { type: 'string' },
                  description: { type: 'string' },
                  required: { type: 'boolean' },
                  uploadedAt: { type: 'string' },
                  reviewedAt: { type: 'string' },
                  approvedBy: { type: 'string' },
                  rejectedBy: { type: 'string' },
                  rejectionReason: { type: 'string' },
                  notes: { type: 'string' },
                  downloadUrl: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                pages: { type: 'integer' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetDocumentsQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10, search, status, type, clientId, sortBy = 'uploadedAt', sortOrder = 'desc' } = request.query;
      
      const result = await fastify.documentService.getDocuments({
        page,
        limit,
        search,
        status,
        type,
        clientId,
        sortBy,
        sortOrder
      });

      logger.info('Documents retrieved', {
        userId: (request as any).user?.id,
        count: result.documents.length,
        page,
        limit
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to get documents', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve documents' });
    }
  });

  // Get document by ID
  fastify.get('/documents/:id', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get document by ID',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            originalName: { type: 'string' },
            clientId: { type: 'string' },
            type: { type: 'string' },
            status: { type: 'string' },
            size: { type: 'integer' },
            mimeType: { type: 'string' },
            description: { type: 'string' },
            required: { type: 'boolean' },
            uploadedAt: { type: 'string' },
            reviewedAt: { type: 'string' },
            approvedBy: { type: 'string' },
            rejectedBy: { type: 'string' },
            rejectionReason: { type: 'string' },
            notes: { type: 'string' },
            downloadUrl: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: DocumentParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const document = await fastify.documentService.getDocumentById(id);
      
      if (!document) {
        return reply.status(404).send({ error: 'Document not found' });
      }

      logger.info('Document retrieved', {
        userId: (request as any).user?.id,
        documentId: id
      });

      return reply.status(200).send(document);
    } catch (error) {
      logger.error('Failed to get document', { error, documentId: request.params.id });
      return reply.status(500).send({ error: 'Failed to retrieve document' });
    }
  });

  // Upload document
  fastify.post('/documents/upload', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Upload a document',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      consumes: ['multipart/form-data'],
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            originalName: { type: 'string' },
            clientId: { type: 'string' },
            type: { type: 'string' },
            status: { type: 'string' },
            size: { type: 'integer' },
            mimeType: { type: 'string' },
            description: { type: 'string' },
            required: { type: 'boolean' },
            uploadedAt: { type: 'string' },
            downloadUrl: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const data = await request.file();
      
      if (!data) {
        return reply.status(400).send({ error: 'No file uploaded' });
      }

      // Get form fields
      const fields = data.fields;
      const clientId = (fields.clientId as any)?.value;
      const type = (fields.type as any)?.value;
      const description = (fields.description as any)?.value;
      const required = (fields.required as any)?.value === 'true';

      if (!clientId || !type) {
        return reply.status(400).send({ error: 'clientId and type are required' });
      }

      // Verify client exists
      const client = await fastify.clientService.getClientById(clientId);
      if (!client) {
        return reply.status(400).send({ error: 'Client not found' });
      }

      // Read file buffer
      const buffer = await data.toBuffer();
      
      const document = await fastify.documentService.uploadDocument({
        file: {
          buffer,
          originalName: data.filename,
          mimeType: data.mimetype,
          size: buffer.length
        },
        clientId,
        type,
        description,
        required
      });

      logger.info('Document uploaded', {
        userId: (request as any).user?.id,
        documentId: document.id,
        clientId,
        filename: data.filename,
        size: buffer.length
      });

      return reply.status(201).send(document);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to upload document', { error });
      return reply.status(500).send({ error: 'Failed to upload document' });
    }
  });

  // Download document
  fastify.get('/documents/:id/download', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Download a document',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'string',
          format: 'binary'
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: DocumentParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const result = await fastify.documentService.downloadDocument(id);
      
      if (!result) {
        return reply.status(404).send({ error: 'Document not found' });
      }

      const { document, buffer } = result;

      logger.info('Document downloaded', {
        userId: (request as any).user?.id,
        documentId: id,
        filename: document.originalName
      });

      return reply
        .header('Content-Type', document.mimeType)
        .header('Content-Disposition', `attachment; filename="${document.originalName}"`)
        .header('Content-Length', document.size.toString())
        .send(buffer);
    } catch (error) {
      logger.error('Failed to download document', { error, documentId: request.params.id });
      return reply.status(500).send({ error: 'Failed to download document' });
    }
  });

  // Approve document
  fastify.post('/documents/:id/approve', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Approve a document',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          notes: { type: 'string', maxLength: 1000 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            reviewedAt: { type: 'string' },
            approvedBy: { type: 'string' },
            notes: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: DocumentParams; Body: ApproveDocumentBody }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { notes } = request.body;
      const approverId = (request as any).user?.id;
      
      const result = await fastify.documentService.approveDocument(id, approverId, notes);
      
      if (!result) {
        return reply.status(404).send({ error: 'Document not found' });
      }

      // Get document and client for notification
      const document = await fastify.documentService.getDocumentById(id);
      if (document) {
        const client = await fastify.clientService.getClientById(document.clientId);
        if (client) {
          try {
            await fastify.emailService.sendDocumentApprovalNotification(
              document.originalName,
              client.email,
              approverId
            );
          } catch (emailError) {
            logger.warn('Failed to send document approval notification', { error: emailError, documentId: id });
          }
        }
      }

      logger.info('Document approved', {
        userId: approverId,
        documentId: id,
        notes: notes ? 'provided' : 'none'
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to approve document', { error, documentId: request.params.id });
      return reply.status(500).send({ error: 'Failed to approve document' });
    }
  });

  // Reject document
  fastify.post('/documents/:id/reject', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Reject a document',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          reason: { type: 'string', minLength: 1, maxLength: 500 },
          notes: { type: 'string', maxLength: 1000 }
        },
        required: ['reason']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            status: { type: 'string' },
            reviewedAt: { type: 'string' },
            rejectedBy: { type: 'string' },
            rejectionReason: { type: 'string' },
            notes: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: DocumentParams; Body: RejectDocumentBody }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      const { reason, notes } = request.body;
      const rejectorId = (request as any).user?.id;
      
      const result = await fastify.documentService.rejectDocument(id, rejectorId, reason, notes);
      
      if (!result) {
        return reply.status(404).send({ error: 'Document not found' });
      }

      // Get document and client for notification
      const document = await fastify.documentService.getDocumentById(id);
      if (document) {
        const client = await fastify.clientService.getClientById(document.clientId);
        if (client) {
          try {
            await fastify.emailService.sendDocumentRejectionNotification(
              document.originalName,
              client.email,
              reason
            );
          } catch (emailError) {
            logger.warn('Failed to send document rejection notification', { error: emailError, documentId: id });
          }
        }
      }

      logger.info('Document rejected', {
        userId: rejectorId,
        documentId: id,
        reason,
        notes: notes ? 'provided' : 'none'
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to reject document', { error, documentId: request.params.id });
      return reply.status(500).send({ error: 'Failed to reject document' });
    }
  });

  // Delete document
  fastify.delete('/documents/:id', {
    preHandler: [authenticateToken, requireRole(['admin'])],
    schema: {
      description: 'Delete document',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        204: {
          type: 'null'
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: DocumentParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const deleted = await fastify.documentService.deleteDocument(id);
      
      if (!deleted) {
        return reply.status(404).send({ error: 'Document not found' });
      }

      logger.info('Document deleted', {
        userId: (request as any).user?.id,
        documentId: id
      });

      return reply.status(204).send();
    } catch (error) {
      logger.error('Failed to delete document', { error, documentId: request.params.id });
      return reply.status(500).send({ error: 'Failed to delete document' });
    }
  });

  // Get document statistics
  fastify.get('/documents/stats', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get document statistics',
      tags: ['Documents'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            pending: { type: 'integer' },
            approved: { type: 'integer' },
            rejected: { type: 'integer' },
            approvalRate: { type: 'number' },
            averageProcessingTime: { type: 'number' },
            totalSize: { type: 'integer' },
            byType: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await fastify.documentService.getDocumentStats();

      logger.info('Document statistics retrieved', {
        userId: (request as any).user?.id,
        total: stats.total
      });

      return reply.status(200).send(stats);
    } catch (error) {
      logger.error('Failed to get document statistics', { error });
      return reply.status(500).send({ error: 'Failed to retrieve document statistics' });
    }
  });
}