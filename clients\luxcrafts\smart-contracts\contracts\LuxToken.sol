// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts-upgradeable/token/ERC20/ERC20Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20BurnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC20/extensions/ERC20PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";

/**
 * @title LuxToken
 * @dev The native utility token for the ESTRATIX platform
 * @notice LUX token is used for property transactions, staking, governance, and platform fees
 */
contract LuxToken is
    Initializable,
    ERC20Upgradeable,
    ERC20BurnableUpgradeable,
    ERC20PausableUpgradeable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant BURNER_ROLE = keccak256("BURNER_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");
    bytes32 public constant PAUSER_ROLE = keccak256("PAUSER_ROLE");

    // Token economics
    uint256 public constant MAX_SUPPLY = 1_000_000_000 * 10**18; // 1 billion LUX
    uint256 public constant INITIAL_SUPPLY = 100_000_000 * 10**18; // 100 million LUX
    
    // Vesting and distribution
    mapping(address => VestingSchedule) public vestingSchedules;
    mapping(address => bool) public isVestingBeneficiary;
    address[] public vestingBeneficiaries;
    
    struct VestingSchedule {
        uint256 totalAmount;
        uint256 releasedAmount;
        uint256 startTime;
        uint256 cliffDuration;
        uint256 vestingDuration;
        bool revocable;
        bool revoked;
    }
    
    // Staking rewards
    mapping(address => uint256) public stakingRewards;
    uint256 public totalStakingRewards;
    
    // Transaction fees
    uint256 public transactionFeePercentage; // Basis points (100 = 1%)
    address public feeRecipient;
    mapping(address => bool) public feeExemptAddresses;
    
    // Events
    event VestingScheduleCreated(
        address indexed beneficiary,
        uint256 totalAmount,
        uint256 startTime,
        uint256 cliffDuration,
        uint256 vestingDuration
    );
    
    event TokensVested(address indexed beneficiary, uint256 amount);
    event VestingRevoked(address indexed beneficiary, uint256 unvestedAmount);
    event StakingRewardDistributed(address indexed recipient, uint256 amount);
    event TransactionFeeCollected(address indexed from, address indexed to, uint256 feeAmount);
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    function initialize(
        address _admin,
        address _feeRecipient,
        uint256 _transactionFeePercentage
    ) public initializer {
        __ERC20_init("LuxToken", "LUX");
        __ERC20Burnable_init();
        __ERC20Pausable_init();
        __AccessControl_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(MINTER_ROLE, _admin);
        _grantRole(BURNER_ROLE, _admin);
        _grantRole(UPGRADER_ROLE, _admin);
        _grantRole(PAUSER_ROLE, _admin);
        
        feeRecipient = _feeRecipient;
        transactionFeePercentage = _transactionFeePercentage;
        
        // Mint initial supply to admin
        _mint(_admin, INITIAL_SUPPLY);
    }
    
    /**
     * @dev Mint new tokens (only up to max supply)
     */
    function mint(address to, uint256 amount) public onlyRole(MINTER_ROLE) {
        require(totalSupply() + amount <= MAX_SUPPLY, "Exceeds maximum supply");
        _mint(to, amount);
    }
    
    /**
     * @dev Burn tokens from a specific account
     */
    function burnFrom(address account, uint256 amount) public override onlyRole(BURNER_ROLE) {
        super.burnFrom(account, amount);
    }
    
    /**
     * @dev Create a vesting schedule for a beneficiary
     */
    function createVestingSchedule(
        address _beneficiary,
        uint256 _totalAmount,
        uint256 _startTime,
        uint256 _cliffDuration,
        uint256 _vestingDuration,
        bool _revocable
    ) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_beneficiary != address(0), "Invalid beneficiary");
        require(_totalAmount > 0, "Invalid amount");
        require(_vestingDuration > 0, "Invalid vesting duration");
        require(!isVestingBeneficiary[_beneficiary], "Vesting schedule already exists");
        
        vestingSchedules[_beneficiary] = VestingSchedule({
            totalAmount: _totalAmount,
            releasedAmount: 0,
            startTime: _startTime,
            cliffDuration: _cliffDuration,
            vestingDuration: _vestingDuration,
            revocable: _revocable,
            revoked: false
        });
        
        isVestingBeneficiary[_beneficiary] = true;
        vestingBeneficiaries.push(_beneficiary);
        
        // Transfer tokens to this contract for vesting
        _transfer(msg.sender, address(this), _totalAmount);
        
        emit VestingScheduleCreated(
            _beneficiary,
            _totalAmount,
            _startTime,
            _cliffDuration,
            _vestingDuration
        );
    }
    
    /**
     * @dev Release vested tokens to beneficiary
     */
    function releaseVestedTokens(address _beneficiary) public nonReentrant {
        require(isVestingBeneficiary[_beneficiary], "No vesting schedule");
        
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        require(!schedule.revoked, "Vesting revoked");
        
        uint256 vestedAmount = _calculateVestedAmount(_beneficiary);
        uint256 releasableAmount = vestedAmount - schedule.releasedAmount;
        
        require(releasableAmount > 0, "No tokens to release");
        
        schedule.releasedAmount += releasableAmount;
        _transfer(address(this), _beneficiary, releasableAmount);
        
        emit TokensVested(_beneficiary, releasableAmount);
    }
    
    /**
     * @dev Revoke vesting schedule (only if revocable)
     */
    function revokeVesting(address _beneficiary) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(isVestingBeneficiary[_beneficiary], "No vesting schedule");
        
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        require(schedule.revocable, "Vesting not revocable");
        require(!schedule.revoked, "Already revoked");
        
        uint256 vestedAmount = _calculateVestedAmount(_beneficiary);
        uint256 releasableAmount = vestedAmount - schedule.releasedAmount;
        uint256 unvestedAmount = schedule.totalAmount - vestedAmount;
        
        schedule.revoked = true;
        
        // Release any vested tokens
        if (releasableAmount > 0) {
            schedule.releasedAmount += releasableAmount;
            _transfer(address(this), _beneficiary, releasableAmount);
        }
        
        // Return unvested tokens to admin
        if (unvestedAmount > 0) {
            _transfer(address(this), msg.sender, unvestedAmount);
        }
        
        emit VestingRevoked(_beneficiary, unvestedAmount);
    }
    
    /**
     * @dev Distribute staking rewards
     */
    function distributeStakingReward(address _recipient, uint256 _amount) public onlyRole(MINTER_ROLE) {
        require(_recipient != address(0), "Invalid recipient");
        require(_amount > 0, "Invalid amount");
        
        stakingRewards[_recipient] += _amount;
        totalStakingRewards += _amount;
        
        _mint(_recipient, _amount);
        
        emit StakingRewardDistributed(_recipient, _amount);
    }
    
    /**
     * @dev Calculate vested amount for a beneficiary
     */
    function _calculateVestedAmount(address _beneficiary) internal view returns (uint256) {
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        
        if (block.timestamp < schedule.startTime + schedule.cliffDuration) {
            return 0;
        }
        
        if (block.timestamp >= schedule.startTime + schedule.vestingDuration) {
            return schedule.totalAmount;
        }
        
        uint256 timeFromStart = block.timestamp - schedule.startTime;
        return (schedule.totalAmount * timeFromStart) / schedule.vestingDuration;
    }
    
    /**
     * @dev Get vesting information for a beneficiary
     */
    function getVestingInfo(address _beneficiary) public view returns (
        uint256 totalAmount,
        uint256 releasedAmount,
        uint256 vestedAmount,
        uint256 releasableAmount,
        bool revoked
    ) {
        require(isVestingBeneficiary[_beneficiary], "No vesting schedule");
        
        VestingSchedule storage schedule = vestingSchedules[_beneficiary];
        uint256 vested = _calculateVestedAmount(_beneficiary);
        uint256 releasable = vested - schedule.releasedAmount;
        
        return (
            schedule.totalAmount,
            schedule.releasedAmount,
            vested,
            releasable,
            schedule.revoked
        );
    }
    
    /**
     * @dev Override transfer to implement transaction fees
     */
    function _transfer(address from, address to, uint256 amount) internal override {
        require(from != address(0), "Transfer from zero address");
        require(to != address(0), "Transfer to zero address");
        
        // Skip fees for exempt addresses or internal transfers
        if (feeExemptAddresses[from] || feeExemptAddresses[to] || 
            from == address(this) || to == address(this) ||
            transactionFeePercentage == 0) {
            super._transfer(from, to, amount);
            return;
        }
        
        uint256 feeAmount = (amount * transactionFeePercentage) / 10000;
        uint256 transferAmount = amount - feeAmount;
        
        if (feeAmount > 0) {
            super._transfer(from, feeRecipient, feeAmount);
            emit TransactionFeeCollected(from, to, feeAmount);
        }
        
        super._transfer(from, to, transferAmount);
    }
    
    // Admin functions
    function pause() public onlyRole(PAUSER_ROLE) {
        _pause();
    }
    
    function unpause() public onlyRole(PAUSER_ROLE) {
        _unpause();
    }
    
    function setTransactionFee(uint256 _newFeePercentage) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_newFeePercentage <= 1000, "Fee too high"); // Max 10%
        transactionFeePercentage = _newFeePercentage;
    }
    
    function setFeeRecipient(address _newFeeRecipient) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_newFeeRecipient != address(0), "Invalid address");
        feeRecipient = _newFeeRecipient;
    }
    
    function setFeeExemption(address _address, bool _exempt) public onlyRole(DEFAULT_ADMIN_ROLE) {
        feeExemptAddresses[_address] = _exempt;
    }
    
    // Required overrides
    function _authorizeUpgrade(address newImplementation) internal onlyRole(UPGRADER_ROLE) override {}
    
    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 amount
    ) internal override(ERC20Upgradeable, ERC20PausableUpgradeable) {
        super._beforeTokenTransfer(from, to, amount);
    }
}