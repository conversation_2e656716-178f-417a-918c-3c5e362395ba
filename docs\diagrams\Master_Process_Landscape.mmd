graph TD;
    classDef planning fill:#add8e6,stroke:#333,stroke-width:2px;
    classDef draft fill:#ffff99,stroke:#333,stroke-width:2px;
    classDef active fill:#90ee90,stroke:#333,stroke-width:2px;
    classDef deprecated fill:#cccccc,stroke:#333,stroke-width:2px;

    subgraph SG_PRIMARY ["PRIMARY ACTIVITIES"]
        direction TB;

        subgraph SG_INBOUND_LOGISTICS ["Inbound Logistics"]
            direction TB;
            subgraph SG_WF_MARKET_RESEARCH_ANALYSIS ["wf_market_research_analysis"]
                direction TB;
                P101["P101: Market Research & Analysis"]:::planning;
                click P101 "../../definitions/P101_Market_Research_Analysis.md" "View P101 Definition";
            end
        end

        subgraph SG_OPERATIONS ["Operations"]
            direction TB;
            subgraph SG_WF_PRODUCT_STRATEGY_ROADMAP ["wf_product_strategy_roadmap"]
                direction TB;
                P102["P102: Product Strategy & Roadmap"]:::planning;
                click P102 "../../definitions/P102_Product_Strategy_Roadmap.md" "View P102 Definition";
            end
            subgraph SG_WF_SOLUTION_DESIGN_ARCHITECTURE ["wf_solution_design_architecture"]
                direction TB;
                P103["P103: Solution Design & Architecture"]:::planning;
                click P103 "../../definitions/P103_Solution_Design_Architecture.md" "View P103 Definition";
            end
            subgraph SG_WF_DEVELOPMENT_ENGINEERING ["wf_development_engineering"]
                direction TB;
                P104["P104: Development & Engineering"]:::planning;
                click P104 "../../definitions/P104_Development_Engineering.md" "View P104 Definition";
            end
            subgraph SG_WF_QUALITY_ASSURANCE_TESTING ["wf_quality_assurance_testing"]
                direction TB;
                P105["P105: Quality Assurance & Testing"]:::planning;
                click P105 "../../definitions/P105_Quality_Assurance_Testing.md" "View P105 Definition";
            end
        end

        subgraph SG_OUTBOUND_LOGISTICS ["Outbound Logistics"]
            direction TB;
            subgraph SG_WF_DEPLOYMENT_RELEASE_MANAGEMENT ["wf_deployment_release_management"]
                direction TB;
                P106["P106: Deployment & Release Management"]:::planning;
                click P106 "../../definitions/P106_Deployment_Release_Management.md" "View P106 Definition";
            end
        end

        subgraph SG_MARKETING_SALES ["Marketing and Sales"]
            direction TB;
            subgraph SG_WF_MARKETING_SALES_STRATEGY ["wf_marketing_sales_strategy"]
                direction TB;
                P107["P107: Marketing & Sales Strategy"]:::planning;
                click P107 "../../definitions/P107_Marketing_Sales_Strategy.md" "View P107 Definition";
            end
        end

        subgraph SG_SERVICE ["Service"]
            direction TB;
            subgraph SG_WF_CUSTOMER_ONBOARDING_SUPPORT ["wf_customer_onboarding_support"]
                direction TB;
                P108["P108: Customer Onboarding & Support"]:::planning;
                click P108 "../../definitions/P108_Customer_Onboarding_Support.md" "View P108 Definition";
            end
            subgraph SG_WF_FEEDBACK_COLLECTION_ITERATION ["wf_feedback_collection_iteration"]
                direction TB;
                P109["P109: Feedback Collection & Iteration"]:::planning;
                click P109 "../../definitions/P109_Feedback_Collection_Iteration.md" "View P109 Definition";
            end
        end
    end

    subgraph SG_SUPPORT ["SUPPORT ACTIVITIES"]
        direction TB;

        subgraph SG_FIRM_INFRASTRUCTURE ["Firm Infrastructure"]
            direction TB;
            subgraph SG_WF_DEFINE_ESTRATIX_PROCESS ["wf_define_estratix_process"]
                direction TB;
                P001["P001: Define ESTRATIX Process Itself"]:::active;
                click P001 "../../definitions/P001_Define_ESTRATIX_Process_Itself.md" "View P001 Definition";
            end
            subgraph SG_WF_FINANCIAL_MANAGEMENT_ACCOUNTING ["wf_financial_management_accounting"]
                direction TB;
                P204["P204: Financial Management & Accounting"]:::planning;
                click P204 "../../definitions/P204_Financial_Management_Accounting.md" "View P204 Definition";
            end
            subgraph SG_WF_LEGAL_COMPLIANCE_MANAGEMENT ["wf_legal_compliance_management"]
                direction TB;
                P205["P205: Legal & Compliance Management"]:::planning;
                click P205 "../../definitions/P205_Legal_Compliance_Management.md" "View P205 Definition";
            end
            subgraph SG_WF_GOVERNANCE_RISK_MANAGEMENT ["wf_governance_risk_management"]
                direction TB;
                P207["P207: Governance & Risk Management"]:::planning;
                click P207 "../../definitions/P207_Governance_Risk_Management.md" "View P207 Definition";
            end
        end

        subgraph SG_HUMAN_RESOURCE_MANAGEMENT ["Human Resource Management"]
            direction TB;
            subgraph SG_WF_HUMAN_CAPITAL_MANAGEMENT ["wf_human_capital_management"]
                direction TB;
                P201["P201: Human Capital Management"]:::planning;
                click P201 "../../definitions/P201_Human_Capital_Management.md" "View P201 Definition";
            end
        end

        subgraph SG_TECHNOLOGY_DEVELOPMENT ["Technology Development"]
            direction TB;
            subgraph SG_WF_TECH_INFRA_MANAGEMENT ["wf_tech_infra_management"]
                direction TB;
                P202["P202: Technology Infrastructure Management"]:::planning;
                click P202 "../../definitions/P202_Technology_Infrastructure_Management.md" "View P202 Definition";
            end
            subgraph SG_WF_KNOWLEDGE_MANAGEMENT_RD ["wf_knowledge_management_rd"]
                direction TB;
                P206["P206: Knowledge Management & R&D"]:::planning;
                click P206 "../../definitions/P206_Knowledge_Management_RD.md" "View P206 Definition";
            end
        end

        subgraph SG_PROCUREMENT ["Procurement"]
            direction TB;
            subgraph SG_WF_PROCUREMENT_VENDOR_MANAGEMENT ["wf_procurement_vendor_management"]
                direction TB;
                P203["P203: Procurement & Vendor Management"]:::planning;
                click P203 "../../definitions/P203_Procurement_Vendor_Management.md" "View P203 Definition";
            end
        end
    end

    %% Dependencies will be added here if any are specified
