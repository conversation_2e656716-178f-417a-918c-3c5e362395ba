import { v4 as uuidv4 } from 'uuid';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';

export interface AnalyticsEvent {
  id: string;
  type: 'agent_created' | 'agent_started' | 'agent_stopped' | 'workflow_executed' | 'task_completed' | 'task_failed' | 'user_action';
  category: 'agent' | 'workflow' | 'task' | 'user' | 'system';
  action: string;
  entityId?: string;
  userId?: string;
  organizationId?: string;
  metadata: Record<string, any>;
  timestamp: Date;
}

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: 'ms' | 'count' | 'percentage' | 'bytes' | 'rate';
  category: 'performance' | 'usage' | 'error' | 'business';
  tags: Record<string, string>;
  timestamp: Date;
}

export interface AnalyticsSummary {
  totalEvents: number;
  eventsByType: Record<string, number>;
  eventsByCategory: Record<string, number>;
  averageResponseTime: number;
  errorRate: number;
  activeUsers: number;
  topActions: Array<{ action: string; count: number }>;
  timeRange: {
    start: Date;
    end: Date;
  };
}

export class AnalyticsService {
  private events: Map<string, AnalyticsEvent> = new Map();
  private metrics: Map<string, PerformanceMetric> = new Map();
  private isInitialized = false;
  private cleanupInterval?: NodeJS.Timeout;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Analytics Service...');
      
      // Start cleanup interval for old events
      this.startCleanupInterval();
      
      this.isInitialized = true;
      logger.info('Analytics Service initialized successfully');
    } catch (error) {
      logger.error(error, 'Failed to initialize Analytics Service');
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    try {
      logger.info('Cleaning up Analytics Service...');
      
      // Stop cleanup interval
      if (this.cleanupInterval) {
        clearInterval(this.cleanupInterval);
        this.cleanupInterval = undefined;
      }
      
      this.events.clear();
      this.metrics.clear();
      this.isInitialized = false;
      
      logger.info('Analytics Service cleanup completed');
    } catch (error) {
      logger.error(error, 'Error during Analytics Service cleanup');
      throw error;
    }
  }

  async getStatus(): Promise<string> {
    if (!this.isInitialized) {
      return 'initializing';
    }
    
    const eventCount = this.events.size;
    const metricCount = this.metrics.size;
    
    if (eventCount > 100000 || metricCount > 50000) {
      return 'overloaded';
    }
    
    return 'healthy';
  }

  async getMetrics(): Promise<{ totalEvents: number; totalMetrics: number; memoryUsage: number }> {
    const totalEvents = this.events.size;
    const totalMetrics = this.metrics.size;
    
    // Estimate memory usage (rough calculation)
    const avgEventSize = 500; // bytes
    const avgMetricSize = 200; // bytes
    const memoryUsage = (totalEvents * avgEventSize) + (totalMetrics * avgMetricSize);
    
    return {
      totalEvents,
      totalMetrics,
      memoryUsage
    };
  }

  // Track analytics events
  trackEvent(data: Partial<AnalyticsEvent>): void {
    const event: AnalyticsEvent = {
      id: uuidv4(),
      type: data.type || 'user_action',
      category: data.category || 'user',
      action: data.action || 'unknown',
      entityId: data.entityId,
      userId: data.userId,
      organizationId: data.organizationId,
      metadata: data.metadata || {},
      timestamp: new Date()
    };

    this.events.set(event.id, event);
    
    logger.debug(`Analytics event tracked: ${event.type} - ${event.action}`);
  }

  // Track performance metrics
  trackMetric(data: Partial<PerformanceMetric>): void {
    const metric: PerformanceMetric = {
      id: uuidv4(),
      name: data.name || 'unknown_metric',
      value: data.value || 0,
      unit: data.unit || 'count',
      category: data.category || 'performance',
      tags: data.tags || {},
      timestamp: new Date()
    };

    this.metrics.set(metric.id, metric);
    
    logger.debug(`Performance metric tracked: ${metric.name} = ${metric.value}${metric.unit}`);
  }

  // Get analytics summary for a time range
  async getAnalyticsSummary(startDate: Date, endDate: Date, organizationId?: string): Promise<AnalyticsSummary> {
    const events = Array.from(this.events.values())
      .filter(event => {
        const inTimeRange = event.timestamp >= startDate && event.timestamp <= endDate;
        const inOrganization = !organizationId || event.organizationId === organizationId;
        return inTimeRange && inOrganization;
      });

    const totalEvents = events.length;
    
    // Events by type
    const eventsByType: Record<string, number> = {};
    events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
    });
    
    // Events by category
    const eventsByCategory: Record<string, number> = {};
    events.forEach(event => {
      eventsByCategory[event.category] = (eventsByCategory[event.category] || 0) + 1;
    });
    
    // Calculate average response time from performance metrics
    const responseTimeMetrics = Array.from(this.metrics.values())
      .filter(metric => {
        const inTimeRange = metric.timestamp >= startDate && metric.timestamp <= endDate;
        const isResponseTime = metric.name.includes('response_time') || metric.name.includes('duration');
        return inTimeRange && isResponseTime;
      });
    
    const averageResponseTime = responseTimeMetrics.length > 0
      ? responseTimeMetrics.reduce((sum, metric) => sum + metric.value, 0) / responseTimeMetrics.length
      : 0;
    
    // Calculate error rate
    const errorEvents = events.filter(event => event.type.includes('failed') || event.action.includes('error'));
    const errorRate = totalEvents > 0 ? (errorEvents.length / totalEvents) * 100 : 0;
    
    // Count active users
    const uniqueUsers = new Set(events.map(event => event.userId).filter(Boolean));
    const activeUsers = uniqueUsers.size;
    
    // Top actions
    const actionCounts: Record<string, number> = {};
    events.forEach(event => {
      actionCounts[event.action] = (actionCounts[event.action] || 0) + 1;
    });
    
    const topActions = Object.entries(actionCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([action, count]) => ({ action, count }));
    
    return {
      totalEvents,
      eventsByType,
      eventsByCategory,
      averageResponseTime,
      errorRate,
      activeUsers,
      topActions,
      timeRange: {
        start: startDate,
        end: endDate
      }
    };
  }

  // Get events for a specific entity
  async getEntityEvents(entityId: string, entityType?: string): Promise<AnalyticsEvent[]> {
    return Array.from(this.events.values())
      .filter(event => {
        const matchesEntity = event.entityId === entityId;
        const matchesType = !entityType || event.type.includes(entityType);
        return matchesEntity && matchesType;
      })
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Get metrics for a specific name pattern
  async getMetricsByName(namePattern: string, startDate?: Date, endDate?: Date): Promise<PerformanceMetric[]> {
    return Array.from(this.metrics.values())
      .filter(metric => {
        const matchesName = metric.name.includes(namePattern);
        const inTimeRange = !startDate || !endDate || 
          (metric.timestamp >= startDate && metric.timestamp <= endDate);
        return matchesName && inTimeRange;
      })
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Track agent lifecycle events
  trackAgentEvent(agentId: string, action: string, metadata?: Record<string, any>): void {
    this.trackEvent({
      type: action.includes('create') ? 'agent_created' : 
            action.includes('start') ? 'agent_started' : 
            action.includes('stop') ? 'agent_stopped' : 'user_action',
      category: 'agent',
      action,
      entityId: agentId,
      metadata
    });
  }

  // Track workflow events
  trackWorkflowEvent(workflowId: string, action: string, metadata?: Record<string, any>): void {
    this.trackEvent({
      type: 'workflow_executed',
      category: 'workflow',
      action,
      entityId: workflowId,
      metadata
    });
  }

  // Track task events
  trackTaskEvent(taskId: string, action: string, metadata?: Record<string, any>): void {
    this.trackEvent({
      type: action.includes('completed') ? 'task_completed' : 
            action.includes('failed') ? 'task_failed' : 'user_action',
      category: 'task',
      action,
      entityId: taskId,
      metadata
    });
  }

  // Track performance metrics with common patterns
  trackResponseTime(operation: string, duration: number, tags?: Record<string, string>): void {
    this.trackMetric({
      name: `response_time_${operation}`,
      value: duration,
      unit: 'ms',
      category: 'performance',
      tags: { operation, ...tags }
    });
  }

  trackCounter(name: string, value: number = 1, tags?: Record<string, string>): void {
    this.trackMetric({
      name,
      value,
      unit: 'count',
      category: 'usage',
      tags
    });
  }

  trackGauge(name: string, value: number, unit: 'count' | 'percentage' | 'bytes' = 'count', tags?: Record<string, string>): void {
    this.trackMetric({
      name,
      value,
      unit,
      category: 'usage',
      tags
    });
  }

  private startCleanupInterval(): void {
    // Clean up old events and metrics every hour
    this.cleanupInterval = setInterval(() => {
      this.cleanupOldData();
    }, 60 * 60 * 1000); // 1 hour
  }

  private cleanupOldData(): void {
    const cutoffDate = new Date(Date.now() - (7 * 24 * 60 * 60 * 1000)); // 7 days ago
    
    // Clean up old events
    let eventsRemoved = 0;
    for (const [id, event] of this.events.entries()) {
      if (event.timestamp < cutoffDate) {
        this.events.delete(id);
        eventsRemoved++;
      }
    }
    
    // Clean up old metrics
    let metricsRemoved = 0;
    for (const [id, metric] of this.metrics.entries()) {
      if (metric.timestamp < cutoffDate) {
        this.metrics.delete(id);
        metricsRemoved++;
      }
    }
    
    if (eventsRemoved > 0 || metricsRemoved > 0) {
      logger.info(`Cleaned up ${eventsRemoved} old events and ${metricsRemoved} old metrics`);
    }
  }
}