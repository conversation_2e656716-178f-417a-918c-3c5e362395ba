# ESTRATIX User & SME Matrix

**Objective**: This matrix serves as the central registry for all key personnel and subject matter experts (SMEs) within the ESTRATIX ecosystem. It makes human knowledge discoverable and integrates it directly into the research pipeline, treating internal expertise as a formal, traceable knowledge source.

**Scope**: This matrix includes all individuals whose expertise is leveraged in research, development, and strategic decision-making. It links users to the topics they inform (`topic_matrix.md`) and the research batches they contribute to (`research_matrix.md`).

---

## User & SME Management Lifecycle

1. **Onboarding**: A new user or SME is added to the matrix with a unique `User ID`.
2. **Expertise Profiling**: Their `Areas of Expertise` are documented using standardized keywords, and they are linked to relevant `Command Offices`.
3. **Engagement**: When their expertise is required, they are linked to a `Topic ID` or assigned to a `Research Batch ID`.
4. **Contribution Tracking**: Their involvement is implicitly tracked through their association with research and project outcomes.

---

## User & SME Matrix

| User ID | User Name | Role / Title | Command Office | Areas of Expertise (Keywords) | Related Topic ID(s) | Related Research Batch ID(s) | Status | Notes |
|---|---|---|---|---|---|---|---|---|
| `USR-001` | John Doe | Principal AI Engineer | CTO | `Agentic AI`, `RAG`, `LLM Tooling`, `Vector DBs` | `TOP-001`, `TOP-002`, `TOP-003` | `RB-************` | `Active` | Go-to expert for agentic framework architecture and implementation. |
| `USR-002` | Jane Smith | Lead DevOps Engineer | CTO | `IaC`, `Kubernetes`, `CI/CD`, `Observability` | `TOP-001` | `N/A` | `Active` | Manages infrastructure and deployment standards. |
| `USR-003` | Bob Johnson | Senior Knowledge Architect | CIO | `Knowledge Graphs`, `ETL`, `Data Modeling`, `Ontology` | `TOP-003` | `RB-************` | `Active` | Designs and oversees the knowledge lifecycle and data pipelines. |
| `USR-004` | Alice Williams | Lead Process Analyst | CPO | `Business Process Modeling`, `Sales Funnels`, `Automation` | `TOP-004` | `RB-************` | `Active` | Specializes in optimizing business workflows and automation strategies. |

---

## Guidance for Use

- **Discoverability**: Use this matrix to identify and engage the right internal experts for new research topics and projects.
- **Traceability**: When an SME is consulted or involved in a research batch, ensure their `User ID` is referenced in the `research_matrix.md`.
- **Maintain Accuracy**: Keep user roles and areas of expertise up-to-date to ensure the matrix remains a reliable resource.
