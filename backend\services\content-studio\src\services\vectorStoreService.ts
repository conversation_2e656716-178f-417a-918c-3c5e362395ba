import { MilvusClient, DataType, MetricType } from '@zilliz/milvus2-sdk-node';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

export interface VectorDocument {
  id: string;
  content: string;
  metadata: {
    type: 'content' | 'template' | 'brand_guideline' | 'keyword' | 'audience_insight';
    title?: string;
    category?: string;
    tags?: string[];
    platform?: string;
    language?: string;
    createdAt: string;
    updatedAt: string;
    userId: string;
    [key: string]: any;
  };
  embedding?: number[];
}

export interface SearchResult {
  id: string;
  content: string;
  metadata: VectorDocument['metadata'];
  score: number;
  distance: number;
}

export interface SearchOptions {
  limit?: number;
  threshold?: number;
  filters?: {
    type?: string[];
    category?: string[];
    tags?: string[];
    platform?: string[];
    language?: string[];
    userId?: string;
    dateRange?: {
      start: string;
      end: string;
    };
  };
}

export class VectorStoreService {
  private client: MilvusClient;
  private collectionName = 'content_vectors';
  private isConnected = false;
  private embeddingDimension = 1536; // OpenAI text-embedding-ada-002 dimension

  constructor() {
    this.client = new MilvusClient({
      address: config.vectorDb.host,
      username: config.vectorDb.username,
      password: config.vectorDb.password,
      ssl: config.vectorDb.ssl,
    });
  }

  async initialize(): Promise<void> {
    try {
      // In development mode, make vector store optional
      if (config.nodeEnv === 'development') {
        logger.warn('Vector store service disabled in development mode');
        return;
      }
      
      // Connect to Milvus
      await this.connect();
      
      // Create collection if it doesn't exist
      await this.createCollectionIfNotExists();
      
      // Load collection
      await this.loadCollection();
      
      logger.info('Vector store service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize vector store service:', error);
      if (config.nodeEnv === 'production') {
        throw error;
      } else {
        logger.warn('Vector store service disabled due to connection failure in development');
      }
    }
  }

  private async connect(): Promise<void> {
    try {
      const health = await this.client.checkHealth();
      if (health.isHealthy) {
        this.isConnected = true;
        logger.info('Connected to Milvus vector database');
      } else {
        throw new Error('Milvus health check failed');
      }
    } catch (error) {
      logger.error('Failed to connect to Milvus:', error);
      throw error;
    }
  }

  private async createCollectionIfNotExists(): Promise<void> {
    try {
      const collections = await this.client.listCollections();
      const exists = collections.collection_names?.includes(this.collectionName);

      if (!exists) {
        await this.client.createCollection({
          collection_name: this.collectionName,
          description: 'Content vectors for AI-powered content generation',
          fields: [
            {
              name: 'id',
              description: 'Document ID',
              data_type: DataType.VarChar,
              max_length: 100,
              is_primary_key: true,
            },
            {
              name: 'content',
              description: 'Document content',
              data_type: DataType.VarChar,
              max_length: 65535,
            },
            {
              name: 'embedding',
              description: 'Content embedding vector',
              data_type: DataType.FloatVector,
              dim: this.embeddingDimension,
            },
            {
              name: 'type',
              description: 'Document type',
              data_type: DataType.VarChar,
              max_length: 50,
            },
            {
              name: 'title',
              description: 'Document title',
              data_type: DataType.VarChar,
              max_length: 500,
            },
            {
              name: 'category',
              description: 'Document category',
              data_type: DataType.VarChar,
              max_length: 100,
            },
            {
              name: 'platform',
              description: 'Target platform',
              data_type: DataType.VarChar,
              max_length: 50,
            },
            {
              name: 'language',
              description: 'Content language',
              data_type: DataType.VarChar,
              max_length: 10,
            },
            {
              name: 'userId',
              description: 'User ID',
              data_type: DataType.VarChar,
              max_length: 100,
            },
            {
              name: 'createdAt',
              description: 'Creation timestamp',
              data_type: DataType.VarChar,
              max_length: 50,
            },
            {
              name: 'updatedAt',
              description: 'Update timestamp',
              data_type: DataType.VarChar,
              max_length: 50,
            },
            {
              name: 'tags',
              description: 'Document tags (JSON)',
              data_type: DataType.VarChar,
              max_length: 1000,
            },
            {
              name: 'metadata',
              description: 'Additional metadata (JSON)',
              data_type: DataType.VarChar,
              max_length: 5000,
            },
          ],
        });

        // Create index for vector search
        await this.client.createIndex({
          collection_name: this.collectionName,
          field_name: 'embedding',
          index_type: 'IVF_FLAT',
          metric_type: MetricType.COSINE,
          params: { nlist: 1024 },
        });

        logger.info(`Created collection: ${this.collectionName}`);
      }
    } catch (error) {
      logger.error('Failed to create collection:', error);
      throw error;
    }
  }

  private async loadCollection(): Promise<void> {
    try {
      await this.client.loadCollection({
        collection_name: this.collectionName,
      });
      logger.info(`Loaded collection: ${this.collectionName}`);
    } catch (error) {
      logger.error('Failed to load collection:', error);
      throw error;
    }
  }

  async addDocument(document: VectorDocument): Promise<void> {
    try {
      if (!document.embedding) {
        throw new Error('Document must have an embedding vector');
      }

      const data = {
        id: document.id,
        content: document.content,
        embedding: document.embedding,
        type: document.metadata.type,
        title: document.metadata.title || '',
        category: document.metadata.category || '',
        platform: document.metadata.platform || '',
        language: document.metadata.language || 'en',
        userId: document.metadata.userId,
        createdAt: document.metadata.createdAt,
        updatedAt: document.metadata.updatedAt,
        tags: JSON.stringify(document.metadata.tags || []),
        metadata: JSON.stringify(document.metadata),
      };

      await this.client.insert({
        collection_name: this.collectionName,
        data: [data],
      });

      logger.debug(`Added document to vector store: ${document.id}`);
    } catch (error) {
      logger.error('Failed to add document to vector store:', error);
      throw error;
    }
  }

  async addDocuments(documents: VectorDocument[]): Promise<void> {
    try {
      const data = documents.map(doc => {
        if (!doc.embedding) {
          throw new Error(`Document ${doc.id} must have an embedding vector`);
        }

        return {
          id: doc.id,
          content: doc.content,
          embedding: doc.embedding,
          type: doc.metadata.type,
          title: doc.metadata.title || '',
          category: doc.metadata.category || '',
          platform: doc.metadata.platform || '',
          language: doc.metadata.language || 'en',
          userId: doc.metadata.userId,
          createdAt: doc.metadata.createdAt,
          updatedAt: doc.metadata.updatedAt,
          tags: JSON.stringify(doc.metadata.tags || []),
          metadata: JSON.stringify(doc.metadata),
        };
      });

      await this.client.insert({
        collection_name: this.collectionName,
        data,
      });

      logger.info(`Added ${documents.length} documents to vector store`);
    } catch (error) {
      logger.error('Failed to add documents to vector store:', error);
      throw error;
    }
  }

  async searchSimilar(
    queryEmbedding: number[],
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    try {
      const {
        limit = 10,
        threshold = 0.7,
        filters = {},
      } = options;

      // Build filter expression
      let filterExpr = '';
      const conditions: string[] = [];

      if (filters.type && filters.type.length > 0) {
        const typeConditions = filters.type.map(t => `type == "${t}"`).join(' or ');
        conditions.push(`(${typeConditions})`);
      }

      if (filters.category && filters.category.length > 0) {
        const categoryConditions = filters.category.map(c => `category == "${c}"`).join(' or ');
        conditions.push(`(${categoryConditions})`);
      }

      if (filters.platform && filters.platform.length > 0) {
        const platformConditions = filters.platform.map(p => `platform == "${p}"`).join(' or ');
        conditions.push(`(${platformConditions})`);
      }

      if (filters.language && filters.language.length > 0) {
        const languageConditions = filters.language.map(l => `language == "${l}"`).join(' or ');
        conditions.push(`(${languageConditions})`);
      }

      if (filters.userId) {
        conditions.push(`userId == "${filters.userId}"`);
      }

      if (conditions.length > 0) {
        filterExpr = conditions.join(' and ');
      }

      const searchParams = {
        collection_name: this.collectionName,
        vector: queryEmbedding,
        filter: filterExpr || undefined,
        limit,
        metric_type: MetricType.COSINE,
        params: { nprobe: 10 },
        output_fields: [
          'id',
          'content',
          'type',
          'title',
          'category',
          'platform',
          'language',
          'userId',
          'createdAt',
          'updatedAt',
          'tags',
          'metadata',
        ],
      };

      const results = await this.client.search(searchParams);

      return results.results.map((result: any) => {
        const metadata = {
          type: result.type,
          title: result.title,
          category: result.category,
          platform: result.platform,
          language: result.language,
          userId: result.userId,
          createdAt: result.createdAt,
          updatedAt: result.updatedAt,
          tags: JSON.parse(result.tags || '[]'),
          ...JSON.parse(result.metadata || '{}'),
        };

        return {
          id: result.id,
          content: result.content,
          metadata,
          score: result.score,
          distance: result.distance,
        };
      }).filter((result: SearchResult) => result.score >= threshold);
    } catch (error) {
      logger.error('Failed to search similar documents:', error);
      throw error;
    }
  }

  async searchByText(
    query: string,
    queryEmbedding: number[],
    options: SearchOptions = {}
  ): Promise<SearchResult[]> {
    try {
      // First, perform vector similarity search
      const vectorResults = await this.searchSimilar(queryEmbedding, options);

      // Then, perform text-based filtering if needed
      const queryLower = query.toLowerCase();
      const textFilteredResults = vectorResults.filter(result => {
        const contentMatch = result.content.toLowerCase().includes(queryLower);
        const titleMatch = result.metadata.title?.toLowerCase().includes(queryLower);
        const tagsMatch = result.metadata.tags?.some((tag: string) => 
          tag.toLowerCase().includes(queryLower)
        );
        
        return contentMatch || titleMatch || tagsMatch;
      });

      // If text filtering returns results, use those; otherwise, use vector results
      return textFilteredResults.length > 0 ? textFilteredResults : vectorResults;
    } catch (error) {
      logger.error('Failed to search by text:', error);
      throw error;
    }
  }

  async updateDocument(document: VectorDocument): Promise<void> {
    try {
      // Delete existing document
      await this.deleteDocument(document.id);
      
      // Add updated document
      await this.addDocument(document);
      
      logger.debug(`Updated document in vector store: ${document.id}`);
    } catch (error) {
      logger.error('Failed to update document in vector store:', error);
      throw error;
    }
  }

  async deleteDocument(id: string): Promise<void> {
    try {
      await this.client.delete({
        collection_name: this.collectionName,
        filter: `id == "${id}"`,
      });
      
      logger.debug(`Deleted document from vector store: ${id}`);
    } catch (error) {
      logger.error('Failed to delete document from vector store:', error);
      throw error;
    }
  }

  async deleteDocuments(ids: string[]): Promise<void> {
    try {
      const filterExpr = ids.map(id => `id == "${id}"`).join(' or ');
      
      await this.client.delete({
        collection_name: this.collectionName,
        filter: filterExpr,
      });
      
      logger.info(`Deleted ${ids.length} documents from vector store`);
    } catch (error) {
      logger.error('Failed to delete documents from vector store:', error);
      throw error;
    }
  }

  async getDocument(id: string): Promise<VectorDocument | null> {
    try {
      const results = await this.client.query({
        collection_name: this.collectionName,
        filter: `id == "${id}"`,
        output_fields: [
          'id',
          'content',
          'type',
          'title',
          'category',
          'platform',
          'language',
          'userId',
          'createdAt',
          'updatedAt',
          'tags',
          'metadata',
        ],
      });

      if (results.data.length === 0) {
        return null;
      }

      const result = results.data[0];
      const metadata = {
        type: result.type,
        title: result.title,
        category: result.category,
        platform: result.platform,
        language: result.language,
        userId: result.userId,
        createdAt: result.createdAt,
        updatedAt: result.updatedAt,
        tags: JSON.parse(result.tags || '[]'),
        ...JSON.parse(result.metadata || '{}'),
      };

      return {
        id: result.id,
        content: result.content,
        metadata,
      };
    } catch (error) {
      logger.error('Failed to get document from vector store:', error);
      throw error;
    }
  }

  async getCollectionStats(): Promise<{
    totalDocuments: number;
    documentsByType: Record<string, number>;
    documentsByCategory: Record<string, number>;
    documentsByPlatform: Record<string, number>;
    documentsByLanguage: Record<string, number>;
  }> {
    try {
      const stats = await this.client.getCollectionStatistics({
        collection_name: this.collectionName,
      });

      // Get document counts by different fields
      const typeStats = await this.client.query({
        collection_name: this.collectionName,
        output_fields: ['type'],
      });

      const categoryStats = await this.client.query({
        collection_name: this.collectionName,
        output_fields: ['category'],
      });

      const platformStats = await this.client.query({
        collection_name: this.collectionName,
        output_fields: ['platform'],
      });

      const languageStats = await this.client.query({
        collection_name: this.collectionName,
        output_fields: ['language'],
      });

      // Count occurrences
      const documentsByType: Record<string, number> = {};
      const documentsByCategory: Record<string, number> = {};
      const documentsByPlatform: Record<string, number> = {};
      const documentsByLanguage: Record<string, number> = {};

      typeStats.data.forEach((doc: any) => {
        documentsByType[doc.type] = (documentsByType[doc.type] || 0) + 1;
      });

      categoryStats.data.forEach((doc: any) => {
        if (doc.category) {
          documentsByCategory[doc.category] = (documentsByCategory[doc.category] || 0) + 1;
        }
      });

      platformStats.data.forEach((doc: any) => {
        if (doc.platform) {
          documentsByPlatform[doc.platform] = (documentsByPlatform[doc.platform] || 0) + 1;
        }
      });

      languageStats.data.forEach((doc: any) => {
        documentsByLanguage[doc.language] = (documentsByLanguage[doc.language] || 0) + 1;
      });

      return {
        totalDocuments: parseInt(stats.row_count),
        documentsByType,
        documentsByCategory,
        documentsByPlatform,
        documentsByLanguage,
      };
    } catch (error) {
      logger.error('Failed to get collection stats:', error);
      throw error;
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      // In development mode, return true if vector store is disabled
      if (config.nodeEnv === 'development' && !this.isConnected) {
        return true;
      }
      
      const health = await this.client.checkHealth();
      return health.isHealthy && this.isConnected;
    } catch (error) {
      logger.error('Vector store health check failed:', error);
      // In development mode, return true even if health check fails
      return config.nodeEnv === 'development';
    }
  }

  async disconnect(): Promise<void> {
    try {
      if (this.isConnected) {
        await this.client.closeConnection();
        this.isConnected = false;
        logger.info('Disconnected from vector store');
      } else if (config.nodeEnv === 'development') {
        logger.info('Vector store was not connected (development mode)');
      }
    } catch (error) {
      logger.error('Failed to disconnect from vector store:', error);
    }
  }
}

// Export singleton instance
export const vectorStoreService = new VectorStoreService();