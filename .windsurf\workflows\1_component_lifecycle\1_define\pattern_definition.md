---
description: "Guides the definition of a new ESTRATIX Pattern, from conceptualization to formal registration in the pattern_matrix.md."
---

# Workflow: Define New ESTRATIX Pattern

## Objective

To produce a comprehensive and standardized definition document for a reusable ESTRATIX Pattern, which orchestrates multiple components (processes, flows, agents, etc.) to achieve a specific, complex objective. The definition will be stored in `docs/patterns/[Pattern_ID]_[PatternName_PascalCase]/`.

## Trigger

A recurring business problem or operational capability is identified that can be solved by a standard, orchestrated set of actions.

## Responsible Command Office (Lead)

CPO (for business process patterns), CTO (for technology/development patterns), or other relevant Command Officer.

## Dependencies

* `docs/models/pattern_matrix.md`
* `docs/models/process_matrix.md`
* `docs/models/flow_matrix.md`
* `docs/models/service_matrix.md`
* `docs/models/agent_matrix.md`
* `docs/templates/estratix_pattern_template.md`

## Outputs

* A new definition file: `docs/patterns/[Pattern_ID]_[PatternName_PascalCase]/[Pattern_ID]_[PatternName_PascalCase]_Definition.md`
* An updated entry in `docs/models/pattern_matrix.md`

## Steps

1. **Initiation & Identification**
   * **Action**: An agent or user identifies a candidate for a new pattern. This could be a recurring project type, a complex internal operation, or a core part of a productized service.
   * **Input**: A clear problem statement and the proposed name for the pattern (e.g., "Client Project Bootstrap").
   * **Verification**: Check `docs/models/pattern_matrix.md` to ensure a similar pattern does not already exist.

2. **Register Pattern & Reserve ID (Atomic Operation)**
   * **Action (Automated)**: An agent programmatically reserves a unique ID and registers the pattern.
   * **Logic**:
     1. Read `docs/models/pattern_matrix.md`.
     2. Determine the next available `Pattern_ID` (e.g., if the last ID is `pt001`, the new one is `pt002`).
     3. Add a new row to the matrix with the new `Pattern_ID`, the `Pattern Name`, a placeholder `Description`, and set the `Status` to `Pending Definition`.
     4. Save the updated `docs/models/pattern_matrix.md`.
   * **Output**: The newly reserved `Pattern_ID` (e.g., `pt002`).

3. **Create Definition File from Template**
   * **Action (Automated)**:
     1. Create the target directory: `docs/patterns/[Pattern_ID]_[PatternName_PascalCase]/`.
     2. Copy the template `docs/templates/estratix_pattern_template.md` to the target file: `[Pattern_ID]_[PatternName_PascalCase]_Definition.md`.
     3. Replace placeholders like `[Pattern_ID]` and `[PatternName]` in the new file.
   * **Tooling**: File system operations, text replacement.

4. **Populate Pattern Definition**
   * **Action (User/Agent)**: Open the newly created definition file and populate all sections based on the template's guidance (Objective, Scope, Component Orchestration, Data Flow, etc.).

5. **Review and Finalize**
   * **Action**: Review the populated definition for clarity, completeness, and feasibility.
   * **Input**: Feedback from relevant Command Officers (e.g., CTO, CPO).
   * **Output**: A finalized definition document.

6. **Update Matrix with Definition Link**
   * **Action (Automated)**: Upon finalization, an agent updates the pattern's entry in the matrix.
   * **Logic**:
     1. Open `docs/models/pattern_matrix.md`.
     2. Locate the row corresponding to the `Pattern_ID`.
     3. Update the `Description` with the final summary from the definition file.
     4. Set the `Definition Link` to point to the new definition file.
     5. Update the `Status` from `Pending Definition` to `Defined`.
   * **Tooling**: Markdown table manipulation script.

---

## Next Steps

* **Generate the Pattern**: Once defined, use the `/1_component_lifecycle/2_generate/pattern_generation.md` workflow to create the framework-specific implementation.
* **Create Template**: Create the `docs/templates/estratix_pattern_template.md` if it doesn't exist.
