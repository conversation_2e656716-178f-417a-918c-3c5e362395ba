version: '3.8'

services:
  # Frontend React Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_WALLETCONNECT_PROJECT_ID=${VITE_WALLETCONNECT_PROJECT_ID}
        - VITE_ALCHEMY_API_KEY=${VITE_ALCHEMY_API_KEY}
        - VITE_INFURA_API_KEY=${VITE_INFURA_API_KEY}
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_CHAIN_ID=${VITE_CHAIN_ID}
        - VITE_CONTRACT_ADDRESS=${VITE_CONTRACT_ADDRESS}
        - VITE_LUX_TOKEN_ADDRESS=${VITE_LUX_TOKEN_ADDRESS}
    ports:
      - "80:80"
      - "443:443"
    environment:
      - NODE_ENV=production
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API Services
  backend:
    image: node:20-alpine
    working_dir: /app
    command: sh -c "npm install && npm run start:prod"
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN}
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_S3_BUCKET=${AWS_S3_BUCKET}
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - BLOCKCHAIN_RPC_URL=${BLOCKCHAIN_RPC_URL}
      - PRIVATE_KEY=${PRIVATE_KEY}
    volumes:
      - ./backend:/app
      - /app/node_modules
    depends_on:
      - postgres
      - redis
      - mongodb
    restart: unless-stopped
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-luxcrafts}
      - POSTGRES_USER=${POSTGRES_USER:-luxcrafts}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-luxcrafts}"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB for Analytics and Logs
  mongodb:
    image: mongo:7-jammy
    environment:
      - MONGO_INITDB_ROOT_USERNAME=${MONGO_ROOT_USERNAME:-admin}
      - MONGO_INITDB_ROOT_PASSWORD=${MONGO_ROOT_PASSWORD}
      - MONGO_INITDB_DATABASE=${MONGO_DATABASE:-luxcrafts_analytics}
    volumes:
      - mongodb_data:/data/db
      - ./database/mongo-init:/docker-entrypoint-initdb.d
    ports:
      - "27017:27017"
    restart: unless-stopped
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    restart: unless-stopped
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Blockchain Node (Optional - for private network)
  blockchain-node:
    image: ethereum/client-go:latest
    command: >
      geth --dev
      --http --http.addr 0.0.0.0 --http.port 8545
      --http.corsdomain "*" --http.api "eth,net,web3,personal,miner"
      --ws --ws.addr 0.0.0.0 --ws.port 8546
      --ws.origins "*" --ws.api "eth,net,web3,personal,miner"
      --allow-insecure-unlock
    ports:
      - "8545:8545"
      - "8546:8546"
    volumes:
      - blockchain_data:/root/.ethereum
    restart: unless-stopped
    networks:
      - luxcrafts-network
    profiles:
      - blockchain

  # IPFS Node for Decentralized Storage
  ipfs:
    image: ipfs/kubo:latest
    ports:
      - "4001:4001"
      - "5001:5001"
      - "8080:8080"
    volumes:
      - ipfs_data:/data/ipfs
    restart: unless-stopped
    networks:
      - luxcrafts-network
    profiles:
      - ipfs

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - luxcrafts-network
    profiles:
      - monitoring

  # Grafana for Visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - luxcrafts-network
    profiles:
      - monitoring

  # Nginx Load Balancer (for multiple backend instances)
  load-balancer:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx/load-balancer.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - luxcrafts-network
    profiles:
      - load-balancer

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
  blockchain_data:
  ipfs_data:
  prometheus_data:
  grafana_data:

networks:
  luxcrafts-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16