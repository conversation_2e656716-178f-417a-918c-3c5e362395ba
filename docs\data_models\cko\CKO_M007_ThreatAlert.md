# CKO_M007: ThreatAlert

## 1. Metadata

*   **Data Model ID:** CKO_M007
*   **Data Model Name:** ThreatAlert
*   **Version:** 1.1
*   **Status:** Definition
*   **Last Updated:** 2025-05-27
*   **Owner Command Office:** CKO
*   **Primary Contact/SME:** CKO_A004_KnowledgeAnalystAgent, Chief Risk Officer (CRO) - (Conceptual, as CRO role is defined)

## 2. Purpose

*   This data model represents a concise, actionable alert highlighting a potential threat or risk to ESTRATIX. Threats could relate to market changes, competitor actions, technological disruptions, regulatory shifts, cybersecurity vulnerabilities, or operational weaknesses. It is a key output of `CKO_F002_KnowledgeAnalysisAndInsightGeneration` and an input for strategic planning, risk management processes, and potentially `CPO_F001_StrategicOpportunityToProposalDevelopment` (for mitigation proposals).

## 3. Pydantic Model Definition

```python
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, HttpUrl
from datetime import datetime, timedelta
from enum import Enum
import uuid

class CKO_ThreatTypeEnum(str, Enum):
    COMPETITOR_ACTION = "Competitor Action"
    MARKET_SHIFT = "Market Shift/Disruption"
    TECHNOLOGICAL_DISRUPTION = "Technological Disruption"
    REGULATORY_CHANGE_RISK = "Regulatory Change Risk"
    CYBERSECURITY_VULNERABILITY = "Cybersecurity Vulnerability"
    OPERATIONAL_RISK = "Operational Risk"
    SUPPLY_CHAIN_DISRUPTION = "Supply Chain Disruption"
    GEOPOLITICAL_RISK = "Geopolitical Risk"
    REPUTATIONAL_RISK = "Reputational Risk"
    FINANCIAL_RISK = "Financial Risk"
    OTHER = "Other"

class CKO_AlertStatusEnum(str, Enum):
    NEW = "New"
    UNDER_ASSESSMENT = "Under Assessment (by CRO/Relevant CO)"
    MITIGATION_PLAN_IN_DEVELOPMENT = "Mitigation Plan in Development"
    ACTIONED = "Actioned (Mitigation Implemented/Proposal Submitted)"
    MONITORING = "Monitoring"
    CLOSED = "Closed (Resolved/Accepted Risk)"
    ARCHIVED = "Archived"

class CKO_SeverityLevelEnum(str, Enum):
    CRITICAL = "Critical"
    HIGH = "High"
    MEDIUM = "Medium"
    LOW = "Low"
    INFORMATIONAL = "Informational"

class CKO_ThreatAlert(BaseModel):
    alert_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="Unique identifier for the threat alert.")
    title: str = Field(..., description="Concise title summarizing the threat.")
    description: str = Field(..., description="Detailed description of the threat, its context, and potential negative impacts on ESTRATIX.")
    threat_type: CKO_ThreatTypeEnum = Field(..., description="Categorization of the threat.")
    
    # Supporting Evidence & Context
    source_insight_report_id: Optional[uuid.UUID] = Field(None, description="ID of the CKO_M005_InsightReport from which this alert was primarily derived, if any.")
    source_asset_ids: List[uuid.UUID] = Field(default_factory=list, description="List of key CKO_M003_CuratedKnowledgeAsset IDs supporting this alert.")
    source_graph_node_ids: Optional[List[uuid.UUID]] = Field(default_factory=list, description="Key CKO_M008_KnowledgeGraphNode IDs related to this threat.")
    
    # Assessment & Prioritization
    potential_negative_impact: str = Field(..., description="Qualitative assessment of the potential negative impact on ESTRATIX (e.g., revenue loss, market share decline, operational disruption, reputational damage).")
    likelihood_score: float = Field(..., ge=0.0, le=1.0, description="Assessed likelihood (0.0-1.0) of the threat materializing.")
    severity_level: CKO_SeverityLevelEnum = Field(..., description="Assessed severity of the threat if it materializes.")
    time_horizon: Optional[timedelta] = Field(None, description="Estimated timeframe in which the threat might materialize or become critical.")
    existing_mitigations: Optional[List[str]] = Field(default_factory=list, description="Any existing ESTRATIX controls or mitigations relevant to this threat.")
    
    # Origination & Status
    generated_by_agent_id: str = Field(..., description="ID of the ESTRATIX agent (e.g., CKO_A004_KnowledgeAnalystAgent) that generated this alert.")
    generation_date: datetime = Field(default_factory=datetime.utcnow, description="Date the alert was generated.")
    alert_status: CKO_AlertStatusEnum = Field(default=CKO_AlertStatusEnum.NEW, description="Current status of the threat alert.")
    status_last_updated: datetime = Field(default_factory=datetime.utcnow, description="Timestamp of the last status update.")
    assigned_to_co_office: Optional[str] = Field(None, description="Command Office primarily responsible for assessment (e.g., CRO, CSO). Default could be CRO.")
    assessment_notes: Optional[str] = Field(None, description="Notes from the assessment process by CRO or other stakeholders.")
    
    # Link to Action
    related_mitigation_proposal_id: Optional[uuid.UUID] = Field(None, description="ID of a proposal developed to mitigate this threat, if applicable.")
    related_risk_register_id: Optional[str] = Field(None, description="ID or link to an entry in a formal Risk Register, if applicable.")

    custom_fields: Optional[Dict[str, Any]] = Field(None, description="Flexible dictionary for additional, alert-specific metadata.")

## 4. Field Descriptions

| Field Name                        | Type                             | Description                                                                                                 | Required | Example Value(s)                                                               |
|-----------------------------------|----------------------------------|-------------------------------------------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------|
| `alert_id`                        | `uuid.UUID`                      | Unique identifier for the threat alert.                                                                     | Yes      | `"def456gh-789i-jklm-nopq-rstuvwxyz123"`                                       |
| `title`                           | `str`                            | Concise title summarizing the threat.                                                                       | Yes      | `"New Competitor X Launching Disruptive Product Y"`                         |
| `description`                     | `str`                            | Detailed description of the threat, context, and potential negative impacts.                                | Yes      | `"Competitor X, known for aggressive pricing, is launching Product Y..."`     |
| `threat_type`                     | `CKO_ThreatTypeEnum`             | Categorization of the threat.                                                                              | Yes      | `"Competitor Action"`                                                         |
| `source_insight_report_id`        | `Optional[uuid.UUID]`            | ID of the `CKO_M005_InsightReport` if derived from one.                                                    | No       | `"report789-uuid-goes-here"`                                                |
| `source_asset_ids`                | `List[uuid.UUID]`                | List of key `CKO_M003_CuratedKnowledgeAsset` IDs supporting this alert.                                    | Yes      | `["assetABC-uuid", "assetDEF-uuid"]`                                        |
| `source_graph_node_ids`           | `Optional[List[uuid.UUID]]`      | Key `CKO_M008_KnowledgeGraphNode` IDs related to this threat.                                               | No       | `["node123-uuid", "node456-uuid"]`                                          |
| `potential_negative_impact`       | `str`                            | Qualitative assessment of the potential negative impact.                                                   | Yes      | `"High: Potential loss of 15% market share if no action taken."`            |
| `likelihood_score`                | `float`                          | Assessed likelihood (0.0-1.0) of the threat materializing.                                                 | Yes      | `0.65`                                                                        |
| `severity_level`                  | `CKO_SeverityLevelEnum`            | Assessed severity of the threat if it materializes.                                                        | Yes      | `"High"`                                                                      |
| `time_horizon`                    | `Optional[timedelta]`            | Estimated timeframe in which the threat might materialize.                                                 | No       | `"P6M"` (6 months)                                                            |
| `existing_mitigations`            | `Optional[List[str]]`            | Any existing ESTRATIX controls relevant to this threat.                                                    | No       | `["Current market monitoring procedures", "Brand loyalty program"]`         |
| `generated_by_agent_id`           | `str`                            | ID of the ESTRATIX agent (e.g., CKO_A004_KnowledgeAnalystAgent) that generated this alert.               | Yes      | `"CKO_A004_KnowledgeAnalystAgent"`                                            |
| `generation_date`                 | `datetime`                       | Date the alert was generated.                                                                               | Yes      | `"2025-05-28T15:00:00Z"`                                                      |
| `alert_status`                    | `CKO_AlertStatusEnum`            | Current status of the threat alert.                                                                         | Yes      | `"New"`, `"Under Assessment"`                                                  |
| `status_last_updated`             | `datetime`                       | Timestamp of the last status update.                                                                        | Yes      | `"2025-05-28T15:00:00Z"`                                                      |
| `assigned_to_co_office`           | `Optional[str]`                  | Command Office primarily responsible for assessment (e.g., CRO, CSO). Default could be CRO.              | No       | `"CRO"`, `"CSO"`                                                                |
| `assessment_notes`                | `Optional[str]`                  | Notes from the assessment process by CRO or other stakeholders.                                          | No       | `"CRO assessment: Valid threat, requires proactive strategy. Assigning to CSO for market response plan."` |
| `related_mitigation_proposal_id`  | `Optional[uuid.UUID]`            | ID of a proposal developed to mitigate this threat, if applicable.                                         | No       | `"prop-mit-456-uuid"`                                                          |
| `related_risk_register_id`        | `Optional[str]`                  | ID or link to an entry in a formal Risk Register, if applicable.                                           | No       | `"RR_2025_088"`                                                                |
| `custom_fields`                   | `Optional[Dict[str, Any]]`       | Flexible dictionary for additional, alert-specific metadata.                                                | No       | `{"competitor_id": "COMP_X"}`                                                 |

## 5. Relationships to Other Data Models

*   **`source_insight_report_id` (references `CKO_M005_InsightReport.report_id`):** The report that may have triggered or heavily informed this alert.
*   **`source_asset_ids` (references `CKO_M003_CuratedKnowledgeAsset.asset_id`):** Supporting evidence from curated knowledge.
*   **`source_graph_node_ids` (references `CKO_M008_KnowledgeGraphNode.node_id`):** Related entities in the knowledge graph.
*   This model is an input to strategic planning, risk management (potentially a `CRO_FXXX_RiskManagement` flow), and `CPO_F001_StrategicOpportunityToProposalDevelopment` (for proposals aimed at threat mitigation).
*   Can lead to the creation of a proposal (linking via `related_mitigation_proposal_id`).

## 6. Usage Context

*   **Primary Producing Flow(s)/Process(es):** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (specifically `CKO_P008_IdentifyAndLogOpportunityOrThreat`). Created by `CKO_A004_KnowledgeAnalystAgent`.
*   **Primary Consuming Flow(s)/Process(es):** Risk management flows (e.g., overseen by CRO), strategic planning sessions, `CPO_F001_StrategicOpportunityToProposalDevelopment`.
*   **Key Agents Interacting:** `CKO_A004_KnowledgeAnalystAgent` (creates), CRO agents (assess, manage), CSO agents (assess, plan response), CPO agents (develop mitigation proposals).

## 7. Notes / Future Considerations

*   The `alert_status` lifecycle should be integrated with formal risk management and incident response workflows if applicable.
*   A quantitative impact assessment (e.g., financial impact range) could be added if feasible.
*   Integration with a formal Risk Register is highly recommended.
*   Consider fields for tracking the effectiveness of implemented mitigation strategies related to closed alerts.

## 8. Revision History

| Version | Date       | Author     | Changes                                                                                                                                                                                                                               |
| :------ | :--------- | :--------- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_M007 to CKO_M007. Updated ID, version, owner, SME, Pydantic enum and class names, and all internal KNO references to CKO (including CKO_M008 and CKO_P008). Updated Last Updated date. Added revision history.      |
| 1.0     | YYYY-MM-DD | KNO Team   | Initial definition of the ThreatAlert data model.                                                                                                                                                                   |
