# ESTRATIX Data Model: Analytical Query/Request (CKO_M004)

## 1. Metadata

*   **ID:** CKO_M004
*   **Model Name:** Analytical Query/Request
*   **Version:** 1.1
*   **Status:** Definition
*   **Owner(s):** Chief Knowledge Officer (CKO), Chief Analytics Officer (CAO)
*   **Related Flows:** `CKO_F002_KnowledgeAnalysisAndInsightGeneration`
*   **Date Created:** YYYY-MM-DD
*   **Last Updated:** 2025-05-27
*   **SOP References:** CKO_SOP_007: Analytical Request Submission and Processing

## 2. Purpose

*   To provide a standardized structure for Command Officers or automated ESTRATIX systems to submit specific analytical queries or requests to the Knowledge Analysis and Insight Generation flow (`CKO_F002`). This model ensures that all necessary information is captured to define the scope, objectives, and desired outputs of an analytical task, facilitating efficient and targeted analysis.

## 3. Description

This data model captures the details of a request for knowledge analysis. It helps in framing the problem, specifying the type of analysis needed, identifying relevant data sources or criteria, and defining the expected format and use of the analytical output.

## 4. Pydantic Model Definition (Conceptual)

```python
from pydantic import BaseModel, Field, HttpUrl
from typing import List, Optional, Dict, Any
from enum import Enum
from datetime import datetime

class AnalysisPriority(str, Enum):
    LOW = "Low"
    MEDIUM = "Medium"
    HIGH = "High"
    CRITICAL = "Critical"

class AnalysisType(str, Enum):
    TREND_ANALYSIS = "Trend Analysis"
    PATTERN_RECOGNITION = "Pattern Recognition"
    PREDICTIVE_MODELING = "Predictive Modeling"
    IMPACT_ASSESSMENT = "Impact Assessment"
    SWOT_ANALYSIS = "SWOT Analysis"
    PESTLE_ANALYSIS = "PESTLE Analysis"
    COMPETITIVE_LANDSCAPE = "Competitive Landscape"
    MARKET_SIZING = "Market Sizing"
    ROOT_CAUSE_ANALYSIS = "Root Cause Analysis"
    SENTIMENT_ANALYSIS = "Sentiment Analysis"
    TOPIC_MODELING = "Topic Modeling"
    CUSTOM = "Custom"

class ExpectedOutputFormat(str, Enum):
    INSIGHT_REPORT_CKO_M005 = "Insight Report (CKO_M005)"
    OPPORTUNITY_SIGNAL_CKO_M006 = "Opportunity Signal (CKO_M006)"
    THREAT_ALERT_CKO_M007 = "Threat Alert (CKO_M007)"
    DASHBOARD_VISUALIZATION = "Dashboard/Visualization"
    PRESENTATION_SLIDES = "Presentation Slides"
    RAW_DATA_EXTRACT = "Raw Data Extract"
    CUSTOM_REPORT = "Custom Report"

class CKO_M004_AnalyticalQueryOrRequest(BaseModel):
    request_id: str = Field(..., description="Unique identifier for the analytical request.", example="ARQ_20231026_001")
    request_name: str = Field(..., description="Short, descriptive name for the request.", example="Q4 Competitive Threat Analysis - Tech Sector")
    requesting_officer_id: str = Field(..., description="ID of the Command Officer or ESTRATIX Agent initiating the request.", example="CPO_A001")
    requesting_office_code: str = Field(..., description="Acronym of the requesting Command Office.", example="CPO")
    submission_timestamp: datetime = Field(default_factory=datetime.utcnow, description="Timestamp when the request was submitted.")
    priority: AnalysisPriority = Field(default=AnalysisPriority.MEDIUM, description="Priority level of the analysis.")
    
    objective: str = Field(..., description="Clear statement of what the analysis aims to achieve or what question it seeks to answer.")
    scope_description: str = Field(..., description="Detailed description of the analysis scope, including boundaries, specific areas of focus, or exclusions.")
    key_questions: List[str] = Field(default_factory=list, description="Specific questions the analysis should address.")
    
    analysis_type_requested: List[AnalysisType] = Field(..., description="Type(s) of analysis requested.")
    custom_analysis_description: Optional[str] = Field(None, description="Description if 'CUSTOM' analysis type is selected.")
    
    # Data Source Specification (Optional - if not relying on general knowledge base scan)
    target_knowledge_source_ids: Optional[List[str]] = Field(default_factory=list, description="Specific CKO_M001_KnowledgeSourceRegistry IDs to focus on.")
    target_asset_ids: Optional[List[str]] = Field(default_factory=list, description="Specific CKO_M003_CuratedKnowledgeAsset IDs to analyze.")
    keywords_or_phrases: Optional[List[str]] = Field(default_factory=list, description="Keywords or phrases to filter relevant assets from the knowledge base.")
    date_range_start: Optional[datetime] = Field(None, description="Start date for filtering knowledge assets.")
    date_range_end: Optional[datetime] = Field(None, description="End date for filtering knowledge assets.")
    custom_filter_criteria: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Other custom criteria for data selection (e.g., specific metadata fields, tags).")

    # Output Specification
    expected_output_formats: List[ExpectedOutputFormat] = Field(..., description="Desired format(s) for the analytical output.")
    custom_output_format_description: Optional[str] = Field(None, description="Description if 'CUSTOM_REPORT' output format is selected.")
    intended_use_of_output: str = Field(..., description="How the output will be used (e.g., strategic planning, proposal development, operational adjustment).")
    recipients: List[str] = Field(..., description="Intended recipients or consuming systems/agents for the output (e.g., Officer IDs, Flow IDs).")
    desired_completion_date: Optional[datetime] = Field(None, description="Requested completion date for the analysis.")

    # Context & Background
    background_information: Optional[str] = Field(None, description="Any relevant background, context, or previous findings related to this request.")
    related_documents_or_links: Optional[List[HttpUrl]] = Field(default_factory=list, description="Links to related documents or resources.")

    # Management & Tracking
    status: str = Field(..., description="Current status of the request (e.g., Submitted, In Progress, Pending Review, Completed, Cancelled).", example="Submitted")
    assigned_analyst_agent_id: Optional[str] = Field(None, description="ID of the primary ESTRATIX Analyst Agent (e.g., CKO_A004) or human analyst assigned.", example="CKO_A004_KnowledgeAnalystAgent")
    start_timestamp: Optional[datetime] = Field(None, description="Timestamp when work on the request began.")
    completion_timestamp: Optional[datetime] = Field(None, description="Timestamp when the request was completed.")
    output_references: Optional[List[str]] = Field(default_factory=list, description="IDs or links to the generated output(s) (e.g., CKO_M005_InsightReport ID).")

    custom_fields: Optional[Dict[str, Any]] = Field(default_factory=dict, description="Custom fields for additional information specific to certain request types.")

    class Config:
        use_enum_values = True
        json_schema_extra = {
            "example": {
                "request_id": "ARQ_20240115_001",
                "request_name": "Analyze Impact of New AI Regulations on ESTRATIX Service Delivery",
                "requesting_officer_id": "CLO_001",
                "requesting_office_code": "CLO",
                "submission_timestamp": "2024-01-15T10:00:00Z",
                "priority": "High",
                "objective": "To understand the potential impact of new AI regulations on ESTRATIX's ability to deliver services to clients in the EU.",
                "scope_description": "Focus on GDPR, EU AI Act, and relevant national laws. Analyze impact on data handling, model development, and service deployment for ESTRATIX CKO and COO services. Exclude impact on internal HR processes.",
                "key_questions": [
                    "What are the key compliance requirements for ESTRATIX under the new AI regulations?",
                    "How will these regulations affect our current data processing and knowledge management practices (CKO_P001 to CKO_P007)?",
                    "What changes are needed in our service delivery models (e.g., COO_F002) to ensure compliance?",
                    "What are the competitive implications?"
                ],
                "analysis_type_requested": ["Impact Assessment", "Competitive Landscape"],
                "keywords_or_phrases": ["AI regulation", "data privacy", "algorithmic bias", "EU AI Act", "NIST AI RMF"],
                "target_knowledge_source_ids": ["KSR_GOV_EU_001", "KSR_INDUSTRY_AI_ETHICS_005"],
                "target_asset_ids": ["CKA_20231201_008"],
                "date_range_start": "2023-10-01T00:00:00Z",
                "expected_output_formats": ["Insight Report (CKO_M005)", "Presentation Slides"],
                "intended_use_of_output": "Briefing for C-suite, input for strategic planning session Q1 2024.",
                "recipients": ["CEO", "CSO", "CKO", "COO", "CLO"],
                "desired_completion_date": "2024-02-15T17:00:00Z",
                "background_information": "Preliminary scan of news articles indicates significant changes. Previous internal review of AI ethics (Q2 2023) available on intranet.",
                "status": "Submitted"
            }
        }

```

## 5. Fields and Descriptions

*   **`request_id` (str, required):** Unique identifier for the analytical request.
*   **`request_name` (str, required):** Short, descriptive name for the request.
*   **`requesting_officer_id` (str, required):** ID of the Command Officer or ESTRATIX Agent initiating the request.
*   **`requesting_office_code` (str, required):** Acronym of the requesting Command Office.
*   **`submission_timestamp` (datetime, required):** Timestamp when the request was submitted.
*   **`priority` (AnalysisPriority, required):** Priority level of the analysis (Low, Medium, High, Critical).
*   **`objective` (str, required):** Clear statement of what the analysis aims to achieve.
*   **`scope_description` (str, required):** Detailed description of the analysis scope.
*   **`key_questions` (List[str], optional):** Specific questions the analysis should address.
*   **`analysis_type_requested` (List[AnalysisType], required):** Type(s) of analysis requested (e.g., Trend Analysis, Predictive Modeling).
*   **`custom_analysis_description` (Optional[str]):** Description if 'CUSTOM' analysis type is selected.
*   **`target_knowledge_source_ids` (Optional[List[str]]):** Specific `CKO_M001` IDs to focus on.
*   **`target_asset_ids` (Optional[List[str]]):** Specific `CKO_M003` IDs to analyze.
*   **`keywords_or_phrases` (Optional[List[str]]):** Keywords to filter relevant assets.
*   **`date_range_start` (Optional[datetime]):** Start date for filtering knowledge assets.
*   **`date_range_end` (Optional[datetime]):** End date for filtering knowledge assets.
*   **`custom_filter_criteria` (Optional[Dict[str, Any]]):** Other custom criteria for data selection.
*   **`expected_output_formats` (List[ExpectedOutputFormat], required):** Desired format(s) for the analytical output.
*   **`custom_output_format_description` (Optional[str]):** Description if 'CUSTOM_REPORT' output format is selected.
*   **`intended_use_of_output` (str, required):** How the output will be used.
*   **`recipients` (List[str], required):** Intended recipients or consuming systems/agents.
*   **`desired_completion_date` (Optional[datetime]):** Requested completion date.
*   **`background_information` (Optional[str]):** Relevant background or context.
*   **`related_documents_or_links` (Optional[List[HttpUrl]]):** Links to related resources.
*   **`status` (str, required):** Current status of the request.
*   **`assigned_analyst_agent_id` (Optional[str]):** ID of the primary agent/analyst assigned (e.g., `CKO_A004`).
*   **`completion_timestamp` (Optional[datetime]):** Timestamp when the request was completed.
*   **`output_references` (Optional[List[str]]):** IDs or links to the generated output(s) (e.g. `CKO_M005`).
*   **`custom_fields` (Optional[Dict[str, Any]]):** For additional information.

## 6. Relationships to Other Data Models

*   **Input to:** `CKO_F002_KnowledgeAnalysisAndInsightGeneration` (Flow)
*   **May reference:** `CKO_M001_KnowledgeSourceRegistry`, `CKO_M003_CuratedKnowledgeAsset`
*   **Results in:** `CKO_M005_InsightReport`, `CKO_M006_OpportunitySignal`, `CKO_M007_ThreatAlert` (indirectly, as outputs of the flow triggered by this request).

## 7. Usage Examples

*   A Chief Portfolio Officer (CPO) submits a request to analyze market trends for a potential new service offering.
*   An automated ESTRATIX monitoring agent submits a request for root cause analysis upon detecting a significant anomaly in operational data.
*   The Chief Strategy Officer (CSO) requests a PESTLE analysis for a new target market.

## 8. Security and Access Control

*   Access to submit requests may be role-based (e.g., limited to Command Officers or designated agents).
*   Sensitive information within requests (e.g., related to unannounced strategic initiatives) should be handled with appropriate confidentiality.
*   The ESTRATIX system should log all submitted requests and track their status changes for auditability.

## 9. Data Retention and Archival

*   Analytical requests should be retained for a defined period (e.g., 5 years) to provide historical context and support longitudinal analysis of ESTRATIX's analytical activities.
*   Archived requests should remain accessible for review and audit purposes.

## 10. Future Enhancements

*   Integration with a resource estimation module to predict time/effort for fulfilling the request.
*   Workflow for approval of high-priority or resource-intensive requests.
*   Automated suggestion of relevant `CKO_M003` assets based on request parameters.

## 11. Revision History

| Version | Date       | Author        | Changes                                                                                                                                                              |
| :------ | :--------- | :------------ | :------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| 1.1     | 2025-05-27 | Cascade AI    | Refactored from KNO_M004 to CKO_M004. Updated ID, version, owner, SOP, class name, enum members, and all internal KNO references to CKO. Updated Last Updated date. |
| 1.0     | YYYY-MM-DD | Cascade AI    | Initial Definition                                                                                                                                                   |
