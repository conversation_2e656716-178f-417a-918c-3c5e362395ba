// Dokploy Configuration for Luxcrafts Production Deployment
// Target: www.luxcrafts.co

module.exports = {
  // Application Configuration
  app: {
    name: 'luxcrafts-platform',
    type: 'static',
    framework: 'vite',
    domain: 'www.luxcrafts.co',
    subdomain: null,
    port: 3000,
    ssl: true,
    autoSSL: true
  },

  // Build Configuration
  build: {
    command: 'npm run build',
    outputDirectory: 'dist',
    installCommand: 'npm ci',
    nodeVersion: '20',
    environment: 'production'
  },

  // Environment Variables
  env: {
    VITE_APP_ENVIRONMENT: 'production',
    VITE_API_BASE_URL: 'https://api.luxcrafts.co',
    VITE_CHAIN_ID: '1',
    VITE_WALLETCONNECT_PROJECT_ID: '${WALLETCONNECT_PROJECT_ID}',
    VITE_ALCHEMY_API_KEY: '${ALCHEMY_API_KEY}',
    VITE_INFURA_API_KEY: '${INFURA_API_KEY}',
    VITE_CONTRACT_ADDRESS: '${CONTRACT_ADDRESS}',
    VITE_LUX_TOKEN_ADDRESS: '${LUX_TOKEN_ADDRESS}'
  },

  // Health Check Configuration
  healthCheck: {
    enabled: true,
    path: '/health',
    interval: 30,
    timeout: 10,
    retries: 3
  },

  // Security Configuration
  security: {
    headers: {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https: wss:; font-src 'self' data:;"
    },
    rateLimit: {
      enabled: true,
      requests: 100,
      window: 60
    }
  },

  // Performance Configuration
  performance: {
    compression: true,
    caching: {
      static: {
        maxAge: 31536000, // 1 year
        immutable: true
      },
      html: {
        maxAge: 3600 // 1 hour
      }
    },
    cdn: {
      enabled: true,
      provider: 'cloudflare'
    }
  },

  // Monitoring Configuration
  monitoring: {
    enabled: true,
    metrics: {
      enabled: true,
      endpoint: '/metrics'
    },
    logging: {
      level: 'info',
      format: 'json'
    },
    alerts: {
      enabled: true,
      webhook: '${MONITORING_WEBHOOK_URL}',
      thresholds: {
        responseTime: 2000,
        errorRate: 0.05,
        uptime: 0.99
      }
    }
  },

  // Backup Configuration
  backup: {
    enabled: true,
    schedule: '0 2 * * *', // Daily at 2 AM
    retention: 30, // 30 days
    storage: {
      type: 's3',
      bucket: '${BACKUP_S3_BUCKET}',
      region: '${BACKUP_S3_REGION}'
    }
  },

  // Deployment Configuration
  deployment: {
    strategy: 'blue-green',
    rollback: {
      enabled: true,
      automatic: true,
      healthCheckFails: 3
    },
    hooks: {
      preDeploy: [
        'npm run test',
        'npm run lint',
        'npm audit --audit-level=moderate'
      ],
      postDeploy: [
        'curl -f https://www.luxcrafts.co/health',
        'npm run e2e:smoke'
      ]
    }
  },

  // Scaling Configuration
  scaling: {
    enabled: true,
    minInstances: 2,
    maxInstances: 10,
    targetCPU: 70,
    targetMemory: 80
  },

  // Database Configuration (if needed)
  database: {
    enabled: false,
    type: 'postgresql',
    host: '${DB_HOST}',
    port: 5432,
    name: '${DB_NAME}',
    user: '${DB_USER}',
    password: '${DB_PASSWORD}',
    ssl: true,
    poolSize: 10
  },

  // Redis Configuration (for caching)
  redis: {
    enabled: true,
    host: '${REDIS_HOST}',
    port: 6379,
    password: '${REDIS_PASSWORD}',
    db: 0,
    ttl: 3600
  },

  // Network Configuration
  network: {
    vpc: {
      enabled: true,
      cidr: '10.0.0.0/16'
    },
    firewall: {
      enabled: true,
      rules: [
        {
          port: 80,
          protocol: 'tcp',
          source: '0.0.0.0/0'
        },
        {
          port: 443,
          protocol: 'tcp',
          source: '0.0.0.0/0'
        },
        {
          port: 22,
          protocol: 'tcp',
          source: '${ADMIN_IP_RANGE}'
        }
      ]
    }
  },

  // CI/CD Integration
  cicd: {
    enabled: true,
    provider: 'github',
    repository: 'estratix/luxcrafts',
    branch: 'main',
    webhook: {
      enabled: true,
      secret: '${WEBHOOK_SECRET}'
    },
    autoDeployment: {
      enabled: true,
      branches: ['main'],
      requiresApproval: true
    }
  }
};