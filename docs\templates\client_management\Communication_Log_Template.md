# ESTRATIX Communication Log Template

---

## Document Control

- **Template Version:** ESTRATIX-TEMPL-CMT-CLT-1.0
- **Document Version:** `{{Project Communication Log Version, e.g., 1.0}}`
- **Status:** `{{Active | Archived}}`
- **Author(s):** `{{Author Name/Team}}`, `AGENT_Client_Relationship_Manager` (ID: AGENT_CPO_CRM001)
- **Reviewer(s):** `{{Reviewer Name/Team}}`, `AGENT_Project_Manager` (ID: AGENT_COO_PM001)
- **Approver(s):** `{{Approver Name/Team}}`, `AGENT_CPO_Office_Lead` (ID: AGENT_CPO_OL001)
- **Date Created:** `{{YYYY-MM-DD}}`
- **Last Updated Date:** `{{YYYY-MM-DD}}`
- **Security Classification:** `{{ESTRATIX Confidential - Internal Use Only}}`
- **ESTRATIX Document ID (Instance):** `{{ProjectID_CL_YYYYMMDD_Version}}`
- **Distribution List:** ESTRATIX CPO Office, Project Team

---

## Guidance for Use (ESTRATIX)

### 1. Purpose

This Communication Log Template is designed to systematically record all significant communications with clients and key stakeholders throughout the project lifecycle. Its primary purpose is to ensure clarity, maintain a historical record of discussions and decisions, and support effective relationship management.

### 2. Mandatory Use

This log is a mandatory deliverable for all ESTRATIX client-facing projects. It must be initiated at the start of the project and maintained diligently until project closure.

### 3. Agent Integration

- **`AGENT_Client_Relationship_Manager` (ID: AGENT_CPO_CRM001):** Responsible for ensuring the log is consistently updated, accurate, and reflects the current state of client communications. The agent will periodically audit the log for completeness and flag any overdue action items.
- **`AGENT_Meeting_Scribe` (ID: AGENT_CPO_MS001):** Can be tasked with automatically transcribing meeting recordings and generating draft log entries for review and approval.
- **`AGENT_Sentiment_Analyzer` (ID: AGENT_CPO_SA001):** Can be run against the log entries to provide insights into client sentiment and satisfaction trends over time.

### 4. Process

1. **Log Entry:** For every significant interaction (meeting, call, email thread), create a new entry in the log.
2. **Completeness:** Fill in all relevant fields, including date, participants, communication type, key discussion points, decisions made, and action items.
3. **Action Items:** Clearly assign ownership and due dates for all action items.
4. **Review:** The Project Manager and Client Relationship Manager should review the log weekly to track progress and address any emerging issues.
5. **Storage:** The completed log must be stored in the official project repository under the specified ESTRATIX Document ID.

---

## Communication Log

| Date & Time         | Communication Type (Meeting, Call, Email) | Participants (Internal & Client)                               | Key Discussion Points & Summary                                                              | Decisions Made                                                              | Action Items (Owner, Due Date)                                            | Entry By        |
| ------------------- | ----------------------------------------- | -------------------------------------------------------------- | ---------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | ------------------------------------------------------------------------- | --------------- |
| `{{YYYY-MM-DD HH:MM}}`  | `{{Type}}`                                | **Internal:** `{{Names}}` **Client:** `{{Names}}`                | `{{Summary of the conversation, including objectives, issues discussed, and outcomes.}}`         | `{{Record any formal decisions that were agreed upon during the communication.}}` | **1.** `{{Action Item 1}}` - `{{Owner}}` - `{{YYYY-MM-DD}}` **2.** `{{Action Item 2}}` - `{{Owner}}` - `{{YYYY-MM-DD}}` | `{{Name}}`      |
| `{{YYYY-MM-DD HH:MM}}`  | `{{Type}}`                                | **Internal:** `{{Names}}` **Client:** `{{Names}}`                | `{{Summary of the conversation, including objectives, issues discussed, and outcomes.}}`         | `{{Record any formal decisions that were agreed upon during the communication.}}` | **1.** `{{Action Item 1}}` - `{{Owner}}` - `{{YYYY-MM-DD}}` **2.** `{{Action Item 2}}` - `{{Owner}}` - `{{YYYY-MM-DD}}` | `{{Name}}`      |
| | | | | | | |

> **Agent Prompt (`AGENT_Meeting_Scribe`):** "Review the transcript from the meeting on `{{Date}}` with `{{Client Name}}`. Generate a communication log entry summarizing key points, decisions, and action items. Format the output for the ESTRATIX Communication Log table."
> **Agent Prompt (`AGENT_Sentiment_Analyzer`):** "Analyze the 'Key Discussion Points & Summary' column for the past 30 days and generate a sentiment trend report for `{{Client Name}}`."
