// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts-upgradeable/token/ERC721/ERC721Upgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721EnumerableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/token/ERC721/extensions/ERC721URIStorageUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/utils/Counters.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

/**
 * @title PropertyTokenization
 * @dev Smart contract for tokenizing real estate properties as NFTs
 * @notice This contract allows property owners to tokenize their real estate assets
 */
contract PropertyTokenization is
    Initializable,
    ERC721Upgradeable,
    ERC721EnumerableUpgradeable,
    ERC721URIStorageUpgradeable,
    PausableUpgradeable,
    AccessControlUpgradeable,
    ReentrancyGuardUpgradeable,
    UUPSUpgradeable
{
    using Counters for Counters.Counter;

    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");
    bytes32 public constant VALIDATOR_ROLE = keccak256("VALIDATOR_ROLE");

    Counters.Counter private _tokenIdCounter;

    struct Property {
        uint256 tokenId;
        string propertyId;
        address owner;
        uint256 totalValue;
        uint256 fractionSize;
        uint256 availableFractions;
        uint256 pricePerFraction;
        bool isVerified;
        bool isActive;
        string metadataURI;
        uint256 createdAt;
        uint256 updatedAt;
        mapping(address => uint256) fractionOwnership;
        address[] fractionHolders;
    }

    struct PropertyMetadata {
        string name;
        string description;
        string location;
        uint256 area;
        string propertyType;
        string[] images;
        string[] documents;
        mapping(string => string) additionalData;
    }

    mapping(uint256 => Property) public properties;
    mapping(string => uint256) public propertyIdToTokenId;
    mapping(uint256 => PropertyMetadata) public propertyMetadata;
    mapping(address => uint256[]) public ownerProperties;
    mapping(address => mapping(uint256 => uint256)) public userFractions;

    // Platform fees
    uint256 public platformFeePercentage;
    address public feeRecipient;
    IERC20 public luxToken;

    // Events
    event PropertyTokenized(
        uint256 indexed tokenId,
        string indexed propertyId,
        address indexed owner,
        uint256 totalValue,
        uint256 fractionSize
    );

    event FractionPurchased(
        uint256 indexed tokenId,
        address indexed buyer,
        uint256 fractions,
        uint256 totalCost
    );

    event PropertyVerified(uint256 indexed tokenId, address indexed validator);
    event PropertyUpdated(uint256 indexed tokenId, uint256 newValue);
    event FractionsTransferred(
        uint256 indexed tokenId,
        address indexed from,
        address indexed to,
        uint256 fractions
    );

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(
        address _luxToken,
        uint256 _platformFeePercentage,
        address _feeRecipient
    ) public initializer {
        __ERC721_init("ESTRATIX Property Tokens", "EPT");
        __ERC721Enumerable_init();
        __ERC721URIStorage_init();
        __Pausable_init();
        __AccessControl_init();
        __ReentrancyGuard_init();
        __UUPSUpgradeable_init();

        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _grantRole(MINTER_ROLE, msg.sender);
        _grantRole(UPGRADER_ROLE, msg.sender);
        _grantRole(VALIDATOR_ROLE, msg.sender);

        luxToken = IERC20(_luxToken);
        platformFeePercentage = _platformFeePercentage;
        feeRecipient = _feeRecipient;
    }

    /**
     * @dev Tokenize a new property
     */
    function tokenizeProperty(
        string memory _propertyId,
        uint256 _totalValue,
        uint256 _fractionSize,
        uint256 _pricePerFraction,
        string memory _metadataURI,
        PropertyMetadata memory _metadata
    ) public onlyRole(MINTER_ROLE) returns (uint256) {
        require(propertyIdToTokenId[_propertyId] == 0, "Property already tokenized");
        require(_totalValue > 0, "Invalid total value");
        require(_fractionSize > 0, "Invalid fraction size");
        require(_pricePerFraction > 0, "Invalid price per fraction");

        uint256 tokenId = _tokenIdCounter.current();
        _tokenIdCounter.increment();

        _safeMint(msg.sender, tokenId);
        _setTokenURI(tokenId, _metadataURI);

        Property storage property = properties[tokenId];
        property.tokenId = tokenId;
        property.propertyId = _propertyId;
        property.owner = msg.sender;
        property.totalValue = _totalValue;
        property.fractionSize = _fractionSize;
        property.availableFractions = _fractionSize;
        property.pricePerFraction = _pricePerFraction;
        property.isVerified = false;
        property.isActive = true;
        property.metadataURI = _metadataURI;
        property.createdAt = block.timestamp;
        property.updatedAt = block.timestamp;

        propertyIdToTokenId[_propertyId] = tokenId;
        ownerProperties[msg.sender].push(tokenId);

        // Set metadata
        PropertyMetadata storage metadata = propertyMetadata[tokenId];
        metadata.name = _metadata.name;
        metadata.description = _metadata.description;
        metadata.location = _metadata.location;
        metadata.area = _metadata.area;
        metadata.propertyType = _metadata.propertyType;
        metadata.images = _metadata.images;
        metadata.documents = _metadata.documents;

        emit PropertyTokenized(tokenId, _propertyId, msg.sender, _totalValue, _fractionSize);
        return tokenId;
    }

    /**
     * @dev Purchase property fractions
     */
    function purchaseFractions(
        uint256 _tokenId,
        uint256 _fractions
    ) public nonReentrant whenNotPaused {
        require(_exists(_tokenId), "Property does not exist");
        require(_fractions > 0, "Invalid fraction amount");
        
        Property storage property = properties[_tokenId];
        require(property.isActive, "Property is not active");
        require(property.isVerified, "Property is not verified");
        require(property.availableFractions >= _fractions, "Insufficient fractions available");

        uint256 totalCost = _fractions * property.pricePerFraction;
        uint256 platformFee = (totalCost * platformFeePercentage) / 10000;
        uint256 ownerAmount = totalCost - platformFee;

        // Transfer LUX tokens
        require(luxToken.transferFrom(msg.sender, property.owner, ownerAmount), "Owner payment failed");
        require(luxToken.transferFrom(msg.sender, feeRecipient, platformFee), "Platform fee payment failed");

        // Update fraction ownership
        if (property.fractionOwnership[msg.sender] == 0) {
            property.fractionHolders.push(msg.sender);
        }
        property.fractionOwnership[msg.sender] += _fractions;
        property.availableFractions -= _fractions;
        userFractions[msg.sender][_tokenId] += _fractions;

        emit FractionPurchased(_tokenId, msg.sender, _fractions, totalCost);
    }

    /**
     * @dev Verify a property
     */
    function verifyProperty(uint256 _tokenId) public onlyRole(VALIDATOR_ROLE) {
        require(_exists(_tokenId), "Property does not exist");
        properties[_tokenId].isVerified = true;
        properties[_tokenId].updatedAt = block.timestamp;
        emit PropertyVerified(_tokenId, msg.sender);
    }

    /**
     * @dev Transfer fractions between users
     */
    function transferFractions(
        uint256 _tokenId,
        address _to,
        uint256 _fractions
    ) public nonReentrant {
        require(_exists(_tokenId), "Property does not exist");
        require(_to != address(0), "Invalid recipient");
        require(_fractions > 0, "Invalid fraction amount");
        
        Property storage property = properties[_tokenId];
        require(property.fractionOwnership[msg.sender] >= _fractions, "Insufficient fractions");

        // Update ownership
        property.fractionOwnership[msg.sender] -= _fractions;
        if (property.fractionOwnership[_to] == 0) {
            property.fractionHolders.push(_to);
        }
        property.fractionOwnership[_to] += _fractions;
        
        userFractions[msg.sender][_tokenId] -= _fractions;
        userFractions[_to][_tokenId] += _fractions;

        emit FractionsTransferred(_tokenId, msg.sender, _to, _fractions);
    }

    /**
     * @dev Get property information
     */
    function getProperty(uint256 _tokenId) public view returns (
        string memory propertyId,
        address owner,
        uint256 totalValue,
        uint256 fractionSize,
        uint256 availableFractions,
        uint256 pricePerFraction,
        bool isVerified,
        bool isActive
    ) {
        require(_exists(_tokenId), "Property does not exist");
        Property storage property = properties[_tokenId];
        return (
            property.propertyId,
            property.owner,
            property.totalValue,
            property.fractionSize,
            property.availableFractions,
            property.pricePerFraction,
            property.isVerified,
            property.isActive
        );
    }

    /**
     * @dev Get user's fraction ownership for a property
     */
    function getUserFractions(address _user, uint256 _tokenId) public view returns (uint256) {
        return userFractions[_user][_tokenId];
    }

    /**
     * @dev Get all properties owned by a user
     */
    function getUserProperties(address _user) public view returns (uint256[] memory) {
        return ownerProperties[_user];
    }

    // Admin functions
    function pause() public onlyRole(DEFAULT_ADMIN_ROLE) {
        _pause();
    }

    function unpause() public onlyRole(DEFAULT_ADMIN_ROLE) {
        _unpause();
    }

    function setPlatformFee(uint256 _newFeePercentage) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_newFeePercentage <= 1000, "Fee too high"); // Max 10%
        platformFeePercentage = _newFeePercentage;
    }

    function setFeeRecipient(address _newFeeRecipient) public onlyRole(DEFAULT_ADMIN_ROLE) {
        require(_newFeeRecipient != address(0), "Invalid address");
        feeRecipient = _newFeeRecipient;
    }

    // Required overrides
    function _authorizeUpgrade(address newImplementation) internal onlyRole(UPGRADER_ROLE) override {}

    function _beforeTokenTransfer(
        address from,
        address to,
        uint256 tokenId,
        uint256 batchSize
    ) internal override(ERC721Upgradeable, ERC721EnumerableUpgradeable) whenNotPaused {
        super._beforeTokenTransfer(from, to, tokenId, batchSize);
    }

    function _burn(uint256 tokenId) internal override(ERC721Upgradeable, ERC721URIStorageUpgradeable) {
        super._burn(tokenId);
    }

    function tokenURI(uint256 tokenId) public view override(ERC721Upgradeable, ERC721URIStorageUpgradeable) returns (string memory) {
        return super.tokenURI(tokenId);
    }

    function supportsInterface(bytes4 interfaceId) public view override(ERC721Upgradeable, ERC721EnumerableUpgradeable, AccessControlUpgradeable) returns (bool) {
        return super.supportsInterface(interfaceId);
    }
}