---
description: Guides the generation and update of the Service Landscape diagram based on the service_matrix.md.
---

# Workflow: Update Service Landscape Diagram

This workflow details the steps to generate or update the `Service_Landscape.mmd` diagram, providing a visual representation of all ESTRATIX services, their status, and key relationships as defined in `docs/services/service_matrix.md`.

**Responsible Command Office:** COO (for overall operational view), with input from CPO, CVO (for strategic visualization).

## Prerequisites:

*   Access to `docs/services/service_matrix.md`.
*   Understanding of Mermaid syntax for diagrams.
*   (Optional) A script or tool capable of parsing the matrix and generating Mermaid code.

## Steps:

1.  **Access Service Matrix Data:**
    *   Action: Read the latest `docs/services/service_matrix.md`.
    *   Tool: `view_file` to load the content of the service matrix.
    *   Extract relevant data for each service: 
        *   `Service ID ([CO_ACRO]_S[NUM])` (for node ID and linking)
        *   `Service Name` (for node label)
        *   `Service Type` (for styling/grouping)
        *   `Responsible Officer (Acronym)` (for grouping in subgraphs)
        *   `Status` (for styling)
        *   `Version` (for node label)
        *   `Primary Flow(s) Orchestrated` (for potential future relationship lines)
        *   `Key Processes Involved` (for potential future relationship lines)
        *   `Key Collaborating COs` (for potential future relationship lines or CO subgraph links)

2.  **Define Diagram Scope and Layout (Initial Setup or Major Revision):**
    *   Action: Determine the key information to visualize and the desired layout.
    *   Consider using Mermaid subgraphs to group services by `Responsible Officer (Acronym)`.
    *   Define node shapes/styles based on `Service Status` (e.g., Concept, Planning, Implementation, Operational, Deprecated) and `Service Type`.
    *   Ensure `Service ID` is used as the Mermaid node ID to allow direct linking (e.g., `Service_Landscape.mmd#CSOLO_S001_Example`).

3.  **Generate/Update Mermaid Diagram Code:**
    *   Action: Translate the service matrix data into Mermaid diagram syntax.
    *   **Manual Method:** Manually write/edit the Mermaid code in `docs/services/diagrams/Service_Landscape.mmd`.
        *   Represent each service as a node, using its `Service ID` as the Mermaid node ID.
        *   Node Label Example: `"Service Name (vVersion)\nType: Service Type\nStatus: Status"`
        *   Use styling to indicate status and type.
        *   Group services into subgraphs by `Responsible Officer (Acronym)`.
        *   Draw relationships if applicable (e.g., dependencies, collaborations noted in the matrix).
    *   **Automated/Scripted Method (Preferred for Scalability):**
        *   Develop or use a script (e.g., Python) to parse `docs/services/service_matrix.md` (e.g., treating it as CSV or using markdown parsing libraries).
        *   The script should generate the Mermaid syntax for nodes (using `Service ID` as node ID and a descriptive label), subgraphs (for `Responsible Officer`), and styling.
        *   The script outputs the complete Mermaid code for the diagram.

4.  **Write to Service Landscape Diagram File:**
    *   Action: Save the generated/updated Mermaid code to `docs/services/diagrams/Service_Landscape.mmd`.
    *   Tool: `write_to_file` (if creating new or fully overwriting with scripted output) or `edit_file` (for manual edits or incremental script updates).

5.  **Validate Diagram:**
    *   Action: Use a Mermaid preview tool (e.g., online editor, IDE plugin) to render `docs/services/diagrams/Service_Landscape.mmd` and verify its correctness and readability.
    *   Check for syntax errors and ensure the visual representation aligns with the data in the service matrix.

6.  **Commit and Document Changes:**
    *   Action: Commit the updated `Service_Landscape.mmd` and `service_matrix.md` (if it was also changed as part of a larger update process) to version control.
    *   Briefly document the changes or the scope of the update if significant.

## Outputs:

*   An updated `docs/services/diagrams/Service_Landscape.mmd` file containing the Mermaid diagram code.

## Next Steps:

*   Embed or link the `Service_Landscape.mmd` in relevant ESTRATIX documentation or dashboards for visibility.
*   Regularly run this workflow to keep the landscape diagram current, especially after new services are defined or existing services change status.

## Considerations for Automation:

*   A script to parse the `service_matrix.md` and generate the Mermaid code is highly recommended for consistency and efficiency as the number of services grows.
*   This script could be triggered automatically as part of a CI/CD pipeline when `service_matrix.md` is updated.
