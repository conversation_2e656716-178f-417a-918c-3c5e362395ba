#!/bin/bash

# Luxcrafts VPS Deployment Script
# This script deploys the Luxcrafts platform to a VPS with proper domain configuration

set -e

# Configuration
APP_NAME="luxcrafts"
APP_DIR="/var/www/luxcrafts"
NGINX_CONFIG="/etc/nginx/sites-available/luxcrafts"
DOMAIN="staging.luxcrafts.com"  # Change to production domain when ready
GIT_REPO="https://github.com/your-username/luxcrafts.git"  # Update with actual repo
NODE_VERSION="20"

echo "🚀 Starting Luxcrafts VPS Deployment..."

# Update system
echo "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install Node.js
echo "📦 Installing Node.js ${NODE_VERSION}..."
curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Nginx
echo "📦 Installing Nginx..."
sudo apt install nginx -y

# Install Certbot for SSL
echo "📦 Installing Certbot..."
sudo apt install certbot python3-certbot-nginx -y

# Install PM2 for process management
echo "📦 Installing PM2..."
sudo npm install -g pm2

# Create application directory
echo "📁 Creating application directory..."
sudo mkdir -p $APP_DIR
sudo chown -R $USER:$USER $APP_DIR

# Clone repository (if using Git)
echo "📥 Cloning repository..."
if [ -d "$APP_DIR/.git" ]; then
    cd $APP_DIR
    git pull origin main
else
    git clone $GIT_REPO $APP_DIR
    cd $APP_DIR
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Build application
echo "🔨 Building application..."
npm run build

# Configure Nginx
echo "⚙️ Configuring Nginx..."
sudo tee $NGINX_CONFIG > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN www.$DOMAIN;
    root $APP_DIR/dist;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # Static assets caching
    location /assets/ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files \$uri =404;
    }
    
    # Main application
    location / {
        try_files \$uri \$uri/ /index.html;
    }
    
    # API proxy (if needed)
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF

# Enable Nginx site
echo "🔗 Enabling Nginx site..."
sudo ln -sf $NGINX_CONFIG /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
echo "🧪 Testing Nginx configuration..."
sudo nginx -t

# Restart Nginx
echo "🔄 Restarting Nginx..."
sudo systemctl restart nginx
sudo systemctl enable nginx

# Setup SSL with Certbot
echo "🔒 Setting up SSL certificate..."
sudo certbot --nginx -d $DOMAIN -d www.$DOMAIN --non-interactive --agree-tos --email admin@$DOMAIN

# Setup firewall
echo "🛡️ Configuring firewall..."
sudo ufw allow 'Nginx Full'
sudo ufw allow OpenSSH
sudo ufw --force enable

# Create deployment script for updates
echo "📝 Creating update script..."
sudo tee /usr/local/bin/update-luxcrafts > /dev/null <<EOF
#!/bin/bash
cd $APP_DIR
git pull origin main
npm install
npm run build
sudo systemctl reload nginx
echo "✅ Luxcrafts updated successfully!"
EOF

sudo chmod +x /usr/local/bin/update-luxcrafts

# Setup automatic SSL renewal
echo "🔄 Setting up SSL auto-renewal..."
(crontab -l 2>/dev/null; echo "0 12 * * * /usr/bin/certbot renew --quiet") | crontab -

echo "✅ Deployment completed successfully!"
echo "🌐 Your site is available at: https://$DOMAIN"
echo "📋 To update the application, run: sudo /usr/local/bin/update-luxcrafts"
echo "📊 Check Nginx status: sudo systemctl status nginx"
echo "🔒 Check SSL status: sudo certbot certificates"

# Display final status
echo "\n📊 Final Status:"
echo "- Nginx: $(sudo systemctl is-active nginx)"
echo "- SSL Certificate: $(sudo certbot certificates 2>/dev/null | grep -c 'Certificate Name' || echo '0') certificate(s) installed"
echo "- Firewall: $(sudo ufw status | grep -c 'Status: active' || echo 'inactive')"
echo "\n🎉 Luxcrafts VPS deployment completed!"