import { FastifyInstance } from 'fastify';
import { AgentService } from '@/services/agentService';
import { WorkflowService } from '@/services/workflowService';
import { OrchestrationService } from '@/services/orchestrationService';
import { WebSocketService } from '@/services/webSocketService';
import { AnalyticsService } from '@/services/analyticsService';

declare module 'fastify' {
  interface FastifyInstance {
    agentService: AgentService;
    workflowService: WorkflowService;
    orchestrationService: OrchestrationService;
    webSocketService: WebSocketService;
    analyticsService: AnalyticsService;
  }
}