# ESTRATIX Deployment Guide

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Infrastructure Planning](#infrastructure-planning)
3. [Local Development Setup](#local-development-setup)
4. [Production Deployment](#production-deployment)
5. [Coolify Configuration](#coolify-configuration)
6. [Kubernetes Setup](#kubernetes-setup)
7. [Monitoring Configuration](#monitoring-configuration)
8. [Security Hardening](#security-hardening)
9. [CI/CD Pipeline Setup](#cicd-pipeline-setup)
10. [Troubleshooting](#troubleshooting)
11. [Maintenance](#maintenance)

## 🔧 Prerequisites

### System Requirements

#### Local Development
- **OS**: Windows 10/11, macOS 10.15+, or Linux (Ubuntu 20.04+)
- **RAM**: 16GB minimum, 32GB recommended
- **Storage**: 100GB free space
- **CPU**: 4 cores minimum, 8 cores recommended

#### Production Environment
- **VPS Nodes**: 3-5 nodes minimum
- **RAM per Node**: 4GB minimum, 8GB recommended
- **Storage per Node**: 50GB minimum, 100GB recommended
- **CPU per Node**: 2 cores minimum, 4 cores recommended
- **Network**: 1Gbps bandwidth recommended

### Software Dependencies

```bash
# Required Software
- Python 3.9+
- Docker 20.10+
- Docker Compose 2.0+
- kubectl 1.28+
- Helm 3.12+
- Terraform 1.5+ (optional)
- Ansible 2.14+ (optional)

# Development Tools
- Node.js 18+
- npm/yarn
- Git 2.30+
- SSH client
```

### Cloud Provider Accounts

Choose one or more cloud providers:

- **DigitalOcean**: Recommended for simplicity
- **Linode**: Good performance/price ratio
- **Vultr**: Global presence
- **AWS**: Enterprise features
- **Google Cloud**: Advanced networking
- **Azure**: Microsoft ecosystem integration

## 🏗️ Infrastructure Planning

### Architecture Decision Matrix

| Requirement | Small Setup (3 nodes) | Medium Setup (5 nodes) | Large Setup (7+ nodes) |
|-------------|----------------------|------------------------|------------------------|
| **Budget** | $150-300/month | $300-600/month | $600+/month |
| **Users** | 1-50 | 50-500 | 500+ |
| **Applications** | 5-10 | 10-50 | 50+ |
| **High Availability** | Basic | Standard | Enterprise |
| **Disaster Recovery** | Manual | Semi-automated | Fully automated |

### Node Distribution Strategy

#### Small Setup (3 Nodes)
```yaml
Nodes:
  - control-plane: Kubernetes master + Coolify + Monitoring
  - worker-1: Applications + Grafana
  - worker-2: Applications + Backup services
```

#### Medium Setup (5 Nodes)
```yaml
Nodes:
  - control-plane: Kubernetes master + etcd
  - coolify-node: Coolify + CI/CD
  - monitoring-node: Prometheus + Grafana + AlertManager
  - worker-1: Applications
  - worker-2: Applications + Load balancer
```

#### Large Setup (7+ Nodes)
```yaml
Nodes:
  - control-plane-1: Kubernetes master (HA)
  - control-plane-2: Kubernetes master (HA)
  - control-plane-3: Kubernetes master (HA)
  - coolify-node: Coolify + CI/CD
  - monitoring-node: Monitoring stack
  - worker-1: Applications
  - worker-2: Applications
  - worker-n: Additional workers as needed
  - load-balancer: External load balancer
```

## 🐳 Local Development Setup

### Step 1: Clone and Setup

```bash
# Clone the repository
git clone https://github.com/your-org/estratix-v3.git
cd estratix-v3

# Create environment file
cp .env.example .env

# Edit environment variables
nano .env
```

### Step 2: Configure Environment

```bash
# .env file configuration
ESTRATIX_ENV=development
ESTRATIX_LOG_LEVEL=DEBUG
ESTRATIX_DATA_DIR=./data

# Database URLs
POSTGRES_URL=********************************************/estratix
REDIS_URL=redis://redis:6379/0
MONGO_URL=mongodb://mongo:27017/estratix

# Monitoring URLs
PROMETHEUS_URL=http://prometheus:9090
GRAFANA_URL=http://grafana:3000

# Security
JWT_SECRET=your-jwt-secret-change-in-production
VAULT_TOKEN=dev-only-token
```

### Step 3: Start Development Environment

```bash
# Start all services
docker-compose up -d

# Verify services are running
docker-compose ps

# Check logs
docker-compose logs -f central-dashboard
```

### Step 4: Access Services

| Service | URL | Credentials |
|---------|-----|-------------|
| Central Dashboard | http://localhost:3000 | admin/admin |
| Grafana | http://localhost:3001 | admin/admin |
| Prometheus | http://localhost:9090 | - |
| Kibana | http://localhost:5601 | - |
| Portainer | http://localhost:9000 | admin/password |
| Adminer | http://localhost:8080 | postgres/estratix/password |

## ☁️ Production Deployment

### Step 1: Infrastructure Configuration

```bash
# Copy and customize infrastructure configuration
cp config/infrastructure.example.yaml config/infrastructure.yaml

# Edit the configuration
nano config/infrastructure.yaml
```

**Key Configuration Points:**

```yaml
# Update these sections in infrastructure.yaml
project:
  name: "your-project-name"
  domain: "yourdomain.com"
  contact_email: "<EMAIL>"

vps_nodes:
  - name: "control-plane"
    provider: "digitalocean"  # Change to your provider
    size: "s-4vcpu-8gb"       # Adjust size as needed
    region: "nyc3"            # Change to your preferred region
    ip_address: ""            # Leave empty for auto-assignment
    ssh_key_path: "~/.ssh/your_key"  # Update SSH key path

coolify:
  domain: "coolify.yourdomain.com"  # Update domain
  email: "<EMAIL>"     # Update email

monitoring:
  grafana:
    admin_password: "secure-password-change-me"  # Change password
```

### Step 2: SSH Key Setup

```bash
# Generate SSH key pair
ssh-keygen -t ed25519 -f ~/.ssh/estratix_key -C "<EMAIL>"

# Add public key to your cloud provider
cat ~/.ssh/estratix_key.pub
# Copy this output and add it to your cloud provider's SSH keys

# Test SSH connectivity (after VPS creation)
ssh -i ~/.ssh/estratix_key root@your-vps-ip
```

### Step 3: VPS Provisioning

#### Option A: Automated Provisioning

```bash
# Run the infrastructure automation script
python src/infrastructure/automation/infrastructure_automation.py \
  --config config/infrastructure.yaml \
  --provision

# Monitor the provisioning process
tail -f logs/infrastructure_automation.log
```

#### Option B: Manual Provisioning

1. **Create VPS instances** in your cloud provider dashboard
2. **Configure DNS records** for your domains
3. **Update infrastructure.yaml** with actual IP addresses
4. **Run security hardening**:

```bash
# Run security hardening on all nodes
for node in control-plane worker-1 worker-2; do
  ssh -i ~/.ssh/estratix_key root@$node-ip "bash -s" < scripts/security_hardening.sh
done
```

### Step 4: Kubernetes Cluster Setup

```bash
# Initialize Kubernetes on control plane
ssh -i ~/.ssh/estratix_key root@control-plane-ip

# On control plane node
curl -sfL https://get.k3s.io | INSTALL_K3S_EXEC="--disable traefik" sh -

# Get join token
sudo cat /var/lib/rancher/k3s/server/node-token

# Get kubeconfig
sudo cat /etc/rancher/k3s/k3s.yaml
```

```bash
# Join worker nodes
ssh -i ~/.ssh/estratix_key root@worker-ip

# On each worker node
curl -sfL https://get.k3s.io | K3S_URL=https://control-plane-ip:6443 \
  K3S_TOKEN=your-join-token sh -
```

```bash
# Configure local kubectl
mkdir -p ~/.kube
scp -i ~/.ssh/estratix_key root@control-plane-ip:/etc/rancher/k3s/k3s.yaml ~/.kube/config

# Update server IP in kubeconfig
sed -i 's/127.0.0.1/control-plane-ip/g' ~/.kube/config

# Test cluster connectivity
kubectl get nodes
```

### Step 5: Deploy Core Services

```bash
# Create namespace
kubectl create namespace estratix-system

# Deploy central dashboard
kubectl apply -f deployments/kubernetes/central-dashboard/

# Deploy monitoring stack
kubectl apply -f deployments/kubernetes/monitoring/

# Verify deployments
kubectl get pods -n estratix-system
```

## 🚀 Coolify Configuration

### Step 1: Coolify Installation

```bash
# Copy setup script to control plane
scp -i ~/.ssh/estratix_key scripts/coolify_complete_setup.sh root@control-plane-ip:/tmp/

# Run Coolify installation
ssh -i ~/.ssh/estratix_key root@control-plane-ip
chmod +x /tmp/coolify_complete_setup.sh
/tmp/coolify_complete_setup.sh
```

### Step 2: Initial Coolify Setup

1. **Access Coolify**: Navigate to `https://coolify.yourdomain.com`
2. **Complete setup wizard**:
   - Set admin email and password
   - Configure domain settings
   - Set up SSL certificates

### Step 3: Server Configuration

```bash
# Add servers to Coolify
# In Coolify dashboard:
# 1. Go to Servers
# 2. Add New Server
# 3. Configure each worker node:

Servers:
  - Name: worker-1
    IP: worker-1-ip
    SSH Key: (paste private key)
    
  - Name: worker-2
    IP: worker-2-ip
    SSH Key: (paste private key)
```

### Step 4: Project Setup

```bash
# Create projects in Coolify
# 1. ESTRATIX Frontend
Project: estratix-frontend
Repository: https://github.com/your-org/estratix-frontend.git
Branch: main
Domain: app.yourdomain.com
Build Command: npm run build
Start Command: npm start
Port: 3000

# 2. ESTRATIX API
Project: estratix-api
Repository: https://github.com/your-org/estratix-api.git
Branch: main
Domain: api.yourdomain.com
Build Command: pip install -r requirements.txt
Start Command: python app.py
Port: 8000

# 3. ESTRATIX Workers
Project: estratix-workers
Repository: https://github.com/your-org/estratix-workers.git
Branch: main
Domain: workers.yourdomain.com
Build Command: pip install -r requirements.txt
Start Command: celery worker -A app.celery
Port: 5555
```

## 📊 Monitoring Configuration

### Step 1: Prometheus Configuration

```yaml
# deployments/kubernetes/monitoring/prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: estratix-system
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      - job_name: 'prometheus'
        static_configs:
          - targets: ['localhost:9090']
      
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [__address__]
            regex: '(.*):(\d+)'
            target_label: __address__
            replacement: '${1}:9100'
      
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
```

### Step 2: Grafana Dashboard Import

```bash
# Access Grafana
kubectl port-forward -n estratix-system svc/grafana 3000:3000

# Import dashboards
# 1. Go to http://localhost:3000
# 2. Login with admin/admin
# 3. Import dashboards:
#    - Kubernetes Cluster Monitoring (ID: 315)
#    - Node Exporter Full (ID: 1860)
#    - ESTRATIX Custom Dashboard (from deployments/grafana/dashboards/)
```

### Step 3: Alert Configuration

```yaml
# deployments/kubernetes/monitoring/alert-rules.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-rules
  namespace: estratix-system
data:
  infrastructure.yml: |
    groups:
      - name: infrastructure
        rules:
          - alert: NodeDown
            expr: up{job="kubernetes-nodes"} == 0
            for: 5m
            labels:
              severity: critical
            annotations:
              summary: "Node {{ $labels.instance }} is down"
              description: "Node {{ $labels.instance }} has been down for more than 5 minutes."
          
          - alert: HighCPUUsage
            expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High CPU usage on {{ $labels.instance }}"
              description: "CPU usage is above 80% for more than 5 minutes."
          
          - alert: HighMemoryUsage
            expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
            for: 5m
            labels:
              severity: warning
            annotations:
              summary: "High memory usage on {{ $labels.instance }}"
              description: "Memory usage is above 85% for more than 5 minutes."
```

## 🔒 Security Hardening

### Step 1: System Security

```bash
# Run security hardening script on all nodes
for node_ip in control-plane-ip worker-1-ip worker-2-ip; do
  echo "Hardening $node_ip..."
  ssh -i ~/.ssh/estratix_key root@$node_ip 'bash -s' << 'EOF'
    # Update system
    apt update && apt upgrade -y
    
    # Configure firewall
    ufw --force reset
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow 22/tcp
    ufw allow 80/tcp
    ufw allow 443/tcp
    ufw allow 6443/tcp
    ufw allow 2379:2380/tcp
    ufw allow 10250/tcp
    ufw --force enable
    
    # Disable root login
    sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
    sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
    systemctl restart sshd
    
    # Install fail2ban
    apt install -y fail2ban
    systemctl enable fail2ban
    systemctl start fail2ban
EOF
done
```

### Step 2: Kubernetes Security

```bash
# Apply network policies
kubectl apply -f deployments/kubernetes/security/network-policies.yaml

# Apply pod security policies
kubectl apply -f deployments/kubernetes/security/pod-security-policies.yaml

# Apply RBAC configurations
kubectl apply -f deployments/kubernetes/security/rbac.yaml
```

### Step 3: SSL/TLS Configuration

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Configure Let's Encrypt issuer
kubectl apply -f deployments/kubernetes/security/letsencrypt-issuer.yaml

# Apply SSL certificates
kubectl apply -f deployments/kubernetes/security/certificates.yaml
```

## 🔄 CI/CD Pipeline Setup

### Step 1: ArgoCD Installation

```bash
# Install ArgoCD
kubectl create namespace argocd
kubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml

# Get admin password
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

# Access ArgoCD
kubectl port-forward svc/argocd-server -n argocd 8080:443
```

### Step 2: GitHub Actions Setup

```yaml
# .github/workflows/deploy.yml
name: Deploy to ESTRATIX

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.9'
      
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-cov
      
      - name: Run tests
        run: |
          pytest tests/ --cov=src/ --cov-report=xml
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Log in to Container Registry
        uses: docker/login-action@v2
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Build and push Docker image
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest
      
      - name: Deploy to Coolify
        run: |
          curl -X POST "${{ secrets.COOLIFY_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{"ref": "${{ github.ref }}"}'
      
      - name: Update ArgoCD Application
        run: |
          # Update Kubernetes manifests
          sed -i 's|image: .*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest|' k8s/deployment.yaml
          
          # Commit and push changes
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          git add k8s/deployment.yaml
          git commit -m "Update image to latest" || exit 0
          git push
```

### Step 3: Webhook Configuration

```bash
# Configure webhooks in your repositories
# GitHub: Settings > Webhooks > Add webhook
# URL: https://coolify.yourdomain.com/webhooks/github
# Content type: application/json
# Secret: your-webhook-secret
# Events: Push, Pull request

# GitLab: Settings > Webhooks
# URL: https://coolify.yourdomain.com/webhooks/gitlab
# Secret token: your-webhook-secret
# Trigger: Push events, Merge request events
```

## 🐛 Troubleshooting

### Common Issues and Solutions

#### 1. VPS Connection Issues

```bash
# Problem: Cannot SSH to VPS
# Solution:
# Check SSH key permissions
chmod 600 ~/.ssh/estratix_key

# Test SSH connectivity
ssh -i ~/.ssh/estratix_key -v root@vps-ip

# Check firewall rules
ssh -i ~/.ssh/estratix_key root@vps-ip "ufw status"
```

#### 2. Kubernetes Cluster Issues

```bash
# Problem: Nodes not joining cluster
# Solution:
# Check join token
ssh -i ~/.ssh/estratix_key root@control-plane-ip "sudo cat /var/lib/rancher/k3s/server/node-token"

# Check cluster status
kubectl get nodes
kubectl describe node worker-1

# Check k3s logs
ssh -i ~/.ssh/estratix_key root@worker-ip "journalctl -u k3s-agent -f"
```

#### 3. Coolify Issues

```bash
# Problem: Coolify not accessible
# Solution:
# Check Docker containers
ssh -i ~/.ssh/estratix_key root@control-plane-ip "docker ps | grep coolify"

# Check Coolify logs
ssh -i ~/.ssh/estratix_key root@control-plane-ip "docker logs coolify"

# Restart Coolify
ssh -i ~/.ssh/estratix_key root@control-plane-ip "cd /data/coolify && docker-compose restart"
```

#### 4. Application Deployment Issues

```bash
# Problem: Application not deploying
# Solution:
# Check pod status
kubectl get pods -n estratix-system
kubectl describe pod <pod-name> -n estratix-system
kubectl logs <pod-name> -n estratix-system

# Check service status
kubectl get svc -n estratix-system

# Check ingress status
kubectl get ingress -n estratix-system
```

#### 5. Monitoring Issues

```bash
# Problem: Metrics not showing
# Solution:
# Check Prometheus targets
kubectl port-forward -n estratix-system svc/prometheus 9090:9090
# Go to http://localhost:9090/targets

# Check Grafana data sources
kubectl port-forward -n estratix-system svc/grafana 3000:3000
# Go to http://localhost:3000/datasources

# Restart monitoring stack
kubectl rollout restart deployment/prometheus -n estratix-system
kubectl rollout restart deployment/grafana -n estratix-system
```

### Performance Optimization

```bash
# Monitor resource usage
kubectl top nodes
kubectl top pods -n estratix-system

# Optimize resource allocation
kubectl apply -f deployments/kubernetes/optimizations/resource-limits.yaml

# Scale deployments
kubectl scale deployment central-dashboard --replicas=3 -n estratix-system

# Check cluster autoscaler (if enabled)
kubectl logs -f deployment/cluster-autoscaler -n kube-system
```

## 🔄 Maintenance

### Daily Maintenance

```bash
# Check system health
python scripts/health_check.py

# Monitor resource usage
kubectl top nodes
kubectl top pods --all-namespaces

# Check application logs
kubectl logs -f deployment/central-dashboard -n estratix-system
```

### Weekly Maintenance

```bash
# Update system packages
for node_ip in control-plane-ip worker-1-ip worker-2-ip; do
  ssh -i ~/.ssh/estratix_key root@$node_ip "apt update && apt upgrade -y"
done

# Backup databases
python scripts/backup_databases.py

# Clean up unused resources
kubectl delete pods --field-selector=status.phase=Succeeded --all-namespaces
docker system prune -f
```

### Monthly Maintenance

```bash
# Security updates
python scripts/security_scan.py

# Performance review
python scripts/performance_analysis.py

# Capacity planning
python scripts/capacity_planning.py

# Backup verification
python scripts/verify_backups.py
```

### Scaling Operations

```bash
# Add new worker node
python scripts/add_worker_node.py --config new-worker.yaml

# Scale applications
kubectl scale deployment central-dashboard --replicas=5 -n estratix-system

# Update resource limits
kubectl apply -f deployments/kubernetes/scaling/increased-limits.yaml
```

## 📚 Additional Resources

### Documentation Links

- [Kubernetes Documentation](https://kubernetes.io/docs/)
- [Coolify Documentation](https://coolify.io/docs)
- [Prometheus Documentation](https://prometheus.io/docs/)
- [Grafana Documentation](https://grafana.com/docs/)
- [ArgoCD Documentation](https://argo-cd.readthedocs.io/)

### Community Support

- [ESTRATIX Discord](https://discord.gg/estratix)
- [GitHub Discussions](https://github.com/your-org/estratix-v3/discussions)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/estratix)

### Professional Support

- Email: <EMAIL>
- Enterprise Support: <EMAIL>
- Consulting: <EMAIL>

---

**🚀 Happy Deploying with ESTRATIX!**