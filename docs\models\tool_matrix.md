# ESTRATIX Tool Matrix

**Objective:** This document serves as the central registry for all reusable tools defined within the ESTRATIX framework. It provides a discoverable, version-controlled inventory that enables agents and developers to find and leverage existing capabilities, promoting reuse and standardization.

**Scope:** This matrix includes all tools, from simple utility functions to complex API clients, that have been formally defined and approved for use within the ESTRATIX ecosystem. This includes both framework-agnostic tools (e.g., Python scripts) and framework-specific tool implementations.

| Tool ID | Tool Name / Identifier | Type | Purpose / Description | Status | Version | Responsible CO | Conceptual Definition Link | Implementation / Usage Link / Notes |
|:---|:---|:---|:---|:---|:---|:---|:---|:---|
| `TL_MCP_001` | Brave Search | MCP | Provides web search capabilities via Brave Search API. | Implemented | 1.0 | CTO | `../tools/mcp/TL_MCP_001_BraveSearch_definition.md` | MCP Server: `brave-search` |
| `TL_CLI_001` | Docker Build | CLI | Builds Docker images from a Dockerfile. | Implemented | N/A | CTO | `../tools/cli/TL_CLI_001_DockerBuild_definition.md` | Standard Docker CLI command. |
| `TL_FUNC_001` | Generate ESTRATIX ID | Function | Generates standardized ESTRATIX IDs for various components. | Definition | 1.0 | CStO | `../tools/csto/TL_FUNC_001_GenerateID_definition.md` | `src/utils/id_generator.py` |
| `TL_FUNC_002` | Vector Search Tool | Function | Provides a standardized interface for semantic vector searches. | Defined | 1.0 | CIO | `../tools/cio/TL_FUNC_002_VectorSearchTool_definition.md` | `src/domain/tools/cio/vector_search_tool.py` (Proposed) |
| `k001` | Matrix Updater Tool | Function | Safely and programmatically edits markdown matrix files. | Implemented | 1.0 | CIO | `../tools/cio/k001_MatrixUpdaterTool.md` | `src/domain/tools/cio/k001_matrix_updater.py` |

| `k002` | Source Scanner Tool | Function | Fetches metadata from a source URI to detect changes. | Implemented | 1.0 | CIO | `../tools/cio/k002_SourceScannerTool.md` | `src/domain/tools/cio/k002_source_scanner_tool.py` |

| `k003` | Web Scraper Tool | Function | Extracts content from static web pages. | Implemented | 1.0 | CTO | `../tools/cto/k003_WebScraperTool.md` | `src/domain/tools/cto/k003_web_scraper_tool.py` |
| `k004` | PDF Processor Tool | Function | Extracts text and data from PDF documents. | Implemented | 1.0 | CTO | `../tools/cto/k004_PDFProcessorTool.md` | `src/domain/tools/cto/k004_pdf_processor_tool.py` |
| `k005` | Content Processor Tool | Function | Cleans, normalizes, and chunks raw text for embedding. | Implemented | 1.0 | CTO | `../tools/cto/k005_ContentProcessorTool.md` | `src/domain/tools/cto/k005_content_processor_tool.py` |

| `k006` | Firecrawl Scraper Tool | API | Advanced, API-driven tool for reliable, large-scale web scraping. | Implemented | 1.0 | CTO | `../tools/cto/k006_FirecrawlScraperTool.md` | `src/domain/tools/cto/k006_firecrawl_scraper_tool.py` |
| `k007` | BrowserUse Tool | Function | Sophisticated, prompt-driven tool for intelligent web automation and structured data extraction. | Active | 2.0.0 | CTO | `../tools/cto/k007_BrowserUseTool.md` | `src/domain/tools/cto/k007_browser_use_tool.py` |
| `k008` | Crawl4AI Scraper | Function | Advanced, asynchronous tool for reliable web scraping and markdown generation. | Implemented | 1.0 | CTO | `../tools/cto/k008_Crawl4AIScraper.md` | `src/domain/tools/cto/k008_crawl4ai_scraper.py` |
| `k009` | ScrapeGraphAI Tool | Function | Intelligent, graph-based web scraping tool using ScrapeGraphAI. | Implemented | 1.0 | CTO | `../tools/cto/k009_ScrapeGraphAITool.md` | `src/domain/tools/cto/k009_scrapegraphai_tool.py` |
| `k010` | File System Tools | Function | Tools for creating files and directories. | Implemented | 1.0 | CTO | `../tools/cto/k010_FileSystemTools.md` | `src/domain/tools/cto/k010_file_system_tools.py` |
| `k011` | Headquarters Parser Tool | Function | Parses a Command Office headquarters definition file. | Implemented | 1.0 | CTO | `../tools/cto/k011_HeadquartersParserTool.md` | `src/domain/tools/cto/k011_headquarters_parser_tool.py` |
| `k012` | CTO Ingestion Wrappers | Function | Wrappers for various document and data ingestion services. | Implemented | 1.0 | CTO | `../tools/cto/k012_CTOIngestionWrappers.md` | `src/infrastructure/frameworks/crewAI/tools/cto/k012_cto_ingestion_wrappers.py` |
| `k013` | Web Content Fetcher | Function | Fetches and cleans the main textual content of a web page. | Implemented | 1.0 | CIO | `../tools/cio/k013_WebContentFetcher.md` | `src/infrastructure/frameworks/crewAI/tools/cio/k013_web_content_fetcher.py` |
| `k014` | Traffic Campaign Execution Tool | Function | Executes a traffic campaign based on provided parameters. | Implemented | 1.0 | CPO | `../tools/cpo/k014_TrafficCampaignExecutionTool.md` | `src/domain/tools/cpo/k014_traffic_campaign_execution_tool.py` |
| `k015` | Component Scaffolder | Function | Scaffolds new ESTRATIX components from templates. | Defined | 1.0 | CHRO | `../tools/chro/k015_ComponentScaffolder.md` | `NEEDS_IMPLEMENTATION` |
| `k016` | YAML File Editor | Function | Edits YAML files programmatically. | Defined | 1.0 | CHRO | `../tools/chro/k016_YAMLFileEditor.md` | `NEEDS_IMPLEMENTATION` |
| `k017` | Python File Editor | Function | Edits Python files programmatically. | Defined | 1.0 | CHRO | `../tools/chro/k017_PythonFileEditor.md` | `NEEDS_IMPLEMENTATION` |
| `k018` | Agent Tester | Function | Runs standardized tests against an agent definition. | Defined | 1.0 | CHRO | `../tools/chro/k018_AgentTester.md` | `NEEDS_IMPLEMENTATION` |
| `k019` | ApiClientTool | Function | Generic API client for connecting to RESTful services like CRM and Resource Management Systems. | Defined | 1.0 | COO | `../tools/coo/k019_ApiClientTool.md` | `NEEDS_IMPLEMENTATION` |
| `k020` | DataAnalysisTool | Function | A tool with data analysis libraries (e.g., Pandas) for statistical modeling and forecasting. | Defined | 1.0 | COO | `../tools/coo/k020_DataAnalysisTool.md` | `NEEDS_IMPLEMENTATION` |
| `k021` | ResourceSchedulerTool | Function | A tool for creating and optimizing resource allocation schedules based on defined rules. | Defined | 1.0 | COO | `../tools/coo/k021_ResourceSchedulerTool.md` | `NEEDS_IMPLEMENTATION` |

**Column Explanations:**

* **Tool ID (`TL_[TYPE]_[NAME]`):** `TL` for Tool. `[TYPE]` (e.g., `MCP`, `FUNC`, `CLI`, `API`, `LIB`). `[NAME]` a short, unique, PascalCase or descriptive name.
* **Tool Name / Identifier:** The common name or actual identifier (e.g., MCP server name, function name, CLI command).
* **Type:** Category of the tool.
* **Purpose / Description:** Brief explanation.
* **Status:** (e.g., Idea, Research, Proposal, Approved, Definition, Implemented, Deprecated).
* **Version:** Version of the tool or its wrapper/definition.
* **Responsible CO / Agent:** Who oversees this tool's definition and maintenance within ESTRATIX.
* **Conceptual Definition Link:** Path to the markdown definition file, e.g., `../tools/[type_lowercase]/[ToolName]_Definition.md`.
* **Implementation / Usage Link / Notes:** Path to code, MCP documentation, API docs, or specific usage notes.
* **Key Inputs / Parameters (Summary):** Critical inputs.
* **Expected Outputs / Behavior (Summary):** What the tool does/returns.
* **Associated ESTRATIX Components:** Where this tool is primarily used.
* **Notes:** Additional relevant info.
