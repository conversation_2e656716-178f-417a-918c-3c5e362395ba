#!/usr/bin/env python3
"""
Monitoring and Observability Agent

This agent handles system monitoring, performance tracking, alerting,
and observability for the Luxcrafts platform.
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import time
import psutil
import requests
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import docker
import prometheus_client
from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram
import smtplib
from email.mime.text import MimeText
from email.mime.multipart import MimeMultipart
import slack_sdk
from fabric import Connection

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MonitoringConfig:
    environment: str
    host: str
    services: List[str]
    alert_thresholds: Dict[str, float]
    notification_channels: Dict[str, Dict[str, str]]
    monitoring_interval: int = 60
    retention_days: int = 30
    prometheus_port: int = 9090
    grafana_port: int = 3001

class MonitoringAgent:
    """AI-powered monitoring and observability agent"""
    
    def __init__(self, config: MonitoringConfig):
        self.config = config
        self.docker_client = None
        self.metrics_registry = CollectorRegistry()
        self.metrics = self._setup_metrics()
        self.monitoring_log: List[Dict[str, Any]] = []
        self.alerts_sent: Dict[str, datetime] = {}
        
    def log_monitoring_event(self, event_type: str, status: str, details: str = "", metrics: Dict = None):
        """Log monitoring events for audit and analysis"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "status": status,
            "details": details,
            "metrics": metrics or {},
            "environment": self.config.environment
        }
        self.monitoring_log.append(log_entry)
        logger.info(f"{event_type}: {status} - {details}")
    
    def _setup_metrics(self) -> Dict[str, Any]:
        """Setup Prometheus metrics"""
        return {
            # System metrics
            "cpu_usage": Gauge('system_cpu_usage_percent', 'CPU usage percentage', registry=self.metrics_registry),
            "memory_usage": Gauge('system_memory_usage_percent', 'Memory usage percentage', registry=self.metrics_registry),
            "disk_usage": Gauge('system_disk_usage_percent', 'Disk usage percentage', registry=self.metrics_registry),
            "network_io": Gauge('system_network_io_bytes', 'Network I/O bytes', ['direction'], registry=self.metrics_registry),
            
            # Application metrics
            "app_response_time": Histogram('app_response_time_seconds', 'Application response time', ['endpoint'], registry=self.metrics_registry),
            "app_requests_total": Counter('app_requests_total', 'Total application requests', ['method', 'endpoint', 'status'], registry=self.metrics_registry),
            "app_errors_total": Counter('app_errors_total', 'Total application errors', ['type'], registry=self.metrics_registry),
            "app_active_users": Gauge('app_active_users', 'Active users count', registry=self.metrics_registry),
            
            # Database metrics
            "db_connections": Gauge('database_connections_active', 'Active database connections', ['database'], registry=self.metrics_registry),
            "db_query_time": Histogram('database_query_duration_seconds', 'Database query duration', ['database', 'operation'], registry=self.metrics_registry),
            "db_size": Gauge('database_size_bytes', 'Database size in bytes', ['database'], registry=self.metrics_registry),
            
            # Blockchain metrics
            "blockchain_sync_status": Gauge('blockchain_sync_status', 'Blockchain sync status', ['network'], registry=self.metrics_registry),
            "token_price": Gauge('token_price_usd', 'Token price in USD', ['token'], registry=self.metrics_registry),
            "transaction_count": Counter('blockchain_transactions_total', 'Total blockchain transactions', ['type'], registry=self.metrics_registry),
            
            # Business metrics
            "property_listings": Gauge('property_listings_active', 'Active property listings', registry=self.metrics_registry),
            "user_registrations": Counter('user_registrations_total', 'Total user registrations', registry=self.metrics_registry),
            "revenue": Gauge('revenue_total_usd', 'Total revenue in USD', ['period'], registry=self.metrics_registry),
            
            # AI/ML metrics
            "ai_requests": Counter('ai_requests_total', 'Total AI requests', ['service', 'model'], registry=self.metrics_registry),
            "ai_response_time": Histogram('ai_response_time_seconds', 'AI service response time', ['service'], registry=self.metrics_registry),
            "content_generation": Counter('content_generation_total', 'Total content generations', ['type'], registry=self.metrics_registry)
        }
    
    def collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system-level metrics"""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            self.metrics['cpu_usage'].set(cpu_percent)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            self.metrics['memory_usage'].set(memory_percent)
            
            # Disk metrics
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            self.metrics['disk_usage'].set(disk_percent)
            
            # Network metrics
            network = psutil.net_io_counters()
            self.metrics['network_io'].labels(direction='sent').set(network.bytes_sent)
            self.metrics['network_io'].labels(direction='recv').set(network.bytes_recv)
            
            # Process metrics
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    if 'luxcrafts' in proc.info['name'].lower() or 'node' in proc.info['name'].lower():
                        processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            system_metrics = {
                "cpu_usage": cpu_percent,
                "memory_usage": memory_percent,
                "disk_usage": disk_percent,
                "network_sent": network.bytes_sent,
                "network_recv": network.bytes_recv,
                "processes": processes,
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }
            
            self.log_monitoring_event(
                "System Metrics Collection",
                "SUCCESS",
                f"CPU: {cpu_percent:.1f}%, Memory: {memory_percent:.1f}%, Disk: {disk_percent:.1f}%",
                system_metrics
            )
            
            return system_metrics
            
        except Exception as e:
            self.log_monitoring_event(
                "System Metrics Collection",
                "FAILED",
                f"Failed to collect system metrics: {str(e)}"
            )
            return {}
    
    def collect_application_metrics(self) -> Dict[str, Any]:
        """Collect application-specific metrics"""
        try:
            app_metrics = {}
            
            # Health check endpoints
            health_endpoints = {
                "main_app": f"http://localhost:3000/health",
                "api": f"http://localhost:3000/api/health",
                "nginx": f"http://localhost/health"
            }
            
            for service, endpoint in health_endpoints.items():
                try:
                    start_time = time.time()
                    response = requests.get(endpoint, timeout=10)
                    response_time = time.time() - start_time
                    
                    app_metrics[f"{service}_status"] = response.status_code
                    app_metrics[f"{service}_response_time"] = response_time
                    
                    # Update Prometheus metrics
                    self.metrics['app_response_time'].labels(endpoint=service).observe(response_time)
                    self.metrics['app_requests_total'].labels(
                        method='GET',
                        endpoint=service,
                        status=str(response.status_code)
                    ).inc()
                    
                except requests.RequestException as e:
                    app_metrics[f"{service}_status"] = 0
                    app_metrics[f"{service}_response_time"] = 0
                    self.metrics['app_errors_total'].labels(type='connection_error').inc()
                    logger.warning(f"Health check failed for {service}: {str(e)}")
            
            # Docker container metrics
            if self.docker_client:
                try:
                    containers = self.docker_client.containers.list()
                    app_metrics['container_count'] = len(containers)
                    
                    for container in containers:
                        if 'luxcrafts' in container.name.lower():
                            stats = container.stats(stream=False)
                            cpu_usage = self._calculate_cpu_percent(stats)
                            memory_usage = stats['memory_stats']['usage'] / stats['memory_stats']['limit'] * 100
                            
                            app_metrics[f"{container.name}_cpu"] = cpu_usage
                            app_metrics[f"{container.name}_memory"] = memory_usage
                            
                except Exception as e:
                    logger.warning(f"Failed to collect Docker metrics: {str(e)}")
            
            self.log_monitoring_event(
                "Application Metrics Collection",
                "SUCCESS",
                f"Collected metrics for {len(health_endpoints)} services",
                app_metrics
            )
            
            return app_metrics
            
        except Exception as e:
            self.log_monitoring_event(
                "Application Metrics Collection",
                "FAILED",
                f"Failed to collect application metrics: {str(e)}"
            )
            return {}
    
    def _calculate_cpu_percent(self, stats: Dict) -> float:
        """Calculate CPU percentage from Docker stats"""
        try:
            cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - stats['precpu_stats']['cpu_usage']['total_usage']
            system_delta = stats['cpu_stats']['system_cpu_usage'] - stats['precpu_stats']['system_cpu_usage']
            cpu_count = stats['cpu_stats']['online_cpus']
            
            if system_delta > 0:
                return (cpu_delta / system_delta) * cpu_count * 100.0
            return 0.0
        except (KeyError, ZeroDivisionError):
            return 0.0
    
    def collect_database_metrics(self) -> Dict[str, Any]:
        """Collect database performance metrics"""
        try:
            db_metrics = {}
            
            # PostgreSQL metrics
            try:
                import psycopg2
                conn = psycopg2.connect(
                    host=os.getenv('POSTGRES_HOST', 'localhost'),
                    database=os.getenv('POSTGRES_DB', 'luxcrafts'),
                    user=os.getenv('POSTGRES_USER', 'postgres'),
                    password=os.getenv('POSTGRES_PASSWORD', '')
                )
                
                with conn.cursor() as cursor:
                    # Active connections
                    cursor.execute("SELECT count(*) FROM pg_stat_activity WHERE state = 'active';")
                    active_connections = cursor.fetchone()[0]
                    db_metrics['postgres_active_connections'] = active_connections
                    self.metrics['db_connections'].labels(database='postgres').set(active_connections)
                    
                    # Database size
                    cursor.execute("SELECT pg_database_size(current_database());")
                    db_size = cursor.fetchone()[0]
                    db_metrics['postgres_size_bytes'] = db_size
                    self.metrics['db_size'].labels(database='postgres').set(db_size)
                    
                conn.close()
                
            except Exception as e:
                logger.warning(f"Failed to collect PostgreSQL metrics: {str(e)}")
            
            # MongoDB metrics
            try:
                from pymongo import MongoClient
                client = MongoClient(os.getenv('MONGODB_URI', 'mongodb://localhost:27017/'))
                db = client[os.getenv('MONGODB_DB', 'luxcrafts')]
                
                # Connection count
                server_status = db.command('serverStatus')
                connections = server_status['connections']['current']
                db_metrics['mongodb_connections'] = connections
                self.metrics['db_connections'].labels(database='mongodb').set(connections)
                
                # Database stats
                stats = db.command('dbStats')
                db_metrics['mongodb_size_bytes'] = stats['dataSize']
                self.metrics['db_size'].labels(database='mongodb').set(stats['dataSize'])
                
                client.close()
                
            except Exception as e:
                logger.warning(f"Failed to collect MongoDB metrics: {str(e)}")
            
            # Redis metrics
            try:
                import redis
                r = redis.Redis(
                    host=os.getenv('REDIS_HOST', 'localhost'),
                    port=int(os.getenv('REDIS_PORT', 6379)),
                    decode_responses=True
                )
                
                info = r.info()
                db_metrics['redis_connected_clients'] = info['connected_clients']
                db_metrics['redis_used_memory'] = info['used_memory']
                db_metrics['redis_keyspace_hits'] = info['keyspace_hits']
                db_metrics['redis_keyspace_misses'] = info['keyspace_misses']
                
                self.metrics['db_connections'].labels(database='redis').set(info['connected_clients'])
                
            except Exception as e:
                logger.warning(f"Failed to collect Redis metrics: {str(e)}")
            
            self.log_monitoring_event(
                "Database Metrics Collection",
                "SUCCESS",
                f"Collected metrics for {len(db_metrics)} database parameters",
                db_metrics
            )
            
            return db_metrics
            
        except Exception as e:
            self.log_monitoring_event(
                "Database Metrics Collection",
                "FAILED",
                f"Failed to collect database metrics: {str(e)}"
            )
            return {}
    
    def collect_blockchain_metrics(self) -> Dict[str, Any]:
        """Collect blockchain and Web3 metrics"""
        try:
            blockchain_metrics = {}
            
            # Ethereum mainnet metrics
            try:
                from web3 import Web3
                w3 = Web3(Web3.HTTPProvider(os.getenv('ETHEREUM_RPC_URL', 'https://mainnet.infura.io/v3/YOUR_PROJECT_ID')))
                
                if w3.is_connected():
                    latest_block = w3.eth.get_block('latest')
                    blockchain_metrics['ethereum_latest_block'] = latest_block['number']
                    blockchain_metrics['ethereum_gas_price'] = w3.eth.gas_price
                    
                    # Sync status
                    sync_status = w3.eth.syncing
                    blockchain_metrics['ethereum_syncing'] = bool(sync_status)
                    self.metrics['blockchain_sync_status'].labels(network='ethereum').set(1 if not sync_status else 0)
                    
            except Exception as e:
                logger.warning(f"Failed to collect Ethereum metrics: {str(e)}")
            
            # Token price metrics (using CoinGecko API)
            try:
                response = requests.get('https://api.coingecko.com/api/v3/simple/price?ids=ethereum,bitcoin&vs_currencies=usd', timeout=10)
                if response.status_code == 200:
                    prices = response.json()
                    if 'ethereum' in prices:
                        eth_price = prices['ethereum']['usd']
                        blockchain_metrics['ethereum_price_usd'] = eth_price
                        self.metrics['token_price'].labels(token='ethereum').set(eth_price)
                    
                    if 'bitcoin' in prices:
                        btc_price = prices['bitcoin']['usd']
                        blockchain_metrics['bitcoin_price_usd'] = btc_price
                        self.metrics['token_price'].labels(token='bitcoin').set(btc_price)
                        
            except Exception as e:
                logger.warning(f"Failed to collect token prices: {str(e)}")
            
            self.log_monitoring_event(
                "Blockchain Metrics Collection",
                "SUCCESS",
                f"Collected {len(blockchain_metrics)} blockchain metrics",
                blockchain_metrics
            )
            
            return blockchain_metrics
            
        except Exception as e:
            self.log_monitoring_event(
                "Blockchain Metrics Collection",
                "FAILED",
                f"Failed to collect blockchain metrics: {str(e)}"
            )
            return {}
    
    def check_alerts(self, metrics: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Check metrics against alert thresholds"""
        alerts = []
        current_time = datetime.now()
        
        for metric_name, threshold in self.config.alert_thresholds.items():
            if metric_name in metrics:
                value = metrics[metric_name]
                
                # Check if threshold is exceeded
                if isinstance(value, (int, float)) and value > threshold:
                    alert_key = f"{metric_name}_{self.config.environment}"
                    
                    # Rate limiting: don't send same alert within 15 minutes
                    if alert_key in self.alerts_sent:
                        time_diff = current_time - self.alerts_sent[alert_key]
                        if time_diff < timedelta(minutes=15):
                            continue
                    
                    alert = {
                        "metric": metric_name,
                        "value": value,
                        "threshold": threshold,
                        "severity": "critical" if value > threshold * 1.5 else "warning",
                        "timestamp": current_time.isoformat(),
                        "environment": self.config.environment,
                        "host": self.config.host
                    }
                    
                    alerts.append(alert)
                    self.alerts_sent[alert_key] = current_time
                    
                    self.log_monitoring_event(
                        "Alert Triggered",
                        "WARNING",
                        f"{metric_name}: {value} > {threshold}",
                        alert
                    )
        
        return alerts
    
    def send_notifications(self, alerts: List[Dict[str, Any]]) -> None:
        """Send alert notifications via configured channels"""
        if not alerts:
            return
        
        for channel_name, channel_config in self.config.notification_channels.items():
            try:
                if channel_name == 'slack':
                    self._send_slack_notification(alerts, channel_config)
                elif channel_name == 'email':
                    self._send_email_notification(alerts, channel_config)
                elif channel_name == 'webhook':
                    self._send_webhook_notification(alerts, channel_config)
                    
            except Exception as e:
                logger.error(f"Failed to send notification via {channel_name}: {str(e)}")
    
    def _send_slack_notification(self, alerts: List[Dict[str, Any]], config: Dict[str, str]) -> None:
        """Send alerts to Slack"""
        try:
            client = slack_sdk.WebClient(token=config['token'])
            
            for alert in alerts:
                color = "danger" if alert['severity'] == "critical" else "warning"
                message = {
                    "channel": config['channel'],
                    "attachments": [{
                        "color": color,
                        "title": f"🚨 {alert['severity'].upper()} Alert - {alert['metric']}",
                        "fields": [
                            {"title": "Value", "value": str(alert['value']), "short": True},
                            {"title": "Threshold", "value": str(alert['threshold']), "short": True},
                            {"title": "Environment", "value": alert['environment'], "short": True},
                            {"title": "Host", "value": alert['host'], "short": True}
                        ],
                        "timestamp": int(datetime.fromisoformat(alert['timestamp']).timestamp())
                    }]
                }
                
                client.chat_postMessage(**message)
                
        except Exception as e:
            logger.error(f"Failed to send Slack notification: {str(e)}")
    
    def _send_email_notification(self, alerts: List[Dict[str, Any]], config: Dict[str, str]) -> None:
        """Send alerts via email"""
        try:
            msg = MimeMultipart()
            msg['From'] = config['from_email']
            msg['To'] = config['to_email']
            msg['Subject'] = f"Luxcrafts Monitoring Alert - {self.config.environment}"
            
            body = "Alert Summary:\n\n"
            for alert in alerts:
                body += f"• {alert['severity'].upper()}: {alert['metric']} = {alert['value']} (threshold: {alert['threshold']})\n"
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(config['smtp_server'], int(config.get('smtp_port', 587)))
            server.starttls()
            server.login(config['smtp_user'], config['smtp_password'])
            server.send_message(msg)
            server.quit()
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
    
    def _send_webhook_notification(self, alerts: List[Dict[str, Any]], config: Dict[str, str]) -> None:
        """Send alerts via webhook"""
        try:
            payload = {
                "environment": self.config.environment,
                "host": self.config.host,
                "timestamp": datetime.now().isoformat(),
                "alerts": alerts
            }
            
            response = requests.post(
                config['url'],
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            response.raise_for_status()
            
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {str(e)}")
    
    def setup_docker_client(self) -> None:
        """Initialize Docker client"""
        try:
            self.docker_client = docker.from_env()
            self.log_monitoring_event("Docker Client Setup", "SUCCESS", "Docker client initialized")
        except Exception as e:
            self.log_monitoring_event("Docker Client Setup", "FAILED", f"Failed to initialize Docker client: {str(e)}")
    
    def start_prometheus_server(self) -> None:
        """Start Prometheus metrics server"""
        try:
            prometheus_client.start_http_server(self.config.prometheus_port, registry=self.metrics_registry)
            self.log_monitoring_event(
                "Prometheus Server",
                "SUCCESS",
                f"Prometheus metrics server started on port {self.config.prometheus_port}"
            )
        except Exception as e:
            self.log_monitoring_event(
                "Prometheus Server",
                "FAILED",
                f"Failed to start Prometheus server: {str(e)}"
            )
    
    def run_monitoring_cycle(self) -> Dict[str, Any]:
        """Run a complete monitoring cycle"""
        cycle_start = time.time()
        all_metrics = {}
        
        try:
            # Collect all metrics
            system_metrics = self.collect_system_metrics()
            app_metrics = self.collect_application_metrics()
            db_metrics = self.collect_database_metrics()
            blockchain_metrics = self.collect_blockchain_metrics()
            
            # Combine all metrics
            all_metrics.update(system_metrics)
            all_metrics.update(app_metrics)
            all_metrics.update(db_metrics)
            all_metrics.update(blockchain_metrics)
            
            # Check for alerts
            alerts = self.check_alerts(all_metrics)
            
            # Send notifications if needed
            if alerts:
                self.send_notifications(alerts)
            
            cycle_duration = time.time() - cycle_start
            
            self.log_monitoring_event(
                "Monitoring Cycle",
                "SUCCESS",
                f"Completed monitoring cycle in {cycle_duration:.2f}s, {len(alerts)} alerts triggered",
                {"cycle_duration": cycle_duration, "metrics_count": len(all_metrics), "alerts_count": len(alerts)}
            )
            
            return all_metrics
            
        except Exception as e:
            self.log_monitoring_event(
                "Monitoring Cycle",
                "FAILED",
                f"Monitoring cycle failed: {str(e)}"
            )
            return {}
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive monitoring report"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "environment": self.config.environment,
            "host": self.config.host,
            "monitoring_log": self.monitoring_log[-100:],  # Last 100 events
            "alert_history": list(self.alerts_sent.keys()),
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "monitoring_agent_version": "1.0.0"
            }
        }
        
        return report

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Luxcrafts Monitoring Agent')
    parser.add_argument('--environment', default='staging', choices=['production', 'staging', 'development'])
    parser.add_argument('--host', default='localhost')
    parser.add_argument('--config-file', help='Path to configuration file')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    parser.add_argument('--report', action='store_true', help='Generate report and exit')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config_file and os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            config_data = json.load(f)
    else:
        # Default configuration
        config_data = {
            "environment": args.environment,
            "host": args.host,
            "services": ["main_app", "api", "nginx"],
            "alert_thresholds": {
                "cpu_usage": 80.0,
                "memory_usage": 85.0,
                "disk_usage": 90.0,
                "main_app_response_time": 2.0,
                "api_response_time": 1.0
            },
            "notification_channels": {
                "slack": {
                    "token": os.getenv('SLACK_BOT_TOKEN', ''),
                    "channel": os.getenv('SLACK_ALERT_CHANNEL', '#alerts')
                },
                "email": {
                    "smtp_server": os.getenv('SMTP_SERVER', 'smtp.gmail.com'),
                    "smtp_port": os.getenv('SMTP_PORT', '587'),
                    "smtp_user": os.getenv('SMTP_USER', ''),
                    "smtp_password": os.getenv('SMTP_PASSWORD', ''),
                    "from_email": os.getenv('FROM_EMAIL', ''),
                    "to_email": os.getenv('TO_EMAIL', '')
                }
            },
            "monitoring_interval": 60,
            "retention_days": 30
        }
    
    config = MonitoringConfig(**config_data)
    agent = MonitoringAgent(config)
    
    # Setup Docker client and Prometheus server
    agent.setup_docker_client()
    agent.start_prometheus_server()
    
    if args.report:
        # Generate and print report
        report = agent.generate_report()
        print(json.dumps(report, indent=2))
        return
    
    if args.daemon:
        # Run as daemon
        logger.info(f"Starting monitoring agent in daemon mode for {config.environment} environment")
        
        try:
            while True:
                agent.run_monitoring_cycle()
                time.sleep(config.monitoring_interval)
                
        except KeyboardInterrupt:
            logger.info("Monitoring agent stopped by user")
        except Exception as e:
            logger.error(f"Monitoring agent crashed: {str(e)}")
            sys.exit(1)
    else:
        # Run single cycle
        logger.info(f"Running single monitoring cycle for {config.environment} environment")
        metrics = agent.run_monitoring_cycle()
        print(json.dumps(metrics, indent=2))

if __name__ == "__main__":
    main()