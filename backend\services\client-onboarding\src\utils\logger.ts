import pino from 'pino';
import { environment } from '@/config/environment';

// Create Pino logger instance
export const logger = pino({
  level: environment.nodeEnv === 'production' ? 'info' : 'debug',
  base: {
    service: 'client-onboarding',
    version: process.env.npm_package_version || '1.0.0'
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  formatters: {
    level: (label) => {
      return { level: label };
    }
  },
  transport: environment.nodeEnv !== 'production' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'SYS:standard',
      ignore: 'pid,hostname'
    }
  } : undefined
});

// Create a stream object for Morgan HTTP request logging
export const loggerStream = {
  write: (message: string) => {
    logger.info(message.trim());
  }
};