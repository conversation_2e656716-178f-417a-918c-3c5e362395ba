import dotenv from 'dotenv';
import { z } from 'zod';

dotenv.config();

const envSchema = z.object({
  // Server Configuration
  PORT: z.string().default('3004'),
  HOST: z.string().default('0.0.0.0'),
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  
  // Security
  JWT_SECRET: z.string().min(32),
  CORS_ORIGINS: z.string().transform(val => val.split(',')),
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'),
  
  // Database
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url(),
  
  // External Services
  SMART_CONTRACTS_SERVICE_URL: z.string().url().default('http://localhost:3003'),
  CLIENT_ONBOARDING_SERVICE_URL: z.string().url().default('http://localhost:3005'),
  AGENT_ORCHESTRATION_SERVICE_URL: z.string().url().default('http://localhost:3006'),
  
  // File Upload
  MAX_FILE_SIZE: z.string().transform(Number).default('10485760'),
  UPLOAD_DIR: z.string().default('./uploads'),
  
  // Queue Configuration
  QUEUE_REDIS_URL: z.string().url(),
  
  // Monitoring
  SENTRY_DSN: z.string().optional(),
  LOG_LEVEL: z.enum(['error', 'warn', 'info', 'debug']).default('info'),
  
  // Webhook Configuration
  WEBHOOK_SECRET: z.string(),
  
  // Feature Flags
  ENABLE_NOTIFICATIONS: z.string().transform(val => val === 'true').default('true'),
  ENABLE_ANALYTICS: z.string().transform(val => val === 'true').default('true'),
  ENABLE_INTEGRATIONS: z.string().transform(val => val === 'true').default('true'),
});

const env = envSchema.parse(process.env);

export const config = {
  // Server
  PORT: parseInt(env.PORT),
  HOST: env.HOST,
  NODE_ENV: env.NODE_ENV,
  
  // Security
  JWT_SECRET: env.JWT_SECRET,
  CORS_ORIGINS: env.CORS_ORIGINS,
  RATE_LIMIT_MAX: env.RATE_LIMIT_MAX,
  RATE_LIMIT_WINDOW: env.RATE_LIMIT_WINDOW,
  
  // Database
  DATABASE_URL: env.DATABASE_URL,
  REDIS_URL: env.REDIS_URL,
  
  // External Services
  SMART_CONTRACTS_SERVICE_URL: env.SMART_CONTRACTS_SERVICE_URL,
  CLIENT_ONBOARDING_SERVICE_URL: env.CLIENT_ONBOARDING_SERVICE_URL,
  AGENT_ORCHESTRATION_SERVICE_URL: env.AGENT_ORCHESTRATION_SERVICE_URL,
  
  // File Upload
  MAX_FILE_SIZE: env.MAX_FILE_SIZE,
  UPLOAD_DIR: env.UPLOAD_DIR,
  
  // Queue
  QUEUE_REDIS_URL: env.QUEUE_REDIS_URL,
  
  // Monitoring
  SENTRY_DSN: env.SENTRY_DSN,
  LOG_LEVEL: env.LOG_LEVEL,
  
  // Webhook
  WEBHOOK_SECRET: env.WEBHOOK_SECRET,
  
  // Features
  ENABLE_NOTIFICATIONS: env.ENABLE_NOTIFICATIONS,
  ENABLE_ANALYTICS: env.ENABLE_ANALYTICS,
  ENABLE_INTEGRATIONS: env.ENABLE_INTEGRATIONS,
};

export const logConfig = {
  level: config.LOG_LEVEL,
  transport: config.NODE_ENV === 'development' ? {
    target: 'pino-pretty',
    options: {
      colorize: true,
      translateTime: 'UTC:yyyy-mm-dd HH:MM:ss',
      ignore: 'pid,hostname'
    }
  } : undefined
};

// Log configuration on startup
if (config.NODE_ENV === 'development') {
  console.log('🔧 Project Management service configuration loaded for development environment');
  console.log(`🚀 Server will start on ${config.HOST}:${config.PORT}`);
}