#!/bin/bash

# Git Worktree Manager for Sorteo Estelar
# Implements trunk-based development with automated worktree management

set -euo pipefail

# Configuration
PROJECT_NAME="sorteo-estelar"
BASE_DIR="/opt/sorteo-estelar"
WORKTREE_DIR="$BASE_DIR/worktrees"
MAIN_REPO="$BASE_DIR/main-repo"
REMOTE_URL="https://github.com/estratix/sorteo-estelar.git"
VPS_HOST="**************"
VPS_PORT="2222"
VPS_USER="admin"

# Branch configuration
MAIN_BRANCH="main"
DEVELOP_BRANCH="develop"
STAGING_BRANCH="staging"
FEATURE_PREFIX="feature/"
HOTFIX_PREFIX="hotfix/"
RELEASE_PREFIX="release/"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

# Initialize main repository
init_main_repo() {
    log_info "Initializing main repository..."
    
    if [ ! -d "$MAIN_REPO" ]; then
        log_info "Cloning main repository..."
        git clone "$REMOTE_URL" "$MAIN_REPO"
    else
        log_info "Main repository exists, updating..."
        cd "$MAIN_REPO"
        git fetch --all --prune
        git checkout "$MAIN_BRANCH"
        git pull origin "$MAIN_BRANCH"
    fi
    
    cd "$MAIN_REPO"
    
    # Ensure required branches exist
    for branch in "$DEVELOP_BRANCH" "$STAGING_BRANCH"; do
        if ! git show-ref --verify --quiet "refs/heads/$branch"; then
            log_info "Creating branch: $branch"
            git checkout -b "$branch" "$MAIN_BRANCH"
            git push -u origin "$branch"
        fi
    done
    
    git checkout "$MAIN_BRANCH"
    log_success "Main repository initialized"
}

# Create worktree directory structure
setup_worktree_structure() {
    log_info "Setting up worktree directory structure..."
    
    mkdir -p "$WORKTREE_DIR"/{main,develop,staging,features,hotfixes,releases}
    mkdir -p "$BASE_DIR"/{logs,backups,temp}
    
    # Create worktrees for main branches
    cd "$MAIN_REPO"
    
    for branch in "$MAIN_BRANCH" "$DEVELOP_BRANCH" "$STAGING_BRANCH"; do
        local worktree_path="$WORKTREE_DIR/${branch}"
        if [ ! -d "$worktree_path" ]; then
            log_info "Creating worktree for branch: $branch"
            git worktree add "$worktree_path" "$branch"
        else
            log_debug "Worktree already exists for branch: $branch"
        fi
    done
    
    log_success "Worktree structure setup completed"
}

# Create feature branch worktree
create_feature_worktree() {
    local feature_name="$1"
    local base_branch="${2:-$DEVELOP_BRANCH}"
    
    if [ -z "$feature_name" ]; then
        log_error "Feature name is required"
        return 1
    fi
    
    local branch_name="${FEATURE_PREFIX}${feature_name}"
    local worktree_path="$WORKTREE_DIR/features/$feature_name"
    
    log_info "Creating feature worktree: $feature_name"
    
    cd "$MAIN_REPO"
    
    # Ensure base branch is up to date
    git fetch origin
    git checkout "$base_branch"
    git pull origin "$base_branch"
    
    # Create feature branch and worktree
    if git show-ref --verify --quiet "refs/heads/$branch_name"; then
        log_warning "Feature branch already exists: $branch_name"
    else
        git checkout -b "$branch_name" "$base_branch"
        git push -u origin "$branch_name"
    fi
    
    if [ ! -d "$worktree_path" ]; then
        git worktree add "$worktree_path" "$branch_name"
        log_success "Feature worktree created: $worktree_path"
    else
        log_warning "Feature worktree already exists: $worktree_path"
    fi
    
    # Create development environment
    cd "$worktree_path"
    
    # Install dependencies if package.json exists
    if [ -f "package.json" ]; then
        log_info "Installing dependencies for feature: $feature_name"
        npm install
    fi
    
    # Create feature-specific configuration
    cat > ".env.feature" <<EOF
# Feature environment configuration
FEATURE_NAME=$feature_name
BRANCH_NAME=$branch_name
BASE_BRANCH=$base_branch
WORKTREE_PATH=$worktree_path
CREATED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
EOF
    
    log_success "Feature environment ready: $feature_name"
    echo "  📁 Worktree: $worktree_path"
    echo "  🌿 Branch: $branch_name"
    echo "  📋 Base: $base_branch"
}

# Create hotfix branch worktree
create_hotfix_worktree() {
    local hotfix_name="$1"
    local base_branch="${2:-$MAIN_BRANCH}"
    
    if [ -z "$hotfix_name" ]; then
        log_error "Hotfix name is required"
        return 1
    fi
    
    local branch_name="${HOTFIX_PREFIX}${hotfix_name}"
    local worktree_path="$WORKTREE_DIR/hotfixes/$hotfix_name"
    
    log_info "Creating hotfix worktree: $hotfix_name"
    
    cd "$MAIN_REPO"
    
    # Ensure base branch is up to date
    git fetch origin
    git checkout "$base_branch"
    git pull origin "$base_branch"
    
    # Create hotfix branch and worktree
    if git show-ref --verify --quiet "refs/heads/$branch_name"; then
        log_warning "Hotfix branch already exists: $branch_name"
    else
        git checkout -b "$branch_name" "$base_branch"
        git push -u origin "$branch_name"
    fi
    
    if [ ! -d "$worktree_path" ]; then
        git worktree add "$worktree_path" "$branch_name"
        log_success "Hotfix worktree created: $worktree_path"
    else
        log_warning "Hotfix worktree already exists: $worktree_path"
    fi
    
    # Create hotfix environment
    cd "$worktree_path"
    
    # Install dependencies
    if [ -f "package.json" ]; then
        log_info "Installing dependencies for hotfix: $hotfix_name"
        npm install
    fi
    
    # Create hotfix-specific configuration
    cat > ".env.hotfix" <<EOF
# Hotfix environment configuration
HOTFIX_NAME=$hotfix_name
BRANCH_NAME=$branch_name
BASE_BRANCH=$base_branch
WORKTREE_PATH=$worktree_path
CREATED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
PRIORITY=HIGH
EOF
    
    log_success "Hotfix environment ready: $hotfix_name"
    echo "  🚨 Worktree: $worktree_path"
    echo "  🌿 Branch: $branch_name"
    echo "  📋 Base: $base_branch"
}

# Create release branch worktree
create_release_worktree() {
    local version="$1"
    local base_branch="${2:-$DEVELOP_BRANCH}"
    
    if [ -z "$version" ]; then
        log_error "Version is required"
        return 1
    fi
    
    local branch_name="${RELEASE_PREFIX}${version}"
    local worktree_path="$WORKTREE_DIR/releases/$version"
    
    log_info "Creating release worktree: $version"
    
    cd "$MAIN_REPO"
    
    # Ensure base branch is up to date
    git fetch origin
    git checkout "$base_branch"
    git pull origin "$base_branch"
    
    # Create release branch and worktree
    if git show-ref --verify --quiet "refs/heads/$branch_name"; then
        log_warning "Release branch already exists: $branch_name"
    else
        git checkout -b "$branch_name" "$base_branch"
        git push -u origin "$branch_name"
    fi
    
    if [ ! -d "$worktree_path" ]; then
        git worktree add "$worktree_path" "$branch_name"
        log_success "Release worktree created: $worktree_path"
    else
        log_warning "Release worktree already exists: $worktree_path"
    fi
    
    # Create release environment
    cd "$worktree_path"
    
    # Install dependencies
    if [ -f "package.json" ]; then
        log_info "Installing dependencies for release: $version"
        npm install
    fi
    
    # Update version in package.json
    if [ -f "package.json" ] && command -v jq &> /dev/null; then
        log_info "Updating version in package.json to: $version"
        jq ".version = \"$version\"" package.json > package.json.tmp
        mv package.json.tmp package.json
        git add package.json
        git commit -m "chore: bump version to $version"
        git push origin "$branch_name"
    fi
    
    # Create release-specific configuration
    cat > ".env.release" <<EOF
# Release environment configuration
RELEASE_VERSION=$version
BRANCH_NAME=$branch_name
BASE_BRANCH=$base_branch
WORKTREE_PATH=$worktree_path
CREATED_AT=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
STATUS=PREPARATION
EOF
    
    log_success "Release environment ready: $version"
    echo "  🚀 Worktree: $worktree_path"
    echo "  🌿 Branch: $branch_name"
    echo "  📋 Base: $base_branch"
}

# Sync worktree with remote
sync_worktree() {
    local worktree_path="$1"
    
    if [ ! -d "$worktree_path" ]; then
        log_error "Worktree does not exist: $worktree_path"
        return 1
    fi
    
    log_info "Syncing worktree: $worktree_path"
    
    cd "$worktree_path"
    
    local current_branch
    current_branch=$(git branch --show-current)
    
    # Fetch latest changes
    git fetch origin
    
    # Check if branch exists on remote
    if git show-ref --verify --quiet "refs/remotes/origin/$current_branch"; then
        # Rebase on remote branch
        if git rebase "origin/$current_branch"; then
            log_success "Worktree synced successfully: $current_branch"
        else
            log_warning "Rebase conflicts detected in: $current_branch"
            log_info "Please resolve conflicts manually"
            return 1
        fi
    else
        log_warning "Branch does not exist on remote: $current_branch"
    fi
}

# Clean up stale worktrees
cleanup_stale_worktrees() {
    local max_age_days="${1:-7}"
    
    log_info "Cleaning up stale worktrees (older than $max_age_days days)..."
    
    cd "$MAIN_REPO"
    
    # Clean up feature worktrees
    if [ -d "$WORKTREE_DIR/features" ]; then
        find "$WORKTREE_DIR/features" -maxdepth 1 -type d -mtime +"$max_age_days" | while read -r worktree_path; do
            if [ "$worktree_path" != "$WORKTREE_DIR/features" ]; then
                local feature_name
                feature_name=$(basename "$worktree_path")
                local branch_name="${FEATURE_PREFIX}${feature_name}"
                
                log_info "Removing stale feature worktree: $feature_name"
                
                # Remove worktree
                git worktree remove "$worktree_path" --force || true
                
                # Delete branch if it exists
                if git show-ref --verify --quiet "refs/heads/$branch_name"; then
                    git branch -D "$branch_name" || true
                    git push origin --delete "$branch_name" || true
                fi
                
                log_success "Removed stale feature: $feature_name"
            fi
        done
    fi
    
    # Clean up hotfix worktrees (shorter retention)
    local hotfix_max_age="${2:-3}"
    if [ -d "$WORKTREE_DIR/hotfixes" ]; then
        find "$WORKTREE_DIR/hotfixes" -maxdepth 1 -type d -mtime +"$hotfix_max_age" | while read -r worktree_path; do
            if [ "$worktree_path" != "$WORKTREE_DIR/hotfixes" ]; then
                local hotfix_name
                hotfix_name=$(basename "$worktree_path")
                local branch_name="${HOTFIX_PREFIX}${hotfix_name}"
                
                log_info "Removing stale hotfix worktree: $hotfix_name"
                
                # Remove worktree
                git worktree remove "$worktree_path" --force || true
                
                # Delete branch if it exists
                if git show-ref --verify --quiet "refs/heads/$branch_name"; then
                    git branch -D "$branch_name" || true
                    git push origin --delete "$branch_name" || true
                fi
                
                log_success "Removed stale hotfix: $hotfix_name"
            fi
        done
    fi
    
    # Prune worktrees
    git worktree prune
    
    log_success "Stale worktree cleanup completed"
}

# List all worktrees
list_worktrees() {
    log_info "Current worktrees:"
    
    cd "$MAIN_REPO"
    
    echo -e "\n${CYAN}Main Branches:${NC}"
    for branch in "$MAIN_BRANCH" "$DEVELOP_BRANCH" "$STAGING_BRANCH"; do
        local worktree_path="$WORKTREE_DIR/${branch}"
        if [ -d "$worktree_path" ]; then
            echo -e "  ✅ ${GREEN}$branch${NC} -> $worktree_path"
        else
            echo -e "  ❌ ${RED}$branch${NC} -> Missing"
        fi
    done
    
    echo -e "\n${CYAN}Feature Branches:${NC}"
    if [ -d "$WORKTREE_DIR/features" ] && [ "$(ls -A "$WORKTREE_DIR/features" 2>/dev/null)" ]; then
        for feature_dir in "$WORKTREE_DIR/features"/*; do
            if [ -d "$feature_dir" ]; then
                local feature_name
                feature_name=$(basename "$feature_dir")
                echo -e "  🌿 ${YELLOW}$feature_name${NC} -> $feature_dir"
            fi
        done
    else
        echo -e "  ${PURPLE}No feature branches${NC}"
    fi
    
    echo -e "\n${CYAN}Hotfix Branches:${NC}"
    if [ -d "$WORKTREE_DIR/hotfixes" ] && [ "$(ls -A "$WORKTREE_DIR/hotfixes" 2>/dev/null)" ]; then
        for hotfix_dir in "$WORKTREE_DIR/hotfixes"/*; do
            if [ -d "$hotfix_dir" ]; then
                local hotfix_name
                hotfix_name=$(basename "$hotfix_dir")
                echo -e "  🚨 ${RED}$hotfix_name${NC} -> $hotfix_dir"
            fi
        done
    else
        echo -e "  ${PURPLE}No hotfix branches${NC}"
    fi
    
    echo -e "\n${CYAN}Release Branches:${NC}"
    if [ -d "$WORKTREE_DIR/releases" ] && [ "$(ls -A "$WORKTREE_DIR/releases" 2>/dev/null)" ]; then
        for release_dir in "$WORKTREE_DIR/releases"/*; do
            if [ -d "$release_dir" ]; then
                local release_name
                release_name=$(basename "$release_dir")
                echo -e "  🚀 ${BLUE}$release_name${NC} -> $release_dir"
            fi
        done
    else
        echo -e "  ${PURPLE}No release branches${NC}"
    fi
    
    echo -e "\n${CYAN}Git Worktree Status:${NC}"
    git worktree list
}

# Validate branch naming conventions
validate_branch_name() {
    local branch_name="$1"
    
    # Check if branch follows naming conventions
    if [[ "$branch_name" =~ ^(feature|hotfix|release)/.+ ]]; then
        return 0
    elif [[ "$branch_name" == "$MAIN_BRANCH" ]] || [[ "$branch_name" == "$DEVELOP_BRANCH" ]] || [[ "$branch_name" == "$STAGING_BRANCH" ]]; then
        return 0
    else
        log_error "Invalid branch name: $branch_name"
        log_info "Branch names must follow the pattern: feature/*, hotfix/*, release/*, main, develop, or staging"
        return 1
    fi
}

# Merge feature to develop
merge_feature() {
    local feature_name="$1"
    
    if [ -z "$feature_name" ]; then
        log_error "Feature name is required"
        return 1
    fi
    
    local branch_name="${FEATURE_PREFIX}${feature_name}"
    local feature_path="$WORKTREE_DIR/features/$feature_name"
    local develop_path="$WORKTREE_DIR/develop"
    
    if [ ! -d "$feature_path" ]; then
        log_error "Feature worktree does not exist: $feature_name"
        return 1
    fi
    
    log_info "Merging feature to develop: $feature_name"
    
    # Sync feature branch
    sync_worktree "$feature_path"
    
    # Switch to develop worktree
    cd "$develop_path"
    
    # Update develop branch
    git fetch origin
    git rebase origin/develop
    
    # Merge feature branch
    if git merge --no-ff "$branch_name" -m "feat: merge $feature_name into develop"; then
        git push origin develop
        log_success "Feature merged successfully: $feature_name"
        
        # Clean up feature branch
        log_info "Cleaning up feature branch: $branch_name"
        git worktree remove "$feature_path" --force
        git branch -d "$branch_name"
        git push origin --delete "$branch_name"
        
        log_success "Feature cleanup completed: $feature_name"
    else
        log_error "Failed to merge feature: $feature_name"
        log_info "Please resolve conflicts manually"
        return 1
    fi
}

# Deploy to staging
deploy_to_staging() {
    local staging_path="$WORKTREE_DIR/staging"
    local develop_path="$WORKTREE_DIR/develop"
    
    log_info "Deploying develop to staging..."
    
    # Update develop
    cd "$develop_path"
    git fetch origin
    git rebase origin/develop
    
    # Update staging
    cd "$staging_path"
    git fetch origin
    git rebase origin/staging
    
    # Merge develop into staging
    if git merge --no-ff develop -m "deploy: merge develop into staging"; then
        git push origin staging
        log_success "Deployed to staging successfully"
        
        # Trigger staging deployment
        log_info "Triggering staging deployment..."
        # Add deployment trigger logic here
        
    else
        log_error "Failed to deploy to staging"
        return 1
    fi
}

# Deploy to production
deploy_to_production() {
    local main_path="$WORKTREE_DIR/main"
    local staging_path="$WORKTREE_DIR/staging"
    
    log_info "Deploying staging to production..."
    
    # Update staging
    cd "$staging_path"
    git fetch origin
    git rebase origin/staging
    
    # Update main
    cd "$main_path"
    git fetch origin
    git rebase origin/main
    
    # Merge staging into main
    if git merge --no-ff staging -m "deploy: merge staging into main"; then
        git push origin main
        log_success "Deployed to production successfully"
        
        # Create release tag
        local version
        version=$(date +"%Y.%m.%d-%H%M%S")
        git tag -a "v$version" -m "Release v$version"
        git push origin "v$version"
        
        log_success "Created release tag: v$version"
        
        # Trigger production deployment
        log_info "Triggering production deployment..."
        # Add deployment trigger logic here
        
    else
        log_error "Failed to deploy to production"
        return 1
    fi
}

# Show help
show_help() {
    cat <<EOF
Git Worktree Manager for Sorteo Estelar

Usage: $0 <command> [arguments]

Commands:
  init                          Initialize main repository and worktree structure
  feature <name> [base]         Create feature worktree (default base: develop)
  hotfix <name> [base]          Create hotfix worktree (default base: main)
  release <version> [base]      Create release worktree (default base: develop)
  sync <worktree_path>          Sync worktree with remote
  cleanup [max_age_days]        Clean up stale worktrees (default: 7 days)
  list                          List all worktrees
  merge-feature <name>          Merge feature to develop and cleanup
  deploy-staging                Deploy develop to staging
  deploy-production             Deploy staging to production
  validate <branch_name>        Validate branch naming convention
  help                          Show this help message

Examples:
  $0 init
  $0 feature user-authentication
  $0 hotfix critical-security-fix
  $0 release 1.2.0
  $0 sync $WORKTREE_DIR/features/user-authentication
  $0 cleanup 7
  $0 list
  $0 merge-feature user-authentication
  $0 deploy-staging
  $0 deploy-production

Worktree Structure:
  $WORKTREE_DIR/
  ├── main/           (main branch)
  ├── develop/        (develop branch)
  ├── staging/        (staging branch)
  ├── features/       (feature branches)
  ├── hotfixes/       (hotfix branches)
  └── releases/       (release branches)

Branch Naming Conventions:
  - feature/feature-name
  - hotfix/hotfix-name
  - release/version
  - main, develop, staging
EOF
}

# Main function
main() {
    local command="${1:-}"
    
    case "$command" in
        "init")
            init_main_repo
            setup_worktree_structure
            ;;
        "feature")
            create_feature_worktree "${2:-}" "${3:-}"
            ;;
        "hotfix")
            create_hotfix_worktree "${2:-}" "${3:-}"
            ;;
        "release")
            create_release_worktree "${2:-}" "${3:-}"
            ;;
        "sync")
            sync_worktree "${2:-}"
            ;;
        "cleanup")
            cleanup_stale_worktrees "${2:-7}" "${3:-3}"
            ;;
        "list")
            list_worktrees
            ;;
        "merge-feature")
            merge_feature "${2:-}"
            ;;
        "deploy-staging")
            deploy_to_staging
            ;;
        "deploy-production")
            deploy_to_production
            ;;
        "validate")
            validate_branch_name "${2:-}"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            log_error "No command specified"
            show_help
            exit 1
            ;;
        *)
            log_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Create directories if they don't exist
mkdir -p "$BASE_DIR" "$WORKTREE_DIR"

# Run main function with all arguments
main "$@"