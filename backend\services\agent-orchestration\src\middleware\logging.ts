import { FastifyInstance, FastifyPluginOptions, FastifyRequest, FastifyReply } from 'fastify';
import fp from 'fastify-plugin';

export const requestLogging = fp(async function (fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.addHook('onRequest', async (request: FastifyRequest, reply: FastifyReply) => {
    request.log.info({
      method: request.method,
      url: request.url,
      userAgent: request.headers['user-agent'],
      ip: request.ip,
      timestamp: new Date().toISOString()
    }, 'Incoming request');
  });
});

export const responseLogging = fp(async function (fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.addHook('onResponse', async (request: FastifyRequest, reply: FastifyReply) => {
    const responseTime = reply.getResponseTime();
    
    request.log.info({
      method: request.method,
      url: request.url,
      statusCode: reply.statusCode,
      responseTime: `${responseTime}ms`,
      userId: request.user?.id,
      timestamp: new Date().toISOString()
    }, 'Request completed');
  });
});