# Project Issue Log: [Project Name]

## Document Control
*   **Log Title:** Project Issue Log: `[Full Official Project Name]`
*   **Project Name:** `[Full Official Project Name]`
*   **Project ID:** `[Unique Project Identifier]`
*   **Client Name (if applicable):** `[Client Name]`
*   **Client ID (ESTRATIX):** `[Client_ID]`
*   **Log Version:** `[e.g., 1.0, updated as issues are added/resolved]`
*   **Date Created:** `[YYYY-MM-DD]`
*   **Last Updated:** `[YYYY-MM-DD HH:MM Z (Consider ESTRATIX Agent ID if auto-updated, e.g., CPO_AXXX_IssueTrackerAgent)]`
*   **Maintained By:** `[Project Manager Name / ESTRATIX Agent ID, e.g., CPO_AXXX_ProjectManager]`
*   **Document Status:** `[e.g., Active, Archived]`
*   **Security Classification:** `[e.g., ESTRATIX Internal, Client Confidential]`
*   **Related Project Plan ID:** `[Link to ../01_ProjectPlanning/Project_Plan_Template.md or actual Project Plan document ID]`

## 1. Introduction
This Issue Log is a formal record used to identify, track, manage, and resolve issues that arise during the lifecycle of the `[Project Name]` project. An issue is any unplanned event, problem, question, or concern that has occurred or is certain to occur, which could impede project progress or negatively impact project objectives if not addressed. This log is a living document and will be updated regularly by the project team and relevant ESTRATIX agents.

## 2. Issue Log

| Issue ID <br/> `([ProjectID]-ISS-[NNN])` | Status <br/> `(New, Open, Assigned, In Progress, On Hold, Resolved, Closed, Cancelled, Escalated)` | Date Raised <br/> `(YYYY-MM-DD)` | Raised By <br/> `(Name/Role/Agent ID)` | Issue Title <br/> `(Concise Summary)` | Detailed Description <br/> `(Context, Symptoms, Steps to Reproduce)` | Category/Type <br/> `(Technical, Resource, Scope, Schedule, Cost, Quality, Comm., Stakeholder, External, Security, Agentic)` | Impact Assessment <br/> `(Initial & Current: Scope, Schedule, Cost, Quality, Resources)` | Priority <br/> `(Critical, High, Medium, Low - Ref. Project Plan for criteria)` | Assigned To <br/> `(Name/Role/Agent ID)` | Target Resolution Date <br/> `(YYYY-MM-DD)` | Resolution Plan / Actions Taken | Actual Resolution Date <br/> `(YYYY-MM-DD)` | Resolution Details & Outcome <br/> `(Incl. verification)` | Escalation Required? <br/> `(Y/N)` | Escalated To <br/> `(Name/Role/Agent ID)` | Date Escalated <br/> `(YYYY-MM-DD)` | Related Risks (ID(s)) | Related Change Requests (ID(s)) | Attachments / Links | Notes / Comments |
| :------------------------------------ | :----------------------------------------------------------------------------------------------------- | :----------------------------- | :----------------------------- | :---------------------------- | :----------------------------------------------------------------- | :----------------------------------------------------------------------------------------------------------------- | :---------------------------------------------------------------------------------- | :----------------------------------------------------------------------- | :----------------------------- | :----------------------------------- | :------------------------------ | :----------------------------------- | :-------------------------------------- | :------------------------- | :----------------------------- | :----------------------------- | :---------------------- | :--------------------------------- | :------------------ | :--------------- |
| `[PRJID-ISS-001]`                     | `[Open]`                                                                                               | `[YYYY-MM-DD]`                 | `[User Name / User_Role / CPO_AXXX_ObserverAgent]` | `[e.g., API Endpoint Failing]`| `[Detailed description of the failure, error messages, environment details, steps to reproduce the issue.]` | `[Technical]`                                                                                                      | `[Initial: High - Blocks Feature X. Current: High]`                                 | `[High]`                                                                 | `[Dev Team Lead / CPO_AXXX_DeveloperAgent]` | `[YYYY-MM-DD]`                       | `[Investigate logs, debug code, propose fix.]` |                                      |                                         | `[N]`                      |                                |                                | `[RISK-005]`            | `[CR-002 (if fix requires scope change)]` | `[link_to_log_file.txt]` | `[Additional context]` |
| `[...]`                               | `[...]`                                                                                                | `[...]`                        | `[...]`                        | `[...]`                       | `[...]`                                                                            | `[...]`                                                                                                            | `[...]`                                                                             | `[...]`                                                                  | `[...]`                        | `[...]`                              | `[...]`                                 | `[...]`                              | `[...]`                                         | `[...]`                    | `[...]`                        | `[...]`                        | `[...]`                 | `[...]`                                    | `[...]`             | `[...]`          |

## 3. Guidance for Use
*   **Logging an Issue:** Any team member, stakeholder, or designated ESTRATIX agent can raise an issue. Issues should be logged as soon as they are identified. Provide clear, concise, and complete information.
*   **Issue ID Convention:** Use the format `[ProjectID]-ISS-[NNN]`, where `[ProjectID]` is the unique project identifier and `[NNN]` is a sequential number.
*   **Priority Assignment:** Priority should be assigned based on the issue's impact and urgency, as defined in the Project Plan or by the Project Manager. (e.g., Critical: Blocks project progress, requires immediate attention; High: Significant impact, urgent; Medium: Moderate impact, address in due course; Low: Minor impact, address if time permits).
*   **Ownership:** Each issue must have a designated owner (`Assigned To`) responsible for ensuring its investigation, resolution, and tracking.
*   **Status Updates:** The status of each issue should be updated regularly by the owner or relevant ESTRATIX agents.
*   **Review Frequency:** The Issue Log should be reviewed regularly (e.g., in daily stand-ups, weekly project team meetings, and steering committee meetings as appropriate) to monitor progress and address roadblocks.
*   **Escalation:** If an issue cannot be resolved by the assigned owner or within the agreed timeframe, it should be escalated according to the project's Communication Management Plan (Section 7) or as directed by the Project Manager.
*   **Closing an Issue:** An issue can be closed when its resolution has been implemented, verified, and accepted by relevant stakeholders (e.g., the person/agent who raised it or a designated approver).

---
*This Project Issue Log is an official project record. It should be stored in the ESTRATIX Project Document Repository at `[Link to Repository/Project_XYZ/Monitoring_And_Controlling/]` and be accessible to all relevant project stakeholders as defined in the Communication Management Plan.*
