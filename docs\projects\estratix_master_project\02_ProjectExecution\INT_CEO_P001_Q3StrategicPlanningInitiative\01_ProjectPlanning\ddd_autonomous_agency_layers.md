# ESTRATIX DDD Layers for Autonomous Agency Operations

## Document Control
- **Document ID**: ESTRATIX-ARCH-DDD-AGENCY-001
- **Version**: 1.0.0
- **Date**: 2025-01-27
- **Status**: Draft
- **Classification**: Internal
- **Author**: Cascade AI Assistant
- **Reviewer**: User (jdani)
- **Approver**: CEO Command Office

## Overview

This document defines the Domain-Driven Design (DDD) layer responsibilities for ESTRATIX autonomous agency operations, establishing clear boundaries and responsibilities for high-momentum development.

## DDD Layer Architecture

### Domain Layer (`src/domain/`)
**Core Business Logic - Framework Agnostic**

#### Responsibilities:
- **Agency Models**: Core business entities for autonomous operations
  - `AgencyWorkflow` - Workflow definitions and state
  - `AgentCapability` - Agent skills and tool access
  - `TaskExecution` - Task lifecycle and completion tracking
  - `ProposalEvaluation` - Project assessment and approval logic
  - `KnowledgeAsset` - Research findings and insights storage

- **Value Objects**: Immutable business concepts
  - `WorkflowSchedule` - Calendar and timing constraints
  - `ExecutionMetrics` - Performance and success measurements
  - `CompetencyLevel` - Agent skill proficiency ratings
  - `ApprovalCriteria` - Decision-making thresholds

- **Domain Services**: Core business operations
  - `WorkflowOrchestrationService` - Multi-agent coordination logic
  - `ProposalEvaluationService` - Project assessment algorithms
  - `KnowledgeResearchService` - Information discovery and analysis
  - `CapacityPlanningService` - Resource allocation optimization

- **Repository Interfaces**: Data access contracts
  - `IWorkflowRepository` - Workflow persistence abstraction
  - `IKnowledgeRepository` - Research data access contract
  - `IAgentCapabilityRepository` - Agent skill tracking interface

#### Key Principles:
- Pure business logic without framework dependencies
- High-cohesion, low-coupling components
- Rich domain models with behavior
- Explicit domain language (ubiquitous language)

### Application Layer (`src/application/`)
**Use Cases and Orchestration**

#### Responsibilities:
- **Use Cases**: Specific business scenarios
  - `BootstrapAutonomousAgencyUseCase` - Agency initialization
  - `ExecuteWorkflowUseCase` - Workflow lifecycle management
  - `EvaluateProposalUseCase` - Project assessment workflow
  - `ScheduleDailyOperationsUseCase` - Calendar and task planning
  - `ConductResearchUseCase` - Knowledge discovery workflow

- **Application Services**: Coordination between domain services
  - `AgencyOrchestrationService` - High-level workflow coordination
  - `CalendarManagementService` - Scheduling and resource planning
  - `ResearchCoordinationService` - Knowledge acquisition workflows

- **Command/Query Handlers**: CQRS implementation
  - Commands: `CreateWorkflow`, `ScheduleTask`, `ApproveProposal`
  - Queries: `GetWorkflowStatus`, `ListPendingTasks`, `SearchKnowledge`

- **Ports (Interfaces)**: External system contracts
  - `INotificationPort` - Communication with external systems
  - `ISchedulingPort` - Calendar integration interface
  - `IKnowledgeSourcePort` - External research APIs

#### Key Principles:
- Thin orchestration layer
- Use case-driven organization
- Clear input/output boundaries
- Stateless operation coordination

### Infrastructure Layer (`src/infrastructure/`)
**Framework-Specific Implementations**

#### Responsibilities:
- **Adapters**: Port implementations for specific technologies
  - `SqlWorkflowRepository` - Database workflow storage
  - `VectorKnowledgeRepository` - Vector DB for research data
  - `EmailNotificationAdapter` - SMTP notification implementation
  - `CalendarApiAdapter` - External calendar system integration

- **Framework Integrations**: Technology-specific implementations
  - `frameworks/crewai/` - CrewAI agent implementations
  - `frameworks/pydantic_ai/` - Pydantic-AI workflow runners
  - `frameworks/fastapi/` - REST API endpoints

- **External Services**: Third-party integrations
  - `external_apis/` - Research API clients
  - `monitoring/` - Performance tracking systems
  - `messaging/` - Inter-service communication

#### Key Principles:
- Framework-specific implementations
- Adapter pattern for external systems
- Configuration-driven behavior
- Easily replaceable components

## Centralized Workflow Management Architecture

### Core Components

#### 1. Workflow Orchestration Center
```
src/domain/services/workflow_orchestration_service.py
src/application/use_cases/execute_workflow_use_case.py
src/infrastructure/orchestration/centralized_workflow_manager.py
```

#### 2. Operations Scheduling System
```
src/domain/models/operation_schedule.py
src/application/services/calendar_management_service.py
src/infrastructure/scheduling/daily_operations_scheduler.py
```

#### 3. Knowledge Research Engine
```
src/domain/services/knowledge_research_service.py
src/application/use_cases/conduct_research_use_case.py
src/infrastructure/research/knowledge_scouting_engine.py
```

#### 4. Proposal Evaluation Pipeline
```
src/domain/models/proposal_evaluation.py
src/application/use_cases/evaluate_proposal_use_case.py
src/infrastructure/evaluation/project_assessment_engine.py
```

## Integration Points

### Existing Infrastructure Integration
- Leverage `autonomous_agency_workflows.py` for workflow execution
- Integrate with `ceo_headquarters.py` for executive approval
- Utilize existing `AutonomousOrchestrator` for coordination

### Non-Overlapping Design
- Clear boundaries between workflow execution and management
- Separate concerns for scheduling vs. execution
- Distinct research vs. evaluation responsibilities

## High-Momentum Implementation Strategy

### Phase 1: Domain Foundation (Week 1)
1. Define core domain models
2. Implement domain services
3. Create repository interfaces
4. Establish value objects

### Phase 2: Application Orchestration (Week 2)
1. Build use case handlers
2. Implement application services
3. Define port contracts
4. Create command/query structures

### Phase 3: Infrastructure Implementation (Week 3)
1. Build centralized workflow manager
2. Implement scheduling system
3. Create knowledge research engine
4. Deploy proposal evaluation pipeline

### Phase 4: Integration & Testing (Week 4)
1. Integrate with existing CEO headquarters
2. Connect to autonomous workflow engine
3. Implement comprehensive test suites
4. Validate end-to-end workflows

## Success Metrics

### Technical Metrics
- Code coverage ≥ 80% using `uv run pytest --cov=src`
- Response time < 200ms for workflow operations
- Zero dependency violations between layers
- 100% type hint coverage with `uv run mypy src/`

### Business Metrics
- Autonomous operation success rate ≥ 95%
- Proposal evaluation time < 24 hours
- Knowledge research completeness ≥ 90%
- Daily operations scheduling accuracy ≥ 98%

## Compliance & Quality

### Code Quality Standards
- All Python code uses `uv run` for execution
- SOLID principles enforcement
- DRY principle adherence
- TDD implementation with pytest

### Architecture Validation
- Layer dependency analysis
- Port/adapter pattern compliance
- Domain purity verification
- Integration boundary testing

## Guidance for Use

This architecture serves as the foundation for autonomous agency operations. Each layer has distinct responsibilities and clear boundaries. Implementation should follow the high-momentum strategy outlined above, with continuous validation through automated testing and metrics collection.

The architecture supports trunk-based development through clear module boundaries and prevents overlap with other chat plans by establishing explicit integration contracts.

---

**Document Footer**
- **Project**: ESTRATIX Master Project
- **Architecture**: Hexagonal (Ports & Adapters) with DDD
- **Framework**: Autonomous Agency Operations
- **Maintenance**: Living document, updated with architectural evolution
