import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ConnectButton } from '@rainbow-me/rainbowkit';
import { motion } from 'framer-motion';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/stores/authStore';
import { UserRole, Permission } from '@/types/auth';
import LanguageSelector from '@/components/LanguageSelector';
import { 
  HomeIcon, 
  BuildingOfficeIcon, 
  WrenchScrewdriverIcon,
  ChartBarIcon,
  UserGroupIcon,
  CogIcon,
  PhoneIcon,
  PaintBrushIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  Bars3Icon,
  XMarkIcon,
  ShieldCheckIcon,
  CpuChipIcon,
  BanknotesIcon,
  UserIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline';

// Base navigation for all users - will be translated dynamically
const getBaseNavigation = (t: any) => [
  { name: t('navigation.home'), href: '/', icon: HomeIcon },
  { name: t('navigation.marketplace'), href: '/marketplace', icon: BuildingOfficeIcon },
  { name: t('navigation.services'), href: '/services', icon: WrenchScrewdriverIcon },
  { name: t('navigation.defi_pools'), href: '/defi', icon: BanknotesIcon },
];

// Consumer navigation
const getConsumerNavigation = (t: any, baseNav: any[]) => [
  ...baseNav,
  { name: t('navigation.dashboard'), href: '/dashboard', icon: ChartBarIcon },
  { name: t('navigation.nft_marketplace'), href: '/nft', icon: PhotoIcon },
];

// Provider navigation
const getProviderNavigation = (t: any, consumerNav: any[]) => [
  ...consumerNav,
  { name: t('navigation.provider_portal'), href: '/provider', icon: UserGroupIcon },
];

// Admin navigation
const getAdminNavigation = (t: any, providerNav: any[]) => [
  ...providerNav,
  { name: t('navigation.admin_dashboard'), href: '/admin/dashboard', icon: ShieldCheckIcon },
  { name: t('navigation.ai_agents'), href: '/admin/ai-agents', icon: CpuChipIcon },
  { name: t('navigation.property_acquisition'), href: '/acquisition', icon: PhoneIcon },
  { name: t('navigation.content_studio'), href: '/content', icon: PaintBrushIcon },
  { name: t('navigation.lux_tokens'), href: '/tokens', icon: CurrencyDollarIcon },
];

// Developer navigation
const getDeveloperNavigation = (t: any, adminNav: any[]) => [
  ...adminNav,
  { name: t('navigation.dev_tools'), href: '/admin/dev-tools', icon: CogIcon },
];

// Super admin navigation
const getSuperAdminNavigation = (t: any, developerNav: any[]) => [
  ...developerNav,
  { name: t('navigation.admin_console'), href: '/admin', icon: CogIcon },
];

export default function Navbar() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const location = useLocation();
  const { user, logout, hasRole, hasPermission } = useAuthStore();
  const { t } = useTranslation();
  
  // Get subdomain
  const subdomain = typeof window !== 'undefined' 
    ? window.location.hostname.split('.')[0] 
    : '';
  
  // Determine navigation based on user role
  const getNavigation = () => {
    const baseNav = getBaseNavigation(t);
    if (!user) return baseNav;
    
    const consumerNav = getConsumerNavigation(t, baseNav);
    const providerNav = getProviderNavigation(t, consumerNav);
    const adminNav = getAdminNavigation(t, providerNav);
    const developerNav = getDeveloperNavigation(t, adminNav);
    const superAdminNav = getSuperAdminNavigation(t, developerNav);
    
    if (hasRole(UserRole.SUPER_ADMIN)) return superAdminNav;
    if (hasRole(UserRole.DEVELOPER)) return developerNav;
    if (hasRole(UserRole.AGENCY_ADMIN) || hasRole(UserRole.PROPERTY_MANAGER) || 
        hasRole(UserRole.AI_AGENT_OPERATOR) || hasRole(UserRole.CONTENT_MANAGER)) {
      return adminNav;
    }
    if (hasRole(UserRole.SERVICE_PROVIDER)) return providerNav;
    
    return consumerNav;
  };
  
  const navigation = getNavigation();
  
  // Get subdomain-specific title
  const getSubdomainTitle = () => {
    switch (subdomain) {
      case 'admin': return 'Admin Panel';
      case 'dev': return 'Developer Console';
      case 'ai': return 'AI Agents';
      case 'properties': return 'Property Management';
      case 'invest': return 'Investment Portal';
      default: return 'Luxcrafts';
    }
  };
  
  const handleLogout = () => {
    logout();
    setUserMenuOpen(false);
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-md border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ scale: 1.05 }}
              className="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center"
            >
              <span className="text-white font-bold text-xl">L</span>
            </motion.div>
            <div className="flex flex-col">
              <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                {getSubdomainTitle()}
              </span>
              {subdomain && !['localhost', 'luxcrafts', 'www'].includes(subdomain) && (
                <span className="text-xs text-gray-500">
                  {subdomain}.luxcrafts.com
                </span>
              )}
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-1 ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
          </div>

          {/* User Menu & Web3 Connect */}
          <div className="hidden lg:flex items-center space-x-4">
            <LanguageSelector />
            <ConnectButton />
            
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setUserMenuOpen(!userMenuOpen)}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium text-gray-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200"
                >
                  <UserIcon className="w-4 h-4" />
                  <span>{user.name}</span>
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                    {user.role.replace('_', ' ')}
                  </span>
                </button>
                
                {userMenuOpen && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50"
                  >
                    <Link
                      to="/dashboard"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setUserMenuOpen(false)}
                    >
                      {t('navigation.dashboard')}
                    </Link>
                    <Link
                      to="/profile"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setUserMenuOpen(false)}
                    >
                      {t('navigation.profile_settings')}
                    </Link>
                    <hr className="my-1" />
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                    >
                      <ArrowRightOnRectangleIcon className="w-4 h-4" />
                      <span>{t('navigation.sign_out')}</span>
                    </button>
                  </motion.div>
                )}
              </div>
            ) : (
              <Link
                to="/login"
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                {t('navigation.sign_in')}
              </Link>
            )}
          </div>

          {/* Mobile menu button */}
          <button
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
            className="lg:hidden p-2 rounded-lg text-gray-600 hover:text-blue-600 hover:bg-blue-50"
          >
            {mobileMenuOpen ? (
              <XMarkIcon className="w-6 h-6" />
            ) : (
              <Bars3Icon className="w-6 h-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {mobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="lg:hidden bg-white border-t border-gray-200"
        >
          <div className="px-4 py-2 space-y-1">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  onClick={() => setMobileMenuOpen(false)}
                  className={`block px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center space-x-2 ${
                    isActive
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.name}</span>
                </Link>
              );
            })}
            <div className="pt-2 border-t border-gray-200 space-y-2">
              <div className="px-3 py-2">
                <LanguageSelector />
              </div>
              <ConnectButton />
              
              {user ? (
                <div className="space-y-1">
                  <div className="px-3 py-2 text-sm text-gray-600">
                    Signed in as <span className="font-medium">{user.name}</span>
                    <span className="block text-xs text-blue-600">{user.role.replace('_', ' ')}</span>
                  </div>
                  <Link
                    to="/dashboard"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                  >
                    {t('navigation.dashboard')}
                  </Link>
                  <Link
                    to="/profile"
                    onClick={() => setMobileMenuOpen(false)}
                    className="block px-3 py-2 text-sm text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg"
                  >
                    {t('navigation.profile_settings')}
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-lg flex items-center space-x-2"
                  >
                    <ArrowRightOnRectangleIcon className="w-4 h-4" />
                    <span>{t('navigation.sign_out')}</span>
                  </button>
                </div>
              ) : (
                <Link
                  to="/login"
                  onClick={() => setMobileMenuOpen(false)}
                  className="block px-3 py-2 bg-blue-600 text-white rounded-lg text-center hover:bg-blue-700 transition-colors duration-200"
                >
                  {t('navigation.sign_in')}
                </Link>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </nav>
  );
}