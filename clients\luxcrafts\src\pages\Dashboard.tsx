import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  HomeIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  UserGroupIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  BellIcon,
  CogIcon,
  EyeIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
} from '@heroicons/react/24/outline';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
} from 'recharts';

const portfolioData = [
  { month: 'Jan', value: 125000, income: 1200, expenses: 800 },
  { month: 'Feb', value: 128000, income: 1250, expenses: 820 },
  { month: 'Mar', value: 132000, income: 1300, expenses: 850 },
  { month: 'Apr', value: 135000, income: 1350, expenses: 880 },
  { month: 'May', value: 139000, income: 1400, expenses: 900 },
  { month: 'Jun', value: 142500, income: 1450, expenses: 920 },
];

const propertyPerformance = [
  { name: 'Beacon Hill Penthouse', value: 45000, roi: 8.5, status: 'Excellent' },
  { name: 'Back Bay Condo', value: 32000, roi: 7.2, status: 'Good' },
  { name: 'Cambridge Waterfront', value: 28000, roi: 6.8, status: 'Good' },
  { name: 'Newton Townhouse', value: 38000, roi: 9.1, status: 'Excellent' },
];

const assetAllocation = [
  { name: 'Residential', value: 65, color: '#3b82f6' },
  { name: 'Commercial', value: 25, color: '#10b981' },
  { name: 'REITs', value: 10, color: '#f59e0b' },
];

const recentTransactions = [
  {
    id: 1,
    type: 'Investment',
    property: 'Beacon Hill Penthouse',
    amount: 5000,
    tokens: 175,
    date: '2024-01-15',
    status: 'Completed',
  },
  {
    id: 2,
    type: 'Dividend',
    property: 'Back Bay Condo',
    amount: 247,
    tokens: 0,
    date: '2024-01-14',
    status: 'Completed',
  },
  {
    id: 3,
    type: 'Investment',
    property: 'Cambridge Waterfront',
    amount: 3000,
    tokens: 154,
    date: '2024-01-12',
    status: 'Pending',
  },
];

const notifications = [
  {
    id: 1,
    type: 'dividend',
    title: 'Dividend Payment Received',
    message: 'You received $247 from Back Bay Condo',
    time: '2 hours ago',
    read: false,
  },
  {
    id: 2,
    type: 'property',
    title: 'New Property Available',
    message: 'Luxury penthouse in Seaport District now available for investment',
    time: '5 hours ago',
    read: false,
  },
  {
    id: 3,
    type: 'market',
    title: 'Market Update',
    message: 'Boston real estate market up 3.2% this quarter',
    time: '1 day ago',
    read: true,
  },
];

const upcomingEvents = [
  {
    id: 1,
    title: 'Property Inspection',
    property: 'Beacon Hill Penthouse',
    date: '2024-01-18',
    time: '10:00 AM',
    type: 'maintenance',
  },
  {
    id: 2,
    title: 'Quarterly Report',
    property: 'All Properties',
    date: '2024-01-20',
    time: '2:00 PM',
    type: 'report',
  },
  {
    id: 3,
    title: 'Rent Collection',
    property: 'Cambridge Waterfront',
    date: '2024-01-25',
    time: '9:00 AM',
    type: 'payment',
  },
];

export default function Dashboard() {
  const [timeRange, setTimeRange] = useState('6M');
  const [selectedMetric, setSelectedMetric] = useState('value');

  const kpiData = [
    {
      title: 'Total Portfolio Value',
      value: '$142,500',
      change: '+14.0%',
      trend: 'up',
      icon: CurrencyDollarIcon,
      color: 'blue',
    },
    {
      title: 'Monthly Income',
      value: '$1,450',
      change: '+8.5%',
      trend: 'up',
      icon: ArrowTrendingUpIcon,
      color: 'green',
    },
    {
      title: 'Properties Owned',
      value: '4',
      change: '+1',
      trend: 'up',
      icon: HomeIcon,
      color: 'purple',
    },
    {
      title: 'Average ROI',
      value: '7.9%',
      change: '+0.3%',
      trend: 'up',
      icon: ChartBarIcon,
      color: 'yellow',
    },
  ];

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              Investment Dashboard
            </h1>
            <p className="text-xl text-gray-600">
              Track your real estate portfolio performance and analytics
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <button className="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <BellIcon className="w-6 h-6" />
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <CogIcon className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {kpiData.map((kpi, index) => (
            <motion.div
              key={kpi.title}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white rounded-xl p-6 shadow-lg"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-600 mb-1">{kpi.title}</p>
                  <p className="text-3xl font-bold text-gray-900 mb-2">{kpi.value}</p>
                  <div className="flex items-center space-x-1">
                    {kpi.trend === 'up' ? (
                      <ArrowUpIcon className="w-4 h-4 text-green-500" />
                    ) : (
                      <ArrowDownIcon className="w-4 h-4 text-red-500" />
                    )}
                    <span className={`text-sm font-medium ${
                      kpi.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {kpi.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-lg ${
                  kpi.color === 'blue' ? 'bg-blue-100' :
                  kpi.color === 'green' ? 'bg-green-100' :
                  kpi.color === 'purple' ? 'bg-purple-100' :
                  'bg-yellow-100'
                }`}>
                  <kpi.icon className={`w-6 h-6 ${
                    kpi.color === 'blue' ? 'text-blue-600' :
                    kpi.color === 'green' ? 'text-green-600' :
                    kpi.color === 'purple' ? 'text-purple-600' :
                    'text-yellow-600'
                  }`} />
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Portfolio Performance Chart */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-bold text-gray-900">Portfolio Performance</h3>
                <div className="flex items-center space-x-4">
                  <select
                    value={selectedMetric}
                    onChange={(e) => setSelectedMetric(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  >
                    <option value="value">Portfolio Value</option>
                    <option value="income">Monthly Income</option>
                    <option value="expenses">Monthly Expenses</option>
                  </select>
                  <div className="flex space-x-2">
                    {['1M', '3M', '6M', '1Y'].map((range) => (
                      <button
                        key={range}
                        onClick={() => setTimeRange(range)}
                        className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                          timeRange === range
                            ? 'bg-blue-100 text-blue-700'
                            : 'text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        {range}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={portfolioData}>
                    <defs>
                      <linearGradient id="colorValue" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.3}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [formatCurrency(value), selectedMetric]} />
                    <Area
                      type="monotone"
                      dataKey={selectedMetric}
                      stroke="#3b82f6"
                      strokeWidth={3}
                      fillOpacity={1}
                      fill="url(#colorValue)"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Asset Allocation */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Asset Allocation</h3>
              <div className="h-48">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={assetAllocation}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      dataKey="value"
                    >
                      {assetAllocation.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </div>
              <div className="space-y-2">
                {assetAllocation.map((item) => (
                  <div key={item.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }} />
                      <span className="text-sm text-gray-600">{item.name}</span>
                    </div>
                    <span className="text-sm font-semibold">{item.value}%</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button className="w-full px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  Invest in New Property
                </button>
                <button className="w-full px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                  Withdraw Earnings
                </button>
                <button className="w-full px-4 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                  View All Properties
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Property Performance & Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Property Performance */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Property Performance</h3>
            <div className="space-y-4">
              {propertyPerformance.map((property, index) => (
                <motion.div
                  key={property.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow"
                >
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{property.name}</h4>
                    <p className="text-sm text-gray-600">Current Value: {formatCurrency(property.value)}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-green-600">{property.roi}%</p>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      property.status === 'Excellent' ? 'bg-green-100 text-green-800' :
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {property.status}
                    </span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Recent Transactions */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Transactions</h3>
            <div className="space-y-4">
              {recentTransactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      transaction.type === 'Investment' ? 'bg-blue-100' : 'bg-green-100'
                    }`}>
                      {transaction.type === 'Investment' ? (
                        <ArrowTrendingUpIcon className={`w-5 h-5 ${
                          transaction.type === 'Investment' ? 'text-blue-600' : 'text-green-600'
                        }`} />
                      ) : (
                        <CurrencyDollarIcon className="w-5 h-5 text-green-600" />
                      )}
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900">{transaction.type}</p>
                      <p className="text-sm text-gray-600">{transaction.property}</p>
                      <p className="text-xs text-gray-500">{transaction.date}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(transaction.amount)}</p>
                    {transaction.tokens > 0 && (
                      <p className="text-sm text-gray-600">{transaction.tokens} tokens</p>
                    )}
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      transaction.status === 'Completed' ? 'bg-green-100 text-green-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {transaction.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Notifications & Upcoming Events */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Notifications */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-gray-900">Notifications</h3>
              <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                Mark all as read
              </button>
            </div>
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 rounded-lg border transition-colors ${
                    notification.read ? 'border-gray-200 bg-white' : 'border-blue-200 bg-blue-50'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                      notification.type === 'dividend' ? 'bg-green-100' :
                      notification.type === 'property' ? 'bg-blue-100' :
                      'bg-yellow-100'
                    }`}>
                      {notification.type === 'dividend' ? (
                        <CurrencyDollarIcon className="w-4 h-4 text-green-600" />
                      ) : notification.type === 'property' ? (
                        <HomeIcon className="w-4 h-4 text-blue-600" />
                      ) : (
                        <ChartBarIcon className="w-4 h-4 text-yellow-600" />
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900">{notification.title}</h4>
                      <p className="text-sm text-gray-600">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                    </div>
                    {!notification.read && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Upcoming Events */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-6">Upcoming Events</h3>
            <div className="space-y-4">
              {upcomingEvents.map((event) => (
                <div key={event.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                    event.type === 'maintenance' ? 'bg-orange-100' :
                    event.type === 'report' ? 'bg-blue-100' :
                    'bg-green-100'
                  }`}>
                    {event.type === 'maintenance' ? (
                      <ExclamationTriangleIcon className="w-6 h-6 text-orange-600" />
                    ) : event.type === 'report' ? (
                      <ChartBarIcon className="w-6 h-6 text-blue-600" />
                    ) : (
                      <CurrencyDollarIcon className="w-6 h-6 text-green-600" />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{event.title}</h4>
                    <p className="text-sm text-gray-600">{event.property}</p>
                    <div className="flex items-center space-x-2 mt-1">
                      <CalendarIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-xs text-gray-500">{event.date}</span>
                      <ClockIcon className="w-4 h-4 text-gray-400" />
                      <span className="text-xs text-gray-500">{event.time}</span>
                    </div>
                  </div>
                  <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm font-medium">
                    View
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}