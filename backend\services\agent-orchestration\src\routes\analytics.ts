import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { authenticateToken, requirePermission } from '@/middleware/auth';
import { AnalyticsService } from '@/services/analyticsService';

// Request/Response Schemas
const trackEventSchema = z.object({
  type: z.enum(['agent_created', 'agent_started', 'agent_stopped', 'workflow_executed', 'task_completed', 'task_failed', 'user_action']),
  category: z.enum(['agent', 'workflow', 'task', 'user', 'system']),
  action: z.string().min(1).max(255),
  entityId: z.string().optional(),
  metadata: z.record(z.any()).default({})
});

const trackMetricSchema = z.object({
  name: z.string().min(1).max(255),
  value: z.number(),
  unit: z.enum(['ms', 'count', 'percentage', 'bytes', 'rate']).default('count'),
  category: z.enum(['performance', 'usage', 'error', 'business']).default('usage'),
  tags: z.record(z.string()).default({})
});

const analyticsQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  entityId: z.string().optional(),
  entityType: z.string().optional(),
  category: z.enum(['agent', 'workflow', 'task', 'user', 'system']).optional(),
  action: z.string().optional(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0)
});

const metricsQuerySchema = z.object({
  namePattern: z.string().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  category: z.enum(['performance', 'usage', 'error', 'business']).optional(),
  unit: z.enum(['ms', 'count', 'percentage', 'bytes', 'rate']).optional(),
  limit: z.coerce.number().min(1).max(1000).default(100),
  offset: z.coerce.number().min(0).default(0)
});

const summaryQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional()
});

interface AnalyticsRouteContext {
  analyticsService: AnalyticsService;
}

export async function analyticsRoutes(fastify: FastifyInstance) {
  const { analyticsService } = fastify as any as AnalyticsRouteContext;

  // Track analytics event
  fastify.post('/events', {
    preHandler: [authenticateToken, requirePermission('analytics:write')],
    schema: {
      body: trackEventSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const eventData = trackEventSchema.parse(request.body);
      const user = (request as any).user;
      
      analyticsService.trackEvent({
        ...eventData,
        userId: user.id,
        organizationId: user.organizationId
      });
      
      return reply.status(201).send({
        success: true,
        message: 'Event tracked successfully'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to track event'
      });
    }
  });

  // Track performance metric
  fastify.post('/metrics', {
    preHandler: [authenticateToken, requirePermission('analytics:write')],
    schema: {
      body: trackMetricSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metricData = trackMetricSchema.parse(request.body);
      
      analyticsService.trackMetric(metricData);
      
      return reply.status(201).send({
        success: true,
        message: 'Metric tracked successfully'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to track metric'
      });
    }
  });

  // Get analytics summary
  fastify.get('/summary', {
    preHandler: [authenticateToken, requirePermission('analytics:read')],
    schema: {
      querystring: summaryQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalEvents: { type: 'number' },
                eventsByType: { type: 'object' },
                eventsByCategory: { type: 'object' },
                averageResponseTime: { type: 'number' },
                errorRate: { type: 'number' },
                activeUsers: { type: 'number' },
                topActions: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      action: { type: 'string' },
                      count: { type: 'number' }
                    }
                  }
                },
                timeRange: {
                  type: 'object',
                  properties: {
                    start: { type: 'string' },
                    end: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = summaryQuerySchema.parse(request.query);
      const user = (request as any).user;
      
      // Default to last 24 hours if no dates provided
      const endDate = query.endDate ? new Date(query.endDate) : new Date();
      const startDate = query.startDate ? new Date(query.startDate) : new Date(endDate.getTime() - 24 * 60 * 60 * 1000);
      
      const summary = await analyticsService.getAnalyticsSummary(startDate, endDate, user.organizationId);
      
      return reply.send({
        success: true,
        data: summary
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve analytics summary'
      });
    }
  });

  // Get events for specific entity
  fastify.get('/events/:entityId', {
    preHandler: [authenticateToken, requirePermission('analytics:read')],
    schema: {
      params: {
        type: 'object',
        properties: {
          entityId: { type: 'string' }
        },
        required: ['entityId']
      },
      querystring: {
        type: 'object',
        properties: {
          entityType: { type: 'string' },
          limit: { type: 'number', minimum: 1, maximum: 1000, default: 100 },
          offset: { type: 'number', minimum: 0, default: 0 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  category: { type: 'string' },
                  action: { type: 'string' },
                  entityId: { type: 'string' },
                  userId: { type: 'string' },
                  organizationId: { type: 'string' },
                  metadata: { type: 'object' },
                  timestamp: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { entityId } = request.params as { entityId: string };
      const { entityType, limit = 100, offset = 0 } = request.query as any;
      
      const events = await analyticsService.getEntityEvents(entityId, entityType);
      
      // Apply pagination
      const total = events.length;
      const paginatedEvents = events.slice(offset, offset + limit);
      
      return reply.send({
        success: true,
        data: paginatedEvents,
        pagination: {
          total,
          limit,
          offset
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve entity events'
      });
    }
  });

  // Get metrics by name pattern
  fastify.get('/metrics/search', {
    preHandler: [authenticateToken, requirePermission('analytics:read')],
    schema: {
      querystring: metricsQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  value: { type: 'number' },
                  unit: { type: 'string' },
                  category: { type: 'string' },
                  tags: { type: 'object' },
                  timestamp: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = metricsQuerySchema.parse(request.query);
      
      const startDate = query.startDate ? new Date(query.startDate) : undefined;
      const endDate = query.endDate ? new Date(query.endDate) : undefined;
      
      let metrics = await analyticsService.getMetricsByName(
        query.namePattern || '',
        startDate,
        endDate
      );
      
      // Apply additional filters
      if (query.category) {
        metrics = metrics.filter(m => m.category === query.category);
      }
      
      if (query.unit) {
        metrics = metrics.filter(m => m.unit === query.unit);
      }
      
      // Apply pagination
      const total = metrics.length;
      const paginatedMetrics = metrics.slice(query.offset, query.offset + query.limit);
      
      return reply.send({
        success: true,
        data: paginatedMetrics,
        pagination: {
          total,
          limit: query.limit,
          offset: query.offset
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve metrics'
      });
    }
  });

  // Get analytics service status and metrics
  fastify.get('/status', {
    preHandler: [authenticateToken, requirePermission('analytics:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                totalEvents: { type: 'number' },
                totalMetrics: { type: 'number' },
                memoryUsage: { type: 'number' },
                timestamp: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const status = await analyticsService.getStatus();
      const metrics = await analyticsService.getMetrics();
      
      return reply.send({
        success: true,
        data: {
          status,
          ...metrics,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve analytics status'
      });
    }
  });

  // Bulk track events (for high-volume scenarios)
  fastify.post('/events/bulk', {
    preHandler: [authenticateToken, requirePermission('analytics:write')],
    schema: {
      body: {
        type: 'object',
        properties: {
          events: {
            type: 'array',
            items: trackEventSchema,
            maxItems: 100
          }
        },
        required: ['events']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            processed: { type: 'number' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { events } = request.body as { events: any[] };
      const user = (request as any).user;
      
      let processed = 0;
      for (const eventData of events) {
        try {
          const validatedEvent = trackEventSchema.parse(eventData);
          analyticsService.trackEvent({
            ...validatedEvent,
            userId: user.id,
            organizationId: user.organizationId
          });
          processed++;
        } catch (error) {
          fastify.log.warn(error, 'Failed to process event in bulk operation');
        }
      }
      
      return reply.status(201).send({
        success: true,
        message: `Processed ${processed} out of ${events.length} events`,
        processed
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to process bulk events'
      });
    }
  });
}