# ESTRATIX Knowledge Source Matrix

**Objective**: This matrix is the central registry for all external and internal knowledge sources that feed the ESTRATIX research and development lifecycle. It defines not just *what* the sources are, but *why* and *how* they are monitored, providing the operational backbone for the `knowledge_lifecycle_management.md` workflow.

**Scope**: This matrix governs all recurring data sources, including websites, APIs, publications, code repositories, and internal subject matter experts (SMEs) tracked in the `user_matrix.md`.

---

## Source Monitoring & Ingestion Lifecycle

1. **Registration**: A new knowledge source is identified and registered here with a unique `Source ID`.
2. **Association**: The source is linked to relevant `Topic ID(s)` (from `topic_matrix.md`) and `User ID(s)` (from `user_matrix.md`) to establish context and ownership.
3. **Monitoring**: A designated `Monitoring Agent` is assigned to watch the source at a specified `Check Frequency`.
4. **Triggering Research**: When the monitoring agent detects a relevant update, it triggers the creation of a `Research Batch ID` in the `research_matrix.md`, formally initiating a new research cycle.

---

## Source Matrix

| Source ID | Source Name | Source Type | URL / Identifier | Status | Check Frequency | Last Checked | Monitored By (Agent ID) | Related Topic ID(s) | Related User ID(s) | Notes |
|---|---|---|---|---|---|---|---|---|---|---|
| `SRC-001` | YouTube | Video | `https://www.youtube.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-001`, `TOP-003` | `USR-001` | General research, tutorials, market analysis. |
| `SRC-002` | GitHub | Code Repository | `https://github.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-001`, `TOP-002` | `USR-002` | Source code, documentation, and technical discussions. |
| `SRC-003` | ArXiv | Research Paper | `https://arxiv.org` | `Active` | Weekly | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-002`, `TOP-003` | `N/A` | Pre-print articles for foundational research. |
| `SRC-004` | Hugging Face | AI/ML Hub | `https://huggingface.co` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-003` | `USR-001` | Models, datasets, and papers for agentic development. |
| `SRC-005` | Reddit | Forum | `https://www.reddit.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-001` | `N/A` | Community discussions and sentiment analysis. |
| `SRC-006` | Internal SME | User | `USR-001` | `Active` | As Needed | `N/A` | `N/A` | `ALL` | `USR-001` | John Doe, expert on Agentic AI. Tracked in `user_matrix.md`. |
| `SRC-007` | CrewAI Core Docs | Documentation (File) | `../../archive/documentation/crewaiinc-crewai.txt` | `Pending Ingestion` | On-Demand | `2025-07-07` | `N/A` | `TOP-004` | `N/A` | For training MasterBuilderAgent. Ingestion is a one-time event. |
| `SRC-008` | CrewAI Tools Docs | Documentation (File) | `../../archive/documentation/crewaiinc-crewai-tools.txt` | `Pending Ingestion` | On-Demand | `2025-07-07` | `N/A` | `TOP-004` | `N/A` | For training MasterBuilderAgent. Ingestion is a one-time event. |
| `SRC-009` | CrewAI Examples Docs | Documentation (File) | `../../archive/documentation/crewaiinc-crewai-examples.txt` | `Pending Ingestion` | On-Demand | `2025-07-07` | `N/A` | `TOP-004` | `N/A` | For training MasterBuilderAgent. Ingestion is a one-time event. |
| `SRC-010` | Instagram | Social Media | `https://www.instagram.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-004` | `N/A` | Instagram posts for trend analysis. |
| `SRC-011` | TikTok | Social Media | `https://www.tiktok.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-004` | `N/A` | TikTok posts for trend analysis. |
| `SRC-012` | Medium | Social Media | `https://medium.com` | `Active` | Daily | `YYYY-MM-DD` | `CIO_A001_SourceMonitor` | `TOP-004` | `N/A` | Medium posts for trend analysis. |

---

## Guidance for Use

- **Central Registry**: All recurring knowledge sources *must* be registered here before being used in a research batch.
- **Dynamic Tracking**: The `Last Checked` and `Status` columns should be updated programmatically by the `Monitoring Agent`.
- **Context is Key**: Linking to `Topic` and `User` matrices is critical for ensuring that monitoring efforts are targeted and relevant.
