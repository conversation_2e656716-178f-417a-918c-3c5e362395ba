import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { authenticateToken, requireRole } from '@/middleware/auth';
import { validateCreateClient, validateUpdateClient, ValidationError } from '@/utils/validation';
import { logger } from '@/utils/logger';
import { Client, CreateClientRequest, UpdateClientRequest } from '@/types';

export interface GetClientsQuery {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ClientParams {
  id: string;
}

export async function clientRoutes(fastify: FastifyInstance) {
  // Get all clients with filtering and pagination
  fastify.get('/clients', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get all clients with filtering and pagination',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 10 },
          search: { type: 'string' },
          status: { type: 'string', enum: ['active', 'inactive', 'pending'] },
          sortBy: { type: 'string', enum: ['name', 'email', 'company', 'createdAt', 'lastContactDate'] },
          sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            clients: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  email: { type: 'string' },
                  phone: { type: 'string' },
                  company: { type: 'string' },
                  position: { type: 'string' },
                  status: { type: 'string' },
                  source: { type: 'string' },
                  notes: { type: 'string' },
                  tags: { type: 'array', items: { type: 'string' } },
                  createdAt: { type: 'string' },
                  updatedAt: { type: 'string' },
                  lastContactDate: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'integer' },
                limit: { type: 'integer' },
                total: { type: 'integer' },
                pages: { type: 'integer' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Querystring: GetClientsQuery }>, reply: FastifyReply) => {
    try {
      const { page = 1, limit = 10, search, status, sortBy = 'createdAt', sortOrder = 'desc' } = request.query;
      
      const result = await (fastify as any).clientService.getClients({
        page,
        limit,
        search,
        status,
        sortBy,
        sortOrder
      });

      logger.info('Clients retrieved', {
        userId: (request as any).user?.id,
        count: result.clients.length,
        page,
        limit
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to get clients', { error, query: request.query });
      return reply.status(500).send({ error: 'Failed to retrieve clients' });
    }
  });

  // Get client by ID
  fastify.get('/clients/:id', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get client by ID',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            email: { type: 'string' },
            phone: { type: 'string' },
            company: { type: 'string' },
            position: { type: 'string' },
            status: { type: 'string' },
            source: { type: 'string' },
            notes: { type: 'string' },
            tags: { type: 'array', items: { type: 'string' } },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
            lastContactDate: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: ClientParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const client = await (fastify as any).clientService.getClientById(id);
      
      if (!client) {
        return reply.status(404).send({ error: 'Client not found' });
      }

      logger.info('Client retrieved', {
        userId: (request as any).user?.id,
        clientId: id
      });

      return reply.status(200).send(client);
    } catch (error) {
      logger.error('Failed to get client', { error, clientId: request.params.id });
      return reply.status(500).send({ error: 'Failed to retrieve client' });
    }
  });

  // Create new client
  fastify.post('/clients', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Create a new client',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100 },
          email: { type: 'string', format: 'email' },
          phone: { type: 'string' },
          company: { type: 'string', maxLength: 100 },
          position: { type: 'string', maxLength: 100 },
          source: { type: 'string', enum: ['website', 'referral', 'social_media', 'email_campaign', 'cold_outreach', 'event', 'other'] },
          notes: { type: 'string', maxLength: 1000 },
          tags: { type: 'array', items: { type: 'string' }, maxItems: 10 }
        },
        required: ['name', 'email']
      },
      response: {
        201: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            email: { type: 'string' },
            phone: { type: 'string' },
            company: { type: 'string' },
            position: { type: 'string' },
            status: { type: 'string' },
            source: { type: 'string' },
            notes: { type: 'string' },
            tags: { type: 'array', items: { type: 'string' } },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
            lastContactDate: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'string' } }
          }
        },
        409: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Body: CreateClientRequest }>, reply: FastifyReply) => {
    try {
      // Validate request body
      const validationResult = validateCreateClient(request.body);
      if (!validationResult.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validationResult.error.errors.map(err => err.message)
        });
      }

      // Check if client with email already exists
      const existingClient = await (fastify as any).clientService.getClientByEmail(request.body.email);
      if (existingClient) {
        return reply.status(409).send({ error: 'Client with this email already exists' });
      }

      const client = await (fastify as any).clientService.createClient(request.body);

      // Send welcome email
      try {
        await (fastify as any).emailService.sendWelcomeEmail(client);
      } catch (emailError) {
        logger.warn('Failed to send welcome email', { error: emailError, clientId: client.id });
      }

      logger.info('Client created', {
        userId: (request as any).user?.id,
        clientId: client.id,
        email: client.email
      });

      return reply.status(201).send(client);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to create client', { error, body: request.body });
      return reply.status(500).send({ error: 'Failed to create client' });
    }
  });

  // Update client
  fastify.put('/clients/:id', {
    preHandler: [authenticateToken, requireRole(['admin', 'manager'])],
    schema: {
      description: 'Update client information',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          name: { type: 'string', minLength: 1, maxLength: 100 },
          email: { type: 'string', format: 'email' },
          phone: { type: 'string' },
          company: { type: 'string', maxLength: 100 },
          position: { type: 'string', maxLength: 100 },
          status: { type: 'string', enum: ['active', 'inactive', 'pending'] },
          source: { type: 'string', enum: ['website', 'referral', 'social_media', 'email_campaign', 'cold_outreach', 'event', 'other'] },
          notes: { type: 'string', maxLength: 1000 },
          tags: { type: 'array', items: { type: 'string' }, maxItems: 10 }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            email: { type: 'string' },
            phone: { type: 'string' },
            company: { type: 'string' },
            position: { type: 'string' },
            status: { type: 'string' },
            source: { type: 'string' },
            notes: { type: 'string' },
            tags: { type: 'array', items: { type: 'string' } },
            createdAt: { type: 'string' },
            updatedAt: { type: 'string' },
            lastContactDate: { type: 'string' }
          }
        },
        400: {
          type: 'object',
          properties: {
            error: { type: 'string' },
            details: { type: 'array', items: { type: 'string' } }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: ClientParams; Body: UpdateClientRequest }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      // Validate request body
      const validationResult = validateUpdateClient(request.body);
      if (!validationResult.success) {
        return reply.status(400).send({
          error: 'Validation failed',
          details: validationResult.error.errors.map(err => err.message)
        });
      }

      // Check if client exists
      const existingClient = await fastify.clientService.getClientById(id);
      if (!existingClient) {
        return reply.status(404).send({ error: 'Client not found' });
      }

      // Check if email is being changed and if new email already exists
      if (request.body.email && request.body.email !== existingClient.email) {
        const clientWithEmail = await fastify.clientService.getClientByEmail(request.body.email);
        if (clientWithEmail) {
          return reply.status(409).send({ error: 'Client with this email already exists' });
        }
      }

      const updatedClient = await fastify.clientService.updateClient(id, request.body);

      logger.info('Client updated', {
        userId: (request as any).user?.id,
        clientId: id,
        changes: Object.keys(request.body)
      });

      return reply.status(200).send(updatedClient);
    } catch (error) {
      if (error instanceof ValidationError) {
        return reply.status(400).send({ error: error.message });
      }
      
      logger.error('Failed to update client', { error, clientId: request.params.id, body: request.body });
      return reply.status(500).send({ error: 'Failed to update client' });
    }
  });

  // Delete client
  fastify.delete('/clients/:id', {
    preHandler: [authenticateToken, requireRole(['admin'])],
    schema: {
      description: 'Delete client',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        204: {
          type: 'null'
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: ClientParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const deleted = await fastify.clientService.deleteClient(id);
      
      if (!deleted) {
        return reply.status(404).send({ error: 'Client not found' });
      }

      logger.info('Client deleted', {
        userId: (request as any).user?.id,
        clientId: id
      });

      return reply.status(204).send();
    } catch (error) {
      logger.error('Failed to delete client', { error, clientId: request.params.id });
      return reply.status(500).send({ error: 'Failed to delete client' });
    }
  });

  // Update last contact date
  fastify.patch('/clients/:id/contact', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Update client last contact date',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            lastContactDate: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest<{ Params: ClientParams }>, reply: FastifyReply) => {
    try {
      const { id } = request.params;
      
      const result = await fastify.clientService.updateLastContact(id);
      
      if (!result) {
        return reply.status(404).send({ error: 'Client not found' });
      }

      logger.info('Client last contact updated', {
        userId: (request as any).user?.id,
        clientId: id
      });

      return reply.status(200).send(result);
    } catch (error) {
      logger.error('Failed to update client last contact', { error, clientId: request.params.id });
      return reply.status(500).send({ error: 'Failed to update last contact' });
    }
  });

  // Get client statistics
  fastify.get('/clients/stats', {
    preHandler: [authenticateToken],
    schema: {
      description: 'Get client statistics',
      tags: ['Clients'],
      security: [{ bearerAuth: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            total: { type: 'integer' },
            active: { type: 'integer' },
            inactive: { type: 'integer' },
            pending: { type: 'integer' },
            newThisMonth: { type: 'integer' },
            bySource: {
              type: 'object',
              additionalProperties: { type: 'integer' }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const stats = await fastify.clientService.getClientStats();

      logger.info('Client statistics retrieved', {
        userId: (request as any).user?.id,
        total: stats.total
      });

      return reply.status(200).send(stats);
    } catch (error) {
      logger.error('Failed to get client statistics', { error });
      return reply.status(500).send({ error: 'Failed to retrieve client statistics' });
    }
  });
}