// User interface definition
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  permissions: Permission[];
  subdomain?: string;
  createdAt: Date;
  lastLogin?: Date;
  isActive: boolean;
}

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  AGENCY_ADMIN = 'agency_admin',
  CLIENT_ADMIN = 'client_admin',
  DEVELOPER = 'developer',
  PROPERTY_MANAGER = 'property_manager',
  AI_AGENT_OPERATOR = 'ai_agent_operator',
  CONTENT_MANAGER = 'content_manager',
  INVESTOR = 'investor',
  SERVICE_PROVIDER = 'service_provider',
  CONSUMER = 'consumer'
}

export enum Permission {
  // Super Admin
  MANAGE_ALL_CLIENTS = 'manage_all_clients',
  MANAGE_INFRASTRUCTURE = 'manage_infrastructure',
  
  // Agency Admin
  MANAGE_CLIENT_ACCOUNTS = 'manage_client_accounts',
  VIEW_ALL_ANALYTICS = 'view_all_analytics',
  MANAGE_DEVELOPERS = 'manage_developers',
  
  // Client Admin
  MANAGE_PROPERTIES = 'manage_properties',
  MANAGE_TEAM = 'manage_team',
  VIEW_CLIENT_ANALYTICS = 'view_client_analytics',
  
  // Developer
  ACCESS_DEV_TOOLS = 'access_dev_tools',
  DEPLOY_APPLICATIONS = 'deploy_applications',
  MANAGE_INTEGRATIONS = 'manage_integrations',
  
  // Property Manager
  MANAGE_LISTINGS = 'manage_listings',
  HANDLE_BOOKINGS = 'handle_bookings',
  MANAGE_MAINTENANCE = 'manage_maintenance',
  
  // AI Agent Operator
  MANAGE_AI_AGENTS = 'manage_ai_agents',
  TRAIN_MODELS = 'train_models',
  VIEW_AI_ANALYTICS = 'view_ai_analytics',
  
  // Content Manager
  MANAGE_CONTENT = 'manage_content',
  MANAGE_MEDIA = 'manage_media',
  PUBLISH_CONTENT = 'publish_content',
  
  // Investor
  VIEW_INVESTMENT_OPPORTUNITIES = 'view_investment_opportunities',
  MANAGE_PORTFOLIO = 'manage_portfolio',
  ACCESS_FINANCIAL_DATA = 'access_financial_data',
  
  // Service Provider
  MANAGE_SERVICES = 'manage_services',
  HANDLE_SERVICE_BOOKINGS = 'handle_service_bookings',
  VIEW_EARNINGS = 'view_earnings',
  
  // Consumer
  BOOK_SERVICES = 'book_services',
  MANAGE_BOOKINGS = 'manage_bookings',
  LEAVE_REVIEWS = 'leave_reviews'
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  subdomain?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role: UserRole;
  subdomain?: string;
}

export interface SubdomainConfig {
  subdomain: string;
  clientId: string;
  theme: {
    primaryColor: string;
    logo: string;
    brandName: string;
  };
  features: string[];
  customDomain?: string;
}