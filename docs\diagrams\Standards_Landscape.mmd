```mermaid
graph TD
    subgraph "ESTRATIX Standards Landscape"
        direction LR

        subgraph "Categories"
            direction TB
            cat_coding["Coding Conventions"] --> std_python["STD_CODING_PYTHON_001 <br/> Python Coding Standards"];
            cat_pm["Project Management"] --> std_scrum["STD_PM_AGILE_SCRUM_001 <br/> Agile Scrum Methodology"];
            cat_security["Security Protocols"] --> std_data["STD_SECURITY_DATAPROTECTION_001 <br/> Data Protection"];
            cat_naming["Naming Conventions"]
            cat_compliance["Compliance Requirements"]
        end

        subgraph "Responsible Offices"
            CTO --> cat_coding;
            CTO --> cat_naming;
            CPrO --> cat_pm;
            CIO --> cat_security;
            CEO --> cat_compliance; %% Example for high-level compliance standards
        end

        std_python -.-> AppDevProcess["App Development Process"];
        std_scrum -.-> ClientProjectA["Client Project Alpha"];
        std_data -.-> UserManagementService["User Management Service"];

    end

    classDef category fill:#e6e6fa,stroke:#333,stroke-width:2px;
    classDef standard fill:#fffacd,stroke:#333,stroke-width:2px;
    classDef office fill:#add8e6,stroke:#333,stroke-width:2px;
    classDef component fill:#d3d3d3,stroke:#333,stroke-width:1px;

    class cat_coding,cat_pm,cat_security,cat_naming,cat_compliance category;
    class std_python,std_scrum,std_data standard;
    class CTO,CPrO,CIO,CEO office;
    class AppDevProcess,ClientProjectA,UserManagementService component;

    %% Note: This is a placeholder diagram.
    %% It will be automatically updated by the wf_update_standards_landscape.md workflow
    %% based on the contents of docs/matrices/standards_matrix.md.
```
