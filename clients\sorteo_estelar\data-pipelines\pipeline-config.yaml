# Data Pipeline Configuration for Sorteo Estelar
# Comprehensive data processing, analytics, and monitoring setup

apiVersion: v1
kind: ConfigMap
metadata:
  name: sorteo-estelar-data-pipelines
  namespace: default
data:
  # Main Pipeline Configuration
  pipeline-config.yaml: |
    pipelines:
      # Real-time Event Processing Pipeline
      event-processing:
        name: "sorteo-estelar-events"
        description: "Process real-time lottery and gaming events"
        type: "streaming"
        source:
          type: "kafka"
          config:
            bootstrap_servers: "kafka:9092"
            topics:
              - "lottery-events"
              - "user-actions"
              - "nft-transactions"
              - "defi-operations"
            consumer_group: "sorteo-events-processor"
            auto_offset_reset: "latest"
        
        processors:
          - name: "event-validator"
            type: "validation"
            config:
              schema_registry: "http://schema-registry:8081"
              validate_schema: true
              drop_invalid: false
              
          - name: "event-enricher"
            type: "enrichment"
            config:
              user_service: "http://user-service:8080"
              nft_service: "http://nft-service:8080"
              cache_ttl: 300
              
          - name: "event-transformer"
            type: "transformation"
            config:
              add_timestamp: true
              add_session_id: true
              normalize_amounts: true
              
        sinks:
          - name: "analytics-store"
            type: "clickhouse"
            config:
              host: "clickhouse:8123"
              database: "sorteo_analytics"
              table: "events"
              batch_size: 1000
              flush_interval: "30s"
              
          - name: "real-time-alerts"
            type: "webhook"
            config:
              url: "http://alert-service:8080/alerts"
              method: "POST"
              headers:
                Content-Type: "application/json"
              
      # Batch Analytics Pipeline
      batch-analytics:
        name: "sorteo-estelar-analytics"
        description: "Daily batch processing for analytics and reporting"
        type: "batch"
        schedule: "0 2 * * *"  # Daily at 2 AM
        
        source:
          type: "postgresql"
          config:
            host: "postgres:5432"
            database: "sorteo_estelar"
            username: "postgres"
            password_env: "DB_PASSWORD"
            
        jobs:
          - name: "user-analytics"
            query: |
              SELECT 
                DATE(created_at) as date,
                COUNT(*) as new_users,
                COUNT(CASE WHEN last_login > NOW() - INTERVAL '7 days' THEN 1 END) as active_users,
                AVG(total_spent) as avg_spending
              FROM users 
              WHERE created_at >= CURRENT_DATE - INTERVAL '1 day'
              GROUP BY DATE(created_at)
              
          - name: "lottery-analytics"
            query: |
              SELECT 
                DATE(draw_date) as date,
                lottery_id,
                COUNT(DISTINCT user_id) as participants,
                SUM(ticket_price) as total_revenue,
                COUNT(*) as total_tickets
              FROM lottery_tickets 
              WHERE draw_date >= CURRENT_DATE - INTERVAL '1 day'
              GROUP BY DATE(draw_date), lottery_id
              
          - name: "nft-analytics"
            query: |
              SELECT 
                DATE(transaction_date) as date,
                nft_collection,
                COUNT(*) as transactions,
                SUM(price) as volume,
                AVG(price) as avg_price
              FROM nft_transactions 
              WHERE transaction_date >= CURRENT_DATE - INTERVAL '1 day'
              GROUP BY DATE(transaction_date), nft_collection
              
        sink:
          type: "clickhouse"
          config:
            host: "clickhouse:8123"
            database: "sorteo_analytics"
            tables:
              user_analytics: "daily_user_stats"
              lottery_analytics: "daily_lottery_stats"
              nft_analytics: "daily_nft_stats"
              
      # ML Feature Pipeline
      ml-features:
        name: "sorteo-estelar-ml-features"
        description: "Generate ML features for recommendation and fraud detection"
        type: "streaming"
        
        source:
          type: "kafka"
          config:
            bootstrap_servers: "kafka:9092"
            topics:
              - "user-actions"
              - "lottery-events"
            consumer_group: "ml-feature-processor"
            
        processors:
          - name: "feature-extractor"
            type: "ml-features"
            config:
              window_size: "1h"
              features:
                - "user_activity_frequency"
                - "spending_velocity"
                - "lottery_participation_pattern"
                - "nft_trading_behavior"
                - "session_duration"
                - "device_fingerprint"
                
          - name: "anomaly-detector"
            type: "ml-inference"
            config:
              model_endpoint: "http://ml-service:8080/detect-anomaly"
              threshold: 0.8
              
        sinks:
          - name: "feature-store"
            type: "redis"
            config:
              host: "redis:6379"
              key_prefix: "features:"
              ttl: 86400  # 24 hours
              
          - name: "fraud-alerts"
            type: "webhook"
            config:
              url: "http://fraud-service:8080/alerts"
              condition: "anomaly_score > 0.8"
              
  # Data Sources Configuration
  data-sources.yaml: |
    sources:
      # Primary Database
      postgresql:
        host: "postgres"
        port: 5432
        database: "sorteo_estelar"
        username: "postgres"
        password_env: "DB_PASSWORD"
        ssl_mode: "require"
        connection_pool:
          max_connections: 20
          idle_timeout: "30s"
          
      # Analytics Database
      clickhouse:
        host: "clickhouse"
        port: 8123
        database: "sorteo_analytics"
        username: "default"
        password_env: "CLICKHOUSE_PASSWORD"
        compression: true
        
      # Cache and Session Store
      redis:
        host: "redis"
        port: 6379
        password_env: "REDIS_PASSWORD"
        database: 0
        
      # Message Queue
      kafka:
        bootstrap_servers: "kafka:9092"
        security_protocol: "SASL_SSL"
        sasl_mechanism: "PLAIN"
        sasl_username_env: "KAFKA_USERNAME"
        sasl_password_env: "KAFKA_PASSWORD"
        
      # Blockchain Data
      ethereum:
        rpc_url_env: "ETHEREUM_RPC_URL"
        websocket_url_env: "ETHEREUM_WS_URL"
        contracts:
          lottery_contract: "******************************************"
          nft_contract: "******************************************"
          token_contract: "******************************************"
          
      # External APIs
      external_apis:
        coingecko:
          base_url: "https://api.coingecko.com/api/v3"
          rate_limit: 50  # requests per minute
          
        opensea:
          base_url: "https://api.opensea.io/api/v1"
          api_key_env: "OPENSEA_API_KEY"
          rate_limit: 4  # requests per second
          
  # Data Transformations
  transformations.yaml: |
    transformations:
      # User Event Transformation
      user_events:
        input_schema:
          user_id: "string"
          event_type: "string"
          timestamp: "datetime"
          properties: "json"
          
        output_schema:
          user_id: "string"
          event_type: "string"
          event_timestamp: "datetime"
          session_id: "string"
          device_info: "json"
          location: "json"
          processed_at: "datetime"
          
        rules:
          - add_field:
              field: "processed_at"
              value: "now()"
              
          - extract_field:
              source: "properties.session_id"
              target: "session_id"
              
          - transform_field:
              field: "timestamp"
              target: "event_timestamp"
              type: "datetime"
              format: "ISO8601"
              
      # Lottery Transaction Transformation
      lottery_transactions:
        input_schema:
          transaction_hash: "string"
          user_address: "string"
          lottery_id: "string"
          ticket_numbers: "array"
          amount: "decimal"
          block_number: "integer"
          
        output_schema:
          transaction_id: "string"
          user_id: "string"
          lottery_id: "string"
          ticket_count: "integer"
          ticket_numbers: "array"
          amount_eth: "decimal"
          amount_usd: "decimal"
          gas_used: "integer"
          gas_price: "decimal"
          block_number: "integer"
          transaction_timestamp: "datetime"
          
        rules:
          - map_field:
              source: "transaction_hash"
              target: "transaction_id"
              
          - lookup_field:
              source: "user_address"
              target: "user_id"
              lookup_table: "user_addresses"
              
          - calculate_field:
              target: "ticket_count"
              expression: "len(ticket_numbers)"
              
          - convert_currency:
              source: "amount"
              target: "amount_usd"
              from: "ETH"
              to: "USD"
              
  # Data Quality Rules
  data-quality.yaml: |
    quality_rules:
      # User Data Quality
      users:
        - rule: "not_null"
          fields: ["user_id", "email", "created_at"]
          severity: "error"
          
        - rule: "unique"
          fields: ["user_id", "email"]
          severity: "error"
          
        - rule: "format"
          field: "email"
          pattern: "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
          severity: "error"
          
        - rule: "range"
          field: "age"
          min: 18
          max: 120
          severity: "warning"
          
      # Transaction Data Quality
      transactions:
        - rule: "not_null"
          fields: ["transaction_id", "user_id", "amount", "timestamp"]
          severity: "error"
          
        - rule: "positive"
          fields: ["amount"]
          severity: "error"
          
        - rule: "recent"
          field: "timestamp"
          max_age: "24h"
          severity: "warning"
          
      # Event Data Quality
      events:
        - rule: "not_null"
          fields: ["event_id", "user_id", "event_type", "timestamp"]
          severity: "error"
          
        - rule: "enum"
          field: "event_type"
          values: ["login", "logout", "purchase", "lottery_entry", "nft_mint", "nft_transfer"]
          severity: "error"
          
        - rule: "json_valid"
          field: "properties"
          severity: "warning"
          
  # Monitoring and Alerting
  monitoring.yaml: |
    monitoring:
      # Pipeline Health Metrics
      metrics:
        - name: "pipeline_throughput"
          type: "counter"
          description: "Number of records processed per pipeline"
          labels: ["pipeline_name", "stage"]
          
        - name: "pipeline_latency"
          type: "histogram"
          description: "Processing latency per pipeline stage"
          labels: ["pipeline_name", "stage"]
          buckets: [0.1, 0.5, 1.0, 2.5, 5.0, 10.0]
          
        - name: "data_quality_violations"
          type: "counter"
          description: "Number of data quality rule violations"
          labels: ["table", "rule", "severity"]
          
        - name: "pipeline_errors"
          type: "counter"
          description: "Number of pipeline processing errors"
          labels: ["pipeline_name", "error_type"]
          
      # Alerting Rules
      alerts:
        - name: "PipelineDown"
          condition: "pipeline_throughput == 0"
          duration: "5m"
          severity: "critical"
          message: "Pipeline {{ $labels.pipeline_name }} has stopped processing data"
          
        - name: "HighLatency"
          condition: "pipeline_latency > 10"
          duration: "2m"
          severity: "warning"
          message: "Pipeline {{ $labels.pipeline_name }} latency is high"
          
        - name: "DataQualityIssues"
          condition: "rate(data_quality_violations[5m]) > 10"
          duration: "1m"
          severity: "warning"
          message: "High rate of data quality violations in {{ $labels.table }}"
          
        - name: "PipelineErrors"
          condition: "rate(pipeline_errors[5m]) > 5"
          duration: "1m"
          severity: "critical"
          message: "High error rate in pipeline {{ $labels.pipeline_name }}"
          
      # Notification Channels
      notifications:
        slack:
          webhook_url_env: "SLACK_WEBHOOK_URL"
          channel: "#sorteo-alerts"
          
        email:
          smtp_server: "smtp.gmail.com"
          smtp_port: 587
          username_env: "SMTP_USERNAME"
          password_env: "SMTP_PASSWORD"
          recipients: ["<EMAIL>", "<EMAIL>"]
          
        pagerduty:
          integration_key_env: "PAGERDUTY_INTEGRATION_KEY"
          
  # Backup and Recovery
  backup-config.yaml: |
    backup:
      # Database Backups
      databases:
        postgresql:
          schedule: "0 2 * * *"  # Daily at 2 AM
          retention: "30d"
          compression: true
          encryption: true
          storage:
            type: "s3"
            bucket: "sorteo-estelar-backups"
            prefix: "postgresql/"
            
        clickhouse:
          schedule: "0 3 * * *"  # Daily at 3 AM
          retention: "90d"
          compression: true
          storage:
            type: "s3"
            bucket: "sorteo-estelar-backups"
            prefix: "clickhouse/"
            
      # Data Pipeline State
      pipeline_state:
        schedule: "0 */6 * * *"  # Every 6 hours
        retention: "7d"
        storage:
          type: "s3"
          bucket: "sorteo-estelar-backups"
          prefix: "pipeline-state/"
          
      # Configuration Backups
      configurations:
        schedule: "0 1 * * *"  # Daily at 1 AM
        retention: "365d"
        storage:
          type: "git"
          repository: "https://github.com/estratix/sorteo-estelar-config.git"
          branch: "backup"
          
    recovery:
      # Recovery Procedures
      procedures:
        database_restore:
          steps:
            - "Stop application services"
            - "Download backup from S3"
            - "Decrypt and decompress backup"
            - "Restore database"
            - "Verify data integrity"
            - "Start application services"
            
        pipeline_restore:
          steps:
            - "Stop data pipelines"
            - "Restore pipeline state"
            - "Verify configuration"
            - "Start pipelines"
            - "Monitor for errors"
            
      # Recovery Testing
      testing:
        schedule: "0 4 * * 0"  # Weekly on Sunday at 4 AM
        procedures:
          - "database_restore"
          - "pipeline_restore"
        environment: "staging"
        
---
# Data Pipeline Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sorteo-estelar-data-pipeline
  namespace: default
spec:
  replicas: 2
  selector:
    matchLabels:
      app: sorteo-estelar-data-pipeline
  template:
    metadata:
      labels:
        app: sorteo-estelar-data-pipeline
    spec:
      containers:
      - name: pipeline-processor
        image: apache/kafka:latest
        env:
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sorteo-estelar-secrets
              key: database-password
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        volumeMounts:
        - name: pipeline-config
          mountPath: /etc/pipeline
      volumes:
      - name: pipeline-config
        configMap:
          name: sorteo-estelar-data-pipelines
          
---
# ClickHouse Analytics Database
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: clickhouse
  namespace: default
spec:
  serviceName: clickhouse
  replicas: 1
  selector:
    matchLabels:
      app: clickhouse
  template:
    metadata:
      labels:
        app: clickhouse
    spec:
      containers:
      - name: clickhouse
        image: clickhouse/clickhouse-server:latest
        ports:
        - containerPort: 8123
        - containerPort: 9000
        env:
        - name: CLICKHOUSE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: sorteo-estelar-secrets
              key: clickhouse-password
        resources:
          requests:
            cpu: "1"
            memory: "2Gi"
          limits:
            cpu: "4"
            memory: "8Gi"
        volumeMounts:
        - name: clickhouse-data
          mountPath: /var/lib/clickhouse
  volumeClaimTemplates:
  - metadata:
      name: clickhouse-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
          
---
# Kafka Message Queue
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
  namespace: default
spec:
  serviceName: kafka
  replicas: 3
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:latest
        ports:
        - containerPort: 9092
        env:
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper:2181"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka:9092"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "3"
        resources:
          requests:
            cpu: "500m"
            memory: "1Gi"
          limits:
            cpu: "2"
            memory: "4Gi"
        volumeMounts:
        - name: kafka-data
          mountPath: /var/lib/kafka/data
  volumeClaimTemplates:
  - metadata:
      name: kafka-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi