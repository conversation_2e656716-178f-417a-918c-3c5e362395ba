# Sorteo Estelar - Web3 Gaming Ecosystem

🎰 **A comprehensive NFT-based lottery platform with DeFi integration**

[![CI/CD Pipeline](https://github.com/estratix/sorteo-estelar/workflows/CI-CD/badge.svg)](https://github.com/estratix/sorteo-estelar/actions)
[![Security Scan](https://github.com/estratix/sorteo-estelar/workflows/Security/badge.svg)](https://github.com/estratix/sorteo-estelar/actions)
[![Deployment Status](https://img.shields.io/badge/deployment-active-green)](https://www.sorteoestelar.com)
[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)

## 🌟 Overview

Sorteo Estelar is a cutting-edge Web3 gaming platform that combines the excitement of lottery games with the innovation of NFTs and DeFi protocols. Built with modern technologies and deployed using advanced DevOps practices.

### 🎯 Key Features

- **🎲 NFT-Based Lottery System**: Unique lottery tickets as NFTs
- **💰 DeFi Integration**: Yield farming and staking mechanisms
- **🔗 Multi-Chain Support**: Ethereum, Polygon, and BSC compatibility
- **📱 Progressive Web App**: Mobile-first responsive design
- **🛡️ Enterprise Security**: Advanced fraud detection and security measures
- **📊 Real-Time Analytics**: Comprehensive data pipelines and monitoring

## 🏗️ Architecture

### Technology Stack

- **Frontend**: React 18 + TypeScript + Vite + Tailwind CSS
- **Backend**: Node.js + Express + TypeScript
- **Database**: PostgreSQL + Redis + ClickHouse (Analytics)
- **Blockchain**: Ethereum + Web3.js + MetaMask Integration
- **Infrastructure**: Docker + Kubernetes + Nginx + Let's Encrypt
- **Monitoring**: Prometheus + Grafana + ELK Stack
- **CI/CD**: GitHub Actions + Dokploy + GitOps

### System Architecture

```mermaid
graph TB
    subgraph "Frontend Layer"
        A[React PWA] --> B[Web3 Wallet]
        A --> C[Real-time UI]
    end
    
    subgraph "API Gateway"
        D[Nginx Ingress] --> E[Load Balancer]
        E --> F[Rate Limiting]
    end
    
    subgraph "Application Layer"
        G[User Service] --> H[Lottery Service]
        H --> I[NFT Service]
        I --> J[DeFi Service]
    end
    
    subgraph "Data Layer"
        K[PostgreSQL] --> L[Redis Cache]
        L --> M[ClickHouse Analytics]
    end
    
    subgraph "Blockchain Layer"
        N[Ethereum] --> O[Smart Contracts]
        O --> P[IPFS Storage]
    end
    
    A --> D
    F --> G
    J --> K
    J --> N
```

## 🚀 Quick Start

### Prerequisites

- Node.js 20+
- Docker & Docker Compose
- Git
- SSH access to VPS (for deployment)

### Local Development

```bash
# Clone the repository
git clone https://github.com/estratix/sorteo-estelar.git
cd sorteo-estelar

# Install dependencies
npm install

# Start development server
npm run dev

# Open browser
open http://localhost:5173
```

### Environment Setup

```bash
# Copy environment template
cp .env.example .env.local

# Edit environment variables
vim .env.local
```

### Required Environment Variables

```env
# Application
NODE_ENV=development
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/sorteo_estelar
REDIS_URL=redis://localhost:6379

# Blockchain
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/YOUR_PROJECT_ID
ETHEREUM_WS_URL=wss://mainnet.infura.io/ws/v3/YOUR_PROJECT_ID
PRIVATE_KEY=your_private_key_here

# External Services
OPENSEA_API_KEY=your_opensea_api_key
COINGECKO_API_KEY=your_coingecko_api_key
```

## 🔧 Development Workflow

### Git Worktree Management

We use Git worktrees for efficient branch management and trunk-based development:

```bash
# Initialize worktree structure
./scripts/git-worktree-manager.sh init

# Create feature branch
./scripts/git-worktree-manager.sh feature user-authentication

# Create hotfix branch
./scripts/git-worktree-manager.sh hotfix critical-security-fix

# List all worktrees
./scripts/git-worktree-manager.sh list

# Merge feature to develop
./scripts/git-worktree-manager.sh merge-feature user-authentication
```

### Branch Strategy

- **`main`**: Production-ready code
- **`develop`**: Integration branch for features
- **`staging`**: Pre-production testing
- **`feature/*`**: Feature development
- **`hotfix/*`**: Critical fixes
- **`release/*`**: Release preparation

### Development Commands

```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build

# Testing
npm run test         # Run unit tests
npm run test:e2e     # Run end-to-end tests
npm run test:coverage # Generate coverage report

# Code Quality
npm run lint         # Lint code
npm run lint:fix     # Fix linting issues
npm run type-check   # TypeScript type checking
npm run format       # Format code with Prettier

# Security
npm audit            # Check for vulnerabilities
npm run security:scan # Run security scan
```

## 🏭 Production Deployment

### VPS Infrastructure

**Server Details:**
- **Host**: **************
- **Port**: 2222
- **User**: admin
- **Domain**: www.sorteoestelar.com

### Deployment Architecture

```mermaid
graph LR
    subgraph "CI/CD Pipeline"
        A[GitHub] --> B[GitHub Actions]
        B --> C[Build & Test]
        C --> D[Security Scan]
        D --> E[Docker Build]
    end
    
    subgraph "VPS Infrastructure"
        F[Nginx] --> G[Dokploy]
        G --> H[Docker Containers]
        H --> I[PostgreSQL]
        H --> J[Redis]
    end
    
    subgraph "Monitoring"
        K[Prometheus] --> L[Grafana]
        L --> M[Alerting]
    end
    
    E --> F
    H --> K
```

### Automated Deployment

```bash
# Full VPS deployment
./scripts/deploy-vps.sh

# Individual deployment steps
./scripts/deploy-vps.sh vps-setup     # Setup VPS environment
./scripts/deploy-vps.sh dokploy       # Install Dokploy
./scripts/deploy-vps.sh ssl           # Setup SSL certificates
./scripts/deploy-vps.sh deploy        # Deploy application
./scripts/deploy-vps.sh monitoring    # Setup monitoring
```

### SSH and VPS Management

```bash
# SSH key management
./scripts/ssh-vps-manager.sh generate-key
./scripts/ssh-vps-manager.sh install-key
./scripts/ssh-vps-manager.sh test-connection

# VPS administration
./scripts/ssh-vps-manager.sh security-audit
./scripts/ssh-vps-manager.sh update-system
./scripts/ssh-vps-manager.sh monitor-resources
./scripts/ssh-vps-manager.sh backup full
```

## 📊 Data Pipelines

### Real-Time Processing

- **Event Streaming**: Kafka-based event processing
- **Analytics**: ClickHouse for real-time analytics
- **ML Features**: Real-time feature extraction for ML models
- **Fraud Detection**: Anomaly detection pipeline

### Batch Processing

- **Daily Analytics**: User, lottery, and NFT analytics
- **Reporting**: Automated report generation
- **Data Quality**: Comprehensive data validation
- **Backup**: Automated data backup and recovery

### Pipeline Management

```bash
# View pipeline status
kubectl get pods -l app=sorteo-estelar-data-pipeline

# Check pipeline logs
kubectl logs -f deployment/sorteo-estelar-data-pipeline

# Monitor pipeline metrics
open http://monitoring.sorteoestelar.com
```

## 🔐 Security

### Security Measures

- **SSL/TLS**: Let's Encrypt certificates with auto-renewal
- **Firewall**: UFW with strict rules
- **SSH**: Key-based authentication only
- **Container Security**: Non-root users, read-only filesystems
- **Secrets Management**: Encrypted environment variables
- **Vulnerability Scanning**: Automated security scans

### Security Monitoring

- **Failed Login Attempts**: Real-time monitoring
- **Anomaly Detection**: ML-based fraud detection
- **Security Audits**: Daily automated audits
- **Compliance**: GDPR and security best practices

## 📈 Monitoring & Observability

### Monitoring Stack

- **Metrics**: Prometheus + Grafana
- **Logs**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **Tracing**: Jaeger for distributed tracing
- **Uptime**: Synthetic monitoring
- **Alerts**: PagerDuty integration

### Key Metrics

- **Application Performance**: Response time, throughput, error rate
- **Infrastructure**: CPU, memory, disk, network usage
- **Business Metrics**: User engagement, revenue, lottery participation
- **Security**: Failed logins, anomalies, vulnerabilities

### Monitoring URLs

- **Grafana**: https://monitoring.sorteoestelar.com
- **Prometheus**: http://**************:9090
- **Dokploy**: http://**************:3000

## 🧪 Testing

### Testing Strategy

- **Unit Tests**: Jest + React Testing Library
- **Integration Tests**: Supertest for API testing
- **E2E Tests**: Playwright for browser automation
- **Performance Tests**: K6 for load testing
- **Security Tests**: OWASP ZAP integration

### Test Commands

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:performance

# Generate coverage report
npm run test:coverage
```

## 📚 API Documentation

### REST API

- **Base URL**: https://api.sorteoestelar.com
- **Documentation**: https://api.sorteoestelar.com/docs
- **OpenAPI Spec**: https://api.sorteoestelar.com/openapi.json

### WebSocket API

- **URL**: wss://api.sorteoestelar.com
- **Events**: Real-time lottery draws, NFT updates, user notifications

### Blockchain Integration

- **Smart Contracts**: Ethereum mainnet
- **IPFS**: Metadata and asset storage
- **Web3 Provider**: MetaMask, WalletConnect

## 🔄 CI/CD Workflows

### GitHub Actions Workflows

1. **CI/CD Pipeline** (`.github/workflows/ci-cd.yml`)
   - Linting, type checking, testing
   - Security scanning
   - Docker build and push
   - Deployment to staging/production

2. **GitOps Deployment** (`.github/workflows/gitops-deployment.yml`)
   - VPS environment management
   - Dokploy API deployment
   - Post-deployment verification

3. **Git Worktree Management** (`.github/workflows/git-worktree-management.yml`)
   - Automated worktree creation
   - Branch compliance validation
   - Cleanup of stale branches

4. **SSH/VPS Management** (`.github/workflows/ssh-vps-management.yml`)
   - SSH key rotation
   - Security audits
   - System updates
   - Backup management

5. **Monitoring & Performance** (`.github/workflows/monitoring-performance.yml`)
   - Health checks
   - Performance testing
   - Security scanning
   - Dependency audits

### Deployment Environments

- **Development**: https://dev.sorteoestelar.com
- **Staging**: https://staging.sorteoestelar.com
- **Production**: https://www.sorteoestelar.com

## 🛠️ Troubleshooting

### Common Issues

#### Development Server Won't Start

```bash
# Check Node.js version
node --version  # Should be 20+

# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Check for port conflicts
lsof -i :5173
```

#### Docker Issues

```bash
# Restart Docker services
docker-compose down
docker-compose up -d

# Check container logs
docker-compose logs -f

# Clean up Docker resources
docker system prune -f
```

#### VPS Connection Issues

```bash
# Test SSH connection
./scripts/ssh-vps-manager.sh test-connection

# Check VPS status
./scripts/ssh-vps-manager.sh status

# View VPS logs
ssh -p 2222 admin@************** 'sudo journalctl -f'
```

### Debug Commands

```bash
# Application logs
npm run logs

# Database connection test
npm run db:test

# Health check
curl https://www.sorteoestelar.com/health

# Performance metrics
curl https://www.sorteoestelar.com/metrics
```

## 📋 Project Structure

```
sorteo-estelar/
├── .github/workflows/          # GitHub Actions workflows
├── data-pipelines/            # Data processing configurations
├── gitops/                    # GitOps deployment configs
├── scripts/                   # Deployment and management scripts
├── src/                       # Source code
│   ├── components/           # React components
│   ├── hooks/               # Custom React hooks
│   ├── pages/               # Page components
│   ├── services/            # API services
│   ├── store/               # State management
│   ├── types/               # TypeScript types
│   └── utils/               # Utility functions
├── public/                    # Static assets
├── tests/                     # Test files
├── docker-compose.yml         # Local development setup
├── Dockerfile                 # Production container
├── dokploy.config.json       # Dokploy configuration
├── package.json              # Dependencies and scripts
├── tailwind.config.js        # Tailwind CSS configuration
├── tsconfig.json             # TypeScript configuration
├── vite.config.ts            # Vite configuration
└── README.md                 # This file
```

## 🤝 Contributing

### Development Process

1. **Fork** the repository
2. **Create** a feature branch using worktree manager
3. **Develop** your feature with tests
4. **Test** thoroughly (unit, integration, e2e)
5. **Submit** a pull request
6. **Review** process and merge

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Airbnb configuration
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Semantic commit messages
- **Test Coverage**: Minimum 80% coverage

### Pull Request Process

1. Ensure all tests pass
2. Update documentation
3. Add changelog entry
4. Request review from maintainers
5. Address feedback
6. Merge after approval

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help

- **Documentation**: Check this README and inline code comments
- **Issues**: Create a GitHub issue for bugs or feature requests
- **Discussions**: Use GitHub Discussions for questions
- **Email**: Contact <EMAIL> for urgent issues

### Maintenance

- **Security Updates**: Automated daily scans and updates
- **Dependency Updates**: Weekly automated updates
- **Performance Monitoring**: 24/7 monitoring and alerting
- **Backup**: Daily automated backups with 30-day retention

---

**Built with ❤️ by the ESTRATIX team**

🌟 **Star this repository if you find it useful!**

📧 **Contact**: <EMAIL>  
🌐 **Website**: https://www.sorteoestelar.com  
📱 **Twitter**: @SorteoEstelar  
💬 **Discord**: https://discord.gg/sorteoestelar