import { FastifyPluginAsync, FastifyRequest, FastifyReply, FastifyError } from 'fastify';
import { ZodError } from 'zod';
import { logger } from '../utils/logger';
import { config } from '../config/environment';

interface CustomError extends Error {
  statusCode?: number;
  code?: string;
  validation?: any[];
}

export const errorHandler: FastifyPluginAsync = async (fastify) => {
  // Global error handler
  fastify.setErrorHandler(async (error: FastifyError, request: FastifyRequest, reply: FastifyReply) => {
    const requestId = request.id;
    const method = request.method;
    const url = request.url;
    const userAgent = request.headers['user-agent'];
    const ip = request.ip;

    // Log error with context
    logger.error({
      error: {
        message: error.message,
        stack: error.stack,
        code: error.code,
        statusCode: error.statusCode,
      },
      request: {
        id: requestId,
        method,
        url,
        userAgent,
        ip,
        userId: request.user?.id,
      },
    }, 'Request error occurred');

    // Handle different types of errors
    let statusCode = 500;
    let errorResponse: any = {
      success: false,
      error: 'Internal Server Error',
      requestId,
      timestamp: new Date().toISOString(),
    };

    // Validation errors (Zod)
    if (error instanceof ZodError) {
      statusCode = 400;
      errorResponse = {
        success: false,
        error: 'Validation Error',
        details: error.errors.map(err => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        })),
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // Fastify validation errors
    else if (error.validation) {
      statusCode = 400;
      errorResponse = {
        success: false,
        error: 'Validation Error',
        details: error.validation,
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // JWT errors
    else if (error.code === 'FST_JWT_AUTHORIZATION_TOKEN_EXPIRED') {
      statusCode = 401;
      errorResponse = {
        success: false,
        error: 'Token Expired',
        message: 'Your session has expired. Please log in again.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    else if (error.code?.startsWith('FST_JWT')) {
      statusCode = 401;
      errorResponse = {
        success: false,
        error: 'Authentication Error',
        message: 'Invalid or missing authentication token.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // Rate limiting errors
    else if (error.code === 'FST_TOO_MANY_REQUESTS') {
      statusCode = 429;
      errorResponse = {
        success: false,
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // Custom application errors
    else if (error.statusCode) {
      statusCode = error.statusCode;
      errorResponse = {
        success: false,
        error: error.message || 'Application Error',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // Database errors
    else if (error.message.includes('MongoError') || error.message.includes('MongoDB')) {
      statusCode = 503;
      errorResponse = {
        success: false,
        error: 'Database Error',
        message: 'Database temporarily unavailable. Please try again later.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // AI service errors
    else if (error.message.includes('OpenAI') || error.message.includes('Anthropic')) {
      statusCode = 503;
      errorResponse = {
        success: false,
        error: 'AI Service Error',
        message: 'AI service temporarily unavailable. Please try again later.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }
    // Network/timeout errors
    else if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      statusCode = 503;
      errorResponse = {
        success: false,
        error: 'Service Unavailable',
        message: 'External service temporarily unavailable. Please try again later.',
        requestId,
        timestamp: new Date().toISOString(),
      };
    }

    // Add stack trace in development
    if (config.nodeEnv === 'development') {
      errorResponse.stack = error.stack;
      errorResponse.details = errorResponse.details || error.message;
    }

    // Send error response
    reply.code(statusCode).send(errorResponse);
  });

  // Not found handler
  fastify.setNotFoundHandler(async (request: FastifyRequest, reply: FastifyReply) => {
    logger.warn({
      request: {
        method: request.method,
        url: request.url,
        ip: request.ip,
        userAgent: request.headers['user-agent'],
      },
    }, 'Route not found');

    reply.code(404).send({
      success: false,
      error: 'Not Found',
      message: `Route ${request.method} ${request.url} not found`,
      requestId: request.id,
      timestamp: new Date().toISOString(),
    });
  });
};

// Custom error classes
export class ValidationError extends Error {
  statusCode = 400;
  constructor(message: string, public details?: any) {
    super(message);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends Error {
  statusCode = 401;
  constructor(message: string = 'Authentication required') {
    super(message);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends Error {
  statusCode = 403;
  constructor(message: string = 'Access denied') {
    super(message);
    this.name = 'AuthorizationError';
  }
}

export class NotFoundError extends Error {
  statusCode = 404;
  constructor(message: string = 'Resource not found') {
    super(message);
    this.name = 'NotFoundError';
  }
}

export class ConflictError extends Error {
  statusCode = 409;
  constructor(message: string = 'Resource conflict') {
    super(message);
    this.name = 'ConflictError';
  }
}

export class RateLimitError extends Error {
  statusCode = 429;
  constructor(message: string = 'Rate limit exceeded') {
    super(message);
    this.name = 'RateLimitError';
  }
}

export class ServiceUnavailableError extends Error {
  statusCode = 503;
  constructor(message: string = 'Service temporarily unavailable') {
    super(message);
    this.name = 'ServiceUnavailableError';
  }
}