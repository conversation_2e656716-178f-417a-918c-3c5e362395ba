---
Description: Reads the API matrix and automatically updates the API landscape Mermaid diagram.
---

# Workflow: Update API Landscape Diagram

**Objective:** To ensure the `docs/diagrams/api_landscape.mmd` accurately reflects the APIs registered in `docs/matrices/api_matrix.md` and their key relationships.

**Trigger:**

* Manual execution.
* Can be triggered automatically after an update to `docs/matrices/api_matrix.md` (e.g., via a file watcher or as a post-commit hook, or as a step in `1_component_lifecycle/1_define/api_definition.md`).

**Responsible Command Office (Lead):** CTO (for maintaining the integrity of the landscape diagram).

**Key ESTRATIX Components Involved:**

* `docs/matrices/api_matrix.md` (Input)
* `docs/diagrams/api_landscape.mmd` (Output)

## Steps

1. **Read API Matrix Data**
   * **Action:** Parse the `docs/matrices/api_matrix.md` file.
   * **Tooling:** Use a script (e.g., Python with a Markdown parsing library) or a specialized agent capable of table data extraction.
   * **Input:** `docs/matrices/api_matrix.md`.
   * **Output:** Structured data representing the API matrix (e.g., a list of dictionaries, where each dictionary is a row).

2. **Identify APIs and Key Relationships**
   * **Action:** From the structured data, extract relevant information for each API:
     * API ID
     * API Name
     * Responsible Command Office
     * Status
     * Related Services (from `Related Services` column)
     * Related Flows (from `Related Flows` column)
   * **Guidance:** Filter out APIs with statuses like 'Deprecated' or 'Archived' if desired for the main landscape view, or represent them differently.
   * **Output:** Lists of APIs and their identified relationships.

3. **Generate Mermaid Diagram Syntax**
   * **Action:** Construct the Mermaid `graph TD` syntax based on the extracted APIs and relationships.
   * **Logic:**
     * Represent each API as a node (e.g., `API_ID["API Name (Office)"]`).
     * Represent related services/flows as nodes if they are to be visualized.
     * Create links between APIs and their related services/flows (e.g., `API_ID --> Service_ID`).
     * Group APIs by Responsible Command Office or by functional clusters if meaningful.
     * Apply styling using `classDef` for different component types (API, Service, Flow).
   * **Example Snippet (Conceptual)**

     ```mermaid
     graph TD
         subgraph "CTO APIs"
             direction LR
             CTO_API001["ExampleInternalAuthAPI (CTO)"]
             CTO_API002["DataProcessingServiceAPI (CTO)"]
         end

         subgraph "CPO APIs"
             direction LR
             CPO_API001["CustomerManagementAPI (CPO)"]
         end

         subgraph "Services"
             direction LR
             CSOL_S001["Authentication Service (CSOL)"]
             CPO_S005["Customer Data Platform (CPO)"]
         end

         CTO_API001 --> CSOL_S001;
         CPO_API001 --> CPO_S005;
         CTO_API002 -.-> CPO_API001; // Optional: API to API interaction

     classDef api fill:#lightgreen,stroke:#333,stroke-width:2px;
     classDef service fill:#lightblue,stroke:#333,stroke-width:2px;
     class CTO_API001,CTO_API002,CPO_API001 api;
     class CSOL_S001,CPO_S005 service;
     ```

   * **Output:** A string containing the complete Mermaid syntax for the diagram.

4. **Write to API Landscape Diagram File**
   * **Action:** Overwrite the content of `docs/diagrams/api_landscape.mmd` with the newly generated Mermaid syntax.
   * **Tooling:** File system operations.
   * **Input:** Generated Mermaid syntax string.
   * **Output:** Updated `docs/diagrams/api_landscape.mmd`.

5. **Commit Changes (If in a Git Repository)**
   * **Action (Optional):** If this workflow is part of an automated CI/CD pipeline or script, commit the changes to the `api_landscape.mmd` file.
   * **Tooling:** Git commands.
   * **Output:** Committed changes to version control.

## Considerations & Enhancements

* **Complexity Management:** For a large number of APIs, the diagram could become cluttered. Consider strategies like:
  * Generating multiple focused diagrams (e.g., per Command Office, per major service area).
  * Using subgraphs extensively.
  * Providing different levels of detail (e.g., a high-level overview vs. detailed interaction diagrams).
* **Relationship Types:** The `Notes` or dedicated columns in the `api_matrix.md` could define different types of relationships (e.g., 'consumes', 'provides_data_to', 'depends_on') which can be reflected in link styles or labels in Mermaid.
* **Automated Execution:** Implement this workflow as a script (Python, Node.js, etc.) that can be run manually or automatically.
* **Error Handling:** Include error handling in the script (e.g., if `api_matrix.md` is malformed).
