# ESTRATIX Stakeholder Register

## Document Control
*   **Document Title:** Stakeholder Register
*   **Register Template Version:** `[e.g., 1.0 - Version of this template]`
*   **Security Classification:** `[e.g., ESTRATIX Internal, Client Confidential]`

## 1. Project Information
*   **Project Name:** `[Full Project Name]`
*   **Project ID:** `[ESTRATIX_Project_ID]`
*   **Version:** `[e.g., 1.0]`
*   **Date Prepared:** `[YYYY-MM-DD]`
*   **Last Updated:** `[YYYY-MM-DD]`
*   **Prepared By:** `[Name/Agent ID (e.g., CPO_AXXX_ProjectManager)]`

## 2. Stakeholder Register Log

| Stakeholder ID (`[ProjID_SHXXX]`) | Name | Official Title/Role | Organization | Category/Type | Role in this Project | Contact Email | Contact Phone | Preferred Communication Method | Key Expectations/Interests in Project | Interest Level (`Ref: SEP`) | Influence/Power (`Ref: SEP`) | Impact of Project on Stakeholder | Attitude towards Project (`Ref: SEP`) | Current Engagement Level (`Ref: SEP`) | Desired Engagement Level (`Ref: SEP`) | Engagement Strategy/Actions | Key Messages | Communication Frequency | Primary ESTRATIX Contact/Liaison (`Name/Agent ID`) | Date Added (`YYYY-MM-DD`) | Date Last Reviewed (`YYYY-MM-DD`) | Notes/Comments |
| :-------------------------------- | :--- | :------------------ | :----------- | :------------ | :------------------- | :------------ | :------------ | :--------------------------- | :------------------------------------ | :--------------------------- | :---------------------------- | :------------------------------- | :------------------------------------- | :------------------------------------- | :------------------------------------- | :-------------------------- | :----------- | :----------------------- | :------------------------------------------------- | :------------------------ | :-------------------------------- | :------------- |
| `[ProjID_SH001]`                  | `[Full Name - Consider key ESTRATIX Agent System Owners if their systems are critical stakeholders, e.g., 'Owner of CIO_A001_KnowledgeManagerSystem']` | `[e.g., CEO, Head of Department, Agent System Owner]` | `[Company/Department Name or 'ESTRATIX Agent Command - CIO']` | `[e.g., Client Core, Vendor, Internal, ESTRATIX Agent System Representative]` | `[e.g., Project Sponsor, SME, End User, Key System Interface]` | `[<EMAIL>]` | `[Phone Number]` | `[e.g., Email, Weekly Meeting, Slack, System Alerts]` | `[What they want from/care about in the project. For agent systems: e.g., 'Reliable data inputs', 'Clear operational parameters', 'Minimal disruption to agent tasks'.]` | `[High/Med/Low or 1-5 - see SEP]`      | `[High/Med/Low or 1-5 - see SEP]`       | `[e.g., High Positive, Neutral, Low Negative]` | `[e.g., Champion, Supporter, Neutral, Blocker, Dependent System]` | `[e.g., Unaware, Resistant, Neutral, Supportive, Leading - see SEP]` | `[e.g., Supportive, Leading - see SEP]`          | `[Specific actions to engage this stakeholder. For agent systems: e.g., 'Regular updates on API changes', 'Involve in UAT for system integrations'.]` | `[Tailored messages]` | `[e.g., Daily, Weekly, Ad-hoc, Event-driven]` | `[e.g., CPO_AXXX_ProjectManager, or specific technical liaison for agent systems]`                  | `[YYYY-MM-DD]`            | `[YYYY-MM-DD]`                    |                |
| `[ProjID_SH002]`                  |      |                     |              |               |                      |               |               |                              |                                       |                              |                               |                                  |                                        |                                        |                                        |                             |              |                          |                                                    |                           |                                   |                |
| `...`                             | `...`  | `...`               | `...`        | `...`         | `...`                | `...`         | `...`         | `...`                        | `...`                                 | `...`                        | `...`                         | `...`                            | `...`                                  | `...`                                  | `...`                                  | `...`                       | `...`        | `...`                    | `...`                                              | `...`                     | `...`                             | `...`          |

---
***SEP (Stakeholder Engagement Plan):** All references to scales (Interest, Influence, Attitude, Engagement Levels) and analysis models are defined in the project's **Stakeholder Engagement Plan**. This plan is typically based on `../01_ProjectPlanning/Stakeholder_Engagement_Plan_Template.md` (or its project-specific instantiation, often part of the overall Project Plan).

**Notes & Definitions:**
*   **Stakeholder ID:** Unique identifier for the stakeholder within the project context.
*   **Category/Type:** Classification of the stakeholder (e.g., Internal, External, Client, Vendor, Regulatory Body, ESTRATIX Agent System Representative - if a human represents a critical agent system's interests).
*   **Role in this Project:** Specific function or contribution to this project (e.g., Sponsor, SME, End User, Approver, Key System Interface Provider/Consumer).
*   **Key Expectations/Interests:** What the stakeholder hopes to gain or is concerned about regarding the project. For representatives of ESTRATIX Agent Systems, this could include data quality, API stability, processing load, changes to operational parameters, etc.
*   **Interest Level:** Degree to which the stakeholder is affected by or interested in the project outcomes.
*   **Influence/Power:** Stakeholder's ability to impact the project (positively or negatively).
*   **Impact of Project on Stakeholder:** How the project's outcomes will affect the stakeholder.
*   **Attitude towards Project:** Stakeholder's current disposition towards the project.
*   **Current/Desired Engagement Level:** Assessment of current and target levels of stakeholder involvement and support.
*   **Engagement Strategy/Actions:** Planned activities to achieve the desired engagement level.
*   **Primary ESTRATIX Contact/Liaison:** The main ESTRATIX team member (human or designated communication agent like COM_AXXX_StakeholderCommsAgent if applicable for routine updates) responsible for communication and engagement with this stakeholder.

## 3. General Guidance for Use
*   This Stakeholder Register is a living document, initiated during project planning and updated throughout the project lifecycle as new stakeholders are identified or existing stakeholder information changes.
*   Stakeholder identification should be a continuous process involving the project team, sponsors, and potentially ESTRATIX discovery agents (e.g., `CPO_AXXX_StakeholderDiscoveryAgent`).
*   Analyze each stakeholder's interest, influence, expectations, and attitude towards the project to develop appropriate engagement strategies. Refer to the Stakeholder Engagement Plan (SEP) for methodologies (e.g., Power/Interest Grid).
*   Consider the interdependencies between stakeholders.
*   For ESTRATIX projects with significant agentic components, identify human representatives or owners of critical ESTRATIX Agent Systems if their operations are directly impacted or they provide key services/data to the project. Their "expectations" might relate to system performance, data integrity, or API contracts.
*   Engagement strategies should be tailored to each stakeholder or group and regularly reviewed for effectiveness.

---
*This Stakeholder Register is a key ESTRATIX document for managing relationships and ensuring project alignment with stakeholder needs and expectations. It is maintained as per the project's Stakeholder Engagement Plan and supports effective project communication and governance.*
