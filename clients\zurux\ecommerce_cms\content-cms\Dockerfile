# Dockerfile for Payload CMS

# Use an official Node runtime as a parent image
FROM node:18-alpine AS base

# Set the working directory
WORKDIR /app

# --- Dependencies ---
FROM base AS deps
# Copy package.json and lock file
COPY package.json yarn.lock* package-lock.json* ./
# Install dependencies
RUN yarn install --frozen-lockfile || npm install

# --- Builder ---
FROM base AS builder
# Copy dependencies from the 'deps' stage
COPY --from=deps /app/node_modules ./node_modules/
# Copy the rest of the application code
COPY . .
# Build the Payload project
RUN yarn build || npm run build

# --- Runner ---
FROM base AS runner
WORKDIR /app

# Set environment variables (can be overridden)
ENV NODE_ENV production
ENV PAYLOAD_SECRET your-payload-secret
ENV MONGODB_URI ****************************:port/db
# ENV DATABASE_URI postgres://user:password@host:port/db # If using Postgres adapter
# ENV PAYLOAD_CONFIG_PATH src/payload.config.ts # Adjust if needed

# Copy necessary artifacts from the builder stage
COPY --from=builder /app/build ./build/
COPY --from=builder /app/node_modules ./node_modules/
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/payload.config.js ./payload.config.js
# Or payload.config.ts if using TypeScript config
# Copy uploads directory if needed, or use volume
# COPY --from=builder /app/uploads ./uploads/

# Expose the port Payload runs on (default 3000)
EXPOSE 3000

# Define the command to run the application
# Use 'npm run serve' or 'yarn serve' (or equivalent) based on your setup
CMD ["npm", "run", "serve"]
# Or: CMD ["yarn", "serve"]
