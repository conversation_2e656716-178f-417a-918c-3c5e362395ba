---
description: Guides the generation of a framework-specific Crew implementation for a pre-defined ESTRATIX Process, producing a runnable Crew file in `src/infrastructure/frameworks/[framework]/crews/[officer_acronym]/`.
---

# ESTRATIX Workflow: Generate Crew (Process Implementation)

**Objective:** To translate a defined ESTRATIX Process into a runnable, framework-specific Crew. This workflow generates the main crew file, orchestrates the creation of its constituent agent and task YAML files, and places them in the correct directory structure according to the project's naming conventions.

**Agent Persona:** `AGENT_MasterBuilder_Expert`

**Governing Rules**:

- `docs/standards/naming_conventions.md`
- `.windsurf/rules/project_rules.md`

## 1. Prerequisite

- A `Process_ID` for an existing process that is in `Defined` or `Planning` status in the `process_matrix.md`.

## Workflow Steps

1. **Identify Target Process & Retrieve Metadata**
    - **Action**: Obtain the `Process_ID` from the user or orchestrating workflow.
    - **Action**: Read the `docs/models/process_matrix.md` to find the row for the given `Process_ID`.
    - **Tool**: `grep_search`
    - **Goal**: Extract the `Process_Name`, `Owner_Office_Code`, `Parent_Flow_ID`, and the path to the definition file. This makes the workflow self-sufficient and reduces manual input.

2. **Retrieve Process Definition Details**
    - **Action**: Read the process definition markdown file (using the path from the matrix) to get the detailed lists of required `Agents` and `Tasks`.
    - **Tool**: `view_line_range`
    - **Goal**: Extract the specific agent and task IDs required to build the crew.

3. **Generate Agent & Task YAML Definitions**
    - **Action**: For each agent and task identified in the process definition, trigger the respective generation workflow. This ensures the component YAML files are created before the crew that uses them.
    - **Workflows**:
      - `/task_generation`
      - `/agent_generation`
    - **Input for Workflows**: The `Process_ID` is passed as the parent `crew_id` to the generation workflows to ensure correct naming (`p019_a002_...`, `p019_t001_...`).

4. **Scaffold Crew Implementation File**
    - **Action**: Create the main boilerplate file for the Crew implementation.
    - **Tool**: `write_to_file`
    - **Path**: `src/infrastructure/frameworks/[Agentic_Framework]/crews/[Command_Office]/[Flow_ID]_[Process_ID]_[Crew_Name_snake_case].py`
    - **Content**: A Python script that imports `Crew` from `crewai`, loads the agents and tasks from their respective YAML files, and assembles them into a runnable Crew.

5. **Update Process Matrix**
    - **Action**: Update the process matrix to link to the new crew implementation file.
    - **Tool**: `replace_file_content` or a dedicated matrix update tool.
    - **Guidance**: Change the status to `Implemented` and add the correct path to the `Implementation Path` column.

6. **Confirmation**
    - **Action**: Confirm to the user that the Crew and its components have been successfully generated, providing a link to the new crew file.

## Guidance for Use

- This workflow is designed to be executed by a specialized "Builder" agent with expertise in the target framework.
- **Hexagonal Architecture**: The agent must distinguish between pure business logic and framework-specific implementation. Reusable domain logic and data models should be placed in `src/domain/` and imported by the framework-specific code in `src/infrastructure/frameworks/[framework]/`.
- The generated `crew.py` should contain the detailed, step-by-step implementation of the business process.
