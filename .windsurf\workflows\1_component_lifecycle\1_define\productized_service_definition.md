---
description: "Guides the definition of a new ESTRATIX Productized Service, from conceptualization to formal registration in the productized_services_matrix.md."
---

# Workflow: Define Productized Service

## Objective

To guide a user or an ESTRATIX agent (e.g., `AGENT_ProductManager_Lead`) through the standardized process of defining a new external-facing, client-oriented Productized Service. This workflow ensures that all necessary commercial, operational, and strategic aspects are considered and documented, leading to a comprehensive definition file and proper registration within the ESTRATIX ecosystem.

## Prerequisites

* A clear concept for a new productized service.
* Understanding of the target market and client needs.
* Access to the ESTRATIX project structure and the `productized_services_matrix.md`.

## Steps

1. **Initiation**
   * The user or an assigned agent (e.g., `AGENT_ProductManager_Lead`) identifies the need to formalize a new productized service.

2. **Gather Core Information**
   * **Determine Service Name:** Define a clear, client-facing name for the service (e.g., "Agentic Website Generation").
   * **Assign Service ID:** Following the format `PROD_S[NNN]`, find the next available number from the `productized_services_matrix.md`.
   * **Identify Operational Area:** Determine which operational area the service belongs to by consulting `docs/matrices/operational_areas_matrix.md`. Note the `Operational_Area_ID`.

3. **Create Service Directory & Definition File**
   * Create a new directory for the service definition and related artifacts:
     `docs/productized_services/[Operational_Area_ID]/[Service_ID]_[ServiceName_PascalCase]/`
   * Copy the template `docs/templates/estratix_productized_service_definition_template.md` into the new directory.
   * Rename the copied template to:
     `[Service_ID]_[ServiceName_PascalCase]_Definition.md`

4. **Populate the Definition Document**
   * Open the newly created definition file.
   * Methodically fill out all sections of the template, including:
     * **Metadata:** Service ID, name, owner, status.
     * **Market Analysis:** Target customers, competitive landscape, USP.
     * **Scope & Deliverables:** What the client gets.
     * **SLAs & KPIs:** Formal commitments and internal metrics.
     * **Pricing & Commercial Model:** Tiers, billing model.
     * **Go-to-Market Strategy:** Launch, marketing, and sales plans.
     * **Client Onboarding & Support:** Processes and resources.
     * **Constituent ESTRATIX Components:** Link to the internal processes/flows that power the service.
     * **Technology Stack & Dependencies.**
     * **Launch Readiness Criteria.**

5. **Register the New Service**
   * Open the `docs/matrices/productized_services_matrix.md` file.
   * Add a new row to the table with the details of the newly defined service. Ensure the path points to the new definition file.
   * **Example Row:**

     ```markdown
     | PROD_S001 | Agentic Website Generation | The CPO's Office | Active | [Link to Definition](./docs/productized_services/OA01/PROD_S001_AgenticWebsiteGeneration/PROD_S001_AgenticWebsiteGeneration_Definition.md) | Generates a basic website for a client based on a prompt. |
     ```

6. **Review and Finalize**
   * Submit the completed definition document for review by the designated Product Owner or the Chief Product Officer (CPO).
   * Once approved, update the status in the definition file and the matrix to "Active" (or as appropriate).

## Outputs

* A new productized service directory containing a comprehensive definition document.
* An updated `productized_services_matrix.md` reflecting the newly registered service.

## Agentic Implementation Notes

* This workflow is designed to be executed by a `AGENT_ProductManager_Lead`.
* The agent should be capable of file system operations (creating directories, copying files), text editing (populating templates), and interacting with the user for required inputs (Service Name, details for each section).
* Future enhancements could involve the agent partially auto-populating sections based on high-level user prompts and knowledge of existing ESTRATIX components.