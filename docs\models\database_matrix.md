# ESTRATIX Database Matrix

## 1. Overview

This matrix catalogs all database instances used across the ESTRATIX project, including relational, NoSQL, and vector databases. It provides a central reference for connection details, configurations, and ownership.

## 2. Database Instance Registry

| DB ID      | DB Name                     | Type          | Environment | Provider | Status   | Host / Endpoint      | Definition Link |
| :--------- | :-------------------------- | :------------ | :---------- | :------- | :------- | :------------------- | :-------------- |
| DB-PGS-001 | estratix-prod-postgres      | PostgreSQL    | Production  | AWS RDS  | `Active` | prod.db.estratix.co  | [DB-PGS-001_Definition.md](./definitions/DB-PGS-001.md) |
| DB-MDB-001 | estratix-prod-mongo         | MongoDB Atlas | Production  | MongoDB  | `Active` | cluster0.mongodb.net | [DB-MDB-001_Definition.md](./definitions/DB-MDB-001.md) |
| DB-QDR-001 | estratix-knowledge-base     | Qdrant        | Production  | Qdrant   | `Active` | qdrant.estratix.co   | [DB-QDR-001_Definition.md](./definitions/DB-QDR-001.md) |
| DB-RDS-001 | estratix-staging-cache      | Redis         | Staging     | AWS EC   | `Active` | staging.cache.estratix.co | [DB-RDS-001_Definition.md](./definitions/DB-RDS-001.md) |

## 3. Database Types

- **Relational (SQL)**: For structured data requiring ACID compliance (e.g., PostgreSQL, MySQL).
- **NoSQL**: For flexible, scalable data models (e.g., MongoDB, DynamoDB).
- **Vector**: For high-dimensional vector data used in AI/ML applications (e.g., Qdrant, Milvus).
- **In-Memory**: For high-speed caching and session storage (e.g., Redis, Memcached).

## 4. Integration Points

- **Service Matrix**: Services define their data persistence requirements, linking to specific databases in this matrix.
- **Data Model Matrix**: Defines the schemas (e.g., Pydantic models) for the data stored in these databases.
- **VPC Server Matrix**: Tracks the infrastructure hosting self-managed database instances.

---

**Last Updated**: YYYY-MM-DD  
**Next Review**: YYYY-MM-DD  
**Owner**: CIO (Chief Information Officer)
