---
description: Guides the design and definition of ESTRATIX Flows that orchestrate multiple ESTRATIX Processes to achieve specific business objectives.
---

# Workflow: Define ESTRATIX Flow

## Objective

To produce a comprehensive and standardized definition document for a new ESTRATIX Flow, ensuring it is atomically registered in the `flow_matrix.md` and stored in the correct directory: `docs/flows/[Owner_Office_Code]/`.

## Architectural Note

An ESTRATIX Flow represents a high-level business or operational capability. It achieves its objective not by containing complex logic itself, but by **orchestrating one or more ESTRATIX Processes**. Each Process encapsulates a specific, cohesive set of tasks, agents, and tools. This separation ensures that Flows remain clean, readable, and focused on orchestration, while the detailed implementation is handled by modular, reusable Processes.

## Agent Persona

`AGENT_Architect_Expert`

## Steps

1. **Initiation & Information Gathering**
   - **Action**: An agent or user identifies the need for a new flow to orchestrate existing processes.
   - **Input**: `Flow_Name` (e.g., "End-to-End Service Provisioning"), `Owner_Office_Code` (e.g., "CPOO"), a brief `Description`, and a list of `Orchestrated_Process_IDs`.
   - **Verification**: Check `docs/models/flow_matrix.md` to ensure a similar flow does not already exist. Check that all `Orchestrated_Process_IDs` exist in `docs/models/process_matrix.md`.

2. **Register Flow & Reserve ID (Atomic Operation)**
   - **Action (Automated)**: An agent programmatically reserves a unique ID and registers the flow.
   - **Logic**:
     1. Read `docs/models/flow_matrix.md`.
     2. Determine the next available `Flow_ID` (e.g., if the last ID is `f001`, the new one is `f002`).
     3. Add a new row to the matrix with the new `Flow_ID`, `Flow_Name`, `Owner_Office_Code`, a placeholder `Description`, and set the `Status` to `Pending Definition`.
     4. Save the updated `docs/models/flow_matrix.md`.
   - **Output**: The newly reserved `Flow_ID` (e.g., `f002`).

3. **Create Flow Definition File from Template**
   - **Action (Automated)**:
     1. Create the target directory if it doesn't exist: `docs/flows/[Owner_Office_Code]/`.
     2. Copy the template `docs/templates/estratix_flow_definition_template.md` to the target file: `docs/flows/[Owner_Office_Code]/[Flow_ID]_[Flow_Name_PascalCase]_Definition.md`.
     3. Replace placeholders like `[Flow_ID]` and `[FlowName]` in the new file.
   - **Tooling**: File system operations, text replacement.

4. **Populate Flow Definition**
   - **Action (User/Agent)**: Open the newly created definition file and populate all sections, especially the `Orchestrated Processes` and the Mermaid diagram illustrating the sequence.

5. **Review and Finalize**
   - **Action**: Review the populated definition for clarity, completeness, and logical correctness.
   - **Input**: Feedback from the `Owner_Office_Code` Command Officer.
   - **Output**: A finalized definition document.

6. **Update Matrix with Definition Link**
   - **Action (Automated)**: Upon finalization, an agent updates the flow's entry in the matrix.
   - **Logic**:
     1. Open `docs/models/flow_matrix.md`.
     2. Locate the row corresponding to the `Flow_ID`.
     3. Update the `Description` with the final summary from the definition file.
     4. Set the `Definition Link` to point to the new definition file.
     5. Update the `Status` from `Pending Definition` to `Defined`.
   - **Tooling**: Markdown table manipulation script.

7. **Confirmation**
   - **Action**: Confirm to the user that the flow has been successfully defined and registered.
   - **Output**: Provide a direct link to the new definition file and the updated flow matrix.
