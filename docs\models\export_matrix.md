# ESTRATIX Content Export Format Matrix

---

## 1. Overview

This matrix defines the standard content export formats used across the ESTRATIX ecosystem. It provides a registry of supported formats, their specifications, and the tools or agents responsible for generating them. This ensures that all generated content adheres to consistent, predictable structures.

## 2. Export Format Matrix

| Format ID | Format Name | Description | File Extension | MIME Type | Generating Tool/Agent | Notes |
|---|---|---|---|---|---|---|
| `EXP-MD-001` | Standard Markdown | Basic markdown file for documentation and articles. | `.md` | `text/markdown` | `AGENT-WRITER-MD` | GFM (GitHub Flavored Markdown) compliant. |
| `EXP-PDF-001` | Standard PDF | Portable Document Format for reports and whitepapers. | `.pdf` | `application/pdf` | `AGENT-EXPORTER-PDF` | Uses a standard ESTRATIX template for branding. |
| `EXP-HTML-001` | Basic HTML | A standard HTML5 document for web content. | `.html` | `text/html` | `AGENT-WRITER-HTML` | |
| `EXP-JSON-001` | Standard JSON | Standard JSON format for data interchange. | `.json` | `application/json` | `AGENT-SERIALIZER-JSON` | |
| `EXP-IMG-001` | PNG Image | Lossless image format with transparency support. | `.png` | `image/png` | `AGENT-GENERATOR-IMG` | Use for high-quality graphics, posts, ads. |
| `EXP-IMG-002` | JPEG Image | Lossy image format for photographic content. | `.jpg`, `.jpeg` | `image/jpeg` | `AGENT-GENERATOR-IMG` | Use for web posts, ads where file size is a concern. |
| `EXP-IMG-003` | SVG Image | Scalable Vector Graphics for logos and icons. | `.svg` | `image/svg+xml` | `AGENT-GENERATOR-IMG` | Ideal for client branding elements. |
| `EXP-IMG-004` | WebP Image | Modern image format for superior compression. | `.webp` | `image/webp` | `AGENT-GENERATOR-IMG` | Use for web content like banners and carousels to improve performance. |
| `EXP-VID-001` | MP4 Video | Widely supported video format for web and mobile. | `.mp4` | `video/mp4` | `AGENT-GENERATOR-VID` | For short-form (reels, ads) and long-form content. |
| `EXP-VID-002` | WebM Video | Open video format for the web, sponsored by Google. | `.webm` | `video/webm` | `AGENT-GENERATOR-VID` | Optimized for web streaming. |
| `EXP-3D-001` | Blender Scene/Object | Native file format for Blender 3D projects. | `.blend` | `application/x-blender` | `AGENT-GENERATOR-3D` | For creating and storing 3D scenes and objects. |
| `EXP-3D-002` | GLB/glTF | 3D file format for efficient transmission and loading. | `.glb` | `model/gltf-binary` | `AGENT-EXPORTER-3D` | Standard for web-based 3D experiences. |
| `EXP-3D-003` | Scene/Object Animation | Rendered animation from a 3D scene. | `.mp4`, `.gif` | `video/mp4`, `image/gif` | `AGENT-RENDERER-3D` | For exporting final animations (e.g., product showcases). |

## 3. Guidance for Use

- **Format Definition**: Before a new content format can be generated, it must be defined and registered in this matrix.
- **Tool/Agent Assignment**: Each format must be associated with a specific tool or agent responsible for its generation.
- **Content Lifecycle**: The `content_matrix.md` references these formats to specify the target output for each content piece.
