# clients/apps/zurux_ecommerce_cms/bootstrap.py

import json
import os
import datetime
import yaml # Or use json for output
from pathlib import Path
from typing import Dict, Any, List
import sys
from typing import Optional
import subprocess # Added import

# --- Add project root to sys.path --- 
# This allows importing from the 'domain' package assuming this script
# is executed from within the project structure or the root is discoverable.
PROJECT_ROOT = Path(__file__).resolve().parents[4] # Adjust depth if structure changes
if str(PROJECT_ROOT) not in sys.path:
    sys.path.append(str(PROJECT_ROOT))

# --- Import Domain Models ---
try:
    from domain.project_management.models import (
        ProjectPlan,
        WorkBreakdownStructure,
        WBSElement,
        ProjectTask,
        TaskStatus
    )
except ImportError as e:
    print(f"Error importing domain models: {e}")
    print(f"Ensure the project root ({PROJECT_ROOT}) is correct and contains the 'domain' package.")
    # Provide default dummy classes if import fails to allow script structure to run
    class BaseModel:
        def model_dump(self, mode=None): return {}
    class ProjectPlan(BaseModel): pass
    class WorkBreakdownStructure(BaseModel): pass
    class WBSElement(BaseModel): pass
    class ProjectTask(BaseModel): pass
    class TaskStatus:
        PENDING = "pending"

# Assuming domain models are accessible (adjust import paths as needed)
# Need to make the main project a package or adjust PYTHONPATH
# For now, assume they can be imported relative to a future execution context
# from domain.project_management.models import ProjectPlan, WorkBreakdownStructure, WBSElement, ProjectTask, TaskStatus

# --- Configuration ---
CLIENT_APP_DIR = Path(__file__).parent
PROJECT_BRIEF_PATH = CLIENT_APP_DIR / "project_brief.sample.json" # Use the specific brief for the client
PROJECT_PLAN_OUTPUT_PATH = CLIENT_APP_DIR / "project_plan.yaml" # Where to save the generated plan
TASK_LIST_OUTPUT_PATH = CLIENT_APP_DIR / "tasks.json" # Where to save the structured tasks
PROJECT_TEMPLATE_DIR = Path("../../../../docs/project_planning_template") # Relative path to template

# --- Helper Functions ---

def load_project_brief(path: Path) -> Dict[str, Any]:
    """Loads the project brief JSON file."""
    print(f"Loading project brief from: {path}")
    with open(path, 'r') as f:
        brief = json.load(f)
    print("Project brief loaded successfully.")
    return brief

def load_wbs_template() -> Dict[str, Any]:
    """
    Loads the WBS structure/guidance from the template directory.
    Placeholder: This needs implementation based on how the template is structured.
    It could be a YAML file, a directory structure, or Python logic.
    Returns a dictionary representing the generic WBS structure.
    """
    print(f"Loading WBS template structure from: {PROJECT_TEMPLATE_DIR}")
    # Example: Load from a YAML file within the template dir
    template_wbs_path = PROJECT_TEMPLATE_DIR / "wbs_structure.yaml"
    if template_wbs_path.exists():
         with open(template_wbs_path, 'r') as f:
             return yaml.safe_load(f)
    else:
        print(f"Warning: WBS template file not found at {template_wbs_path}. Using basic structure.")
        # Return a default basic structure if template is missing
        return {
            "root_elements": [
                {"code": "1", "title": "Project Setup & Configuration", "children": [
                    {"code": "1.1", "title": "Backend Setup (Medusa)"},
                    {"code": "1.2", "title": "CMS Setup (Payload)"},
                    {"code": "1.3", "title": "Frontend Setup (React/Vue/etc.)"},
                    {"code": "1.4", "title": "Infrastructure & Deployment Setup"},
                ]},
                {"code": "2", "title": "Core Feature Development", "children": []}, # To be populated based on brief
                {"code": "3", "title": "Testing & QA"},
                {"code": "4", "title": "Deployment"},
                {"code": "5", "title": "Documentation & Handover"},
            ]
        }


def _build_wbs_elements_recursive(template_elements: List[Dict[str, Any]], brief: Dict[str, Any]) -> List[WBSElement]:
    """Recursive helper to build WBSElement list from template data."""
    elements = []
    for elem_data in template_elements:
        # TODO: Add logic here to customize elements based on the brief
        # Example: Skip certain elements, modify titles, add new children based on features
        # if elem_data.get('code') == '1.3' and brief.get('tech_stack', {}).get('frontend_framework') == 'Vue':
        #     elem_data['title'] = "Frontend Setup (Vue)"

        children = []
        if 'children' in elem_data and elem_data['children']:
            children = _build_wbs_elements_recursive(elem_data['children'], brief)

        try:
            element = WBSElement(
                code=elem_data.get('code', ''),
                title=elem_data.get('title', 'Untitled Element'),
                description=elem_data.get('description'),
                tasks=[], # Tasks will be generated in a separate step
                children=children
            )
            elements.append(element)
        except Exception as e:
            print(f"Error creating WBSElement for data {elem_data}: {e}")
            # Decide how to handle errors: skip element, use defaults, etc.

    return elements

def generate_wbs_from_template(template_wbs: Dict[str, Any], brief: Dict[str, Any]) -> Optional[WorkBreakdownStructure]:
    """
    Generates a specific Work Breakdown Structure based on the template and project brief.
    """
    print("Generating Work Breakdown Structure...")
    # This is where you would dynamically add/remove/modify WBS elements
    # For example, add specific feature nodes under "Core Feature Development"
    # Or tailor the "Frontend Setup" node based on brief.tech_stack.frontend_framework

    # NOTE: Direct instantiation might fail if template structure doesn't perfectly match models
    # A recursive function to build WBSElement objects from the dict is more robust
    try:
        if 'root_elements' not in template_wbs:
            print("Error: Template WBS missing 'root_elements' key.")
            return None

        root_elements = _build_wbs_elements_recursive(template_wbs['root_elements'], brief)
        wbs = WorkBreakdownStructure(root_elements=root_elements)

        print("WBS structure generated from template.")
        # TODO: Implement logic to customize WBS based on brief
        return wbs
    except Exception as e:
        print(f"Error generating WBS from template: {e}.")
        return None

def _create_tasks_recursive(wbs_element: WBSElement, brief: Dict[str, Any], task_list: List[ProjectTask]):
    """Recursive helper to generate tasks from WBS elements."""
    if not wbs_element.children: # It's a leaf node, create a task
        # Try to determine target directory based on title hints
        target_dir = "./" # Default
        title_lower = wbs_element.title.lower()
        if "backend" in title_lower or "medusa" in title_lower:
            target_dir = str(CLIENT_APP_DIR / 'backend')
        elif "cms" in title_lower or "payload" in title_lower:
            target_dir = str(CLIENT_APP_DIR / 'content-cms')
        elif "frontend" in title_lower:
            frontend_dir_name = f"frontend-{brief.get('tech_stack', {}).get('frontend_framework', 'react').lower()}"
            target_dir = str(CLIENT_APP_DIR / frontend_dir_name)
        elif "deployment" in title_lower or "infra" in title_lower:
             target_dir = str(CLIENT_APP_DIR) # Or maybe DevOps/IaC directory?

        # Basic acceptance criteria
        acceptance = [
            f"Functionality for '{wbs_element.title}' implemented.",
            "Code passes linting and basic tests.",
            "Relevant documentation updated (if applicable)."
        ]

        # Basic command hint
        command_hint = f"/add Implement '{wbs_element.title}' in '{target_dir}' based on WBS element {wbs_element.code} and project brief {PROJECT_BRIEF_PATH}. Review acceptance criteria."

        task = ProjectTask(
            wbs_code=wbs_element.code,
            title=f"Implement: {wbs_element.title}",
            description=wbs_element.description or f"Implement the work described by WBS element {wbs_element.code}: {wbs_element.title}. Refer to the project brief.",
            status=TaskStatus.PENDING,
            # assigned_to=... # Assign later
            dependencies=[], # TODO: Implement dependency logic if needed
            target_files=[target_dir], # Can be refined to specific files later
            context_references=[str(PROJECT_BRIEF_PATH)], # Add links to specific sections or docs
            acceptance_criteria=acceptance,
            aider_command_hint=command_hint,
            metadata={ 'wbs_description': wbs_element.description } # Example metadata
        )
        task_list.append(task)
    else:
        # Recursively call for children
        for child in wbs_element.children:
            _create_tasks_recursive(child, brief, task_list)

def generate_tasks_for_wbs(wbs: WorkBreakdownStructure, brief: Dict[str, Any]) -> List[ProjectTask]:
    """
    Generates a flat list of ProjectTask objects from the WBS.
    """
    print("Generating project tasks from WBS...")
    tasks = []
    if not wbs or not wbs.root_elements:
        print("WBS is empty or invalid. Cannot generate tasks.")
        return tasks

    for root_element in wbs.root_elements:
        _create_tasks_recursive(root_element, brief, tasks)

    print(f"Generated {len(tasks)} tasks from WBS.")
    # TODO: Add logic for task dependencies, refinement, and assignment
    return tasks


def generate_project_plan(brief: Dict[str, Any], wbs: WorkBreakdownStructure) -> Optional[ProjectPlan]:
    """Creates the overall ProjectPlan object."""
    print("Generating project plan...")
    if not wbs:
        print("Cannot generate plan without a WBS.")
        return None
    try:
        plan = ProjectPlan(
            project_brief_id=brief.get("project_id", "N/A"), # Assuming brief has an ID
            project_name=brief.get("project_info", {}).get("name", "Unnamed Project"),
            client_name=brief.get("client_info", {}).get("name", "Unknown Client"),
            wbs=wbs,
            start_date=datetime.date.today(),
            status="planning"
            # end_date=... # Estimate based on tasks?
            # project_manager_id=... # Assign later
        )
        print("Project plan generated.")
        return plan
    except Exception as e:
        print(f"Error creating ProjectPlan object: {e}")
        return None


def save_project_plan(plan: ProjectPlan, path: Path):
    """Saves the project plan to a YAML (or JSON) file."""
    if not plan:
        print("No project plan object to save.")
        return
    print(f"Saving project plan to: {path}")
    try:
        # Requires plan to be converted to dict first if using Pydantic models
        plan_dict = plan.model_dump(mode='json') # Use 'json' for better serialization support
        with open(path, 'w') as f:
            yaml.dump(plan_dict, f, indent=2, default_flow_style=False, sort_keys=False)
        print("Project plan saved successfully.")
    except Exception as e:
        print(f"Error saving project plan: {e}")


def save_tasks(tasks: List[ProjectTask], path: Path):
    """Saves the generated tasks to a file (e.g., JSON)."""
    if not tasks:
        print("No tasks to save.")
        return
    print(f"Saving tasks to: {path}")
    try:
        tasks_list = [task.model_dump(mode='json') for task in tasks]
        with open(path, 'w') as f:
            json.dump(tasks_list, f, indent=2)
        print("Tasks saved successfully.")
    except Exception as e:
        print(f"Error saving tasks: {e}")


def run_scaffolding_command(command: List[str], cwd: Path, description: str):
    """Runs a scaffolding command using subprocess."""
    print(f"--- Running: {description} ---")
    print(f"Command: {' '.join(command)}")
    print(f"Working Directory: {cwd}")
    try:
        # Ensure the directory exists
        cwd.mkdir(parents=True, exist_ok=True)
        result = subprocess.run(command, cwd=cwd, check=True, capture_output=True, text=True, shell=True) # Using shell=True for npx/npm on Windows, adjust if needed
        print(f"Success: {description}")
        print("Output:\n", result.stdout[-500:]) # Show last 500 chars of stdout
    except FileNotFoundError:
         print(f"Error: Command not found ({command[0]}). Is Node.js/npm/npx installed and in PATH?")
    except subprocess.CalledProcessError as e:
        print(f"Error executing {description}:")
        print(f"Return Code: {e.returncode}")
        print("Stderr:\n", e.stderr)
        print("Stdout:\n", e.stdout)
    except Exception as e:
         print(f"An unexpected error occurred during {description}: {e}")
    print("-" * (len(description) + 12))


# --- Main Execution ---
if __name__ == "__main__":
    print("--- Starting Project Bootstrap ---")

    # 1. Load Project Brief
    project_brief = load_project_brief(PROJECT_BRIEF_PATH)

    # 2. Load WBS Template/Structure
    wbs_template = load_wbs_template()

    # 3. Generate Specific WBS (Requires Pydantic Model Implementation)
    project_wbs = generate_wbs_from_template(wbs_template, project_brief)
    # project_wbs = None # Placeholder

    # 4. Generate Project Plan (Requires Pydantic Model Implementation)
    project_plan = generate_project_plan(project_brief, project_wbs)
    # project_plan = None # Placeholder

    # 5. Generate Tasks (Requires Pydantic Model Implementation)
    if project_wbs:
       project_tasks = generate_tasks_for_wbs(project_wbs, project_brief)
    else:
       project_tasks = []
    # project_tasks = [] # Placeholder

    # 6. Save Plan and Tasks (Requires Implementation)
    if project_plan:
       save_project_plan(project_plan, PROJECT_PLAN_OUTPUT_PATH)
    if project_tasks:
       save_tasks(project_tasks, TASK_LIST_OUTPUT_PATH)

    print("--- Plan and Tasks Generation Complete ---")

    # 7. Execute initial scaffolding commands
    print("--- Starting Application Scaffolding ---")
    tech_stack = project_brief.get("tech_stack", {})
    db_details = project_brief.get("database", {}) # Assuming db details might be needed

    # --- Scaffold Backend (MedusaJS) ---
    backend_dir = CLIENT_APP_DIR / "backend"
    # Basic command - might need database flags (--database_url etc.)
    # Consult MedusaJS CLI docs for specific options
    # Using `--seed` might require additional setup or flags depending on version
    medusa_command = [
        "npx", "create-medusa-app@latest", ".",
        # Example: "--db-type=postgres", f"--db-url={db_details.get('connection_string', '')}",
        # "--no-seed" # Use if seeding manually or not desired initially
    ]
    run_scaffolding_command(medusa_command, backend_dir, "MedusaJS Backend Setup")

    # --- Scaffold Content CMS (Payload CMS) ---
    cms_dir = CLIENT_APP_DIR / "content-cms"
    # Consult Payload CMS create command docs for options
    # Template might vary (e.g., 'blank', 'website', 'ecommerce')
    payload_command = [
        "npm", "create", "payload-app@latest", ".",
        "--name", project_brief.get("project_info", {}).get("name", "payload-cms").replace(" ", "-").lower(), # Project name slug
        "--template=blank", # Choose appropriate template
        # "--db=mongodb" or "--db=postgres" # Specify database
        # Might prompt interactively if options aren't provided
    ]
    run_scaffolding_command(payload_command, cms_dir, "Payload CMS Setup")

    # --- Scaffold Frontend ---
    frontend_framework = tech_stack.get("frontend_framework", "react").lower()
    frontend_dir_name = f"frontend-{frontend_framework}"
    frontend_dir = CLIENT_APP_DIR / frontend_dir_name
    frontend_command = []

    if frontend_framework == "react":
        frontend_command = ["npx", "create-react-app@latest", "."] # Scaffolds in current dir
    elif frontend_framework == "vue":
        frontend_command = ["npm", "create", "vue@latest", ".", "--", "--default"] # Scaffolds in current dir, accept defaults
    elif frontend_framework == "svelte":
         # SvelteKit create process often requires interaction or specific template flags
        frontend_command = ["npm", "create", "svelte@latest", "."] # Might prompt
    elif frontend_framework == "alpine":
        print("Alpine.js typically integrated into HTML, skipping separate scaffolding.")
    # Add other frameworks (Angular, etc.) as needed
    else:
        print(f"Unsupported frontend framework specified: {frontend_framework}. Skipping frontend scaffold.")

    if frontend_command:
         # Create the directory first if it doesn't exist from previous steps
        if not frontend_dir.exists():
             try:
                 print(f"Creating frontend directory: {frontend_dir}")
                 frontend_dir.mkdir(parents=True, exist_ok=True)
             except Exception as e:
                 print(f"Error creating frontend directory {frontend_dir}: {e}")

        # Only run if directory exists/was created
        if frontend_dir.exists():
            run_scaffolding_command(frontend_command, frontend_dir, f"{frontend_framework.capitalize()} Frontend Setup")

    print("--- Application Scaffolding Attempted ---")
    print("Note: Scaffolding commands might require manual interaction or configuration.")
    print("--- Project Bootstrap Script Finished ---")
