# ESTRATIX Standards Matrix

This matrix provides a comprehensive overview of all defined standards within the ESTRATIX framework, including coding conventions, security protocols, project management methodologies, compliance requirements, and more.

| Standard ID                                 | Standard Name                                     | Version | Status      | Responsible Command Office | Category                                  | Date Created | Last Updated | Link to Definition                                                     | Key Applicability                                  | Notes                                                              |
| :------------------------------------------ | :------------------------------------------------ | :------ | :---------- | :------------------------- | :---------------------------------------- | :----------- | :----------- | :--------------------------------------------------------------------- | :------------------------------------------------- | :----------------------------------------------------------------- |
| `STD_CODING_PYTHON_001`                     | Python Coding Standards                           | `0.1`   | `Draft`     | `CTO`                      | `Coding Convention`                       | `YYYY-MM-DD` | `YYYY-MM-DD` | `../standards/cto/STD_CODING_PYTHON_001_PythonCodingStandards.md`      | All Python projects                                | Based on PEP 8 with ESTRATIX-specific additions.                   |
| `STD_PM_AGILE_SCRUM_001`                    | Agile Scrum Methodology                           | `0.1`   | `Proposed`  | `CPrO`                     | `Project Management Methodology`          | `YYYY-MM-DD` | `YYYY-MM-DD` | `../standards/cpro/STD_PM_AGILE_SCRUM_001_AgileScrumMethodology.md` | Software development projects                      | Defines roles, ceremonies, artifacts for Scrum.                    |
| `STD_SECURITY_DATAPROTECTION_001`           | Data Protection and Privacy Standard              | `0.1`   | `Draft`     | `CIO`                      | `Security Protocol`                       | `YYYY-MM-DD` | `YYYY-MM-DD` | `../standards/cio/STD_SECURITY_DATAPROTECTION_001_DataProtection.md` | All systems handling PII or sensitive client data. | Aligns with GDPR and CCPA principles.                              |
| `STD_NAMING_GENERAL_001`                    | General Naming Conventions                        | `0.1`   | `Draft`     | `CTO`                      | `Naming Convention`                       | `YYYY-MM-DD` | `YYYY-MM-DD` | `../standards/cto/STD_NAMING_GENERAL_001_GeneralNamingConventions.md`  | All ESTRATIX components (files, folders, classes)  | Provides consistent naming across the framework.                   |
| `<!-- ADD MORE STANDARDS HERE -->`           | `<!-- ... -->`                                    |         |             |                            |                                           |              |              |                                                                        |                                                    |                                                                    |

**Matrix Columns:**

* **Standard ID:** Unique identifier for the standard (e.g., `STD_CATEGORY_SPECIFIC_001`).
* **Standard Name:** Descriptive name of the standard.
* **Version:** Current version of the standard document.
* **Status:** Current status (`Draft`, `Proposed`, `Active`, `Deprecated`, `Archived`).
* **Responsible Command Office:** The office primarily responsible for defining and maintaining the standard.
* **Category:** Broad classification (e.g., `Coding Convention`, `Security Protocol`, `Project Management Methodology`, `Compliance Requirement`).
* **Date Created:** Date the standard definition was initiated.
* **Last Updated:** Date the standard definition was last modified.
* **Link to Definition:** Direct link to the detailed standard definition document in `docs/standards/[OFFICER_CODE]/`.
* **Key Applicability:** Brief summary of where the standard primarily applies.
* **Notes:** Any additional relevant information or context.

**To Add a New Standard:**

1. Define the standard using the `estratix_standard_definition_template.md`.
2. Save the definition in the appropriate `docs/standards/[OFFICER_CODE]/` directory.
3. Add a new row to this matrix with the relevant details and a link to the definition file.
4. Update the `docs/diagrams/standards_landscape.mmd` (potentially via `wf_update_standards_landscape.md`).
