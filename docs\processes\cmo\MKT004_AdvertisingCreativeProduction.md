# ESTRATIX Process Definition: MKT004 - Advertising Creative Production

**Process ID:** MKT004
**Process Name:** Advertising Creative Production
**Version:** 1.0
**Status:** Draft
**Responsible Team / Focus Area:** Marketing Ad Creative Crew, Ad Design Specialists
**Last Reviewed:** 2025-05-14

---

## 1. Purpose

To design, develop, and produce high-performing, platform-specific advertising creatives (static, video, interactive) that effectively communicate the value of ESTRATIX's productized services, align with campaign objectives, and drive conversions. This process combines strong marketing acumen with creative production skills, tailored for various advertising channels.

## 2. Goal

*   Produce 100% of planned ad creatives on schedule and compliant with platform specifications.
*   Achieve an average X% improvement in Click-Through Rates (CTR) or Conversion Rates for ads using creatives from this process compared to baseline or previous ad versions.
*   Reduce ad creative iteration cycles by Y% through clear briefs and efficient agent/human workflows.
*   Ensure all ad creatives are A/B testable with clear variations.
*   Maintain a library of ad creatives, performance data, and learnings for future campaigns.

## 3. Scope

*   **In Scope:** Interpreting advertising campaign briefs; Static Ad Creative Production; Video Ad Creative Production (editing/repurposing existing content, creating net-new short video ads); Motion Graphics for Ads; Ad Copywriting (headlines, body, CTAs); Developing A/B test variations; Ensuring ad creatives comply with platform policies/specs; Localizing ad creatives; Managing ad creative assets and performance linkage.
*   **Out of Scope:** Overall advertising campaign strategy, media buying, campaign management (`MKT005`); Complex, long-form video production (`MKT003`); Organic content creation (`MKT002`, `MKT003`); Landing page design/development.

## 4. Triggers

*   Approved advertising campaign brief requiring new/refreshed ad creatives (from `MKT005`).
*   Performance data indicating need to iterate on existing ad creatives.
*   Launch of new productized services requiring ad support.
*   Expansion to new advertising platforms.

## 5. Inputs

*   Advertising Campaign Brief (`MKT005`/`MKT001`).
*   Brand Guidelines & Messaging Framework (`MKT001`).
*   Productized Service Information & USPs (`MKT001`/Product Management).
*   Audience Insights & Persona Profiles (`MKT001`).
*   Assets for Repurposing (from `MKT003`, `MKT002`).
*   Ad Platform Specifications & Best Practices.
*   Historical Ad Performance Data & Learnings.
*   Style Guides for Ad Copy & Visuals.

## 6. Outputs

*   **`AdCreativePackage`:**
    *   Platform-Specific Ad Creative Files (e.g., images for Facebook, videos for TikTok, HTML5 banners for GDN).
    *   Directory of A/B Test Variations for each creative set (if applicable).
    *   Detailed Specifications Sheet for each creative (dimensions, file size, format, character limits, compliance notes).
    *   List of all copy variations (headlines, body text, CTAs).
    *   Included Tracking Parameters/UTM Tags for each variant.
    *   Link to originating `ContentPlanItem` from `MKT001` or `CampaignBrief` from `MKT005`.
    *   Notes on creative rationale or performance expectations.
*   Approved Ad Creatives ready for `MKT005 (Paid Acquisition)` or other distribution channels.
*   Source files for ad creatives (e.g., PSD, AI, AE project files).
*   Documentation of A/B testing strategy for the creatives.
*   Updated status in project management system / content calendar.

## 7. High-Level Steps

1.  **Campaign Brief Intake & Deconstruction (`AdCreativeStrategistAgent`):** Analyze brief, identify key messages, audience, platform requirements.
2.  **Concept Development & Brainstorming (Human Ad Creatives, `AdConceptGeneratorAgent` support):** Develop creative concepts, storyboards, layouts.
3.  **Asset Sourcing & Curation:** Identify existing assets from `MKT003`/`MKT002`; request new core assets from `MKT003` if needed.
4.  **Creative Production (Iterative, by specialized agents/humans):** `StaticAdDesignerAgent`, `VideoAdEditorAgent`, `MotionGraphicsAdArtistAgent`, `AdCopywriterAgent`.
5.  **Platform Compliance Check (`AdPlatformComplianceAgent`):** Verify creatives against platform specs/policies.
6.  **Internal Review & A/B Variation Setup:** Review by `AdCreativeStrategistAgent`/creative leads; define A/B tests.
7.  **Stakeholder Review (Optional):** By `MktExec_CoordinatorAgent` or campaign manager (`MKT005`).
8.  **Finalization & Packaging:** Final edits, rendering, organize files for `MKT005`.
9.  **Ad Creative Archival (`AdCreativeLibrarianAgent`):** Store creatives, source files, performance links.

## 8. Tools, Libraries & MCPs (Open Source & Professional Grade)

*   **Graphic Design for Ads:** Adobe CC (Photoshop, Illustrator); Figma/Penpot; Canva; Open-Source: Inkscape, GIMP.
*   **Video Editing & Motion Graphics for Ads:** Adobe Premiere Pro, After Effects; DaVinci Resolve; Blender; Online Video Ad Makers (Veed.io, Pictory).
*   **Ad Copywriting & Management:** Text Editors / Notion / Google Docs; LLMs (via `MCP_LLM_AdCopy_Service`); Spreadsheets.
*   **Ad Platform Specific Tools:** Meta Ads Library, Google Ads Creative Studio.
*   **Collaboration & Asset Management:** Notion / Google Workspace; Frame.io; Nextcloud / DAM.
*   **MCPs (Conceptual/Internal):** `MCP_AdPlatform_Specs_API`, `MCP_LLM_AdCopy_Service`, `MCP_VideoAd_Templating_Service`, `MCP_AdCreative_Repository_Client`.

## 9. Roles & Responsibilities (Agentic & Human for `MKT004`)

*   **`AdCreativeStrategistAgent` / Human Ad Creative Lead:** Leads process, interprets briefs, guides concepts, ensures quality.
*   **Specialized Creative Production Agents/Roles:** `AdCopywriterAgent`, `StaticAdDesignerAgent`/Human Designer, `VideoAdEditorAgent`/Human Editor, `MotionGraphicsAdArtistAgent`, `AdConceptGeneratorAgent`.
*   **Support & QA Agents:** `AdPlatformComplianceAgent`, `AdCreativeLibrarianAgent`.

## 10. Metrics & KPIs (for `OBS001`, linked to `MKT005` data)

*   **Ad Creative CTR & Conversion Rate.**
*   **CPA associated with specific creatives.**
*   **ROAS by creative set.**
*   **Volume of A/B test variations produced.**
*   **Turnaround Time for ad creative production.**
*   **Platform Rejection Rate.**

## 11. Dependencies

*   **Relies On:** `MKT001` (brand strategy), `MKT005` (campaign briefs), `MKT003` (source assets), `MKT002` (copy drafts), `CIO_P002` (platform best practices).
*   **Feeds Into:** `MKT005` (ready-to-deploy creatives), `OBS001` (creative details for tracking).

## 12. Exception Handling

*   Ad Rejections: Root cause analysis, rapid iteration.
*   Poor Performing Creatives: Rapid A/B testing, new concepts.
*   Creative Fatigue: Proactive development of new concepts.
*   Misalignment with Campaign Goals: Feedback loop with `MKT005`.

## 13. PDCA (Continuous Improvement for `MKT004`)

*   **Plan:** Analyze ad creative performance, review competitor ads, study platform updates.
*   **Do:** Experiment with new ad formats, messaging, visuals, A/B testing.
*   **Check:** Monitor creative KPIs, solicit campaign manager feedback.
*   **Act:** Scale winning approaches, update best practices, retire underperforming styles.

## 14. Agentic Framework Mapping

*   **Hybrid: Agent-led strategy & compliance, Human-led high-skill creative, Agent-assisted production & management.**
*   **Pydantic-AI Models:** `AdCreativeRequestBrief` (linking to `ContentPlanItem` or `MKT005_CampaignBrief`), `PlatformAdSpecification`, **`AdCreativePackage`, `PlatformAdVariant`, `AdCreativeMetadata` (common metadata including specs, tracking, source file links, originating brief ID).**
*   **Windsurf Workflows:** `/wf_mkt_develop_facebook_ad_set <request_brief_id>`, `/wf_mkt_produce_tiktok_video_ad_variations <request_brief_id>`, `/wf_mkt_ad_creative_ab_test_setup <creative_package_id>`.
*   **A2A/ACP Protocols:** Standardized JSON for creative briefs and ad creative packages.
*   **Aider Integration:** Can assist in generating copy variations, adapting messaging for different platforms based on character limits and best practices, or creating boilerplate for A/B testing documentation.
