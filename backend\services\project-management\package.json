{"name": "@estratix/project-management-service", "version": "1.0.0", "description": "Project Management microservice for ESTRATIX platform - handles project lifecycle, task management, resource allocation, and team collaboration", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/jwt": "^8.0.1", "@fastify/multipart": "^8.3.0", "@fastify/swagger": "^8.15.0", "@fastify/swagger-ui": "^4.1.0", "@prisma/client": "^5.22.0", "axios": "^1.7.7", "bullmq": "^5.15.0", "dotenv": "^16.4.5", "fastify": "^4.28.1", "fastify-plugin": "^5.0.1", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "pino": "^9.4.0", "zod": "^3.23.8", "date-fns": "^3.6.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.16.5", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "eslint": "^9.25.0", "jest": "^29.7.0", "prisma": "^5.22.0", "ts-jest": "^29.2.5", "tsx": "^4.19.2", "typescript": "^5.8.3"}, "keywords": ["project-management", "task-management", "resource-allocation", "team-collaboration", "agile", "scrum", "kanban", "real-estate"], "author": "ESTRATIX Development Team", "license": "MIT"}