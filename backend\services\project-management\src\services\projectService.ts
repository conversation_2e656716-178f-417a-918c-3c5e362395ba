import { logger } from '../utils/logger';
import { config } from '../config/environment';

interface Project {
  id: string;
  name: string;
  description: string;
  type: 'real-estate' | 'development' | 'investment' | 'tokenization';
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  ownerId: string;
  createdBy: string;
  startDate?: Date;
  endDate?: Date;
  budget?: number;
  currency: string;
  location?: {
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  tags: string[];
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

interface CreateProjectData {
  name: string;
  description: string;
  type: 'real-estate' | 'development' | 'investment' | 'tokenization';
  status?: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  ownerId: string;
  createdBy: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
  currency?: string;
  location?: {
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  tags?: string[];
  metadata?: Record<string, any>;
}

interface ProjectQuery {
  page: number;
  limit: number;
  status?: string;
  type?: string;
  priority?: string;
  search?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
  userId: string;
  userRole: string;
}

class ProjectService {
  private projects: Map<string, Project> = new Map();
  private initialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Project Service...');
      
      // Initialize database connection
      // In a real implementation, this would connect to PostgreSQL
      
      // Create sample projects for development
      if (config.NODE_ENV === 'development') {
        await this.createSampleProjects();
      }
      
      this.initialized = true;
      logger.info('Project Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Project Service:', error);
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Project Service...');
    // Cleanup database connections, etc.
    this.initialized = false;
  }

  async isReady(): Promise<boolean> {
    return this.initialized;
  }

  async checkDatabaseConnection(): Promise<{ status: string; message?: string }> {
    try {
      // In a real implementation, this would check PostgreSQL connection
      return { status: 'ok' };
    } catch (error) {
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  async getServiceStatus(): Promise<{ status: string; message?: string }> {
    return {
      status: this.initialized ? 'ok' : 'not-ready',
      message: this.initialized ? undefined : 'Service not initialized'
    };
  }

  async getMetrics(): Promise<Record<string, any>> {
    return {
      totalProjects: this.projects.size,
      projectsByStatus: this.getProjectCountByStatus(),
      projectsByType: this.getProjectCountByType(),
      projectsByPriority: this.getProjectCountByPriority(),
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime()
    };
  }

  async createProject(data: CreateProjectData): Promise<Project> {
    const id = this.generateId();
    const now = new Date();
    
    const project: Project = {
      id,
      name: data.name,
      description: data.description,
      type: data.type,
      status: data.status || 'planning',
      priority: data.priority || 'medium',
      ownerId: data.ownerId,
      createdBy: data.createdBy,
      startDate: data.startDate ? new Date(data.startDate) : undefined,
      endDate: data.endDate ? new Date(data.endDate) : undefined,
      budget: data.budget,
      currency: data.currency || 'USD',
      location: data.location,
      tags: data.tags || [],
      metadata: data.metadata || {},
      createdAt: now,
      updatedAt: now
    };
    
    this.projects.set(id, project);
    
    logger.info(`Project created: ${id}`, { projectId: id, name: project.name });
    
    return project;
  }

  async getProjects(query: ProjectQuery): Promise<{
    projects: Project[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    let filteredProjects = Array.from(this.projects.values());
    
    // Apply filters
    if (query.status) {
      filteredProjects = filteredProjects.filter(p => p.status === query.status);
    }
    
    if (query.type) {
      filteredProjects = filteredProjects.filter(p => p.type === query.type);
    }
    
    if (query.priority) {
      filteredProjects = filteredProjects.filter(p => p.priority === query.priority);
    }
    
    if (query.search) {
      const searchLower = query.search.toLowerCase();
      filteredProjects = filteredProjects.filter(p => 
        p.name.toLowerCase().includes(searchLower) ||
        p.description.toLowerCase().includes(searchLower) ||
        p.tags.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }
    
    // Apply sorting
    filteredProjects.sort((a, b) => {
      const aValue = this.getProjectSortValue(a, query.sortBy);
      const bValue = this.getProjectSortValue(b, query.sortBy);
      
      if (query.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
    
    // Apply pagination
    const total = filteredProjects.length;
    const totalPages = Math.ceil(total / query.limit);
    const startIndex = (query.page - 1) * query.limit;
    const endIndex = startIndex + query.limit;
    const projects = filteredProjects.slice(startIndex, endIndex);
    
    return {
      projects,
      total,
      page: query.page,
      limit: query.limit,
      totalPages
    };
  }

  async getProjectById(id: string, userId: string, userRole: string): Promise<Project | null> {
    const project = this.projects.get(id);
    
    if (!project) {
      return null;
    }
    
    // Check permissions
    if (!this.canAccessProject(project, userId, userRole)) {
      return null;
    }
    
    return project;
  }

  async updateProject(
    id: string,
    updateData: Partial<CreateProjectData>,
    userId: string,
    userRole: string
  ): Promise<Project | null> {
    const project = this.projects.get(id);
    
    if (!project) {
      return null;
    }
    
    // Check permissions
    if (!this.canModifyProject(project, userId, userRole)) {
      throw new Error('Insufficient permissions to update project');
    }
    
    const updatedProject: Project = {
      ...project,
      ...updateData,
      startDate: updateData.startDate ? new Date(updateData.startDate) : project.startDate,
      endDate: updateData.endDate ? new Date(updateData.endDate) : project.endDate,
      updatedAt: new Date()
    };
    
    this.projects.set(id, updatedProject);
    
    logger.info(`Project updated: ${id}`, { projectId: id });
    
    return updatedProject;
  }

  async deleteProject(id: string, userId: string, userRole: string): Promise<boolean> {
    const project = this.projects.get(id);
    
    if (!project) {
      return false;
    }
    
    // Check permissions
    if (!this.canModifyProject(project, userId, userRole)) {
      throw new Error('Insufficient permissions to delete project');
    }
    
    this.projects.delete(id);
    
    logger.info(`Project deleted: ${id}`, { projectId: id });
    
    return true;
  }

  async getProjectAnalytics(id: string, userId: string, userRole: string): Promise<Record<string, any>> {
    const project = this.projects.get(id);
    
    if (!project) {
      throw new Error('Project not found');
    }
    
    // Check permissions
    if (!this.canAccessProject(project, userId, userRole)) {
      throw new Error('Insufficient permissions to view project analytics');
    }
    
    // Mock analytics data
    return {
      projectId: id,
      overview: {
        status: project.status,
        priority: project.priority,
        progress: this.calculateProjectProgress(project),
        daysActive: this.calculateDaysActive(project),
        budget: {
          allocated: project.budget || 0,
          spent: (project.budget || 0) * 0.3, // Mock 30% spent
          remaining: (project.budget || 0) * 0.7
        }
      },
      timeline: {
        startDate: project.startDate,
        endDate: project.endDate,
        estimatedCompletion: this.estimateCompletion(project)
      },
      team: {
        totalMembers: 5, // Mock data
        activeMembers: 4,
        roles: ['Project Manager', 'Developer', 'Designer', 'Analyst']
      },
      tasks: {
        total: 25, // Mock data
        completed: 8,
        inProgress: 12,
        pending: 5
      }
    };
  }

  private async createSampleProjects(): Promise<void> {
    const sampleProjects = [
      {
        name: 'Luxury Residential Complex',
        description: 'High-end residential development with smart home integration',
        type: 'real-estate' as const,
        status: 'active' as const,
        priority: 'high' as const,
        ownerId: 'user-1',
        createdBy: 'user-1',
        budget: 5000000,
        location: {
          address: '123 Main St',
          city: 'Miami',
          state: 'FL',
          country: 'USA'
        },
        tags: ['luxury', 'residential', 'smart-home']
      },
      {
        name: 'Commercial Office Tower',
        description: 'Modern office building with sustainable features',
        type: 'development' as const,
        status: 'planning' as const,
        priority: 'medium' as const,
        ownerId: 'user-1',
        createdBy: 'user-1',
        budget: 15000000,
        location: {
          address: '456 Business Ave',
          city: 'New York',
          state: 'NY',
          country: 'USA'
        },
        tags: ['commercial', 'office', 'sustainable']
      }
    ];
    
    for (const projectData of sampleProjects) {
      await this.createProject(projectData);
    }
  }

  private generateId(): string {
    return `proj_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getProjectSortValue(project: Project, sortBy: string): any {
    switch (sortBy) {
      case 'name':
        return project.name;
      case 'createdAt':
        return project.createdAt;
      case 'updatedAt':
        return project.updatedAt;
      case 'startDate':
        return project.startDate || new Date(0);
      case 'endDate':
        return project.endDate || new Date(0);
      case 'priority':
        const priorityOrder = { low: 1, medium: 2, high: 3, critical: 4 };
        return priorityOrder[project.priority];
      default:
        return project.createdAt;
    }
  }

  private canAccessProject(project: Project, userId: string, userRole: string): boolean {
    // Admin can access all projects
    if (userRole === 'admin') {
      return true;
    }
    
    // Owner can access their projects
    if (project.ownerId === userId) {
      return true;
    }
    
    // Add more permission logic as needed
    return false;
  }

  private canModifyProject(project: Project, userId: string, userRole: string): boolean {
    // Admin can modify all projects
    if (userRole === 'admin') {
      return true;
    }
    
    // Owner can modify their projects
    if (project.ownerId === userId) {
      return true;
    }
    
    return false;
  }

  private getProjectCountByStatus(): Record<string, number> {
    const counts: Record<string, number> = {};
    for (const project of this.projects.values()) {
      counts[project.status] = (counts[project.status] || 0) + 1;
    }
    return counts;
  }

  private getProjectCountByType(): Record<string, number> {
    const counts: Record<string, number> = {};
    for (const project of this.projects.values()) {
      counts[project.type] = (counts[project.type] || 0) + 1;
    }
    return counts;
  }

  private getProjectCountByPriority(): Record<string, number> {
    const counts: Record<string, number> = {};
    for (const project of this.projects.values()) {
      counts[project.priority] = (counts[project.priority] || 0) + 1;
    }
    return counts;
  }

  private calculateProjectProgress(project: Project): number {
    // Mock progress calculation
    const daysSinceStart = project.startDate ? 
      Math.floor((Date.now() - project.startDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
    return Math.min(daysSinceStart * 2, 100); // Mock 2% per day
  }

  private calculateDaysActive(project: Project): number {
    return project.startDate ? 
      Math.floor((Date.now() - project.startDate.getTime()) / (1000 * 60 * 60 * 24)) : 0;
  }

  private estimateCompletion(project: Project): Date | null {
    if (!project.startDate || !project.endDate) {
      return null;
    }
    
    const progress = this.calculateProjectProgress(project);
    if (progress === 0) {
      return project.endDate;
    }
    
    const totalDuration = project.endDate.getTime() - project.startDate.getTime();
    const estimatedDuration = totalDuration / (progress / 100);
    
    return new Date(project.startDate.getTime() + estimatedDuration);
  }
}

export const projectService = new ProjectService();