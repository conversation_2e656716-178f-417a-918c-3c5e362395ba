import { apiClient, ApiResponse } from './api.client';
import { BaseService } from './base.service';
import { buildSearchParams, applyFilters, sortItems, paginate } from './service.utils';

// NFT types
export interface NFT {
  id: string;
  tokenId: string;
  contractAddress: string;
  name: string;
  description: string;
  image: string;
  animationUrl?: string;
  externalUrl?: string;
  attributes: NFTAttribute[];
  collection: NFTCollection;
  owner: string;
  creator: string;
  price?: number;
  currency: 'ETH' | 'MATIC' | 'USD';
  isForSale: boolean;
  isAuction: boolean;
  auctionEndTime?: Date;
  highestBid?: number;
  bidCount: number;
  viewCount: number;
  likeCount: number;
  rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
  rarityScore: number;
  blockchain: 'ethereum' | 'polygon' | 'binance';
  standard: 'ERC-721' | 'ERC-1155';
  royalty: number;
  createdAt: Date;
  updatedAt: Date;
  lastSalePrice?: number;
  lastSaleDate?: Date;
  metadata: Record<string, any>;
}

export interface NFTAttribute {
  trait_type: string;
  value: string | number;
  display_type?: 'number' | 'boost_percentage' | 'boost_number' | 'date';
  max_value?: number;
}

export interface NFTCollection {
  id: string;
  name: string;
  description: string;
  symbol: string;
  contractAddress: string;
  image: string;
  bannerImage?: string;
  creator: string;
  totalSupply: number;
  floorPrice: number;
  volume24h: number;
  volumeTotal: number;
  owners: number;
  isVerified: boolean;
  blockchain: NFT['blockchain'];
  royalty: number;
  createdAt: Date;
}

export interface NFTFilters {
  collection?: string[];
  priceMin?: number;
  priceMax?: number;
  currency?: NFT['currency'][];
  isForSale?: boolean;
  isAuction?: boolean;
  rarity?: NFT['rarity'][];
  blockchain?: NFT['blockchain'][];
  attributes?: Record<string, string[]>;
  creator?: string;
  owner?: string;
}

export interface NFTSearchParams extends NFTFilters {
  query?: string;
  sortBy?: 'price' | 'created' | 'updated' | 'rarity' | 'views' | 'likes';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface NFTStats {
  totalNFTs: number;
  totalCollections: number;
  totalVolume: number;
  averagePrice: number;
  topCollections: Array<{
    collection: NFTCollection;
    volume: number;
    floorPrice: number;
  }>;
  recentSales: Array<{
    nft: NFT;
    price: number;
    buyer: string;
    seller: string;
    timestamp: Date;
  }>;
}

export interface BidRequest {
  nftId: string;
  amount: number;
  currency: NFT['currency'];
  expiresAt?: Date;
}

export interface ListingRequest {
  nftId: string;
  price: number;
  currency: NFT['currency'];
  isAuction: boolean;
  auctionDuration?: number; // hours
  reservePrice?: number;
}

// Mock data for development
const mockCollections: NFTCollection[] = [
  {
    id: 'collection-1',
    name: 'Luxury Properties',
    description: 'Exclusive NFT collection representing luxury real estate properties',
    symbol: 'LUXPROP',
    contractAddress: '******************************************',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20real%20estate%20nft%20collection%20logo&image_size=square',
    bannerImage: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20real%20estate%20collection%20banner%20modern&image_size=landscape_16_9',
    creator: '******************************************',
    totalSupply: 1000,
    floorPrice: 2.5,
    volume24h: 45.8,
    volumeTotal: 1250.6,
    owners: 342,
    isVerified: true,
    blockchain: 'ethereum',
    royalty: 5,
    createdAt: new Date('2023-12-01')
  },
  {
    id: 'collection-2',
    name: 'Digital Art Gallery',
    description: 'Curated collection of digital art pieces',
    symbol: 'DIGART',
    contractAddress: '******************************************',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=digital%20art%20gallery%20nft%20collection%20logo&image_size=square',
    creator: '******************************************',
    totalSupply: 500,
    floorPrice: 0.8,
    volume24h: 12.3,
    volumeTotal: 456.7,
    owners: 156,
    isVerified: true,
    blockchain: 'polygon',
    royalty: 7.5,
    createdAt: new Date('2024-01-01')
  }
];

const mockNFTs: NFT[] = [
  {
    id: 'nft-1',
    tokenId: '1',
    contractAddress: '******************************************',
    name: 'Luxury Penthouse #001',
    description: 'Exclusive NFT representing ownership rights to a luxury penthouse in Manhattan',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=luxury%20penthouse%20manhattan%20nft%20artwork%20modern&image_size=square_hd',
    attributes: [
      { trait_type: 'Location', value: 'Manhattan' },
      { trait_type: 'Bedrooms', value: 3, display_type: 'number' },
      { trait_type: 'Bathrooms', value: 2, display_type: 'number' },
      { trait_type: 'Square Feet', value: 2200, display_type: 'number' },
      { trait_type: 'Floor', value: 45, display_type: 'number' },
      { trait_type: 'View', value: 'City Skyline' },
      { trait_type: 'Rarity', value: 'Legendary' }
    ],
    collection: mockCollections[0],
    owner: '0xowner1234567890abcdef1234567890abcdef12',
    creator: '******************************************',
    price: 15.5,
    currency: 'ETH',
    isForSale: true,
    isAuction: false,
    bidCount: 0,
    viewCount: 1250,
    likeCount: 89,
    rarity: 'legendary',
    rarityScore: 95.8,
    blockchain: 'ethereum',
    standard: 'ERC-721',
    royalty: 5,
    createdAt: new Date('2023-12-15'),
    updatedAt: new Date('2024-01-10'),
    lastSalePrice: 12.8,
    lastSaleDate: new Date('2023-12-20'),
    metadata: {
      propertyId: 'prop-1',
      legalDocuments: 'https://example.com/legal/prop-1.pdf'
    }
  },
  {
    id: 'nft-2',
    tokenId: '2',
    contractAddress: '******************************************',
    name: 'Historic Brownstone #002',
    description: 'NFT representing a historic brownstone in Boston\'s Beacon Hill',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=historic%20brownstone%20beacon%20hill%20nft%20artwork&image_size=square_hd',
    attributes: [
      { trait_type: 'Location', value: 'Beacon Hill' },
      { trait_type: 'Bedrooms', value: 4, display_type: 'number' },
      { trait_type: 'Bathrooms', value: 3, display_type: 'number' },
      { trait_type: 'Square Feet', value: 2800, display_type: 'number' },
      { trait_type: 'Year Built', value: 1890, display_type: 'date' },
      { trait_type: 'Style', value: 'Victorian' },
      { trait_type: 'Rarity', value: 'Epic' }
    ],
    collection: mockCollections[0],
    owner: '0xowner2345678901bcdef12345678901bcdef123',
    creator: '******************************************',
    price: 8.2,
    currency: 'ETH',
    isForSale: false,
    isAuction: true,
    auctionEndTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
    highestBid: 7.5,
    bidCount: 12,
    viewCount: 890,
    likeCount: 67,
    rarity: 'epic',
    rarityScore: 87.3,
    blockchain: 'ethereum',
    standard: 'ERC-721',
    royalty: 5,
    createdAt: new Date('2023-12-18'),
    updatedAt: new Date('2024-01-12'),
    metadata: {
      propertyId: 'prop-2',
      historicalSignificance: 'Listed on National Register of Historic Places'
    }
  },
  {
    id: 'nft-3',
    tokenId: '1',
    contractAddress: '******************************************',
    name: 'Abstract Composition #001',
    description: 'Digital art piece exploring modern abstract forms',
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=abstract%20digital%20art%20composition%20modern%20colorful&image_size=square_hd',
    attributes: [
      { trait_type: 'Style', value: 'Abstract' },
      { trait_type: 'Colors', value: 'Vibrant' },
      { trait_type: 'Technique', value: 'Digital' },
      { trait_type: 'Mood', value: 'Energetic' },
      { trait_type: 'Rarity', value: 'Rare' }
    ],
    collection: mockCollections[1],
    owner: '0xowner3456789012cdef123456789012cdef1234',
    creator: '******************************************',
    price: 1.2,
    currency: 'ETH',
    isForSale: true,
    isAuction: false,
    bidCount: 0,
    viewCount: 456,
    likeCount: 34,
    rarity: 'rare',
    rarityScore: 72.1,
    blockchain: 'polygon',
    standard: 'ERC-721',
    royalty: 7.5,
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-15'),
    metadata: {
      artist: 'Digital Artist',
      medium: 'Digital Canvas'
    }
  }
];

class NFTService extends BaseService {

  async getNFTs(params?: NFTSearchParams): Promise<{ nfts: NFT[]; total: number }> {
    return this.handleApiCall(
      async () => {
        const searchParams = buildSearchParams({
          query: params?.query,
          collection: params?.collection?.join(','),
          priceMin: params?.priceMin?.toString(),
          priceMax: params?.priceMax?.toString(),
          currency: params?.currency?.join(','),
          isForSale: params?.isForSale?.toString(),
          isAuction: params?.isAuction?.toString(),
          rarity: params?.rarity?.join(','),
          blockchain: params?.blockchain?.join(','),
          creator: params?.creator,
          owner: params?.owner,
          sortBy: params?.sortBy,
          sortOrder: params?.sortOrder,
          page: params?.page?.toString(),
          limit: params?.limit?.toString()
        });
        const response = await apiClient.get<{ nfts: NFT[]; total: number }>(
          `/nfts?${searchParams}`
        );
        return response.data;
      },
      async () => {
        let filteredNFTs = applyFilters(mockNFTs, {
          collection: params?.collection ? (nft: NFT) => params.collection!.includes(nft.collection.id) : undefined,
          priceRange: params?.priceMin && params?.priceMax ? (nft: NFT) => 
            nft.price && nft.price >= params.priceMin! && nft.price <= params.priceMax! : undefined,
          currency: params?.currency ? (nft: NFT) => params.currency!.includes(nft.currency) : undefined,
          isForSale: params?.isForSale !== undefined ? (nft: NFT) => nft.isForSale === params.isForSale : undefined,
          isAuction: params?.isAuction !== undefined ? (nft: NFT) => nft.isAuction === params.isAuction : undefined,
          rarity: params?.rarity ? (nft: NFT) => params.rarity!.includes(nft.rarity) : undefined,
          blockchain: params?.blockchain ? (nft: NFT) => params.blockchain!.includes(nft.blockchain) : undefined,
          creator: params?.creator ? (nft: NFT) => nft.creator === params.creator : undefined,
          owner: params?.owner ? (nft: NFT) => nft.owner === params.owner : undefined,
          query: params?.query ? (nft: NFT) => {
            const query = params.query!.toLowerCase();
            return nft.name.toLowerCase().includes(query) ||
                   nft.description.toLowerCase().includes(query) ||
                   nft.collection.name.toLowerCase().includes(query);
          } : undefined
        });
        
        if (params?.sortBy) {
          filteredNFTs = sortItems(filteredNFTs, params.sortBy, params.sortOrder || 'asc', {
            price: (nft: NFT) => nft.price || 0,
            created: (nft: NFT) => nft.createdAt,
            updated: (nft: NFT) => nft.updatedAt,
            rarity: (nft: NFT) => nft.rarityScore,
            views: (nft: NFT) => nft.viewCount,
            likes: (nft: NFT) => nft.likeCount
          });
        }
        
        const page = params?.page || 1;
        const limit = params?.limit || 12;
        const paginatedResult = paginate(filteredNFTs, page, limit);
        
        return {
          nfts: paginatedResult.items,
          total: filteredNFTs.length
        };
      },
      600
    );
  }

  async getNFTById(id: string): Promise<NFT> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFT>(`/nfts/${id}`);
        return response.data;
      },
      async () => {
        const nft = mockNFTs.find(n => n.id === id);
        if (!nft) {
          throw new Error('NFT not found');
        }
        return nft;
      },
      400
    );
  }

  async getCollections(): Promise<NFTCollection[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFTCollection[]>('/nfts/collections');
        return response.data;
      },
      async () => [...mockCollections],
      400
    );
  }

  async getCollectionById(id: string): Promise<NFTCollection> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFTCollection>(`/nfts/collections/${id}`);
        return response.data;
      },
      async () => {
        const collection = mockCollections.find(c => c.id === id);
        if (!collection) {
          throw new Error('Collection not found');
        }
        return collection;
      },
      300
    );
  }

  async placeBid(bid: BidRequest): Promise<{ success: boolean; message: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; message: string }>('/nfts/bid', bid);
        return response.data;
      },
      async () => {
        const nft = mockNFTs.find(n => n.id === bid.nftId);
        if (!nft) {
          throw new Error('NFT not found');
        }
        
        if (!nft.isAuction) {
          throw new Error('NFT is not available for auction');
        }
        
        if (nft.highestBid && bid.amount <= nft.highestBid) {
          throw new Error('Bid amount must be higher than current highest bid');
        }
        
        // Update mock data
        nft.highestBid = bid.amount;
        nft.bidCount += 1;
        nft.updatedAt = new Date();
        
        return {
          success: true,
          message: 'Bid placed successfully'
        };
      },
      1500
    );
  }

  async listNFT(listing: ListingRequest): Promise<{ success: boolean; message: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; message: string }>('/nfts/list', listing);
        return response.data;
      },
      async () => {
        const nft = mockNFTs.find(n => n.id === listing.nftId);
        if (!nft) {
          throw new Error('NFT not found');
        }
        
        // Update mock data
        nft.price = listing.price;
        nft.currency = listing.currency;
        nft.isForSale = true;
        nft.isAuction = listing.isAuction;
        
        if (listing.isAuction && listing.auctionDuration) {
          nft.auctionEndTime = new Date(Date.now() + listing.auctionDuration * 60 * 60 * 1000);
        }
        
        nft.updatedAt = new Date();
        
        return {
          success: true,
          message: 'NFT listed successfully'
        };
      },
      1200
    );
  }

  async buyNFT(nftId: string): Promise<{ success: boolean; transactionHash: string }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; transactionHash: string }>(
          `/nfts/${nftId}/buy`
        );
        return response.data;
      },
      async () => {
        const nft = mockNFTs.find(n => n.id === nftId);
        if (!nft) {
          throw new Error('NFT not found');
        }
        
        if (!nft.isForSale || nft.isAuction) {
          throw new Error('NFT is not available for direct purchase');
        }
        
        // Update mock data
        nft.isForSale = false;
        nft.lastSalePrice = nft.price;
        nft.lastSaleDate = new Date();
        nft.owner = 'current-user'; // Would come from auth context
        nft.updatedAt = new Date();
        
        return {
          success: true,
          transactionHash: '0x' + Math.random().toString(16).substr(2, 64)
        };
      },
      2000
    );
  }

  async getNFTStats(): Promise<NFTStats> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFTStats>('/nfts/stats');
        return response.data;
      },
      async () => {
        const totalVolume = mockNFTs.reduce((sum, nft) => sum + (nft.lastSalePrice || 0), 0);
        const averagePrice = totalVolume / mockNFTs.filter(nft => nft.lastSalePrice).length || 0;
        
        const topCollections = mockCollections.map(collection => ({
          collection,
          volume: collection.volume24h,
          floorPrice: collection.floorPrice
        })).sort((a, b) => b.volume - a.volume);
        
        const recentSales = mockNFTs
          .filter(nft => nft.lastSalePrice && nft.lastSaleDate)
          .map(nft => ({
            nft,
            price: nft.lastSalePrice!,
            buyer: nft.owner,
            seller: 'previous-owner',
            timestamp: nft.lastSaleDate!
          }))
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
          .slice(0, 10);
        
        return {
          totalNFTs: mockNFTs.length,
          totalCollections: mockCollections.length,
          totalVolume,
          averagePrice,
          topCollections,
          recentSales
        };
      },
      500
    );
  }

  async likeNFT(nftId: string): Promise<{ success: boolean; likeCount: number }> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.post<{ success: boolean; likeCount: number }>(
          `/nfts/${nftId}/like`
        );
        return response.data;
      },
      async () => {
        const nft = mockNFTs.find(n => n.id === nftId);
        if (!nft) {
          throw new Error('NFT not found');
        }
        
        nft.likeCount += 1;
        nft.updatedAt = new Date();
        
        return {
          success: true,
          likeCount: nft.likeCount
        };
      },
      300
    );
  }

  async getMyNFTs(address: string): Promise<NFT[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFT[]>(`/nfts/my/${address}`);
        return response.data;
      },
      async () => mockNFTs.filter(nft => nft.owner === address),
      600
    );
  }

  async getCreatedNFTs(address: string): Promise<NFT[]> {
    return this.handleApiCall(
      async () => {
        const response = await apiClient.get<NFT[]>(`/nfts/created/${address}`);
        return response.data;
      },
      async () => mockNFTs.filter(nft => nft.creator === address),
      600
    );
  }
}

export const nftService = new NFTService();
export default nftService;