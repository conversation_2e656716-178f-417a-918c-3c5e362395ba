/**
 * Utility functions for service layer
 */

/**
 * Builds URLSearchParams from an object, filtering out undefined values
 */
export function buildSearchParams(params: Record<string, string | undefined>): URLSearchParams {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      searchParams.append(key, value);
    }
  });
  
  return searchParams;
}

/**
 * Applies multiple filters to an array of items
 */
export function applyFilters<T>(
  items: T[],
  filters: Record<string, ((item: T) => boolean) | undefined>
): T[] {
  return items.filter(item => {
    return Object.values(filters).every(filter => {
      return !filter || filter(item);
    });
  });
}

/**
 * Sorts an array of items based on a field and order
 */
export function sortItems<T>(
  items: T[],
  sortBy: string,
  sortOrder: 'asc' | 'desc',
  fieldMap?: Record<string, string | ((item: T) => any)>
): T[] {
  const sortedItems = [...items];
  
  return sortedItems.sort((a, b) => {
    let aValue: any;
    let bValue: any;
    
    if (fieldMap && fieldMap[sortBy]) {
      const mapper = fieldMap[sortBy];
      if (typeof mapper === 'function') {
        aValue = mapper(a);
        bValue = mapper(b);
      } else {
        aValue = (a as any)[mapper];
        bValue = (b as any)[mapper];
      }
    } else {
      aValue = (a as any)[sortBy];
      bValue = (b as any)[sortBy];
    }
    
    // Handle dates
    if (aValue instanceof Date && bValue instanceof Date) {
      aValue = aValue.getTime();
      bValue = bValue.getTime();
    }
    
    // Handle strings
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }
    
    let comparison = 0;
    if (aValue > bValue) {
      comparison = 1;
    } else if (aValue < bValue) {
      comparison = -1;
    }
    
    return sortOrder === 'desc' ? -comparison : comparison;
  });
}

/**
 * Paginates an array of items
 */
export function paginate<T>(
  items: T[],
  page: number,
  limit: number
): { items: T[]; total: number; page: number; limit: number; totalPages: number } {
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedItems = items.slice(startIndex, endIndex);
  
  return {
    items: paginatedItems,
    total: items.length,
    page,
    limit,
    totalPages: Math.ceil(items.length / limit)
  };
}

/**
 * Calculates distance between two coordinates (Haversine formula)
 */
export function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLon = toRadians(lon2 - lon1);
  
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI / 180);
}

/**
 * Debounces a function call
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttles a function call
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}