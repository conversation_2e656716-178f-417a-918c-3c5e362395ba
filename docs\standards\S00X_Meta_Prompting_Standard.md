---
ID: S00X
Title: ESTRATIX Meta-Prompting Standard
Version: 1.0
Status: Draft
ResponsibleCommandOffice: CTO
DateCreated: 2025-06-02
DateUpdated: 2025-06-02
---

# ESTRATIX Meta-Prompting Standard (S00X)

## 1. Introduction

This standard defines the structure and components of meta-prompts used within the ESTRATIX framework. Meta-prompts provide comprehensive contextual information, task directives, tool specifications, and agency guidelines to ESTRATIX agents, enabling them to perform complex tasks autonomously and effectively.

## 2. Core Meta-Prompt Schema

A meta-prompt in ESTRATIX should ideally conform to the following high-level structure. Implementations may use formats like YAML or JSON.

```yaml
MetaPromptID: [Unique ID for this meta-prompt instance]
GeneratedForAgentID: [Target Agent ID]
GeneratedByAgentID: [Agent/System that generated this prompt]
Timestamp: [ISO 8601 Timestamp]

OverallTaskGoal: [Clear, concise statement of the ultimate objective]

ContextualPayload:
  RelevantEstratixDefinitions: # Links or embedded content of Processes, Services, Data Models, etc.
    - type: "ProcessDefinition"
      id: "P001"
      version: "1.1"
      path: "docs/processes/definitions/cpo/P001_DefineProcessItself.md"
  ProjectContext: # Information about the current project, if applicable
    ProjectID: "PROJ_XYZ"
    CurrentPhase: "Development"
  InputData: # Specific data inputs required for the task
    - name: "user_requirements_doc"
      format: "markdown"
      content: "...or path to content..."
  # ... other relevant contextual information

RoleDefinition:
  AgentRole: [e.g., "CodeGenerator", "RiskAssessor", "ProjectPlanner"]
  Responsibilities: 
    - "Generate Python code for FastAPI endpoints."
    - "Ensure generated code adheres to PEP8 standards."
  KeySkillsRequired: ["Python", "FastAPI", "Pydantic", "SQLAlchemy"]

TaskDirectives:
  MainTaskDescription: [Detailed description of the primary task]
  SubTasks:
    - subTaskID: "T1.1"
      description: "Analyze Pydantic models in provided schema file."
      expectedOutcome: "List of model names and fields."
      dependsOn: []
    - subTaskID: "T1.2"
      description: "Generate CRUD endpoint functions for each model."
      expectedOutcome: "Python code for CRUD functions."
      dependsOn: ["T1.1"]
  Deliverables:
    - deliverableID: "D1"
      description: "Python file containing generated FastAPI routes."
      format: "python"
    - deliverableID: "D2"
      description: "Unit tests for generated routes."
      format: "python"
  SuccessMetrics:
    - metricID: "M1"
      description: "All generated code passes linter checks (e.g., Flake8)."
    - metricID: "M2"
      description: "Unit test coverage for generated code is >= 80%."
    - metricID: "M3"
      description: "Generated endpoints correctly perform CRUD operations as verified by integration tests (to be run by a separate agent/process)."
  ConstraintsAndGuidelines:
    - constraintID: "C1"
      description: "Adhere to coding standards defined in docs/standards/S00_CodingStandard.md."
    - constraintID: "C2"
      description: "Use asynchronous functions (async/await) for all I/O bound operations."

ToolingSpecification:
  AvailableTools: # List of globally registered tools available for this task
    - toolName: "view_file"
      purpose: "Read content of existing project files (e.g., Pydantic models, templates)."
    - toolName: "edit_file" # Could also be write_to_file or replace_file_content
      purpose: "Write or modify project files with generated code or changes."
    - toolName: "run_command"
      purpose: "Execute shell commands for linting, testing, or building code."
    - toolName: "codebase_search"
      purpose: "Find relevant code snippets, function definitions, or class structures."
    # ... other ESTRATIX core tools
  CustomToolDefinitions: # Task-specific or agent-specific tools
    - toolName: "generate_fastapi_router_template"
      description: "Generates a boilerplate FastAPI APIRouter structure for a given resource name."
      inputSchema:
        type: "object"
        properties:
          resource_name: { type: "string", description: "Name of the resource (e.g., 'user', 'product')." }
          model_name: { type: "string", description: "Pydantic model name for request/response." }
        required: ["resource_name", "model_name"]
      outputDescription: "String containing Python code for the APIRouter."
  ToolUsageProtocols:
    DefaultErrorHandling: "Retry up to 2 times with exponential backoff. If still failing, log error and escalate to HITL_CodeReview_Squad."
    ResultValidation: "For 'edit_file', agent should internally verify that the file content matches the intended change before marking the tool use as successful."
    ToolChainingExample: "Output of 'view_file' (Pydantic model content) can be directly used as input for 'generate_fastapi_router_template'."

AgencyPrinciples:
  GoalDecompositionHints:
    - "First, understand the data models. Second, generate core logic. Third, add error handling. Fourth, write tests."
  DecisionMakingAuthority:
    - "Agent can autonomously choose between `edit_file` and `replace_file_content` based on the scope of changes."
    - "Agent can decide on the specific structure of unit tests as long as SuccessMetrics are met."
  LearningDirectives:
    - "Log all `run_command` invocations for linting/testing and their success/failure, including error messages, to `logs/agent_dev_task_[TaskID].json` for later analysis."
  EscalationPathways:
    - trigger: "If `run_command` for unit tests fails after 2 retries with fixes."
      action: "Package current code state, relevant logs, and meta-prompt. Escalate to `HITL_CodeReview_Squad` with priority 'Medium'."
    - trigger: "If confidence in generated code for a critical function is below 70% (self-assessed)."
      action: "Flag the specific function, provide rationale, and request review from `HITL_SeniorDeveloper_Agent` before proceeding with integration."

OutputSchemaDefinition: # Expected structure of the final output from the agent for this meta-prompt
  type: "object"
  properties:
    generated_files_paths:
      type: "array"
      items: { type: "string" }
    test_results_summary_path: { type: "string" }
    issues_encountered: 
      type: "array"
      items: { type: "string" }
    escalation_triggered: { type: "boolean" }
  required: ["generated_files_paths", "test_results_summary_path", "issues_encountered", "escalation_triggered"]

## 3. Tool Definition and Usage

### 3.1. Global Tools (`AvailableTools`)
Agents are provided with a list of ESTRATIX core tools (e.g., `view_file`, `edit_file`, `run_command`, `codebase_search`, `grep_search`, `find_by_name`, `create_memory`, `mcpX_...` tools) relevant to their task. The `purpose` field guides the agent in selecting the appropriate tool.

### 3.2. Custom Tools (`CustomToolDefinitions`)
For specialized tasks, meta-prompts can define custom tools. These definitions must include:
*   `toolName`: A unique identifier for the tool within the context of the prompt.
*   `description`: A clear explanation of what the tool does, which helps the LLM understand when and how to use it.
*   `inputSchema`: A JSON schema defining the expected input parameters for the tool. This allows for structured tool calls.
*   `outputDescription`: A description of what the tool is expected to return. (Future versions might include an `outputSchema`).

This is analogous to tool/function calling in OpenAI's API or Anthropic's SDKs, where the LLM can request to call a tool with specific arguments matching the schema.

### 3.3. Tool Usage Protocols
This section provides agents with rules and best practices for using tools, including error handling, retries, validation of results, and how to chain tool outputs.

## 4. Agency Principles in Meta-Prompts
To foster greater autonomy and effectiveness:
*   **GoalDecompositionHints:** Guide the agent in breaking down complex tasks.
*   **DecisionMakingAuthority:** Define areas where the agent can make autonomous choices.
*   **LearningDirectives:** Instruct agents on how to capture data from their operations for future improvement or analysis.
*   **EscalationPathways:** Clearly define when and how an agent should seek human intervention or escalate issues.

## 5. Human-in-the-Loop (HITL) Integration
Escalation pathways are a key mechanism for HITL. Meta-prompts should specify the conditions for escalation and the target HITL agent, squad, or process.

## 6. Versioning and Evolution
This standard will be versioned. As ESTRATIX evolves, new fields and structures may be added to the meta-prompt schema to support more advanced agent capabilities.

```
