# CKO_F001: ExternalKnowledgeIngestionAndCuration

## 1. Metadata

* **Flow ID:** CKO_F001
* **Flow Name:** ExternalKnowledgeIngestionAndCuration
* **Version:** 1.1
* **Status:** Definition
* **Last Updated:** 2025-05-27
* **Owner/SME:** Chief Knowledge Officer (CKO)
* **Responsible Command Office:** CKO
* **SOP References:** (e.g., CKO_SOP_001: Knowledge Source Vetting, CKO_SOP_002: Data Curation Standards)
* **SLOs/SLAs:** (e.g., 99% uptime for ingestion agents; 95% of critical alerts processed within 1 hour)

## 2. Purpose & Goal

* **Purpose:** To systematically identify, acquire, process, and curate relevant external knowledge from diverse sources to build and maintain a high-quality, up-to-date ESTRATIX knowledge base.
* **Goal(s):**
  * Increase the volume of relevant ingested knowledge assets by 20% quarterly.
  * Ensure 90% of ingested knowledge assets are tagged, categorized, and summarized within 4 hours of acquisition.
  * Maintain a knowledge base freshness score of 95% (i.e., 95% of critical domain knowledge updated/validated within the last 30 days).
  * Achieve a 90% accuracy rate in automated summarization and entity extraction as validated by human review sampling.

## 3. Scope

* **In Scope:** Identification of new external knowledge sources, automated and manual data acquisition from approved web sources/APIs/feeds, data cleaning and normalization, automated summarization, entity extraction, keyword tagging, sentiment analysis (where applicable), categorization according to ESTRATIX ontology, storage in the vector database, and basic notification mechanisms for newly ingested critical information.
* **Out of Scope:** In-depth analysis or synthesis of knowledge beyond initial curation (handled by downstream flows like CKO_F002), creation of original research content, direct proposal generation (this flow provides inputs to proposal flows).

## 4. Triggers

* Scheduled intervals (e.g., hourly for high-velocity news feeds, daily for journals, weekly for specific industry reports).
* Ad-hoc requests from COs or other ESTRATIX Flows (e.g., `CPO_FXXX_StrategicProposalDevelopment` needing specific market data, submitted via `CKO_M002_KnowledgeIngestionRequest`).
* Detection of new relevant information sources by `CKO_A001_KnowledgeScoutAgent`.
* Manual trigger by CKO personnel.

## 5. Inputs

* `CKO_M001_KnowledgeSourceRegistry`: Data Model containing list of approved/subscribed knowledge sources (RSS feeds, APIs, databases, journals, websites, news outlets), including access credentials, update frequency, and relevance scores.
* `CKO_M002_KnowledgeIngestionRequest`: Data Model for ad-hoc requests, specifying topics, keywords, desired sources, and urgency.
* Knowledge ingestion parameters (keywords, topics of interest, exclusion criteria) defined by CKO and other COs.
* ESTRATIX Ontology/Taxonomy for categorization and tagging.

## 6. Outputs

* `CKO_M003_CuratedKnowledgeAsset`: Data Model instances stored in the ESTRATIX Vector Database (e.g., QdrantDB). Includes original content, summary, extracted entities, keywords, sentiment, source, ingestion date, relevance score, and links to related internal concepts.
* Notifications to relevant COs/Flows about newly ingested critical information (e.g., via an ESTRATIX Notification Service).
* Updated `CKO_M001_KnowledgeSourceRegistry` (with performance/relevance metrics for each source).
* Logs of ingestion activities, errors, and curation steps.

## 7. Constituent Work Units & Sequence

1. `CKO_P002: Identify & Vet Knowledge Sources` (Process: Ongoing and event-driven, updates `CKO_M001`)
2. `CKO_T001: Configure & Schedule Data Scrapers/Connectors` (Task: Based on `CKO_M001` and `CKO_M002`)
3. `CKO_T002: Execute Knowledge Acquisition` (Task: Automated agents fetching data from sources)
4. `CKO_P005: Process & Normalize Raw Knowledge` (Process: Includes sub-tasks like HTML stripping, text extraction, deduplication, format conversion)
5. `CKO_P006: Enrich & Contextualize Knowledge` (Process: Includes summarization, NER, tagging, sentiment, vectorization)
6. `CKO_T003: Store Curated Knowledge Asset` (Task: Store enriched data into `CKO_M003` in Vector DB)
7. `CKO_T004: Notify Stakeholders of Critical Ingestion` (Task: Based on predefined rules and asset criticality)

### 7.1. Acceptance Criteria for Key Steps

* [ ] **CKO_P002:** `CKO_M001` is updated with vetted sources and relevant metadata (access methods, frequency, initial relevance).
* [ ] **CKO_T001:** Scrapers/connectors are configured correctly and scheduled according to source update frequency.
* [ ] **CKO_T002:** Knowledge acquisition tasks run reliably and handle common errors (e.g., source unavailability) gracefully.
* [ ] **CKO_P005:** Raw data is consistently cleaned and normalized into a standard internal format.
* [ ] **CKO_P006:** Enrichment services (summarization, NER, tagging) operate with defined accuracy levels and integrate seamlessly.
* [ ] **CKO_T003:** `CKO_M003` assets are correctly structured, vectorized, and stored with all required metadata.
* [ ] **CKO_T004:** Notifications are triggered accurately for critical assets to the correct stakeholders.

## 8. Key Roles & Agents Involved

* **`CKO_A001_KnowledgeScoutAgent`:** Identifies and suggests new knowledge sources.
* **`CKO_A002_DataAcquisitionAgent`:** Executes scraping and API connection tasks.
* **`CKO_A003_NLProcessingAgent`:** Performs summarization, entity extraction, tagging, and other NLP tasks.
* **`CKO_A004_VectorStorageAgent`:** Manages interaction with the QdrantDB for storing and retrieving knowledge assets.
* **`CKO_A005_NotificationAgent`:** Handles alerting and notifications to stakeholders.
* **Chief Knowledge Officer (CKO) (Human Role):** Oversees the flow, sets strategic direction for knowledge acquisition, resolves escalations.

## 9. Tools & MCPs Utilized

* Web scraping libraries (e.g., BeautifulSoup, Scrapy, Playwright)
* API client libraries (e.g., Requests)
* NLP libraries/services (e.g., spaCy, NLTK, Hugging Face Transformers, Azure Cognitive Services, Google Cloud AI Platform NLP)
* QdrantDB (Vector Database MCP or direct integration)
* Workflow orchestration engine (e.g., CrewAI, Apache Airflow, Prefect, or a custom ESTRATIX tool)
* Notification systems (e.g., ESTRATIX Notification Service, email gateways, Slack integration)
* Fetcher MCP (`mcp11_fetch_url`, `mcp11_fetch_urls`)
* Containerization (Docker) and Orchestration (Kubernetes) for deploying agents/services.

## 10. Success Metrics & KPIs

* **Volume of Relevant Knowledge Assets Ingested:** Number of `CKO_M003` assets created per day/week/month, filtered by relevance score > threshold.
* **Knowledge Source Coverage:** Percentage of identified key domains/topics covered by active knowledge sources.
* **Ingestion Pipeline Latency:** Average time from information availability at source to its availability as a curated asset in Vector DB.
* **Curation Accuracy:** Percentage of assets correctly summarized, tagged, and categorized (measured by human spot-checks or automated validation).
* **Knowledge Asset Quality Score:** Composite score based on completeness, accuracy, relevance, and timeliness.
* **Vector DB Query Performance:** Average latency for search queries against the curated knowledge.
* **Stakeholder Satisfaction:** Feedback from COs and other flows on the relevance and timeliness of ingested knowledge.

## 11. Dependencies

* **Upstream Flows/Processes:** Initial setup of ESTRATIX Ontology/Taxonomy; `SYS_P001_SystemConfiguration` for access credentials and tool setup.
* **Downstream Flows/Processes:** `CKO_F002_TrendAnalysisAndOpportunityScouting`, `CPO_FXXX_StrategicProposalDevelopment`, various CO-specific analytical flows, `SEC_FXXX_ThreatIntelligenceAnalysis`.

## 12. Exception Handling & Escalation

* **Common Issues:** Source unavailability, changes in website structure breaking scrapers, API rate limits exceeded, NLP model errors, data quality issues.
* **Handling:** Automated retries for transient errors, fallback mechanisms, dead-letter queues for unprocessable data, alerts to CKO personnel for manual intervention.
* **Escalation:** Critical pipeline failures escalate to CKO and CTO. Persistent source issues escalate to CKO for source re-vetting.
* **Logging Strategy:** Comprehensive logging at each step of the flow (acquisition, processing, enrichment, storage) to a centralized logging platform (e.g., ELK stack, Splunk) filterable by Flow ID, Agent ID, Source ID, and timestamp.

## 13. PDCA (Plan-Do-Check-Act) / Continuous Improvement

* **Review Cadence:** Monthly review of pipeline performance and KPIs; Quarterly strategic review of source effectiveness and topic coverage.
* **Responsible for Review:** CKO, Lead Data Engineer.
* **Key Metrics for Review:** All metrics from Section 10.
* **Process for Incorporating Improvements:** Proposals for new sources, tool upgrades, or process changes are submitted via `CKO_PRXXX_KnowledgeFlowEnhancement`. Approved changes are implemented by data engineering and CKO teams.
* **Lessons Learned Integration:** A `CKO_LM001_IngestionFlowLessonsLearned` document is maintained. Insights are reviewed monthly and used to update SOPs, agent configurations, and training for human-in-the-loop roles.

### 13.1. Lessons Learned & Iterations

* (e.g., Iteration 1.1: Improved scraper resilience for dynamic websites by adopting Playwright. Iteration 1.2: Enhanced NLP model for more accurate summarization of technical documents.)
* Link to `docs/lessons_learned/cko/CKO_LM001_IngestionFlowLessonsLearned.md`

## 14. Agentic Framework Mapping

* **Windsurf Workflows:** This ESTRATIX Flow could be orchestrated by a master Windsurf workflow that triggers sub-workflows for each constituent process/task.
* **CrewAI Conceptual Mapping:**
  * **Knowledge Ingestion Crew:**
    * `KnowledgeSourceScoutAgent`: Uses web search tools, monitors industry news to find new sources. Task: Identify and propose new knowledge sources.
    * `WebScrapingAgent`: Executes scraping tasks using tools like BeautifulSoup/Scrapy. Task: Fetch content from specified URLs.
    * `APIAgent`: Interacts with external APIs. Task: Retrieve data from API endpoints.
    * `DataCleaningAgent`: Normalizes and cleans raw data. Task: Process raw text/HTML into clean text.
    * `NLPSummarizationAgent`: Generates summaries. Task: Create concise summary of text.
    * `NLPEntityExtractionAgent`: Extracts entities and keywords. Task: Identify key entities and tag content.
    * `VectorDBStorageAgent`: Interacts with QdrantDB. Task: Store enriched asset in database.
    * `NotificationAgent`: Alerts stakeholders. Task: Send notification about new critical asset.
* **Pydantic-AI Conceptual Mapping:**
  * Pydantic models for `CKO_M001`, `CKO_M002`, `CKO_M003`.
  * A Pydantic-AI graph could represent the pipeline: SourceInputNode -> FetchNode -> CleanNode -> EnrichNode (with sub-graphs for Summarize, NER, Tag) -> StorageNode -> NotifyNode.
* **Aider Integration:** Aider can assist in generating and maintaining the Python code for scrapers, API clients, NLP processing scripts, and agent definitions for CrewAI.
* **Other Frameworks:** (e.g., Google ADK could be used to build more complex, stateful ingestion agents; PocketFlow could define the data structures and simple processing steps.)

## 15. Notes & Diagram Link

* This flow is foundational for enabling data-driven decision-making and proactive strategy development within ESTRATIX.
* Continuous monitoring and adaptation of knowledge sources are critical due to the dynamic nature of external information.
* **Diagram:** See the visual representation of this ESTRATIX Flow in `docs/flows/cko/CKO_F001_ExternalKnowledgeIngestionAndCuration.mmd` (to be created).

## 16. Revision History

| Version | Date       | Author     | Changes                                                                                                |
| :------ | :--------- | :--------- | :----------------------------------------------------------------------------------------------------- |
| 1.0     | YYYY-MM-DD | KNO        | Initial Definition of the External Knowledge Ingestion and Curation Flow. Placeholder for detailed content. |
| 1.1     | 2025-05-27 | Cascade AI | Refactored from KNO_F001 to CKO_F001. Original KNO version 1.0. Updated internal references. |
