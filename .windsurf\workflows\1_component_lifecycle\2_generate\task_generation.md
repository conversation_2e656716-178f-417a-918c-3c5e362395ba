---
description: "Guides the agentic generation of a framework-specific YAML file for a defined ESTRATIX Task."
---

# ESTRATIX Workflow: Generate Task YAML

**Objective:** To generate a single, framework-specific task definition file (e.g., a `.yaml` for CrewAI) based on a pre-defined task definition, ensuring it adheres to the project's naming and directory conventions.

**Agent Persona:** `AGENT_MasterBuilder_Expert`

**Governing Rules**:

- `docs/standards/naming_conventions.md`

## 1. Prerequisites

- A `Task_ID` for an existing, defined task (e.g., `t001`).
- The parent `Process_ID` (acting as the `crew_id`) that this task belongs to (e.g., `p019`).
- The owning `Command_Office` acronym (e.g., `cpo`).
- The target `Agentic_Framework` (e.g., `crewAI`).

## Workflow Steps

1. **Identify Target Task**
   - **Action**: Obtain the `Task_ID`, parent `Process_ID`, `Command_Office`, and `Agentic_Framework` from the user or the orchestrating workflow (e.g., `process_generation`).

2. **Retrieve Task Definition**
   - **Action**: Read the task's conceptual definition markdown file to get the `Task_Name`, `description`, and `expected_output`.
   - **Tool**: `view_line_range`
   - **Path**: `docs/tasks/[Command_Office]/[Process_ID]_[Task_ID]_[Task_Name_PascalCase].md`
   - **Goal**: Extract the core attributes for the task's YAML file.

3. **Scaffold Task YAML File**
   - **Action**: Create the task's YAML definition file.
   - **Tool**: `write_to_file`
   - **Path**: `src/infrastructure/frameworks/[Agentic_Framework]/tasks/[Command_Office]/[Process_ID]_[Task_ID]_[Task_Name_snake_case].yaml`
   - **Content**: A YAML structure containing the task's `description` and `expected_output`.
   - **Example**:

     ```yaml
     description: "Analyze the given data and provide insights."
     expected_output: "A report summarizing the key findings."
     ```

4. **Update Task Matrix**
   - **Action**: Update the task matrix to link to the new YAML implementation file.
   - **Tool**: `replace_file_content` or a dedicated matrix update tool.
   - **Guidance**: Change the status to `Implemented` and add the correct path to the `Implementation Path` column.

5. **Confirmation**
   - **Action**: Confirm to the user that the task's YAML file has been successfully generated.
