# ESTRATIX Agentic Rules: MongoDB Persistence

---

## 1. Overview

These rules govern the use of MongoDB as a persistence layer within the ESTRATIX framework. They are designed to ensure data integrity, performance, security, and scalability. All agents interacting with or designing systems that use MongoDB must adhere to these standards.

## 2. Core Principles

- **Schema on Read, Defined in Code**: While MongoDB is flexible, all data models **must** be explicitly defined using Pydantic models within the `src/domain/models/` directory. This ensures a clear, version-controlled source of truth for data structures.
- **Security First**: Never store credentials or connection strings directly in code. Use environment variables managed by a secure configuration system.
- **Performance by Design**: Indexes are not optional. Design data access patterns first, then define indexes to support them.

## 3. Rules

### 3.1. Data Modeling (Pydantic)
- **Rule R-DA-003.1**: Every MongoDB collection must have a corresponding Pydantic model.
- **Rule R-DA-003.2**: Use Pydantic's `Field` for detailed validation, including default values, min/max length, and regex patterns.
- **Rule R-DA-003.3**: For relationships, prefer embedding for one-to-one or one-to-few relationships. Use referencing (storing `ObjectId`) for one-to-many or many-to-many relationships to avoid large document sizes.

### 3.2. Indexing
- **Rule R-DA-003.4**: All queries must be supported by an index. Before deploying new features, use the `.explain("executionStats")` method to verify that queries are using indexes and not performing collection scans (COLLSCAN).
- **Rule R-DA-003.5**: Create compound indexes to satisfy multiple query fields, following the ESR (Equality, Sort, Range) rule for field order.
- **Rule R-DA-003.6**: Utilize Time-To-Live (TTL) indexes for collections that store temporary data (e.g., session data, logs).

### 3.3. Querying & Aggregation
- **Rule R-DA-003.7**: When retrieving documents, project only the necessary fields to minimize network overhead.
- **Rule R-DA-003.8**: Use the aggregation framework for complex data processing on the server side to reduce the amount of data transferred to the client.

### 3.4. Security & Connection
- **Rule R-DA-003.9**: The MongoDB connection URI must be loaded from environment variables, never hardcoded.
- **Rule R-DA-003.10**: Enforce the principle of least privilege for database users. Application users should not have administrative privileges.

## 4. Enforcement

- **Agentic Enforcement**: Agents generating data access code will be primed with these rules to automatically generate compliant Pydantic models and query patterns.
- **Code Review**: All pull requests containing MongoDB interactions must be reviewed for index usage, model correctness, and security compliance.
- **Static Analysis**: Linters and static analysis tools will be configured to check for hardcoded connection strings.
