{"common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "cancel": "Annuler", "confirm": "Confirmer", "save": "Enregistrer", "edit": "Modifier", "delete": "<PERSON><PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "previous": "Précédent", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "sort": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view": "Voir", "download": "Télécharger", "upload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connect": "Connecter", "disconnect": "Déconnecter", "wallet": "Portefeuille", "balance": "Solde", "transaction": "Transaction", "transactions": "Transactions", "address": "<PERSON><PERSON><PERSON>", "amount": "<PERSON><PERSON>", "fee": "<PERSON><PERSON>", "total": "Total", "status": "Statut", "pending": "En attente", "completed": "<PERSON><PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON>", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "date": "Date", "time": "<PERSON><PERSON>", "name": "Nom", "email": "E-mail", "phone": "Téléphone", "location": "Emplacement", "price": "Prix", "rating": "Note", "reviews": "<PERSON><PERSON>", "availability": "Disponibilité", "book": "Réserver", "booked": "Réservé", "booking": "Réservation", "bookings": "Réservations", "service": "Service", "services": "Services", "provider": "Fournisseur", "providers": "Fournisseurs", "property": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": "Propriétés", "dashboard": "Tableau de bord", "profile": "Profil", "settings": "Paramètres", "help": "Aide", "support": "Support", "contact": "Contact", "about": "À propos", "terms": "Conditions de service", "privacy": "Politique de confidentialité", "logout": "Déconnexion", "login": "Connexion", "register": "S'inscrire", "signup": "S'inscrire", "signin": "Se connecter"}, "navigation": {"home": "Accueil", "marketplace": "<PERSON><PERSON>", "services": "Services", "dashboard": "Tableau de bord", "tokens": "Jet<PERSON>", "nft": "NFT", "defi": "<PERSON><PERSON><PERSON>", "admin": "Administrateur", "provider": "Portail fournisseur", "content": "Studio de contenu", "tokenization": "Tokenisation d'actifs", "analytics": "Analyses"}, "home": {"hero": {"title": "Services immobiliers de luxe", "subtitle": "Alimenté par Web3", "description": "Plateforme révolutionnaire connectant les propriétaires avec des fournisseurs de services premium grâce à la technologie blockchain, l'appariement alimenté par l'IA et les récompenses tokenisées.", "cta_primary": "Réserver des services maintenant", "cta_secondary": "<PERSON> le marché"}, "stats": {"properties_managed": "Propriétés g<PERSON>rées", "service_providers": "Fournisseurs de services", "lux_tokens_staked": "Jetons LUX mis en jeu", "customer_satisfaction": "Satisfaction client"}, "services": {"title": "Services immobiliers premium", "description": "Découvrez notre suite complète de services immobiliers de luxe, chacun alimenté par une technologie de pointe et livré par des professionnels certifiés.", "property_management": {"title": "Gestion immobilière", "description": "Supervision complète des propriétés avec planification de maintenance alimentée par l'IA"}, "luxury_cleaning": {"title": "Nettoyage de luxe", "description": "Services de nettoyage premium avec produits écologiques et traitement gants blancs"}, "landscaping": {"title": "Aménagement paysager et design", "description": "Aménagement paysager professionnel avec design durable et irrigation intelligente"}, "remediation": {"title": "Remédiation immobilière", "description": "Services experts de restauration pour dégâts d'eau, moisissures et problèmes structurels"}, "analytics": {"title": "Analyses d'investissement", "description": "Évaluation immobilière alimentée par l'IA et analyse d'opportunités d'investissement"}, "crypto_payments": {"title": "Paiements crypto", "description": "Paiements Web3 transparents avec récompenses de jetons LUX et certificats NFT"}, "learn_more": "En savoir plus"}, "web3": {"title": "Écosystème immobilier alimenté par Web3", "description": "Découvrez l'avenir des services immobiliers avec la technologie blockchain, les récompenses tokenisées et l'intégration de la finance décentralisée.", "lux_rewards": {"title": "Récompenses de jetons LUX", "description": "Gagnez des jetons LUX pour chaque réservation de service, amélioration de propriété et participation à la plateforme. Mettez en jeu des jetons pour des avantages premium."}, "tokenization": {"title": "Tokenisation immobilière", "description": "Tokenisez les actifs immobiliers pour la propriété fractionnée, la distribution transparente des revenus locatifs et l'investissement immobilier liquide."}, "nft_certificates": {"title": "Certificats NFT", "description": "Recevez des certificats NFT vérifiables pour les améliorations de propriétés, les achèvements de services et les niveaux d'adhésion."}, "dashboard_preview": {"title": "Aperçu du tableau de bord des jetons", "lux_balance": "Solde LUX", "staked_amount": "<PERSON><PERSON> mis en jeu", "rewards_earned": "Récompenses gagnées", "nfts_owned": "NFT possédés", "access_dashboard": "Accéder au tableau de bord des jetons"}}, "cta": {"title": "Prêt à transformer votre expérience immobilière ?", "description": "Rejoignez des milliers de propriétaires qui font confiance à Luxcrafts pour des services premium et des récompenses Web3 innovantes.", "start_booking": "Commencer à réserver des services", "become_provider": "<PERSON><PERSON><PERSON>"}}, "payment": {"web2": {"title": "Banque traditionnelle", "description": "Payez avec cartes de crédit, virements bancaires et paiements ACH", "available_in": "Disponible aux États-Unis", "methods": {"credit_card": "Carte de <PERSON>", "bank_transfer": "Virement bancaire", "ach": "Paiement ACH", "paypal": "PayPal"}}, "web3": {"title": "Paiements en cryptomonnaie", "description": "Payez avec Bitcoin, Ethereum, stablecoins et jetons LUX", "available_globally": "Disponible mondialement", "methods": {"bitcoin": "Bitcoin", "ethereum": "Ethereum", "usdc": "USDC", "usdt": "USDT", "lux_token": "Jeton LUX"}}, "toggle": {"web2": "Banque traditionnelle", "web3": "Paiements crypto", "switch_to_web2": "Passer à la banque traditionnelle", "switch_to_web3": "Passer aux paiements crypto"}}, "accessibility": {"skip_to_content": "Aller au contenu principal", "menu": "<PERSON><PERSON>", "close_menu": "<PERSON><PERSON><PERSON> le menu", "open_menu": "<PERSON><PERSON><PERSON><PERSON><PERSON> le menu", "language_selector": "<PERSON><PERSON><PERSON><PERSON> de langue", "current_language": "Langue actuelle", "change_language": "Changer de langue", "high_contrast": "Mode contraste élevé", "normal_contrast": "Mode contraste normal", "increase_font_size": "Augmenter la taille de police", "decrease_font_size": "Diminuer la taille de police", "reset_font_size": "Réinitialiser la taille de police", "screen_reader_content": "Contenu pour lecteur d'écran", "keyboard_navigation": "Navigation au clavier disponible", "alternative_text": "Texte alternatif", "loading_content": "Chargement du contenu, veuil<PERSON><PERSON> patienter", "error_occurred": "Une erreur s'est produite", "success_message": "Action terminée avec succès", "required_field": "Champ requis", "invalid_input": "<PERSON><PERSON> invalide", "form_validation_error": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs dans le formulaire"}, "errors": {"generic": "Une erreur inattendue s'est produite. Veuillez réessayer.", "network": "Erreur réseau. Veuillez vérifier votre connexion.", "validation": "Veuillez vérifier votre saisie et réessayer.", "authentication": "Échec de l'authentification. Veuillez vous reconnecter.", "authorization": "Vous n'avez pas la permission d'effectuer cette action.", "not_found": "La ressource demandée n'a pas été trouvée.", "server_error": "Erreur serveur. Veuillez réessayer plus tard.", "wallet_connection": "Échec de la connexion du portefeuille. Veuillez réessayer.", "transaction_failed": "La transaction a échoué. Veuillez réessayer.", "insufficient_funds": "Fonds insuffisants pour cette transaction.", "gas_estimation_failed": "Échec de l'estimation des frais de gaz.", "contract_interaction_failed": "Échec de l'interaction avec le contrat intelligent.", "token_approval_failed": "Échec de l'approbation du jeton.", "nft_mint_failed": "Échec de la frappe du NFT.", "staking_failed": "Échec de l'opération de mise en jeu.", "unstaking_failed": "Échec de l'opération de retrait de mise.", "swap_failed": "Échec de l'échange de jetons.", "liquidity_failed": "Échec de l'opération de liquidité.", "payment_processing_failed": "Échec du traitement du paiement.", "service_booking_failed": "Échec de la réservation de service."}}