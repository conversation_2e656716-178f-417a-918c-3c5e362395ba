# ESTRATIX Rules Matrix

---

## 1. Overview

This matrix serves as a central registry for all ESTRATIX operational, agentic, and governance rules. It provides visibility into the rules that govern system behavior, agent interactions, data handling, security, and compliance.

---

## 2. Rules Inventory

| Rule ID | Rule Name/Category | Type (e.g., Operational, Agentic, Security, Compliance, Workflow) | Purpose & Description | Source/Location (e.g., `.windsurf/rules`, specific config files, document links) | Enforcement Mechanism | Status     | Notes |
|---------|--------------------|-------------------------------------------------------------------|-----------------------|------------------------------------------------------------------------------------|-----------------------|------------|-------|
| R-BE-001 | Backend Development Practices | Agentic, Workflow | Defines coding standards for Python backend development, including SOLID, DRY, naming conventions, and type hinting. | [/.windsurf/rules/backend/python/](./.windsurf/rules/backend/python/) | Agentic System Prompt, Code Review, CI/CD Linting | Active | Governs all Python-based services and components. |
| R-FE-001 | Frontend Development Practices | Agentic, Workflow | Defines standards for JavaScript frontend development, including component architecture, state management, and styling. | [/.windsurf/rules/frontend/javascript/](./.windsurf/rules/frontend/javascript/) | Agentic System Prompt, Code Review, CI/CD Linting | Active | Governs all UI components and web applications. |
| R-DO-001 | DevOps & IaC Practices | Operational, Agentic | Defines standards for Infrastructure as Code using Terraform, including state management, security, and CI/CD pipelines. | [/.windsurf/rules/devops/terraform/](./.windsurf/rules/devops/terraform/) | Agentic System Prompt, CI/CD Pipeline (tfsec, checkov), Peer Review | Active | Ensures consistent and secure infrastructure management. |
| R-DA-001 | Data & Vector DB Standards | Operational, Agentic | Defines standards for using vector databases, covering schema design, indexing, and querying for RAG. | [/.windsurf/rules/data/vector_db/](./.windsurf/rules/data/vector_db/) | Agentic System Prompt, Data Governance Review | Active | Critical for knowledge management and AI capabilities. |
| R-TE-001 | Testing Practices | Workflow, Quality | Defines the ESTRATIX testing methodology, including the testing pyramid (Unit, Integration, E2E) and TDD. | [/.windsurf/rules/testing/](./.windsurf/rules/testing/) | CI/CD Pipeline, Code Coverage Gates, Peer Review | Active | Ensures quality and reliability of all components. |
| R-BE-002 | FastAPI Response Model | Agentic, Workflow | Enforce FastAPI routes have a 'response_model' to ensure consistent API contracts. | [/.windsurf/rules/backend/python/fastapi_response_model.md](./.windsurf/rules/backend/python/fastapi_response_model.md) | Agentic System Prompt, Static Analysis | Active | Ensures API stability and auto-documentation. |
| R-FE-002 | Vue Component Naming | Agentic, Workflow | Enforce all Vue components define a 'name' property for better debugging and dev tools integration. | [/.windsurf/rules/frontend/javascript/vue_component_naming.md](./.windsurf/rules/frontend/javascript/vue_component_naming.md) | Agentic System Prompt, Static Analysis, Code Review | Active | Improves debugging and Vue Devtools integration. |
| R-DO-002 | Terraform Required Tags | Operational, Agentic | Enforce all AWS resources managed by Terraform include a standard set of ESTRATIX tags for cost tracking and ownership. | [/.windsurf/rules/devops/terraform/required_tags.md](./.windsurf/rules/devops/terraform/required_tags.md) | Agentic System Prompt, CI/CD Static Analysis | Active | Critical for cloud governance and cost tracking. |
| R-DA-002 | Vector DB Collection Naming | Operational, Agentic | Enforce a consistent naming convention for all vector database collections to ensure clarity and prevent conflicts. | [/.windsurf/rules/data/vector_db/collection_naming.md](./.windsurf/rules/data/vector_db/collection_naming.md) | Agentic System Prompt, Code Review | Active | Ensures clean and scalable data architecture. |
| R-DA-003 | MongoDB Persistence Rules | Agentic, Data | Defines standards for data modeling, indexing, and access patterns in MongoDB. | [/.windsurf/rules/data/persistence/mongodb/mongodb_persistence_rules.md](./.windsurf/rules/data/persistence/mongodb/mongodb_persistence_rules.md) | Agentic System Prompt, Code Review | Active | Ensures performant and secure data layer. |
| R-FE-003 | Frontend Component Design | Agentic, Workflow | Defines standards for creating modular, reusable, and accessible UI components. | [/.windsurf/rules/frontend/javascript/component_design_rules.md](./.windsurf/rules/frontend/javascript/component_design_rules.md) | Agentic System Prompt, Linting, Code Review | Active | Governs all UI component development. |
| R-BE-003 | Backend Payment & Invoicing | Agentic, Security | Defines secure patterns for backend payment processing and invoicing. | [/.windsurf/rules/backend/payments/payment_processing_rules.md](./.windsurf/rules/backend/payments/payment_processing_rules.md) | Agentic System Prompt, Security Review, Logging | Active | Critical for financial transaction integrity. |
| R-SE-001 | User Authentication Rules | Security, Agentic | Defines security standards for user authentication and session management. | [/.windsurf/rules/security/authentication_rules.md](./.windsurf/rules/security/authentication_rules.md) | Agentic System Prompt, Security Audits | Active | Protects user accounts from unauthorized access. |
| R-DO-003 | Kubernetes Cluster Orchestration | Operational, Agentic, Security | Defines standards for deploying, managing, and scaling applications on Kubernetes. | [/.windsurf/rules/devops/kubernetes/kubernetes_cluster_orchestration_rules.md](./.windsurf/rules/devops/kubernetes/kubernetes_cluster_orchestration_rules.md) | GitOps Agents, SecOps Agents, CI/CD Policies | Active | Foundation for all containerized deployments. |
| R-DO-004 | Load Balancing & Ingress Management | Operational, Agentic, Security | Defines standards for routing external traffic to services within Kubernetes, ensuring security and reliability. | [/.windsurf/rules/devops/load_balancing/load_balancing_ingress_rules.md](./.windsurf/rules/devops/load_balancing/load_balancing_ingress_rules.md) | DevOps Agents, SecOps Agents, GitOps Agents | Active | Manages all inbound HTTP/S traffic. |
| R-DO-005 | Observability & Monitoring | Operational, Agentic | Defines standards for logging, metrics, and tracing to ensure system visibility and enable automated diagnostics. | [/.windsurf/rules/devops/observability/observability_monitoring_rules.md](./.windsurf/rules/devops/observability/observability_monitoring_rules.md) | DevOps Agents, MLOps Agents, CI/CD Pipelines | Active | Foundation for proactive monitoring and incident response. |
| R-AE-001 | Prompt Engineering Standards | Agentic, Workflow, Security | Defines standards for designing, managing, and securing prompts to ensure reliable and secure agent behavior. | [/.windsurf/rules/agentic_engineering/prompt_engineering/prompt_engineering_standards.md](./.windsurf/rules/agentic_engineering/prompt_engineering/prompt_engineering_standards.md) | Prompt Engineering Agents, MLOps Agents, SecOps Agents | Active | Governs the quality and security of all agent interactions. |
| R-DEV-001 | Development Workflow Standards | Operational, Agentic, Workflow | Defines ESTRATIX development philosophy, UV-powered workflows, high-performance patterns, automated quality assurance, and strategic task execution. | [/.trae/rules/devops/development_workflow_standards.md](./.trae/rules/devops/development_workflow_standards.md) / [/.windsurf/rules/devops/development_workflow_standards.md](./.windsurf/rules/devops/development_workflow_standards.md) | Agentic System Prompt, CI/CD Pipeline, Code Review | Active | Foundation for all development activities and agent workflows. |
| R-TEST-001 | Testing Execution Standards | Workflow, Quality, Operational | Defines ESTRATIX's high-momentum TDD philosophy, UV-powered testing workflow, high-performance testing patterns, and agent/tool testing frameworks. | [/.trae/rules/testing/testing_execution_standards.md](./.trae/rules/testing/testing_execution_standards.md) / [/.windsurf/rules/testing/testing_execution_standards.md](./.windsurf/rules/testing/testing_execution_standards.md) | CI/CD Pipeline, Quality Gates, Automated Testing | Active | Ensures comprehensive testing coverage and quality assurance. |
| R-TEST-002 | UV Testing Standards | Workflow, Quality, Operational | Defines UV-based testing execution protocol, test categories, configuration standards, and performance optimization for ESTRATIX testing. | [/.trae/rules/testing/uv_testing_standards.md](./.trae/rules/testing/uv_testing_standards.md) / [/.windsurf/rules/testing/uv_testing_standards.md](./.windsurf/rules/testing/uv_testing_standards.md) | UV Runtime, CI/CD Pipeline, Quality Gates | Active | Mandatory UV usage for all testing activities with consistent environment management. |
| R-PROJ-001 | Project Rules (Trae) | Operational, Agentic, Governance | Core project governance rules for ESTRATIX development, agent coordination, and system operations. | [/.trae/rules/project_rules.md](./.trae/rules/project_rules.md) | Trae Assistant, System Governance | Active | Primary governance framework for Trae assistant operations. |
| R-PROJ-002 | Project Rules (Windsurf) | Operational, Agentic, Governance | Core project governance rules for ESTRATIX development, agent coordination, and system operations (Windsurf variant). | [/.windsurf/project_rules.md](./.windsurf/project_rules.md) / [/.windsurf/rules/project_rules.md](./.windsurf/rules/project_rules.md) | Windsurf Assistant, System Governance | Active | Primary governance framework for Windsurf assistant operations. |

---

## 3. Rule Synchronization Status

### 3.1. Synchronized Rules
The following rules are maintained in both `.trae/rules` and `.windsurf/rules` directories to ensure consistency across different AI assistants:

- **Development Workflow Standards**: Synchronized between Trae and Windsurf
- **Testing Execution Standards**: Synchronized between Trae and Windsurf  
- **UV Testing Standards**: Synchronized between Trae and Windsurf

### 3.2. Assistant-Specific Rules
- **Trae Rules**: Located in `.trae/rules/` - optimized for Trae assistant operations
- **Windsurf Rules**: Located in `.windsurf/rules/` - optimized for Windsurf assistant operations
- **Project Rules**: Maintained separately for each assistant while preserving core governance principles

---

## 4. Maintenance

This matrix should be updated when new rules are defined, existing ones are modified, or their enforcement or location changes. It is crucial for maintaining a clear understanding of the ESTRATIX regulatory landscape.

### 4.1. Rule Naming Conventions
- **R-[CATEGORY]-[NUMBER]**: Standard rule ID format
- **Categories**: DEV (Development), TEST (Testing), PROJ (Project), BE (Backend), FE (Frontend), DO (DevOps), DA (Data), SE (Security), AE (Agentic Engineering)
- **Synchronized rules**: Include both file paths when rules exist in multiple locations

### 4.2. Update Protocol
- Rules changes must be synchronized between `.trae/rules` and `.windsurf/rules` when applicable
- Rule matrix must be updated immediately after rule modifications
- Status changes require documentation in the Notes column
