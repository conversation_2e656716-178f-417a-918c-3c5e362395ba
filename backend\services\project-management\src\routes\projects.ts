import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { authenticateToken, requireRole, requirePermission } from '../middleware/auth';
import { projectService } from '../services/projectService';
import { logger } from '../utils/logger';

const createProjectSchema = {
  body: {
    type: 'object',
    required: ['name', 'description', 'type'],
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', minLength: 1, maxLength: 2000 },
      type: { type: 'string', enum: ['real-estate', 'development', 'investment', 'tokenization'] },
      status: { type: 'string', enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled'], default: 'planning' },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'], default: 'medium' },
      startDate: { type: 'string', format: 'date' },
      endDate: { type: 'string', format: 'date' },
      budget: { type: 'number', minimum: 0 },
      currency: { type: 'string', default: 'USD' },
      location: {
        type: 'object',
        properties: {
          address: { type: 'string' },
          city: { type: 'string' },
          state: { type: 'string' },
          country: { type: 'string' },
          coordinates: {
            type: 'object',
            properties: {
              lat: { type: 'number' },
              lng: { type: 'number' }
            }
          }
        }
      },
      tags: {
        type: 'array',
        items: { type: 'string' }
      },
      metadata: { type: 'object' }
    }
  }
};

const updateProjectSchema = {
  params: {
    type: 'object',
    required: ['projectId'],
    properties: {
      projectId: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    properties: {
      name: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', minLength: 1, maxLength: 2000 },
      status: { type: 'string', enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled'] },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
      startDate: { type: 'string', format: 'date' },
      endDate: { type: 'string', format: 'date' },
      budget: { type: 'number', minimum: 0 },
      currency: { type: 'string' },
      location: {
        type: 'object',
        properties: {
          address: { type: 'string' },
          city: { type: 'string' },
          state: { type: 'string' },
          country: { type: 'string' },
          coordinates: {
            type: 'object',
            properties: {
              lat: { type: 'number' },
              lng: { type: 'number' }
            }
          }
        }
      },
      tags: {
        type: 'array',
        items: { type: 'string' }
      },
      metadata: { type: 'object' }
    }
  }
};

const projectIdSchema = {
  params: {
    type: 'object',
    required: ['projectId'],
    properties: {
      projectId: { type: 'string' }
    }
  }
};

const querySchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'number', minimum: 1, default: 1 },
      limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
      status: { type: 'string', enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled'] },
      type: { type: 'string', enum: ['real-estate', 'development', 'investment', 'tokenization'] },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
      search: { type: 'string' },
      sortBy: { type: 'string', enum: ['name', 'createdAt', 'updatedAt', 'startDate', 'endDate', 'priority'], default: 'createdAt' },
      sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
    }
  }
};

async function projectRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Create project
  fastify.post('/', {
    schema: createProjectSchema,
    preHandler: [authenticateToken, requirePermission(['project:create'])]
  }, async (request, reply) => {
    try {
      const projectData = request.body as any;
      const userId = (request.user as any)?.userId;
      
      const project = await projectService.createProject({
        ...projectData,
        ownerId: userId,
        createdBy: userId
      });
      
      logger.info(`Project created: ${project.id} by user ${userId}`, { projectId: project.id });
      
      return {
        success: true,
        project
      };
    } catch (error) {
      logger.error('Project creation failed:', error);
      return reply.status(500).send({
        error: 'Failed to create project',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get projects list
  fastify.get('/', {
    schema: querySchema,
    preHandler: [authenticateToken]
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const userId = (request.user as any)?.userId;
      const userRole = (request.user as any)?.role;
      
      const result = await projectService.getProjects({
        ...query,
        userId,
        userRole
      });
      
      return {
        success: true,
        ...result
      };
    } catch (error) {
      logger.error('Failed to fetch projects:', error);
      return reply.status(500).send({
        error: 'Failed to fetch projects',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get project by ID
  fastify.get('/:projectId', {
    schema: projectIdSchema,
    preHandler: [authenticateToken]
  }, async (request, reply) => {
    try {
      const { projectId } = request.params as { projectId: string };
      const userId = (request.user as any)?.userId;
      const userRole = (request.user as any)?.role;
      
      const project = await projectService.getProjectById(projectId, userId, userRole);
      
      if (!project) {
        return reply.status(404).send({
          error: 'Project not found',
          code: 'PROJECT_NOT_FOUND'
        });
      }
      
      return {
        success: true,
        project
      };
    } catch (error) {
      logger.error('Failed to fetch project:', error);
      return reply.status(500).send({
        error: 'Failed to fetch project',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Update project
  fastify.put('/:projectId', {
    schema: updateProjectSchema,
    preHandler: [authenticateToken, requirePermission(['project:update'])]
  }, async (request, reply) => {
    try {
      const { projectId } = request.params as { projectId: string };
      const updateData = request.body as any;
      const userId = (request.user as any)?.userId;
      const userRole = (request.user as any)?.role;
      
      const project = await projectService.updateProject(projectId, updateData, userId, userRole);
      
      if (!project) {
        return reply.status(404).send({
          error: 'Project not found',
          code: 'PROJECT_NOT_FOUND'
        });
      }
      
      logger.info(`Project updated: ${projectId} by user ${userId}`);
      
      return {
        success: true,
        project
      };
    } catch (error) {
      logger.error('Project update failed:', error);
      return reply.status(500).send({
        error: 'Failed to update project',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Delete project
  fastify.delete('/:projectId', {
    schema: projectIdSchema,
    preHandler: [authenticateToken, requirePermission(['project:delete'])]
  }, async (request, reply) => {
    try {
      const { projectId } = request.params as { projectId: string };
      const userId = (request.user as any)?.userId;
      const userRole = (request.user as any)?.role;
      
      const success = await projectService.deleteProject(projectId, userId, userRole);
      
      if (!success) {
        return reply.status(404).send({
          error: 'Project not found',
          code: 'PROJECT_NOT_FOUND'
        });
      }
      
      logger.info(`Project deleted: ${projectId} by user ${userId}`);
      
      return {
        success: true,
        message: 'Project deleted successfully'
      };
    } catch (error) {
      logger.error('Project deletion failed:', error);
      return reply.status(500).send({
        error: 'Failed to delete project',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Get project analytics
  fastify.get('/:projectId/analytics', {
    schema: projectIdSchema,
    preHandler: [authenticateToken, requirePermission(['project:read'])]
  }, async (request, reply) => {
    try {
      const { projectId } = request.params as { projectId: string };
      const userId = (request.user as any)?.userId;
      const userRole = (request.user as any)?.role;
      
      const analytics = await projectService.getProjectAnalytics(projectId, userId, userRole);
      
      return {
        success: true,
        analytics
      };
    } catch (error) {
      logger.error('Failed to fetch project analytics:', error);
      return reply.status(500).send({
        error: 'Failed to fetch project analytics',
        details: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}

export default fp(projectRoutes);