---
description: "Enforce all Vue components define a ''name'' property for better debugging and dev tools integration."
globs: "[''**/*.vue'']"
alwaysApply: true
---

# Vue Component Name Enforcement

## Context

- This rule applies to all Single File Components (`.vue` files) within the project.
- Explicitly naming components is a critical best practice for Vue development. It significantly improves the debugging experience, especially when using Vue Devtools, by providing clear component hierarchy trees instead of generic `<AnonymousComponent>` tags.

## Requirements

- Every `.vue` component''s `<script>` section **MUST** export a `name` property.
- The name should be in `PascalCase` and match the component''s filename (e.g., `MyComponent.vue` should have `name: ''MyComponent''`).

## Examples

<example>
A compliant Vue component that correctly defines its name.

```vue
<template>
  <div>Hello from MyComponent!</div>
</template>

<script>
export default {
  name: ''MyComponent'',
  // ... other component options
}
</script>
```
</example>

<example type="invalid">
A non-compliant Vue component that is missing the `name` property.

```vue
<template>
  <div>...</div>
</template>

<script>
export default {
  // Missing the ''name'' property
  props: {
    // ...
  }
}
</script>
```
</example>

## Critical Rules

- **ALWAYS** include a `name` property in every Vue component.
- **NEVER** create anonymous components, as it hinders debugging and maintainability.
