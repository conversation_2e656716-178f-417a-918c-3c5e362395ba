# Luxcrafts Security & Penetration Testing Guide

## Overview

This document outlines comprehensive security measures and penetration testing procedures for the Luxcrafts enterprise deployment. It covers security hardening, vulnerability assessment, penetration testing methodologies, and incident response procedures.

## Security Architecture

### Defense in Depth Strategy

```
┌─────────────────────────────────────────────────────────────┐
│                    Internet/Public Access                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Cloudflare WAF/DDoS                        │
│  • Rate Limiting  • Bot Protection  • SSL/TLS              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Load Balancer/Nginx                        │
│  • SSL Termination  • Security Headers  • Rate Limiting    │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Application Layer                          │
│  • Input Validation  • Authentication  • Authorization     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Database Layer                             │
│  • Encryption at Rest  • Access Controls  • Audit Logs    │
└─────────────────────────────────────────────────────────────┘
```

### Security Controls Matrix

| Layer | Control Type | Implementation | Status |
|-------|-------------|----------------|--------|
| Network | Firewall | UFW + iptables | ✅ |
| Network | DDoS Protection | Cloudflare | ✅ |
| Network | WAF | Cloudflare + Nginx | ✅ |
| Transport | SSL/TLS | Let's Encrypt + Strong Ciphers | ✅ |
| Application | Input Validation | Zod + Sanitization | ✅ |
| Application | Authentication | JWT + MFA | ✅ |
| Application | Authorization | RBAC | ✅ |
| Application | Session Management | Secure Cookies + CSRF | ✅ |
| Data | Encryption at Rest | AES-256 | ✅ |
| Data | Encryption in Transit | TLS 1.3 | ✅ |
| Infrastructure | OS Hardening | CIS Benchmarks | ✅ |
| Infrastructure | Intrusion Detection | AIDE + Fail2ban | ✅ |
| Infrastructure | Vulnerability Scanning | Lynis + ClamAV | ✅ |
| Monitoring | SIEM | Centralized Logging | ✅ |
| Monitoring | Alerting | Real-time Notifications | ✅ |

## Security Hardening Checklist

### Operating System Hardening

#### System Configuration
- [ ] Disable unnecessary services
- [ ] Configure secure boot parameters
- [ ] Enable audit logging
- [ ] Set proper file permissions
- [ ] Configure system resource limits
- [ ] Enable address space layout randomization (ASLR)
- [ ] Configure kernel parameters for security

```bash
#!/bin/bash
# OS Hardening Script

# Disable unnecessary services
sudo systemctl disable avahi-daemon
sudo systemctl disable cups
sudo systemctl disable bluetooth

# Configure kernel parameters
cat >> /etc/sysctl.conf << EOF
# Security hardening
kernel.dmesg_restrict = 1
kernel.kptr_restrict = 2
kernel.yama.ptrace_scope = 1
kernel.kexec_load_disabled = 1
kernel.unprivileged_bpf_disabled = 1
net.core.bpf_jit_harden = 2
EOF

# Apply settings
sudo sysctl -p

# Set file permissions
sudo chmod 700 /root
sudo chmod 600 /etc/ssh/sshd_config
sudo chmod 644 /etc/passwd
sudo chmod 600 /etc/shadow

# Configure limits
cat >> /etc/security/limits.conf << EOF
* hard core 0
* soft nproc 1024
* hard nproc 2048
EOF
```

#### SSH Hardening
- [ ] Disable root login
- [ ] Use key-based authentication only
- [ ] Change default SSH port
- [ ] Configure SSH banner
- [ ] Limit SSH access by user/group
- [ ] Enable SSH audit logging

```bash
# SSH Configuration (/etc/ssh/sshd_config)
Port 2222
Protocol 2
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
MaxSessions 2
ClientAliveInterval 300
ClientAliveCountMax 2
AllowUsers luxcrafts
Banner /etc/ssh/banner
```

### Network Security

#### Firewall Configuration
```bash
#!/bin/bash
# Advanced Firewall Rules

# Reset UFW
sudo ufw --force reset

# Default policies
sudo ufw default deny incoming
sudo ufw default allow outgoing

# SSH (custom port)
sudo ufw allow 2222/tcp

# HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Dokploy
sudo ufw allow from 10.0.0.0/8 to any port 3000

# Rate limiting for SSH
sudo ufw limit 2222/tcp

# Enable firewall
sudo ufw --force enable

# Configure iptables for advanced rules
sudo iptables -A INPUT -p tcp --dport 80 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT
sudo iptables -A INPUT -p tcp --dport 443 -m limit --limit 25/minute --limit-burst 100 -j ACCEPT

# Save iptables rules
sudo iptables-save > /etc/iptables/rules.v4
```

#### DDoS Protection
```bash
# Configure rate limiting in Nginx
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
limit_req_zone $binary_remote_addr zone=general:10m rate=200r/m;

# Connection limiting
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
limit_conn conn_limit_per_ip 20;

# Configure Fail2ban
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
ignoreip = 127.0.0.1/8 ::1

[sshd]
enabled = true
port = 2222
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 86400

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
logpath = /var/log/nginx/error.log
maxretry = 3
EOF
```

### Application Security

#### Input Validation & Sanitization
```typescript
// Input validation with Zod
import { z } from 'zod';
import DOMPurify from 'dompurify';

// User input schema
const userInputSchema = z.object({
  email: z.string().email().max(255),
  password: z.string().min(8).max(128).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/),
  name: z.string().min(1).max(100).regex(/^[a-zA-Z\s]+$/),
  message: z.string().max(1000)
});

// Sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = DOMPurify.sanitize(req.body[key]);
      }
    });
  }
  next();
};

// SQL injection prevention
export const safeQuery = async (query: string, params: any[]) => {
  // Use parameterized queries only
  return await db.query(query, params);
};
```

#### Authentication & Authorization
```typescript
// JWT Security Configuration
const jwtConfig = {
  secret: process.env.JWT_SECRET,
  algorithm: 'HS256' as const,
  expiresIn: '15m',
  issuer: 'luxcrafts.com',
  audience: 'luxcrafts-app'
};

// Multi-factor authentication
export const enableMFA = async (userId: string) => {
  const secret = speakeasy.generateSecret({
    name: 'Luxcrafts',
    account: userId,
    issuer: 'Luxcrafts'
  });
  
  await db.user.update({
    where: { id: userId },
    data: { mfaSecret: secret.base32 }
  });
  
  return secret;
};

// Role-based access control
export const requireRole = (roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRole = req.user?.role;
    if (!userRole || !roles.includes(userRole)) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    next();
  };
};
```

#### Session Security
```typescript
// Secure session configuration
const sessionConfig = {
  secret: process.env.SESSION_SECRET,
  name: 'luxcrafts.sid',
  cookie: {
    secure: true, // HTTPS only
    httpOnly: true, // No client-side access
    maxAge: 15 * 60 * 1000, // 15 minutes
    sameSite: 'strict' as const
  },
  resave: false,
  saveUninitialized: false
};

// CSRF protection
import csrf from 'csurf';
const csrfProtection = csrf({
  cookie: {
    httpOnly: true,
    secure: true,
    sameSite: 'strict'
  }
});
```

## Penetration Testing Methodology

### Testing Phases

#### Phase 1: Reconnaissance
```bash
#!/bin/bash
# Information Gathering

# Domain enumeration
nslookup luxcrafts.com
dig luxcrafts.com ANY
whois luxcrafts.com

# Subdomain discovery
subfinder -d luxcrafts.com
amass enum -d luxcrafts.com

# Port scanning
nmap -sS -sV -O -A luxcrafts.com
nmap -sU --top-ports 1000 luxcrafts.com

# Service enumeration
nmap --script=default,discovery,safe luxcrafts.com

# SSL/TLS analysis
sslscan luxcrafts.com
testssl.sh luxcrafts.com
```

#### Phase 2: Vulnerability Assessment
```bash
#!/bin/bash
# Automated Vulnerability Scanning

# Web application scanning
nukei -target https://luxcrafts.com
nikto -h https://luxcrafts.com

# OWASP ZAP scanning
zap-baseline.py -t https://luxcrafts.com

# Infrastructure scanning
nessus_scan.sh luxcrafts.com
openvas_scan.sh luxcrafts.com

# Container scanning
trivy image luxcrafts:latest
clair-scanner luxcrafts:latest

# Dependency scanning
npm audit
snyk test
```

#### Phase 3: Exploitation
```bash
#!/bin/bash
# Manual Testing Procedures

# SQL Injection Testing
sqlmap -u "https://luxcrafts.com/api/search?q=test" --batch --risk=3 --level=5

# XSS Testing
xsser -u "https://luxcrafts.com/search" --auto

# CSRF Testing
csrf_tester.py https://luxcrafts.com

# Authentication bypass
hydra -l admin -P passwords.txt https-post-form "/api/auth/login:username=^USER^&password=^PASS^:Invalid"

# Directory traversal
dirb https://luxcrafts.com /usr/share/dirb/wordlists/common.txt
gobuster dir -u https://luxcrafts.com -w /usr/share/wordlists/dirbuster/directory-list-2.3-medium.txt
```

### Security Testing Checklist

#### OWASP Top 10 Testing

1. **Injection Attacks**
   - [ ] SQL Injection
   - [ ] NoSQL Injection
   - [ ] LDAP Injection
   - [ ] Command Injection
   - [ ] XPath Injection

2. **Broken Authentication**
   - [ ] Weak password policies
   - [ ] Session management flaws
   - [ ] Credential stuffing
   - [ ] Brute force attacks
   - [ ] Session fixation

3. **Sensitive Data Exposure**
   - [ ] Unencrypted data transmission
   - [ ] Weak encryption algorithms
   - [ ] Exposed configuration files
   - [ ] Information leakage
   - [ ] Insecure data storage

4. **XML External Entities (XXE)**
   - [ ] XML parsing vulnerabilities
   - [ ] External entity injection
   - [ ] Billion laughs attack
   - [ ] File disclosure

5. **Broken Access Control**
   - [ ] Privilege escalation
   - [ ] Insecure direct object references
   - [ ] Missing authorization
   - [ ] CORS misconfiguration

6. **Security Misconfiguration**
   - [ ] Default credentials
   - [ ] Unnecessary services
   - [ ] Verbose error messages
   - [ ] Missing security headers
   - [ ] Outdated software

7. **Cross-Site Scripting (XSS)**
   - [ ] Reflected XSS
   - [ ] Stored XSS
   - [ ] DOM-based XSS
   - [ ] Content Security Policy bypass

8. **Insecure Deserialization**
   - [ ] Object injection
   - [ ] Remote code execution
   - [ ] Privilege escalation

9. **Using Components with Known Vulnerabilities**
   - [ ] Outdated dependencies
   - [ ] Vulnerable libraries
   - [ ] Unpatched frameworks

10. **Insufficient Logging & Monitoring**
    - [ ] Missing audit trails
    - [ ] Inadequate alerting
    - [ ] Log injection
    - [ ] Log tampering

### Automated Security Testing

#### CI/CD Security Pipeline
```yaml
# Security testing in GitHub Actions
name: Security Testing

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: SAST Scan
        uses: github/super-linter@v4
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Dependency Scan
        run: |
          npm audit --audit-level high
          npx snyk test --severity-threshold=high
          
      - name: Container Scan
        run: |
          docker build -t luxcrafts:test .
          trivy image --exit-code 1 --severity HIGH,CRITICAL luxcrafts:test
          
      - name: Infrastructure Scan
        run: |
          terraform plan -out=tfplan
          tfsec tfplan
          checkov -f tfplan
          
      - name: DAST Scan
        run: |
          docker run -t owasp/zap2docker-stable zap-baseline.py -t ${{ env.APP_URL }}
```

#### Security Monitoring
```bash
#!/bin/bash
# Continuous Security Monitoring

# Real-time vulnerability monitoring
while true; do
    # Check for new CVEs
    cve-search.py --product luxcrafts
    
    # Monitor system integrity
    aide --check
    
    # Check for malware
    clamscan -r /var/www/luxcrafts --infected --log=/var/log/clamav/scan.log
    
    # Monitor network connections
    netstat -tulpn | grep LISTEN
    
    # Check for suspicious processes
    ps aux | grep -E '(nc|netcat|ncat|socat)'
    
    sleep 3600  # Run every hour
done
```

## Incident Response Procedures

### Security Incident Classification

| Severity | Description | Response Time | Escalation |
|----------|-------------|---------------|------------|
| Critical | Active breach, data theft | 15 minutes | CISO, CEO |
| High | Attempted breach, service disruption | 1 hour | Security Team |
| Medium | Suspicious activity, policy violation | 4 hours | IT Manager |
| Low | Minor security event | 24 hours | Security Analyst |

### Incident Response Playbook

#### Step 1: Detection & Analysis
```bash
#!/bin/bash
# Incident Detection Script

# Check for indicators of compromise
grep -i "attack\|breach\|intrusion\|malware" /var/log/syslog
grep -i "failed login" /var/log/auth.log | tail -20

# Network analysis
netstat -an | grep ESTABLISHED
ss -tulpn | grep :80
ss -tulpn | grep :443

# Process analysis
ps aux --sort=-%cpu | head -10
lsof -i -P -n | grep LISTEN

# File integrity check
aide --check
find /var/www -type f -mtime -1 -ls

# Memory analysis
free -m
top -b -n 1 | head -20
```

#### Step 2: Containment
```bash
#!/bin/bash
# Incident Containment

# Block suspicious IP
SUSPICIOUS_IP="*************"
sudo ufw deny from $SUSPICIOUS_IP
sudo iptables -A INPUT -s $SUSPICIOUS_IP -j DROP

# Isolate affected systems
sudo systemctl stop nginx
sudo systemctl stop luxcrafts

# Preserve evidence
sudo dd if=/dev/sda of=/mnt/evidence/disk_image.dd bs=4096
sudo tar -czf /mnt/evidence/logs_$(date +%Y%m%d_%H%M%S).tar.gz /var/log/

# Create memory dump
sudo dd if=/dev/mem of=/mnt/evidence/memory_dump.dd
```

#### Step 3: Eradication
```bash
#!/bin/bash
# Threat Eradication

# Remove malware
sudo clamscan -r / --remove --log=/var/log/clamav/removal.log

# Patch vulnerabilities
sudo apt update && sudo apt upgrade -y
npm audit fix

# Update security configurations
sudo fail2ban-client reload
sudo systemctl restart ufw

# Reset compromised accounts
sudo passwd luxcrafts
sudo usermod -L suspicious_user
```

#### Step 4: Recovery
```bash
#!/bin/bash
# System Recovery

# Restore from clean backup
sudo systemctl stop luxcrafts
sudo rm -rf /var/www/luxcrafts/current
sudo tar -xzf /var/backups/luxcrafts/clean_backup.tar.gz -C /var/www/luxcrafts/

# Restart services
sudo systemctl start nginx
sudo systemctl start luxcrafts

# Verify integrity
curl -f https://luxcrafts.com/health
sudo aide --check

# Monitor for reinfection
tail -f /var/log/nginx/access.log | grep -E '(attack|exploit|injection)'
```

#### Step 5: Lessons Learned
```markdown
# Incident Report Template

## Incident Summary
- **Date/Time**: 
- **Severity**: 
- **Type**: 
- **Affected Systems**: 
- **Impact**: 

## Timeline
- **Detection**: 
- **Containment**: 
- **Eradication**: 
- **Recovery**: 

## Root Cause Analysis
- **Initial Vector**: 
- **Vulnerabilities Exploited**: 
- **Security Controls Failed**: 

## Remediation Actions
- **Immediate**: 
- **Short-term**: 
- **Long-term**: 

## Lessons Learned
- **What Worked Well**: 
- **What Could Be Improved**: 
- **Recommendations**: 
```

## Compliance & Audit

### Security Frameworks

#### NIST Cybersecurity Framework
- **Identify**: Asset management, risk assessment
- **Protect**: Access control, data security
- **Detect**: Continuous monitoring, anomaly detection
- **Respond**: Incident response, communications
- **Recover**: Recovery planning, improvements

#### ISO 27001 Controls
- Information security policies
- Organization of information security
- Human resource security
- Asset management
- Access control
- Cryptography
- Physical and environmental security
- Operations security
- Communications security
- System acquisition, development and maintenance
- Supplier relationships
- Information security incident management
- Information security aspects of business continuity management
- Compliance

### Audit Checklist

#### Technical Controls
- [ ] Firewall configuration review
- [ ] Access control verification
- [ ] Encryption implementation check
- [ ] Vulnerability assessment results
- [ ] Patch management status
- [ ] Backup and recovery testing
- [ ] Monitoring and logging review
- [ ] Incident response capability

#### Administrative Controls
- [ ] Security policies and procedures
- [ ] Employee security training
- [ ] Risk assessment documentation
- [ ] Vendor security assessments
- [ ] Business continuity planning
- [ ] Compliance documentation

#### Physical Controls
- [ ] Data center security
- [ ] Equipment protection
- [ ] Environmental controls
- [ ] Secure disposal procedures

## Security Metrics & KPIs

### Security Metrics Dashboard
```yaml
# Security Metrics Configuration
metrics:
  vulnerability_metrics:
    - critical_vulnerabilities: 0
    - high_vulnerabilities: < 5
    - medium_vulnerabilities: < 20
    - time_to_patch: < 72h
    
  incident_metrics:
    - mean_time_to_detect: < 15m
    - mean_time_to_respond: < 1h
    - mean_time_to_recover: < 4h
    - false_positive_rate: < 5%
    
  compliance_metrics:
    - security_training_completion: > 95%
    - policy_acknowledgment: 100%
    - audit_findings: 0 critical
    - penetration_test_score: > 90%
    
  operational_metrics:
    - uptime: > 99.9%
    - backup_success_rate: 100%
    - ssl_certificate_validity: > 30 days
    - security_scan_frequency: daily
```

### Reporting
```bash
#!/bin/bash
# Security Report Generation

# Generate monthly security report
cat > security_report_$(date +%Y%m).md << EOF
# Monthly Security Report - $(date +"%B %Y")

## Executive Summary
- Security incidents: $(grep -c "SECURITY" /var/log/security.log)
- Vulnerabilities patched: $(grep -c "PATCHED" /var/log/updates.log)
- Uptime: $(uptime | awk '{print $3}')

## Vulnerability Status
$(lynis audit system --quiet | grep "Warnings")

## Compliance Status
- Backup success rate: $(grep -c "SUCCESS" /var/log/backup.log)
- SSL certificate expiry: $(openssl x509 -in /etc/ssl/certs/luxcrafts.pem -noout -dates)

## Recommendations
- Update security policies
- Conduct security training
- Review access controls
EOF
```

---

**Document Version**: 1.0
**Last Updated**: $(date)
**Classification**: Confidential
**Owner**: Security Team