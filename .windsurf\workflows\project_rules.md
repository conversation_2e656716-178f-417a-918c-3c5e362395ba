# ESTRATIX Agentic Workflows

This directory contains all the agentic workflows used to automate development, management, and operational tasks within the ESTRATIX project. These workflows are designed to be executed by autonomous agents, orchestrated by the Command Office structure.

## Workflow Structure

The workflows are organized into a lifecycle-oriented structure:

- **`0_master_orchestration/`**: High-level master workflows that orchestrate complex, end-to-end processes.
- **`1_component_lifecycle/`**: Workflows for managing the lifecycle of individual ESTRATIX components (e.g., defining, generating, updating).
- **`2_operational_tasks/`**: Workflows for executing specific operational duties, such as observability and knowledge management.

## Available Workflows

Below is an index of all available workflows, categorized by their location.

---

### 0. Master Orchestration

- **`/bootstrap_client_project`**: (MASTER WORKFLOW) Orchestrates the complete bootstrapping of a new ESTRATIX client project, from initial onboarding and RFP to a ready-to-develop project structure.
- **`/bootstrap_command_office`**: (MASTER WORKFLOW) Orchestrates the complete bootstrapping of a new ESTRATIX Command Office Headquarters, from definition to the generation of its entire component chain.
- **`/bootstrap_estratix_project`**: (MASTER WORKFLOW) Bootstraps a new, composable ESTRATIX subproject from an approved proposal, creating an integrated, hexagonal structure within the master project.
- **`/value_chain_observability_and_improvement`**: (MASTER WORKFLOW) Manages the ongoing observability, alignment, and continuous improvement of the ESTRATIX value chain.

---

### 1. Component Lifecycle

#### 1.1. Define

- **`/agent_definition`**: Guides the generation of agent definitions from ESTRATIX value chain processes.
- **`/api_definition`**: Guides the definition of a new ESTRATIX API.
- **`/co_headquarter_definition`**: Guides the definition of a new ESTRATIX Command Officer Headquarters.
- **`/data_model_definition`**: Guides the definition of a new ESTRATIX Data Model.
- **`/flow_definition`**: Guides the design and definition of ESTRATIX Flows.
- **`/pattern_definition`**: Guides the definition of a new ESTRATIX Pattern.
- **`/process_definition`**: Guides the user or agent through defining a new ESTRATIX process.
- **`/productized_service_definition`**: Guides the definition of a new ESTRATIX Productized Service.
- **`/project_definition`**: Guides the definition of a new ESTRATIX project.
- **`/proposal_definition`**: Guides the creation of comprehensive ESTRATIX proposals.
- **`/rule_definition`**: Guides the definition of a new ESTRATIX Rule.
- **`/service_definition`**: Guides the definition of a new ESTRATIX Productized Service.
- **`/standard_definition`**: Guides the definition of a new ESTRATIX Standard.
- **`/task_definition`**: Guides the definition of a new granular ESTRATIX Task.
- **`/tool_definition`**: Guides the lifecycle of defining and registering a new ESTRATIX Tool.

#### 1.2. Generate

##### Core Agentic Components (Framework-Specific)

These are modular workflows that generate individual, framework-specific components. They are typically orchestrated by higher-level workflows like `/process_generation` or `/flow_generation` to assemble a complete, runnable capability.

- **/agent**: Generates a single, framework-specific agent component (e.g., a CrewAI `Agent`, a Pydantic-AI `Instruct` model, a PocketFlow `Pipe`, a Google ADK `Agent`).
- **/task**: Generates a single, framework-specific task component (e.g., a CrewAI `Task`, a PocketFlow `Task`).
- **/crew**: Generates a framework-specific group of agents that work together (e.g., a CrewAI `Crew`).

##### Orchestrators & Other ESTRATIX Components

- **/api_generation**: Guides the agentic generation of a new ESTRATIX API from its conceptual definition.
- **/bootstrap_fastapi_service**: Guides the generation of a new ESTRATIX FastAPI microservice based on the Hexagonal Architecture boilerplate.
- **/co_headquarter_generation**: (Orchestrator) Translates a conceptual Command Officer HQ definition into a tangible, operational structure.
- **/data_model_generation**: Guides the generation of Pydantic models from conceptual ESTRATIX data model definitions.
- **/flow_generation**: (Orchestrator) Guides the generation of a framework-specific implementation for a pre-defined ESTRATIX Flow, recursively calling component workflows.
- **/pattern_generation**: (Orchestrator) Guides the generation of a framework-specific implementation for a pre-defined ESTRATIX Pattern.
- **/process_generation**: (Orchestrator) Guides the generation of a framework-specific implementation for a pre-defined ESTRATIX Process, recursively calling component workflows.
- **/productized_service_generation**: Guides the agentic implementation of a defined ESTRATIX Productized Service.
- **/project_generation**: Populates a defined ESTRATIX project with initial planning documents and artifacts.
- **/rule_generation**: Guides the generation of a new ESTRATIX Rule in the machine-readable `.md` format for agentic consumption.
- **/service_generation**: Guides the generation of a new ESTRATIX domain service, bootstrapping a dedicated microservice project.
- **/tool_generation**: Guides the agentic generation of runnable code for a defined ESTRATIX Tool.

---

### 2. Operational Tasks

#### 2.1. Knowledge

- **`/ingest_framework_documentation`**: Defines the process for ingesting framework documentation into a Milvus vector database.

#### 2.2. Observability

- **`/component_registration_and_management`**: Defines the standard process for registering and managing ESTRATIX components.
- **`/update_agents_org_landscape`**: Guides the creation and refinement of the ESTRATIX Agents Organizational Landscape diagram.
- **`/update_api_landscape`**: Guides the generation and update of the API Landscape diagram.
- **`/update_command_officer_org_chart`**: Guides the creation and refinement of the ESTRATIX Command Officer Organizational Chart.
- **`/update_data_models_landscape`**: Generates the Data Models Landscape diagram.
- **`/update_flow_landscape`**: Generates the Flow_Landscape.mmd diagram.
- **`/update_master_process_landscape`**: Updates the Master Process Landscape diagram.
- **`/update_project_landscape`**: Guides the generation and update of the Project Landscape diagram.
- **`/update_service_landscape`**: Guides the generation and update of the Service Landscape diagram.
- **`/update_standards_landscape`**: Guides the generation and update of the Standards Landscape diagram.
- **`/update_tool_landscape`**: Generates the Tool_Landscape.mmd diagram.
