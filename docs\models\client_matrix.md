# ESTRATIX Client Matrix

**Objective**: This matrix serves as the master registry for all external clients. It provides a high-level overview of each client relationship, their status, and links to the specific projects being delivered for them, ensuring a clear line of sight from strategic engagement to project execution.

**Scope**: This matrix governs all client engagements, from initial prospecting to active project delivery and post-engagement support. It is the authoritative source for client-related information and links directly to the `project_matrix.md`.

---

## Client Engagement Lifecycle

1. **Prospecting**: A potential client is identified and added with a `Prospect` status.
2. **Onboarding**: Once a contract is signed, the status moves to `Active`, a `Client Directory` is created, and a `Project ID` is generated in the `project_matrix.md`.
3. **Delivery**: Projects are executed and tracked in the `project_matrix.md`.
4. **Archival**: Upon completion of all engagements, the client status is moved to `Archived`.

---

## Client Matrix

| Client ID | Client Name | Engagement Status | Primary Contact | Related Project ID(s) | Client Directory | Onboarding Date | Notes |
|---|---|---|---|---|---|---|---|
| `ZURUX` | Zurux Inc. | `Active` | `[Contact Name]` | `CL_ZURUX_P001` | `clients/zurux/` | `YYYY-MM-DD` | Engagement for a generative e-commerce CMS platform. |
| `ACME_CORP` | Acme Corporation | `Active` | `[Contact Name]` | `CL_ACME_P001` | `clients/acme_corp/` | `YYYY-MM-DD` | Digital transformation initiative. |

---

## Guidance for Use

- **Single Source of Truth**: This matrix is the definitive registry for all client information.
- **Link to Projects**: Always ensure the `Related Project ID(s)` column is populated and links correctly to the `project_matrix.md`.
- **Directory Structure**: The `Client Directory` must follow the standardized structure: `clients/[client_id_lowercase]/`.
