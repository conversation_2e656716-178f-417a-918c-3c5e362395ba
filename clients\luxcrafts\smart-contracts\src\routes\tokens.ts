import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import fp from 'fastify-plugin';
import { web3Service } from '../services/web3Service';
import { authenticateToken } from '../middleware/auth';

// Token schemas
const mintSchema = {
  body: {
    type: 'object',
    required: ['to', 'amount'],
    properties: {
      to: { type: 'string' },
      amount: { type: 'string' }
    }
  }
};

const burnSchema = {
  body: {
    type: 'object',
    required: ['amount'],
    properties: {
      amount: { type: 'string' }
    }
  }
};

const transferSchema = {
  body: {
    type: 'object',
    required: ['to', 'amount'],
    properties: {
      to: { type: 'string' },
      amount: { type: 'string' }
    }
  }
};

const approveSchema = {
  body: {
    type: 'object',
    required: ['spender', 'amount'],
    properties: {
      spender: { type: 'string' },
      amount: { type: 'string' }
    }
  }
};

const allowanceSchema = {
  querystring: {
    type: 'object',
    required: ['owner', 'spender'],
    properties: {
      owner: { type: 'string' },
      spender: { type: 'string' }
    }
  }
};

const balanceParamsSchema = {
  params: {
    type: 'object',
    required: ['address'],
    properties: {
      address: { type: 'string' }
    }
  }
};

async function tokenRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Mint tokens (admin only)
  fastify.post('/mint', {
    schema: mintSchema,
    preHandler: [authenticateToken, async (request: any, reply: any) => {
      if (request.user?.role !== 'admin') {
        return reply.code(403).send({ error: 'Admin access required' });
      }
    }]
  }, async (request: any, reply: any) => {
    try {
      const { to, amount } = request.body;
      const txHash = await web3Service.mintTokens(to, amount);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to mint tokens' });
    }
  });

  // Burn tokens
  fastify.post('/burn', {
    schema: burnSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { amount } = request.body;
      const userAddress = request.user?.walletAddress;
      if (!userAddress) {
        return reply.code(400).send({ error: 'Wallet address required' });
      }
      const txHash = await web3Service.burnTokens(userAddress, amount);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to burn tokens' });
    }
  });

  // Transfer tokens
  fastify.post('/transfer', {
    schema: transferSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { to, amount } = request.body;
      const fromAddress = request.user?.walletAddress;
      if (!fromAddress) {
        return reply.code(400).send({ error: 'Wallet address required' });
      }
      const txHash = await web3Service.transferTokens(fromAddress, to, amount);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to transfer tokens' });
    }
  });

  // Approve tokens
  fastify.post('/approve', {
    schema: approveSchema,
    preHandler: [authenticateToken]
  }, async (request: any, reply: any) => {
    try {
      const { spender, amount } = request.body;
      const ownerAddress = request.user?.walletAddress;
      if (!ownerAddress) {
        return reply.code(400).send({ error: 'Wallet address required' });
      }
      const txHash = await web3Service.approveTokens(ownerAddress, spender, amount);
      reply.send({ success: true, transactionHash: txHash });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to approve tokens' });
    }
  });

  // Get token balance
  fastify.get('/balance/:address', {
    schema: balanceParamsSchema
  }, async (request: any, reply: any) => {
    try {
      const { address } = request.params;
      const balance = await web3Service.getTokenBalance(address);
      reply.send({ success: true, address, balance });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get token balance' });
    }
  });

  // Get token allowance
  fastify.get('/allowance', {
    schema: allowanceSchema
  }, async (request: any, reply: any) => {
    try {
      const { owner, spender } = request.query;
      const allowance = await web3Service.getTokenAllowance(owner, spender);
      reply.send({ success: true, owner, spender, allowance });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get token allowance' });
    }
  });

  // Get token info
  fastify.get('/info', async (request: any, reply: any) => {
    try {
      const tokenInfo = await web3Service.getTokenInfo();
      reply.send({ success: true, tokenInfo });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get token info' });
    }
  });

  // Get total supply
  fastify.get('/total-supply', async (request: any, reply: any) => {
    try {
      const totalSupply = await web3Service.getTotalSupply();
      reply.send({ success: true, totalSupply });
    } catch (error) {
      reply.code(500).send({ error: 'Failed to get total supply' });
    }
  });
}

export default fp(tokenRoutes);