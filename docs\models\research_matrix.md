# ESTRATIX Research Matrix

**Objective**: This matrix orchestrates the entire research lifecycle, providing end-to-end traceability from initial topic identification to the final delivery of a proposal, project, or internal service. It connects sources, topics, research activities, and outcomes, ensuring every research effort is purposeful, tracked, and aligned with strategic goals.

**Scope**: This matrix governs all research conducted within ESTRATIX, whether for internal capability development (assigned to a Command Office) or for external client projects.

---

## Research & Development Lifecycle

1. **Topic & Source Identification**: A `Topic` (from `topic_matrix.md`) is identified for investigation, often triggered by monitoring a `Source` (from `source_matrix.md`) or a `User` (from `user_matrix.md`).
2. **Batch Creation**: A new `Research Batch ID` is created in this matrix to formally initiate the research project.
3. **Execution & Analysis**: Research is conducted, and key insights are synthesized.
4. **Outcome & Traceability**: The research culminates in an outcome, which is explicitly linked back to its origin and forward to its application:
    - **Assignment**: The research is formally assigned to either a `Client ID` (for external projects) or a `Command Office ID` (for internal initiatives).
    - **Outcome & Proposal**: The research culminates in a `Proposal ID` (from `proposal_matrix.md`), which is then linked to a `Project ID` (from `project_matrix.md`) upon approval.

---

## Research Matrix

| Research Batch ID | Title / Subject | Status | Start Date | End Date | Lead Researcher (Agent ID) | Topic ID(s) | Source/User ID(s) | Assigned To (Client/CO ID) | Key Insights & Summary | Outcome Type | Outcome ID | Notes |
|---|---|---|---|---|---|---|---|---|---|---|---|---|
| `RB-20240520-001` | Generative Website Platforms | `Completed` | `2024-05-20` | `2024-05-22` | `CTO_A002_TechScout` | `TOP-001` | `SRC-001`, `SRC-002` | `ZURUX` | Investigated multi-provider LLM architectures for code generation, focusing on live preview and component-based design. Findings from LocalSite.ai and DeepSite.ai concepts are highly relevant. | `Proposal` | `PROP-20240520-001` | Links to `PROJ-EXT-001`. |
| `RB-20240521-001` | Advanced Document Ingestion | `Completed` | `2024-05-21` | `2024-05-23` | `CIO_A002_KnowledgeEngineer` | `TOP-003` | `SRC-003` | `CIO` | Analyzed Bytedance's Dolphin research for advanced PDF parsing, layout preservation, and structured markdown conversion. | `Proposal` | `PROP-20240521-001` | For internal service `STRAT-DOCING-01`. Links to `PROJ-INT-002`. |
| `RB-20240522-001` | Reinforcement Learning for Sales Automation | `In-Progress` | `2024-05-22` | `TBD` | `CPO_A001_ProcessAnalyst` | `TOP-004` | `SRC-004`, `USR-001` | `CPO` | Explored RL techniques for sales conversation analysis and conversion prediction, based on the SalesRL paper. | `Proposal` | `PROP-20240522-001` | For internal initiative `STRAT-SALESRL-01`. Links to `PROJ-INT-003`. |

---

## Guidance for Use

- **Start Here**: All new research initiatives must begin with the creation of a `Research Batch ID` in this matrix.
- **Link Everything**: Ensure all relevant IDs (`Topic`, `Source`, `Proposal`, `Project`, `Client`, `Command Office`) are filled in to maintain the integrity of the knowledge graph.
- **Status Tracking**: Keep the `Status` (`Planning`, `In-Progress`, `Completed`, `Archived`) updated to reflect the current state of the research.
