name: Luxcrafts CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      deployment_target:
        description: 'Deployment target'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
        - both
      skip_tests:
        description: 'Skip test execution'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/luxcrafts
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Code Quality and Testing
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npm run check

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm test -- --coverage
        env:
          CI: true

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  # Security Scanning
  security:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Audit npm dependencies
        run: npm audit --audit-level=high

  # Build and Push Docker Image
  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: |
            VITE_WALLETCONNECT_PROJECT_ID=${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
            VITE_ALCHEMY_API_KEY=${{ secrets.VITE_ALCHEMY_API_KEY }}
            VITE_INFURA_API_KEY=${{ secrets.VITE_INFURA_API_KEY }}
            VITE_API_BASE_URL=${{ secrets.VITE_API_BASE_URL }}
            VITE_CHAIN_ID=${{ secrets.VITE_CHAIN_ID }}
            VITE_CONTRACT_ADDRESS=${{ secrets.VITE_CONTRACT_ADDRESS }}
            VITE_LUX_TOKEN_ADDRESS=${{ secrets.VITE_LUX_TOKEN_ADDRESS }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Deploy to Vercel Staging
  deploy-vercel-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop' || (github.event_name == 'workflow_dispatch' && (github.event.inputs.deployment_target == 'staging' || github.event.inputs.deployment_target == 'both'))
    environment:
      name: staging
      url: ${{ steps.vercel-deploy.outputs.preview-url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install Vercel CLI
        run: npm install -g vercel@latest

      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        env:
          VITE_APP_ENVIRONMENT: staging
          VITE_WALLETCONNECT_PROJECT_ID: ${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY: ${{ secrets.VITE_ALCHEMY_API_KEY_STAGING }}
          VITE_INFURA_API_KEY: ${{ secrets.VITE_INFURA_API_KEY_STAGING }}
          VITE_API_BASE_URL: ${{ secrets.VITE_API_BASE_URL_STAGING }}
          VITE_CHAIN_ID: ${{ secrets.VITE_CHAIN_ID_STAGING }}
          VITE_CONTRACT_ADDRESS: ${{ secrets.VITE_CONTRACT_ADDRESS_STAGING }}
          VITE_LUX_TOKEN_ADDRESS: ${{ secrets.VITE_LUX_TOKEN_ADDRESS_STAGING }}

      - name: Deploy to Vercel
        id: vercel-deploy
        run: |
          DEPLOYMENT_URL=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "preview-url=$DEPLOYMENT_URL" >> $GITHUB_OUTPUT
          echo "Staging deployed to: $DEPLOYMENT_URL"

      - name: Run staging health checks
        run: |
          sleep 30
          curl -f ${{ steps.vercel-deploy.outputs.preview-url }}/health || curl -f ${{ steps.vercel-deploy.outputs.preview-url }}/ || exit 1
          echo "✅ Staging deployment health check passed"

      - name: Comment PR with staging URL
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 **Staging Deployment Ready**\n\n📱 Preview: ${{ steps.vercel-deploy.outputs.preview-url }}\n\n✅ All checks passed!`
            })

  # Deploy to Production via Dokploy
  deploy-production:
    needs: [build, deploy-vercel-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || (github.event_name == 'workflow_dispatch' && (github.event.inputs.deployment_target == 'production' || github.event.inputs.deployment_target == 'both'))
    environment:
      name: production
      url: https://luxcrafts.co
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build production artifacts
        run: npm run build
        env:
          VITE_APP_ENVIRONMENT: production
          VITE_WALLETCONNECT_PROJECT_ID: ${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY: ${{ secrets.VITE_ALCHEMY_API_KEY_PRODUCTION }}
          VITE_INFURA_API_KEY: ${{ secrets.VITE_INFURA_API_KEY_PRODUCTION }}
          VITE_API_BASE_URL: https://api.luxcrafts.co
          VITE_CHAIN_ID: ${{ secrets.VITE_CHAIN_ID_PRODUCTION }}
          VITE_CONTRACT_ADDRESS: ${{ secrets.VITE_CONTRACT_ADDRESS_PRODUCTION }}
          VITE_LUX_TOKEN_ADDRESS: ${{ secrets.VITE_LUX_TOKEN_ADDRESS_PRODUCTION }}

      - name: Create deployment package
        run: |
          tar -czf luxcrafts-production.tar.gz dist/ dokploy.config.json package.json package-lock.json
          echo "📦 Production package created"

      - name: Deploy to Dokploy VPS
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            # Create deployment directory
            mkdir -p /opt/luxcrafts-production/releases/$(date +%Y%m%d_%H%M%S)
            cd /opt/luxcrafts-production
            
            # Backup current deployment
            if [ -d "current" ]; then
              cp -r current backup_$(date +%Y%m%d_%H%M%S)
              echo "✅ Backup created"
            fi
            
            # Download and extract new release
            wget -O luxcrafts-production.tar.gz "${{ secrets.DEPLOYMENT_ARTIFACT_URL }}"
            tar -xzf luxcrafts-production.tar.gz -C releases/$(date +%Y%m%d_%H%M%S)/
            
            # Update symlink to new release
            ln -sfn releases/$(date +%Y%m%d_%H%M%S) current
            
            # Deploy with Dokploy
            dokploy deploy --config current/dokploy.config.json --domain luxcrafts.co
            
            echo "🚀 Production deployment initiated"

      - name: Wait for deployment and health check
        run: |
          echo "⏳ Waiting for deployment to complete..."
          sleep 120
          
          # Health check with retries
          for i in {1..5}; do
            if curl -f https://luxcrafts.co/health || curl -f https://luxcrafts.co/; then
              echo "✅ Production deployment health check passed"
              break
            else
              echo "⚠️ Health check attempt $i failed, retrying..."
              sleep 30
            fi
            
            if [ $i -eq 5 ]; then
              echo "❌ Production deployment health check failed after 5 attempts"
              exit 1
            fi
          done

      - name: Verify SSL and domain configuration
        run: |
          echo "🔒 Verifying SSL certificate..."
          curl -I https://luxcrafts.co | grep -i "HTTP/2 200" || exit 1
          
          echo "🌐 Verifying www redirect..."
          curl -I https://www.luxcrafts.co | grep -i "location: https://luxcrafts.co" || echo "⚠️ WWW redirect not configured"
          
          echo "✅ Domain configuration verified"

      - name: Cleanup old releases
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            cd /opt/luxcrafts-production/releases
            # Keep only the 5 most recent releases
            ls -t | tail -n +6 | xargs -r rm -rf
            echo "🧹 Old releases cleaned up"

      - name: Notify deployment success
        run: |
          echo "🎉 Production deployment to luxcrafts.co completed successfully!"
          echo "🌐 Live at: https://luxcrafts.co"
          echo "📊 Deployment time: $(date)"

  # Performance Testing
  performance-test:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          urls: |
            https://staging.luxcrafts.com
            https://staging.luxcrafts.com/marketplace
            https://staging.luxcrafts.com/services
          uploadArtifacts: true
          temporaryPublicStorage: true

      - name: Run load testing
        run: |
          npm install -g artillery
          artillery run tests/load-test.yml

  # Security Scan Deployed Application
  security-scan-deployed:
    needs: deploy-staging
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/develop'
    steps:
      - name: Run OWASP ZAP scan
        uses: zaproxy/action-full-scan@v0.7.0
        with:
          target: 'https://staging.luxcrafts.com'
          rules_file_name: '.zap/rules.tsv'
          cmd_options: '-a'

  # Notify deployment status
  notify:
    needs: [deploy-staging, deploy-production]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify Slack
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}

      - name: Notify Discord
        uses: Ilshidur/action-discord@master
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        with:
          args: |
            🚀 **Luxcrafts Deployment Status**
            **Environment:** ${{ github.ref == 'refs/heads/main' && 'Production' || 'Staging' }}
            **Status:** ${{ job.status }}
            **Commit:** ${{ github.sha }}
            **Author:** ${{ github.actor }}
            **Branch:** ${{ github.ref_name }}