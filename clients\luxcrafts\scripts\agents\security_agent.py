#!/usr/bin/env python3
"""
Security and Compliance Agent

This agent handles security monitoring, vulnerability scanning, compliance checks,
and security incident response for the Luxcrafts platform.
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import time
import hashlib
import secrets
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import requests
from cryptography.fernet import <PERSON><PERSON>t
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SecurityConfig:
    environment: str
    host: str
    scan_interval: int = 3600  # 1 hour
    vulnerability_db_url: str = "https://services.nvd.nist.gov/rest/json/cves/1.0"
    compliance_frameworks: List[str] = None
    alert_thresholds: Dict[str, int] = None
    encryption_key: Optional[str] = None
    
    def __post_init__(self):
        if self.compliance_frameworks is None:
            self.compliance_frameworks = ["SOC2", "GDPR", "CCPA", "PCI-DSS"]
        if self.alert_thresholds is None:
            self.alert_thresholds = {
                "critical_vulnerabilities": 0,
                "high_vulnerabilities": 5,
                "failed_logins": 10,
                "suspicious_activities": 3
            }

class SecurityAgent:
    """AI-powered security and compliance agent"""
    
    def __init__(self, config: SecurityConfig):
        self.config = config
        self.security_log: List[Dict[str, Any]] = []
        self.vulnerability_cache: Dict[str, Any] = {}
        self.threat_indicators: List[Dict[str, Any]] = []
        self.compliance_status: Dict[str, Any] = {}
        self.encryption_key = self._setup_encryption()
        
    def log_security_event(self, event_type: str, severity: str, details: str = "", metadata: Dict = None):
        """Log security events for audit and analysis"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "severity": severity,
            "details": details,
            "metadata": metadata or {},
            "environment": self.config.environment,
            "host": self.config.host
        }
        self.security_log.append(log_entry)
        logger.info(f"{event_type} [{severity}]: {details}")
    
    def _setup_encryption(self) -> Fernet:
        """Setup encryption for sensitive data"""
        try:
            if self.config.encryption_key:
                key = base64.urlsafe_b64decode(self.config.encryption_key)
            else:
                # Generate a new key if none provided
                key = Fernet.generate_key()
                logger.warning("No encryption key provided, generated new key")
            
            return Fernet(key)
        except Exception as e:
            self.log_security_event(
                "Encryption Setup",
                "ERROR",
                f"Failed to setup encryption: {str(e)}"
            )
            return None
    
    def scan_vulnerabilities(self) -> Dict[str, Any]:
        """Scan for known vulnerabilities in dependencies"""
        try:
            vulnerabilities = {
                "critical": [],
                "high": [],
                "medium": [],
                "low": [],
                "scan_timestamp": datetime.now().isoformat()
            }
            
            # NPM audit for Node.js dependencies
            try:
                result = subprocess.run(
                    ["npm", "audit", "--json"],
                    capture_output=True,
                    text=True,
                    cwd="."
                )
                
                if result.returncode == 0 or result.stdout:
                    audit_data = json.loads(result.stdout)
                    
                    if "vulnerabilities" in audit_data:
                        for vuln_name, vuln_data in audit_data["vulnerabilities"].items():
                            severity = vuln_data.get("severity", "unknown")
                            if severity in vulnerabilities:
                                vulnerabilities[severity].append({
                                    "name": vuln_name,
                                    "severity": severity,
                                    "via": vuln_data.get("via", []),
                                    "effects": vuln_data.get("effects", []),
                                    "range": vuln_data.get("range", "unknown")
                                })
                
            except Exception as e:
                logger.warning(f"NPM audit failed: {str(e)}")
            
            # Python security scan (if requirements.txt exists)
            try:
                if os.path.exists("requirements-agents.txt"):
                    result = subprocess.run(
                        ["pip-audit", "--format=json", "-r", "requirements-agents.txt"],
                        capture_output=True,
                        text=True
                    )
                    
                    if result.returncode == 0 and result.stdout:
                        pip_audit_data = json.loads(result.stdout)
                        for vuln in pip_audit_data:
                            severity = self._map_cvss_to_severity(vuln.get("vulnerability", {}).get("cvss", 0))
                            vulnerabilities[severity].append({
                                "name": vuln.get("package", "unknown"),
                                "severity": severity,
                                "cve": vuln.get("vulnerability", {}).get("id", "unknown"),
                                "description": vuln.get("vulnerability", {}).get("description", "")
                            })
                            
            except Exception as e:
                logger.warning(f"Python security scan failed: {str(e)}")
            
            # Docker image scanning (if Dockerfile exists)
            if os.path.exists("Dockerfile"):
                try:
                    result = subprocess.run(
                        ["trivy", "fs", "--format", "json", "."],
                        capture_output=True,
                        text=True
                    )
                    
                    if result.returncode == 0 and result.stdout:
                        trivy_data = json.loads(result.stdout)
                        for result_item in trivy_data.get("Results", []):
                            for vuln in result_item.get("Vulnerabilities", []):
                                severity = vuln.get("Severity", "unknown").lower()
                                if severity in vulnerabilities:
                                    vulnerabilities[severity].append({
                                        "name": vuln.get("PkgName", "unknown"),
                                        "severity": severity,
                                        "cve": vuln.get("VulnerabilityID", "unknown"),
                                        "description": vuln.get("Description", "")
                                    })
                                    
                except Exception as e:
                    logger.warning(f"Docker image scan failed: {str(e)}")
            
            total_vulns = sum(len(vulnerabilities[sev]) for sev in ["critical", "high", "medium", "low"])
            
            self.log_security_event(
                "Vulnerability Scan",
                "INFO",
                f"Found {total_vulns} vulnerabilities: {len(vulnerabilities['critical'])} critical, {len(vulnerabilities['high'])} high",
                vulnerabilities
            )
            
            return vulnerabilities
            
        except Exception as e:
            self.log_security_event(
                "Vulnerability Scan",
                "ERROR",
                f"Vulnerability scan failed: {str(e)}"
            )
            return {}
    
    def _map_cvss_to_severity(self, cvss_score: float) -> str:
        """Map CVSS score to severity level"""
        if cvss_score >= 9.0:
            return "critical"
        elif cvss_score >= 7.0:
            return "high"
        elif cvss_score >= 4.0:
            return "medium"
        else:
            return "low"
    
    def check_compliance(self) -> Dict[str, Any]:
        """Check compliance with various frameworks"""
        try:
            compliance_results = {
                "timestamp": datetime.now().isoformat(),
                "frameworks": {},
                "overall_score": 0
            }
            
            for framework in self.config.compliance_frameworks:
                framework_result = self._check_framework_compliance(framework)
                compliance_results["frameworks"][framework] = framework_result
            
            # Calculate overall compliance score
            if compliance_results["frameworks"]:
                total_score = sum(fw["score"] for fw in compliance_results["frameworks"].values())
                compliance_results["overall_score"] = total_score / len(compliance_results["frameworks"])
            
            self.log_security_event(
                "Compliance Check",
                "INFO",
                f"Overall compliance score: {compliance_results['overall_score']:.1f}%",
                compliance_results
            )
            
            return compliance_results
            
        except Exception as e:
            self.log_security_event(
                "Compliance Check",
                "ERROR",
                f"Compliance check failed: {str(e)}"
            )
            return {}
    
    def _check_framework_compliance(self, framework: str) -> Dict[str, Any]:
        """Check compliance for a specific framework"""
        checks = {
            "SOC2": self._check_soc2_compliance,
            "GDPR": self._check_gdpr_compliance,
            "CCPA": self._check_ccpa_compliance,
            "PCI-DSS": self._check_pci_compliance
        }
        
        if framework in checks:
            return checks[framework]()
        else:
            return {"score": 0, "checks": [], "status": "unknown"}
    
    def _check_soc2_compliance(self) -> Dict[str, Any]:
        """Check SOC 2 compliance requirements"""
        checks = [
            {"name": "Encryption at Rest", "status": self._check_encryption_at_rest()},
            {"name": "Encryption in Transit", "status": self._check_encryption_in_transit()},
            {"name": "Access Controls", "status": self._check_access_controls()},
            {"name": "Audit Logging", "status": self._check_audit_logging()},
            {"name": "Backup Procedures", "status": self._check_backup_procedures()}
        ]
        
        passed_checks = sum(1 for check in checks if check["status"])
        score = (passed_checks / len(checks)) * 100
        
        return {
            "score": score,
            "checks": checks,
            "status": "compliant" if score >= 80 else "non-compliant"
        }
    
    def _check_gdpr_compliance(self) -> Dict[str, Any]:
        """Check GDPR compliance requirements"""
        checks = [
            {"name": "Data Encryption", "status": self._check_data_encryption()},
            {"name": "Right to be Forgotten", "status": self._check_data_deletion()},
            {"name": "Data Portability", "status": self._check_data_export()},
            {"name": "Consent Management", "status": self._check_consent_management()},
            {"name": "Breach Notification", "status": self._check_breach_notification()}
        ]
        
        passed_checks = sum(1 for check in checks if check["status"])
        score = (passed_checks / len(checks)) * 100
        
        return {
            "score": score,
            "checks": checks,
            "status": "compliant" if score >= 80 else "non-compliant"
        }
    
    def _check_ccpa_compliance(self) -> Dict[str, Any]:
        """Check CCPA compliance requirements"""
        checks = [
            {"name": "Privacy Policy", "status": self._check_privacy_policy()},
            {"name": "Data Collection Notice", "status": self._check_data_collection_notice()},
            {"name": "Opt-out Mechanism", "status": self._check_opt_out_mechanism()},
            {"name": "Data Access Rights", "status": self._check_data_access_rights()}
        ]
        
        passed_checks = sum(1 for check in checks if check["status"])
        score = (passed_checks / len(checks)) * 100
        
        return {
            "score": score,
            "checks": checks,
            "status": "compliant" if score >= 80 else "non-compliant"
        }
    
    def _check_pci_compliance(self) -> Dict[str, Any]:
        """Check PCI-DSS compliance requirements"""
        checks = [
            {"name": "Secure Network", "status": self._check_secure_network()},
            {"name": "Cardholder Data Protection", "status": self._check_cardholder_data_protection()},
            {"name": "Vulnerability Management", "status": self._check_vulnerability_management()},
            {"name": "Access Control", "status": self._check_strong_access_control()},
            {"name": "Network Monitoring", "status": self._check_network_monitoring()}
        ]
        
        passed_checks = sum(1 for check in checks if check["status"])
        score = (passed_checks / len(checks)) * 100
        
        return {
            "score": score,
            "checks": checks,
            "status": "compliant" if score >= 80 else "non-compliant"
        }
    
    # Compliance check helper methods
    def _check_encryption_at_rest(self) -> bool:
        """Check if data is encrypted at rest"""
        # Check database encryption, file system encryption, etc.
        return os.path.exists(".env") and "ENCRYPTION_KEY" in open(".env").read()
    
    def _check_encryption_in_transit(self) -> bool:
        """Check if data is encrypted in transit"""
        # Check SSL/TLS configuration
        return os.path.exists("nginx.conf") and "ssl" in open("nginx.conf").read()
    
    def _check_access_controls(self) -> bool:
        """Check access control implementation"""
        # Check authentication and authorization mechanisms
        return True  # Placeholder - implement actual check
    
    def _check_audit_logging(self) -> bool:
        """Check audit logging implementation"""
        # Check if audit logs are being generated
        return True  # Placeholder - implement actual check
    
    def _check_backup_procedures(self) -> bool:
        """Check backup procedures"""
        # Check backup configuration
        return os.path.exists("docker-compose.yml") and "backup" in open("docker-compose.yml").read()
    
    def _check_data_encryption(self) -> bool:
        """Check data encryption for GDPR"""
        return self._check_encryption_at_rest()
    
    def _check_data_deletion(self) -> bool:
        """Check data deletion capabilities"""
        return True  # Placeholder - implement actual check
    
    def _check_data_export(self) -> bool:
        """Check data export capabilities"""
        return True  # Placeholder - implement actual check
    
    def _check_consent_management(self) -> bool:
        """Check consent management system"""
        return True  # Placeholder - implement actual check
    
    def _check_breach_notification(self) -> bool:
        """Check breach notification procedures"""
        return True  # Placeholder - implement actual check
    
    def _check_privacy_policy(self) -> bool:
        """Check privacy policy existence"""
        return True  # Placeholder - implement actual check
    
    def _check_data_collection_notice(self) -> bool:
        """Check data collection notice"""
        return True  # Placeholder - implement actual check
    
    def _check_opt_out_mechanism(self) -> bool:
        """Check opt-out mechanism"""
        return True  # Placeholder - implement actual check
    
    def _check_data_access_rights(self) -> bool:
        """Check data access rights implementation"""
        return True  # Placeholder - implement actual check
    
    def _check_secure_network(self) -> bool:
        """Check secure network configuration"""
        return self._check_encryption_in_transit()
    
    def _check_cardholder_data_protection(self) -> bool:
        """Check cardholder data protection"""
        return self._check_encryption_at_rest()
    
    def _check_vulnerability_management(self) -> bool:
        """Check vulnerability management"""
        return True  # Placeholder - implement actual check
    
    def _check_strong_access_control(self) -> bool:
        """Check strong access control measures"""
        return self._check_access_controls()
    
    def _check_network_monitoring(self) -> bool:
        """Check network monitoring implementation"""
        return True  # Placeholder - implement actual check
    
    def detect_threats(self) -> List[Dict[str, Any]]:
        """Detect potential security threats"""
        try:
            threats = []
            
            # Check for suspicious file changes
            suspicious_files = self._check_file_integrity()
            if suspicious_files:
                threats.append({
                    "type": "file_integrity",
                    "severity": "medium",
                    "description": f"Suspicious file changes detected: {len(suspicious_files)} files",
                    "files": suspicious_files
                })
            
            # Check for unusual network activity
            network_threats = self._check_network_activity()
            threats.extend(network_threats)
            
            # Check for failed authentication attempts
            auth_threats = self._check_authentication_failures()
            threats.extend(auth_threats)
            
            self.log_security_event(
                "Threat Detection",
                "INFO",
                f"Detected {len(threats)} potential threats",
                {"threats": threats}
            )
            
            return threats
            
        except Exception as e:
            self.log_security_event(
                "Threat Detection",
                "ERROR",
                f"Threat detection failed: {str(e)}"
            )
            return []
    
    def _check_file_integrity(self) -> List[str]:
        """Check for suspicious file changes"""
        suspicious_files = []
        
        # Check for recently modified sensitive files
        sensitive_patterns = [
            "*.env*",
            "*config*",
            "*secret*",
            "*key*",
            "*password*"
        ]
        
        try:
            import glob
            for pattern in sensitive_patterns:
                files = glob.glob(pattern, recursive=True)
                for file_path in files:
                    if os.path.exists(file_path):
                        mtime = os.path.getmtime(file_path)
                        if time.time() - mtime < 3600:  # Modified in last hour
                            suspicious_files.append(file_path)
        except Exception as e:
            logger.warning(f"File integrity check failed: {str(e)}")
        
        return suspicious_files
    
    def _check_network_activity(self) -> List[Dict[str, Any]]:
        """Check for unusual network activity"""
        threats = []
        
        # Placeholder for network monitoring
        # In a real implementation, this would check:
        # - Unusual outbound connections
        # - Port scanning attempts
        # - DDoS patterns
        # - Suspicious IP addresses
        
        return threats
    
    def _check_authentication_failures(self) -> List[Dict[str, Any]]:
        """Check for authentication failures"""
        threats = []
        
        # Placeholder for authentication monitoring
        # In a real implementation, this would check:
        # - Failed login attempts
        # - Brute force attacks
        # - Unusual login patterns
        # - Privilege escalation attempts
        
        return threats
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate comprehensive security report"""
        try:
            vulnerabilities = self.scan_vulnerabilities()
            compliance = self.check_compliance()
            threats = self.detect_threats()
            
            report = {
                "timestamp": datetime.now().isoformat(),
                "environment": self.config.environment,
                "host": self.config.host,
                "summary": {
                    "total_vulnerabilities": sum(len(vulnerabilities.get(sev, [])) for sev in ["critical", "high", "medium", "low"]),
                    "critical_vulnerabilities": len(vulnerabilities.get("critical", [])),
                    "compliance_score": compliance.get("overall_score", 0),
                    "active_threats": len(threats)
                },
                "vulnerabilities": vulnerabilities,
                "compliance": compliance,
                "threats": threats,
                "security_events": self.security_log[-50:],  # Last 50 events
                "recommendations": self._generate_recommendations(vulnerabilities, compliance, threats)
            }
            
            self.log_security_event(
                "Security Report Generated",
                "INFO",
                f"Report generated with {report['summary']['total_vulnerabilities']} vulnerabilities, {report['summary']['compliance_score']:.1f}% compliance"
            )
            
            return report
            
        except Exception as e:
            self.log_security_event(
                "Security Report Generation",
                "ERROR",
                f"Failed to generate security report: {str(e)}"
            )
            return {}
    
    def _generate_recommendations(self, vulnerabilities: Dict, compliance: Dict, threats: List) -> List[str]:
        """Generate security recommendations based on findings"""
        recommendations = []
        
        # Vulnerability-based recommendations
        if vulnerabilities.get("critical"):
            recommendations.append("URGENT: Address critical vulnerabilities immediately")
        
        if len(vulnerabilities.get("high", [])) > 5:
            recommendations.append("High priority: Reduce number of high-severity vulnerabilities")
        
        # Compliance-based recommendations
        if compliance.get("overall_score", 0) < 80:
            recommendations.append("Improve compliance posture to meet industry standards")
        
        # Threat-based recommendations
        if threats:
            recommendations.append("Investigate and mitigate detected security threats")
        
        # General recommendations
        recommendations.extend([
            "Implement regular security training for development team",
            "Establish incident response procedures",
            "Enable automated security scanning in CI/CD pipeline",
            "Implement zero-trust network architecture",
            "Regular security audits and penetration testing"
        ])
        
        return recommendations

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Luxcrafts Security Agent')
    parser.add_argument('--environment', default='staging', choices=['production', 'staging', 'development'])
    parser.add_argument('--host', default='localhost')
    parser.add_argument('--config-file', help='Path to configuration file')
    parser.add_argument('--scan-only', action='store_true', help='Run vulnerability scan only')
    parser.add_argument('--compliance-only', action='store_true', help='Run compliance check only')
    parser.add_argument('--report', action='store_true', help='Generate full security report')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config_file and os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            config_data = json.load(f)
    else:
        config_data = {
            "environment": args.environment,
            "host": args.host,
            "scan_interval": 3600,
            "encryption_key": os.getenv('SECURITY_ENCRYPTION_KEY')
        }
    
    config = SecurityConfig(**config_data)
    agent = SecurityAgent(config)
    
    if args.scan_only:
        vulnerabilities = agent.scan_vulnerabilities()
        print(json.dumps(vulnerabilities, indent=2))
    elif args.compliance_only:
        compliance = agent.check_compliance()
        print(json.dumps(compliance, indent=2))
    elif args.report:
        report = agent.generate_security_report()
        print(json.dumps(report, indent=2))
    else:
        # Run full security assessment
        logger.info(f"Starting security assessment for {config.environment} environment")
        report = agent.generate_security_report()
        
        # Save report to file
        report_file = f"security_report_{config.environment}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Security report saved to {report_file}")
        print(json.dumps(report['summary'], indent=2))

if __name__ == "__main__":
    main()