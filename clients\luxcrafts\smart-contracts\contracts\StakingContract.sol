// SPDX-License-Identifier: MIT
pragma solidity ^0.8.24;

import "@openzeppelin/contracts-upgradeable/security/ReentrancyGuardUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/security/PausableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/access/AccessControlUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

/**
 * @title StakingContract
 * @dev Staking contract for LUX tokens with multiple pools and reward mechanisms
 * @notice Users can stake LUX tokens to earn rewards and participate in governance
 */
contract StakingContract is
    Initializable,
    ReentrancyGuardUpgradeable,
    PausableUpgradeable,
    AccessControlUpgradeable,
    UUPSUpgradeable
{
    using SafeMath for uint256;

    bytes32 public constant ADMIN_ROLE = keccak256("ADMIN_ROLE");
    bytes32 public constant UPGRADER_ROLE = keccak256("UPGRADER_ROLE");
    bytes32 public constant REWARD_DISTRIBUTOR_ROLE = keccak256("REWARD_DISTRIBUTOR_ROLE");

    IERC20 public luxToken;

    struct StakingPool {
        uint256 poolId;
        string name;
        uint256 minStakeAmount;
        uint256 maxStakeAmount;
        uint256 lockPeriod; // in seconds
        uint256 apy; // Annual Percentage Yield in basis points (10000 = 100%)
        uint256 totalStaked;
        uint256 maxPoolSize;
        bool isActive;
        uint256 createdAt;
        uint256 rewardRate; // tokens per second per token staked
        uint256 lastUpdateTime;
        uint256 rewardPerTokenStored;
    }

    struct UserStake {
        uint256 amount;
        uint256 poolId;
        uint256 stakedAt;
        uint256 unlockTime;
        uint256 userRewardPerTokenPaid;
        uint256 rewards;
        bool isActive;
    }

    struct UserInfo {
        uint256[] stakeIds;
        uint256 totalStaked;
        uint256 totalRewards;
        mapping(uint256 => uint256) poolStakes; // poolId => total amount staked
    }

    // State variables
    mapping(uint256 => StakingPool) public stakingPools;
    mapping(address => UserInfo) public userInfo;
    mapping(uint256 => UserStake) public userStakes; // stakeId => UserStake
    mapping(address => mapping(uint256 => uint256[])) public userPoolStakes; // user => poolId => stakeIds
    
    uint256 public nextPoolId;
    uint256 public nextStakeId;
    uint256 public totalStakedGlobal;
    uint256 public totalRewardsDistributed;
    
    // Emergency withdrawal settings
    bool public emergencyWithdrawEnabled;
    uint256 public emergencyWithdrawPenalty; // in basis points
    
    // Events
    event PoolCreated(
        uint256 indexed poolId,
        string name,
        uint256 minStakeAmount,
        uint256 maxStakeAmount,
        uint256 lockPeriod,
        uint256 apy
    );
    
    event Staked(
        address indexed user,
        uint256 indexed poolId,
        uint256 indexed stakeId,
        uint256 amount,
        uint256 unlockTime
    );
    
    event Unstaked(
        address indexed user,
        uint256 indexed poolId,
        uint256 indexed stakeId,
        uint256 amount
    );
    
    event RewardClaimed(
        address indexed user,
        uint256 indexed stakeId,
        uint256 amount
    );
    
    event EmergencyWithdraw(
        address indexed user,
        uint256 indexed stakeId,
        uint256 amount,
        uint256 penalty
    );
    
    event PoolUpdated(uint256 indexed poolId, uint256 newApy, bool isActive);
    
    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }
    
    function initialize(
        address _luxToken,
        address _admin
    ) public initializer {
        __ReentrancyGuard_init();
        __Pausable_init();
        __AccessControl_init();
        __UUPSUpgradeable_init();
        
        luxToken = IERC20(_luxToken);
        
        _grantRole(DEFAULT_ADMIN_ROLE, _admin);
        _grantRole(ADMIN_ROLE, _admin);
        _grantRole(UPGRADER_ROLE, _admin);
        _grantRole(REWARD_DISTRIBUTOR_ROLE, _admin);
        
        nextPoolId = 1;
        nextStakeId = 1;
        emergencyWithdrawPenalty = 1000; // 10%
    }
    
    /**
     * @dev Create a new staking pool
     */
    function createPool(
        string memory _name,
        uint256 _minStakeAmount,
        uint256 _maxStakeAmount,
        uint256 _lockPeriod,
        uint256 _apy,
        uint256 _maxPoolSize
    ) public onlyRole(ADMIN_ROLE) returns (uint256) {
        require(_minStakeAmount > 0, "Invalid min stake amount");
        require(_maxStakeAmount >= _minStakeAmount, "Invalid max stake amount");
        require(_apy > 0, "Invalid APY");
        require(_maxPoolSize > 0, "Invalid max pool size");
        
        uint256 poolId = nextPoolId++;
        uint256 rewardRate = _apy.div(365 days).div(10000); // Convert APY to reward rate per second
        
        stakingPools[poolId] = StakingPool({
            poolId: poolId,
            name: _name,
            minStakeAmount: _minStakeAmount,
            maxStakeAmount: _maxStakeAmount,
            lockPeriod: _lockPeriod,
            apy: _apy,
            totalStaked: 0,
            maxPoolSize: _maxPoolSize,
            isActive: true,
            createdAt: block.timestamp,
            rewardRate: rewardRate,
            lastUpdateTime: block.timestamp,
            rewardPerTokenStored: 0
        });
        
        emit PoolCreated(poolId, _name, _minStakeAmount, _maxStakeAmount, _lockPeriod, _apy);
        return poolId;
    }
    
    /**
     * @dev Stake tokens in a specific pool
     */
    function stake(uint256 _poolId, uint256 _amount) public nonReentrant whenNotPaused {
        require(_amount > 0, "Invalid stake amount");
        require(stakingPools[_poolId].isActive, "Pool is not active");
        
        StakingPool storage pool = stakingPools[_poolId];
        require(_amount >= pool.minStakeAmount, "Below minimum stake amount");
        require(_amount <= pool.maxStakeAmount, "Above maximum stake amount");
        require(pool.totalStaked.add(_amount) <= pool.maxPoolSize, "Pool capacity exceeded");
        
        // Update pool rewards
        _updatePool(_poolId);
        
        // Transfer tokens from user
        require(luxToken.transferFrom(msg.sender, address(this), _amount), "Transfer failed");
        
        // Create stake record
        uint256 stakeId = nextStakeId++;
        uint256 unlockTime = block.timestamp.add(pool.lockPeriod);
        
        userStakes[stakeId] = UserStake({
            amount: _amount,
            poolId: _poolId,
            stakedAt: block.timestamp,
            unlockTime: unlockTime,
            userRewardPerTokenPaid: pool.rewardPerTokenStored,
            rewards: 0,
            isActive: true
        });
        
        // Update user info
        UserInfo storage user = userInfo[msg.sender];
        user.stakeIds.push(stakeId);
        user.totalStaked = user.totalStaked.add(_amount);
        user.poolStakes[_poolId] = user.poolStakes[_poolId].add(_amount);
        userPoolStakes[msg.sender][_poolId].push(stakeId);
        
        // Update pool and global stats
        pool.totalStaked = pool.totalStaked.add(_amount);
        totalStakedGlobal = totalStakedGlobal.add(_amount);
        
        emit Staked(msg.sender, _poolId, stakeId, _amount, unlockTime);
    }
    
    /**
     * @dev Unstake tokens from a specific stake
     */
    function unstake(uint256 _stakeId) public nonReentrant {
        UserStake storage userStake = userStakes[_stakeId];
        require(userStake.isActive, "Stake is not active");
        require(block.timestamp >= userStake.unlockTime, "Stake is still locked");
        
        uint256 poolId = userStake.poolId;
        uint256 amount = userStake.amount;
        
        // Update pool rewards
        _updatePool(poolId);
        
        // Calculate and transfer rewards
        uint256 reward = _calculateReward(_stakeId);
        if (reward > 0) {
            userStake.rewards = userStake.rewards.add(reward);
            require(luxToken.transfer(msg.sender, reward), "Reward transfer failed");
            totalRewardsDistributed = totalRewardsDistributed.add(reward);
            emit RewardClaimed(msg.sender, _stakeId, reward);
        }
        
        // Transfer staked tokens back to user
        require(luxToken.transfer(msg.sender, amount), "Unstake transfer failed");
        
        // Update records
        userStake.isActive = false;
        
        UserInfo storage user = userInfo[msg.sender];
        user.totalStaked = user.totalStaked.sub(amount);
        user.poolStakes[poolId] = user.poolStakes[poolId].sub(amount);
        user.totalRewards = user.totalRewards.add(reward);
        
        StakingPool storage pool = stakingPools[poolId];
        pool.totalStaked = pool.totalStaked.sub(amount);
        totalStakedGlobal = totalStakedGlobal.sub(amount);
        
        emit Unstaked(msg.sender, poolId, _stakeId, amount);
    }
    
    /**
     * @dev Claim rewards for a specific stake
     */
    function claimRewards(uint256 _stakeId) public nonReentrant {
        UserStake storage userStake = userStakes[_stakeId];
        require(userStake.isActive, "Stake is not active");
        
        // Update pool rewards
        _updatePool(userStake.poolId);
        
        uint256 reward = _calculateReward(_stakeId);
        require(reward > 0, "No rewards to claim");
        
        userStake.rewards = userStake.rewards.add(reward);
        userStake.userRewardPerTokenPaid = stakingPools[userStake.poolId].rewardPerTokenStored;
        
        require(luxToken.transfer(msg.sender, reward), "Reward transfer failed");
        
        userInfo[msg.sender].totalRewards = userInfo[msg.sender].totalRewards.add(reward);
        totalRewardsDistributed = totalRewardsDistributed.add(reward);
        
        emit RewardClaimed(msg.sender, _stakeId, reward);
    }
    
    /**
     * @dev Emergency withdraw with penalty
     */
    function emergencyWithdraw(uint256 _stakeId) public nonReentrant {
        require(emergencyWithdrawEnabled, "Emergency withdraw disabled");
        
        UserStake storage userStake = userStakes[_stakeId];
        require(userStake.isActive, "Stake is not active");
        
        uint256 amount = userStake.amount;
        uint256 penalty = amount.mul(emergencyWithdrawPenalty).div(10000);
        uint256 withdrawAmount = amount.sub(penalty);
        
        // Update records
        userStake.isActive = false;
        
        UserInfo storage user = userInfo[msg.sender];
        user.totalStaked = user.totalStaked.sub(amount);
        user.poolStakes[userStake.poolId] = user.poolStakes[userStake.poolId].sub(amount);
        
        StakingPool storage pool = stakingPools[userStake.poolId];
        pool.totalStaked = pool.totalStaked.sub(amount);
        totalStakedGlobal = totalStakedGlobal.sub(amount);
        
        // Transfer tokens (minus penalty)
        require(luxToken.transfer(msg.sender, withdrawAmount), "Emergency withdraw failed");
        
        emit EmergencyWithdraw(msg.sender, _stakeId, withdrawAmount, penalty);
    }
    
    /**
     * @dev Update pool reward calculations
     */
    function _updatePool(uint256 _poolId) internal {
        StakingPool storage pool = stakingPools[_poolId];
        
        if (pool.totalStaked == 0) {
            pool.lastUpdateTime = block.timestamp;
            return;
        }
        
        uint256 timeElapsed = block.timestamp.sub(pool.lastUpdateTime);
        uint256 reward = timeElapsed.mul(pool.rewardRate);
        pool.rewardPerTokenStored = pool.rewardPerTokenStored.add(
            reward.mul(1e18).div(pool.totalStaked)
        );
        pool.lastUpdateTime = block.timestamp;
    }
    
    /**
     * @dev Calculate pending rewards for a stake
     */
    function _calculateReward(uint256 _stakeId) internal view returns (uint256) {
        UserStake storage userStake = userStakes[_stakeId];
        StakingPool storage pool = stakingPools[userStake.poolId];
        
        uint256 rewardPerToken = pool.rewardPerTokenStored;
        if (pool.totalStaked > 0) {
            uint256 timeElapsed = block.timestamp.sub(pool.lastUpdateTime);
            uint256 reward = timeElapsed.mul(pool.rewardRate);
            rewardPerToken = rewardPerToken.add(
                reward.mul(1e18).div(pool.totalStaked)
            );
        }
        
        return userStake.amount.mul(
            rewardPerToken.sub(userStake.userRewardPerTokenPaid)
        ).div(1e18);
    }
    
    /**
     * @dev Get pending rewards for a stake
     */
    function getPendingRewards(uint256 _stakeId) public view returns (uint256) {
        return _calculateReward(_stakeId);
    }
    
    /**
     * @dev Get user's stakes in a pool
     */
    function getUserPoolStakes(address _user, uint256 _poolId) public view returns (uint256[] memory) {
        return userPoolStakes[_user][_poolId];
    }
    
    /**
     * @dev Get user's total stakes
     */
    function getUserStakes(address _user) public view returns (uint256[] memory) {
        return userInfo[_user].stakeIds;
    }
    
    /**
     * @dev Get pool information
     */
    function getPoolInfo(uint256 _poolId) public view returns (
        string memory name,
        uint256 minStakeAmount,
        uint256 maxStakeAmount,
        uint256 lockPeriod,
        uint256 apy,
        uint256 totalStaked,
        uint256 maxPoolSize,
        bool isActive
    ) {
        StakingPool storage pool = stakingPools[_poolId];
        return (
            pool.name,
            pool.minStakeAmount,
            pool.maxStakeAmount,
            pool.lockPeriod,
            pool.apy,
            pool.totalStaked,
            pool.maxPoolSize,
            pool.isActive
        );
    }
    
    // Admin functions
    function updatePool(
        uint256 _poolId,
        uint256 _newApy,
        bool _isActive
    ) public onlyRole(ADMIN_ROLE) {
        StakingPool storage pool = stakingPools[_poolId];
        pool.apy = _newApy;
        pool.rewardRate = _newApy.div(365 days).div(10000);
        pool.isActive = _isActive;
        
        emit PoolUpdated(_poolId, _newApy, _isActive);
    }
    
    function setEmergencyWithdraw(bool _enabled, uint256 _penalty) public onlyRole(ADMIN_ROLE) {
        emergencyWithdrawEnabled = _enabled;
        emergencyWithdrawPenalty = _penalty;
    }
    
    function pause() public onlyRole(ADMIN_ROLE) {
        _pause();
    }
    
    function unpause() public onlyRole(ADMIN_ROLE) {
        _unpause();
    }
    
    function _authorizeUpgrade(address newImplementation) internal onlyRole(UPGRADER_ROLE) override {}
}