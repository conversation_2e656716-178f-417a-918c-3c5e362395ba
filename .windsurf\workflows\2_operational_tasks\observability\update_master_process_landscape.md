---
description: Updates the Master Process Landscape diagram based on the current `primary_activities_process_matrix.md` and `support_activities_process_matrix.md`.
---

# Workflow: Update Master Process Landscape Diagram

This workflow regenerates the `docs/processes/diagrams/Master_Process_Landscape.mmd` file by parsing data from `docs/processes/primary_activities_process_matrix.md` and `docs/processes/support_activities_process_matrix.md`.

## Steps:

1.  **Read Process Matrices:**
    *   Action: Read the entire content of `docs/processes/primary_activities_process_matrix.md` and `docs/processes/support_activities_process_matrix.md`.
    *   Tool: `mcp9_read_multiple_files` (to get content of both files) or two separate `mcp9_read_file` calls.

2.  **Parse Process Data from Matrices:**
    *   Action: Parse the Markdown tables from both matrix files to extract relevant data for each process.
    *   Concatenate the lists of processes obtained from both files.
    *   Identify if a process belongs to 'PRIMARY' or 'SUPPORT' activities (e.g., based on the source file or a mapping of `Value Chain Activity` to category).
    *   Required columns for diagram generation:
        *   `Process ID` (e.g., `CPO_P001`, `CTO_P002`)
        *   `Process Name`
        *   `Value Chain Activity` (e.g., MKT_SAL, CONB for primary; STRAT, FIN for support) - This will be the direct parent subgraph under PRIMARY/SUPPORT.
        *   `Organizational Layer` (e.g., Executive, Management, Operational) - For ordering/grouping within the `Value Chain Activity` subgraph.
        *   `Status` (e.g., Planning, Draft, Active)
        *   `Definition Doc Link` (Column name from new matrix, e.g., `Definition Doc Link`, expected to contain paths like `docs/processes/definitions/cpo/CPO_P001_DefineProcessItself.md` or relative paths from the matrix file that resolve to this structure).
        *   (Optional for future: `Dependencies` - a comma-separated list of upstream Process IDs for drawing arrows)
    *   Process each table row, skipping header and separator lines.
    *   Store the parsed data in a structured format (e.g., a list of dictionaries, with an added field indicating if it's a primary or support activity).

3.  **Generate Mermaid Diagram Syntax (`Master_Process_Landscape.mmd`):**
    *   Action: Construct the Mermaid `graph TD;` syntax.
    *   Define CSS classes for different statuses: `planning`, `draft`, `active`, `deprecated`.
    *   **Overall Structure:**
        *   Start with `graph TD;` and class definitions.
        *   Create main subgraphs: `subgraph SG_PRIMARY ["PRIMARY ACTIVITIES"]` and `subgraph SG_SUPPORT ["SUPPORT ACTIVITIES"]`. Set `direction TB` for these.
    *   **Value Chain Activity Subgraphs:**
        *   Iterate through the parsed processes. For each unique `Value Chain Activity` within PRIMARY activities, create a subgraph under `SG_PRIMARY`. Example: `subgraph SG_MKT_SAL ["Marketing & Sales (MKT_SAL)"] direction TB; end`.
        *   Similarly, for each unique `Value Chain Activity` within SUPPORT activities, create a subgraph under `SG_SUPPORT`. Example: `subgraph SG_STRAT ["Strategic Management (STRAT)"] direction TB; end`.
    *   **Process Nodes:**
        *   For each process, create a node within its respective `Value Chain Activity` subgraph (e.g., `CPO_P001` node goes into `SG_MKT_SAL` if its `Value Chain Activity` is `MKT_SAL`).
        *   Node ID should be the `Process ID` (e.g., `CPO_P001`).
        *   Node text should be `"[Process ID]: [Process Name]"` (e.g., `CPO_P001["CPO_P001: Define Process Itself"]`).
        *   Apply a CSS class based on the `Status` (e.g., `CPO_P001:::planning;`).
        *   Add a click event for the node: `click [Process ID] "[Relative Link to Definition]" "View [Process ID] Definition";`. The relative link needs to be calculated from `docs/processes/diagrams/` to the path specified in `Definition Doc Link`. For a definition at `docs/processes/definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md`, the relative link from `docs/processes/diagrams/Master_Process_Landscape.mmd` would be `../definitions/[officer_code]/[ID]_[DescriptiveName_PascalCase].md`.
        *   **Hierarchical Layering (E/M/O):** Within each `Value Chain Activity` subgraph, ensure processes are defined/ordered based on their `Organizational Layer`: Executive first, then Management, then Operational. This helps in visual organization.
    *   **(Optional) Dependency Links:**
        *   After all nodes are defined, if `Dependencies` data is available and parsed, create directed links: `UPSTREAM_PROCESS_ID --> CURRENT_PROCESS_ID;`.

4.  **Write/Update Master Diagram File:**
    *   Action: Write the generated Mermaid syntax to `docs/processes/diagrams/Master_Process_Landscape.mmd`. If the file exists, overwrite it.
    *   Tool: `mcp9_write_file`.

5.  **Completion:**
    *   The `Master_Process_Landscape.mmd` is now updated to reflect the new `primary_activities_process_matrix.md` and `support_activities_process_matrix.md` structure and content.
