# ESTRATIX Agentic Rules: Frontend Component Design

---

## 1. Overview

These rules define the standards for creating frontend components (e.g., Vue, React, Svelte) within the ESTRATIX framework. The goal is to produce modular, reusable, maintainable, and accessible UI components for both admin panels and public-facing storefronts.

## 2. Core Principles

- **Atomicity**: Components should be small and focused on a single responsibility (Single Responsibility Principle).
- **Encapsulation**: Components should manage their own state and expose a clear API through props and events. Logic, template, and styles should be co-located.
- **Accessibility (A11y)**: All components must be accessible to users with disabilities, adhering to WCAG 2.1 AA standards.

## 3. Rules

### 3.1. Component Structure & Naming
- **Rule R-FE-003.1**: All components must be defined in their own file (e.g., `ComponentName.vue`). For Vue, components must have a `name` property in their definition for better debugging.
- **Rule R-FE-003.2**: Use PascalCase for component filenames and when referencing them in templates (e.g., `<BaseButton>`).

### 3.2. Props
- **Rule R-FE-003.3**: All props must have type validation. For non-trivial components, use detailed object-based prop definitions with `type`, `required`, and `default` properties.
- **Rule R-FE-003.4**: Props should be treated as immutable within the component. If a prop's value needs to be changed, emit an event to the parent.

### 3.3. Events
- **Rule R-FE-003.5**: Event names must be declared (e.g., using Vue's `emits` option).
- **Rule R-FE-003.6**: Use kebab-case for event names (e.g., `update:modelValue`). When emitting events, pass a single payload object for clarity and future-proofing.

### 3.4. State Management
- **Rule R-FE-003.7**: For simple, local state, use the component's internal state management. For complex or shared state, use the approved global state management library (e.g., Pinia, Vuex). Do not create your own global state stores.

### 3.5. Accessibility
- **Rule R-FE-003.8**: All interactive elements (buttons, inputs) must be keyboard accessible and have clear focus indicators.
- **Rule R-FE-003.9**: Use appropriate ARIA (Accessible Rich Internet Applications) attributes where necessary, especially for complex components like modals, tabs, and dropdowns.
- **Rule R-FE-003.10**: All images must have descriptive `alt` tags.

## 4. Enforcement

- **Agentic Enforcement**: Frontend agents will be primed with these rules to generate compliant component scaffolds and code.
- **Linting**: ESLint and stylelint configurations will enforce naming conventions and code style.
- **Code Review**: Pull requests for new or modified components will be reviewed against these standards, with a focus on reusability and accessibility.
