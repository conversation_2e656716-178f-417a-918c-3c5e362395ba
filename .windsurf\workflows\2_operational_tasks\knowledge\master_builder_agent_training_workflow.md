---
description: Defines the standardized workflow for training the MasterBuilderAgent, responsible for generating and maintaining ESTRATIX components.
---

# Workflow: Master Builder Agent Training

**Objective**: To establish a standardized, repeatable process for training and continuously improving the `MasterBuilderAgent`. This ensures the agent remains proficient in generating high-quality, compliant ESTRATIX components as the project's standards and architecture evolve.

**Agentic Orchestrator**: `CIO_AXXX_KnowledgeManagerAgent`, `CTO_AXXX_MasterBuilderAgent` (self-training)

**Guidance**: This workflow should be executed whenever new architectural patterns, standards, or frameworks are introduced, or as part of a regular continuous improvement cycle.

---

## Phase 1: Knowledge Ingestion & Assimilation

**Objective**: To equip the `MasterBuilderAgent` with the foundational knowledge required to understand the ESTRATIX ecosystem.

1. **Ingest Core Project Rules & Standards**
   * **Action**: Feed all documents from `docs/standards/`, `docs/patterns/`, and `.windsurf/rules/` into the agent's knowledge base.
   * **Workflow Trigger**: `/knowledge_lifecycle_management`
   * **Goal**: Ensure the agent has a deep understanding of naming conventions, architectural principles (Hexagonal, DDD), and operational rules.

2. **Ingest Framework & Technology Documentation**
   * **Action**: Process the documentation for all approved frameworks and technologies (e.g., CrewAI, FastAPI, Pydantic).
   * **Workflow Trigger**: `/ingest_framework_documentation`
   * **Goal**: Build expertise in the specific tools and libraries the agent will be using for code generation.

---

## Phase 2: Core Skill Development (Sandbox Practice)

**Objective**: To develop and test the agent's ability to generate individual, compliant components in a controlled sandbox environment.

*   **Action**: Execute the primary generative workflows on a set of test definitions. For each generated component, validate its structure, content, and compliance against ESTRATIX standards.
*   **Critical Validation Point**: For component types like Processes, the agent must first run the `/process_definition` workflow to **atomically assign and register the new component ID** in the `process_matrix.md`. Only after successful registration should it proceed with the `/process_generation` workflow. This "define-then-generate" sequence is a core competency.
*   **Workflow Triggers (Sequential Practice)**:
    1.  `/tool_generation`
    2.  `/data_model_generation`
    3.  `/agent_generation`
    4.  `/task_generation`
    5.  `/process_generation`
    6.  `/flow_generation`
    7.  `/service_generation`
*   **Environment**: A temporary sandbox directory that can be wiped after each training session.
*   **Goal**: Achieve a >95% success rate in generating syntactically correct and structurally compliant individual components.

---

## Phase 3: Orchestration & Integration Practice

**Objective**: To train the agent on orchestrating multiple component generations using master workflows.

*   **Action**: Execute the master bootstrapping workflows to build complex, interconnected structures like a full Command Office or a new subproject.
*   **Workflow Triggers (Integration Practice)**:
    *   `/bootstrap_command_office`
    *   `/bootstrap_estratix_project`
    *   `/productized_service_generation`
*   **Goal**: Ensure the agent can manage dependencies and correctly sequence the generation of a full component chain.

---

## Phase 4: Continuous Improvement & Refinement

**Objective**: To establish a feedback loop for identifying weaknesses and improving the agent's performance over time.

1.  **Analyze Failures**
    *   **Action**: When a generation task fails, the `MasterBuilderAgent`, assisted by a human supervisor or a `CTO_AXXX_RootCauseAnalysisAgent`, will analyze the error.
    *   **Output**: A report detailing the cause of failure (e.g., misinterpretation of a template, incorrect path, outdated knowledge).

2.  **Update Knowledge & Prompts**
    *   **Action**: Based on the failure analysis, update the agent's core prompts, system instructions, or knowledge base to correct the issue.
    *   **Goal**: Prevent the recurrence of the same error.

3.  **Regression Testing**
    *   **Action**: After an update, re-run the relevant training exercises from Phase 2 and 3 to ensure the fix has not introduced new issues.

**Confirmation**: The `MasterBuilderAgent`'s training cycle is complete. The agent is considered proficient and ready for operational tasks.
