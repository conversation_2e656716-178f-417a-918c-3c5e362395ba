import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import fp from 'fastify-plugin';
import { authenticateToken, requirePermission } from '../middleware/auth';
import { taskService } from '../services/taskService';
import { logger } from '../utils/logger';

// Request/Response schemas
const createTaskSchema = {
  body: {
    type: 'object',
    required: ['title', 'projectId'],
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', maxLength: 2000 },
      projectId: { type: 'string' },
      assigneeId: { type: 'string' },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] },
      status: { type: 'string', enum: ['todo', 'in_progress', 'review', 'done', 'blocked'] },
      dueDate: { type: 'string', format: 'date-time' },
      estimatedHours: { type: 'number', minimum: 0 },
      tags: { type: 'array', items: { type: 'string' } },
      dependencies: { type: 'array', items: { type: 'string' } },
      metadata: { type: 'object' }
    }
  }
};

const updateTaskSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    properties: {
      title: { type: 'string', minLength: 1, maxLength: 200 },
      description: { type: 'string', maxLength: 2000 },
      assigneeId: { type: 'string' },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] },
      status: { type: 'string', enum: ['todo', 'in_progress', 'review', 'done', 'blocked'] },
      dueDate: { type: 'string', format: 'date-time' },
      estimatedHours: { type: 'number', minimum: 0 },
      actualHours: { type: 'number', minimum: 0 },
      tags: { type: 'array', items: { type: 'string' } },
      dependencies: { type: 'array', items: { type: 'string' } },
      metadata: { type: 'object' }
    }
  }
};

const getTasksSchema = {
  querystring: {
    type: 'object',
    properties: {
      page: { type: 'integer', minimum: 1, default: 1 },
      limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
      projectId: { type: 'string' },
      assigneeId: { type: 'string' },
      status: { type: 'string', enum: ['todo', 'in_progress', 'review', 'done', 'blocked'] },
      priority: { type: 'string', enum: ['low', 'medium', 'high', 'urgent'] },
      search: { type: 'string' },
      sortBy: { type: 'string', enum: ['createdAt', 'updatedAt', 'dueDate', 'priority', 'title'] },
      sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' },
      tags: { type: 'string' }, // Comma-separated tags
      dueDateFrom: { type: 'string', format: 'date-time' },
      dueDateTo: { type: 'string', format: 'date-time' }
    }
  }
};

const taskParamsSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  }
};

const addCommentSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    required: ['content'],
    properties: {
      content: { type: 'string', minLength: 1, maxLength: 1000 },
      metadata: { type: 'object' }
    }
  }
};

const logTimeSchema = {
  params: {
    type: 'object',
    required: ['id'],
    properties: {
      id: { type: 'string' }
    }
  },
  body: {
    type: 'object',
    required: ['hours', 'description'],
    properties: {
      hours: { type: 'number', minimum: 0.1, maximum: 24 },
      description: { type: 'string', minLength: 1, maxLength: 500 },
      date: { type: 'string', format: 'date-time' },
      metadata: { type: 'object' }
    }
  }
};

interface AuthenticatedRequest extends FastifyRequest {
  user?: {
    id: string;
    email: string;
    role: string;
    permissions: string[];
  };
}

async function taskRoutes(fastify: FastifyInstance) {
  // Create task
  fastify.post('/tasks', {
    schema: createTaskSchema,
    preHandler: [authenticateToken, requirePermission('tasks:create')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const taskData = request.body as any;
      const userId = request.user!.id;
      
      const task = await taskService.createTask({
        ...taskData,
        createdBy: userId
      });
      
      logger.info(`Task created: ${task.id}`, {
        taskId: task.id,
        projectId: task.projectId,
        userId
      });
      
      reply.code(201).send({
        success: true,
        data: task
      });
    } catch (error) {
      logger.error('Error creating task:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to create task'
      });
    }
  });
  
  // Get tasks with filtering and pagination
  fastify.get('/tasks', {
    schema: getTasksSchema,
    preHandler: [authenticateToken, requirePermission('tasks:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const query = request.query as any;
      const userId = request.user!.id;
      const userRole = request.user!.role;
      
      // Apply user-based filtering for non-admin users
      if (userRole !== 'admin' && userRole !== 'manager') {
        query.assigneeId = userId;
      }
      
      const result = await taskService.getTasks(query);
      
      reply.send({
        success: true,
        data: result.tasks,
        pagination: {
          page: query.page || 1,
          limit: query.limit || 20,
          total: result.total,
          totalPages: Math.ceil(result.total / (query.limit || 20))
        }
      });
    } catch (error) {
      logger.error('Error fetching tasks:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch tasks'
      });
    }
  });
  
  // Get task by ID
  fastify.get('/tasks/:id', {
    schema: taskParamsSchema,
    preHandler: [authenticateToken, requirePermission('tasks:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user!.id;
      const userRole = request.user!.role;
      
      const task = await taskService.getTaskById(id);
      
      if (!task) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      // Check if user has access to this task
      if (userRole !== 'admin' && userRole !== 'manager' && 
          task.assigneeId !== userId && task.createdBy !== userId) {
        return reply.code(403).send({
          success: false,
          error: 'Access denied'
        });
      }
      
      reply.send({
        success: true,
        data: task
      });
    } catch (error) {
      logger.error('Error fetching task:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch task'
      });
    }
  });
  
  // Update task
  fastify.put('/tasks/:id', {
    schema: updateTaskSchema,
    preHandler: [authenticateToken, requirePermission('tasks:update')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = request.body as any;
      const userId = request.user!.id;
      
      const task = await taskService.updateTask(id, updateData, userId);
      
      if (!task) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      logger.info(`Task updated: ${id}`, {
        taskId: id,
        userId
      });
      
      reply.send({
        success: true,
        data: task
      });
    } catch (error) {
      logger.error('Error updating task:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to update task'
      });
    }
  });
  
  // Delete task
  fastify.delete('/tasks/:id', {
    schema: taskParamsSchema,
    preHandler: [authenticateToken, requirePermission('tasks:delete')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user!.id;
      
      const success = await taskService.deleteTask(id, userId);
      
      if (!success) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      logger.info(`Task deleted: ${id}`, {
        taskId: id,
        userId
      });
      
      reply.send({
        success: true,
        message: 'Task deleted successfully'
      });
    } catch (error) {
      logger.error('Error deleting task:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to delete task'
      });
    }
  });
  
  // Add comment to task
  fastify.post('/tasks/:id/comments', {
    schema: addCommentSchema,
    preHandler: [authenticateToken, requirePermission('tasks:comment')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const commentData = request.body as any;
      const userId = request.user!.id;
      
      const comment = await taskService.addComment(id, {
        ...commentData,
        authorId: userId
      });
      
      if (!comment) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      logger.info(`Comment added to task: ${id}`, {
        taskId: id,
        commentId: comment.id,
        userId
      });
      
      reply.code(201).send({
        success: true,
        data: comment
      });
    } catch (error) {
      logger.error('Error adding comment:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to add comment'
      });
    }
  });
  
  // Log time to task
  fastify.post('/tasks/:id/time-logs', {
    schema: logTimeSchema,
    preHandler: [authenticateToken, requirePermission('tasks:log_time')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const timeLogData = request.body as any;
      const userId = request.user!.id;
      
      const timeLog = await taskService.logTime(id, {
        ...timeLogData,
        userId
      });
      
      if (!timeLog) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      logger.info(`Time logged for task: ${id}`, {
        taskId: id,
        timeLogId: timeLog.id,
        hours: timeLogData.hours,
        userId
      });
      
      reply.code(201).send({
        success: true,
        data: timeLog
      });
    } catch (error) {
      logger.error('Error logging time:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to log time'
      });
    }
  });
  
  // Get task analytics
  fastify.get('/tasks/analytics', {
    preHandler: [authenticateToken, requirePermission('tasks:analytics')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const query = request.query as any;
      const userId = request.user!.id;
      const userRole = request.user!.role;
      
      // Apply user-based filtering for non-admin users
      if (userRole !== 'admin' && userRole !== 'manager') {
        query.assigneeId = userId;
      }
      
      const analytics = await taskService.getTaskAnalytics(query);
      
      reply.send({
        success: true,
        data: analytics
      });
    } catch (error) {
      logger.error('Error fetching task analytics:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch task analytics'
      });
    }
  });
  
  // Get task dependencies
  fastify.get('/tasks/:id/dependencies', {
    schema: taskParamsSchema,
    preHandler: [authenticateToken, requirePermission('tasks:read')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      
      const dependencies = await taskService.getTaskDependencies(id);
      
      reply.send({
        success: true,
        data: dependencies
      });
    } catch (error) {
      logger.error('Error fetching task dependencies:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to fetch task dependencies'
      });
    }
  });
  
  // Update task status
  fastify.patch('/tasks/:id/status', {
    params: {
      type: 'object',
      required: ['id'],
      properties: {
        id: { type: 'string' }
      }
    },
    body: {
      type: 'object',
      required: ['status'],
      properties: {
        status: { type: 'string', enum: ['todo', 'in_progress', 'review', 'done', 'blocked'] },
        comment: { type: 'string', maxLength: 500 }
      }
    },
    preHandler: [authenticateToken, requirePermission('tasks:update')]
  }, async (request: AuthenticatedRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const { status, comment } = request.body as any;
      const userId = request.user!.id;
      
      const task = await taskService.updateTaskStatus(id, status, userId, comment);
      
      if (!task) {
        return reply.code(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      logger.info(`Task status updated: ${id}`, {
        taskId: id,
        newStatus: status,
        userId
      });
      
      reply.send({
        success: true,
        data: task
      });
    } catch (error) {
      logger.error('Error updating task status:', error);
      reply.code(500).send({
        success: false,
        error: 'Failed to update task status'
      });
    }
  });
}

export default fp(taskRoutes, {
  name: 'task-routes'
});