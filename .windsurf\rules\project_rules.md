---
trigger: always_on
---

# ESTRATIX Project Global Rules

## 1. Introduction & Philosophy

This document outlines the global rules, architectural principles, and operational standards for the ESTRATIX project. These rules are designed to be machine-readable and serve as a foundational prompt for all agentic activities, ensuring consistency, quality, and alignment with the project's strategic objectives.

**Core Philosophy**: ESTRATIX is an agentic development framework that models business value chains as a digital twin. It uses a process-centric, hexagonal architecture to create a clear separation of concerns, enabling robust, scalable, and maintainable solutions. **Flows orchestrate Processes, and Processes orchestrate Tasks, Agents, and Tools.**

## 2. Architectural Foundation

### 2.1. Hexagonal Architecture (Ports and Adapters)

- **Domain Layer**: Contains the core business logic, entities, and value objects. It is framework-agnostic and represents the "physical twin" of the documented components. All domain code resides in `src/domain/`.
- **Application Layer**: Contains the application-specific use cases and services. This layer orchestrates the domain logic to fulfill business requirements.
- **Infrastructure Layer**: Contains all external concerns, such as databases, UIs, and third-party APIs. It includes framework-specific implementations. All framework-specific code resides in `src/infrastructure/frameworks/[framework_name]`.
- **Ports**: Define the interfaces for interacting with the application. They belong to the application layer.
- **Adapters**: Provide the concrete implementations of the ports, connecting the application to external systems.

### 2.2. Process-Centric Orchestration

The core of ESTRATIX orchestration follows a strict hierarchy:

1. **Flows (`F###`)**: High-level business capabilities. A Flow's sole responsibility is to orchestrate one or more Processes in a specific sequence to achieve a business goal. Flows DO NOT contain implementation logic.
2. **Processes (`P###`)**: Cohesive, reusable units of work that encapsulate a specific business or technical process. Processes are the primary unit of orchestration, responsible for sourcing and coordinating Agents, Tasks, and Tools.
3. **Tasks (`T###`)**: The smallest unit of executable work assigned to an Agent. A Task has a clear description, expected output, and leverages specific Tools.
4. **Agents (`A###`)**: Specialized actors responsible for executing Tasks. Agents are defined by their role, goal, backstory, and capabilities (tools).
5. **Tools (`T_***_###`)**: Reusable functions or components that Agents use to perform Tasks. Tools must be framework-agnostic and reside in `src/domain/tools/`.

This model ensures that logic is modular, reusable, and easy to trace, forming a clear "chain of command" from high-level business flow to low-level tool execution.

## 3. Development Rules (SOLID, DRY, TDD)

1. **SOLID Principles**: All code must adhere to SOLID principles.
2. **DRY (Don't Repeat Yourself)**: Maintain clean, non-repetitive code. Centralize reusable logic in the `src/domain` layer, especially for tools and data models.
3. **Test-Driven Development (TDD)**: All major components (especially Tools and Processes) must be developed with corresponding tests.
4. **Type Hints**: All Python code must use type hints for clarity and static analysis.
5. **Atomic Components**: Create focused components with clear, single responsibilities.
6. **Input Validation & Error Handling**: Implement robust validation and error handling at all layers.
7. **Security**: Follow security best practices. Never hardcode credentials; use a secrets management system.
8. **Documentation**: All code must be thoroughly documented with docstrings. All architectural decisions must be recorded in the relevant markdown definition files.

## 4. Rules Management & Orchestration

### 4.1. Rule Activation Modes

ESTRATIX rules operate in three modes to ensure continuous compliance and adaptability:

1. **Always-On (Default)**: These global rules are prepended to every prompt and are active for all agentic activities. They form the baseline operational context.
2. **Workflow-Triggered**: Specific rule files can be activated by generative workflows (e.g., [flow_generation.md](cci:7://file:///c:/Users/<USER>/Downloads/PROJECTS/ESTRATIX/DEVELOPMENT/projectManagement/estratix_v3/.windsurf/workflows/1_component_lifecycle/2_generate/flow_generation.md:0:0-0:0) can activate rules related to code generation standards). This provides context-specific guidance.
3. **Agent-Triggered**: Agents can be given the capability to dynamically load specific rules based on the task at hand, allowing for specialized, fine-grained control.

### 4.2. Rules for Rules (Self-Observability)

The management of this rule system is itself an agentic process, managed by **DocOps Agents** under the CIO.

- **Workflow**: The master workflow for this is [value_chain_observability_and_improvement.md](cci:7://file:///c:/Users/<USER>/Downloads/PROJECTS/ESTRATIX/DEVELOPMENT/projectManagement/estratix_v3/.windsurf/workflows/0_master_orchestration/value_chain_observability_and_improvement.md:0:0-0:0).
- **Process**: The core process is `CIO_P00X_RulesMaintenance`.
- **Task**: Key tasks include linting, validating, and updating rule files.
- **Goal**: To ensure the rule system remains consistent, up-to-date, and effective, creating a self-observing and self-governing system.

## 5. Multi-Agent Orchestration Patterns

ESTRATIX will progressively implement advanced multi-agent patterns:

- **MCP (Multi-Crew/Company Protocol)**: Defines standards for interaction between different Command Offices or major crews.
- **A2A (Agent-to-Agent Protocol)**: Defines direct communication and collaboration patterns between individual agents.
- **ACP (Agent-to-Component Protocol)**: Defines how agents interact with non-agentic components like APIs, databases, and legacy systems.

These patterns will be defined as ESTRATIX Standards (`docs/standards/`) and implemented within the orchestration logic of Processes and Flows.

## 6. Digital Twin Implementation

The entire `docs` directory serves as the **Digital Twin** of the ESTRATIX system. The `src` directory is the **Physical Twin** (the implementation).

- There must be a 1:1 correspondence between a conceptual component in `docs` (e.g., a tool definition) and its implementation in `src`.
- The [value_chain_observability_and_improvement.md](cci:7://file:///c:/Users/<USER>/Downloads/PROJECTS/ESTRATIX/DEVELOPMENT/projectManagement/estratix_v3/.windsurf/workflows/0_master_orchestration/value_chain_observability_and_improvement.md:0:0-0:0) workflow is responsible for orchestrating agents that continuously scan both twins to ensure they remain synchronized. Any drift must be flagged and corrected.
