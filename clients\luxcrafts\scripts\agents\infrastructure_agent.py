#!/usr/bin/env python3
"""
Infrastructure Deployment Agent

This agent handles VPS deployment, Docker container management, load balancer
configuration, SSL setup, and infrastructure monitoring.
"""

import argparse
import json
import logging
import os
import subprocess
import sys
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime
import paramiko
import docker
from fabric import Connection
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class DeploymentConfig:
    target: str
    host: str
    user: str
    image: str
    timeout: int
    ssh_key_path: Optional[str] = None
    port: int = 22
    docker_registry: str = "ghcr.io"
    app_port: int = 3000
    nginx_port: int = 80
    ssl_enabled: bool = True
    domain: Optional[str] = None
    environment: Dict[str, str] = None

class InfrastructureAgent:
    """AI-powered infrastructure deployment agent"""
    
    def __init__(self, config: DeploymentConfig):
        self.config = config
        self.connection: Optional[Connection] = None
        self.docker_client: Optional[docker.DockerClient] = None
        self.deployment_log: List[Dict[str, Any]] = []
        
    def log_action(self, action: str, status: str, details: str = "", duration: float = 0):
        """Log deployment actions for audit and debugging"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "action": action,
            "status": status,
            "details": details,
            "duration": duration
        }
        self.deployment_log.append(log_entry)
        logger.info(f"{action}: {status} - {details}")
    
    def connect_to_server(self) -> bool:
        """Establish SSH connection to the target server"""
        try:
            start_time = time.time()
            
            # Setup SSH connection
            connect_kwargs = {}
            if self.config.ssh_key_path:
                connect_kwargs['key_filename'] = self.config.ssh_key_path
            
            self.connection = Connection(
                host=self.config.host,
                user=self.config.user,
                port=self.config.port,
                connect_kwargs=connect_kwargs
            )
            
            # Test connection
            result = self.connection.run('echo "Connection test"', hide=True)
            
            duration = time.time() - start_time
            self.log_action(
                "SSH Connection", 
                "SUCCESS", 
                f"Connected to {self.config.host} as {self.config.user}",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "SSH Connection", 
                "FAILED", 
                f"Failed to connect: {str(e)}",
                duration
            )
            return False
    
    def setup_docker_environment(self) -> bool:
        """Setup Docker and Docker Compose on the server"""
        try:
            start_time = time.time()
            
            # Check if Docker is installed
            result = self.connection.run('docker --version', warn=True, hide=True)
            
            if result.return_code != 0:
                logger.info("Installing Docker...")
                
                # Install Docker
                commands = [
                    'sudo apt-get update',
                    'sudo apt-get install -y apt-transport-https ca-certificates curl gnupg lsb-release',
                    'curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg',
                    'echo "deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null',
                    'sudo apt-get update',
                    'sudo apt-get install -y docker-ce docker-ce-cli containerd.io',
                    'sudo systemctl start docker',
                    'sudo systemctl enable docker',
                    f'sudo usermod -aG docker {self.config.user}'
                ]
                
                for cmd in commands:
                    self.connection.run(cmd)
            
            # Install Docker Compose
            compose_result = self.connection.run('docker-compose --version', warn=True, hide=True)
            if compose_result.return_code != 0:
                logger.info("Installing Docker Compose...")
                self.connection.run(
                    'sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose'
                )
                self.connection.run('sudo chmod +x /usr/local/bin/docker-compose')
            
            # Verify installation
            self.connection.run('docker --version')
            self.connection.run('docker-compose --version')
            
            duration = time.time() - start_time
            self.log_action(
                "Docker Setup", 
                "SUCCESS", 
                "Docker and Docker Compose installed and configured",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "Docker Setup", 
                "FAILED", 
                f"Failed to setup Docker: {str(e)}",
                duration
            )
            return False
    
    def setup_nginx_load_balancer(self) -> bool:
        """Setup Nginx as a reverse proxy and load balancer"""
        try:
            start_time = time.time()
            
            # Install Nginx
            self.connection.run('sudo apt-get update')
            self.connection.run('sudo apt-get install -y nginx')
            
            # Create Nginx configuration
            nginx_config = f"""
upstream luxcrafts_backend {{
    server localhost:{self.config.app_port};
    # Add more servers for load balancing
    # server localhost:3001;
    # server localhost:3002;
}}

server {{
    listen 80;
    server_name {self.config.domain or '_'};
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Static files
    location /static/ {{
        alias /var/www/luxcrafts/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }}
    
    # Health check endpoint
    location /health {{
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }}
    
    # Main application
    location / {{
        proxy_pass http://luxcrafts_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }}
    
    # WebSocket support
    location /ws {{
        proxy_pass http://luxcrafts_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""
            
            # Write Nginx configuration
            self.connection.run('sudo mkdir -p /etc/nginx/sites-available')
            self.connection.run(f'echo "{nginx_config}" | sudo tee /etc/nginx/sites-available/luxcrafts')
            
            # Enable site
            self.connection.run('sudo ln -sf /etc/nginx/sites-available/luxcrafts /etc/nginx/sites-enabled/')
            self.connection.run('sudo rm -f /etc/nginx/sites-enabled/default')
            
            # Test and reload Nginx
            self.connection.run('sudo nginx -t')
            self.connection.run('sudo systemctl restart nginx')
            self.connection.run('sudo systemctl enable nginx')
            
            duration = time.time() - start_time
            self.log_action(
                "Nginx Setup", 
                "SUCCESS", 
                "Nginx configured as reverse proxy",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "Nginx Setup", 
                "FAILED", 
                f"Failed to setup Nginx: {str(e)}",
                duration
            )
            return False
    
    def setup_ssl_certificate(self) -> bool:
        """Setup SSL certificate using Let's Encrypt"""
        if not self.config.ssl_enabled or not self.config.domain:
            self.log_action("SSL Setup", "SKIPPED", "SSL not enabled or domain not specified")
            return True
            
        try:
            start_time = time.time()
            
            # Install Certbot
            self.connection.run('sudo apt-get update')
            self.connection.run('sudo apt-get install -y certbot python3-certbot-nginx')
            
            # Obtain SSL certificate
            self.connection.run(
                f'sudo certbot --nginx -d {self.config.domain} --non-interactive --agree-tos --email admin@{self.config.domain}'
            )
            
            # Setup auto-renewal
            self.connection.run('sudo systemctl enable certbot.timer')
            self.connection.run('sudo systemctl start certbot.timer')
            
            duration = time.time() - start_time
            self.log_action(
                "SSL Setup", 
                "SUCCESS", 
                f"SSL certificate obtained for {self.config.domain}",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "SSL Setup", 
                "FAILED", 
                f"Failed to setup SSL: {str(e)}",
                duration
            )
            return False
    
    def deploy_application(self) -> bool:
        """Deploy the application using Docker Compose"""
        try:
            start_time = time.time()
            
            # Create deployment directory
            self.connection.run('mkdir -p /opt/luxcrafts')
            
            # Create environment file
            env_content = "\n".join([f"{k}={v}" for k, v in (self.config.environment or {}).items()])
            if env_content:
                self.connection.run(f'echo "{env_content}" > /opt/luxcrafts/.env')
            
            # Create Docker Compose file
            compose_content = f"""
version: '3.8'

services:
  luxcrafts-app:
    image: {self.config.image}
    container_name: luxcrafts-app
    restart: unless-stopped
    ports:
      - "{self.config.app_port}:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - luxcrafts-network
    volumes:
      - app-data:/app/data
      - /var/log/luxcrafts:/app/logs

  redis:
    image: redis:7-alpine
    container_name: luxcrafts-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:15-alpine
    container_name: luxcrafts-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: luxcrafts
      POSTGRES_USER: luxcrafts
      POSTGRES_PASSWORD: ${{POSTGRES_PASSWORD:-luxcrafts123}}
    ports:
      - "5432:5432"
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    networks:
      - luxcrafts-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U luxcrafts"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  app-data:
  redis-data:
  postgres-data:

networks:
  luxcrafts-network:
    driver: bridge
"""
            
            self.connection.run(f'echo "{compose_content}" > /opt/luxcrafts/docker-compose.yml')
            
            # Login to Docker registry if needed
            if self.config.docker_registry != "docker.io":
                registry_token = os.getenv('GITHUB_TOKEN')
                if registry_token:
                    self.connection.run(f'echo {registry_token} | docker login {self.config.docker_registry} -u {os.getenv("GITHUB_ACTOR", "token")} --password-stdin')
            
            # Pull and deploy
            with self.connection.cd('/opt/luxcrafts'):
                self.connection.run('docker-compose pull')
                self.connection.run('docker-compose up -d')
                
                # Wait for services to be healthy
                logger.info("Waiting for services to be healthy...")
                time.sleep(30)
                
                # Check service status
                result = self.connection.run('docker-compose ps', hide=True)
                logger.info(f"Service status:\n{result.stdout}")
            
            duration = time.time() - start_time
            self.log_action(
                "Application Deployment", 
                "SUCCESS", 
                "Application deployed and running",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "Application Deployment", 
                "FAILED", 
                f"Failed to deploy application: {str(e)}",
                duration
            )
            return False
    
    def setup_monitoring(self) -> bool:
        """Setup basic monitoring and logging"""
        try:
            start_time = time.time()
            
            # Create log directories
            self.connection.run('sudo mkdir -p /var/log/luxcrafts')
            self.connection.run(f'sudo chown {self.config.user}:{self.config.user} /var/log/luxcrafts')
            
            # Setup log rotation
            logrotate_config = """
/var/log/luxcrafts/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 luxcrafts luxcrafts
    postrotate
        docker-compose -f /opt/luxcrafts/docker-compose.yml restart luxcrafts-app
    endscript
}
"""
            
            self.connection.run(f'echo "{logrotate_config}" | sudo tee /etc/logrotate.d/luxcrafts')
            
            # Setup basic monitoring script
            monitoring_script = f"""
#!/bin/bash

# Health check script for Luxcrafts
LOG_FILE="/var/log/luxcrafts/health-check.log"
APP_URL="http://localhost:{self.config.app_port}/health"

echo "$(date): Starting health check" >> $LOG_FILE

# Check application health
if curl -f $APP_URL > /dev/null 2>&1; then
    echo "$(date): Application is healthy" >> $LOG_FILE
else
    echo "$(date): Application health check failed" >> $LOG_FILE
    # Restart application if unhealthy
    cd /opt/luxcrafts && docker-compose restart luxcrafts-app
fi

# Check disk space
DISK_USAGE=$(df / | awk 'NR==2 {{print $5}}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): WARNING: Disk usage is $DISK_USAGE%" >> $LOG_FILE
fi

# Check memory usage
MEM_USAGE=$(free | awk 'NR==2{{printf "%.0f", $3*100/$2 }}')
if [ $MEM_USAGE -gt 80 ]; then
    echo "$(date): WARNING: Memory usage is $MEM_USAGE%" >> $LOG_FILE
fi

echo "$(date): Health check completed" >> $LOG_FILE
"""
            
            self.connection.run(f'echo "{monitoring_script}" > /opt/luxcrafts/health-check.sh')
            self.connection.run('chmod +x /opt/luxcrafts/health-check.sh')
            
            # Setup cron job for health checks
            self.connection.run('(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/luxcrafts/health-check.sh") | crontab -')
            
            duration = time.time() - start_time
            self.log_action(
                "Monitoring Setup", 
                "SUCCESS", 
                "Basic monitoring and logging configured",
                duration
            )
            return True
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "Monitoring Setup", 
                "FAILED", 
                f"Failed to setup monitoring: {str(e)}",
                duration
            )
            return False
    
    def verify_deployment(self) -> bool:
        """Verify that the deployment is working correctly"""
        try:
            start_time = time.time()
            
            # Check if containers are running
            with self.connection.cd('/opt/luxcrafts'):
                result = self.connection.run('docker-compose ps --services --filter "status=running"', hide=True)
                running_services = result.stdout.strip().split('\n')
                logger.info(f"Running services: {running_services}")
            
            # Test application health endpoint
            health_result = self.connection.run(f'curl -f http://localhost:{self.config.app_port}/health', warn=True, hide=True)
            
            if health_result.return_code == 0:
                logger.info("Application health check passed")
            else:
                logger.warning("Application health check failed")
            
            # Test Nginx
            nginx_result = self.connection.run('curl -f http://localhost/health', warn=True, hide=True)
            
            if nginx_result.return_code == 0:
                logger.info("Nginx health check passed")
            else:
                logger.warning("Nginx health check failed")
            
            # Check SSL if enabled
            if self.config.ssl_enabled and self.config.domain:
                ssl_result = self.connection.run(f'curl -f https://{self.config.domain}/health', warn=True, hide=True)
                if ssl_result.return_code == 0:
                    logger.info("SSL health check passed")
                else:
                    logger.warning("SSL health check failed")
            
            duration = time.time() - start_time
            
            # Determine overall success
            success = health_result.return_code == 0 and nginx_result.return_code == 0
            
            self.log_action(
                "Deployment Verification", 
                "SUCCESS" if success else "PARTIAL", 
                f"Health checks completed - App: {health_result.return_code == 0}, Nginx: {nginx_result.return_code == 0}",
                duration
            )
            return success
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_action(
                "Deployment Verification", 
                "FAILED", 
                f"Failed to verify deployment: {str(e)}",
                duration
            )
            return False
    
    def generate_deployment_report(self) -> Dict[str, Any]:
        """Generate a comprehensive deployment report"""
        total_duration = sum(log['duration'] for log in self.deployment_log)
        successful_actions = len([log for log in self.deployment_log if log['status'] == 'SUCCESS'])
        total_actions = len(self.deployment_log)
        
        return {
            "deployment_config": {
                "target": self.config.target,
                "host": self.config.host,
                "image": self.config.image,
                "ssl_enabled": self.config.ssl_enabled,
                "domain": self.config.domain
            },
            "execution_summary": {
                "total_duration": total_duration,
                "total_actions": total_actions,
                "successful_actions": successful_actions,
                "success_rate": (successful_actions / total_actions * 100) if total_actions > 0 else 0,
                "timestamp": datetime.now().isoformat()
            },
            "deployment_log": self.deployment_log,
            "status": "SUCCESS" if successful_actions == total_actions else "PARTIAL" if successful_actions > 0 else "FAILED"
        }
    
    def deploy(self) -> Dict[str, Any]:
        """Execute the complete deployment process"""
        logger.info(f"Starting infrastructure deployment to {self.config.host}")
        
        deployment_steps = [
            ("connect_to_server", "Connecting to server"),
            ("setup_docker_environment", "Setting up Docker environment"),
            ("setup_nginx_load_balancer", "Configuring Nginx load balancer"),
            ("setup_ssl_certificate", "Setting up SSL certificate"),
            ("deploy_application", "Deploying application"),
            ("setup_monitoring", "Setting up monitoring"),
            ("verify_deployment", "Verifying deployment")
        ]
        
        for step_method, step_description in deployment_steps:
            logger.info(f"Executing: {step_description}")
            method = getattr(self, step_method)
            success = method()
            
            if not success and step_method in ['connect_to_server', 'deploy_application']:
                logger.error(f"Critical step failed: {step_description}")
                break
        
        # Generate and return deployment report
        report = self.generate_deployment_report()
        logger.info(f"Deployment completed with status: {report['status']}")
        
        return report

def main():
    parser = argparse.ArgumentParser(description="Infrastructure Deployment Agent")
    parser.add_argument("--target", required=True, choices=["staging", "production"],
                       help="Deployment target environment")
    parser.add_argument("--host", required=True, help="Target host IP or domain")
    parser.add_argument("--user", required=True, help="SSH user")
    parser.add_argument("--image", required=True, help="Docker image to deploy")
    parser.add_argument("--timeout", type=int, default=600, help="Deployment timeout")
    parser.add_argument("--ssh-key", help="Path to SSH private key")
    parser.add_argument("--domain", help="Domain name for SSL certificate")
    parser.add_argument("--app-port", type=int, default=3000, help="Application port")
    parser.add_argument("--output", default="infrastructure-report.json",
                       help="Output file for deployment report")
    
    args = parser.parse_args()
    
    # Create deployment configuration
    config = DeploymentConfig(
        target=args.target,
        host=args.host,
        user=args.user,
        image=args.image,
        timeout=args.timeout,
        ssh_key_path=args.ssh_key,
        domain=args.domain,
        app_port=args.app_port,
        ssl_enabled=bool(args.domain),
        environment={
            "NODE_ENV": "production",
            "DATABASE_URL": os.getenv("DATABASE_URL", ""),
            "REDIS_URL": os.getenv("REDIS_URL", ""),
            "VITE_WALLETCONNECT_PROJECT_ID": os.getenv("VITE_WALLETCONNECT_PROJECT_ID", ""),
            "VITE_ALCHEMY_API_KEY": os.getenv("VITE_ALCHEMY_API_KEY", ""),
            "VITE_INFURA_API_KEY": os.getenv("VITE_INFURA_API_KEY", "")
        }
    )
    
    # Create and run infrastructure agent
    agent = InfrastructureAgent(config)
    report = agent.deploy()
    
    # Save deployment report
    with open(args.output, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Deployment report saved to {args.output}")
    
    # Exit with appropriate code
    if report['status'] == 'SUCCESS':
        sys.exit(0)
    elif report['status'] == 'PARTIAL':
        sys.exit(1)
    else:
        sys.exit(2)

if __name__ == "__main__":
    main()