# Sorteo Estelar Environment Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# APPLICATION SETTINGS
# ===========================================
NODE_ENV=development
APP_NAME=sorteo-estelar
APP_VERSION=1.0.0
APP_URL=http://localhost:3000
API_URL=http://localhost:3001
WS_URL=ws://localhost:3001

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
DATABASE_URL=postgresql://sorteo_user:sorteo_pass@localhost:5432/sorteo_estelar
DB_HOST=localhost
DB_PORT=5432
DB_NAME=sorteo_estelar
DB_USER=sorteo_user
DB_PASSWORD=sorteo_pass
DB_SSL=false
DB_POOL_MIN=2
DB_POOL_MAX=10

# ===========================================
# REDIS CONFIGURATION
# ===========================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# ===========================================
# JWT & AUTHENTICATION
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
BCRYPT_ROUNDS=12

# ===========================================
# BLOCKCHAIN & WEB3 CONFIGURATION
# ===========================================
# Ethereum/Polygon
INFURA_PROJECT_ID=your-infura-project-id
INFURA_PROJECT_SECRET=your-infura-project-secret
CHAIN_ID=1
ETH_NETWORK=mainnet
ETH_RPC_URL=https://mainnet.infura.io/v3/your-project-id
POLYGON_RPC_URL=https://polygon-mainnet.infura.io/v3/your-project-id

# WalletConnect
WALLETCONNECT_PROJECT_ID=your-walletconnect-project-id

# XRPL Configuration
XRPL_NETWORK=testnet
XRPL_SERVER=wss://s.altnet.rippletest.net:51233
XRPL_WALLET_SEED=your-xrpl-wallet-seed

# Smart Contract Addresses
NFT_CONTRACT_ADDRESS=0x...
TOKEN_CONTRACT_ADDRESS=0x...
LOTTERY_CONTRACT_ADDRESS=0x...
STAKING_CONTRACT_ADDRESS=0x...

# ===========================================
# PAYMENT PROCESSORS
# ===========================================
# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# PayPal
PAYPAL_CLIENT_ID=your-paypal-client-id
PAYPAL_CLIENT_SECRET=your-paypal-client-secret
PAYPAL_MODE=sandbox

# Binance Pay
BINANCE_API_KEY=your-binance-api-key
BINANCE_SECRET_KEY=your-binance-secret-key
BINANCE_SANDBOX=true

# ===========================================
# EMAIL CONFIGURATION
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME=Sorteo Estelar

# SendGrid (Alternative)
SENDGRID_API_KEY=your-sendgrid-api-key

# ===========================================
# SOCIAL MEDIA & MARKETING
# ===========================================
# Twitter/X API
TWITTER_API_KEY=your-twitter-api-key
TWITTER_API_SECRET=your-twitter-api-secret
TWITTER_ACCESS_TOKEN=your-twitter-access-token
TWITTER_ACCESS_TOKEN_SECRET=your-twitter-access-token-secret
TWITTER_BEARER_TOKEN=your-twitter-bearer-token

# Instagram
INSTAGRAM_ACCESS_TOKEN=your-instagram-access-token
INSTAGRAM_BUSINESS_ACCOUNT_ID=your-instagram-business-account-id

# Facebook
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
FACEBOOK_ACCESS_TOKEN=your-facebook-access-token

# ===========================================
# FILE STORAGE
# ===========================================
# AWS S3
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=sorteo-estelar-assets

# Cloudinary (Alternative)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# ===========================================
# MONITORING & ANALYTICS
# ===========================================
# Sentry
SENTRY_DSN=your-sentry-dsn
SENTRY_ENVIRONMENT=development

# Google Analytics
GA_TRACKING_ID=G-XXXXXXXXXX

# Mixpanel
MIXPANEL_TOKEN=your-mixpanel-token

# ===========================================
# EXTERNAL APIS
# ===========================================
# CoinGecko
COINGECKO_API_KEY=your-coingecko-api-key

# CoinMarketCap
COINMARKETCAP_API_KEY=your-coinmarketcap-api-key

# OpenAI (for RAG/AI features)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4

# ===========================================
# SECURITY & RATE LIMITING
# ===========================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CORS_ORIGIN=http://localhost:3000
CSRF_SECRET=your-csrf-secret

# ===========================================
# DEPLOYMENT & INFRASTRUCTURE
# ===========================================
# VPS Configuration
VPS_HOST=**************
VPS_PORT=2222
VPS_USER=admin
DOMAIN=www.sorteoestelar.com

# Docker Registry
DOCKER_REGISTRY=registry.digitalocean.com/sorteo-estelar
DOCKER_USERNAME=your-registry-username
DOCKER_PASSWORD=your-registry-password

# Dokploy
DOKPLOY_API_URL=http://localhost:3000/api
DOKPLOY_API_KEY=your-dokploy-api-key

# ===========================================
# LOGGING & DEBUGGING
# ===========================================
LOG_LEVEL=info
LOG_FORMAT=combined
DEBUG=sorteo:*

# ===========================================
# FEATURE FLAGS
# ===========================================
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_2FA=false
ENABLE_SOCIAL_LOGIN=true
ENABLE_CRYPTO_PAYMENTS=true
ENABLE_NFT_MARKETPLACE=true
ENABLE_STAKING=true
ENABLE_GOVERNANCE=true
ENABLE_DEFI_FEATURES=true

# ===========================================
# DEVELOPMENT TOOLS
# ===========================================
# Hot reload
WATCHPACK_POLLING=true
CHOKIDAR_USEPOLLING=true

# Testing
TEST_DATABASE_URL=postgresql://test_user:test_pass@localhost:5432/sorteo_estelar_test
TEST_REDIS_URL=redis://localhost:6379/1