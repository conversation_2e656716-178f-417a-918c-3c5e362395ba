#cloud-config
# Cloud-init configuration for Luxcrafts web server
# Automated server setup with security hardening

users:
  - name: luxcrafts
    groups: sudo
    shell: /bin/bash
    sudo: ['ALL=(ALL) NOPASSWD:ALL']
    ssh_authorized_keys:
      - ${ssh_public_key}
    lock_passwd: true

# Disable root login
disable_root: true

# Package updates
package_update: true
package_upgrade: true
package_reboot_if_required: true

# Install essential packages
packages:
  - curl
  - wget
  - git
  - htop
  - vim
  - ufw
  - fail2ban
  - unattended-upgrades
  - apt-transport-https
  - ca-certificates
  - gnupg
  - lsb-release
  - software-properties-common
  - nginx
  - certbot
  - python3-certbot-nginx
  - nodejs
  - npm
  - docker.io
  - docker-compose
  - rkhunter
  - chkrootkit
  - lynis
  - aide
  - clamav
  - clamav-daemon
  - auditd
  - psad
  - logwatch
  - mailutils
  - postfix
  - rsyslog
  - ntp
  - chrony

# Configure automatic security updates
write_files:
  # Unattended upgrades configuration
  - path: /etc/apt/apt.conf.d/50unattended-upgrades
    content: |
      Unattended-Upgrade::Allowed-Origins {
          "${distro_id}:${distro_codename}";
          "${distro_id}:${distro_codename}-security";
          "${distro_id}ESMApps:${distro_codename}-apps-security";
          "${distro_id}ESM:${distro_codename}-infra-security";
      };
      Unattended-Upgrade::Package-Blacklist {
      };
      Unattended-Upgrade::DevRelease "false";
      Unattended-Upgrade::Remove-Unused-Kernel-Packages "true";
      Unattended-Upgrade::Remove-New-Unused-Dependencies "true";
      Unattended-Upgrade::Remove-Unused-Dependencies "true";
      Unattended-Upgrade::Automatic-Reboot "false";
      Unattended-Upgrade::Automatic-Reboot-WithUsers "false";
      Unattended-Upgrade::Automatic-Reboot-Time "02:00";
      Unattended-Upgrade::SyslogEnable "true";
      Unattended-Upgrade::SyslogFacility "daemon";
      Unattended-Upgrade::OnlyOnACPower "false";
      Unattended-Upgrade::Skip-Updates-On-Metered-Connections "true";
      Unattended-Upgrade::Verbose "false";
      Unattended-Upgrade::Debug "false";
      Unattended-Upgrade::Mail "${admin_email}";
      Unattended-Upgrade::MailReport "on-change";
    owner: root:root
    permissions: '0644'

  # Auto-upgrades configuration
  - path: /etc/apt/apt.conf.d/20auto-upgrades
    content: |
      APT::Periodic::Update-Package-Lists "1";
      APT::Periodic::Download-Upgradeable-Packages "1";
      APT::Periodic::AutocleanInterval "7";
      APT::Periodic::Unattended-Upgrade "1";
    owner: root:root
    permissions: '0644'

  # SSH hardening configuration
  - path: /etc/ssh/sshd_config
    content: |
      Port 22
      Protocol 2
      HostKey /etc/ssh/ssh_host_rsa_key
      HostKey /etc/ssh/ssh_host_ecdsa_key
      HostKey /etc/ssh/ssh_host_ed25519_key
      UsePrivilegeSeparation yes
      KeyRegenerationInterval 3600
      ServerKeyBits 1024
      SyslogFacility AUTH
      LogLevel VERBOSE
      LoginGraceTime 120
      PermitRootLogin no
      StrictModes yes
      RSAAuthentication yes
      PubkeyAuthentication yes
      IgnoreRhosts yes
      RhostsRSAAuthentication no
      HostbasedAuthentication no
      PermitEmptyPasswords no
      ChallengeResponseAuthentication no
      PasswordAuthentication no
      X11Forwarding no
      X11DisplayOffset 10
      PrintMotd no
      PrintLastLog yes
      TCPKeepAlive yes
      MaxAuthTries 3
      MaxSessions 2
      ClientAliveInterval 300
      ClientAliveCountMax 2
      UsePAM yes
      AllowUsers luxcrafts
      DenyUsers root
      Banner /etc/ssh/banner
    owner: root:root
    permissions: '0600'

  # SSH banner
  - path: /etc/ssh/banner
    content: |
      **************************************************************************
      *                                                                        *
      *                        LUXCRAFTS SECURE SERVER                        *
      *                                                                        *
      *  This system is for authorized users only. All activities on this     *
      *  system are monitored and recorded. Unauthorized access is prohibited *
      *  and will be prosecuted to the full extent of the law.                *
      *                                                                        *
      **************************************************************************
    owner: root:root
    permissions: '0644'

  # Fail2ban configuration
  - path: /etc/fail2ban/jail.local
    content: |
      [DEFAULT]
      bantime = 3600
      findtime = 600
      maxretry = 3
      ignoreip = 127.0.0.1/8 ::1
      backend = systemd
      
      [sshd]
      enabled = true
      port = ssh
      filter = sshd
      logpath = /var/log/auth.log
      maxretry = 3
      bantime = 3600
      
      [nginx-http-auth]
      enabled = true
      filter = nginx-http-auth
      logpath = /var/log/nginx/error.log
      maxretry = 3
      bantime = 3600
      
      [nginx-limit-req]
      enabled = true
      filter = nginx-limit-req
      logpath = /var/log/nginx/error.log
      maxretry = 3
      bantime = 3600
      
      [nginx-botsearch]
      enabled = true
      filter = nginx-botsearch
      logpath = /var/log/nginx/access.log
      maxretry = 2
      bantime = 86400
    owner: root:root
    permissions: '0644'

  # Audit rules
  - path: /etc/audit/rules.d/audit.rules
    content: |
      # Delete all existing rules
      -D
      
      # Buffer size
      -b 8192
      
      # Failure mode
      -f 1
      
      # Monitor authentication events
      -w /etc/passwd -p wa -k identity
      -w /etc/group -p wa -k identity
      -w /etc/shadow -p wa -k identity
      -w /etc/sudoers -p wa -k identity
      
      # Monitor system calls
      -a always,exit -F arch=b64 -S adjtimex -S settimeofday -k time-change
      -a always,exit -F arch=b32 -S adjtimex -S settimeofday -S stime -k time-change
      -a always,exit -F arch=b64 -S clock_settime -k time-change
      -a always,exit -F arch=b32 -S clock_settime -k time-change
      
      # Monitor network configuration
      -a always,exit -F arch=b64 -S sethostname -S setdomainname -k system-locale
      -a always,exit -F arch=b32 -S sethostname -S setdomainname -k system-locale
      
      # Monitor login/logout events
      -w /var/log/lastlog -p wa -k logins
      -w /var/run/faillock -p wa -k logins
      
      # Monitor file access
      -a always,exit -F arch=b64 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EACCES -F auid>=1000 -F auid!=4294967295 -k access
      -a always,exit -F arch=b32 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EACCES -F auid>=1000 -F auid!=4294967295 -k access
      -a always,exit -F arch=b64 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EPERM -F auid>=1000 -F auid!=4294967295 -k access
      -a always,exit -F arch=b32 -S creat -S open -S openat -S truncate -S ftruncate -F exit=-EPERM -F auid>=1000 -F auid!=4294967295 -k access
      
      # Make the configuration immutable
      -e 2
    owner: root:root
    permissions: '0640'

  # System monitoring script
  - path: /usr/local/bin/system-monitor
    content: |
      #!/bin/bash
      # System monitoring script for Luxcrafts
      
      LOG_FILE="/var/log/system-monitor.log"
      ALERT_EMAIL="${admin_email}"
      
      # Check system resources
      CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
      MEM_USAGE=$(free | grep Mem | awk '{printf("%.2f", $3/$2 * 100.0)}')
      DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}' | sed 's/%//')
      
      # Check services
      NGINX_STATUS=$(systemctl is-active nginx)
      DOCKER_STATUS=$(systemctl is-active docker)
      FAIL2BAN_STATUS=$(systemctl is-active fail2ban)
      
      # Log status
      echo "$(date): CPU: ${CPU_USAGE}%, MEM: ${MEM_USAGE}%, DISK: ${DISK_USAGE}%, NGINX: ${NGINX_STATUS}, DOCKER: ${DOCKER_STATUS}, FAIL2BAN: ${FAIL2BAN_STATUS}" >> $LOG_FILE
      
      # Send alerts if needed
      if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
          echo "High CPU usage: ${CPU_USAGE}%" | mail -s "Luxcrafts Alert: High CPU" $ALERT_EMAIL
      fi
      
      if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
          echo "High memory usage: ${MEM_USAGE}%" | mail -s "Luxcrafts Alert: High Memory" $ALERT_EMAIL
      fi
      
      if [ "$NGINX_STATUS" != "active" ]; then
          echo "Nginx is not running" | mail -s "Luxcrafts Alert: Nginx Down" $ALERT_EMAIL
      fi
      
      if [ "$DOCKER_STATUS" != "active" ]; then
          echo "Docker is not running" | mail -s "Luxcrafts Alert: Docker Down" $ALERT_EMAIL
      fi
    owner: root:root
    permissions: '0755'

  # Dokploy installation script
  - path: /usr/local/bin/install-dokploy
    content: |
      #!/bin/bash
      set -e
      
      echo "Installing Dokploy..."
      
      # Install Docker if not already installed
      if ! command -v docker &> /dev/null; then
          curl -fsSL https://get.docker.com -o get-docker.sh
          sh get-docker.sh
          usermod -aG docker luxcrafts
      fi
      
      # Install Dokploy
      curl -sSL https://dokploy.com/install.sh | sh
      
      # Configure Dokploy
      systemctl enable dokploy
      systemctl start dokploy
      
      echo "Dokploy installation completed"
    owner: root:root
    permissions: '0755'

  # Security hardening script
  - path: /usr/local/bin/security-hardening
    content: |
      #!/bin/bash
      set -e
      
      echo "Applying security hardening..."
      
      # Kernel parameters
      cat >> /etc/sysctl.conf << EOF
      # IP Spoofing protection
      net.ipv4.conf.default.rp_filter = 1
      net.ipv4.conf.all.rp_filter = 1
      
      # Ignore ICMP redirects
      net.ipv4.conf.all.accept_redirects = 0
      net.ipv6.conf.all.accept_redirects = 0
      net.ipv4.conf.default.accept_redirects = 0
      net.ipv6.conf.default.accept_redirects = 0
      
      # Ignore send redirects
      net.ipv4.conf.all.send_redirects = 0
      net.ipv4.conf.default.send_redirects = 0
      
      # Disable source packet routing
      net.ipv4.conf.all.accept_source_route = 0
      net.ipv6.conf.all.accept_source_route = 0
      net.ipv4.conf.default.accept_source_route = 0
      net.ipv6.conf.default.accept_source_route = 0
      
      # Log Martians
      net.ipv4.conf.all.log_martians = 1
      net.ipv4.conf.default.log_martians = 1
      
      # Ignore ICMP ping requests
      net.ipv4.icmp_echo_ignore_all = 1
      
      # Ignore Directed pings
      net.ipv4.icmp_echo_ignore_broadcasts = 1
      
      # Disable IPv6 if not needed
      net.ipv6.conf.all.disable_ipv6 = 1
      net.ipv6.conf.default.disable_ipv6 = 1
      net.ipv6.conf.lo.disable_ipv6 = 1
      
      # TCP SYN flood protection
      net.ipv4.tcp_syncookies = 1
      net.ipv4.tcp_max_syn_backlog = 2048
      net.ipv4.tcp_synack_retries = 2
      net.ipv4.tcp_syn_retries = 5
      
      # Control buffer overflow attacks
      kernel.exec-shield = 1
      kernel.randomize_va_space = 2
      EOF
      
      # Apply sysctl settings
      sysctl -p
      
      # Set file permissions
      chmod 700 /root
      chmod 600 /etc/ssh/sshd_config
      chmod 644 /etc/passwd
      chmod 644 /etc/group
      chmod 600 /etc/shadow
      chmod 600 /etc/gshadow
      
      # Remove unnecessary packages
      apt-get autoremove -y
      apt-get autoclean
      
      echo "Security hardening completed"
    owner: root:root
    permissions: '0755'

# Run commands
runcmd:
  # Update system
  - apt-get update
  - apt-get upgrade -y
  
  # Configure timezone
  - timedatectl set-timezone UTC
  
  # Configure firewall
  - ufw --force reset
  - ufw default deny incoming
  - ufw default allow outgoing
  - ufw allow ssh
  - ufw allow 80/tcp
  - ufw allow 443/tcp
  - ufw --force enable
  
  # Start and enable services
  - systemctl enable nginx
  - systemctl enable fail2ban
  - systemctl enable auditd
  - systemctl enable docker
  - systemctl start nginx
  - systemctl start fail2ban
  - systemctl start auditd
  - systemctl start docker
  
  # Add luxcrafts user to docker group
  - usermod -aG docker luxcrafts
  
  # Install Node.js 20
  - curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
  - apt-get install -y nodejs
  
  # Install PM2
  - npm install -g pm2
  
  # Run security hardening
  - /usr/local/bin/security-hardening
  
  # Install Dokploy
  - /usr/local/bin/install-dokploy
  
  # Initialize AIDE
  - aideinit
  - mv /var/lib/aide/aide.db.new /var/lib/aide/aide.db
  
  # Update ClamAV
  - freshclam
  
  # Setup cron jobs
  - echo "*/5 * * * * root /usr/local/bin/system-monitor" >> /etc/crontab
  - echo "0 2 * * * root /usr/bin/aide --check" >> /etc/crontab
  - echo "0 3 * * * root /usr/bin/clamscan -r /var/www --log=/var/log/clamav/scan.log" >> /etc/crontab
  - echo "0 4 * * 0 root /usr/sbin/lynis audit system --cronjob" >> /etc/crontab
  - echo "0 12 * * * root /usr/bin/certbot renew --quiet --nginx" >> /etc/crontab
  
  # Restart SSH with new configuration
  - systemctl restart sshd
  
  # Create application directory
  - mkdir -p /var/www/luxcrafts
  - chown luxcrafts:luxcrafts /var/www/luxcrafts
  
  # Setup log rotation
  - logrotate -f /etc/logrotate.conf
  
  # Final system update
  - apt-get update
  - apt-get autoremove -y
  - apt-get autoclean

# Reboot after setup
power_state:
  delay: "+1"
  mode: reboot
  message: "Rebooting after initial setup"
  timeout: 30
  condition: True