# ESTRATIX Project Structure

## Root Directory Organization
```
.
├── .github/              # CI/CD workflows and issue templates
├── .kiro/               # Kiro AI assistant configuration and steering
├── .windsurf/           # Agentic workflows and project automation
├── docs/                # All project documentation and matrices
├── src/                 # Source code (hexagonal architecture)
├── tests/               # Test suites mirroring src structure
├── notebooks/           # Markdown notebooks for research/experimentation
├── data/                # Runtime data storage
├── archive/             # Historical documentation and legacy code
├── clients/             # Client-specific implementations
├── project_management/  # Project planning and management docs
└── executive_strategy/  # High-level strategic planning
```

## Source Code Architecture (src/)
Following **Hexagonal Architecture** principles:

```
src/
├── domain/              # Core business logic (inner layer)
│   ├── models/          # Domain entities and value objects
│   ├── services/        # Domain services
│   ├── tools/           # Domain-specific tools
│   └── productized_services/  # Business service definitions
├── application/         # Application layer (orchestration)
│   ├── services/        # Application sers and use cases
│   ├── ports/           # Interface definitions (contracts)
│   └── dtos/            # Data transfer objects
└── infrastructure/      # External adapters (outer layer)
    ├── adapters/        # External system adapters
    ├── agents/          # AI agent implementations
    ├── database/        # Database connections and repositories
    ├── llm/             # LLM provider integrations
    ├── vector_database/ # Vector database implementations
    ├── orchestration/   # Agent orchestration systems
    ├── monitoring/      # System monitoring and metrics
    ├── workflows/       # Workflow automation
    └── [many others]/   # Specialized infrastructure components
```

## Documentation Structure (docs/)
**Matrix-driven documentation** organized by concern:

```
docs/
├── agents/              # Agent definitions and capabilities
├── processes/           # Business process documentation
├── tools/               # Tool specifications and usage
├── services/            # Service definitions and APIs
├── patterns/            # Architectural and design patterns
├── standards/           # Coding and operational standards
├── system_prompts/      # AI system prompts and templates
├── meta_prompts/        # Meta-prompting strategies
├── flows/               # Workflow and process flows
└── [specialized]/       # Domain-specific documentation
```

## Test Structure (tests/)
Mirrors source structure for clear test organization:

```
tests/
├── domain/              # Domain logic tests
├── application/         # Application service tests
├── infrastructure/      # Infrastructure adapter tests
├── e2e/                 # End-to-end integration tests
└── interfaces/          # API and interface tests
```

## Key Conventions

### File Naming
- **Python modules**: snake_case
- **Classes**: PascalCase
- **Functions/methods**: snake_case
- **Constants**: UPPER_SNAKE_CASE
- **Documentation**: kebab-case.md

### Directory Principles
- **Domain-first**: Core business logic isolated in domain layer
- **Dependency Direction**: Dependencies point inward (infrastructure → application → domain)
- **Matrix Registration**: All components registered in docs/ matrices
- **Agent-Accessible**: Structure optimized for AI agent navigation and understanding

### Special Directories
- **.kiro/**: AI assistant configuration and steering rules
- **.windsurf/**: Agentic workflow automation
- **data/**: Runtime data (logs, cache, metrics) - not version controlled
- **archive/**: Historical artifacts - preserved but not active
- **notebooks/**: Research and experimentation - temporary workspace