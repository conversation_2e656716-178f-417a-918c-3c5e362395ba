---
description: Guides the definition of a new ESTRATIX internal subproject, including its charter creation and registration in the project matrix.
---

# Workflow: Define New ESTRATIX Internal Project

**Objective:** To operationalize the initiation and formal definition of a new ESTRATIX internal subproject. This workflow ensures all necessary documentation is created using standard templates and that the project is registered in the central `project_matrix.md`.

**Orchestrating Command Office:** The Sponsoring Command Office (e.g., CTO, COO).

## Prerequisites

- An approved proposal exists in `docs/proposals/`.
- The agent has access to `docs/templates/estratix_project_definition_template.md`.

## Steps

1. **Initiate Project Definition & Assign Metadata**
   - **Action:** From the approved proposal, confirm the Project Name, Sponsoring CO, and Project Type (e.g., Internal Strategic Initiative, Service Development).
   - **Action:** Consult `docs/matrices/project_matrix.md` to determine the next sequential Project ID (`[SCOPE]_[REF]_P[NUM]`).

2. **Create Draft Project Definition Document**
   - **Action:** Create the project definition document using the standard template.
   - **Template:** `docs/templates/estratix_project_definition_template.md`
   - **Path:** `docs/projects/estratix_master_project/02_Subprojects/[Project_ID]_[ProjectName]/00_Charter_and_Definition/[Project_ID]_Definition.md`
   - **Tool:** `write_to_file` to create the document and populate it with the template, filling in the initial overview section.

3. **Complete & Approve Project Definition**
   - **Action:** Collaboratively fill in all remaining sections of the definition document.
   - **Action:** Circulate the completed document for formal approval from the Sponsoring CO.

4. **Update Project Matrix**
   - **Action:** Register the newly approved project in `docs/matrices/project_matrix.md`.
   - **Tool:** `edit_file` to add a new row with all details from the definition, including the correct `Definition Link` and `Project Directory Link`.
   - **Status:** Set to "Defined" or "Planning".

## Outputs

- An approved Project Definition document.
- An updated `docs/matrices/project_matrix.md`.
