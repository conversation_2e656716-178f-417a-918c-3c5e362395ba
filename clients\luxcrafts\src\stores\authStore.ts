import { create } from 'zustand/react';
import { persist } from 'zustand/middleware';
import { authService } from '../services/auth.service';
import { AuthState, LoginCredentials, RegisterData, User, UserRole, Permission } from '../types/auth';

// Re-export types for backward compatibility
export type { User, UserRole, Permission } from '../types/auth';

interface AuthStore extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  updateUser: (user: Partial<User>) => void;
  hasPermission: (permission: Permission) => boolean;
  hasRole: (role: UserRole) => boolean;
  getCurrentSubdomain: () => string | null;
  switchSubdomain: (subdomain: string) => void;
}



export const useAuthStore = create<AuthStore>()(persist(
  (set, get) => ({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
    
    login: async (credentials: LoginCredentials) => {
      set({ isLoading: true, error: null });
      try {
        const response = await authService.login(credentials);
        set({ user: response.user, isAuthenticated: true, isLoading: false });
      } catch (error) {
        set({ error: (error as Error).message, isLoading: false });
      }
    },
    
    register: async (data: RegisterData) => {
      set({ isLoading: true, error: null });
      try {
        const response = await authService.register(data);
        set({ user: response.user, isAuthenticated: true, isLoading: false });
      } catch (error) {
        set({ error: (error as Error).message, isLoading: false });
      }
    },
    
    logout: () => {
      set({ user: null, isAuthenticated: false, error: null });
    },
    
    updateUser: (userData: Partial<User>) => {
      const { user } = get();
      if (user) {
        set({ user: { ...user, ...userData } });
      }
    },
    
    hasPermission: (permission: Permission) => {
      const { user } = get();
      return user?.permissions.includes(permission) || false;
    },
    
    hasRole: (role: UserRole) => {
      const { user } = get();
      return user?.role === role;
    },
    
    getCurrentSubdomain: () => {
      if (typeof window !== 'undefined') {
        const hostname = window.location.hostname;
        const parts = hostname.split('.');
        if (parts.length > 2) {
          return parts[0];
        }
      }
      return null;
    },
    
    switchSubdomain: (subdomain: string) => {
      if (typeof window !== 'undefined') {
        const currentDomain = window.location.hostname.split('.').slice(-2).join('.');
        const newUrl = `${window.location.protocol}//${subdomain}.${currentDomain}${window.location.pathname}`;
        window.location.href = newUrl;
      }
    }
  }),
  {    name: 'auth-storage',    partialize: (state: AuthStore) => ({       user: state.user,       isAuthenticated: state.isAuthenticated     })  }
));