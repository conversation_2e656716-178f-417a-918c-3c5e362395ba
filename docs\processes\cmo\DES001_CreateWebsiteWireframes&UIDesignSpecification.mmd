graph TD
    A[Start: PLN001 Approved, DES001 Initiated] --> B["Step 1: Initiation & Input Assimilation"];
    B -- Inputs Reviewed & Project Setup --> C["Step 2: User Flow Analysis & Wireframing"];
    C -- Wireframes Iterated & Internally Approved --> D["Step 3: Mood Boarding & Visual Style Definition (if needed)"];
    D -- Visual Direction Aligned --> E["Step 4: High-Fidelity Mockup Design"];
    E -- Mockups Created --> F["Step 5: Optional: Interactive Prototyping (Figma)"];
    F -- Prototypes Developed (or Skipped) --> G["Step 6: Responsive Design & Accessibility Specifications"];
    G -- Specs Defined --> H["Step 7: UI Design Specification Document Compilation"];
    H -- Spec Doc Drafted --> I["Step 8: Internal Review & QA"];
    I -- Design Internally Approved --> K["Step 9: Client/Stakeholder Presentation, Feedback & Iteration"];
    K -- Revisions Incorporated & Design Approved --> L["Step 10: Final Approval & Handoff Preparation"];
    L -- Assets & Docs Prepared --> M[End: UI Design & Specification Approved & Handoff Ready];

    subgraph Inputs_PLN001
        PLN_Plan["Website Strategy & Plan Document"]
        PLN_Sitemap["Finalized Sitemap"]
        PLN_ContentOutline["High-Level Content Structure Outline"]
        PLN_Moodboard["(Optional) Initial Visual Concept/Mood Board"]
    end

    subgraph Inputs_Brand
        Brand_Assets["Client Brand Assets/Style Guides"]
        Estratix_Standards["ESTRATIX Design Standards"]
    end

    subgraph Outputs_DES001
        O_Wireframes["Annotated Wireframes (Figma)"]
        O_Mockups["High-Fidelity Mockups (Figma)"]
        O_Prototype["(Optional) Interactive Prototype (Figma)"]
        O_SpecDoc["UI Design Specification Document"]
        O_Assets["Design Assets & Resources"]
        O_Approval["Client Approval Record"]
    end

    PLN_Plan --> B;
    PLN_Sitemap --> B;
    PLN_ContentOutline --> B;
    PLN_Moodboard --> B;
    Brand_Assets --> B;
    Estratix_Standards --> B;

    C --> O_Wireframes;
    E --> O_Mockups;
    F --> O_Prototype;
    H --> O_SpecDoc;
    L --> O_Assets;
    L --> O_Approval;

    %% Key Tools/Activities
    C --> T_FigmaWire[Figma for Wireframing]
    E --> T_FigmaMock[Figma for Mockups]
    E --> T_MagicUIDesign[Optional: @magicuidesign/mcp]
    F --> T_FigmaProto[Figma for Prototyping]
    H --> T_DocTools[Notion/Confluence/Markdown for Spec Doc]

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style M fill:#f9f,stroke:#333,stroke-width:2px

%% Notes:
%% This is a high-level overview of the DES001 process.
%% It details the transformation of PLN001 outputs into a full UI Design Specification.
%% Key tools like Figma and potential MCPs are highlighted.
