---
version: 1.1
last_updated: 2025-06-29
responsible_office: CIO
status: Defined
description: "Defines the master ESTRATIX workflow for the end-to-end knowledge lifecycle, from ingestion and embedding to retrieval, context augmentation, and maintenance."
---

# ESTRATIX Master Workflow: Knowledge Lifecycle Management

## 1. Workflow Overview

**Purpose:** This document outlines the definitive, end-to-end lifecycle for all knowledge assets within ESTRATIX. It serves as the master framework for ensuring that all agents and systems have access to secure, relevant, and up-to-date information to perform their tasks. This workflow integrates ingestion, retrieval, and maintenance into a single, cohesive strategy.

**Governing Offices:** CIO (Overall Lifecycle, Ingestion, Maintenance), CTO (Retrieval Tooling), CPO (Process Standardization)

## 2. Workflow Diagram

```mermaid
graph TD
    subgraph Phase 1: Ingestion & Embedding
        A[Start: New Knowledge Source Identified] --> B{Register in Primary Matrix};
        B --> C{Register in source_matrix.md};
        C --> D[Acquire Content];
        D --> E[Process & Chunk Content];
        E --> F[Generate Embeddings];
        F --> G[Store Vectors & Metadata];
        G --> H[Log Ingestion];
    end

    subgraph Phase 2: Retrieval & Augmentation
        I[Agent Task Requires Knowledge] --> J[Formulate Query];
        J --> K[Use T_CIO_001_VectorSearchTool];
        K --> L[Retrieve Relevant Chunks];
        L --> M[Augment Prompt];
        M --> N[Generate Informed Response];
    end

    subgraph Phase 3: Curation & Maintenance
        O[Scheduled Monitoring] --> P{New Version Detected?};
        P -- Yes --> Q{Update Primary Matrix};
        Q --> C;
        P -- No --> R[Check for Staleness];
        R -- Stale --> S{Recommend Archival};
        S --> T[Manual Approval];
        T -- Approved --> U[Archive/Prune Data];
        U --> V[Log Action];
    end

    H --> I;
    N --> O;
```

## 3. Phase 1: Knowledge Ingestion & Embedding

This phase covers the process of identifying, acquiring, processing, and storing external and internal knowledge in a queryable format.

**Orchestrating Flow:** `CIO_F001_DocumentationIngestion`

### Ingestion Steps

1. **Source Registration & Provenance:** A knowledge source is identified from a governing matrix. Any component with associated documentation or unstructured data can be a source. The component is first registered in its primary matrix, then registered in `source_matrix.md` to trigger ingestion.
    - **Examples of Governing Matrices & Sources:**
        - **`library_matrix.md`**: A new library version.
        - **`tool_matrix.md`**: A tool's README or documentation.
        - **`research_matrix.md`**: A research paper or article.
        - **`proposal_matrix.md`**: A client or internal proposal.
        - **`project_matrix.md`**: Project charters or design documents.
        - **`productized_service_matrix.md`**: Service definition documents.
        - **`provider_matrix.md`**: Documentation from a third-party provider.
        - **`user_matrix.md`**: Transcripts or notes from expert interviews.
        - **`product_matrix.md`**: Product specifications.
        - **`frontend_matrix.md`, `backend_matrix.md`, `database_matrix.md`**: Framework documentation.
    - **Required Metadata in `source_matrix.md`:**
        - `Governing_Matrix_ID`: The ID of the matrix where the component is primarily defined (e.g., `matrix-001`).
        - `Reference_ID`: The specific ID of the item within its governing matrix (e.g., `LIB-001`, `TOOL-005`).
        - `Reference_Version`: The specific version identifier (e.g., Git tag `v1.2.3`, `Doc v1.1`, `YYYY-MM-DD`).
        - `Source_URI`: The direct URI to the raw content (e.g., URL to docs, file path to markdown).
2. **Content Acquisition:** The appropriate specialist agent (`CTO_A002_WebScrapingSpecialist`, `CTO_A003_PDFProcessingSpecialist`) is dispatched to fetch the raw content based on the `Source_URI`.
3. **Content Processing & Chunking:** The `CTO_A004_ContentProcessingSpecialist` cleans, normalizes, and segments the raw content into semantically meaningful chunks suitable for embedding.
4. **Embedding Generation:** The `CIO_A001_EmbeddingAgent` uses a designated model (from `llm_matrix.md`) to convert the text chunks into vector embeddings.
5. **Vector & Metadata Storage:** The `CIO_A002_VectorDBLoaderAgent` loads the embeddings into the active vector database (from `vector_db_matrix.md`). The metadata for each vector **must** include the `Governing_Matrix_ID`, `Reference_ID`, and `Reference_Version` to ensure full traceability.
6. **Logging & Verification:** The `ingestion_log_matrix.md` is updated with the status and outcome, and the source is marked as processed in the `source_matrix.md`.

---

## 4. Phase 2: Knowledge Retrieval & Context Augmentation (RAG)

This phase defines the standard mechanism by which agents query the knowledge base and use the results to inform their actions (Retrieval-Augmented Generation).

**Governing Process:** `CTO_P007_ContextRetrieval`

### Retrieval & Augmentation Steps

1. **Query Formulation:** An agent, based on its current task, formulates a semantic query to find relevant information.
2. **Tool-Based Retrieval:** The agent utilizes the standardized `T_CIO_001_VectorSearchTool`. This tool is a domain-layer component that provides a consistent interface to the underlying vector database.
3. **Vector Search Execution:** The tool sends the query to the vector database, which performs a similarity search and returns the most relevant text chunks (and their metadata).
4. **Context Augmentation:** The agent receives the retrieved text chunks. It then dynamically inserts this information into its prompt-building process, typically within a designated `<CONTEXT>` or `<KNOWLEDGE_BASE>` section of the prompt template.
5. **Augmented Generation:** The agent submits the augmented prompt (original query + retrieved context) to the LLM for a more informed and accurate response.

---

## 5. Phase 3: Knowledge Curation & Maintenance

This phase ensures the long-term health and relevance of the knowledge base.

**Governing Process:** `CIO_P003_KnowledgeCuration`

### Curation & Maintenance Steps

1. **Proactive Version & Staleness Detection:** Automated agents (`CIO_A003_KnowledgeMonitorAgent`) periodically check sources listed in the various component matrices. This involves querying package repositories, checking Git tags, or monitoring `Last-Modified` headers.
2. **Review & Re-Ingestion Trigger:**
    - **New Version:** If a new version is detected, the agent updates the entry in its primary governing matrix (e.g., `library_matrix.md`) and then creates a new entry in `source_matrix.md` to trigger the Phase 1 ingestion workflow for the new version.
    - **Deprecation:** If a component version is marked as deprecated, the agent flags the corresponding knowledge assets for archival.
3. **Archival & Pruning:** Based on usage metrics, deprecation flags, and strategic relevance, the `CIO_A004_KnowledgeLibrarianAgent` recommends the archival or deletion of outdated knowledge sources. This requires approval from the `CIO`. Archived vectors are moved to cold storage, not deleted, to preserve historical context if needed.
4. **Feedback Loop:** Agents can provide feedback on the quality of retrieved context. Low-quality or irrelevant results can trigger a review of the source document, its chunking strategy, or its entry in its governing matrix, potentially leading to re-ingestion or archival.

---

## 6. Guidance for Use

- This workflow is the master guide for all knowledge-related operations.
- **To add knowledge:** First, ensure the asset is registered in its primary governing matrix (e.g., `tool_matrix.md`). Then, add an entry to `source_matrix.md` with all required metadata to trigger the ingestion workflow.
- **To use knowledge:** All agents must use the standardized `T_CIO_001_VectorSearchTool` as defined in Phase 2.
- **To maintain knowledge:** The processes in Phase 3 are automated and overseen by the CIO's office.
