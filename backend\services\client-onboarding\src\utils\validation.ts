import { z } from 'zod';

// Common validation schemas
export const emailSchema = z.string().email('Invalid email format');
export const phoneSchema = z.string().regex(/^[+]?[1-9]?[0-9]{7,15}$/, 'Invalid phone number format');
export const uuidSchema = z.string().uuid('Invalid UUID format');
export const dateSchema = z.string().datetime('Invalid date format');

// Client validation schemas
export const createClientSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  email: emailSchema,
  phone: phoneSchema.optional(),
  company: z.string().max(100, 'Company name too long').optional(),
  industry: z.string().max(50, 'Industry name too long').optional(),
  source: z.enum(['website', 'referral', 'marketing', 'sales', 'other']),
  tags: z.array(z.string().max(30, 'Tag too long')).max(10, 'Too many tags').optional(),
  customFields: z.record(z.any()).optional()
});

export const updateClientSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  company: z.string().max(100, 'Company name too long').optional(),
  industry: z.string().max(50, 'Industry name too long').optional(),
  status: z.enum(['lead', 'prospect', 'active', 'inactive', 'churned']).optional(),
  assignedTo: uuidSchema.optional(),
  tags: z.array(z.string().max(30, 'Tag too long')).max(10, 'Too many tags').optional(),
  customFields: z.record(z.any()).optional()
});

// RFP validation schemas
export const rfpRequirementSchema = z.object({
  category: z.string().min(1, 'Category is required').max(50, 'Category too long'),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  priority: z.enum(['must_have', 'should_have', 'nice_to_have']),
  criteria: z.array(z.string().max(200, 'Criteria too long')).max(10, 'Too many criteria')
});

export const rfpMilestoneSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long'),
  dueDate: dateSchema,
  deliverables: z.array(z.string().max(200, 'Deliverable too long')).max(20, 'Too many deliverables')
});

export const createRFPSchema = z.object({
  clientId: uuidSchema,
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(2000, 'Description too long'),
  requirements: z.array(rfpRequirementSchema).min(1, 'At least one requirement is needed').max(50, 'Too many requirements'),
  budget: z.object({
    min: z.number().min(0, 'Minimum budget must be positive'),
    max: z.number().min(0, 'Maximum budget must be positive'),
    currency: z.string().length(3, 'Currency must be 3 characters')
  }).refine(data => data.max >= data.min, {
    message: 'Maximum budget must be greater than or equal to minimum budget'
  }).optional(),
  timeline: z.object({
    startDate: dateSchema,
    endDate: dateSchema,
    milestones: z.array(rfpMilestoneSchema).max(20, 'Too many milestones')
  }).refine(data => new Date(data.endDate) > new Date(data.startDate), {
    message: 'End date must be after start date'
  }),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional()
});

// Onboarding validation schemas
export const onboardingFormFieldSchema = z.object({
  name: z.string().min(1, 'Field name is required').max(50, 'Field name too long'),
  label: z.string().min(1, 'Field label is required').max(100, 'Field label too long'),
  type: z.enum(['text', 'email', 'phone', 'number', 'date', 'select', 'multiselect', 'textarea', 'file']),
  required: z.boolean(),
  options: z.array(z.string().max(100, 'Option too long')).max(50, 'Too many options').optional(),
  validation: z.object({
    pattern: z.string().optional(),
    min: z.number().optional(),
    max: z.number().optional(),
    minLength: z.number().min(0, 'Minimum length must be non-negative').optional(),
    maxLength: z.number().min(1, 'Maximum length must be positive').optional()
  }).optional()
});

export const onboardingDocumentSchema = z.object({
  name: z.string().min(1, 'Document name is required').max(100, 'Document name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  type: z.enum(['contract', 'nda', 'sow', 'invoice', 'identity', 'other']),
  required: z.boolean(),
  template: z.string().max(200, 'Template path too long').optional()
});

export const onboardingStepSchema = z.object({
  name: z.string().min(1, 'Step name is required').max(100, 'Step name too long'),
  description: z.string().max(500, 'Description too long'),
  type: z.enum(['form', 'document_upload', 'meeting', 'approval', 'automated', 'custom']),
  required: z.boolean(),
  order: z.number().min(1, 'Order must be positive'),
  estimatedDuration: z.number().min(1, 'Duration must be positive').optional(),
  instructions: z.string().max(1000, 'Instructions too long').optional(),
  formFields: z.array(onboardingFormFieldSchema).max(50, 'Too many form fields').optional(),
  documents: z.array(onboardingDocumentSchema).max(20, 'Too many documents').optional()
});

export const createOnboardingFlowSchema = z.object({
  clientId: uuidSchema,
  type: z.enum(['basic', 'premium', 'enterprise', 'custom']),
  assignedTo: uuidSchema.optional(),
  customSteps: z.array(onboardingStepSchema).max(50, 'Too many custom steps').optional()
});

// Query parameter schemas
export const paginationSchema = z.object({
  page: z.string().regex(/^\d+$/, 'Page must be a number').transform(Number).refine(n => n >= 1, 'Page must be at least 1').optional(),
  limit: z.string().regex(/^\d+$/, 'Limit must be a number').transform(Number).refine(n => n >= 1 && n <= 100, 'Limit must be between 1 and 100').optional(),
  sortBy: z.string().max(50, 'Sort field too long').optional(),
  sortOrder: z.enum(['asc', 'desc']).optional()
});

export const clientFilterSchema = z.object({
  status: z.enum(['lead', 'prospect', 'active', 'inactive', 'churned']).optional(),
  source: z.enum(['website', 'referral', 'marketing', 'sales', 'other']).optional(),
  industry: z.string().max(50, 'Industry filter too long').optional(),
  assignedTo: uuidSchema.optional(),
  tags: z.string().max(200, 'Tags filter too long').optional(),
  search: z.string().max(100, 'Search term too long').optional()
}).merge(paginationSchema);

export const rfpFilterSchema = z.object({
  status: z.enum(['draft', 'submitted', 'under_review', 'approved', 'rejected', 'cancelled']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'urgent']).optional(),
  clientId: uuidSchema.optional(),
  search: z.string().max(100, 'Search term too long').optional()
}).merge(paginationSchema);

export const onboardingFilterSchema = z.object({
  status: z.enum(['not_started', 'in_progress', 'completed', 'paused', 'cancelled']).optional(),
  type: z.enum(['basic', 'premium', 'enterprise', 'custom']).optional(),
  clientId: uuidSchema.optional(),
  assignedTo: uuidSchema.optional()
}).merge(paginationSchema);

// Validation helper functions
export function validateRequest<T>(schema: z.ZodSchema<T>, data: unknown): T {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const formattedErrors = error.errors.map(err => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code
      }));
      
      throw new ValidationError('Validation failed', formattedErrors);
    }
    throw error;
  }
}

export class ValidationError extends Error {
  public readonly errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;

  constructor(message: string, errors: Array<{ field: string; message: string; code: string }>) {
    super(message);
    this.name = 'ValidationError';
    this.errors = errors;
  }
}

export function sanitizeString(input: string, maxLength: number = 1000): string {
  return input
    .trim()
    .replace(/[<>"'&]/g, '') // Remove potentially dangerous characters
    .substring(0, maxLength);
}

export function sanitizeEmail(email: string): string {
  return email.toLowerCase().trim();
}

export function sanitizePhone(phone: string): string {
  return phone.replace(/[^+\d]/g, ''); // Keep only digits and plus sign
}

// Specific validation functions for routes
export function validateCreateClient(data: unknown) {
  return validateRequest(createClientSchema, data);
}

export function validateUpdateClient(data: unknown) {
  return validateRequest(updateClientSchema, data);
}

export function validateCreateRFP(data: unknown) {
  return validateRequest(createRFPSchema, data);
}

export function validateCreateOnboardingFlow(data: unknown) {
  return validateRequest(createOnboardingFlowSchema, data);
}