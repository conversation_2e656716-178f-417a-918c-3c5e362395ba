import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  PhoneIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  CalendarIcon,
  PlayIcon,
  PauseIcon,
  MicrophoneIcon,
  SpeakerWaveIcon,
} from '@heroicons/react/24/outline';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

const propertyData = [
  {
    id: 1,
    address: '123 Beacon Hill, Boston, MA',
    price: '$2,450,000',
    estimatedValue: '$2,680,000',
    potentialProfit: '$230,000',
    status: 'Under Analysis',
    aiScore: 8.5,
    lastContact: '2 hours ago',
    nextAction: 'Schedule viewing',
  },
  {
    id: 2,
    address: '456 Back Bay, Boston, MA',
    price: '$1,850,000',
    estimatedValue: '$2,100,000',
    potentialProfit: '$250,000',
    status: 'Negotiating',
    aiScore: 9.2,
    lastContact: '1 day ago',
    nextAction: 'Counter offer',
  },
  {
    id: 3,
    address: '789 Cambridge St, Cambridge, MA',
    price: '$3,200,000',
    estimatedValue: '$3,450,000',
    potentialProfit: '$250,000',
    status: 'Contract Pending',
    aiScore: 7.8,
    lastContact: '3 hours ago',
    nextAction: 'Review contract',
  },
];

const marketTrends = [
  { month: 'Jan', avgPrice: 2100000, volume: 45 },
  { month: 'Feb', avgPrice: 2150000, volume: 52 },
  { month: 'Mar', avgPrice: 2200000, volume: 48 },
  { month: 'Apr', avgPrice: 2180000, volume: 55 },
  { month: 'May', avgPrice: 2250000, volume: 62 },
  { month: 'Jun', avgPrice: 2300000, volume: 58 },
];

const aiAgents = [
  {
    name: 'Property Scout AI',
    status: 'Active',
    task: 'Scanning MLS listings for undervalued properties',
    propertiesFound: 12,
    successRate: '94%',
  },
  {
    name: 'Negotiation AI',
    status: 'In Call',
    task: 'Negotiating price for 456 Back Bay property',
    callDuration: '00:15:32',
    successRate: '87%',
  },
  {
    name: 'Market Analysis AI',
    status: 'Processing',
    task: 'Analyzing comparable sales in Cambridge area',
    dataPoints: 1247,
    successRate: '96%',
  },
];

export default function PropertyAcquisition() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isRecording, setIsRecording] = useState(false);
  const [selectedProperty, setSelectedProperty] = useState(null);

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: ChartBarIcon },
    { id: 'search', name: 'Property Search', icon: MagnifyingGlassIcon },
    { id: 'ai-agents', name: 'AI Agents', icon: PhoneIcon },
    { id: 'contracts', name: 'Contracts', icon: DocumentTextIcon },
    { id: 'analytics', name: 'Market Analytics', icon: CurrencyDollarIcon },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Property Acquisition Center
          </h1>
          <p className="text-xl text-gray-600">
            AI-powered property research, negotiation, and acquisition management
          </p>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-white rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Properties Tracked</p>
                    <p className="text-3xl font-bold text-gray-900">247</p>
                  </div>
                  <BuildingOfficeIcon className="w-8 h-8 text-blue-500" />
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-white rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Negotiations</p>
                    <p className="text-3xl font-bold text-gray-900">8</p>
                  </div>
                  <PhoneIcon className="w-8 h-8 text-green-500" />
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-white rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Potential Profit</p>
                    <p className="text-3xl font-bold text-gray-900">$1.2M</p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </motion.div>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-white rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Success Rate</p>
                    <p className="text-3xl font-bold text-gray-900">89%</p>
                  </div>
                  <ChartBarIcon className="w-8 h-8 text-purple-500" />
                </div>
              </motion.div>
            </div>

            {/* Property Pipeline */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Property Pipeline</h3>
              <div className="space-y-4">
                {propertyData.map((property) => (
                  <motion.div
                    key={property.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                    onClick={() => setSelectedProperty(property)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <MapPinIcon className="w-5 h-5 text-gray-400" />
                          <h4 className="font-semibold text-gray-900">{property.address}</h4>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            property.status === 'Under Analysis' ? 'bg-yellow-100 text-yellow-800' :
                            property.status === 'Negotiating' ? 'bg-blue-100 text-blue-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {property.status}
                          </span>
                        </div>
                        <div className="mt-2 grid grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">Price:</span>
                            <span className="ml-1 font-medium">{property.price}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Est. Value:</span>
                            <span className="ml-1 font-medium">{property.estimatedValue}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">Profit:</span>
                            <span className="ml-1 font-medium text-green-600">{property.potentialProfit}</span>
                          </div>
                          <div>
                            <span className="text-gray-500">AI Score:</span>
                            <span className="ml-1 font-medium">{property.aiScore}/10</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500">{property.lastContact}</p>
                        <p className="text-sm font-medium text-blue-600">{property.nextAction}</p>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Market Trends Chart */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Market Trends</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={marketTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${(Number(value) / 1000000).toFixed(1)}M`, 'Avg Price']} />
                    <Line type="monotone" dataKey="avgPrice" stroke="#3b82f6" strokeWidth={3} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}

        {/* AI Agents Tab */}
        {activeTab === 'ai-agents' && (
          <div className="space-y-8">
            {/* Voice Control Panel */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">AI Voice Command Center</h3>
              <div className="flex items-center justify-center space-x-6">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => setIsRecording(!isRecording)}
                  className={`w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 ${
                    isRecording
                      ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                      : 'bg-blue-500 hover:bg-blue-600'
                  }`}
                >
                  <MicrophoneIcon className="w-8 h-8 text-white" />
                </motion.button>
                <div className="text-center">
                  <p className="text-lg font-semibold text-gray-900">
                    {isRecording ? 'Listening...' : 'Click to give voice command'}
                  </p>
                  <p className="text-sm text-gray-500">
                    Say: "Find properties under $2M in Cambridge" or "Call owner of 123 Main St"
                  </p>
                </div>
              </div>
            </div>

            {/* Active AI Agents */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {aiAgents.map((agent, index) => (
                <motion.div
                  key={agent.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-lg p-6"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-bold text-gray-900">{agent.name}</h4>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      agent.status === 'Active' ? 'bg-green-100 text-green-800' :
                      agent.status === 'In Call' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {agent.status}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{agent.task}</p>
                  <div className="space-y-2">
                    {agent.propertiesFound && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Properties Found:</span>
                        <span className="text-sm font-medium">{agent.propertiesFound}</span>
                      </div>
                    )}
                    {agent.callDuration && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Call Duration:</span>
                        <span className="text-sm font-medium">{agent.callDuration}</span>
                      </div>
                    )}
                    {agent.dataPoints && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-500">Data Points:</span>
                        <span className="text-sm font-medium">{agent.dataPoints}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Success Rate:</span>
                      <span className="text-sm font-medium text-green-600">{agent.successRate}</span>
                    </div>
                  </div>
                  <div className="mt-4 flex space-x-2">
                    <button className="flex-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                      View Details
                    </button>
                    <button className="px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors">
                      <SpeakerWaveIcon className="w-4 h-4" />
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Call History */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Recent AI Calls</h3>
              <div className="space-y-4">
                {[
                  {
                    property: '456 Back Bay, Boston, MA',
                    contact: 'Property Owner',
                    duration: '12:34',
                    outcome: 'Price reduced by $50K',
                    time: '2 hours ago',
                    status: 'Success',
                  },
                  {
                    property: '789 Cambridge St, Cambridge, MA',
                    contact: 'Real Estate Agent',
                    duration: '8:21',
                    outcome: 'Scheduled viewing for tomorrow',
                    time: '5 hours ago',
                    status: 'Success',
                  },
                  {
                    property: '321 Newbury St, Boston, MA',
                    contact: 'Property Manager',
                    duration: '15:45',
                    outcome: 'Gathering additional documents',
                    time: '1 day ago',
                    status: 'In Progress',
                  },
                ].map((call, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">{call.property}</h4>
                        <p className="text-sm text-gray-600">{call.contact} • {call.duration} • {call.time}</p>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        call.status === 'Success' ? 'bg-green-100 text-green-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {call.status}
                      </span>
                    </div>
                    <p className="mt-2 text-sm text-gray-700">{call.outcome}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Property Search Tab */}
        {activeTab === 'search' && (
          <div className="space-y-8">
            {/* Search Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">AI-Powered Property Search</h3>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                  <input
                    type="text"
                    placeholder="Boston, Cambridge, etc."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Max Price</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>$2,000,000</option>
                    <option>$3,000,000</option>
                    <option>$5,000,000</option>
                    <option>No Limit</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Property Type</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>All Types</option>
                    <option>Single Family</option>
                    <option>Condo</option>
                    <option>Multi-Family</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">AI Score</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>8.0+ (Excellent)</option>
                    <option>7.0+ (Good)</option>
                    <option>6.0+ (Fair)</option>
                    <option>All Scores</option>
                  </select>
                </div>
              </div>
              <div className="mt-4 flex space-x-4">
                <button className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  Search Properties
                </button>
                <button className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium">
                  Start AI Auto-Search
                </button>
              </div>
            </div>

            {/* Search Results */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Search Results (247 properties)</h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {propertyData.map((property) => (
                  <div key={property.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-gray-900">{property.address}</h4>
                        <p className="text-2xl font-bold text-blue-600">{property.price}</p>
                      </div>
                      <div className="text-right">
                        <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                          AI Score: {property.aiScore}/10
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                      <div>
                        <span className="text-gray-500">Est. Value:</span>
                        <span className="ml-1 font-medium">{property.estimatedValue}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Potential Profit:</span>
                        <span className="ml-1 font-medium text-green-600">{property.potentialProfit}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <button className="flex-1 px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors text-sm font-medium">
                        View Details
                      </button>
                      <button className="flex-1 px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm font-medium">
                        Start AI Call
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Contracts Tab */}
        {activeTab === 'contracts' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Contract Management</h3>
              <div className="text-center py-12">
                <DocumentTextIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h4 className="text-lg font-semibold text-gray-900 mb-2">Contract Management System</h4>
                <p className="text-gray-600 mb-6">
                  AI-powered contract generation, review, and management coming soon.
                </p>
                <button className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  Request Early Access
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && (
          <div className="space-y-8">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Market Analytics</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={marketTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="volume" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}