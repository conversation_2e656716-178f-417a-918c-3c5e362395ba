export interface Client {
  id: string;
  organizationId: string;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  industry?: string;
  status: 'lead' | 'prospect' | 'active' | 'inactive' | 'churned';
  source: 'website' | 'referral' | 'marketing' | 'sales' | 'other';
  assignedTo?: string;
  tags: string[];
  customFields: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastContactAt?: Date;
}

export interface RFP {
  id: string;
  clientId: string;
  organizationId: string;
  title: string;
  description: string;
  requirements: RFPRequirement[];
  budget?: {
    min: number;
    max: number;
    currency: string;
  };
  timeline: {
    startDate: Date;
    endDate: Date;
    milestones: RFPMilestone[];
  };
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  attachments: RFPAttachment[];
  responses: RFPResponse[];
  createdAt: Date;
  updatedAt: Date;
  submittedAt?: Date;
  reviewedAt?: Date;
}

export interface RFPRequirement {
  id: string;
  category: string;
  description: string;
  priority: 'must_have' | 'should_have' | 'nice_to_have';
  criteria: string[];
}

export interface RFPMilestone {
  id: string;
  name: string;
  description: string;
  dueDate: Date;
  deliverables: string[];
}

export interface RFPAttachment {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  uploadedAt: Date;
}

export interface RFPResponse {
  id: string;
  rfpId: string;
  vendorId?: string;
  proposal: string;
  estimatedCost: number;
  estimatedTimeline: string;
  attachments: RFPAttachment[];
  status: 'draft' | 'submitted' | 'under_review' | 'accepted' | 'rejected';
  submittedAt?: Date;
  reviewedAt?: Date;
}

export interface OnboardingFlow {
  id: string;
  clientId: string;
  organizationId: string;
  type: 'basic' | 'premium' | 'enterprise' | 'custom';
  status: 'not_started' | 'in_progress' | 'completed' | 'paused' | 'cancelled';
  currentStep: number;
  totalSteps: number;
  steps: OnboardingStep[];
  assignedTo?: string;
  startedAt?: Date;
  completedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface OnboardingStep {
  id: string;
  name: string;
  description: string;
  type: 'form' | 'document_upload' | 'meeting' | 'approval' | 'automated' | 'custom';
  status: 'pending' | 'in_progress' | 'completed' | 'skipped' | 'failed';
  required: boolean;
  order: number;
  estimatedDuration?: number; // in minutes
  instructions?: string;
  formFields?: OnboardingFormField[];
  documents?: OnboardingDocument[];
  completedAt?: Date;
  completedBy?: string;
  notes?: string;
}

export interface OnboardingFormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'email' | 'phone' | 'number' | 'date' | 'select' | 'multiselect' | 'textarea' | 'file';
  required: boolean;
  options?: string[];
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
  value?: any;
}

export interface OnboardingDocument {
  id: string;
  name: string;
  description?: string;
  type: 'contract' | 'nda' | 'sow' | 'invoice' | 'identity' | 'other';
  required: boolean;
  template?: string;
  status: 'pending' | 'uploaded' | 'approved' | 'rejected';
  file?: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    url: string;
    uploadedAt: Date;
  };
  approvedBy?: string;
  approvedAt?: Date;
  rejectionReason?: string;
}

export interface Analytics {
  id: string;
  organizationId: string;
  type: 'client' | 'rfp' | 'onboarding' | 'conversion';
  metrics: Record<string, number>;
  dimensions: Record<string, string>;
  period: {
    start: Date;
    end: Date;
  };
  createdAt: Date;
}

export interface CreateClientRequest {
  name: string;
  email: string;
  phone?: string;
  company?: string;
  industry?: string;
  source: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface UpdateClientRequest {
  name?: string;
  email?: string;
  phone?: string;
  company?: string;
  industry?: string;
  status?: Client['status'];
  assignedTo?: string;
  tags?: string[];
  customFields?: Record<string, any>;
}

export interface CreateRFPRequest {
  clientId: string;
  title: string;
  description: string;
  requirements: Omit<RFPRequirement, 'id'>[];
  budget?: {
    min: number;
    max: number;
    currency: string;
  };
  timeline: {
    startDate: string;
    endDate: string;
    milestones: Omit<RFPMilestone, 'id'>[];
  };
  priority?: RFP['priority'];
}

export interface CreateOnboardingFlowRequest {
  clientId: string;
  type: OnboardingFlow['type'];
  assignedTo?: string;
  customSteps?: Omit<OnboardingStep, 'id' | 'status' | 'completedAt' | 'completedBy'>[];
}