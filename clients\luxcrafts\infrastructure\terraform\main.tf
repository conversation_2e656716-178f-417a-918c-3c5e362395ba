# Luxcrafts Enterprise VPS Infrastructure
# Terraform configuration for automated VPS provisioning

terraform {
  required_version = ">= 1.0"
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 4.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
    tls = {
      source  = "hashicorp/tls"
      version = "~> 4.0"
    }
  }
  
  backend "s3" {
    bucket = "luxcrafts-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
  }
}

# Variables
variable "do_token" {
  description = "DigitalOcean API token"
  type        = string
  sensitive   = true
}

variable "cloudflare_api_token" {
  description = "Cloudflare API token"
  type        = string
  sensitive   = true
}

variable "domain_name" {
  description = "Primary domain name"
  type        = string
  default     = "luxcrafts.co"
}

variable "environment" {
  description = "Environment (staging/production)"
  type        = string
  default     = "production"
}

variable "region" {
  description = "DigitalOcean region"
  type        = string
  default     = "nyc3"
}

variable "droplet_size" {
  description = "Droplet size"
  type        = string
  default     = "s-4vcpu-8gb"
}

variable "admin_email" {
  description = "Administrator email"
  type        = string
  default     = "<EMAIL>"
}

variable "ssh_public_key_path" {
  description = "Path to SSH public key"
  type        = string
  default     = "~/.ssh/id_rsa.pub"
}

# Providers
provider "digitalocean" {
  token = var.do_token
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

# Data sources
data "digitalocean_ssh_key" "main" {
  name = "luxcrafts-${var.environment}"
}

data "cloudflare_zone" "main" {
  name = var.domain_name
}

# Generate SSH key pair
resource "tls_private_key" "ssh_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "digitalocean_ssh_key" "main" {
  name       = "luxcrafts-${var.environment}-${random_id.key_suffix.hex}"
  public_key = tls_private_key.ssh_key.public_key_openssh
}

resource "random_id" "key_suffix" {
  byte_length = 4
}

# VPC Network
resource "digitalocean_vpc" "main" {
  name     = "luxcrafts-${var.environment}-vpc"
  region   = var.region
  ip_range = "*********/16"
  
  description = "VPC for Luxcrafts ${var.environment} environment"
}

# Firewall
resource "digitalocean_firewall" "web" {
  name = "luxcrafts-${var.environment}-firewall"

  droplet_ids = [digitalocean_droplet.web.id]

  # SSH access (restricted to admin IPs)
  inbound_rule {
    protocol         = "tcp"
    port_range       = "22"
    source_addresses = ["0.0.0.0/0", "::/0"]  # Restrict this in production
  }

  # HTTP
  inbound_rule {
    protocol         = "tcp"
    port_range       = "80"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  # HTTPS
  inbound_rule {
    protocol         = "tcp"
    port_range       = "443"
    source_addresses = ["0.0.0.0/0", "::/0"]
  }

  # Monitoring (Prometheus)
  inbound_rule {
    protocol         = "tcp"
    port_range       = "9090"
    source_addresses = [digitalocean_vpc.main.ip_range]
  }

  # Grafana
  inbound_rule {
    protocol         = "tcp"
    port_range       = "3000"
    source_addresses = [digitalocean_vpc.main.ip_range]
  }

  # Outbound rules
  outbound_rule {
    protocol              = "tcp"
    port_range            = "1-65535"
    destination_addresses = ["0.0.0.0/0", "::/0"]
  }

  outbound_rule {
    protocol              = "udp"
    port_range            = "1-65535"
    destination_addresses = ["0.0.0.0/0", "::/0"]
  }

  outbound_rule {
    protocol              = "icmp"
    destination_addresses = ["0.0.0.0/0", "::/0"]
  }
}

# Load Balancer
resource "digitalocean_loadbalancer" "web" {
  count = var.environment == "production" ? 1 : 0
  
  name   = "luxcrafts-${var.environment}-lb"
  region = var.region
  vpc_uuid = digitalocean_vpc.main.id

  forwarding_rule {
    entry_protocol  = "http"
    entry_port      = 80
    target_protocol = "http"
    target_port     = 80
    certificate_name = ""
    tls_passthrough = false
  }

  forwarding_rule {
    entry_protocol  = "https"
    entry_port      = 443
    target_protocol = "https"
    target_port     = 443
    certificate_name = digitalocean_certificate.cert[0].name
    tls_passthrough = false
  }

  healthcheck {
    protocol               = "http"
    port                   = 80
    path                   = "/health.json"
    check_interval_seconds = 10
    response_timeout_seconds = 5
    unhealthy_threshold    = 3
    healthy_threshold      = 2
  }

  droplet_ids = [digitalocean_droplet.web.id]
  
  redirect_http_to_https = true
  enable_proxy_protocol = false
}

# SSL Certificate
resource "digitalocean_certificate" "cert" {
  count = var.environment == "production" ? 1 : 0
  
  name    = "luxcrafts-${var.environment}-cert"
  type    = "lets_encrypt"
  domains = [
    var.domain_name,
    "www.${var.domain_name}",
    "api.${var.domain_name}"
  ]
  
  lifecycle {
    create_before_destroy = true
  }
}

# Main Web Server Droplet
resource "digitalocean_droplet" "web" {
  image    = "ubuntu-22-04-x64"
  name     = "luxcrafts-${var.environment}-web"
  region   = var.region
  size     = var.droplet_size
  vpc_uuid = digitalocean_vpc.main.id
  
  ssh_keys = [digitalocean_ssh_key.main.id]
  
  user_data = templatefile("${path.module}/cloud-init.yml", {
    admin_email = var.admin_email
    domain_name = var.domain_name
    environment = var.environment
  })
  
  tags = [
    "luxcrafts",
    var.environment,
    "web-server",
    "dokploy"
  ]
  
  monitoring = true
  backups    = true
  
  lifecycle {
    create_before_destroy = true
  }
}

# Database Droplet (Production only)
resource "digitalocean_droplet" "database" {
  count = var.environment == "production" ? 1 : 0
  
  image    = "ubuntu-22-04-x64"
  name     = "luxcrafts-${var.environment}-db"
  region   = var.region
  size     = "s-2vcpu-4gb"
  vpc_uuid = digitalocean_vpc.main.id
  
  ssh_keys = [digitalocean_ssh_key.main.id]
  
  user_data = templatefile("${path.module}/database-init.yml", {
    admin_email = var.admin_email
  })
  
  tags = [
    "luxcrafts",
    var.environment,
    "database",
    "postgresql"
  ]
  
  monitoring = true
  backups    = true
}

# Monitoring Droplet
resource "digitalocean_droplet" "monitoring" {
  count = var.environment == "production" ? 1 : 0
  
  image    = "ubuntu-22-04-x64"
  name     = "luxcrafts-${var.environment}-monitoring"
  region   = var.region
  size     = "s-2vcpu-2gb"
  vpc_uuid = digitalocean_vpc.main.id
  
  ssh_keys = [digitalocean_ssh_key.main.id]
  
  user_data = templatefile("${path.module}/monitoring-init.yml", {
    admin_email = var.admin_email
  })
  
  tags = [
    "luxcrafts",
    var.environment,
    "monitoring",
    "prometheus",
    "grafana"
  ]
  
  monitoring = true
}

# Volume for backups
resource "digitalocean_volume" "backup" {
  region                  = var.region
  name                    = "luxcrafts-${var.environment}-backup"
  size                    = 100
  initial_filesystem_type = "ext4"
  description             = "Backup volume for Luxcrafts ${var.environment}"
  
  tags = [
    "luxcrafts",
    var.environment,
    "backup"
  ]
}

resource "digitalocean_volume_attachment" "backup" {
  droplet_id = digitalocean_droplet.web.id
  volume_id  = digitalocean_volume.backup.id
}

# Spaces bucket for static assets
resource "digitalocean_spaces_bucket" "assets" {
  name   = "luxcrafts-${var.environment}-assets"
  region = "nyc3"
  
  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "HEAD"]
    allowed_origins = [
      "https://${var.domain_name}",
      "https://www.${var.domain_name}"
    ]
    max_age_seconds = 3000
  }
  
  lifecycle_rule {
    id      = "cleanup"
    enabled = true
    
    expiration {
      days = 365
    }
    
    noncurrent_version_expiration {
      days = 30
    }
  }
}

# CDN for static assets
resource "digitalocean_cdn" "assets" {
  origin         = digitalocean_spaces_bucket.assets.bucket_domain_name
  custom_domain  = "cdn.${var.domain_name}"
  certificate_name = digitalocean_certificate.cert[0].name
  
  ttl = 3600
}

# DNS Records
resource "cloudflare_record" "root" {
  zone_id = data.cloudflare_zone.main.id
  name    = "@"
  value   = var.environment == "production" ? digitalocean_loadbalancer.web[0].ip : digitalocean_droplet.web.ipv4_address
  type    = "A"
  ttl     = 300
  proxied = true
}

resource "cloudflare_record" "www" {
  zone_id = data.cloudflare_zone.main.id
  name    = "www"
  value   = var.domain_name
  type    = "CNAME"
  ttl     = 300
  proxied = true
}

resource "cloudflare_record" "staging" {
  count   = var.environment == "staging" ? 1 : 0
  zone_id = data.cloudflare_zone.main.id
  name    = "staging"
  value   = digitalocean_droplet.web.ipv4_address
  type    = "A"
  ttl     = 300
  proxied = true
}

resource "cloudflare_record" "api" {
  zone_id = data.cloudflare_zone.main.id
  name    = "api"
  value   = var.environment == "production" ? digitalocean_loadbalancer.web[0].ip : digitalocean_droplet.web.ipv4_address
  type    = "A"
  ttl     = 300
  proxied = false
}

resource "cloudflare_record" "cdn" {
  zone_id = data.cloudflare_zone.main.id
  name    = "cdn"
  value   = digitalocean_cdn.assets.endpoint
  type    = "CNAME"
  ttl     = 300
  proxied = false
}

# Cloudflare Page Rules for security
resource "cloudflare_page_rule" "security" {
  zone_id  = data.cloudflare_zone.main.id
  target   = "${var.domain_name}/*"
  priority = 1
  
  actions {
    security_level = "high"
    ssl = "strict"
    always_use_https = true
    browser_integrity_check = "on"
    
    cache_level = "aggressive"
    edge_cache_ttl = 7200
  }
}

# WAF Rules
resource "cloudflare_filter" "block_threats" {
  zone_id     = data.cloudflare_zone.main.id
  description = "Block known threats"
  expression  = "(cf.threat_score gt 14)"
}

resource "cloudflare_firewall_rule" "block_threats" {
  zone_id     = data.cloudflare_zone.main.id
  description = "Block high threat score requests"
  filter_id   = cloudflare_filter.block_threats.id
  action      = "block"
  priority    = 1
}

# Rate limiting
resource "cloudflare_rate_limit" "api" {
  zone_id   = data.cloudflare_zone.main.id
  threshold = 100
  period    = 60
  
  match {
    request {
      url_pattern = "${var.domain_name}/api/*"
      schemes     = ["HTTPS"]
      methods     = ["GET", "POST", "PUT", "DELETE"]
    }
  }
  
  action {
    mode    = "simulate"
    timeout = 86400
  }
}

# Outputs
output "web_server_ip" {
  description = "IP address of the web server"
  value       = digitalocean_droplet.web.ipv4_address
}

output "load_balancer_ip" {
  description = "IP address of the load balancer"
  value       = var.environment == "production" ? digitalocean_loadbalancer.web[0].ip : null
}

output "database_ip" {
  description = "IP address of the database server"
  value       = var.environment == "production" ? digitalocean_droplet.database[0].ipv4_address_private : null
  sensitive   = true
}

output "monitoring_ip" {
  description = "IP address of the monitoring server"
  value       = var.environment == "production" ? digitalocean_droplet.monitoring[0].ipv4_address_private : null
  sensitive   = true
}

output "ssh_private_key" {
  description = "SSH private key for server access"
  value       = tls_private_key.ssh_key.private_key_pem
  sensitive   = true
}

output "cdn_endpoint" {
  description = "CDN endpoint for static assets"
  value       = digitalocean_cdn.assets.endpoint
}

output "spaces_bucket" {
  description = "Spaces bucket name for assets"
  value       = digitalocean_spaces_bucket.assets.name
}

output "domain_name" {
  description = "Primary domain name"
  value       = var.domain_name
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}