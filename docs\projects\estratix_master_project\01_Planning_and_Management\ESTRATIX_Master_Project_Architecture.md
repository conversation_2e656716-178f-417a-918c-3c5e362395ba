---
**Document Control**

*   **Project ID:** ESTRATIX_MASTER
*   **Version:** 0.1.0
*   **Status:** Draft
*   **Security Classification:** Level 2: Internal
*   **Author:** Cascade
*   **Reviewed By:** [Name/Team]
*   **Approved By:** [Name/Team]
*   **Creation Date:** 2025-06-13
*   **Last Updated:** 2025-06-13
---

# ESTRATIX Master Project Architecture

## Table of Contents

- [1. Overview](#1-overview)
  - [1.1. Purpose](#11-purpose)
  - [1.2. Scope](#12-scope)
- [2. Architectural Principles](#2-architectural-principles)
- [3. High-Level System Structure](#3-high-level-system-structure)
- [4. Core Subprojects & Components](#4-core-subprojects--components)
- [5. Project Management Architecture (PM-EMP-001)](#5-project-management-architecture-pm-emp-001)
- [6. Technology Stack](#6-technology-stack)
- [7. Data Management Strategy](#7-data-management-strategy)
- [8. Guidance for Use](#8-guidance-for-use)

---

## 1. Overview

### 1.1. Purpose

This document defines the master architecture for the ESTRATIX project. It serves as the canonical source of truth for the system's structure, components, integration patterns, and technology stack. Its primary goal is to provide a unified architectural vision to guide the development of all subprojects and ensure they integrate seamlessly into a cohesive, autonomous enterprise ecosystem.

### 1.2. Scope

This architecture covers the entire ESTRATIX system, including:

- The core Domain, Application, and Infrastructure layers.
- The definition of all major subprojects and their responsibilities.
- The primary data flows and integration points between components.
- The foundational technology stack and operational platforms.

---

## 2. Architectural Principles

The ESTRATIX architecture is built upon the following core principles:

- **Domain-Driven Design (DDD)**: The system is modeled around the core business domains of an autonomous, agent-driven enterprise.
- **Hexagonal Architecture (Ports & Adapters)**: The core application logic is decoupled from external concerns (UI, databases, APIs), promoting modularity and testability.
- **Agent-First Design**: The system is designed to be operated and extended by autonomous agents, with APIs and workflows tailored for agentic interaction.
- **Scalability & Resilience**: The architecture is designed to scale horizontally and gracefully handle failures.

---

## 3. High-Level System Structure

The ESTRATIX system is designed using a layered, hexagonal architecture that separates concerns and promotes modularity. The diagram below illustrates the four primary layers:

- **User & Agent Interfaces**: The entry point for all interactions, whether from human users via a Web UI/CLI or from autonomous agents through dedicated agentic UIs.
- **Application Layer**: Contains the application-specific logic, including the API Gateway that routes requests, the autonomous workflows that orchestrate business processes (managed by the CPO), and generative services for creating assets.
- **Domain Layer**: The core of the system, containing the pure business logic, domain entities (Agents, Tasks, Projects), and formally defined data models. This layer is independent of any external technology.
- **Infrastructure Layer**: Provides the technical capabilities and external systems that support the application, including the knowledge base, state database, LLM providers, and the compute/deployment platform.

```mermaid
graph TD
    subgraph User & Agent Interfaces
        A[Web UI / CLI]
        B[Agentic UIs]
    end

    subgraph Application Layer
        C[API Gateway]
        D[Autonomous Workflows (CPO)]
        E[Generative Services (Web, Content)]
    end

    subgraph Domain Layer
        F[Core Business Logic]
        G[Agents, Tasks, Projects]
        H[Data Models]
    end

    subgraph Infrastructure Layer
        I[Knowledge Base (Milvus)]
        J[State Database (MongoDB)]
        K[LLM Providers]
        L[Compute & Deployment (Kubernetes)]
    end

    A & B --> C
    C --> D
    C --> E
    D & E --> F
    F & G & H -- Interacts with --> I & J & K
    D & E & F & G & H -- Deployed on --> L
```

---

## 4. Core Subprojects & Components

The ESTRATIX Master Project is composed of several key subprojects, each with a distinct responsibility:

| Subproject / Component | Description | Key Tasks |
|---|---|---|
| **Autonomous Operations** | The central nervous system of ESTRATIX. This subproject orchestrates high-level business processes, manages project execution via agentic crews, and ensures alignment with strategic objectives defined by the Command Offices. | `WF-AUTONOMOUS-OPS` |
| **Knowledge Ingestion & Management (KIM)** | Responsible for building and maintaining the enterprise knowledge base. It includes workflows and services for ingesting, parsing, embedding, and indexing documentation, research papers, and other unstructured data to make it accessible to all agents. | `STRAT-DOCING-01`, `KIM-WEB-SVC` |
| **Generative Services** | A suite of services that leverage agentic workflows to produce tangible outputs. This includes generating complete websites, creating marketing content, and producing other digital assets based on high-level requirements. | `STRAT-WEBGEN-01`, `DATA-CONTENT-ART` |
| **Agentic Ecosystem** | Focuses on the development and management of the core agentic components. This includes creating standardized agent definitions, building reusable tools, and maintaining the underlying agentic frameworks (e.g., CrewAI, Pydantic-AI). | `IMP-AGENT-OPS`, `R&D-AGENT-UI` |
| **Sales Intelligence** | An AI-driven subproject dedicated to sales and business development. It provides capabilities for sales forecasting, automated lead generation, and conversational AI analysis to optimize sales strategies and execution. | `STRAT-SALESRL-01` |
| **Core Infrastructure** | Manages the foundational technology stack as Infrastructure as Code (IaC). This includes provisioning and maintaining Kubernetes clusters, managing databases (MongoDB, Milvus), and ensuring the CI/CD pipelines are robust and efficient. | `INFRA-K8S`, `DATA-MONGO-INT` |

---

## 5. Project Management Architecture (PM-EMP-001)

This section formalizes the ESTRATIX project management architecture, ensuring consistency, traceability, and alignment with our autonomous operational goals for both internal and client-facing projects.

### 5.1. Dual Project Structure

ESTRATIX operates on a dual-track project structure to clearly separate internal development from client deliverables:

- **Internal Master Project (`/docs/projects/`):** This directory houses the ESTRATIX Master Project and all its subprojects. It is the canonical source for the agency's own development, operational workflows, and core capabilities. The bootstrapping and management of this structure are governed by the `/.windsurf/workflows/0_master_orchestration/bootstrap_estratix_project.md` workflow.

- **Client Projects (`/clients/`):** Each client has a dedicated directory within `/clients/`. This ensures strict isolation and security for client-specific data, code, and project artifacts. The entire lifecycle of a client project, from onboarding to final delivery, is orchestrated by the `/.windsurf/workflows/0_master_orchestration/bootstrap_client_project.md` workflow.

### 5.2. Orchestration via Master Workflows

The project management model is driven by master orchestration workflows that codify our best practices and ensure repeatable success:

- **`bootstrap_estratix_project.md`**: This workflow is responsible for generating the entire scaffolding for a new internal ESTRATIX subproject. It creates the necessary documentation, planning artifacts, and directory structures within `/docs/projects/`, linking it to the master task list and relevant matrices.

- **`bootstrap_client_project.md`**: This workflow manages the end-to-end client engagement process. It starts with the Request for Proposal (RFP), moves to proposal generation, and upon approval, bootstraps the client's dedicated project space in `/clients/[ClientID]/`. It populates the space with tailored project plans, requirements documents, and a starter architecture based on relevant productized services.

This bifurcated approach allows ESTRATIX to develop its core platform robustly while delivering customized, secure, and well-documented solutions to its clients.

---

## 6. Technology Stack

- **Backend**: Python, FastAPI
- **Frontend**: HTMX, Bootstrap, JavaScript
- **Agent Frameworks**: CrewAI, Pydantic-AI, Google ADK, Pocketflow, OpenAI Agents
- **Databases**: MongoDB (Primary State), Milvus (Vector Store), QdrantDB
- **Infrastructure**: Kubernetes, Docker
- **LLM Providers**: OpenAI, Anthropic, Google Gemini, and others via a multi-provider framework.

---

## 7. Data Management Strategy

- **State Persistence**: Core application state, including projects, tasks, and user data, will be stored in MongoDB.
- **Knowledge Persistence**: Vector embeddings and unstructured knowledge will be stored and indexed in a Milvus vector database for efficient retrieval.
- **Data Models**: All data structures are formally defined using Pydantic models, as cataloged in the `data_model_matrix.md`.

---

## 8. Guidance for Use

- **Living Document:** This architecture is not static. It is expected to evolve as the ESTRATIX project progresses and new requirements emerge. All changes must be managed through a formal review and approval process.
- **Traceability:** All components, services, and workflows defined herein should be traceable back to tasks and requirements in the `ESTRATIX_Master_Task_List.md`.
- **Subproject Alignment:** All subproject architectures must align with the principles and structures defined in this master document. Any deviation requires explicit approval from the CTO office.

---

ESTRATIX - Engineering the Future of Autonomous Operations - © 2025
