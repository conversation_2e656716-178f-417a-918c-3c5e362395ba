{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitThis": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "moduleResolution": "node", "baseUrl": "./src", "paths": {"@/*": ["*"], "@/config/*": ["config/*"], "@/services/*": ["services/*"], "@/routes/*": ["routes/*"], "@/middleware/*": ["middleware/*"], "@/utils/*": ["utils/*"], "@/types/*": ["types/*"]}, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "typeRoots": ["node_modules/@types", "src/types"]}, "include": ["src/**/*", "contracts/**/*", "scripts/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "artifacts", "cache"]}