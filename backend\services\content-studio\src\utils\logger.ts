import pino from 'pino';
import { config } from '../config/environment';

// Create logger instance with configuration
export const logger = pino({
  level: config.logging.level,
  formatters: {
    level: (label) => {
      return { level: label.toUpperCase() };
    },
  },
  timestamp: pino.stdTimeFunctions.isoTime,
  ...(config.nodeEnv === 'development' && {
    transport: {
      target: 'pino-pretty',
      options: {
        colorize: true,
        translateTime: 'SYS:standard',
        ignore: 'pid,hostname',
      },
    },
  }),
});

// Create child loggers for different modules
export const createModuleLogger = (module: string) => {
  return logger.child({ module });
};

// Export specific loggers for common modules
export const aiLogger = createModuleLogger('ai-service');
export const queueLogger = createModuleLogger('queue');
export const vectorLogger = createModuleLogger('vector-store');
export const authLogger = createModuleLogger('auth');
export const apiLogger = createModuleLogger('api');

// Log levels: trace, debug, info, warn, error, fatal
export default logger;