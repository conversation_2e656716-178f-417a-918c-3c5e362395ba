# GitOps Deployment Configuration for Sorteo Estelar
# This file defines the complete deployment strategy and automation workflows

apiVersion: v1
kind: ConfigMap
metadata:
  name: sorteo-estelar-gitops-config
  namespace: default
data:
  # Environment Configuration
  environments.yaml: |
    environments:
      development:
        branch: develop
        domain: dev.sorteoestelar.com
        replicas: 1
        resources:
          cpu: "0.5"
          memory: "512Mi"
        database:
          size: "5Gi"
        monitoring: true
        
      staging:
        branch: staging
        domain: staging.sorteoestelar.com
        replicas: 2
        resources:
          cpu: "1"
          memory: "1Gi"
        database:
          size: "10Gi"
        monitoring: true
        
      production:
        branch: main
        domain: www.sorteoestelar.com
        replicas: 3
        resources:
          cpu: "2"
          memory: "2Gi"
        database:
          size: "50Gi"
        monitoring: true
        backup: true
        
  # Deployment Strategy
  deployment-strategy.yaml: |
    strategy:
      type: RollingUpdate
      rollingUpdate:
        maxUnavailable: 1
        maxSurge: 1
      
    healthChecks:
      readinessProbe:
        httpGet:
          path: /health
          port: 80
        initialDelaySeconds: 30
        periodSeconds: 10
        timeoutSeconds: 5
        failureThreshold: 3
        
      livenessProbe:
        httpGet:
          path: /health
          port: 80
        initialDelaySeconds: 60
        periodSeconds: 30
        timeoutSeconds: 10
        failureThreshold: 3
        
    autoscaling:
      enabled: true
      minReplicas: 2
      maxReplicas: 10
      targetCPUUtilizationPercentage: 70
      targetMemoryUtilizationPercentage: 80
      
  # Git Worktree Configuration
  git-worktree.yaml: |
    worktree:
      basePath: "/opt/sorteo-estelar/worktrees"
      branches:
        - main
        - develop
        - staging
        
      featureBranches:
        prefix: "feature/"
        maxAge: "7d"
        autoCleanup: true
        
      hotfixBranches:
        prefix: "hotfix/"
        maxAge: "3d"
        autoCleanup: true
        
      releaseBranches:
        prefix: "release/"
        maxAge: "30d"
        autoCleanup: false
        
    automation:
      syncInterval: "5m"
      cleanupInterval: "1h"
      notifications:
        slack: true
        email: true
        
  # CI/CD Pipeline Configuration
  pipeline.yaml: |
    pipeline:
      stages:
        - name: "validate"
          steps:
            - lint
            - typecheck
            - security-scan
            - dependency-audit
            
        - name: "test"
          steps:
            - unit-tests
            - integration-tests
            - e2e-tests
            - performance-tests
            
        - name: "build"
          steps:
            - docker-build
            - vulnerability-scan
            - image-signing
            
        - name: "deploy"
          steps:
            - deploy-staging
            - smoke-tests
            - deploy-production
            - post-deployment-tests
            
      triggers:
        push:
          branches: ["main", "develop", "staging"]
        pullRequest:
          branches: ["main", "develop"]
        schedule:
          - cron: "0 2 * * *"
            branch: "main"
            
      notifications:
        success:
          - slack
          - email
        failure:
          - slack
          - email
          - pagerduty
          
  # Monitoring Configuration
  monitoring.yaml: |
    monitoring:
      prometheus:
        enabled: true
        retention: "30d"
        scrapeInterval: "15s"
        
      grafana:
        enabled: true
        dashboards:
          - application-metrics
          - infrastructure-metrics
          - business-metrics
          
      alerting:
        rules:
          - name: "high-cpu-usage"
            condition: "cpu > 80%"
            duration: "5m"
            severity: "warning"
            
          - name: "high-memory-usage"
            condition: "memory > 85%"
            duration: "5m"
            severity: "warning"
            
          - name: "application-down"
            condition: "up == 0"
            duration: "1m"
            severity: "critical"
            
          - name: "high-error-rate"
            condition: "error_rate > 5%"
            duration: "2m"
            severity: "warning"
            
      logging:
        enabled: true
        retention: "7d"
        level: "info"
        
  # Security Configuration
  security.yaml: |
    security:
      ssl:
        enabled: true
        provider: "letsencrypt"
        autoRenewal: true
        
      secrets:
        encryption: "aes-256"
        rotation: "90d"
        
      networkPolicies:
        enabled: true
        defaultDeny: true
        
      rbac:
        enabled: true
        
      podSecurityPolicy:
        enabled: true
        
      vulnerabilityScanning:
        enabled: true
        schedule: "daily"
        
  # Backup Configuration
  backup.yaml: |
    backup:
      database:
        enabled: true
        schedule: "0 2 * * *"
        retention: "30d"
        encryption: true
        
      volumes:
        enabled: true
        schedule: "0 3 * * *"
        retention: "7d"
        
      configuration:
        enabled: true
        schedule: "0 1 * * *"
        retention: "90d"
        
---
apiVersion: v1
kind: Secret
metadata:
  name: sorteo-estelar-secrets
  namespace: default
type: Opaque
data:
  # Base64 encoded secrets (to be replaced with actual values)
  database-password: "c29ydGVvX3NlY3VyZV9wYXNzd29yZF8yMDI0"
  jwt-secret: "c29ydGVvX2p3dF9zZWNyZXRfa2V5XzIwMjQ="
  api-key: "c29ydGVvX2FwaV9rZXlfMjAyNA=="
  
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: sorteo-estelar-frontend
  namespace: default
  labels:
    app: sorteo-estelar
    component: frontend
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: sorteo-estelar
      component: frontend
  template:
    metadata:
      labels:
        app: sorteo-estelar
        component: frontend
        version: v1
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "80"
        prometheus.io/path: "/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      containers:
      - name: frontend
        image: ghcr.io/estratix/sorteo-estelar:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 80
          name: http
        env:
        - name: NODE_ENV
          value: "production"
        - name: VITE_API_URL
          value: "https://api.sorteoestelar.com"
        - name: VITE_WS_URL
          value: "wss://api.sorteoestelar.com"
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "500m"
            memory: "512Mi"
        readinessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        livenessProbe:
          httpGet:
            path: /health
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: tmp
          mountPath: /tmp
        - name: nginx-cache
          mountPath: /var/cache/nginx
        - name: nginx-run
          mountPath: /var/run
      volumes:
      - name: tmp
        emptyDir: {}
      - name: nginx-cache
        emptyDir: {}
      - name: nginx-run
        emptyDir: {}
      imagePullSecrets:
      - name: ghcr-secret
      
---
apiVersion: v1
kind: Service
metadata:
  name: sorteo-estelar-frontend-service
  namespace: default
  labels:
    app: sorteo-estelar
    component: frontend
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: sorteo-estelar
    component: frontend
    
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sorteo-estelar-ingress
  namespace: default
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - www.sorteoestelar.com
    - staging.sorteoestelar.com
    - dev.sorteoestelar.com
    secretName: sorteo-estelar-tls
  rules:
  - host: www.sorteoestelar.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sorteo-estelar-frontend-service
            port:
              number: 80
  - host: staging.sorteoestelar.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sorteo-estelar-frontend-service
            port:
              number: 80
  - host: dev.sorteoestelar.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: sorteo-estelar-frontend-service
            port:
              number: 80
              
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: sorteo-estelar-hpa
  namespace: default
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: sorteo-estelar-frontend
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
        
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: default
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        
        # Gzip
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types
            text/plain
            text/css
            text/xml
            text/javascript
            application/json
            application/javascript
            application/xml+rss
            application/atom+xml
            image/svg+xml;
        
        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:;" always;
        
        server {
            listen 80;
            server_name _;
            root /usr/share/nginx/html;
            index index.html;
            
            # Health check endpoint
            location /health {
                access_log off;
                return 200 "healthy\n";
                add_header Content-Type text/plain;
            }
            
            # Metrics endpoint for Prometheus
            location /metrics {
                access_log off;
                return 200 "# HELP nginx_up Nginx status\n# TYPE nginx_up gauge\nnginx_up 1\n";
                add_header Content-Type text/plain;
            }
            
            # Main application
            location / {
                try_files $uri $uri/ /index.html;
                
                # Cache static assets
                location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                    expires 1y;
                    add_header Cache-Control "public, immutable";
                }
            }
            
            # Security
            location ~ /\. {
                deny all;
            }
        }
    }