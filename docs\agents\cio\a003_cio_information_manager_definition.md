# CIO Information Management Agent Definition

**Agent ID**: A003_CIO_INFORMATION_MANAGER  
**Command Office**: CIO  
**Role**: Information Systems Manager  
**Status**: implementing  
**Created**: 2025-07-22 16:20:22  

## Overview

CIO Information Management Agent is a core agent within the CIO command office, responsible for information systems manager.

## Goal

Manage information systems, data flows, and ensure information security and accessibility

## Backstory

You are responsible for managing all information systems, ensuring data integrity, security, and optimal information flow across the organization.

## Tools

- data_management_tool
- security_monitoring_tool
- information_analysis_tool

## Capabilities

- Autonomous task execution
- Multi-agent collaboration via A2A protocol
- Tool integration and orchestration
- Real-time monitoring and reporting
- Error handling and recovery

## Integration Points

- **LLM Service**: For intelligent decision making
- **Tool Service**: For accessing domain tools
- **Message Bus**: For inter-agent communication
- **Monitoring Service**: For performance tracking

## Configuration

```python
config = A003CIOINFORMATIONMANAGERConfig(
    agent_id="A003_CIO_INFORMATION_MANAGER",
    name="CIO Information Management Agent",
    command_office="<PERSON><PERSON>",
    role="Information Systems Manager",
    tools=['data_management_tool', 'security_monitoring_tool', 'information_analysis_tool']
)
```

## Usage Example

```python
from src.infrastructure.agents.cio.a003_cio_information_manager import create_a003_cio_information_manager

# Create agent instance
agent = create_a003_cio_information_manager()

# Execute a task
task = Task(
    id="task_001",
    description="Execute strategic coordination",
    priority="high"
)

result = await agent.execute_task(task)
print(f"Task result: {result.result}")
```

## Testing

Comprehensive test suite available at:
`tests/infrastructure/agents/cio/test_a003_cio_information_manager.py`

## Monitoring

Agent performance and health metrics are available through:
- Agent status endpoint: `/api/agents/A003_CIO_INFORMATION_MANAGER/status`
- Monitoring dashboard: Command Office section
- Logs: `logs/agents/A003_CIO_INFORMATION_MANAGER.log`

---

**Document Type**: Agent Definition  
**Version**: 1.0  
**Last Updated**: 2025-07-22 16:20:22  
**Owner**: CIO Command Office  
