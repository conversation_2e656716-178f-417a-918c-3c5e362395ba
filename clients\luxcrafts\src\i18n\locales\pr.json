{"common": {"loading": "Carregando...", "error": "Erro", "success": "Sucesso", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "save": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "sort": "Ordenar", "close": "<PERSON><PERSON><PERSON>", "open": "Abrir", "view": "Visualizar", "download": "Baixar", "upload": "Enviar", "connect": "Conectar", "disconnect": "Desconectar", "wallet": "<PERSON><PERSON><PERSON>", "balance": "<PERSON><PERSON>", "transaction": "Transação", "transactions": "Transações", "address": "Endereço", "amount": "Quantia", "fee": "Taxa", "total": "Total", "status": "Status", "pending": "Pendente", "completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "failed": "Fal<PERSON>", "approved": "<PERSON><PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON><PERSON>", "date": "Data", "time": "<PERSON><PERSON>", "name": "Nome", "email": "E-mail", "phone": "Telefone", "location": "Localização", "price": "Preço", "rating": "Avaliação", "reviews": "Avaliações", "availability": "Disponibilidade", "book": "<PERSON><PERSON><PERSON>", "booked": "Reservado", "booking": "Reserva", "bookings": "<PERSON><PERSON><PERSON>", "service": "Serviço", "services": "Serviços", "provider": "<PERSON><PERSON><PERSON>", "providers": "Provedores", "property": "<PERSON><PERSON><PERSON><PERSON>", "properties": "<PERSON><PERSON><PERSON><PERSON>", "dashboard": "<PERSON><PERSON>", "profile": "Perfil", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>", "support": "Suporte", "contact": "Contato", "about": "Sobre", "terms": "Termos de Serviço", "privacy": "Política de Privacidade", "logout": "<PERSON><PERSON>", "login": "Entrar", "register": "Registrar", "signup": "Cadastrar-se", "signin": "<PERSON><PERSON><PERSON>"}, "navigation": {"home": "Início", "marketplace": "<PERSON><PERSON><PERSON>", "services": "Serviços", "dashboard": "<PERSON><PERSON>", "tokens": "Tokens", "nft": "NFT", "defi": "<PERSON><PERSON><PERSON>", "admin": "Admin", "provider": "Portal do Provedor", "content": "Estúdio de Conteúdo", "tokenization": "Tokenização de Ativos", "analytics": "<PERSON><PERSON><PERSON><PERSON>"}, "home": {"hero": {"title": "Serviços de Propriedades de Luxo", "subtitle": "Impulsionado por Web3", "description": "Plataforma revolucionária conectando proprietários com provedores de serviços premium através de tecnologia blockchain, correspondência alimentada por IA e recompensas tokenizadas.", "cta_primary": "Reserve Serviços Agora", "cta_secondary": "<PERSON>plora<PERSON>"}, "stats": {"properties_managed": "<PERSON><PERSON><PERSON><PERSON>", "service_providers": "Provedores de Serviços", "lux_tokens_staked": "Tokens LUX em Staking", "customer_satisfaction": "Satisfação do Cliente"}, "services": {"title": "Serviços Premium para Propriedades", "description": "Descubra nossa suíte abrangente de serviços de propriedades de luxo, cada um alimentado por tecnologia de ponta e entregue por profissionais certificados.", "property_management": {"title": "Gestão de Propriedades", "description": "Supervisão abrangente de propriedades com agendamento de manutenção alimentado por IA"}, "luxury_cleaning": {"title": "Limpeza de Luxo", "description": "Serviços de limpeza premium com produtos ecológicos e tratamento de luva branca"}, "landscaping": {"title": "Paisagismo e Design", "description": "Paisagismo profissional com design sustentável e irrigação inteligente"}, "remediation": {"title": "Remediação de Propriedades", "description": "Serviços especializados de restauração para danos por água, mofo e problemas estruturais"}, "analytics": {"title": "Análises de Investimento", "description": "Avaliação de propriedades alimentada por IA e análise de oportunidades de investimento"}, "crypto_payments": {"title": "Pagamento<PERSON>", "description": "Pagamentos Web3 sem problemas com recompensas de tokens LUX e certificados NFT"}, "learn_more": "<PERSON><PERSON> Mai<PERSON>"}, "web3": {"title": "Ecossistema de Propriedades Alimentado por Web3", "description": "Experimente o futuro dos serviços de propriedades com tecnologia blockchain, recompensas tokenizadas e integração de finanças descentralizadas.", "lux_rewards": {"title": "Recompensas de Tokens LUX", "description": "Ganhe tokens LUX para cada reserva de serviço, melhoria de propriedade e participação na plataforma. Faça staking de tokens para benefícios premium."}, "tokenization": {"title": "Tokenização de Propriedades", "description": "Tokenize ativos de propriedades para propriedade fracionária, distribuição transparente de renda de aluguel e investimento imobiliário líquido."}, "nft_certificates": {"title": "Certificados NFT", "description": "Receba certificados NFT verificáveis para melhorias de propriedades, conclusões de serviços e níveis de associação."}, "dashboard_preview": {"title": "Prévia do Painel de Tokens", "lux_balance": "Saldo LUX", "staked_amount": "Quantia em Staking", "rewards_earned": "Recompensas <PERSON>", "nfts_owned": "NFTs Possuídos", "access_dashboard": "Acessa<PERSON>"}}, "cta": {"title": "Pronto para Transformar sua Experiência Imobiliária?", "description": "Junte-se a milhares de proprietários que confiam na Luxcrafts para serviços premium e recompensas Web3 inovadoras.", "start_booking": "Começar a Reservar Ser<PERSON>os", "become_provider": "<PERSON><PERSON><PERSON><PERSON> <PERSON> Provedor"}}, "payment": {"web2": {"title": "Sistema Bancário Tradicional", "description": "Pague com cartões de crédito, transferências bancárias e pagamentos ACH", "available_in": "Disponível nos Estados Unidos", "methods": {"credit_card": "Cartão de Crédito", "bank_transfer": "Transferência Bancária", "ach": "Pagamento ACH", "paypal": "PayPal"}}, "web3": {"title": "Pagamentos com Criptomoedas", "description": "Pague com Bitcoin, Ethereum, stablecoins e tokens LUX", "available_globally": "Disponível Globalmente", "methods": {"bitcoin": "Bitcoin", "ethereum": "Ethereum", "usdc": "USDC", "usdt": "USDT", "lux_token": "Token LUX"}}, "toggle": {"web2": "Sistema Bancário Tradicional", "web3": "Pagamento<PERSON>", "switch_to_web2": "Mudar para Sistema Bancário Tradicional", "switch_to_web3": "Mudar para Pagamentos Cripto"}}, "accessibility": {"skip_to_content": "Pular para o conteúdo principal", "menu": "<PERSON><PERSON>", "close_menu": "Fechar menu", "open_menu": "Abrir menu", "language_selector": "Se<PERSON>or de idioma", "current_language": "Idioma atual", "change_language": "Alterar idioma", "high_contrast": "<PERSON>do de alto contraste", "normal_contrast": "Modo de contraste normal", "increase_font_size": "Aumentar tamanho da fonte", "decrease_font_size": "<PERSON><PERSON><PERSON><PERSON> ta<PERSON><PERSON> da <PERSON>e", "reset_font_size": "<PERSON>ef<PERSON><PERSON> ta<PERSON> da <PERSON>e", "screen_reader_content": "Conteúdo para leitor de tela", "keyboard_navigation": "Navegação por teclado disponível", "alternative_text": "Texto alternativo", "loading_content": "<PERSON><PERSON><PERSON> con<PERSON>, por favor aguarde", "error_occurred": "Ocorreu um erro", "success_message": "Ação concluída com sucesso", "required_field": "Campo obrigatório", "invalid_input": "Entrada inválida", "form_validation_error": "Por favor, corrija os erros no formulário", "page_navigation": "Navegação da página", "breadcrumb_navigation": "Navegação breadcrumb", "main_navigation": "Navegação principal", "footer_navigation": "Navegação do rodapé", "search_results": "Resultados da pesquisa", "no_results_found": "Nenhum resultado encontrado", "results_count": "{{count}} resultados encontrados", "page_of_total": "Página {{current}} de {{total}}", "sort_by": "Ordenar por", "filter_by": "Filtrar por", "clear_filters": "Limpar todos os filtros", "apply_filters": "Aplicar filtros", "expand_section": "Expandir seção", "collapse_section": "<PERSON><PERSON><PERSON><PERSON>", "show_more": "<PERSON><PERSON> mais", "show_less": "<PERSON><PERSON> menos", "external_link": "Link externo", "opens_in_new_window": "Abre em nova janela", "download_file": "Baixar arquivo", "file_size": "Tamanho do arquivo: {{size}}", "file_type": "Tipo de arquivo: {{type}}", "image_description": "Imagem: {{description}}", "video_description": "Vídeo: {{description}}", "audio_description": "Áudio: {{description}}", "chart_description": "Gráfico: {{description}}", "table_description": "Tabela: {{description}}", "interactive_element": "Elemento interativo", "button_action": "Botão: {{action}}", "link_destination": "Link para: {{destination}}", "form_field": "Campo do formulário: {{label}}", "required_field_indicator": "Indicador de campo obrigatório", "optional_field_indicator": "Campo opcional", "character_count": "{{current}} de {{max}} caracteres", "password_requirements": "Requis<PERSON><PERSON> da <PERSON>a", "password_strength": "Força da senha: {{strength}}", "captcha_challenge": "Desafio CAPTCHA", "security_verification": "Verificação de segurança necessária", "two_factor_auth": "Autenticação de dois fatores", "biometric_auth": "Autenticação biométrica", "session_timeout": "Aviso de timeout da sessão", "auto_save": "Salvamento automático ativado", "unsaved_changes": "Você tem alterações não salvas", "confirm_navigation": "Confirmar nave<PERSON> para fora da página", "data_loading": "Dados estão carregando", "data_error": "Erro ao carregar dados", "retry_action": "Tentar novamente", "refresh_page": "<PERSON><PERSON><PERSON><PERSON>", "contact_support": "Entre em contato com o suporte para assistência"}, "errors": {"generic": "Ocorreu um erro inesperado. Por favor, tente novamente.", "network": "Erro de rede. Por favor, verifique sua conexão.", "validation": "Por favor, verifique sua entrada e tente novamente.", "authentication": "Falha na autenticação. Por favor, faça login novamente.", "authorization": "Você não tem permissão para realizar esta ação.", "not_found": "O recurso solicitado não foi encontrado.", "server_error": "Erro do servidor. Por favor, tente novamente mais tarde.", "wallet_connection": "Falha ao conectar carteira. Por favor, tente novamente.", "transaction_failed": "Transação falhou. Por favor, tente novamente.", "insufficient_funds": "Fundos insuficientes para esta transação.", "gas_estimation_failed": "Falha ao estimar taxas de gas.", "contract_interaction_failed": "Interação com contrato inteligente falhou.", "token_approval_failed": "Aprovação de token falhou.", "nft_mint_failed": "<PERSON><PERSON><PERSON><PERSON> de NFT falhou.", "staking_failed": "Operação de staking falhou.", "unstaking_failed": "Operação de unstaking falhou.", "swap_failed": "Troca de tokens falhou.", "liquidity_failed": "Operação de liquidez falhou.", "payment_processing_failed": "Processamento de pagamento falhou.", "service_booking_failed": "Reserva de serviço falhou.", "file_upload_failed": "Upload de arquivo falhou.", "image_processing_failed": "Processamento de imagem falhou.", "video_processing_failed": "Processamento de vídeo falhou.", "audio_processing_failed": "Processamento de áudio falhou.", "content_generation_failed": "Geração de conteúdo falhou.", "ai_processing_failed": "Processamento de IA falhou.", "blockchain_sync_failed": "Sincronização da blockchain falhou.", "ipfs_upload_failed": "Upload IPFS falhou.", "metadata_update_failed": "Atualização de metadados falhou.", "smart_contract_deployment_failed": "Implantação de contrato inteligente falhou.", "oracle_data_failed": "Recuperação de dados do oráculo falhou.", "cross_chain_failed": "Operação cross-chain falhou.", "defi_protocol_failed": "Interação com protocolo DeFi falhou.", "yield_farming_failed": "Operação de yield farming falhou.", "governance_vote_failed": "Voto de governança falhou.", "proposal_creation_failed": "Criação de proposta falhou.", "delegation_failed": "Operação de delegação falhou.", "reward_claim_failed": "Reivindicação de recompensa falhou.", "vesting_failed": "Operação de vesting falhou.", "bridge_failed": "Operação de bridge falhou.", "layer2_failed": "Operação Layer 2 falhou.", "sidechain_failed": "Operação de sidechain falhou.", "rollup_failed": "Operação de rollup falhou.", "state_channel_failed": "Operação de state channel falhou.", "plasma_failed": "Operação Plasma falhou.", "sharding_failed": "Operação de sharding falhou.", "consensus_failed": "Mecanismo de consenso falhou.", "validator_failed": "Operação de validador falhou.", "slashing_occurred": "Evento de slashing ocorreu.", "fork_detected": "Fork da blockchain detectado.", "reorg_detected": "Reorganização da blockchain detectada.", "mempool_congestion": "Congestionamento da rede detectado.", "gas_price_high": "Preços de gas estão atualmente altos.", "block_confirmation_timeout": "Timeout de confirmação de bloco.", "transaction_reverted": "Transação foi revertida.", "contract_paused": "Contrato inteligente está atualmente pausado.", "contract_deprecated": "Contrato inteligente está obsoleto.", "upgrade_required": "Atualização de contrato necessária.", "maintenance_mode": "Sistema está em modo de manutenção.", "rate_limit_exceeded": "Limite de taxa excedido. Por favor, tente novamente mais tarde.", "quota_exceeded": "Cota de uso excedida.", "subscription_expired": "Assinatura expirou.", "feature_not_available": "Recurso não disponível em sua região.", "kyc_required": "Verificação KYC necessária.", "aml_check_failed": "Verificação AML falhou.", "compliance_violation": "Violação de conformidade detectada.", "regulatory_restriction": "Restrição regulatória se aplica.", "jurisdiction_blocked": "Serviço não disponível em sua jurisdição.", "sanctions_check_failed": "Verificação de sanções falhou.", "risk_assessment_failed": "Avaliação de risco falhou.", "fraud_detection_triggered": "Sistema de detecção de fraude acionado.", "security_breach_detected": "Violação de segurança detectada.", "suspicious_activity": "Atividade suspeita detectada.", "account_locked": "Conta foi bloqueada.", "account_suspended": "Conta foi suspensa.", "account_terminated": "Conta foi encerrada.", "password_expired": "<PERSON><PERSON> expirou.", "session_expired": "Sessão expirou.", "token_expired": "Token de acesso expirou.", "refresh_token_invalid": "Token de atualização é inválido.", "api_key_invalid": "<PERSON><PERSON> da <PERSON> in<PERSON>.", "signature_invalid": "Assinatura digital é inválida.", "certificate_expired": "Certificado expirou.", "ssl_verification_failed": "Verificação SSL falhou.", "encryption_failed": "Operação de criptografia falhou.", "decryption_failed": "Operação de descriptografia falhou.", "hash_verification_failed": "Verificação de hash falhou.", "merkle_proof_invalid": "<PERSON>va Merkle é inválida.", "zero_knowledge_proof_failed": "Prova de conhecimento zero falhou.", "multi_sig_threshold_not_met": "Limite de multi-assinatura não atingido.", "timelock_not_expired": "<PERSON><PERSON> ainda não expirou.", "oracle_price_stale": "Dados de preço do oráculo estão obsoletos.", "slippage_too_high": "Tolerância de slippage excedida.", "deadline_exceeded": "Prazo da transação excedido.", "nonce_too_low": "<PERSON>ce da transação muito baixo.", "nonce_too_high": "<PERSON><PERSON> da transação muito alto.", "gas_limit_exceeded": "Limite de gas excedido.", "block_gas_limit_exceeded": "Limite de gas do bloco excedido.", "base_fee_too_high": "Taxa base muito alta.", "priority_fee_too_low": "Taxa de prioridade muito baixa.", "max_fee_too_low": "Taxa máxima muito baixa.", "eip1559_not_supported": "EIP-1559 não suportado nesta rede.", "legacy_transaction_not_supported": "Transações legadas não suportadas.", "access_list_invalid": "Lista de acesso é inválida.", "blob_transaction_failed": "Transação blob falhou.", "proto_danksharding_failed": "Operação proto-danksharding falhou.", "verkle_tree_failed": "Operação Verkle tree falhou.", "state_expiry_failed": "Operação de expiração de estado falhou.", "account_abstraction_failed": "Abstração de conta falhou.", "social_recovery_failed": "Recuperação social falhou.", "guardian_approval_failed": "Aprovação do guardião falhou.", "recovery_delay_not_met": "Período de atraso de recuperação não atendido.", "backup_not_found": "Backup não encontrado.", "seed_phrase_invalid": "Frase semente é inválida.", "private_key_invalid": "Chave privada é inválida.", "public_key_invalid": "Chave pública é inválida.", "address_invalid": "Formato de endereço é inválido.", "checksum_mismatch": "Incompatibilidade de checksum do endereço.", "ens_resolution_failed": "Resolução de nome ENS falhou.", "reverse_ens_failed": "Busca reversa ENS falhou.", "ipfs_hash_invalid": "Hash IPFS é inválido.", "content_hash_mismatch": "Incompatibilidade de hash de conteúdo.", "metadata_invalid": "Formato de metadados é inválido.", "json_parse_error": "Erro de análise JSON.", "xml_parse_error": "Erro de análise XML.", "csv_parse_error": "Erro de análise CSV.", "binary_data_corrupted": "Dados binários estão corrompidos.", "file_format_unsupported": "Formato de arquivo não suportado.", "file_size_too_large": "Tamanho do arquivo excede o limite.", "file_size_too_small": "Tamanho do arquivo é muito pequeno.", "image_resolution_too_low": "Resolução da imagem é muito baixa.", "image_resolution_too_high": "Resolução da imagem é muito alta.", "video_duration_too_long": "Duração do vídeo excede o limite.", "video_duration_too_short": "Duração do vídeo é muito curta.", "audio_quality_too_low": "Qualidade do áudio é muito baixa.", "audio_format_unsupported": "Formato de áudio não suportado.", "codec_not_supported": "Codec não suportado.", "container_format_invalid": "Formato de contêiner é inválido.", "stream_corrupted": "Stream de mídia está corrompido.", "drm_protection_failed": "Proteção DRM falhou.", "watermark_detection_failed": "Detecção de marca d'água falhou.", "content_moderation_failed": "Moderação de conteúdo falhou.", "ai_model_unavailable": "Modelo de IA indisponível.", "ai_inference_failed": "Inferência de IA falhou.", "machine_learning_error": "Erro de aprendizado de máquina.", "neural_network_error": "Erro de rede neural.", "training_data_insufficient": "Dados de treinamento insuficientes.", "model_accuracy_too_low": "Precisão do modelo muito baixa.", "prediction_confidence_low": "Confiança da previsão é baixa.", "feature_extraction_failed": "Extração de características falhou.", "dimensionality_reduction_failed": "Redução de dimensionalidade falhou.", "clustering_failed": "Operação de clustering falhou.", "classification_failed": "Classificação falhou.", "regression_failed": "Análise de regressão falhou.", "anomaly_detection_failed": "Detecção de anomalia falhou.", "pattern_recognition_failed": "Reconhecimento de padrão falhou.", "natural_language_processing_failed": "Processamento de linguagem natural falhou.", "sentiment_analysis_failed": "<PERSON><PERSON><PERSON><PERSON> de sentimento falhou.", "text_summarization_failed": "Resumo de texto falhou.", "translation_failed": "Tradução falhou.", "speech_recognition_failed": "Reconhecimento de fala falhou.", "speech_synthesis_failed": "Síntese de fala falhou.", "computer_vision_failed": "Visão computacional falhou.", "object_detection_failed": "Detecção de objeto falhou.", "face_recognition_failed": "Reconhecimento facial falhou.", "ocr_failed": "Reconhecimento óptico de caracteres falhou.", "barcode_scanning_failed": "Escaneamento de código de barras falhou.", "qr_code_scanning_failed": "Escaneamento de código QR falhou.", "augmented_reality_failed": "Realidade aumentada falhou.", "virtual_reality_failed": "Realidade virtual falhou.", "mixed_reality_failed": "Realidade mista falhou.", "3d_rendering_failed": "Renderização 3D falhou.", "ray_tracing_failed": "<PERSON> tracing falhou.", "physics_simulation_failed": "Simulação de física falhou.", "collision_detection_failed": "Detecção de colisão falhou.", "pathfinding_failed": "Pathfinding falhou.", "ai_agent_failed": "Agente de IA falhou.", "database_connection_failed": "Conexão com banco de dados falhou.", "database_timeout": "Operação de banco de dados expirou.", "database_lock_timeout": "Timeout de bloqueio do banco de dados.", "transaction_rollback": "Transação do banco de dados foi revertida.", "constraint_violation": "Violação de restrição do banco de dados.", "foreign_key_violation": "Violação de restrição de chave estrangeira.", "unique_constraint_violation": "Violação de restrição única.", "check_constraint_violation": "Violação de restrição de verificação.", "not_null_violation": "Violação de restrição não nula.", "data_type_mismatch": "Incompatibilidade de tipo de dados.", "sql_syntax_error": "Erro de sintaxe SQL.", "nosql_operation_failed": "Operação NoSQL falhou.", "sql_query_failed": "Consulta SQL falhou.", "cache_miss": "<PERSON><PERSON> miss ocorreu.", "cache_invalidation_failed": "Invalidação de cache falhou.", "cache_eviction_failed": "Remoção de cache falhou.", "redis_connection_failed": "Conexão Redis falhou.", "memcached_failed": "Memcached falhou.", "elasticsearch_failed": "Elasticsearch falhou.", "solr_failed": "Solr falhou.", "mongodb_failed": "MongoDB falhou.", "cassandra_failed": "<PERSON> falhou.", "dynamodb_failed": "DynamoDB falhou.", "firebase_failed": "Firebase falhou.", "supabase_failed": "Supabase falhou.", "planetscale_failed": "PlanetScale falhou.", "neon_failed": "Neon falhou.", "cockroachdb_failed": "CockroachDB falhou.", "yugabytedb_failed": "YugabyteDB falhou.", "tidb_failed": "TiDB falhou.", "vitess_failed": "<PERSON><PERSON><PERSON> fal<PERSON>.", "spanner_failed": "Cloud Spanner falhou.", "bigtable_failed": "Cloud Bigtable falhou.", "datastore_failed": "Cloud Datastore falhou.", "firestore_failed": "Cloud Firestore falhou.", "realtime_database_failed": "Realtime Database falhou.", "aws_rds_failed": "AWS RDS falhou.", "aws_aurora_failed": "AWS Aurora falhou.", "aws_redshift_failed": "AWS Redshift falhou.", "aws_documentdb_failed": "AWS DocumentDB falhou.", "aws_neptune_failed": "AWS Neptune falhou.", "aws_timestream_failed": "AWS Timestream falhou.", "azure_sql_failed": "Azure SQL falhou.", "azure_cosmos_failed": "Azure Cosmos DB falhou.", "azure_synapse_failed": "Azure Synapse falhou.", "gcp_cloud_sql_failed": "GCP Cloud SQL falhou.", "gcp_bigquery_failed": "GCP BigQuery falhou.", "snowflake_failed": "Snowflake falhou.", "databricks_failed": "Databricks falhou.", "apache_spark_failed": "Apache Spark falhou.", "apache_kafka_failed": "Apache Kafka falhou.", "apache_pulsar_failed": "Apache Pulsar falhou.", "rabbitmq_failed": "RabbitMQ falhou.", "activemq_failed": "ActiveMQ falhou.", "amazon_sqs_failed": "Amazon SQS falhou.", "amazon_sns_failed": "Amazon SNS falhou.", "google_pubsub_failed": "Google Pub/Sub falhou.", "azure_service_bus_failed": "Azure Service Bus falhou.", "nats_failed": "NATS falhou.", "zeromq_failed": "ZeroMQ falhou.", "grpc_failed": "gRPC falhou.", "graphql_failed": "GraphQL falhou.", "rest_api_failed": "REST API falhou.", "soap_failed": "SOAP falhou.", "websocket_failed": "WebSocket falhou.", "sse_failed": "Server-Sent Events falhou.", "webhook_failed": "Webhook falhou.", "oauth_failed": "<PERSON><PERSON><PERSON> falhou.", "jwt_failed": "JWT falhou.", "saml_failed": "SAML falhou.", "ldap_failed": "LDAP falhou.", "active_directory_failed": "Active Directory falhou.", "okta_failed": "<PERSON><PERSON> falhou.", "auth0_failed": "Auth0 falhou.", "cognito_failed": "AWS Cognito falhou.", "firebase_auth_failed": "Firebase Auth falhou.", "supabase_auth_failed": "Supabase Auth falhou.", "clerk_failed": "Clerk falhou.", "magic_link_failed": "Magic Link falhou.", "passwordless_failed": "Autenticação sem senha falhou.", "biometric_failed": "Autenticação biométrica falhou.", "face_id_failed": "Face ID falhou.", "touch_id_failed": "Touch ID falhou.", "fingerprint_failed": "Autenticação por impressão digital falhou.", "voice_recognition_failed": "Reconhecimento de voz falhou.", "iris_scan_failed": "Escaneamento de íris falhou.", "retina_scan_failed": "Escaneamento de retina falhou.", "palm_print_failed": "Impressão palmar falhou.", "dna_analysis_failed": "<PERSON><PERSON><PERSON><PERSON> <PERSON> falhou.", "behavioral_biometrics_failed": "Biometria comportamental falhou.", "keystroke_dynamics_failed": "Dinâmica de digitação falhou.", "gait_analysis_failed": "<PERSON><PERSON><PERSON><PERSON> de marcha falhou.", "signature_verification_failed": "Verificação de assinatura falhou.", "handwriting_analysis_failed": "Análise de caligrafia falhou.", "mouse_dynamics_failed": "Dinâmica do mouse falhou.", "typing_pattern_failed": "Padrão de digitação falhou.", "device_fingerprinting_failed": "Impressão digital do dispositivo falhou.", "browser_fingerprinting_failed": "Impressão digital do navegador falhou.", "canvas_fingerprinting_failed": "Impressão digital do canvas falhou.", "webgl_fingerprinting_failed": "Impressão digital WebGL falhou.", "audio_fingerprinting_failed": "Impressão digital de áudio falhou.", "font_fingerprinting_failed": "Impressão digital de fonte falhou.", "screen_resolution_failed": "Detecção de resolução de tela falhou.", "timezone_detection_failed": "Detecção de fuso horário falhou.", "language_detection_failed": "Detecção de idioma falhou.", "geolocation_failed": "Geolocalização falhou.", "ip_geolocation_failed": "Geolocalização por IP falhou.", "gps_failed": "GPS falhou.", "wifi_positioning_failed": "Posicionamento WiFi falhou.", "bluetooth_positioning_failed": "Posicionamento Bluetooth falhou.", "cellular_positioning_failed": "Posicionamento celular falhou.", "beacon_positioning_failed": "Posicionamento por beacon falhou.", "nfc_failed": "NFC falhou.", "rfid_failed": "RFID falhou.", "qr_code_failed": "Código QR falhou.", "barcode_failed": "<PERSON>ódigo de barras falhou.", "magnetic_stripe_failed": "Tarja magnética falhou.", "chip_card_failed": "Cartão com chip falhou.", "contactless_payment_failed": "Pagamento sem contato falhou.", "apple_pay_failed": "Apple Pay falhou.", "google_pay_failed": "Google Pay falhou.", "samsung_pay_failed": "Samsung Pay falhou.", "paypal_failed": "PayPal falhou.", "stripe_failed": "Stripe falhou.", "square_failed": "Square falhou.", "braintree_failed": "Braintree falhou.", "adyen_failed": "<PERSON><PERSON><PERSON> falhou.", "worldpay_failed": "Worldpay falhou.", "authorize_net_failed": "Authorize.Net falhou.", "cybersource_failed": "CyberSource falhou.", "first_data_failed": "First Data falhou.", "global_payments_failed": "Global Payments falhou.", "fiserv_failed": "Fiserv falhou.", "fis_failed": "FIS falhou.", "ncr_failed": "NCR falhou.", "verifone_failed": "Verifone falhou.", "ingenico_failed": "Ingenico falhou.", "pax_failed": "PAX falhou.", "clover_failed": "Clover falhou.", "toast_failed": "Toast falhou.", "shopify_pos_failed": "Shopify POS falhou.", "lightspeed_failed": "Lightspeed falhou.", "vend_failed": "Vend falhou.", "loyverse_failed": "Loyverse falhou.", "epos_now_failed": "Epos Now falhou.", "revel_failed": "<PERSON>el falhou.", "touchbistro_failed": "TouchBistro falhou.", "upserve_failed": "Upserve falhou.", "breadcrumb_failed": "Breadcrumb falhou.", "talech_failed": "<PERSON><PERSON> falhou.", "erply_failed": "ERPLY falhou.", "cin7_failed": "Cin7 falhou.", "tradegecko_failed": "TradeGecko falhou.", "unleashed_failed": "Unleashed falhou.", "fishbowl_failed": "Fishbowl falhou.", "inflow_failed": "inFlow falhou.", "sortly_failed": "Sortly falhou.", "stockpile_failed": "Stockpile falhou.", "zoho_inventory_failed": "<PERSON><PERSON><PERSON>ory falhou.", "ordoro_failed": "Ordoro falhou.", "skuvault_failed": "S<PERSON><PERSON>ault falhou.", "stitch_labs_failed": "Stitch Labs falhou.", "katana_failed": "<PERSON><PERSON> fal<PERSON>.", "megaventory_failed": "Megaventory falhou.", "dear_inventory_failed": "DEAR Inventory falhou.", "brightpearl_failed": "<PERSON><PERSON><PERSON> falhou.", "netsuite_failed": "NetSuite falhou.", "sap_failed": "SAP falhou.", "oracle_erp_failed": "Oracle ERP falhou.", "microsoft_dynamics_failed": "Microsoft Dynamics falhou.", "workday_failed": "Workday falhou.", "successfactors_failed": "SuccessFactors falhou.", "bamboohr_failed": "BambooHR falhou.", "namely_failed": "Namely falhou.", "zenefits_failed": "Zenefits falhou.", "gusto_failed": "<PERSON><PERSON> falhou.", "adp_failed": "ADP falhou.", "paychex_failed": "Paychex falhou.", "paylocity_failed": "Paylocity falhou.", "ultimate_software_failed": "Ultimate Software falhou.", "kronos_failed": "Kronos falhou.", "ceridian_failed": "Ceridian falhou.", "sage_failed": "<PERSON> falhou."}}