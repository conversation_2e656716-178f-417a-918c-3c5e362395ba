import { FastifyInstance, FastifyPluginOptions } from 'fastify';
import { authenticateToken, requirePermission } from '@/middleware/auth';

const createAgentSchema = {
  type: 'object',
  required: ['name', 'type', 'role', 'goal'],
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 100 },
    type: { type: 'string', enum: ['task_executor', 'workflow_coordinator', 'data_processor', 'decision_maker', 'monitor'] },
    role: { type: 'string', minLength: 1, maxLength: 200 },
    goal: { type: 'string', minLength: 1, maxLength: 500 },
    backstory: { type: 'string', maxLength: 1000 },
    capabilities: {
      type: 'array',
      items: { type: 'string' }
    },
    tools: {
      type: 'array',
      items: { type: 'string' }
    },
    maxConcurrentTasks: { type: 'number', minimum: 1, maximum: 10, default: 3 },
    timeoutMs: { type: 'number', minimum: 1000, maximum: 600000, default: 300000 },
    retryAttempts: { type: 'number', minimum: 0, maximum: 5, default: 3 },
    priority: { type: 'number', minimum: 1, maximum: 10, default: 5 },
    metadata: { type: 'object' }
  }
};

const updateAgentSchema = {
  type: 'object',
  properties: {
    name: { type: 'string', minLength: 1, maxLength: 100 },
    role: { type: 'string', minLength: 1, maxLength: 200 },
    goal: { type: 'string', minLength: 1, maxLength: 500 },
    backstory: { type: 'string', maxLength: 1000 },
    capabilities: {
      type: 'array',
      items: { type: 'string' }
    },
    tools: {
      type: 'array',
      items: { type: 'string' }
    },
    maxConcurrentTasks: { type: 'number', minimum: 1, maximum: 10 },
    timeoutMs: { type: 'number', minimum: 1000, maximum: 600000 },
    retryAttempts: { type: 'number', minimum: 0, maximum: 5 },
    priority: { type: 'number', minimum: 1, maximum: 10 },
    status: { type: 'string', enum: ['active', 'inactive', 'maintenance'] },
    metadata: { type: 'object' }
  }
};

const agentQuerySchema = {
  type: 'object',
  properties: {
    page: { type: 'number', minimum: 1, default: 1 },
    limit: { type: 'number', minimum: 1, maximum: 100, default: 20 },
    type: { type: 'string' },
    status: { type: 'string', enum: ['active', 'inactive', 'maintenance', 'busy', 'idle'] },
    search: { type: 'string', maxLength: 100 },
    sortBy: { type: 'string', enum: ['name', 'type', 'createdAt', 'lastActive', 'priority'], default: 'createdAt' },
    sortOrder: { type: 'string', enum: ['asc', 'desc'], default: 'desc' }
  }
};

export default async function agentsRoutes(
  fastify: FastifyInstance,
  options: FastifyPluginOptions
) {
  // Create agent
  fastify.post('/', {
    preHandler: [authenticateToken, requirePermission('agents:create')],
    schema: {
      description: 'Create a new agent',
      tags: ['Agents'],
      body: createAgentSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                createdAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const agentData = request.body as any;
    const agent = await fastify.agentService.createAgent({
      ...agentData,
      createdBy: request.user!.id,
      organizationId: request.user!.organizationId
    });
    
    reply.status(201);
    return {
      success: true,
      data: agent
    };
  });

  // Get agents list
  fastify.get('/', {
    preHandler: [authenticateToken, requirePermission('agents:read')],
    schema: {
      description: 'Get list of agents',
      tags: ['Agents'],
      querystring: agentQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  type: { type: 'string' },
                  status: { type: 'string' },
                  currentTasks: { type: 'number' },
                  totalTasksCompleted: { type: 'number' },
                  lastActive: { type: 'string' },
                  createdAt: { type: 'string' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'number' },
                limit: { type: 'number' },
                total: { type: 'number' },
                pages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const query = request.query as any;
    const result = await fastify.agentService.getAgents({
      ...query,
      organizationId: request.user!.organizationId
    });
    
    return {
      success: true,
      data: result.agents,
      pagination: result.pagination
    };
  });

  // Get agent by ID
  fastify.get('/:id', {
    preHandler: [authenticateToken, requirePermission('agents:read')],
    schema: {
      description: 'Get agent by ID',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                type: { type: 'string' },
                role: { type: 'string' },
                goal: { type: 'string' },
                backstory: { type: 'string' },
                capabilities: { type: 'array', items: { type: 'string' } },
                tools: { type: 'array', items: { type: 'string' } },
                status: { type: 'string' },
                currentTasks: { type: 'array' },
                metrics: { type: 'object' },
                createdAt: { type: 'string' },
                updatedAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    const agent = await fastify.agentService.getAgent(id, request.user!.organizationId);
    
    if (!agent) {
      return reply.status(404).send({
        error: 'Not Found',
        message: 'Agent not found'
      });
    }
    
    return {
      success: true,
      data: agent
    };
  });

  // Update agent
  fastify.put('/:id', {
    preHandler: [authenticateToken, requirePermission('agents:update')],
    schema: {
      description: 'Update agent',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      body: updateAgentSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                name: { type: 'string' },
                type: { type: 'string' },
                status: { type: 'string' },
                updatedAt: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    const updateData = request.body as any;
    
    const agent = await fastify.agentService.updateAgent(id, {
      ...updateData,
      updatedBy: request.user!.id,
      organizationId: request.user!.organizationId
    });
    
    return {
      success: true,
      data: agent
    };
  });

  // Delete agent
  fastify.delete('/:id', {
    preHandler: [authenticateToken, requirePermission('agents:delete')],
    schema: {
      description: 'Delete agent',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    
    await fastify.agentService.deleteAgent(id, request.user!.organizationId);
    
    return {
      success: true,
      message: 'Agent deleted successfully'
    };
  });

  // Start agent
  fastify.post('/:id/start', {
    preHandler: [authenticateToken, requirePermission('agents:control')],
    schema: {
      description: 'Start agent',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    
    await fastify.agentService.startAgent(id, request.user!.organizationId);
    
    return {
      success: true,
      message: 'Agent started successfully'
    };
  });

  // Stop agent
  fastify.post('/:id/stop', {
    preHandler: [authenticateToken, requirePermission('agents:control')],
    schema: {
      description: 'Stop agent',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    
    await fastify.agentService.stopAgent(id, request.user!.organizationId);
    
    return {
      success: true,
      message: 'Agent stopped successfully'
    };
  });

  // Get agent metrics
  fastify.get('/:id/metrics', {
    preHandler: [authenticateToken, requirePermission('agents:read')],
    schema: {
      description: 'Get agent metrics',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          timeRange: { type: 'string', enum: ['1h', '24h', '7d', '30d'], default: '24h' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                tasksCompleted: { type: 'number' },
                tasksFailed: { type: 'number' },
                averageExecutionTime: { type: 'number' },
                successRate: { type: 'number' },
                uptime: { type: 'number' },
                memoryUsage: { type: 'number' },
                cpuUsage: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    const { timeRange } = request.query as { timeRange?: string };
    
    const metrics = await fastify.agentService.getAgentMetrics(id, {
      timeRange: timeRange || '24h',
      organizationId: request.user!.organizationId
    });
    
    return {
      success: true,
      data: metrics
    };
  });

  // Get agent logs
  fastify.get('/:id/logs', {
    preHandler: [authenticateToken, requirePermission('agents:read')],
    schema: {
      description: 'Get agent logs',
      tags: ['Agents'],
      params: {
        type: 'object',
        required: ['id'],
        properties: {
          id: { type: 'string' }
        }
      },
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'number', minimum: 1, default: 1 },
          limit: { type: 'number', minimum: 1, maximum: 100, default: 50 },
          level: { type: 'string', enum: ['error', 'warn', 'info', 'debug'] },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' }
        }
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  level: { type: 'string' },
                  message: { type: 'string' },
                  timestamp: { type: 'string' },
                  metadata: { type: 'object' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'number' },
                limit: { type: 'number' },
                total: { type: 'number' },
                pages: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { id } = request.params as { id: string };
    const query = request.query as any;
    
    const result = await fastify.agentService.getAgentLogs(id, {
      ...query,
      organizationId: request.user!.organizationId
    });
    
    return {
      success: true,
      data: result.logs,
      pagination: result.pagination
    };
  });
}