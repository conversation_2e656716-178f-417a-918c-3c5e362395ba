import Fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import jwt from '@fastify/jwt';
import multipart from '@fastify/multipart';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';

// Import configuration and utilities
import { config } from './config/environment';
import { logger } from './utils/logger';

// Import middleware
import { authMiddleware } from './middleware/auth';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';

// Import services
import { vectorStoreService } from './services/vectorStoreService'; // Temporarily disabled
import { contentGenerationQueue } from './queues/contentGeneration';

// Import routes
import { contentRoutes } from './routes/content';
import { calendarRoutes } from './routes/calendar';
import { campaignRoutes } from './routes/campaigns';
import { analyticsRoutes } from './routes/analytics';
import { aiRoutes } from './routes/ai';

const fastify = Fastify({
  logger: logger,
  trustProxy: true,
});

// Register plugins gradually
fastify.register(cors, {
  origin: config.cors.origins,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
});

fastify.register(helmet, {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", 'data:', 'https:'],
    },
  },
});

fastify.register(jwt, {
  secret: config.jwt.secret,
  sign: {
    expiresIn: config.jwt.expiresIn,
  },
});

fastify.register(multipart, {
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB
  },
});

// Comment out remaining plugins and routes temporarily
/*

fastify.register(swagger, {
  swagger: {
    info: {
      title: 'Content Studio API',
      description: 'AI-powered content generation and management API',
      version: '1.0.0',
    },
    host: `${config.host}:${config.port}`,
    schemes: ['http', 'https'],
    consumes: ['application/json'],
    produces: ['application/json'],
    securityDefinitions: {
      Bearer: {
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
        description: 'Enter JWT token in format: Bearer <token>',
      },
    },
    tags: [
      { name: 'Content', description: 'Content management endpoints' },
      { name: 'Calendar', description: 'Content scheduling endpoints' },
      { name: 'Campaigns', description: 'Campaign management endpoints' },
      { name: 'Analytics', description: 'Analytics and reporting endpoints' },
      { name: 'AI', description: 'AI content generation endpoints' },
    ],
  },
});

fastify.register(swaggerUi, {
  routePrefix: '/docs',
  uiConfig: {
    docExpansion: 'list',
    deepLinking: false,
  },
  staticCSP: true,
  transformStaticCSP: (header) => header,
});

// Register authentication middleware
fastify.register(authMiddleware);

// Register routes
fastify.register(contentRoutes, { prefix: '/api/content' });
fastify.register(calendarRoutes, { prefix: '/api/calendar' });
fastify.register(campaignRoutes, { prefix: '/api/campaigns' });
fastify.register(analyticsRoutes, { prefix: '/api/analytics' });
fastify.register(aiRoutes, { prefix: '/api/ai' });
*/

// Health check endpoint
fastify.get('/health', async (request, reply) => {
  let vectorStoreHealth = false;
  let queueStats = {
    waiting: 0,
    active: 0,
    completed: 0,
    failed: 0,
    delayed: 0,
    paused: 0,
  };
  
  try {
    vectorStoreHealth = await vectorStoreService.healthCheck();
  } catch (error) {
    logger.debug('Vector store health check failed:', error.message);
  }
  
  try {
    queueStats = await contentGenerationQueue.getQueueStats();
  } catch (error) {
    logger.debug('Queue stats check failed:', error.message);
  }
  
  return {
    status: 'ok',
    timestamp: new Date().toISOString(),
    services: {
      vectorStore: vectorStoreHealth ? 'healthy' : 'disabled',
      queue: 'healthy',
    },
    queue: queueStats,
    version: '1.0.0',
  };
});

// Register error handlers
fastify.setErrorHandler(errorHandler);
fastify.setNotFoundHandler(notFoundHandler);

// Initialize services
const initializeServices = async () => {
  try {
    logger.info('Initializing services...');
    
    // Initialize vector store
    try {
      await vectorStoreService.initialize();
      logger.info('Vector store service initialized');
    } catch (error) {
      logger.warn('Vector store initialization failed, continuing without it:', error.message);
    }
    
    // Initialize content generation queue
    try {
      await contentGenerationQueue.initialize();
      logger.info('Content generation queue initialized');
    } catch (error) {
      logger.warn('Content generation queue initialization failed, continuing without it:', error.message);
    }
    
    logger.info('Service initialization completed');
  } catch (error) {
    logger.error('Failed to initialize services:', error);
    if (config.nodeEnv === 'production') {
      throw error;
    } else {
      logger.warn('Continuing in development mode with limited functionality');
    }
  }
};

// Graceful shutdown
const gracefulShutdown = async (signal: string) => {
  logger.info(`Received ${signal}, starting graceful shutdown...`);
  
  try {
    // Stop accepting new requests
    await fastify.close();
    
    // Shutdown services
    await contentGenerationQueue.shutdown();
    await vectorStoreService.disconnect();
    
    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during graceful shutdown:', error);
    process.exit(1);
  }
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception:', error);
  gracefulShutdown('uncaughtException');
});

process.on('unhandledRejection', (reason, promise) => {
  // Log detailed information about the rejection
  logger.error('Unhandled rejection details:', {
    reason: reason,
    reasonType: typeof reason,
    reasonString: String(reason),
    message: reason && typeof reason === 'object' && 'message' in reason ? reason.message : 'No message',
    stack: reason && typeof reason === 'object' && 'stack' in reason ? reason.stack : 'No stack',
    promise: promise
  });
  
  // In development mode, don't shut down for Redis connection errors
  if (config.nodeEnv === 'development') {
    const reasonStr = String(reason).toLowerCase();
    const messageStr = reason && typeof reason === 'object' && 'message' in reason ? 
      String(reason.message).toLowerCase() : '';
    
    if (reasonStr.includes('redis') || 
        reasonStr.includes('econnrefused') || 
        reasonStr.includes('connect') ||
        messageStr.includes('redis') || 
        messageStr.includes('econnrefused') ||
        messageStr.includes('connect')) {
      logger.warn('Ignoring Redis/connection error in development mode');
      return;
    }
  }
  
  gracefulShutdown('unhandledRejection');
});

// Start server
const start = async () => {
  try {
    // Initialize services first
    await initializeServices();
    logger.info('Services initialized, starting Fastify server...');
    
    // Log the configuration being used
    logger.info('Server configuration:', {
      host: config.host,
      port: config.port,
      nodeEnv: config.nodeEnv
    });
    
    // Start the server
    logger.info('Calling fastify.listen...');
    await fastify.listen({ 
      port: config.port, 
      host: config.host 
    });
    
    logger.info(`Content Studio API server listening on ${config.host}:${config.port}`);
    logger.info(`API documentation available at http://${config.host}:${config.port}/docs`);
  } catch (err) {
    logger.error('Failed to start server - detailed error:', {
      message: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      name: err instanceof Error ? err.name : undefined,
      code: err && typeof err === 'object' && 'code' in err ? err.code : undefined,
      errno: err && typeof err === 'object' && 'errno' in err ? err.errno : undefined,
      syscall: err && typeof err === 'object' && 'syscall' in err ? err.syscall : undefined,
      address: err && typeof err === 'object' && 'address' in err ? err.address : undefined,
      port: err && typeof err === 'object' && 'port' in err ? err.port : undefined
    });
    process.exit(1);
  }
};

start();

export { fastify };