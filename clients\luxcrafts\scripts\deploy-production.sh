#!/bin/bash

# Luxcrafts Production Deployment Script
# This script handles the complete deployment process for production environment
# Usage: ./scripts/deploy-production.sh [--force] [--rollback VERSION]

set -euo pipefail

# Configuration
APP_NAME="luxcrafts-production"
DEPLOY_USER="deploy"
PRODUCTION_HOST="${PRODUCTION_HOST:-luxcrafts.co}"
DEPLOY_PATH="/opt/luxcrafts-production"
BACKUP_PATH="/opt/luxcrafts-backups"
NGINX_CONFIG="/etc/nginx/sites-available/luxcrafts.co"
SSL_CERT_PATH="/etc/letsencrypt/live/luxcrafts.co"
HEALTH_CHECK_URL="https://luxcrafts.co/health"
MAX_RETRIES=5
RETRY_DELAY=10

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Error handling
error_exit() {
    log_error "$1"
    exit 1
}

# Check if running as deploy user
check_user() {
    if [[ "$(whoami)" != "$DEPLOY_USER" ]]; then
        error_exit "This script must be run as the $DEPLOY_USER user"
    fi
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required commands exist
    local required_commands=("docker" "docker-compose" "nginx" "certbot" "curl" "jq")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error_exit "Required command '$cmd' not found"
        fi
    done
    
    # Check if Docker is running
    if ! docker info &> /dev/null; then
        error_exit "Docker is not running"
    fi
    
    # Check if Nginx is running
    if ! systemctl is-active --quiet nginx; then
        log_warning "Nginx is not running, attempting to start..."
        sudo systemctl start nginx || error_exit "Failed to start Nginx"
    fi
    
    log_success "Prerequisites check passed"
}

# Create backup
create_backup() {
    local backup_name="backup_$(date +%Y%m%d_%H%M%S)"
    local backup_dir="$BACKUP_PATH/$backup_name"
    
    log_info "Creating backup: $backup_name"
    
    # Create backup directory
    mkdir -p "$backup_dir"
    
    # Backup current deployment if it exists
    if [[ -d "$DEPLOY_PATH/current" ]]; then
        cp -r "$DEPLOY_PATH/current" "$backup_dir/app"
        log_success "Application backup created"
    fi
    
    # Backup database
    if docker ps | grep -q "luxcrafts-db"; then
        docker exec luxcrafts-db pg_dump -U luxcrafts luxcrafts_production > "$backup_dir/database.sql"
        log_success "Database backup created"
    fi
    
    # Backup Nginx configuration
    if [[ -f "$NGINX_CONFIG" ]]; then
        cp "$NGINX_CONFIG" "$backup_dir/nginx.conf"
        log_success "Nginx configuration backup created"
    fi
    
    # Store backup name for potential rollback
    echo "$backup_name" > "$DEPLOY_PATH/last_backup"
    
    # Clean old backups (keep last 10)
    find "$BACKUP_PATH" -maxdepth 1 -type d -name "backup_*" | sort -r | tail -n +11 | xargs rm -rf
    
    log_success "Backup completed: $backup_name"
}

# Download and extract deployment package
download_package() {
    local package_url="$1"
    local release_dir="$DEPLOY_PATH/releases/$(date +%Y%m%d_%H%M%S)"
    
    log_info "Downloading deployment package..."
    
    # Create release directory
    mkdir -p "$release_dir"
    
    # Download package
    if ! curl -L -o "$release_dir/package.tar.gz" "$package_url"; then
        error_exit "Failed to download deployment package"
    fi
    
    # Extract package
    if ! tar -xzf "$release_dir/package.tar.gz" -C "$release_dir"; then
        error_exit "Failed to extract deployment package"
    fi
    
    # Remove package file
    rm "$release_dir/package.tar.gz"
    
    echo "$release_dir" > "$DEPLOY_PATH/current_release"
    log_success "Package downloaded and extracted to $release_dir"
}

# Update Docker containers
update_containers() {
    local release_dir="$(cat $DEPLOY_PATH/current_release)"
    
    log_info "Updating Docker containers..."
    
    # Navigate to release directory
    cd "$release_dir"
    
    # Pull latest images
    docker-compose pull
    
    # Stop existing containers gracefully
    if docker-compose ps | grep -q "Up"; then
        log_info "Stopping existing containers..."
        docker-compose down --timeout 30
    fi
    
    # Start new containers
    log_info "Starting new containers..."
    docker-compose up -d
    
    # Wait for containers to be ready
    log_info "Waiting for containers to be ready..."
    sleep 30
    
    # Check container health
    local retries=0
    while [[ $retries -lt $MAX_RETRIES ]]; do
        if docker-compose ps | grep -q "healthy\|Up"; then
            log_success "Containers are running"
            break
        fi
        
        retries=$((retries + 1))
        log_warning "Containers not ready, retrying in $RETRY_DELAY seconds... ($retries/$MAX_RETRIES)"
        sleep $RETRY_DELAY
    done
    
    if [[ $retries -eq $MAX_RETRIES ]]; then
        error_exit "Containers failed to start properly"
    fi
}

# Update symlink
update_symlink() {
    local release_dir="$(cat $DEPLOY_PATH/current_release)"
    
    log_info "Updating current symlink..."
    
    # Create or update symlink
    ln -sfn "$release_dir" "$DEPLOY_PATH/current"
    
    log_success "Symlink updated to point to $release_dir"
}

# Reload Nginx
reload_nginx() {
    log_info "Reloading Nginx configuration..."
    
    # Test Nginx configuration
    if ! sudo nginx -t; then
        error_exit "Nginx configuration test failed"
    fi
    
    # Reload Nginx
    if ! sudo systemctl reload nginx; then
        error_exit "Failed to reload Nginx"
    fi
    
    log_success "Nginx reloaded successfully"
}

# Health check
health_check() {
    log_info "Performing health check..."
    
    local retries=0
    while [[ $retries -lt $MAX_RETRIES ]]; do
        if curl -f -s "$HEALTH_CHECK_URL" > /dev/null; then
            log_success "Health check passed"
            return 0
        fi
        
        retries=$((retries + 1))
        log_warning "Health check failed, retrying in $RETRY_DELAY seconds... ($retries/$MAX_RETRIES)"
        sleep $RETRY_DELAY
    done
    
    error_exit "Health check failed after $MAX_RETRIES attempts"
}

# SSL certificate check
check_ssl() {
    log_info "Checking SSL certificate..."
    
    # Check certificate expiration
    local cert_expiry=$(openssl x509 -enddate -noout -in "$SSL_CERT_PATH/fullchain.pem" | cut -d= -f2)
    local expiry_timestamp=$(date -d "$cert_expiry" +%s)
    local current_timestamp=$(date +%s)
    local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
    
    if [[ $days_until_expiry -lt 30 ]]; then
        log_warning "SSL certificate expires in $days_until_expiry days"
        
        # Attempt to renew certificate
        log_info "Attempting to renew SSL certificate..."
        if sudo certbot renew --quiet; then
            log_success "SSL certificate renewed"
            reload_nginx
        else
            log_warning "Failed to renew SSL certificate"
        fi
    else
        log_success "SSL certificate is valid for $days_until_expiry days"
    fi
}

# Performance test
performance_test() {
    log_info "Running performance test..."
    
    # Simple performance check
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$HEALTH_CHECK_URL")
    local response_code=$(curl -o /dev/null -s -w '%{http_code}' "$HEALTH_CHECK_URL")
    
    if [[ "$response_code" == "200" ]]; then
        log_success "Performance test passed (Response time: ${response_time}s)"
    else
        log_warning "Performance test returned HTTP $response_code"
    fi
}

# Rollback function
rollback() {
    local backup_name="$1"
    local backup_dir="$BACKUP_PATH/$backup_name"
    
    log_info "Rolling back to backup: $backup_name"
    
    if [[ ! -d "$backup_dir" ]]; then
        error_exit "Backup directory not found: $backup_dir"
    fi
    
    # Stop current containers
    if [[ -d "$DEPLOY_PATH/current" ]]; then
        cd "$DEPLOY_PATH/current"
        docker-compose down --timeout 30
    fi
    
    # Restore application
    if [[ -d "$backup_dir/app" ]]; then
        rm -rf "$DEPLOY_PATH/current"
        cp -r "$backup_dir/app" "$DEPLOY_PATH/current"
        log_success "Application restored from backup"
    fi
    
    # Restore database
    if [[ -f "$backup_dir/database.sql" ]]; then
        docker exec luxcrafts-db psql -U luxcrafts -d luxcrafts_production < "$backup_dir/database.sql"
        log_success "Database restored from backup"
    fi
    
    # Start containers
    cd "$DEPLOY_PATH/current"
    docker-compose up -d
    
    # Wait and health check
    sleep 30
    health_check
    
    log_success "Rollback completed successfully"
}

# Cleanup old releases
cleanup() {
    log_info "Cleaning up old releases..."
    
    # Keep last 5 releases
    find "$DEPLOY_PATH/releases" -maxdepth 1 -type d | sort -r | tail -n +6 | xargs rm -rf
    
    # Clean Docker images
    docker image prune -f
    
    log_success "Cleanup completed"
}

# Send notification
send_notification() {
    local status="$1"
    local message="$2"
    
    # Send to webhook if configured
    if [[ -n "${WEBHOOK_URL:-}" ]]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\": \"Luxcrafts Deployment $status: $message\"}"
    fi
    
    # Log locally
    echo "$(date): $status - $message" >> "$DEPLOY_PATH/deployment.log"
}

# Main deployment function
deploy() {
    local package_url="$1"
    local force="${2:-false}"
    
    log_info "Starting Luxcrafts production deployment..."
    
    # Pre-deployment checks
    check_user
    check_prerequisites
    
    # Create backup
    create_backup
    
    # Download and deploy
    download_package "$package_url"
    update_containers
    update_symlink
    reload_nginx
    
    # Post-deployment checks
    health_check
    check_ssl
    performance_test
    
    # Cleanup
    cleanup
    
    # Send success notification
    send_notification "SUCCESS" "Deployment completed successfully"
    
    log_success "Deployment completed successfully!"
    log_info "Application is available at: https://luxcrafts.co"
}

# Main script logic
main() {
    local force=false
    local rollback_version=""
    local package_url=""
    
    # Parse arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --force)
                force=true
                shift
                ;;
            --rollback)
                rollback_version="$2"
                shift 2
                ;;
            --package-url)
                package_url="$2"
                shift 2
                ;;
            -h|--help)
                echo "Usage: $0 [--force] [--rollback VERSION] [--package-url URL]"
                echo "  --force: Force deployment without confirmation"
                echo "  --rollback VERSION: Rollback to specified backup version"
                echo "  --package-url URL: URL of the deployment package"
                exit 0
                ;;
            *)
                error_exit "Unknown option: $1"
                ;;
        esac
    done
    
    # Handle rollback
    if [[ -n "$rollback_version" ]]; then
        rollback "$rollback_version"
        exit 0
    fi
    
    # Check for package URL
    if [[ -z "$package_url" ]]; then
        package_url="${DEPLOYMENT_ARTIFACT_URL:-}"
        if [[ -z "$package_url" ]]; then
            error_exit "Package URL not provided. Use --package-url or set DEPLOYMENT_ARTIFACT_URL environment variable"
        fi
    fi
    
    # Confirmation prompt (unless forced)
    if [[ "$force" != "true" ]]; then
        echo -n "Are you sure you want to deploy to production? (y/N): "
        read -r confirmation
        if [[ "$confirmation" != "y" && "$confirmation" != "Y" ]]; then
            log_info "Deployment cancelled"
            exit 0
        fi
    fi
    
    # Start deployment
    deploy "$package_url" "$force"
}

# Trap errors and send notification
trap 'send_notification "FAILED" "Deployment failed with error on line $LINENO"' ERR

# Run main function
main "$@"