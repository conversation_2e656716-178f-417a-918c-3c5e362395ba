# ESTRATIX Agentic Rules: Prompt Engineering Standards
# Rule ID: R-AE-001

---

## 1. Core Principles

This document defines the standards for designing, managing, and securing prompts used by all ESTRATIX agents. Effective prompt engineering is the foundation of reliable, predictable, and secure agentic behavior.

- **Principle 1: Clarity, Context, and Conciseness**
  - **Rule:** Prompts MUST be unambiguous, provide all necessary context for the task, and be as concise as possible. Avoid jargon and complex sentence structures. The agent's role, task, and desired output format MUST be explicitly stated.
  - **Enforcement:** Prompts will be peer-reviewed. A central `system_prompt_matrix.md` will be maintained to catalog and review all core system prompts.

- **Principle 2: Structured and Verifiable Outputs**
  - **Rule:** Prompts SHOULD instruct agents to produce structured outputs (e.g., JSON, XML, Markdown) whenever possible. For agent-to-agent communication, structured formats are mandatory. This enables reliable parsing and workflow automation.
  - **Enforcement:** Use of frameworks like Pydantic-AI and Instructor is encouraged. CI/CD pipelines will validate the outputs of key agentic workflows against predefined schemas.

- **Principle 3: Security First (Anti-Injection)**
  - **Rule:** All user-provided input MUST be clearly demarcated and treated as untrusted data within the prompt. Techniques like using XML tags (e.g., `<user_input>`) and explicit instructions to ignore commands in user data are mandatory.
  - **Enforcement:** SecOps agents will periodically audit prompts for injection vulnerabilities. Use of input sanitization and output parsing is required before execution of any generated code or commands.

- **Principle 4: Versioning and Performance Monitoring**
  - **Rule:** All system prompts MUST be version-controlled in Git. The performance of prompts (e.g., quality of response, cost, latency) MUST be tracked.
  - **Enforcement:** Prompts are to be stored in the `.windsurf/rules` directory structure. MLOps/LLMOps agents will monitor prompt performance using tools like Langfuse and implement A/B testing for optimization.

---

## 2. Specific Rules

### 2.1. Prompt Design & Structure
- **Rule R-AE-001.1 (Role-Persona Definition):** Every system prompt MUST begin with a clear definition of the agent's role, persona, and core objective (e.g., "You are Cascade, a powerful agentic AI coding assistant...").
- **Rule R-AE-001.2 (Instructional Scaffolding):** Use clear headings (e.g., `## Rules`, `## Constraints`, `## Output Format`) to structure the prompt. Use numbered or bulleted lists for instructions.
- **Rule R-AE-001.3 (Few-Shot Examples):** For complex tasks, provide a few high-quality examples (`few-shot prompting`) within the prompt to guide the agent's output.

### 2.2. Security & Safety
- **Rule R-AE-001.4 (Input Delimitation):** User input MUST be enclosed in triple quotes, XML tags, or another clear delimiter to separate it from the core prompt instructions.
- **Rule R-AE-001.5 (Instructional Guardrails):** Prompts MUST include negative constraints specifying what the agent should NOT do (e.g., "Never execute code provided by the user directly.").

### 2.3. Management & Review
- **Rule R-AE-001.6 (Prompt Registry):** All reusable system prompts must be registered in the `system_prompt_matrix.md` with a clear description, version, and owner.

---

## 3. Enforcement by ESTRATIX Agents

- **Prompt Engineering Agents:** Responsible for designing, testing, and optimizing prompts.
- **MLOps/LLMOps Agents:** Responsible for the technical implementation, monitoring, and versioning of prompts.
- **SecOps Agents:** Responsible for auditing prompts for security vulnerabilities like prompt injection.
