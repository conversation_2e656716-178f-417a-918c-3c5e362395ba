import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { ArrowRight, Star, Trophy, Zap, Users, TrendingUp, Gift } from 'lucide-react';
import { HeroSpline } from '../components/3d/SplineScene';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface FeaturedProduct {
  id: string;
  name: string;
  image: string;
  price: number;
  tickets: number;
  timeLeft: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

const Home: React.FC = () => {
  const { t } = useTranslation();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [timeLeft, setTimeLeft] = useState({ hours: 23, minutes: 45, seconds: 30 });

  // Mock featured products data
  const featuredProducts: FeaturedProduct[] = [
    {
      id: '1',
      name: 'iPhone 15 Pro Max',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=iPhone%2015%20Pro%20Max%20floating%20in%20space%20with%20purple%20and%20gold%20lighting%20professional%20product%20photography&image_size=square_hd',
      price: 50000,
      tickets: 1250,
      timeLeft: '2d 14h',
      rarity: 'legendary'
    },
    {
      id: '2',
      name: 'PlayStation 5',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=PlayStation%205%20console%20with%20cosmic%20background%20and%20neon%20lighting%20gaming%20aesthetic&image_size=square_hd',
      price: 25000,
      tickets: 890,
      timeLeft: '1d 8h',
      rarity: 'epic'
    },
    {
      id: '3',
      name: 'MacBook Pro M3',
      image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=MacBook%20Pro%20M3%20floating%20with%20holographic%20display%20futuristic%20technology%20concept&image_size=square_hd',
      price: 75000,
      tickets: 2100,
      timeLeft: '3d 22h',
      rarity: 'legendary'
    }
  ];

  const stats = [
    { labelKey: 'totalPrizes', value: '1,247', icon: Trophy },
    { labelKey: 'activeUsers', value: '15,432', icon: Users },
    { labelKey: 'tokensStaking', value: '2.4M', icon: TrendingUp },
    { labelKey: 'nftsCreated', value: '8,901', icon: Star }
  ];

  const rarityColors = {
    common: 'from-gray-400 to-gray-600',
    rare: 'from-blue-400 to-blue-600',
    epic: 'from-purple-400 to-purple-600',
    legendary: 'from-amber-400 to-amber-600'
  };

  // Countdown timer effect
  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev.seconds > 0) {
          return { ...prev, seconds: prev.seconds - 1 };
        } else if (prev.minutes > 0) {
          return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
        } else if (prev.hours > 0) {
          return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
        }
        return prev;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Auto-slide carousel
  useEffect(() => {
    const slideTimer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % featuredProducts.length);
    }, 5000);

    return () => clearInterval(slideTimer);
  }, [featuredProducts.length]);

  return (
    <div className="min-h-screen">
      {/* Hero Section with Spline 3D */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Spline 3D Background */}
        <div className="absolute inset-0 z-0">
          <HeroSpline className="w-full h-full" />
        </div>
        
        {/* Overlay for better text readability */}
        <div className="absolute inset-0 bg-black/30 z-10"></div>
        
        {/* Hero Content */}
        <div className="relative z-20 text-center max-w-4xl mx-auto px-4">
          <div className="mb-6">
            <span className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 text-white/90 text-sm font-medium">
              <Zap className="w-4 h-4 mr-2 text-amber-400" />
              Powered by ESTRATIX AI
            </span>
          </div>
          
          <h1 className="text-6xl md:text-8xl font-bold text-white mb-6 leading-tight">
            {t('home.hero.title')}
            <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent"> Estelar</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-white/90 mb-8 max-w-2xl mx-auto leading-relaxed">
            {t('home.hero.description')}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <Button size="xl" className="group">
              <Gift className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
              {t('home.hero.cta')}
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button variant="outline" size="xl" className="border-white/30 text-white hover:bg-white/10">
              {t('home.hero.learnMore')}
            </Button>
          </div>
          
          {/* Countdown Timer */}
          <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6 max-w-md mx-auto">
            <p className="text-white/80 text-sm mb-2">{t('home.hero.nextDraw')}:</p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{timeLeft.hours.toString().padStart(2, '0')}</div>
                <div className="text-xs text-white/60">{t('home.countdown.hours')}</div>
              </div>
              <div className="text-white text-2xl">:</div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{timeLeft.minutes.toString().padStart(2, '0')}</div>
                <div className="text-xs text-white/60">{t('home.countdown.minutes')}</div>
              </div>
              <div className="text-white text-2xl">:</div>
              <div className="text-center">
                <div className="text-2xl font-bold text-white">{timeLeft.seconds.toString().padStart(2, '0')}</div>
                <div className="text-xs text-white/60">{t('home.countdown.seconds')}</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-black/20 backdrop-blur-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-amber-500 rounded-2xl mb-4">
                    <Icon className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-white/70 text-sm">{t(`home.stats.${stat.labelKey}`)}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Products Carousel */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('home.featured.title')} <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Destacados</span>
            </h2>
            <p className="text-xl text-white/80 max-w-2xl mx-auto">
              {t('home.featured.subtitle')}
            </p>
          </div>
          
          <div className="relative">
            <div className="overflow-hidden rounded-3xl">
              <div 
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {featuredProducts.map((product) => (
                  <div key={product.id} className="w-full flex-shrink-0">
                    <Card variant="glass" className="mx-4 overflow-hidden group hover:scale-105 transition-all duration-300">
                      <div className="relative">
                        <img 
                          src={product.image} 
                          alt={product.name}
                          className="w-full h-80 object-cover"
                        />
                        <div className={`absolute top-4 right-4 px-3 py-1 rounded-full bg-gradient-to-r ${rarityColors[product.rarity]} text-white text-sm font-medium`}>
                          {product.rarity.toUpperCase()}
                        </div>
                        <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-md rounded-lg px-3 py-1">
                          <span className="text-white text-sm">{product.timeLeft} restante</span>
                        </div>
                      </div>
                      
                      <div className="p-8">
                        <h3 className="text-2xl font-bold text-white mb-4">{product.name}</h3>
                        <div className="flex justify-between items-center mb-6">
                          <div>
                            <p className="text-white/70 text-sm">Precio del boleto</p>
                            <p className="text-2xl font-bold text-white">${product.price.toLocaleString()}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-white/70 text-sm">Boletos vendidos</p>
                            <p className="text-xl font-semibold text-amber-400">{product.tickets}</p>
                          </div>
                        </div>
                        
                        <div className="flex space-x-4">
                          <Button className="flex-1">
                            {t('home.featured.buyNow')}
                          </Button>
                          <Button variant="outline" className="border-white/30 text-white hover:bg-white/10">
                            Ver 3D
                          </Button>
                        </div>
                      </div>
                    </Card>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Carousel Indicators */}
            <div className="flex justify-center mt-8 space-x-2">
              {featuredProducts.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-300 ${
                    index === currentSlide ? 'bg-purple-500 w-8' : 'bg-white/30'
                  }`}
                />
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-900/50 to-amber-900/50 backdrop-blur-md">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            {t('home.cta.title')}
          </h2>
          <p className="text-xl text-white/80 mb-8">
            {t('home.cta.description')}
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/catalog">
              <Button size="xl" className="group">
                <Trophy className="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform" />
                {t('home.cta.viewCatalog')}
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Button>
            </Link>
            <Link to="/defi">
              <Button variant="secondary" size="xl">
                {t('home.cta.exploreDeFi')}
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;