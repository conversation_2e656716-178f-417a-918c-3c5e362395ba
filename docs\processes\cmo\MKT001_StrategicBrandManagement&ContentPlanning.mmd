graph TD
    A[Start: Business/Product Strategy Input from PLN001/DEV001] --> B{Brand Discovery & Definition};
    B -- Research via MarketIntelHarvesterAgent --> Ba(Market/Competitor/Audience Insights);
    Ba --> B;
    B --> C(Visual Identity Definition);
    B --> D(Brand Messaging & Positioning for Productized Services);
    
    D --> E{Content Strategy Formulation};
    E -- SEO Insights via SEOKeywordStrategistAgent --> Ea(Keyword Strategy & Content Gaps);
    Ea --> E;
    E -- Audience Personas via AudiencePersonaCrafterAgent --> Eb(Persona-Driven Themes);
    Eb --> E;

    E --> F(Content Planning & Calendarization via ContentRoadmapPlannerAgent);
    F --> G[Content Briefs for MKT002];

    C --> H(Brand Guidelines Creation via BrandIdentityArchitectAgent);
    H --> I[Disseminate Brand Guidelines];

    J[Track KPIs via BrandPerformanceAnalystAgent] --> K{Performance Review & Reporting};
    K --> A; %% PDCA Loop
    F -- Monitored by --> J;
    I -- Enforces for --> F;

    %% External Interactions & Outputs
    G --> X1[MKT002: Content Generation];
    H --> X2[All Marketing & Sales Processes];
    K --> X3[Marketing Executive Dashboards/Reports];
    X4[OBS001] --> J;

%% Placeholder - Details agent interactions (MCPs) and specific data flows.
