---
trigger: always_on
---

# Python Script Execution & Dependency Management Standards

## 1. Script Execution Protocol

### 1.1. Primary Execution Method: UV Run

**MANDATORY**: All Python scripts in the ESTRATIX project MUST be executed using `uv run` for consistent dependency management and environment isolation.

```bash
# Standard execution pattern
uv run [script_name].py

# With arguments
uv run [script_name].py --arg1 value1 --arg2 value2

# Module execution
uv run -m [module_name]

# Development server execution
uv run --dev [script_name].py
```

### 1.2. Execution Environment Requirements

- **Python Version**: 3.11+ (managed via uv)
- **Virtual Environment**: Automatically managed by uv
- **Dependency Resolution**: Handled by uv with pyproject.toml
- **Lock File**: uv.lock must be committed to repository

### 1.3. Script Categories & Execution Patterns

#### Development Scripts
```bash
# FastAPI development server
uv run --dev src/main.py

# Testing scripts
uv run pytest tests/
uv run pytest tests/unit/
uv run pytest tests/integration/

# Code quality checks
uv run ruff check src/
uv run mypy src/
uv run black src/
```

#### Production Scripts
```bash
# Production server
uv run gunicorn src.main:app

# Database migrations
uv run alembic upgrade head

# Background tasks
uv run celery worker -A src.tasks
```

#### Agent & Tool Scripts
```bash
# Agent execution
uv run src/agents/cto_command_office.py

# Tool testing
uv run src/tools/document_processor.py --test

# Workflow execution
uv run src/workflows/bootstrap_project.py
```

## 2. Dependency Management Standards

### 2.1. Project Configuration (pyproject.toml)

**REQUIRED**: Every ESTRATIX project MUST have a properly configured pyproject.toml:

```toml
[project]
name = "estratix-[component-name]"
version = "0.1.0"
description = "ESTRATIX [Component Description]"
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.0",
    "pydantic>=2.5.0",
    "pydantic-ai>=0.0.13",
    "crewai>=0.70.0",
    "uvicorn>=0.24.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
    "black>=23.0.0",
]
test = [
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
    "pytest-mock>=3.12.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "ruff>=0.1.0",
    "mypy>=1.7.0",
]
```

### 2.2. Dependency Installation & Updates

```bash
# Install all dependencies
uv sync

# Install with dev dependencies
uv sync --dev

# Add new dependency
uv add [package-name]

# Add dev dependency
uv add --dev [package-name]

# Update dependencies
uv lock --upgrade

# Install specific version
uv add "[package-name]>=1.0.0,<2.0.0"
```

### 2.3. Lock File Management

- **uv.lock**: MUST be committed to repository
- **Regeneration**: Run `uv lock` after dependency changes
- **CI/CD**: Use `uv sync --frozen` in production environments
- **Security**: Regular dependency audits with `uv audit`

## 3. Testing Standards

### 3.1. Test Execution Patterns

```bash
# Run all tests
uv run pytest

# Run with coverage
uv run pytest --cov=src --cov-report=html

# Run specific test file
uv run pytest tests/test_document_processor.py

# Run with markers
uv run pytest -m "unit"
uv run pytest -m "integration"
uv run pytest -m "slow"

# Parallel execution
uv run pytest -n auto
```

### 3.2. Test Configuration (pytest.ini)

```ini
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-fail-under=80
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    agent: Agent-specific tests
    tool: Tool-specific tests
```

## 4. Code Quality & Linting

### 4.1. Automated Code Quality Checks

```bash
# Format code
uv run black src/ tests/

# Lint code
uv run ruff check src/ tests/

# Type checking
uv run mypy src/

# Security scanning
uv run bandit -r src/

# Import sorting
uv run isort src/ tests/
```

### 4.2. Pre-commit Hooks

**REQUIRED**: All repositories MUST use pre-commit hooks:

```yaml
# .pre-commit-config.yaml
repos:
  - repo: local
    hooks:
      - id: ruff-check
        name: ruff-check
        entry: uv run ruff check
        language: system
        types: [python]
      - id: ruff-format
        name: ruff-format
        entry: uv run ruff format
        language: system
        types: [python]
      - id: mypy
        name: mypy
        entry: uv run mypy
        language: system
        types: [python]
```

## 5. Development Workflow Integration

### 5.1. IDE Integration

**VS Code Settings (.vscode/settings.json)**:
```json
{
    "python.defaultInterpreterPath": ".venv/bin/python",
    "python.terminal.activateEnvironment": false,
    "python.testing.pytestEnabled": true,
    "python.testing.pytestArgs": ["tests"],
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.formatting.provider": "black",
    "python.typeChecking": "mypy"
}
```

### 5.2. Task Automation (.vscode/tasks.json)

```json
{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "uv: run tests",
            "type": "shell",
            "command": "uv run pytest",
            "group": "test",
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": false,
                "panel": "shared"
            }
        },
        {
            "label": "uv: run dev server",
            "type": "shell",
            "command": "uv run --dev src/main.py",
            "group": "build",
            "isBackground": true
        },
        {
            "label": "uv: code quality",
            "type": "shell",
            "command": "uv run ruff check src/ && uv run mypy src/",
            "group": "build"
        }
    ]
}
```

## 6. CI/CD Integration

### 6.1. GitHub Actions Workflow

```yaml
name: ESTRATIX CI/CD

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          version: "latest"
      - name: Set up Python
        run: uv python install 3.11
      - name: Install dependencies
        run: uv sync --all-extras --dev
      - name: Run tests
        run: uv run pytest --cov --cov-report=xml
      - name: Run linting
        run: uv run ruff check .
      - name: Run type checking
        run: uv run mypy src/
```

## 7. Performance & Monitoring

### 7.1. Script Performance Monitoring

```bash
# Profile script execution
uv run python -m cProfile -o profile.stats script.py

# Memory profiling
uv run python -m memory_profiler script.py

# Execution timing
time uv run script.py
```

### 7.2. Dependency Vulnerability Scanning

```bash
# Security audit
uv audit

# Check for outdated packages
uv tree --outdated

# Update security patches
uv sync --upgrade
```

## 8. Troubleshooting & Best Practices

### 8.1. Common Issues & Solutions

**Issue**: Script fails with import errors
**Solution**: 
```bash
uv sync  # Ensure all dependencies are installed
uv run python -c "import sys; print(sys.path)"  # Check Python path
```

**Issue**: Version conflicts
**Solution**:
```bash
uv lock --upgrade  # Regenerate lock file
uv sync --reinstall  # Clean reinstall
```

**Issue**: Performance issues
**Solution**:
```bash
uv run --python 3.11 script.py  # Use specific Python version
uv run --no-cache script.py  # Disable caching
```

### 8.2. Best Practices

1. **Always use uv run**: Never execute Python scripts directly
2. **Lock file hygiene**: Commit uv.lock, regenerate after changes
3. **Environment isolation**: Let uv manage virtual environments
4. **Dependency pinning**: Use version constraints in pyproject.toml
5. **Regular updates**: Keep dependencies current with security patches
6. **Testing integration**: Include uv run in all test commands
7. **Documentation**: Document script usage with uv run examples

## 9. Compliance & Enforcement

### 9.1. Mandatory Checks

- All Python execution MUST use `uv run`
- All projects MUST have pyproject.toml
- All dependencies MUST be locked in uv.lock
- All tests MUST run via `uv run pytest`
- All code quality checks MUST use `uv run`

### 9.2. Automated Enforcement

- Pre-commit hooks enforce uv usage
- CI/CD pipelines validate uv configuration
- Code review checklist includes uv compliance
- Documentation examples use uv run exclusively

---

**ESTRATIX Engineering Excellence - Powered by UV**
© 2025 ESTRATIX - Autonomous Operations Framework