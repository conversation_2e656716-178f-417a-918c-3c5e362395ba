# Schedule Management Plan: [Project Name]

## Document Control
*   **Project Name:** `[Full Official Project Name]`
*   **Project ID:** `[Unique Project Identifier]`
*   **Client Name (if applicable):** `[Client Name]`
*   **Client ID (ESTRATIX):** `[Client_ID]`
*   **Schedule Version:** `[e.g., 1.0, 1.1, Baseline 1.0]`
*   **Date Prepared:** `[YYYY-MM-DD]`
*   **Prepared By:** `[Name/Role of Preparer / ESTRATIX Agent ID (e.g., CPO_AXXX_Scheduler)]`
*   **Document Status:** `[e.g., Draft, Submitted for Review, Approved, Baseline]`
*   **Security Classification:** `[e.g., ESTRATIX Internal, Client Confidential]`
*   **Related Project Plan ID:** `[Link to or ID of Project_Plan_Template.md]`

## 1. Introduction
    *   `[This Schedule Management Plan outlines the policies, procedures, and documentation for planning, developing, managing, executing, and controlling the project schedule. It is a component of the overall Project Management Plan.]`

## 2. Schedule Development Approach
    *   **Scheduling Methodology:** `[e.g., Critical Path Method (CPM), Agile (Iterations/Sprints), Rolling Wave Planning. Specify if a hybrid approach is used.]`
    *   **Scheduling Tool(s):** `[e.g., ESTRATIX Project Management Suite, Microsoft Project, Jira, Asana, Spreadsheet.]`
    *   **Units of Measure for Duration:** `[e.g., Hours, Days, Weeks.]`
    *   **Level of Detail for Activities:** `[Describe the granularity of activities in the schedule (e.g., work packages detailed to 8-80 hours of effort).]`

## 3. Roles and Responsibilities
    *   `[Define roles and responsibilities for schedule development, maintenance, and control.]`
    *   **Project Manager:** `[e.g., Overall responsibility for developing, maintaining, and controlling the schedule; reporting on schedule performance.]`
    *   **Project Team Members/Agents:** `[e.g., Provide input for activity definition, duration estimates, and resource requirements; update progress on assigned tasks.]`
    *   **Scheduler (if applicable):** `[e.g., ESTRATIX CPO_AXXX_Scheduler - Develops and maintains the detailed schedule using specialized tools.]`
    *   **Project Sponsor:** `[e.g., Approves the schedule baseline and significant changes.]`

## 4. Activity Definition
    *   `[Describe how project activities are identified and decomposed from the Work Breakdown Structure (WBS). Reference the WBS_Dictionary_Template.md.]`
    *   `[Each activity should have a clear description, unique ID, assigned resources, and estimated duration.]`

## 5. Activity Sequencing
    *   `[Describe how dependencies (logical relationships) between activities are determined and documented.]`
    *   **Types of Dependencies:** `[e.g., Mandatory (hard logic), Discretionary (soft logic/preferred), External, Internal. Specify use of leads and lags.]`

## 6. Resource Estimation for Activities
    *   `[Describe how the type and quantity of resources (human/agent, equipment, materials) required for each activity are estimated. This may link to a separate Resource Management Plan.]`
    *   `[Specify if ESTRATIX agent capabilities and availability are factored in.]`

## 7. Duration Estimation for Activities
    *   `[Describe the methods used to estimate the duration of activities (e.g., Analogous, Parametric, Three-Point (PERT), Expert Judgment, Reserve Analysis).]`
    *   `[Consider resource availability, skill levels, and potential risks when estimating durations.]`

## 8. Schedule Baseline
    *   **Schedule Baseline Version:** `[e.g., Baseline 1.0]`
    *   **Date Approved:** `[YYYY-MM-DD]`
    *   **Approved By:** `[Name/Role of Approver, e.g., Project Sponsor]`
    *   **Key Baseline Dates:**
        *   Project Start Date: `[YYYY-MM-DD]`
        *   Project Finish Date: `[YYYY-MM-DD]`
        *   Total Project Duration: `[Calculated Duration]`
    *   `[The schedule baseline is the approved version of the project schedule against which performance is measured. It includes approved start and finish dates for all activities and milestones.]`

## 9. Project Schedule (Detailed)
    *   `[This section can either contain a detailed table of the schedule or provide a link/reference to the schedule maintained in a dedicated scheduling tool. If linking, ensure the tool output is version-controlled and accessible as per ESTRATIX guidelines.]`
    *   **If providing a table (example structure):**
        | WBS ID | Activity Name        | Duration | Start Date | Finish Date| Predecessors | Successors | Resources (Agent/Role) | Effort (e.g., PDs) | % Complete | Status      | Notes |
        | :----- | :------------------- | :------- | :--------- | :--------- | :----------- | :--------- | :--------------------- | :----------------- | :--------- | :---------- | :---- |
        | `1.1`  | `Project Initiation` | `[X days]`| `[...]`    | `[...]`    |              | `[1.2]`    | `[CPO_AXXX_PM]`        | `[Y PDs]`          | `[100%]`   | `Completed` |       |
        | `1.2`  | `Planning`           | `[Z days]`| `[...]`    | `[...]`    | `[1.1]`      | `[1.3]`    | `[CPO_AXXX_PM, Team]`  | `[A PDs]`          | `[0%]`     | `Not Started`|       |
        | `...`  | `...`                | `...`    | `...`      | `...`      | `...`        | `...`      | `...`                  | `...`              | `...`      | `...`       |       |
    *   **Critical Path:** `[Identify the sequence of activities that represents the longest path through the project, which determines the shortest possible project duration. List the activities on the critical path or highlight them in the schedule representation (e.g., Gantt chart link).]`

## 10. Key Milestones List
    *   `[List major project milestones, their descriptions, planned completion dates, actual completion dates (once achieved), and current status.]`
    | Milestone ID | Milestone Description                               | Planned Date | Actual Date | Status (Not Started, In Progress, Completed, Delayed) | Responsible (Agent/Role) |
    | :----------- | :-------------------------------------------------- | :----------- | :---------- | :---------------------------------------------------- | :----------------------- |
    | `[M01]`      | `[e.g., Project Charter Approved]`                  | `[YYYY-MM-DD]`| `[...]`     | `[...]`                                               | `[Project Sponsor]`      |
    | `[M02]`      | `[e.g., Detailed Design Phase Complete]`            | `[YYYY-MM-DD]`| `[...]`     | `[...]`                                               | `[CTO_AXXX_TechLead]`    |
    | `[M03]`      | `[e.g., User Acceptance Testing (UAT) Sign-off]`    | `[YYYY-MM-DD]`| `[...]`     | `[...]`                                               | `[Client Product Owner]` |
    | `[...]`      | `[...]`                                             | `[...]`      | `[...]`     | `[...]`                                               | `[...]`                  |

## 11. Schedule Management and Control
    *   **11.1. Schedule Monitoring & Reporting:**
        *   **Reporting Frequency:** `[e.g., Weekly, Bi-weekly, Monthly, at key milestones.]`
        *   **Reporting Format:** `[Describe the format of schedule reports (e.g., Gantt charts, milestone trend analysis, S-curves, EVM metrics). Link to ../05_CommonTemplates/Status_Report_Template.md.]`
        *   **Key Metrics Tracked:** `[e.g., Schedule Variance (SV), Schedule Performance Index (SPI), % Complete, Milestone Achievement, Critical Path Slippage.]`
    *   **11.2. Variance Analysis:**
        *   **Control Thresholds:** `[Define thresholds for schedule variances that trigger action (e.g., +/- 10% variance on critical path activities, milestone slippage > 5 days).]`
        *   **Corrective Actions:** `[Describe the process for analyzing schedule variances, identifying root causes, and implementing corrective/preventive actions.]`
    *   **11.3. Schedule Change Control Process:**
        *   `[Describe how changes to the schedule baseline will be managed. All changes must follow the integrated project change control process. Reference the overall Project Change Management Plan and the ../05_CommonTemplates/Change_Request_Form_Template.md.]`
        *   `[Specify approval levels for different types of schedule changes.]`
    *   **11.4. Schedule Compression Techniques (if applicable):**
        *   `[Briefly describe conditions under which techniques like Crashing or Fast-Tracking might be considered, and the approval process for their use.]`

## 12. Schedule Assumptions and Constraints
    *   **Assumptions:**
        *   `[List all assumptions made during schedule development (e.g., resource availability as planned, ESTRATIX agent productivity rates, timely client approvals, standard working hours/days).]`
        *   Assumption 1: `[...]`
    *   **Constraints:**
        *   `[List any constraints impacting the schedule (e.g., fixed deadlines, resource limitations, pre-defined phase gates, client-imposed blackout periods).]`
        *   Constraint 1: `[...]`

## 13. Schedule Plan Review and Updates
    *   `[Describe the process and frequency for reviewing and updating the schedule and this management plan (e.g., reviewed weekly by PM, monthly with stakeholders, updated upon approved changes, re-baselined at major phase gates if significant deviations occur).]`

## Appendix (Optional)
    *   `[e.g., Detailed Gantt Chart (if not linked directly), Resource Histograms, Network Diagram, Basis of Estimates for durations.]`

