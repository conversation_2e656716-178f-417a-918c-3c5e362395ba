import { FastifyRequest, FastifyReply } from 'fastify';
import jwt from 'jsonwebtoken';
import { environment } from '@/config/environment';
import { logger } from '@/utils/logger';

export interface UserPayload {
  id: string;
  email: string;
  organizationId: string;
  role: string;
}

export interface AuthenticatedRequest extends FastifyRequest {
  authUser?: UserPayload;
}

export async function authenticateToken(
  request: FastifyRequest,
  reply: FastifyReply
): Promise<void> {
  try {
    const authHeader = request.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return reply.status(401).send({
        error: 'Access token required',
        code: 'MISSING_TOKEN'
      });
    }

    const decoded = jwt.verify(token, environment.jwt.secret) as any;
    (request as AuthenticatedRequest).authUser = {
      id: decoded.id,
      email: decoded.email,
      organizationId: decoded.organizationId,
      role: decoded.role
    };

    logger.debug('Token authenticated successfully', {
      userId: decoded.id,
      organizationId: decoded.organizationId
    });
  } catch (error) {
    logger.error('Token authentication failed', { error });
    return reply.status(403).send({
      error: 'Invalid or expired token',
      code: 'INVALID_TOKEN'
    });
  }
}

export async function requireRole(roles: string[]) {
  return async function(request: FastifyRequest, reply: FastifyReply) {
    const user = (request as AuthenticatedRequest).authUser;
    
    if (!user) {
      return reply.status(401).send({
        error: 'Authentication required',
        code: 'UNAUTHENTICATED'
      });
    }

    if (!roles.includes(user.role)) {
      return reply.status(403).send({
        error: 'Insufficient permissions',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: roles,
        current: user.role
      });
    }
  };
}