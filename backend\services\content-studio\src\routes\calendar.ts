import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { logger } from '../utils/logger';

// Validation schemas
const calendarEventSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().optional(),
  contentId: z.string().optional(),
  type: z.enum(['content_publish', 'campaign_launch', 'review_deadline', 'meeting', 'other']),
  startDate: z.string().datetime(),
  endDate: z.string().datetime().optional(),
  allDay: z.boolean().default(false),
  platform: z.string().optional(),
  status: z.enum(['scheduled', 'published', 'cancelled', 'failed']).default('scheduled'),
  metadata: z.object({
    campaignId: z.string().optional(),
    assignedTo: z.array(z.string()).optional(),
    priority: z.enum(['low', 'medium', 'high']).optional(),
    tags: z.array(z.string()).optional(),
  }).optional(),
});

const updateEventSchema = calendarEventSchema.partial();

const calendarQuerySchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  type: z.string().optional(),
  platform: z.string().optional(),
  status: z.string().optional(),
  view: z.enum(['month', 'week', 'day', 'agenda']).default('month'),
});

const bulkScheduleSchema = z.object({
  contentIds: z.array(z.string()),
  schedule: z.object({
    startDate: z.string().datetime(),
    frequency: z.enum(['daily', 'weekly', 'bi-weekly', 'monthly']),
    interval: z.number().min(1).default(1),
    platforms: z.array(z.string()),
    timeSlots: z.array(z.string()), // HH:MM format
  }),
});

export const calendarRoutes: FastifyPluginAsync = async (fastify) => {
  // Get calendar events
  fastify.get('/', {
    schema: {
      description: 'Get calendar events for a date range',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      querystring: calendarQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                events: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      title: { type: 'string' },
                      description: { type: 'string' },
                      contentId: { type: 'string' },
                      type: { type: 'string' },
                      startDate: { type: 'string' },
                      endDate: { type: 'string' },
                      allDay: { type: 'boolean' },
                      platform: { type: 'string' },
                      status: { type: 'string' },
                      metadata: { type: 'object' },
                    },
                  },
                },
                summary: {
                  type: 'object',
                  properties: {
                    totalEvents: { type: 'number' },
                    byType: { type: 'object' },
                    byStatus: { type: 'object' },
                    byPlatform: { type: 'object' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const query = calendarQuerySchema.parse(request.query);
      const userId = request.user?.id;

      // Mock calendar events
      const mockEvents = [
        {
          id: '1',
          title: 'Publish Blog Post: AI in Marketing',
          description: 'Scheduled publication of our latest blog post about AI trends',
          contentId: 'content-123',
          type: 'content_publish',
          startDate: new Date().toISOString(),
          endDate: null,
          allDay: false,
          platform: 'blog',
          status: 'scheduled',
          metadata: {
            priority: 'high',
            tags: ['ai', 'marketing'],
          },
        },
        {
          id: '2',
          title: 'Social Media Campaign Launch',
          description: 'Launch of Q4 social media campaign',
          type: 'campaign_launch',
          startDate: new Date(Date.now() + 86400000).toISOString(), // Tomorrow
          endDate: new Date(Date.now() + 86400000 * 7).toISOString(), // Next week
          allDay: true,
          platform: 'instagram',
          status: 'scheduled',
          metadata: {
            campaignId: 'campaign-456',
            priority: 'medium',
            assignedTo: ['user-1', 'user-2'],
          },
        },
      ];

      // Filter events by date range
      const startDate = new Date(query.startDate);
      const endDate = new Date(query.endDate);
      
      const filteredEvents = mockEvents.filter(event => {
        const eventStart = new Date(event.startDate);
        return eventStart >= startDate && eventStart <= endDate;
      });

      // Apply additional filters
      let finalEvents = filteredEvents;
      if (query.type) {
        finalEvents = finalEvents.filter(event => event.type === query.type);
      }
      if (query.platform) {
        finalEvents = finalEvents.filter(event => event.platform === query.platform);
      }
      if (query.status) {
        finalEvents = finalEvents.filter(event => event.status === query.status);
      }

      // Generate summary
      const summary = {
        totalEvents: finalEvents.length,
        byType: finalEvents.reduce((acc, event) => {
          acc[event.type] = (acc[event.type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        byStatus: finalEvents.reduce((acc, event) => {
          acc[event.status] = (acc[event.status] || 0) + 1;
          return acc;
        }, {} as Record<string, number>),
        byPlatform: finalEvents.reduce((acc, event) => {
          if (event.platform) {
            acc[event.platform] = (acc[event.platform] || 0) + 1;
          }
          return acc;
        }, {} as Record<string, number>),
      };

      return {
        success: true,
        data: {
          events: finalEvents,
          summary,
        },
      };
    } catch (error) {
      logger.error('Error fetching calendar events:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch calendar events',
      };
    }
  });

  // Create calendar event
  fastify.post('/', {
    schema: {
      description: 'Create a new calendar event',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      body: calendarEventSchema,
    },
  }, async (request, reply) => {
    try {
      const eventData = calendarEventSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock event creation
      const newEvent = {
        id: Date.now().toString(),
        ...eventData,
        userId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Calendar event created: ${newEvent.id}`);

      reply.code(201);
      return {
        success: true,
        data: newEvent,
      };
    } catch (error) {
      logger.error('Error creating calendar event:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to create calendar event',
      };
    }
  });

  // Update calendar event
  fastify.put('/:id', {
    schema: {
      description: 'Update a calendar event',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
      body: updateEventSchema,
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const updateData = updateEventSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock event update
      const updatedEvent = {
        id,
        ...updateData,
        updatedAt: new Date().toISOString(),
      };

      logger.info(`Calendar event updated: ${id}`);

      return {
        success: true,
        data: updatedEvent,
      };
    } catch (error) {
      logger.error('Error updating calendar event:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to update calendar event',
      };
    }
  });

  // Delete calendar event
  fastify.delete('/:id', {
    schema: {
      description: 'Delete a calendar event',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' },
        },
        required: ['id'],
      },
    },
  }, async (request, reply) => {
    try {
      const { id } = request.params as { id: string };
      const userId = request.user?.id;

      // Mock event deletion
      logger.info(`Calendar event deleted: ${id}`);

      reply.code(204);
      return;
    } catch (error) {
      logger.error('Error deleting calendar event:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to delete calendar event',
      };
    }
  });

  // Bulk schedule content
  fastify.post('/bulk-schedule', {
    schema: {
      description: 'Bulk schedule multiple content pieces',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      body: bulkScheduleSchema,
    },
  }, async (request, reply) => {
    try {
      const scheduleData = bulkScheduleSchema.parse(request.body);
      const userId = request.user?.id;

      // Mock bulk scheduling logic
      const scheduledEvents = [];
      const { contentIds, schedule } = scheduleData;
      
      let currentDate = new Date(schedule.startDate);
      
      for (let i = 0; i < contentIds.length; i++) {
        const contentId = contentIds[i];
        const timeSlot = schedule.timeSlots[i % schedule.timeSlots.length];
        const platform = schedule.platforms[i % schedule.platforms.length];
        
        // Set time
        const [hours, minutes] = timeSlot.split(':').map(Number);
        const eventDate = new Date(currentDate);
        eventDate.setHours(hours, minutes, 0, 0);
        
        const event = {
          id: `bulk-${Date.now()}-${i}`,
          title: `Scheduled Content Publication`,
          contentId,
          type: 'content_publish',
          startDate: eventDate.toISOString(),
          platform,
          status: 'scheduled',
          userId,
          createdAt: new Date().toISOString(),
        };
        
        scheduledEvents.push(event);
        
        // Calculate next date based on frequency
        switch (schedule.frequency) {
          case 'daily':
            currentDate.setDate(currentDate.getDate() + schedule.interval);
            break;
          case 'weekly':
            currentDate.setDate(currentDate.getDate() + (7 * schedule.interval));
            break;
          case 'bi-weekly':
            currentDate.setDate(currentDate.getDate() + (14 * schedule.interval));
            break;
          case 'monthly':
            currentDate.setMonth(currentDate.getMonth() + schedule.interval);
            break;
        }
      }

      logger.info(`Bulk scheduled ${scheduledEvents.length} events`);

      return {
        success: true,
        data: {
          scheduledEvents,
          summary: {
            totalScheduled: scheduledEvents.length,
            dateRange: {
              start: scheduledEvents[0]?.startDate,
              end: scheduledEvents[scheduledEvents.length - 1]?.startDate,
            },
          },
        },
      };
    } catch (error) {
      logger.error('Error bulk scheduling:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to bulk schedule content',
      };
    }
  });

  // Get calendar analytics
  fastify.get('/analytics', {
    schema: {
      description: 'Get calendar analytics and insights',
      tags: ['Content Calendar'],
      security: [{ Bearer: [] }],
      querystring: z.object({
        period: z.enum(['week', 'month', 'quarter', 'year']).default('month'),
        startDate: z.string().datetime().optional(),
        endDate: z.string().datetime().optional(),
      }),
    },
  }, async (request, reply) => {
    try {
      const query = request.query as any;
      const userId = request.user?.id;

      // Mock analytics data
      const analytics = {
        overview: {
          totalEvents: 45,
          publishedContent: 32,
          scheduledContent: 13,
          completionRate: 0.89,
        },
        trends: {
          dailyActivity: [
            { date: '2024-01-01', events: 3, published: 2 },
            { date: '2024-01-02', events: 5, published: 4 },
            { date: '2024-01-03', events: 2, published: 2 },
          ],
          platformDistribution: {
            blog: 15,
            instagram: 12,
            twitter: 8,
            linkedin: 6,
            facebook: 4,
          },
          contentTypes: {
            blog_post: 15,
            social_media: 20,
            email: 6,
            ad_copy: 4,
          },
        },
        performance: {
          onTimePublishing: 0.92,
          averageLeadTime: 5.2, // days
          busyDays: ['Monday', 'Wednesday', 'Friday'],
          peakHours: ['09:00', '14:00', '18:00'],
        },
        recommendations: [
          'Consider scheduling more content for Tuesday and Thursday',
          'Your Instagram engagement is highest at 6 PM',
          'Blog posts perform better when published on Monday mornings',
        ],
      };

      return {
        success: true,
        data: analytics,
      };
    } catch (error) {
      logger.error('Error fetching calendar analytics:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to fetch calendar analytics',
      };
    }
  });
};