import { v4 as uuidv4 } from 'uuid';
import { OnboardingFlow, OnboardingStep, CreateOnboardingFlowRequest } from '@/types';
import { logger } from '@/utils/logger';
import { sanitizeString } from '@/utils/validation';

export class OnboardingService {
  private flows: Map<string, OnboardingFlow> = new Map();
  private templates: Map<string, OnboardingStep[]> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Onboarding Service...');
      
      // Initialize templates
      await this.initializeTemplates();
      
      // Generate mock onboarding flows for development
      await this.generateMockFlows();
      
      this.isInitialized = true;
      logger.info('Onboarding Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Onboarding Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Onboarding Service...');
    this.flows.clear();
    this.templates.clear();
    this.isInitialized = false;
  }

  async createOnboardingFlow(
    organizationId: string,
    data: CreateOnboardingFlowRequest
  ): Promise<OnboardingFlow> {
    try {
      const flowId = uuidv4();
      const now = new Date();

      // Get template steps or use custom steps
      let steps: OnboardingStep[];
      if (data.customSteps && data.customSteps.length > 0) {
        steps = data.customSteps.map((step, index) => ({
          ...step,
          id: uuidv4(),
          status: 'pending',
          order: index + 1
        }));
      } else {
        const templateSteps = this.templates.get(data.type) || [];
        steps = templateSteps.map(step => ({
          ...step,
          id: uuidv4(),
          status: 'pending'
        }));
      }

      const flow: OnboardingFlow = {
        id: flowId,
        clientId: data.clientId,
        organizationId,
        type: data.type,
        status: 'not_started',
        currentStep: 0,
        totalSteps: steps.length,
        steps,
        assignedTo: data.assignedTo,
        createdAt: now,
        updatedAt: now
      };

      this.flows.set(flowId, flow);

      logger.info('Onboarding flow created successfully', {
        flowId,
        clientId: data.clientId,
        organizationId,
        type: data.type,
        totalSteps: steps.length
      });

      return flow;
    } catch (error) {
      logger.error('Failed to create onboarding flow', { error, data });
      throw error;
    }
  }

  async getOnboardingFlow(flowId: string, organizationId: string): Promise<OnboardingFlow | null> {
    const flow = this.flows.get(flowId);
    
    if (!flow || flow.organizationId !== organizationId) {
      return null;
    }

    return flow;
  }

  async updateOnboardingFlow(
    flowId: string,
    organizationId: string,
    updates: Partial<Pick<OnboardingFlow, 'status' | 'assignedTo'>>
  ): Promise<OnboardingFlow | null> {
    try {
      const flow = await this.getOnboardingFlow(flowId, organizationId);
      
      if (!flow) {
        return null;
      }

      const updatedFlow: OnboardingFlow = {
        ...flow,
        status: updates.status || flow.status,
        assignedTo: updates.assignedTo !== undefined ? updates.assignedTo : flow.assignedTo,
        updatedAt: new Date()
      };

      // Update timestamps based on status changes
      if (updates.status === 'in_progress' && flow.status === 'not_started') {
        updatedFlow.startedAt = new Date();
      }
      
      if (updates.status === 'completed' && flow.status !== 'completed') {
        updatedFlow.completedAt = new Date();
      }

      this.flows.set(flowId, updatedFlow);

      logger.info('Onboarding flow updated successfully', {
        flowId,
        organizationId,
        changes: Object.keys(updates)
      });

      return updatedFlow;
    } catch (error) {
      logger.error('Failed to update onboarding flow', { error, flowId, updates });
      throw error;
    }
  }

  async completeStep(
    flowId: string,
    stepId: string,
    organizationId: string,
    completedBy: string,
    notes?: string
  ): Promise<OnboardingFlow | null> {
    try {
      const flow = await this.getOnboardingFlow(flowId, organizationId);
      
      if (!flow) {
        return null;
      }

      const stepIndex = flow.steps.findIndex(step => step.id === stepId);
      if (stepIndex === -1) {
        throw new Error('Step not found');
      }

      const step = flow.steps[stepIndex];
      if (step.status === 'completed') {
        throw new Error('Step is already completed');
      }

      // Update step
      const updatedStep: OnboardingStep = {
        ...step,
        status: 'completed',
        completedAt: new Date(),
        completedBy,
        notes
      };

      const updatedSteps = [...flow.steps];
      updatedSteps[stepIndex] = updatedStep;

      // Calculate progress
      const completedSteps = updatedSteps.filter(s => s.status === 'completed').length;
      const currentStep = Math.max(flow.currentStep, stepIndex + 1);
      
      // Determine flow status
      let flowStatus: OnboardingFlow['status'] = flow.status;
      if (completedSteps === flow.totalSteps) {
        flowStatus = 'completed';
      } else if (flowStatus === 'not_started') {
        flowStatus = 'in_progress';
      }

      const updatedFlow: OnboardingFlow = {
        ...flow,
        steps: updatedSteps,
        currentStep,
        status: flowStatus,
        updatedAt: new Date(),
        startedAt: flow.startedAt || (flowStatus === 'in_progress' ? new Date() : undefined),
        completedAt: flowStatus === 'completed' ? new Date() : undefined
      };

      this.flows.set(flowId, updatedFlow);

      logger.info('Onboarding step completed successfully', {
        flowId,
        stepId,
        stepName: step.name,
        completedBy,
        progress: `${completedSteps}/${flow.totalSteps}`
      });

      return updatedFlow;
    } catch (error) {
      logger.error('Failed to complete onboarding step', { error, flowId, stepId });
      throw error;
    }
  }

  async skipStep(
    flowId: string,
    stepId: string,
    organizationId: string,
    skippedBy: string,
    reason?: string
  ): Promise<OnboardingFlow | null> {
    try {
      const flow = await this.getOnboardingFlow(flowId, organizationId);
      
      if (!flow) {
        return null;
      }

      const stepIndex = flow.steps.findIndex(step => step.id === stepId);
      if (stepIndex === -1) {
        throw new Error('Step not found');
      }

      const step = flow.steps[stepIndex];
      if (step.required) {
        throw new Error('Cannot skip required step');
      }

      if (step.status === 'completed' || step.status === 'skipped') {
        throw new Error('Step is already completed or skipped');
      }

      // Update step
      const updatedStep: OnboardingStep = {
        ...step,
        status: 'skipped',
        completedAt: new Date(),
        completedBy: skippedBy,
        notes: reason
      };

      const updatedSteps = [...flow.steps];
      updatedSteps[stepIndex] = updatedStep;

      // Calculate progress
      const completedOrSkippedSteps = updatedSteps.filter(s => 
        s.status === 'completed' || s.status === 'skipped'
      ).length;
      const currentStep = Math.max(flow.currentStep, stepIndex + 1);
      
      // Determine flow status
      let flowStatus: OnboardingFlow['status'] = flow.status;
      if (completedOrSkippedSteps === flow.totalSteps) {
        flowStatus = 'completed';
      } else if (flowStatus === 'not_started') {
        flowStatus = 'in_progress';
      }

      const updatedFlow: OnboardingFlow = {
        ...flow,
        steps: updatedSteps,
        currentStep,
        status: flowStatus,
        updatedAt: new Date(),
        startedAt: flow.startedAt || (flowStatus === 'in_progress' ? new Date() : undefined),
        completedAt: flowStatus === 'completed' ? new Date() : undefined
      };

      this.flows.set(flowId, updatedFlow);

      logger.info('Onboarding step skipped successfully', {
        flowId,
        stepId,
        stepName: step.name,
        skippedBy,
        reason
      });

      return updatedFlow;
    } catch (error) {
      logger.error('Failed to skip onboarding step', { error, flowId, stepId });
      throw error;
    }
  }

  async getOnboardingFlows(
    organizationId: string,
    filters: {
      status?: OnboardingFlow['status'];
      type?: OnboardingFlow['type'];
      clientId?: string;
      assignedTo?: string;
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: 'asc' | 'desc';
    } = {}
  ): Promise<{ flows: OnboardingFlow[]; total: number; page: number; limit: number }> {
    try {
      let filteredFlows = Array.from(this.flows.values())
        .filter(flow => flow.organizationId === organizationId);

      // Apply filters
      if (filters.status) {
        filteredFlows = filteredFlows.filter(flow => flow.status === filters.status);
      }

      if (filters.type) {
        filteredFlows = filteredFlows.filter(flow => flow.type === filters.type);
      }

      if (filters.clientId) {
        filteredFlows = filteredFlows.filter(flow => flow.clientId === filters.clientId);
      }

      if (filters.assignedTo) {
        filteredFlows = filteredFlows.filter(flow => flow.assignedTo === filters.assignedTo);
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'desc';
      
      filteredFlows.sort((a, b) => {
        let aValue: any = (a as any)[sortBy];
        let bValue: any = (b as any)[sortBy];

        if (aValue instanceof Date) aValue = aValue.getTime();
        if (bValue instanceof Date) bValue = bValue.getTime();

        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
          bValue = bValue.toLowerCase();
        }

        if (sortOrder === 'asc') {
          return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
        } else {
          return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
        }
      });

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      
      const paginatedFlows = filteredFlows.slice(startIndex, endIndex);

      return {
        flows: paginatedFlows,
        total: filteredFlows.length,
        page,
        limit
      };
    } catch (error) {
      logger.error('Failed to get onboarding flows', { error, filters });
      throw error;
    }
  }

  async getOnboardingStats(organizationId: string): Promise<{
    total: number;
    byStatus: Record<OnboardingFlow['status'], number>;
    byType: Record<OnboardingFlow['type'], number>;
    averageCompletionTime: number; // in days
    completionRate: number; // percentage
  }> {
    try {
      const flows = Array.from(this.flows.values())
        .filter(flow => flow.organizationId === organizationId);

      const byStatus: Record<OnboardingFlow['status'], number> = {
        not_started: 0,
        in_progress: 0,
        completed: 0,
        paused: 0,
        cancelled: 0
      };

      const byType: Record<OnboardingFlow['type'], number> = {
        basic: 0,
        premium: 0,
        enterprise: 0,
        custom: 0
      };

      let totalCompletionTime = 0;
      let completedFlows = 0;

      flows.forEach(flow => {
        byStatus[flow.status]++;
        byType[flow.type]++;
        
        if (flow.status === 'completed' && flow.startedAt && flow.completedAt) {
          const completionTime = flow.completedAt.getTime() - flow.startedAt.getTime();
          totalCompletionTime += completionTime / (1000 * 60 * 60 * 24); // Convert to days
          completedFlows++;
        }
      });

      const averageCompletionTime = completedFlows > 0 ? totalCompletionTime / completedFlows : 0;
      const completionRate = flows.length > 0 ? (byStatus.completed / flows.length) * 100 : 0;

      return {
        total: flows.length,
        byStatus,
        byType,
        averageCompletionTime: Math.round(averageCompletionTime * 100) / 100,
        completionRate: Math.round(completionRate * 100) / 100
      };
    } catch (error) {
      logger.error('Failed to get onboarding stats', { error, organizationId });
      throw error;
    }
  }

  private async initializeTemplates(): Promise<void> {
    // Basic onboarding template
    const basicTemplate: OnboardingStep[] = [
      {
        id: '',
        name: 'Welcome & Introduction',
        description: 'Welcome the client and provide an overview of the onboarding process',
        type: 'meeting',
        status: 'pending',
        required: true,
        order: 1,
        estimatedDuration: 30
      },
      {
        id: '',
        name: 'Basic Information Collection',
        description: 'Collect essential client information and preferences',
        type: 'form',
        status: 'pending',
        required: true,
        order: 2,
        estimatedDuration: 15
      },
      {
        id: '',
        name: 'Service Agreement',
        description: 'Review and sign the service agreement',
        type: 'document_upload',
        status: 'pending',
        required: true,
        order: 3,
        estimatedDuration: 20
      }
    ];

    // Premium onboarding template
    const premiumTemplate: OnboardingStep[] = [
      ...basicTemplate,
      {
        id: '',
        name: 'Detailed Requirements Gathering',
        description: 'In-depth discussion of project requirements and goals',
        type: 'meeting',
        status: 'pending',
        required: true,
        order: 4,
        estimatedDuration: 60
      },
      {
        id: '',
        name: 'Project Planning Session',
        description: 'Collaborative planning session to define project scope and timeline',
        type: 'meeting',
        status: 'pending',
        required: true,
        order: 5,
        estimatedDuration: 90
      }
    ];

    this.templates.set('basic', basicTemplate);
    this.templates.set('premium', premiumTemplate);
    this.templates.set('enterprise', premiumTemplate); // Same as premium for now
    this.templates.set('custom', []); // Empty template for custom flows

    logger.debug('Onboarding templates initialized');
  }

  private async generateMockFlows(): Promise<void> {
    const mockFlows: Omit<OnboardingFlow, 'id' | 'createdAt' | 'updatedAt' | 'startedAt' | 'completedAt'>[] = [
      {
        clientId: 'client-1',
        organizationId: 'org-1',
        type: 'premium',
        status: 'in_progress',
        currentStep: 2,
        totalSteps: 5,
        steps: this.templates.get('premium')?.map((step, index) => ({
          ...step,
          id: uuidv4(),
          status: index < 2 ? 'completed' : 'pending'
        })) || [],
        assignedTo: 'user-1'
      }
    ];

    for (const mockFlow of mockFlows) {
      const now = new Date();
      const flow: OnboardingFlow = {
        ...mockFlow,
        id: uuidv4(),
        createdAt: now,
        updatedAt: now,
        startedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
      };
      
      this.flows.set(flow.id, flow);
    }

    logger.debug(`Generated ${mockFlows.length} mock onboarding flows`);
  }
}