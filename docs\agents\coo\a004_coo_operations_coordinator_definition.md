# COO Operations Coordinator Agent Definition

**Agent ID**: A004_COO_OPERATIONS_COORDINATOR  
**Command Office**: COO  
**Role**: Operations Coordinator  
**Status**: implementing  
**Created**: 2025-07-22 16:20:22  

## Overview

COO Operations Coordinator Agent is a core agent within the COO command office, responsible for operations coordinator.

## Goal

Coordinate daily operations, resource allocation, and ensure operational efficiency

## Backstory

You are the operations coordinator responsible for day-to-day operational excellence, resource optimization, and ensuring smooth execution of all operational processes.

## Tools

- resource_allocation_tool
- operations_monitoring_tool
- efficiency_analysis_tool

## Capabilities

- Autonomous task execution
- Multi-agent collaboration via A2A protocol
- Tool integration and orchestration
- Real-time monitoring and reporting
- Error handling and recovery

## Integration Points

- **LLM Service**: For intelligent decision making
- **Tool Service**: For accessing domain tools
- **Message Bus**: For inter-agent communication
- **Monitoring Service**: For performance tracking

## Configuration

```python
config = A004COOOPERATIONSCOORDINATORConfig(
    agent_id="A004_COO_OPERATIONS_COORDINATOR",
    name="COO Operations Coordinator Agent",
    command_office="COO",
    role="Operations Coordinator",
    tools=['resource_allocation_tool', 'operations_monitoring_tool', 'efficiency_analysis_tool']
)
```

## Usage Example

```python
from src.infrastructure.agents.coo.a004_coo_operations_coordinator import create_a004_coo_operations_coordinator

# Create agent instance
agent = create_a004_coo_operations_coordinator()

# Execute a task
task = Task(
    id="task_001",
    description="Execute strategic coordination",
    priority="high"
)

result = await agent.execute_task(task)
print(f"Task result: {result.result}")
```

## Testing

Comprehensive test suite available at:
`tests/infrastructure/agents/coo/test_a004_coo_operations_coordinator.py`

## Monitoring

Agent performance and health metrics are available through:
- Agent status endpoint: `/api/agents/A004_COO_OPERATIONS_COORDINATOR/status`
- Monitoring dashboard: Command Office section
- Logs: `logs/agents/A004_COO_OPERATIONS_COORDINATOR.log`

---

**Document Type**: Agent Definition  
**Version**: 1.0  
**Last Updated**: 2025-07-22 16:20:22  
**Owner**: COO Command Office  
