﻿name: Luxcrafts Production Deployment

on:
  push:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      deployment_type:
        description: 'Deployment type'
        required: true
        default: 'standard'
        type: choice
        options:
        - standard
        - hotfix
        - rollback

env:
  NODE_VERSION: '20'
  PRODUCTION_DOMAIN: 'luxcrafts.co'

jobs:
  # Pre-deployment checks
  pre-deployment:
    runs-on: ubuntu-latest
    outputs:
      should-deploy: ${{ steps.checks.outputs.should-deploy }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run comprehensive tests
        run: |
          npm run check
          npm run lint
          npm test -- --coverage
        env:
          CI: true

      - name: Security audit
        run: npm audit --audit-level=high

      - name: Pre-deployment checks
        id: checks
        run: |
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "âœ… All pre-deployment checks passed"

  # Build and Deploy to Production
  deploy-production:
    needs: pre-deployment
    runs-on: ubuntu-latest
    if: needs.pre-deployment.outputs.should-deploy == 'true'
    environment:
      name: production
      url: https://luxcrafts.co
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build for production
        run: npm run build
        env:
          VITE_APP_ENVIRONMENT: production
          VITE_WALLETCONNECT_PROJECT_ID: ${{ secrets.VITE_WALLETCONNECT_PROJECT_ID }}
          VITE_ALCHEMY_API_KEY: ${{ secrets.VITE_ALCHEMY_API_KEY_PRODUCTION }}
          VITE_INFURA_API_KEY: ${{ secrets.VITE_INFURA_API_KEY_PRODUCTION }}
          VITE_API_BASE_URL: https://api.luxcrafts.co
          VITE_CHAIN_ID: ${{ secrets.VITE_CHAIN_ID_PRODUCTION }}
          VITE_CONTRACT_ADDRESS: ${{ secrets.VITE_CONTRACT_ADDRESS_PRODUCTION }}
          VITE_LUX_TOKEN_ADDRESS: ${{ secrets.VITE_LUX_TOKEN_ADDRESS_PRODUCTION }}
          NODE_OPTIONS: "--max-old-space-size=32768 --max-semi-space-size=1024"

      - name: Create production metadata
        run: |
          cat > dist/deployment-info.json << EOF
          {
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "environment": "production",
            "version": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "buildTime": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "domain": "luxcrafts.co"
          }
          EOF

      - name: Create production health check
        run: |
          cat > dist/health.json << EOF
          {
            "status": "healthy",
            "environment": "production",
            "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "version": "${{ github.sha }}",
            "domain": "luxcrafts.co",
            "services": {
              "frontend": "operational",
              "build": "successful",
              "ssl": "active"
            }
          }
          EOF

      - name: Create deployment package
        run: |
          tar -czf luxcrafts-production-${{ github.sha }}.tar.gz dist/ dokploy.config.json package.json
          echo "ðŸ“¦ Production package created"

      - name: Deploy to Dokploy VPS
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            # Create deployment directory
            RELEASE_DIR="/opt/luxcrafts-production/releases/$(date +%Y%m%d_%H%M%S)"
            mkdir -p $RELEASE_DIR
            cd /opt/luxcrafts-production
            
            # Backup current deployment
            if [ -d "current" ]; then
              cp -r current backup_$(date +%Y%m%d_%H%M%S)
              echo "âœ… Backup created"
            fi
            
            # Download and extract new release
            wget -O luxcrafts-production-${{ github.sha }}.tar.gz "${{ secrets.DEPLOYMENT_ARTIFACT_URL }}"
            tar -xzf luxcrafts-production-${{ github.sha }}.tar.gz -C $RELEASE_DIR/
            
            # Update symlink to new release
            ln -sfn $RELEASE_DIR current
            
            # Deploy with Dokploy
            dokploy deploy --config current/dokploy.config.json --domain luxcrafts.co
            
            echo "ðŸš€ Production deployment initiated"

      - name: Wait for deployment and health check
        run: |
          echo "â³ Waiting for deployment to complete..."
          sleep 120
          
          # Health check with retries
          for i in {1..10}; do
            if curl -f https://luxcrafts.co/health.json; then
              echo "âœ… Production deployment health check passed"
              break
            else
              echo "âš ï¸ Health check attempt $i failed, retrying..."
              sleep 30
            fi
            
            if [ $i -eq 10 ]; then
              echo "âŒ Production deployment health check failed after 10 attempts"
              exit 1
            fi
          done

      - name: Verify SSL and domain configuration
        run: |
          echo "ðŸ”’ Verifying SSL certificate..."
          curl -I https://luxcrafts.co | grep -i "HTTP/2 200" || exit 1
          
          echo "ðŸŒ Verifying www redirect..."
          curl -I https://www.luxcrafts.co | grep -i "location: https://luxcrafts.co" || echo "âš ï¸ WWW redirect not configured"
          
          echo "âœ… Domain configuration verified"

      - name: Cleanup old releases
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USERNAME }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          port: ${{ secrets.PRODUCTION_PORT || 22 }}
          script: |
            cd /opt/luxcrafts-production/releases
            # Keep only the 5 most recent releases
            ls -t | tail -n +6 | xargs -r rm -rf
            echo "ðŸ§¹ Old releases cleaned up"

      - name: Notify deployment success
        run: |
          echo "ðŸŽ‰ Production deployment to luxcrafts.co completed successfully!"
          echo "ðŸŒ Live at: https://luxcrafts.co"
          echo "ðŸ“Š Deployment time: $(date)"
          echo "ðŸ”— Health Check: https://luxcrafts.co/health.json"
