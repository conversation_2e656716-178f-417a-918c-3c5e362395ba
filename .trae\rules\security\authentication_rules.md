# ESTRATIX Agentic Rules: User Authentication

---

## 1. Overview

These rules define the security standards for user authentication and session management within all ESTRATIX applications. The objective is to protect user accounts and prevent unauthorized access.

## 2. Core Principles

- **Defense in Depth**: Employ multiple layers of security. Do not rely on a single mechanism.
- **Secure Defaults**: Frameworks and libraries should be configured for maximum security by default.
- **Principle of Least Privilege**: Authenticated users should only have access to the resources they are explicitly granted.

## 3. Rules

### 3.1. Password Security
- **Rule R-SE-001.1**: Passwords must never be stored in plaintext. They must be hashed using a modern, adaptive hashing algorithm like **bcrypt** or **Argon2**.
- **Rule R-SE-001.2**: Implement rate limiting on login endpoints to mitigate brute-force attacks.

### 3.2. Session Management & JWTs
- **Rule R-SE-001.3**: Use JSON Web Tokens (JWTs) for managing API sessions.
- **Rule R-SE-001.4**: Access tokens must be short-lived (e.g., 15 minutes).
- **Rule R-SE-001.5**: Implement a refresh token mechanism to obtain new access tokens. Refresh tokens must be long-lived, stored securely (e.g., in an `HttpOnly` cookie), and allow for revocation.
- **Rule R-SE-001.6**: The JWT signing key (`JWT_SECRET`) must be a high-entropy, randomly generated string stored securely via environment variables.

### 3.3. Secure Transport & Cookies
- **Rule R-SE-001.7**: All authentication-related communication must occur over HTTPS.
- **Rule R-SE-001.8**: When using cookies to store tokens, they must be configured with the `HttpOnly`, `Secure`, and `SameSite=Strict` (or `Lax`) flags to prevent XSS and CSRF attacks.

### 3.4. Multi-Factor Authentication (MFA)
- **Rule R-SE-001.9**: For applications requiring high security, MFA must be available and encouraged. Support standards like TOTP (Time-based One-Time Password).

## 4. Enforcement

- **Agentic Enforcement**: Agents generating authentication logic will be primed to use bcrypt for hashing, implement JWT refresh token flows, and set secure cookie flags.
- **Security Audits**: Authentication flows will be subject to periodic internal and external security audits.
- **Dependency Scanning**: Automatically scan dependencies for known vulnerabilities in authentication-related libraries.
