import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  CreditCard, Wallet, Shield, CheckCircle, AlertCircle,
  ArrowRight, ArrowLeft, Lock, Globe, Zap, Star,
  Clock, Users, TrendingUp, Award, Gift, ExternalLink,
  Copy, QrCode, Smartphone, Laptop, Eye, EyeOff
} from 'lucide-react';
import Button from '../components/ui/Button';
import Card from '../components/ui/Card';

interface PaymentMethod {
  id: string;
  name: string;
  type: 'web2' | 'web3';
  category: 'card' | 'bank' | 'digital' | 'crypto';
  icon: string;
  description: string;
  fees: {
    percentage: number;
    fixed?: number;
    currency: string;
  };
  processingTime: string;
  limits: {
    min: number;
    max: number;
    currency: string;
  };
  supported: boolean;
  popular: boolean;
  secure: boolean;
  countries?: string[];
}

interface PaymentStep {
  id: number;
  title: string;
  description: string;
  completed: boolean;
  active: boolean;
}

interface Transaction {
  id: string;
  amount: number;
  currency: string;
  method: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  date: string;
  productName: string;
  fees: number;
}

const PaymentGateway: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<'payment' | 'history' | 'methods' | 'security'>('payment');
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [paymentAmount, setPaymentAmount] = useState(100);
  const [selectedCurrency, setSelectedCurrency] = useState('USD');
  const [showCardDetails, setShowCardDetails] = useState(false);
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [showCvv, setShowCvv] = useState(false);
  const [walletAddress, setWalletAddress] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);

  const paymentMethods: PaymentMethod[] = [
    // Web2 Methods
    {
      id: 'visa',
      name: 'Visa',
      type: 'web2',
      category: 'card',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Visa%20credit%20card%20logo%20blue%20white%20payment&image_size=square',
      description: 'Tarjeta de crédito/débito Visa',
      fees: { percentage: 2.9, fixed: 0.30, currency: 'USD' },
      processingTime: 'Instantáneo',
      limits: { min: 1, max: 10000, currency: 'USD' },
      supported: true,
      popular: true,
      secure: true
    },
    {
      id: 'mastercard',
      name: 'Mastercard',
      type: 'web2',
      category: 'card',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Mastercard%20logo%20red%20orange%20circles%20payment&image_size=square',
      description: 'Tarjeta de crédito/débito Mastercard',
      fees: { percentage: 2.9, fixed: 0.30, currency: 'USD' },
      processingTime: 'Instantáneo',
      limits: { min: 1, max: 10000, currency: 'USD' },
      supported: true,
      popular: true,
      secure: true
    },
    {
      id: 'wise',
      name: 'Wise',
      type: 'web2',
      category: 'digital',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Wise%20transfer%20logo%20green%20modern%20fintech&image_size=square',
      description: 'Transferencias internacionales con Wise',
      fees: { percentage: 0.5, currency: 'USD' },
      processingTime: '1-2 días hábiles',
      limits: { min: 10, max: 50000, currency: 'USD' },
      supported: true,
      popular: false,
      secure: true,
      countries: ['US', 'EU', 'UK', 'CA', 'AU']
    },
    {
      id: 'payoneer',
      name: 'Payoneer',
      type: 'web2',
      category: 'digital',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Payoneer%20logo%20orange%20global%20payments&image_size=square',
      description: 'Pagos globales con Payoneer',
      fees: { percentage: 1.2, currency: 'USD' },
      processingTime: '2-3 días hábiles',
      limits: { min: 25, max: 25000, currency: 'USD' },
      supported: true,
      popular: false,
      secure: true,
      countries: ['Global']
    },
    {
      id: 'paypal',
      name: 'PayPal',
      type: 'web2',
      category: 'digital',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=PayPal%20logo%20blue%20digital%20payments&image_size=square',
      description: 'Pagos seguros con PayPal',
      fees: { percentage: 3.4, fixed: 0.30, currency: 'USD' },
      processingTime: 'Instantáneo',
      limits: { min: 1, max: 5000, currency: 'USD' },
      supported: true,
      popular: true,
      secure: true
    },
    // Web3 Methods
    {
      id: 'ethereum',
      name: 'Ethereum (ETH)',
      type: 'web3',
      category: 'crypto',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Ethereum%20ETH%20logo%20blue%20cryptocurrency&image_size=square',
      description: 'Pago con Ethereum en red principal',
      fees: { percentage: 0, currency: 'ETH' },
      processingTime: '2-5 minutos',
      limits: { min: 0.001, max: 1000, currency: 'ETH' },
      supported: true,
      popular: true,
      secure: true
    },
    {
      id: 'polygon',
      name: 'Polygon (MATIC)',
      type: 'web3',
      category: 'crypto',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Polygon%20MATIC%20logo%20purple%20cryptocurrency&image_size=square',
      description: 'Pago con MATIC en red Polygon',
      fees: { percentage: 0, currency: 'MATIC' },
      processingTime: '30 segundos',
      limits: { min: 1, max: 100000, currency: 'MATIC' },
      supported: true,
      popular: true,
      secure: true
    },
    {
      id: 'binance',
      name: 'Binance Smart Chain (BNB)',
      type: 'web3',
      category: 'crypto',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=Binance%20BNB%20logo%20yellow%20cryptocurrency&image_size=square',
      description: 'Pago con BNB en BSC',
      fees: { percentage: 0, currency: 'BNB' },
      processingTime: '1-2 minutos',
      limits: { min: 0.01, max: 10000, currency: 'BNB' },
      supported: true,
      popular: false,
      secure: true
    },
    {
      id: 'usdc',
      name: 'USD Coin (USDC)',
      type: 'web3',
      category: 'crypto',
      icon: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=USDC%20USD%20Coin%20logo%20blue%20stablecoin&image_size=square',
      description: 'Stablecoin respaldada por USD',
      fees: { percentage: 0, currency: 'USDC' },
      processingTime: '2-5 minutos',
      limits: { min: 1, max: 100000, currency: 'USDC' },
      supported: true,
      popular: true,
      secure: true
    }
  ];

  const paymentSteps: PaymentStep[] = [
    {
      id: 1,
      title: 'Seleccionar método',
      description: 'Elige tu método de pago preferido',
      completed: currentStep > 1,
      active: currentStep === 1
    },
    {
      id: 2,
      title: 'Detalles de pago',
      description: 'Ingresa la información requerida',
      completed: currentStep > 2,
      active: currentStep === 2
    },
    {
      id: 3,
      title: 'Confirmación',
      description: 'Revisa y confirma tu pago',
      completed: currentStep > 3,
      active: currentStep === 3
    },
    {
      id: 4,
      title: 'Procesamiento',
      description: 'Tu pago está siendo procesado',
      completed: currentStep > 4,
      active: currentStep === 4
    }
  ];

  const recentTransactions: Transaction[] = [
    {
      id: '1',
      amount: 125.50,
      currency: 'USD',
      method: 'Visa',
      status: 'completed',
      date: '2024-01-15T14:30:00Z',
      productName: 'MacBook Pro M3 - Ticket #1234',
      fees: 3.94
    },
    {
      id: '2',
      amount: 0.05,
      currency: 'ETH',
      method: 'Ethereum',
      status: 'completed',
      date: '2024-01-12T09:15:00Z',
      productName: 'iPhone 15 Pro - Ticket #5678',
      fees: 0.002
    },
    {
      id: '3',
      amount: 75.00,
      currency: 'USD',
      method: 'PayPal',
      status: 'processing',
      date: '2024-01-10T16:45:00Z',
      productName: 'PlayStation 5 - Ticket #9012',
      fees: 2.85
    }
  ];

  const currencies = [
    { code: 'USD', name: 'US Dollar', symbol: '$' },
    { code: 'EUR', name: 'Euro', symbol: '€' },
    { code: 'GBP', name: 'British Pound', symbol: '£' },
    { code: 'ETH', name: 'Ethereum', symbol: 'Ξ' },
    { code: 'MATIC', name: 'Polygon', symbol: 'MATIC' },
    { code: 'BNB', name: 'Binance Coin', symbol: 'BNB' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400';
      case 'processing': return 'text-yellow-400';
      case 'pending': return 'text-blue-400';
      case 'failed': return 'text-red-400';
      default: return 'text-white/70';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4" />;
      case 'processing': return <Clock className="w-4 h-4" />;
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'failed': return <AlertCircle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const handlePayment = async () => {
    setIsProcessing(true);
    setCurrentStep(4);
    
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      setCurrentStep(1);
      setSelectedMethod(null);
      // Show success message
    }, 3000);
  };

  const PaymentMethodCard: React.FC<{ method: PaymentMethod }> = ({ method }) => (
    <Card 
      variant={selectedMethod?.id === method.id ? 'primary' : 'glass'}
      className={`cursor-pointer transition-all duration-300 hover:scale-105 ${
        selectedMethod?.id === method.id ? 'ring-2 ring-purple-500' : ''
      }`}
      onClick={() => setSelectedMethod(method)}
    >
      <div className="flex items-center space-x-4">
        <img 
          src={method.icon} 
          alt={method.name}
          className="w-12 h-12 rounded-lg object-cover"
        />
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h3 className="text-white font-semibold">{method.name}</h3>
            {method.popular && (
              <span className="px-2 py-1 bg-amber-500/20 text-amber-400 rounded-full text-xs font-medium">
                Popular
              </span>
            )}
            {method.secure && (
              <Shield className="w-4 h-4 text-green-400" />
            )}
          </div>
          <p className="text-white/70 text-sm mb-2">{method.description}</p>
          <div className="flex items-center justify-between text-xs">
            <span className="text-white/60">
              Comisión: {method.fees.percentage}%
              {method.fees.fixed && ` + $${method.fees.fixed}`}
            </span>
            <span className="text-green-400">{method.processingTime}</span>
          </div>
        </div>
        {selectedMethod?.id === method.id && (
          <CheckCircle className="w-6 h-6 text-green-400" />
        )}
      </div>
    </Card>
  );

  const TransactionCard: React.FC<{ transaction: Transaction }> = ({ transaction }) => (
    <Card variant="glass">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className={`p-2 rounded-lg ${getStatusColor(transaction.status)}`}>
            {getStatusIcon(transaction.status)}
          </div>
          <div>
            <h3 className="text-white font-semibold">{transaction.productName}</h3>
            <p className="text-white/70 text-sm">{transaction.method}</p>
            <p className="text-white/50 text-xs">
              {new Date(transaction.date).toLocaleDateString()}
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="text-white font-bold">
            {transaction.amount} {transaction.currency}
          </div>
          <div className="text-white/60 text-sm">
            Comisión: {transaction.fees} {transaction.currency}
          </div>
          <div className={`text-sm font-medium ${getStatusColor(transaction.status)}`}>
            {transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Gateway de <span className="bg-gradient-to-r from-purple-400 to-amber-400 bg-clip-text text-transparent">Pagos</span>
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Pagos seguros y flexibles con soporte Web2 y Web3
          </p>
        </div>

        {/* Security Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card variant="glass">
            <div className="text-center">
              <Shield className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <h3 className="text-white font-semibold mb-1">Seguridad Avanzada</h3>
              <p className="text-white/70 text-sm">Encriptación de extremo a extremo</p>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Globe className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <h3 className="text-white font-semibold mb-1">Pagos Globales</h3>
              <p className="text-white/70 text-sm">Soporte para múltiples monedas</p>
            </div>
          </Card>
          <Card variant="glass">
            <div className="text-center">
              <Zap className="w-8 h-8 text-amber-400 mx-auto mb-2" />
              <h3 className="text-white font-semibold mb-1">Procesamiento Rápido</h3>
              <p className="text-white/70 text-sm">Confirmación instantánea</p>
            </div>
          </Card>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-white/10 backdrop-blur-md rounded-xl p-1">
          {[
            { id: 'payment', label: 'Realizar Pago', icon: CreditCard },
            { id: 'history', label: 'Historial', icon: Clock },
            { id: 'methods', label: 'Métodos', icon: Wallet },
            { id: 'security', label: 'Seguridad', icon: Shield }
          ].map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-white/20 text-white'
                    : 'text-white/70 hover:text-white hover:bg-white/10'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="font-medium hidden sm:block">{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="space-y-6">
          {activeTab === 'payment' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Payment Steps */}
              <div className="lg:col-span-2">
                {/* Progress Steps */}
                <div className="mb-8">
                  <div className="flex items-center justify-between">
                    {paymentSteps.map((step, index) => (
                      <div key={step.id} className="flex items-center">
                        <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                          step.completed 
                            ? 'bg-green-500 border-green-500 text-white'
                            : step.active
                            ? 'bg-purple-500 border-purple-500 text-white'
                            : 'border-white/30 text-white/50'
                        }`}>
                          {step.completed ? (
                            <CheckCircle className="w-5 h-5" />
                          ) : (
                            <span className="font-semibold">{step.id}</span>
                          )}
                        </div>
                        {index < paymentSteps.length - 1 && (
                          <div className={`w-16 h-0.5 mx-2 ${
                            step.completed ? 'bg-green-500' : 'bg-white/30'
                          }`} />
                        )}
                      </div>
                    ))}
                  </div>
                  <div className="mt-4">
                    <h3 className="text-white font-semibold text-lg">
                      {paymentSteps.find(s => s.active)?.title}
                    </h3>
                    <p className="text-white/70">
                      {paymentSteps.find(s => s.active)?.description}
                    </p>
                  </div>
                </div>

                {/* Step Content */}
                {currentStep === 1 && (
                  <div className="space-y-6">
                    <div className="flex space-x-4 mb-6">
                      <button
                        onClick={() => setActiveTab('payment')}
                        className={`px-4 py-2 rounded-lg transition-colors ${
                          true ? 'bg-purple-500 text-white' : 'bg-white/10 text-white/70'
                        }`}
                      >
                        Todos
                      </button>
                      <button className="px-4 py-2 rounded-lg bg-white/10 text-white/70 hover:bg-white/20 transition-colors">
                        Web2
                      </button>
                      <button className="px-4 py-2 rounded-lg bg-white/10 text-white/70 hover:bg-white/20 transition-colors">
                        Web3
                      </button>
                    </div>
                    
                    <div className="space-y-4">
                      {paymentMethods.filter(m => m.supported).map(method => (
                        <PaymentMethodCard key={method.id} method={method} />
                      ))}
                    </div>
                    
                    {selectedMethod && (
                      <div className="flex justify-end">
                        <Button 
                          variant="primary" 
                          onClick={() => setCurrentStep(2)}
                        >
                          Continuar
                          <ArrowRight className="w-4 h-4 ml-2" />
                        </Button>
                      </div>
                    )}
                  </div>
                )}

                {currentStep === 2 && selectedMethod && (
                  <div className="space-y-6">
                    <Card variant="glass">
                      <h3 className="text-white font-semibold text-lg mb-4">
                        Detalles de {selectedMethod.name}
                      </h3>
                      
                      {selectedMethod.type === 'web2' && selectedMethod.category === 'card' && (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-white font-medium mb-2">
                              Número de tarjeta
                            </label>
                            <input
                              type="text"
                              value={cardNumber}
                              onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                              placeholder="1234 5678 9012 3456"
                              maxLength={19}
                              className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                            />
                          </div>
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label className="block text-white font-medium mb-2">
                                Fecha de expiración
                              </label>
                              <input
                                type="text"
                                value={expiryDate}
                                onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
                                placeholder="MM/YY"
                                maxLength={5}
                                className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                              />
                            </div>
                            <div>
                              <label className="block text-white font-medium mb-2">
                                CVV
                              </label>
                              <div className="relative">
                                <input
                                  type={showCvv ? 'text' : 'password'}
                                  value={cvv}
                                  onChange={(e) => setCvv(e.target.value.replace(/\D/g, '').slice(0, 4))}
                                  placeholder="123"
                                  className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                                />
                                <button
                                  type="button"
                                  onClick={() => setShowCvv(!showCvv)}
                                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/50 hover:text-white"
                                >
                                  {showCvv ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                                </button>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {selectedMethod.type === 'web3' && (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-white font-medium mb-2">
                              Dirección de wallet
                            </label>
                            <div className="flex space-x-2">
                              <input
                                type="text"
                                value={walletAddress}
                                onChange={(e) => setWalletAddress(e.target.value)}
                                placeholder="0x..."
                                className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-purple-500"
                              />
                              <Button variant="outline">
                                <Wallet className="w-4 h-4" />
                              </Button>
                            </div>
                          </div>
                          
                          <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                            <div className="flex items-start space-x-3">
                              <AlertCircle className="w-5 h-5 text-blue-400 mt-0.5" />
                              <div>
                                <h4 className="text-blue-400 font-medium mb-1">Información importante</h4>
                                <p className="text-blue-300 text-sm">
                                  Asegúrate de que tu wallet esté conectada a la red {selectedMethod.name.split(' ')[0]} 
                                  y tenga suficiente saldo para cubrir las tarifas de gas.
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </Card>
                    
                    <div className="flex justify-between">
                      <Button 
                        variant="outline" 
                        onClick={() => setCurrentStep(1)}
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Atrás
                      </Button>
                      <Button 
                        variant="primary" 
                        onClick={() => setCurrentStep(3)}
                      >
                        Continuar
                        <ArrowRight className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                )}

                {currentStep === 3 && selectedMethod && (
                  <div className="space-y-6">
                    <Card variant="glass">
                      <h3 className="text-white font-semibold text-lg mb-4">
                        Confirmar pago
                      </h3>
                      
                      <div className="space-y-4">
                        <div className="flex justify-between items-center py-2 border-b border-white/20">
                          <span className="text-white/70">Método de pago:</span>
                          <span className="text-white font-medium">{selectedMethod.name}</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-white/20">
                          <span className="text-white/70">Monto:</span>
                          <span className="text-white font-medium">{paymentAmount} {selectedCurrency}</span>
                        </div>
                        <div className="flex justify-between items-center py-2 border-b border-white/20">
                          <span className="text-white/70">Comisión:</span>
                          <span className="text-white font-medium">
                            {(paymentAmount * selectedMethod.fees.percentage / 100).toFixed(2)} {selectedCurrency}
                          </span>
                        </div>
                        <div className="flex justify-between items-center py-2 font-semibold">
                          <span className="text-white">Total:</span>
                          <span className="text-green-400 text-lg">
                            {(paymentAmount + (paymentAmount * selectedMethod.fees.percentage / 100)).toFixed(2)} {selectedCurrency}
                          </span>
                        </div>
                      </div>
                    </Card>
                    
                    <div className="flex justify-between">
                      <Button 
                        variant="outline" 
                        onClick={() => setCurrentStep(2)}
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Atrás
                      </Button>
                      <Button 
                        variant="primary" 
                        onClick={handlePayment}
                        disabled={isProcessing}
                      >
                        {isProcessing ? 'Procesando...' : 'Confirmar pago'}
                        <Lock className="w-4 h-4 ml-2" />
                      </Button>
                    </div>
                  </div>
                )}

                {currentStep === 4 && (
                  <div className="text-center py-12">
                    <div className="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-6"></div>
                    <h3 className="text-white font-semibold text-xl mb-2">Procesando pago...</h3>
                    <p className="text-white/70">Por favor espera mientras procesamos tu transacción</p>
                  </div>
                )}
              </div>

              {/* Payment Summary */}
              <div>
                <Card variant="glass">
                  <h3 className="text-white font-semibold text-lg mb-4">Resumen del pago</h3>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-white font-medium mb-2">Monto</label>
                      <div className="flex space-x-2">
                        <input
                          type="number"
                          value={paymentAmount}
                          onChange={(e) => setPaymentAmount(Number(e.target.value))}
                          className="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                        <select
                          value={selectedCurrency}
                          onChange={(e) => setSelectedCurrency(e.target.value)}
                          className="px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          {currencies.map(currency => (
                            <option key={currency.code} value={currency.code}>
                              {currency.code}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                    
                    {selectedMethod && (
                      <div className="space-y-2 pt-4 border-t border-white/20">
                        <div className="flex justify-between text-sm">
                          <span className="text-white/70">Subtotal:</span>
                          <span className="text-white">{paymentAmount} {selectedCurrency}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-white/70">Comisión ({selectedMethod.fees.percentage}%):</span>
                          <span className="text-white">
                            {(paymentAmount * selectedMethod.fees.percentage / 100).toFixed(2)} {selectedCurrency}
                          </span>
                        </div>
                        <div className="flex justify-between font-semibold pt-2 border-t border-white/20">
                          <span className="text-white">Total:</span>
                          <span className="text-green-400">
                            {(paymentAmount + (paymentAmount * selectedMethod.fees.percentage / 100)).toFixed(2)} {selectedCurrency}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold text-white">Historial de transacciones</h2>
                <Button variant="outline">
                  <Download className="w-4 h-4 mr-2" />
                  Exportar
                </Button>
              </div>
              
              {recentTransactions.map(transaction => (
                <TransactionCard key={transaction.id} transaction={transaction} />
              ))}
            </div>
          )}

          {activeTab === 'methods' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-white">Métodos de pago disponibles</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">Web2 (Tradicional)</h3>
                  <div className="space-y-4">
                    {paymentMethods.filter(m => m.type === 'web2').map(method => (
                      <PaymentMethodCard key={method.id} method={method} />
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-xl font-semibold text-white mb-4">Web3 (Criptomonedas)</h3>
                  <div className="space-y-4">
                    {paymentMethods.filter(m => m.type === 'web3').map(method => (
                      <PaymentMethodCard key={method.id} method={method} />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-white">Seguridad y protección</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Shield className="w-8 h-8 text-green-400" />
                      <h3 className="text-white font-semibold text-lg">Encriptación SSL</h3>
                    </div>
                    <p className="text-white/70">
                      Todas las transacciones están protegidas con encriptación SSL de 256 bits.
                    </p>
                  </div>
                </Card>
                
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Lock className="w-8 h-8 text-blue-400" />
                      <h3 className="text-white font-semibold text-lg">PCI DSS Compliant</h3>
                    </div>
                    <p className="text-white/70">
                      Cumplimos con los estándares de seguridad PCI DSS para el manejo de datos de tarjetas.
                    </p>
                  </div>
                </Card>
                
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Award className="w-8 h-8 text-purple-400" />
                      <h3 className="text-white font-semibold text-lg">Verificación Blockchain</h3>
                    </div>
                    <p className="text-white/70">
                      Los pagos Web3 son verificados automáticamente en la blockchain correspondiente.
                    </p>
                  </div>
                </Card>
                
                <Card variant="glass">
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Users className="w-8 h-8 text-amber-400" />
                      <h3 className="text-white font-semibold text-lg">Soporte 24/7</h3>
                    </div>
                    <p className="text-white/70">
                      Nuestro equipo de soporte está disponible las 24 horas para ayudarte.
                    </p>
                  </div>
                </Card>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PaymentGateway;