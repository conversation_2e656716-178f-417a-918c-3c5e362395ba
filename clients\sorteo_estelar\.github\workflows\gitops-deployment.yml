name: GitOps VPS Deployment

on:
  push:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'staging'
        type: choice
        options:
        - staging
        - production
      force_deploy:
        description: 'Force deployment'
        required: false
        default: false
        type: boolean

env:
  VPS_HOST: '**************'
  VPS_PORT: '2222'
  VPS_USER: 'admin'
  DOKPLOY_API_URL: 'https://dokploy.sorteoestelar.com'
  REGISTRY: ghcr.io
  IMAGE_NAME: sorteo-estelar

jobs:
  prepare-deployment:
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env.outputs.environment }}
      image-tag: ${{ steps.tag.outputs.tag }}
      deploy-ready: ${{ steps.check.outputs.ready }}
    steps:
      - name: Determine environment
        id: env
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            echo "environment=${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref }}" == "refs/heads/main" ]; then
            echo "environment=production" >> $GITHUB_OUTPUT
          else
            echo "environment=staging" >> $GITHUB_OUTPUT
          fi

      - name: Generate image tag
        id: tag
        run: |
          if [ "${{ steps.env.outputs.environment }}" == "production" ]; then
            echo "tag=latest" >> $GITHUB_OUTPUT
          else
            echo "tag=staging-${{ github.sha }}" >> $GITHUB_OUTPUT
          fi

      - name: Check deployment readiness
        id: check
        run: |
          if [ "${{ github.event.inputs.force_deploy }}" == "true" ]; then
            echo "ready=true" >> $GITHUB_OUTPUT
          elif [ "${{ steps.env.outputs.environment }}" == "staging" ]; then
            echo "ready=true" >> $GITHUB_OUTPUT
          else
            echo "ready=true" >> $GITHUB_OUTPUT
          fi

  ssh-setup:
    runs-on: ubuntu-latest
    needs: prepare-deployment
    if: needs.prepare-deployment.outputs.deploy-ready == 'true'
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Test SSH connection
        run: |
          ssh -o ConnectTimeout=10 -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} 'echo "SSH connection successful"'

      - name: Verify VPS environment
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== System Information ==="
            uname -a
            echo "=== Docker Status ==="
            docker --version
            docker-compose --version
            echo "=== Dokploy Status ==="
            systemctl status dokploy || echo "Dokploy service not found"
            echo "=== Disk Space ==="
            df -h
            echo "=== Memory Usage ==="
            free -h
          '

  vps-preparation:
    runs-on: ubuntu-latest
    needs: [prepare-deployment, ssh-setup]
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Prepare VPS environment
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            # Update system packages
            sudo apt update && sudo apt upgrade -y
            
            # Ensure Docker is running
            sudo systemctl start docker
            sudo systemctl enable docker
            
            # Create application directories
            sudo mkdir -p /opt/sorteo-estelar/{data,logs,backups,ssl}
            sudo chown -R admin:admin /opt/sorteo-estelar
            
            # Setup log rotation
            sudo tee /etc/logrotate.d/sorteo-estelar > /dev/null <<EOF
            /opt/sorteo-estelar/logs/*.log {
                daily
                rotate 30
                compress
                delaycompress
                missingok
                notifempty
                create 644 admin admin
            }
            EOF
            
            # Setup monitoring directories
            mkdir -p /opt/sorteo-estelar/{prometheus,grafana}
          '

      - name: Configure firewall
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            # Configure UFW firewall
            sudo ufw --force reset
            sudo ufw default deny incoming
            sudo ufw default allow outgoing
            sudo ufw allow ${{ env.VPS_PORT }}/tcp comment "SSH"
            sudo ufw allow 80/tcp comment "HTTP"
            sudo ufw allow 443/tcp comment "HTTPS"
            sudo ufw allow 3000/tcp comment "Dokploy"
            sudo ufw allow 9090/tcp comment "Prometheus"
            sudo ufw allow 3001/tcp comment "Grafana"
            sudo ufw --force enable
            sudo ufw status verbose
          '

  dokploy-deployment:
    runs-on: ubuntu-latest
    needs: [prepare-deployment, vps-preparation]
    environment: ${{ needs.prepare-deployment.outputs.environment }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy via Dokploy API
        env:
          DOKPLOY_TOKEN: ${{ secrets.DOKPLOY_TOKEN }}
          IMAGE_TAG: ${{ needs.prepare-deployment.outputs.image-tag }}
          ENVIRONMENT: ${{ needs.prepare-deployment.outputs.environment }}
        run: |
          # Deploy using Dokploy API
          curl -X POST "${{ env.DOKPLOY_API_URL }}/api/deploy" \
            -H "Authorization: Bearer ${{ env.DOKPLOY_TOKEN }}" \
            -H "Content-Type: application/json" \
            -d '{
              "project": "sorteo-estelar",
              "environment": "'$ENVIRONMENT'",
              "image": "${{ env.REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:'$IMAGE_TAG'",
              "config": {
                "domain": "'$([ "$ENVIRONMENT" == "production" ] && echo "www.sorteoestelar.com" || echo "staging.sorteoestelar.com")'"  
              }
            }'

      - name: Wait for deployment
        run: |
          echo "Waiting for deployment to complete..."
          sleep 60

      - name: Verify deployment
        run: |
          DOMAIN=$([ "${{ needs.prepare-deployment.outputs.environment }}" == "production" ] && echo "www.sorteoestelar.com" || echo "staging.sorteoestelar.com")
          
          # Check if the application is responding
          for i in {1..10}; do
            if curl -f -s "https://$DOMAIN/health" > /dev/null; then
              echo "✅ Application is responding on https://$DOMAIN"
              break
            else
              echo "⏳ Waiting for application to respond... (attempt $i/10)"
              sleep 30
            fi
          done

  post-deployment:
    runs-on: ubuntu-latest
    needs: [prepare-deployment, dokploy-deployment]
    if: always()
    steps:
      - name: Setup SSH key
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.VPS_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -p ${{ env.VPS_PORT }} ${{ env.VPS_HOST }} >> ~/.ssh/known_hosts

      - name: Collect deployment logs
        run: |
          ssh -p ${{ env.VPS_PORT }} ${{ env.VPS_USER }}@${{ env.VPS_HOST }} '
            echo "=== Docker Container Status ==="
            docker ps -a --filter "name=sorteo-estelar"
            
            echo "=== Recent Application Logs ==="
            docker logs --tail=50 sorteo-estelar-frontend || echo "Frontend container not found"
            docker logs --tail=50 sorteo-estelar-backend || echo "Backend container not found"
            
            echo "=== System Resources ==="
            docker stats --no-stream
          '

      - name: Update deployment status
        run: |
          if [ "${{ needs.dokploy-deployment.result }}" == "success" ]; then
            echo "✅ Deployment to ${{ needs.prepare-deployment.outputs.environment }} successful"
            echo "🌐 Application URL: https://$([ "${{ needs.prepare-deployment.outputs.environment }}" == "production" ] && echo "www.sorteoestelar.com" || echo "staging.sorteoestelar.com")"
          else
            echo "❌ Deployment to ${{ needs.prepare-deployment.outputs.environment }} failed"
            exit 1
          fi

      - name: Notify team
        if: always()
        run: |
          STATUS=$([ "${{ needs.dokploy-deployment.result }}" == "success" ] && echo "SUCCESS" || echo "FAILED")
          DOMAIN=$([ "${{ needs.prepare-deployment.outputs.environment }}" == "production" ] && echo "www.sorteoestelar.com" || echo "staging.sorteoestelar.com")
          
          echo "Deployment Status: $STATUS"
          echo "Environment: ${{ needs.prepare-deployment.outputs.environment }}"
          echo "Domain: $DOMAIN"
          echo "Commit: ${{ github.sha }}"
          echo "Actor: ${{ github.actor }}"