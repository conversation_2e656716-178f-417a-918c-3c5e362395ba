import { logger } from '@/utils/logger';
import { Client, RFP, OnboardingFlow, OnboardingDocument } from '@/types';

export interface AnalyticsMetrics {
  totalClients: number;
  activeClients: number;
  newClientsThisMonth: number;
  totalRFPs: number;
  pendingRFPs: number;
  approvedRFPs: number;
  rejectedRFPs: number;
  rfpApprovalRate: number;
  totalOnboardingFlows: number;
  completedOnboardingFlows: number;
  onboardingCompletionRate: number;
  averageOnboardingTime: number;
  totalDocuments: number;
  pendingDocuments: number;
  approvedDocuments: number;
  rejectedDocuments: number;
  documentApprovalRate: number;
}

export interface ClientAnalytics {
  clientId: string;
  name: string;
  company?: string;
  registrationDate: Date;
  lastContactDate?: Date;
  totalRFPs: number;
  approvedRFPs: number;
  onboardingStatus: 'not_started' | 'in_progress' | 'completed';
  onboardingProgress: number;
  totalDocuments: number;
  approvedDocuments: number;
  engagementScore: number;
}

export interface RFPAnalytics {
  rfpId: string;
  title: string;
  clientId: string;
  status: string;
  submittedAt: Date;
  reviewedAt?: Date;
  processingTime?: number; // in hours
  budget?: number;
  priority: string;
}

export interface OnboardingAnalytics {
  flowId: string;
  clientId: string;
  type: string;
  status: string;
  startedAt: Date;
  completedAt?: Date;
  totalSteps: number;
  completedSteps: number;
  progress: number;
  timeToComplete?: number; // in hours
  stuckSteps: string[];
}

export interface DocumentAnalytics {
  documentId: string;
  name: string;
  clientId: string;
  type: string;
  status: string;
  uploadedAt: Date;
  reviewedAt?: Date;
  processingTime?: number; // in hours
  size: number;
  approvedBy?: string;
}

export interface TimeSeriesData {
  date: string;
  value: number;
  label?: string;
}

export interface AnalyticsReport {
  id: string;
  title: string;
  description: string;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  metrics: AnalyticsMetrics;
  charts: {
    clientGrowth: TimeSeriesData[];
    rfpTrends: TimeSeriesData[];
    onboardingProgress: TimeSeriesData[];
    documentProcessing: TimeSeriesData[];
  };
  insights: string[];
  recommendations: string[];
}

export class AnalyticsService {
  private clients: Map<string, Client> = new Map();
  private rfps: Map<string, RFP> = new Map();
  private onboardingFlows: Map<string, OnboardingFlow> = new Map();
  private documents: Map<string, OnboardingDocument> = new Map();
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      logger.info('Initializing Analytics Service...');
      
      // Load sample data for development
      await this.loadSampleData();
      
      this.isInitialized = true;
      logger.info('Analytics Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Analytics Service', { error });
      throw error;
    }
  }

  async cleanup(): Promise<void> {
    logger.info('Cleaning up Analytics Service...');
    this.clients.clear();
    this.rfps.clear();
    this.onboardingFlows.clear();
    this.documents.clear();
    this.isInitialized = false;
  }

  async getOverallMetrics(): Promise<AnalyticsMetrics> {
    try {
      const clients = Array.from(this.clients.values());
      const rfps = Array.from(this.rfps.values());
      const flows = Array.from(this.onboardingFlows.values());
      const documents = Array.from(this.documents.values());

      const now = new Date();
      const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const newClientsThisMonth = clients.filter(
        client => client.createdAt >= thisMonth
      ).length;

      const activeClients = clients.filter(
        client => client.lastContactDate && 
        (now.getTime() - client.lastContactDate.getTime()) < (30 * 24 * 60 * 60 * 1000) // 30 days
      ).length;

      const pendingRFPs = rfps.filter(rfp => rfp.status === 'pending').length;
      const approvedRFPs = rfps.filter(rfp => rfp.status === 'approved').length;
      const rejectedRFPs = rfps.filter(rfp => rfp.status === 'rejected').length;
      const rfpApprovalRate = rfps.length > 0 ? (approvedRFPs / rfps.length) * 100 : 0;

      const completedFlows = flows.filter(flow => flow.status === 'completed').length;
      const onboardingCompletionRate = flows.length > 0 ? (completedFlows / flows.length) * 100 : 0;

      const completedFlowsWithTime = flows.filter(
        flow => flow.status === 'completed' && flow.startedAt && flow.completedAt
      );
      const averageOnboardingTime = completedFlowsWithTime.length > 0 ?
        completedFlowsWithTime.reduce((sum, flow) => {
          const time = flow.completedAt!.getTime() - flow.startedAt!.getTime();
          return sum + (time / (1000 * 60 * 60)); // Convert to hours
        }, 0) / completedFlowsWithTime.length : 0;

      const pendingDocuments = documents.filter(doc => doc.status === 'pending').length;
      const approvedDocuments = documents.filter(doc => doc.status === 'approved').length;
      const rejectedDocuments = documents.filter(doc => doc.status === 'rejected').length;
      const documentApprovalRate = documents.size > 0 ? (approvedDocuments / documents.size) * 100 : 0;

      return {
        totalClients: clients.length,
        activeClients,
        newClientsThisMonth,
        totalRFPs: rfps.length,
        pendingRFPs,
        approvedRFPs,
        rejectedRFPs,
        rfpApprovalRate,
        totalOnboardingFlows: flows.length,
        completedOnboardingFlows: completedFlows,
        onboardingCompletionRate,
        averageOnboardingTime,
        totalDocuments: documents.size,
        pendingDocuments,
        approvedDocuments,
        rejectedDocuments,
        documentApprovalRate
      };
    } catch (error) {
      logger.error('Failed to get overall metrics', { error });
      throw error;
    }
  }

  async getClientAnalytics(clientId?: string): Promise<ClientAnalytics[]> {
    try {
      const clients = clientId ? 
        [this.clients.get(clientId)].filter(Boolean) as Client[] :
        Array.from(this.clients.values());

      const analytics: ClientAnalytics[] = [];

      for (const client of clients) {
        const clientRFPs = Array.from(this.rfps.values()).filter(rfp => rfp.clientId === client.id);
        const approvedRFPs = clientRFPs.filter(rfp => rfp.status === 'approved');
        
        const clientFlows = Array.from(this.onboardingFlows.values()).filter(flow => flow.clientId === client.id);
        const completedFlows = clientFlows.filter(flow => flow.status === 'completed');
        
        let onboardingStatus: 'not_started' | 'in_progress' | 'completed' = 'not_started';
        let onboardingProgress = 0;
        
        if (clientFlows.length > 0) {
          const latestFlow = clientFlows.sort((a, b) => 
            (b.startedAt?.getTime() || 0) - (a.startedAt?.getTime() || 0)
          )[0];
          
          onboardingStatus = latestFlow.status === 'completed' ? 'completed' : 'in_progress';
          onboardingProgress = (latestFlow.completedSteps / latestFlow.totalSteps) * 100;
        }
        
        const clientDocuments = Array.from(this.documents.values()).filter(doc => doc.clientId === client.id);
        const approvedDocuments = clientDocuments.filter(doc => doc.status === 'approved');
        
        // Calculate engagement score based on various factors
        const engagementScore = this.calculateEngagementScore({
          totalRFPs: clientRFPs.length,
          approvedRFPs: approvedRFPs.length,
          onboardingProgress,
          documentsSubmitted: clientDocuments.length,
          lastContactDays: client.lastContactDate ? 
            Math.floor((Date.now() - client.lastContactDate.getTime()) / (1000 * 60 * 60 * 24)) : 999
        });

        analytics.push({
          clientId: client.id,
          name: client.name,
          company: client.company,
          registrationDate: client.createdAt,
          lastContactDate: client.lastContactDate,
          totalRFPs: clientRFPs.length,
          approvedRFPs: approvedRFPs.length,
          onboardingStatus,
          onboardingProgress,
          totalDocuments: clientDocuments.length,
          approvedDocuments: approvedDocuments.length,
          engagementScore
        });
      }

      return analytics.sort((a, b) => b.engagementScore - a.engagementScore);
    } catch (error) {
      logger.error('Failed to get client analytics', { error, clientId });
      throw error;
    }
  }

  async getRFPAnalytics(filters?: {
    status?: string;
    clientId?: string;
    dateRange?: { start: Date; end: Date };
  }): Promise<RFPAnalytics[]> {
    try {
      let rfps = Array.from(this.rfps.values());

      if (filters) {
        if (filters.status) {
          rfps = rfps.filter(rfp => rfp.status === filters.status);
        }
        if (filters.clientId) {
          rfps = rfps.filter(rfp => rfp.clientId === filters.clientId);
        }
        if (filters.dateRange) {
          rfps = rfps.filter(rfp => 
            rfp.submittedAt >= filters.dateRange!.start &&
            rfp.submittedAt <= filters.dateRange!.end
          );
        }
      }

      return rfps.map(rfp => {
        const processingTime = rfp.reviewedAt && rfp.submittedAt ?
          (rfp.reviewedAt.getTime() - rfp.submittedAt.getTime()) / (1000 * 60 * 60) : undefined;

        return {
          rfpId: rfp.id,
          title: rfp.title,
          clientId: rfp.clientId,
          status: rfp.status,
          submittedAt: rfp.submittedAt,
          reviewedAt: rfp.reviewedAt,
          processingTime,
          budget: rfp.budget,
          priority: rfp.priority
        };
      }).sort((a, b) => b.submittedAt.getTime() - a.submittedAt.getTime());
    } catch (error) {
      logger.error('Failed to get RFP analytics', { error, filters });
      throw error;
    }
  }

  async getOnboardingAnalytics(filters?: {
    status?: string;
    type?: string;
    clientId?: string;
  }): Promise<OnboardingAnalytics[]> {
    try {
      let flows = Array.from(this.onboardingFlows.values());

      if (filters) {
        if (filters.status) {
          flows = flows.filter(flow => flow.status === filters.status);
        }
        if (filters.type) {
          flows = flows.filter(flow => flow.type === filters.type);
        }
        if (filters.clientId) {
          flows = flows.filter(flow => flow.clientId === filters.clientId);
        }
      }

      return flows.map(flow => {
        const timeToComplete = flow.completedAt && flow.startedAt ?
          (flow.completedAt.getTime() - flow.startedAt.getTime()) / (1000 * 60 * 60) : undefined;

        const stuckSteps = flow.steps
          .filter(step => step.status === 'pending' && step.startedAt &&
            (Date.now() - step.startedAt.getTime()) > (7 * 24 * 60 * 60 * 1000) // 7 days
          )
          .map(step => step.title);

        return {
          flowId: flow.id,
          clientId: flow.clientId,
          type: flow.type,
          status: flow.status,
          startedAt: flow.startedAt!,
          completedAt: flow.completedAt,
          totalSteps: flow.totalSteps,
          completedSteps: flow.completedSteps,
          progress: (flow.completedSteps / flow.totalSteps) * 100,
          timeToComplete,
          stuckSteps
        };
      }).sort((a, b) => b.startedAt.getTime() - a.startedAt.getTime());
    } catch (error) {
      logger.error('Failed to get onboarding analytics', { error, filters });
      throw error;
    }
  }

  async getDocumentAnalytics(filters?: {
    status?: string;
    type?: string;
    clientId?: string;
  }): Promise<DocumentAnalytics[]> {
    try {
      let documents = Array.from(this.documents.values());

      if (filters) {
        if (filters.status) {
          documents = documents.filter(doc => doc.status === filters.status);
        }
        if (filters.type) {
          documents = documents.filter(doc => doc.type === filters.type);
        }
        if (filters.clientId) {
          documents = documents.filter(doc => doc.clientId === filters.clientId);
        }
      }

      return documents.map(doc => {
        const processingTime = doc.reviewedAt && doc.uploadedAt ?
          (doc.reviewedAt.getTime() - doc.uploadedAt.getTime()) / (1000 * 60 * 60) : undefined;

        return {
          documentId: doc.id,
          name: doc.name,
          clientId: doc.clientId,
          type: doc.type,
          status: doc.status,
          uploadedAt: doc.uploadedAt,
          reviewedAt: doc.reviewedAt,
          processingTime,
          size: doc.size,
          approvedBy: doc.approvedBy
        };
      }).sort((a, b) => b.uploadedAt.getTime() - a.uploadedAt.getTime());
    } catch (error) {
      logger.error('Failed to get document analytics', { error, filters });
      throw error;
    }
  }

  async generateReport(period: { start: Date; end: Date }): Promise<AnalyticsReport> {
    try {
      const reportId = `report_${Date.now()}`;
      const metrics = await this.getOverallMetrics();
      
      // Generate time series data
      const clientGrowth = await this.generateClientGrowthData(period);
      const rfpTrends = await this.generateRFPTrendsData(period);
      const onboardingProgress = await this.generateOnboardingProgressData(period);
      const documentProcessing = await this.generateDocumentProcessingData(period);

      // Generate insights
      const insights = this.generateInsights(metrics);
      const recommendations = this.generateRecommendations(metrics);

      const report: AnalyticsReport = {
        id: reportId,
        title: `ESTRATIX Analytics Report - ${period.start.toLocaleDateString()} to ${period.end.toLocaleDateString()}`,
        description: 'Comprehensive analytics report for client onboarding operations',
        generatedAt: new Date(),
        period,
        metrics,
        charts: {
          clientGrowth,
          rfpTrends,
          onboardingProgress,
          documentProcessing
        },
        insights,
        recommendations
      };

      logger.info('Analytics report generated', {
        reportId,
        period,
        metricsCount: Object.keys(metrics).length
      });

      return report;
    } catch (error) {
      logger.error('Failed to generate analytics report', { error, period });
      throw error;
    }
  }

  private calculateEngagementScore(factors: {
    totalRFPs: number;
    approvedRFPs: number;
    onboardingProgress: number;
    documentsSubmitted: number;
    lastContactDays: number;
  }): number {
    let score = 0;

    // RFP activity (0-30 points)
    score += Math.min(factors.totalRFPs * 5, 30);
    
    // RFP approval rate (0-20 points)
    if (factors.totalRFPs > 0) {
      score += (factors.approvedRFPs / factors.totalRFPs) * 20;
    }

    // Onboarding progress (0-25 points)
    score += (factors.onboardingProgress / 100) * 25;

    // Document submission (0-15 points)
    score += Math.min(factors.documentsSubmitted * 3, 15);

    // Recent activity (0-10 points)
    if (factors.lastContactDays <= 7) {
      score += 10;
    } else if (factors.lastContactDays <= 30) {
      score += 5;
    }

    return Math.round(score);
  }

  private async generateClientGrowthData(period: { start: Date; end: Date }): Promise<TimeSeriesData[]> {
    const data: TimeSeriesData[] = [];
    const clients = Array.from(this.clients.values());
    
    const daysDiff = Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
    const interval = Math.max(1, Math.floor(daysDiff / 30)); // Max 30 data points

    for (let i = 0; i <= daysDiff; i += interval) {
      const date = new Date(period.start.getTime() + i * 24 * 60 * 60 * 1000);
      const count = clients.filter(client => client.createdAt <= date).length;
      
      data.push({
        date: date.toISOString().split('T')[0],
        value: count,
        label: `${count} clients`
      });
    }

    return data;
  }

  private async generateRFPTrendsData(period: { start: Date; end: Date }): Promise<TimeSeriesData[]> {
    const data: TimeSeriesData[] = [];
    const rfps = Array.from(this.rfps.values()).filter(
      rfp => rfp.submittedAt >= period.start && rfp.submittedAt <= period.end
    );
    
    const daysDiff = Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
    const interval = Math.max(1, Math.floor(daysDiff / 30));

    for (let i = 0; i <= daysDiff; i += interval) {
      const date = new Date(period.start.getTime() + i * 24 * 60 * 60 * 1000);
      const nextDate = new Date(date.getTime() + interval * 24 * 60 * 60 * 1000);
      
      const count = rfps.filter(rfp => 
        rfp.submittedAt >= date && rfp.submittedAt < nextDate
      ).length;
      
      data.push({
        date: date.toISOString().split('T')[0],
        value: count,
        label: `${count} RFPs`
      });
    }

    return data;
  }

  private async generateOnboardingProgressData(period: { start: Date; end: Date }): Promise<TimeSeriesData[]> {
    const data: TimeSeriesData[] = [];
    const flows = Array.from(this.onboardingFlows.values()).filter(
      flow => flow.startedAt && flow.startedAt >= period.start && flow.startedAt <= period.end
    );
    
    const daysDiff = Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
    const interval = Math.max(1, Math.floor(daysDiff / 30));

    for (let i = 0; i <= daysDiff; i += interval) {
      const date = new Date(period.start.getTime() + i * 24 * 60 * 60 * 1000);
      const completedFlows = flows.filter(flow => 
        flow.completedAt && flow.completedAt <= date
      ).length;
      
      data.push({
        date: date.toISOString().split('T')[0],
        value: completedFlows,
        label: `${completedFlows} completed`
      });
    }

    return data;
  }

  private async generateDocumentProcessingData(period: { start: Date; end: Date }): Promise<TimeSeriesData[]> {
    const data: TimeSeriesData[] = [];
    const documents = Array.from(this.documents.values()).filter(
      doc => doc.uploadedAt >= period.start && doc.uploadedAt <= period.end
    );
    
    const daysDiff = Math.ceil((period.end.getTime() - period.start.getTime()) / (1000 * 60 * 60 * 24));
    const interval = Math.max(1, Math.floor(daysDiff / 30));

    for (let i = 0; i <= daysDiff; i += interval) {
      const date = new Date(period.start.getTime() + i * 24 * 60 * 60 * 1000);
      const nextDate = new Date(date.getTime() + interval * 24 * 60 * 60 * 1000);
      
      const processed = documents.filter(doc => 
        doc.reviewedAt && doc.reviewedAt >= date && doc.reviewedAt < nextDate
      ).length;
      
      data.push({
        date: date.toISOString().split('T')[0],
        value: processed,
        label: `${processed} processed`
      });
    }

    return data;
  }

  private generateInsights(metrics: AnalyticsMetrics): string[] {
    const insights: string[] = [];

    if (metrics.rfpApprovalRate > 80) {
      insights.push('Excellent RFP approval rate indicates strong client qualification process');
    } else if (metrics.rfpApprovalRate < 50) {
      insights.push('Low RFP approval rate suggests need for better client pre-qualification');
    }

    if (metrics.onboardingCompletionRate > 90) {
      insights.push('Outstanding onboarding completion rate shows effective process design');
    } else if (metrics.onboardingCompletionRate < 70) {
      insights.push('Onboarding completion rate needs improvement - consider process optimization');
    }

    if (metrics.averageOnboardingTime < 24) {
      insights.push('Fast onboarding process provides excellent client experience');
    } else if (metrics.averageOnboardingTime > 72) {
      insights.push('Long onboarding times may impact client satisfaction');
    }

    if (metrics.documentApprovalRate > 85) {
      insights.push('High document approval rate indicates clear requirements communication');
    }

    return insights;
  }

  private generateRecommendations(metrics: AnalyticsMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.rfpApprovalRate < 60) {
      recommendations.push('Implement better RFP screening criteria to improve approval rates');
    }

    if (metrics.onboardingCompletionRate < 80) {
      recommendations.push('Review onboarding process for bottlenecks and simplification opportunities');
    }

    if (metrics.averageOnboardingTime > 48) {
      recommendations.push('Consider automating repetitive onboarding steps to reduce completion time');
    }

    if (metrics.pendingDocuments > metrics.approvedDocuments) {
      recommendations.push('Increase document review capacity to reduce processing backlogs');
    }

    if (metrics.activeClients / metrics.totalClients < 0.5) {
      recommendations.push('Implement client re-engagement campaigns to increase activity levels');
    }

    return recommendations;
  }

  private async loadSampleData(): Promise<void> {
    // This would typically load from a database
    // For now, we'll use empty maps that will be populated by other services
    logger.debug('Sample data loaded for analytics service');
  }

  // Methods to update data from other services
  updateClientData(clients: Client[]): void {
    this.clients.clear();
    clients.forEach(client => this.clients.set(client.id, client));
  }

  updateRFPData(rfps: RFP[]): void {
    this.rfps.clear();
    rfps.forEach(rfp => this.rfps.set(rfp.id, rfp));
  }

  updateOnboardingData(flows: OnboardingFlow[]): void {
    this.onboardingFlows.clear();
    flows.forEach(flow => this.onboardingFlows.set(flow.id, flow));
  }

  updateDocumentData(documents: OnboardingDocument[]): void {
    this.documents.clear();
    documents.forEach(doc => this.documents.set(doc.id, doc));
  }
}