---
description: "Guides the definition of a new ESTRATIX Task, ensuring it is properly documented and registered in the task matrix."
---

# Workflow: Define ESTRATIX Task

## Objective

To systematically define a new, granular ESTRATIX Task using the enhanced, context-aware template. This workflow ensures each task is deeply integrated with its parent components, project management systems, and assigned agents.

## Trigger

A user or agent identifies the need to define a new task as part of a process, flow, or service decomposition.

## Key ESTRATIX Components Involved

* `docs/templates/estratix_task_definition_template.md` (Input Template)
* `docs/tasks/[Owner_Office_Code]/[Parent_Process_ID]/[Task_ID]_[Task_Name_PascalCase]/[Task_ID]_[Task_Name_PascalCase]_Definition.md` (Output: New Task Definition)
* `docs/matrices/task_matrix.md` (Output: Updated Matrix)

## Steps

1. **Assign Task ID**
   * **Action**: Read `docs/matrices/task_matrix.md` to determine the next available `Task_ID`.
   * **Tool**: `view_file_outline`
   * **Guidance**: Read the last task entry, extract its ID (e.g., `T0042`), increment the numeric part, and format it back (e.g., `T0043`). This becomes the new `Task_ID`.

2. **Gather & Validate Inputs**
   * **Action**: Prompt the user/agent for all required information as specified in the template, including `Task Name`, `Owner Office`, `Parent Process ID`, execution details, and framework specifics.
   * **Guidance**: Convert `Task Name` to `PascalCase` for use in file and directory names.

3. **Create Task Directory**
   * **Action**: Create the dedicated, nested directory for the new task definition.
   * **Tool**: `run_command`
   * **Example**:

     ```markdown
     <!-- run_command('mkdir -p docs/tasks/[Owner_Office_Code]/[Parent_Process_ID]/[Task_ID]_[Task_Name_PascalCase]') -->
     ```

4. **Create Task Definition File**
   * **Action**: Create the definition file by copying the standard template (`estratix_task_definition_template.md`) and populating it with the gathered information.
   * **Guidance**: The populated file must contain all standard sections: Description, Inputs, Expected Output, Required Tools, Acceptance Criteria, and Exception Handling. Each section must be filled with detailed, context-specific information to ensure the task is fully understandable and executable by an agent.
   * **Tool**: `run_command` (to copy) and `replace_file_content` (to populate).
   * **Example (Copy)**:

     ```markdown
     <!-- run_command('cp docs/templates/estratix_task_definition_template.md docs/tasks/[Owner_Office_Code]/[Parent_Process_ID]/[Task_ID]_[Task_Name_PascalCase]/[Task_ID]_[Task_Name_PascalCase]_Definition.md') -->
     ```

5. **Update the Task Matrix**
   * **Action**: Add a new entry for the task in `docs/matrices/task_matrix.md`.
   * **Tool**: `replace_file_content`
   * **Example**:

     ```markdown
     <!-- replace_file_content(
         'docs/matrices/task_matrix.md',
         ReplacementChunks=[{
             'AllowMultiple': false,
             'TargetContent': '| --- | --- | --- | --- | --- | --- |',
             'ReplacementContent': '| --- | --- | --- | --- | --- | --- |\n| `[Task_ID]` | `[Task Name]` | `[Parent Process ID]` | `[Parent Service ID]` | Defined | [Definition](../tasks/[Owner_Office_Code]/[Parent_Process_ID]/[Task_ID]_[Task_Name_PascalCase]/[Task_ID]_[Task_Name_PascalCase]_Definition.md) |'
         }]
     ) -->
     ```

   * **Guidance**: The number of `| --- |` columns must exactly match the table in `task_matrix.md`.

6. **Confirmation & Next Steps**
   * **Action**: Confirm that the task has been defined and registered.
   * **Output**: Provide a direct link to the new definition file and suggest running the `/task_generation` workflow.

## Guidance for Use

* This workflow is the cornerstone of creating actionable, traceable, and automatable work units in ESTRATIX.
* Providing detailed context in each section is crucial for enabling autonomous agent operations and maintaining project observability.