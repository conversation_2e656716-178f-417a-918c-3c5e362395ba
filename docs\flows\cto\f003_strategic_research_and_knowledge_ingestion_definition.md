# ESTRATIX Flow Definition: Automated Web & Document Ingestion Flow

**Flow ID:** CTO_F003

**Version:** 1.0

**Status:** Defined

**Date Created:** 2025-06-16

## 1. Overview

**Flow Name:** Automated Web & Document Ingestion Flow

**Description:**
This flow orchestrates the technical steps required to scrape content from a specified source (e.g., a website, a PDF from a URL), process it, and embed it into the Milvus vector database for long-term memory and analysis.

## 2. Orchestration

**Trigger:** New entry in `ingestion_log_matrix.md` with status 'Pending'.

**Orchestrator Agent:** `AGENT_Ingestion_Coordinator`

**Process:** Sequential

## 3. Steps & Associated Agents

1. **Task Pickup:** `AGENT_Ingestion_Coordinator` identifies a pending ingestion task.
2. **Select Scraper:** The coordinator selects the appropriate scraping tool/agent based on the `Source ID` (e.g., `Tool_WebScraper`, `Tool_PDFProcessor`).
3. **Execute Scraping:** The selected scraping agent executes the data extraction.
4. **Log Raw Content:** The raw content is stored temporarily and the `ingestion_log_matrix.md` is updated to 'Scraped'.
5. **Process & Chunk Content:** `AGENT_Content_Processor` cleans and chunks the text for embedding.
6. **Generate & Store Embeddings:** `AGENT_VectorDB_Manager` generates embeddings and stores them in Milvus, updating the log with the `Embedding ID` and setting the status to 'Embedded'.

## 4. Associated Processes

- `CTO_P003_StrategicResearchAndKnowledgeIngestion`
