---
trigger: always_on
---

# ESTRATIX Testing Execution Standards - High Momentum TDD

## 1. Core Testing Philosophy

### 1.1. High-Velocity Test-Driven Development

**ESTRATIX operates on RAPID, CONTINUOUS testing cycles:**

- **Test-First Approach**: Write tests before implementation
- **UV-Powered Testing**: All test execution uses `uv run pytest`
- **Continuous Validation**: Tests run on every code change
- **Fast Feedback Loops**: Test results in < 30 seconds
- **Quality Gates**: No code ships without passing tests

### 1.2. Testing Velocity Metrics

```bash
# Test execution speed tracking
uv run pytest --durations=10 --tb=short

# Test coverage velocity
uv run pytest --cov=src --cov-report=term-missing

# Test reliability metrics
uv run python scripts/test_reliability.py --period daily
```

## 2. UV-Powered Testing Workflow

### 2.1. Rapid Test Setup

```bash
# Initialize testing environment (< 10 seconds)
uv add --dev pytest pytest-cov pytest-mock pytest-asyncio
uv add --dev pytest-xdist pytest-benchmark pytest-html
uv add --dev factory-boy faker hypothesis

# Configure pytest
cat > pytest.ini << EOF
[tool:pytest]
minversion = 6.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
markers =
    unit: Unit tests
    integration: Integration tests
    e2e: End-to-end tests
    slow: Slow running tests
    fast: Fast running tests
    critical: Critical path tests
EOF
```

### 2.2. Daily Testing Cycle

```bash
# Morning test validation (< 30 seconds)
uv run pytest -m "unit and fast" --maxfail=3
uv run pytest -m "critical" --maxfail=1

# Development testing loop
uv run pytest --lf --tb=short              # Last failed tests
uv run pytest tests/test_current.py -v     # Current development
uv run pytest --cov=src/module --cov-report=term

# Pre-commit testing (< 60 seconds)
uv run pytest -m "unit" --cov=src --cov-fail-under=80
uv run pytest -m "integration" --maxfail=5
```

### 2.3. Continuous Integration Testing

```bash
# CI test pipeline
uv run pytest --cov=src --cov-report=xml --cov-fail-under=80
uv run pytest -m "integration" --junitxml=integration-results.xml
uv run pytest -m "e2e" --html=e2e-report.html --self-contained-html

# Performance testing
uv run pytest tests/benchmarks/ --benchmark-json=benchmark.json

# Security testing
uv run pytest tests/security/ --tb=short
```

## 3. High-Performance Testing Patterns

### 3.1. Parallel Test Execution

```bash
# Multi-core test execution
uv run pytest -n auto --dist=worksteal

# Distributed testing across environments
uv run pytest --dist=each --tx=popen//python=python3.11

# Load-balanced test execution
uv run pytest -n 4 --dist=loadscope
```

### 3.2. Smart Test Selection

```bash
# Test only changed code
uv run pytest --testmon

# Test based on git changes
uv run pytest --picked=first

# Test critical paths first
uv run pytest -m "critical" --maxfail=1 && uv run pytest -m "unit"
```

### 3.3. Fast Feedback Testing

```bash
# Watch mode for instant feedback
uv run ptw --runner "pytest -x -vs"

# Continuous testing with coverage
uv run ptw --runner "pytest --cov=src --cov-report=term-missing"

# Real-time test results
uv run pytest --live-log --log-cli-level=INFO
```

## 4. Agent Testing Framework

### 4.1. Agent Unit Testing

```bash
# Test agent initialization
uv run pytest tests/agents/test_agent_init.py -v

# Test agent tools
uv run pytest tests/agents/test_agent_tools.py --cov=src/agents

# Test agent workflows
uv run pytest tests/agents/test_workflows.py -m "unit"
```

### 4.2. Agent Integration Testing

```bash
# Test agent communication
uv run pytest tests/agents/test_communication.py -m "integration"

# Test multi-agent coordination
uv run pytest tests/agents/test_coordination.py --timeout=300

# Test agent-tool integration
uv run pytest tests/agents/test_tool_integration.py -v
```

### 4.3. Agent Performance Testing

```bash
# Agent response time testing
uv run pytest tests/agents/test_performance.py --benchmark-only

# Agent memory usage testing
uv run pytest tests/agents/test_memory.py --memray

# Agent scalability testing
uv run pytest tests/agents/test_scalability.py --stress
```

## 5. Tool Testing Standards

### 5.1. Tool Validation Testing

```bash
# Test tool interfaces
uv run pytest tests/tools/test_interfaces.py --strict

# Test tool functionality
uv run pytest tests/tools/test_functionality.py --cov=src/tools

# Test tool error handling
uv run pytest tests/tools/test_error_handling.py -v
```

### 5.2. Tool Integration Testing

```bash
# Test tool-agent integration
uv run pytest tests/tools/test_agent_integration.py -m "integration"

# Test tool chaining
uv run pytest tests/tools/test_chaining.py --timeout=120

# Test tool dependencies
uv run pytest tests/tools/test_dependencies.py -v
```

### 5.3. Tool Performance Testing

```bash
# Tool response time benchmarks
uv run pytest tests/tools/test_performance.py --benchmark-only

# Tool memory efficiency
uv run pytest tests/tools/test_memory.py --profile

# Tool scalability under load
uv run pytest tests/tools/test_load.py --stress
```

## 6. Infrastructure Testing

### 6.1. Database Testing

```bash
# Vector database testing
uv run pytest tests/infrastructure/test_vector_db.py -v

# MongoDB testing
uv run pytest tests/infrastructure/test_mongodb.py --cov=src/infrastructure

# Neo4j testing
uv run pytest tests/infrastructure/test_neo4j.py -m "integration"
```

### 6.2. API Testing

```bash
# FastAPI endpoint testing
uv run pytest tests/api/test_endpoints.py --cov=src/api

# API integration testing
uv run pytest tests/api/test_integration.py -m "integration"

# API performance testing
uv run pytest tests/api/test_performance.py --benchmark-only
```

### 6.3. Deployment Testing

```bash
# Container testing
uv run pytest tests/deployment/test_containers.py -v

# Kubernetes testing
uv run pytest tests/deployment/test_k8s.py -m "integration"

# Health check testing
uv run pytest tests/deployment/test_health.py --timeout=60
```

## 7. Security Testing

### 7.1. Authentication Testing

```bash
# Authentication flow testing
uv run pytest tests/security/test_auth.py -v

# Authorization testing
uv run pytest tests/security/test_authz.py --strict

# Session management testing
uv run pytest tests/security/test_sessions.py -m "security"
```

### 7.2. Vulnerability Testing

```bash
# SQL injection testing
uv run pytest tests/security/test_sql_injection.py -v

# XSS testing
uv run pytest tests/security/test_xss.py --strict

# CSRF testing
uv run pytest tests/security/test_csrf.py -m "security"
```

### 7.3. Data Protection Testing

```bash
# Encryption testing
uv run pytest tests/security/test_encryption.py -v

# Data masking testing
uv run pytest tests/security/test_masking.py --strict

# Privacy compliance testing
uv run pytest tests/security/test_privacy.py -m "compliance"
```

## 8. Performance Testing

### 8.1. Load Testing

```bash
# API load testing
uv run pytest tests/performance/test_load.py --benchmark-only

# Database load testing
uv run pytest tests/performance/test_db_load.py --stress

# System load testing
uv run pytest tests/performance/test_system_load.py --timeout=300
```

### 8.2. Stress Testing

```bash
# Memory stress testing
uv run pytest tests/performance/test_memory_stress.py --memray

# CPU stress testing
uv run pytest tests/performance/test_cpu_stress.py --profile

# Network stress testing
uv run pytest tests/performance/test_network_stress.py --timeout=600
```

### 8.3. Scalability Testing

```bash
# Horizontal scaling testing
uv run pytest tests/performance/test_horizontal_scaling.py -v

# Vertical scaling testing
uv run pytest tests/performance/test_vertical_scaling.py --benchmark

# Auto-scaling testing
uv run pytest tests/performance/test_auto_scaling.py --timeout=900
```

## 9. Test Data Management

### 9.1. Test Data Generation

```bash
# Generate test data
uv run python scripts/generate_test_data.py --size large

# Factory-based data generation
uv run pytest tests/factories/test_data_factories.py -v

# Synthetic data generation
uv run python scripts/generate_synthetic_data.py --type agents
```

### 9.2. Test Data Cleanup

```bash
# Clean test databases
uv run python scripts/clean_test_data.py --all

# Reset test environments
uv run python scripts/reset_test_env.py --env staging

# Purge old test artifacts
uv run python scripts/purge_test_artifacts.py --older-than 7d
```

### 9.3. Test Data Validation

```bash
# Validate test data integrity
uv run pytest tests/data/test_data_integrity.py -v

# Test data consistency checks
uv run pytest tests/data/test_data_consistency.py --strict

# Test data compliance validation
uv run pytest tests/data/test_data_compliance.py -m "compliance"
```

## 10. Test Reporting & Analytics

### 10.1. Test Result Reporting

```bash
# Generate HTML test reports
uv run pytest --html=reports/test_report.html --self-contained-html

# Generate JUnit XML reports
uv run pytest --junitxml=reports/junit.xml

# Generate coverage reports
uv run pytest --cov=src --cov-report=html:reports/coverage
```

### 10.2. Test Analytics

```bash
# Test execution analytics
uv run python scripts/analyze_test_execution.py --period weekly

# Test coverage analytics
uv run python scripts/analyze_test_coverage.py --trend

# Test reliability analytics
uv run python scripts/analyze_test_reliability.py --flaky
```

### 10.3. Test Optimization

```bash
# Identify slow tests
uv run pytest --durations=0 --tb=no

# Optimize test execution order
uv run python scripts/optimize_test_order.py

# Reduce test execution time
uv run python scripts/reduce_test_time.py --target 50%
```

---

## Enforcement & Compliance

- **Automated Enforcement**: All testing standards are enforced through CI/CD pipelines
- **Quality Gates**: No code merges without passing all required tests
- **Continuous Monitoring**: Test execution metrics are tracked and analyzed
- **Performance Benchmarks**: Test performance is continuously optimized
- **Strategic Alignment**: Testing activities align with business objectives and quality goals

---

**Last Updated**: 2025-01-27  
**Version**: 2.0  
**Status**: Active - High Momentum TDD Operations