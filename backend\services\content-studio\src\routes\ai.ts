import { FastifyPluginAsync } from 'fastify';
import { z } from 'zod';
import { AIContentService } from '../services/aiContentService';
import { ContentGenerationQueue } from '../queues/contentGeneration';
import { logger } from '../utils/logger';

// Validation schemas
const generateContentSchema = z.object({
  type: z.enum(['blog_post', 'social_media', 'email_campaign', 'ad_copy', 'video_script', 'podcast_script', 'press_release', 'product_description']),
  topic: z.string().min(1),
  tone: z.enum(['professional', 'casual', 'friendly', 'authoritative', 'creative', 'persuasive', 'informative']),
  length: z.enum(['short', 'medium', 'long']),
  targetAudience: z.string().optional(),
  keywords: z.array(z.string()).optional(),
  brandGuidelines: z.object({
    brandName: z.string(),
    brandVoice: z.string(),
    brandValues: z.array(z.string()),
    doNotUse: z.array(z.string()).optional(),
  }).optional(),
  customInstructions: z.string().optional(),
  includeImages: z.boolean().default(false),
  includeCallToAction: z.boolean().default(true),
  seoOptimized: z.boolean().default(true),
});

const generateCampaignSchema = z.object({
  campaignType: z.enum(['social_media', 'email_sequence', 'content_series', 'advertising', 'influencer']),
  duration: z.number().min(1).max(365), // days
  frequency: z.enum(['daily', 'weekly', 'bi-weekly', 'monthly']),
  platforms: z.array(z.enum(['facebook', 'instagram', 'twitter', 'linkedin', 'tiktok', 'youtube', 'email', 'blog'])),
  objectives: z.array(z.enum(['awareness', 'engagement', 'conversion', 'retention', 'education'])),
  targetAudience: z.object({
    demographics: z.string(),
    interests: z.array(z.string()),
    painPoints: z.array(z.string()),
  }),
  brandInfo: z.object({
    name: z.string(),
    industry: z.string(),
    uniqueSellingProposition: z.string(),
    competitorAnalysis: z.string().optional(),
  }),
  budget: z.number().optional(),
  kpis: z.array(z.string()),
});

const optimizeContentSchema = z.object({
  content: z.string().min(1),
  optimizationType: z.enum(['seo', 'engagement', 'conversion', 'readability', 'accessibility']),
  targetKeywords: z.array(z.string()).optional(),
  platform: z.string().optional(),
  currentMetrics: z.object({
    views: z.number().optional(),
    engagement: z.number().optional(),
    conversions: z.number().optional(),
  }).optional(),
});

const analyzeContentSchema = z.object({
  content: z.string().min(1),
  analysisType: z.enum(['sentiment', 'readability', 'seo', 'brand_compliance', 'plagiarism', 'performance_prediction']),
  compareWith: z.string().optional(), // URL or content to compare with
});

export const aiRoutes: FastifyPluginAsync = async (fastify) => {
  const aiContentService = new AIContentService();
  const contentQueue = new ContentGenerationQueue();

  // Generate single piece of content
  fastify.post('/generate', {
    schema: {
      description: 'Generate AI-powered content based on specifications',
      tags: ['AI Content Generation'],
      security: [{ Bearer: [] }],
      body: generateContentSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                content: { type: 'string' },
                metadata: {
                  type: 'object',
                  properties: {
                    wordCount: { type: 'number' },
                    readingTime: { type: 'number' },
                    seoScore: { type: 'number' },
                    sentimentScore: { type: 'number' },
                    keywords: { type: 'array', items: { type: 'string' } },
                  },
                },
                suggestions: {
                  type: 'array',
                  items: { type: 'string' },
                },
                images: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      url: { type: 'string' },
                      alt: { type: 'string' },
                      caption: { type: 'string' },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const params = generateContentSchema.parse(request.body);
      const userId = request.user?.id;

      logger.info(`Generating ${params.type} content for user ${userId}`);

      const result = await aiContentService.generateContent({
        ...params,
        userId,
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      logger.error('Error generating content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to generate content',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  // Generate content campaign
  fastify.post('/generate-campaign', {
    schema: {
      description: 'Generate a comprehensive content campaign with multiple pieces',
      tags: ['AI Content Generation'],
      security: [{ Bearer: [] }],
      body: generateCampaignSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                campaignId: { type: 'string' },
                status: { type: 'string' },
                estimatedCompletion: { type: 'string' },
                contentPieces: { type: 'number' },
                timeline: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      date: { type: 'string' },
                      content: { type: 'string' },
                      platform: { type: 'string' },
                      type: { type: 'string' },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const params = generateCampaignSchema.parse(request.body);
      const userId = request.user?.id;

      logger.info(`Generating content campaign for user ${userId}`);

      // Queue campaign generation job
      const job = await contentQueue.addCampaignJob({
        ...params,
        userId,
      });

      const campaign = await aiContentService.generateCampaign({
        ...params,
        userId,
        jobId: job.id,
      });

      return {
        success: true,
        data: campaign,
      };
    } catch (error) {
      logger.error('Error generating campaign:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to generate campaign',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  // Optimize existing content
  fastify.post('/optimize', {
    schema: {
      description: 'Optimize existing content for better performance',
      tags: ['AI Content Optimization'],
      security: [{ Bearer: [] }],
      body: optimizeContentSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                originalContent: { type: 'string' },
                optimizedContent: { type: 'string' },
                improvements: {
                  type: 'array',
                  items: { type: 'string' },
                },
                metrics: {
                  type: 'object',
                  properties: {
                    readabilityScore: { type: 'number' },
                    seoScore: { type: 'number' },
                    engagementPrediction: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const params = optimizeContentSchema.parse(request.body);
      const userId = request.user?.id;

      logger.info(`Optimizing content for user ${userId}`);

      const result = await aiContentService.optimizeContent({
        ...params,
        userId,
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      logger.error('Error optimizing content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to optimize content',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  // Analyze content performance and quality
  fastify.post('/analyze', {
    schema: {
      description: 'Analyze content for various metrics and insights',
      tags: ['AI Content Analysis'],
      security: [{ Bearer: [] }],
      body: analyzeContentSchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                analysis: {
                  type: 'object',
                  properties: {
                    sentiment: {
                      type: 'object',
                      properties: {
                        score: { type: 'number' },
                        label: { type: 'string' },
                        confidence: { type: 'number' },
                      },
                    },
                    readability: {
                      type: 'object',
                      properties: {
                        score: { type: 'number' },
                        grade: { type: 'string' },
                        suggestions: { type: 'array', items: { type: 'string' } },
                      },
                    },
                    seo: {
                      type: 'object',
                      properties: {
                        score: { type: 'number' },
                        keywords: { type: 'array', items: { type: 'string' } },
                        recommendations: { type: 'array', items: { type: 'string' } },
                      },
                    },
                    brandCompliance: {
                      type: 'object',
                      properties: {
                        score: { type: 'number' },
                        violations: { type: 'array', items: { type: 'string' } },
                        suggestions: { type: 'array', items: { type: 'string' } },
                      },
                    },
                  },
                },
                recommendations: { type: 'array', items: { type: 'string' } },
                performancePrediction: {
                  type: 'object',
                  properties: {
                    engagementScore: { type: 'number' },
                    viralityPotential: { type: 'number' },
                    conversionPotential: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const params = analyzeContentSchema.parse(request.body);
      const userId = request.user?.id;

      logger.info(`Analyzing content for user ${userId}`);

      const result = await aiContentService.analyzeContent({
        ...params,
        userId,
      });

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      logger.error('Error analyzing content:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to analyze content',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  // Get content generation job status
  fastify.get('/jobs/:jobId', {
    schema: {
      description: 'Get status of a content generation job',
      tags: ['AI Content Generation'],
      security: [{ Bearer: [] }],
      params: {
        type: 'object',
        properties: {
          jobId: { type: 'string' },
        },
        required: ['jobId'],
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                status: { type: 'string' },
                progress: { type: 'number' },
                result: { type: 'object' },
                error: { type: 'string' },
                createdAt: { type: 'string' },
                completedAt: { type: 'string' },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const { jobId } = request.params as { jobId: string };
      const userId = request.user?.id;

      const jobStatus = await contentQueue.getJobStatus(jobId, userId);

      return {
        success: true,
        data: jobStatus,
      };
    } catch (error) {
      logger.error('Error getting job status:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to get job status',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });

  // Get AI models and capabilities
  fastify.get('/models', {
    schema: {
      description: 'Get available AI models and their capabilities',
      tags: ['AI Content Generation'],
      security: [{ Bearer: [] }],
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                models: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      id: { type: 'string' },
                      name: { type: 'string' },
                      provider: { type: 'string' },
                      capabilities: { type: 'array', items: { type: 'string' } },
                      maxTokens: { type: 'number' },
                      costPerToken: { type: 'number' },
                      speed: { type: 'string' },
                      quality: { type: 'string' },
                    },
                  },
                },
                recommendations: {
                  type: 'object',
                  properties: {
                    creative: { type: 'string' },
                    analytical: { type: 'string' },
                    technical: { type: 'string' },
                    marketing: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  }, async (request, reply) => {
    try {
      const models = await aiContentService.getAvailableModels();

      return {
        success: true,
        data: models,
      };
    } catch (error) {
      logger.error('Error getting AI models:', error);
      reply.code(500);
      return {
        success: false,
        error: 'Failed to get AI models',
        details: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  });
};