---
description: (MASTER WORKFLOW) Bootstraps a new, composable ESTRATIX internal subproject from an approved proposal, creating an integrated, hexagonal structure within the master project.
---

# MASTER WORKFLOW: Bootstrap ESTRATIX Internal Project

**Objective:** To provide a single, orchestrated workflow that takes a new internal project from concept to a fully scaffolded state, with separate, architecturally-compliant documentation and source code structures.

**Agentic Orchestrator:** `COO_A001_MasterOrchestratorAgent`

**Architectural Principles:**

- **Separation of Concerns:** Project documentation and management artifacts are stored separately from implementation source code.
- **Monolithic Source:** All source code resides within the root `src/` directory, organized by domain.
- **Composability:** Each phase is a distinct step, allowing for flexibility and automation.

---

## Phase 1: Project Definition

**Goal**: Formally define the project, creating its identity and documentation home.

1. **Execute Definition Workflow**
   - **Action**: Run the `/estratix_project_definition` workflow.
   - **Inputs**: Project Name, Description, Goals, Stakeholders.
   - **Outputs**:
     - An entry in `docs/matrices/project_matrix.md`.
     - A new directory: `docs/projects/estratix_master_project/02_Subprojects/[Project_ID]_[ProjectName]/`.
     - The `[Project_ID]`, `[ProjectName]`, and `[ProjectName_Snake_Case]` variables are now available for subsequent phases.

---

## Phase 2: Documentation & Management Scaffolding

**Goal**: Create the standard project management folder structure and populate it with essential templates.

1. **Create Management Directories**
   - **Action**: Create the standard project management phase directories inside the new project's documentation path.
   - **Tool**: `run_command`
   - **Command**:

     ```markdown
     <!-- run_command(
         Cwd='docs/projects/estratix_master_project/02_Subprojects/[Project_ID]_[ProjectName]/',
         CommandLine='mkdir 00_ProjectInitiation 01_ProjectPlanning 02_ProjectExecution 03_ProjectMonitoringControlling 04_ProjectClosure'
     ) -->
     ```

2. **Populate with Standard Templates**
   - **Action**: Copy the core planning and management documents from `docs/templates/project_management/` into the corresponding phase directories.
   - **Tool**: `run_command`
   - **Commands**:

     ```markdown
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Project_Charter_Template.md -Destination docs\projects\estratix_master_project\02_Subprojects\[Project_ID]_[ProjectName]\00_ProjectInitiation\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Project_Plan_Template.md -Destination docs\projects\estratix_master_project\02_Subprojects\[Project_ID]_[ProjectName]\01_ProjectPlanning\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Requirements_Specification_Template.md -Destination docs\projects\estratix_master_project\02_Subprojects\[Project_ID]_[ProjectName]\01_ProjectPlanning\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Status_Report_Template.md -Destination docs\projects\estratix_master_project\02_Subprojects\[Project_ID]_[ProjectName]\03_ProjectMonitoringControlling\') -->
     <!-- run_command('Copy-Item -Path docs\templates\project_management\Project_Closure_Report_Template.md -Destination docs\projects\estratix_master_project\02_Subprojects\[Project_ID]_[ProjectName]\04_ProjectClosure\') -->
     ```

---

## Phase 3: Source Code Scaffolding

**Goal**: Create the baseline DDD/Hexagonal source code structure in the correct monorepo location.

1. **Execute Code Generation Workflow**
   - **Action**: Run the `/estratix_project_generation` workflow.
   - **Inputs**: `[ProjectName_Snake_Case]` from Phase 1.
   - **Key Actions of Workflow**:
     - Creates the root directory: `src/domains/projects/[ProjectName_Snake_Case]/`.
     - Creates the layered hexagonal architecture: `domain/`, `application/`, `infrastructure/`, `tests/`.
     - Populates layers with placeholder `README.md` and `__init__.py` files.

---

## 4. Final Outputs

- **Documentation:** A complete project management structure populated with templates, located at `docs/projects/estratix_master_project/02_Subprojects/[Project_ID]_[ProjectName]/`.
- **Source Code:** An architecturally-compliant, baseline source code directory ready for development, located at `src/domains/projects/[ProjectName_Snake_Case]/`.
- **Project Registration:** The project is officially registered in the `project_matrix.md`.
