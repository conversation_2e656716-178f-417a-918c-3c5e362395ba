import { useState } from 'react';
import { motion } from 'framer-motion';
import {
  UserIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  ClockIcon,
  StarIcon,
  ChartBarIcon,
  BellIcon,
  CogIcon,
  DocumentTextIcon,
  PhotoIcon,
  PlusIcon,
  PencilIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  MapPinIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  TrophyIcon,
  HandThumbUpIcon,
  ChatBubbleLeftRightIcon,
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon } from '@heroicons/react/24/solid';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, Pie<PERSON>hart, Pie, Cell } from 'recharts';

const providerStats = {
  totalEarnings: 45680.50,
  monthlyEarnings: 8920.75,
  completedJobs: 156,
  activeBookings: 8,
  rating: 4.8,
  reviewCount: 142,
  responseTime: '2 hours',
  completionRate: 98.5,
};

const recentBookings = [
  {
    id: 1,
    service: 'Deep Cleaning',
    client: '<PERSON>',
    property: 'Beacon Hill Penthouse',
    date: '2024-01-18',
    time: '10:00 AM',
    duration: '4 hours',
    amount: 280.00,
    status: 'confirmed',
    notes: 'Post-renovation cleaning, focus on dust removal',
  },
  {
    id: 2,
    service: 'Plumbing Repair',
    client: 'Michael Chen',
    property: 'Back Bay Condo',
    date: '2024-01-19',
    time: '2:00 PM',
    duration: '2 hours',
    amount: 150.00,
    status: 'pending',
    notes: 'Kitchen sink leak repair',
  },
  {
    id: 3,
    service: 'Garden Maintenance',
    client: 'Emily Davis',
    property: 'Newton Townhouse',
    date: '2024-01-20',
    time: '9:00 AM',
    duration: '3 hours',
    amount: 200.00,
    status: 'confirmed',
    notes: 'Monthly garden maintenance and pruning',
  },
  {
    id: 4,
    service: 'HVAC Maintenance',
    client: 'Robert Wilson',
    property: 'Cambridge Loft',
    date: '2024-01-16',
    time: '11:00 AM',
    duration: '2 hours',
    amount: 180.00,
    status: 'completed',
    notes: 'Quarterly HVAC system check and filter replacement',
  },
];

const services = [
  {
    id: 1,
    name: 'Deep Cleaning',
    category: 'Cleaning',
    price: 70,
    unit: 'hour',
    description: 'Comprehensive deep cleaning service for luxury properties',
    duration: '2-6 hours',
    availability: 'Available',
    bookings: 45,
    rating: 4.9,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20deep%20cleaning%20service&image_size=landscape_4_3',
  },
  {
    id: 2,
    name: 'Plumbing Services',
    category: 'Maintenance',
    price: 85,
    unit: 'hour',
    description: 'Expert plumbing repairs and installations',
    duration: '1-4 hours',
    availability: 'Available',
    bookings: 32,
    rating: 4.7,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20plumbing%20service&image_size=landscape_4_3',
  },
  {
    id: 3,
    name: 'Garden Maintenance',
    category: 'Landscaping',
    price: 65,
    unit: 'hour',
    description: 'Professional garden care and landscaping',
    duration: '2-5 hours',
    availability: 'Seasonal',
    bookings: 28,
    rating: 4.8,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20garden%20maintenance%20service&image_size=landscape_4_3',
  },
  {
    id: 4,
    name: 'HVAC Services',
    category: 'Maintenance',
    price: 90,
    unit: 'hour',
    description: 'Heating, ventilation, and air conditioning services',
    duration: '1-3 hours',
    availability: 'Available',
    bookings: 21,
    rating: 4.6,
    image: 'https://trae-api-us.mchost.guru/api/ide/v1/text_to_image?prompt=professional%20HVAC%20maintenance%20service&image_size=landscape_4_3',
  },
];

const earningsData = [
  { month: 'Jul', earnings: 3200 },
  { month: 'Aug', earnings: 4100 },
  { month: 'Sep', earnings: 3800 },
  { month: 'Oct', earnings: 4500 },
  { month: 'Nov', earnings: 5200 },
  { month: 'Dec', earnings: 4800 },
  { month: 'Jan', earnings: 5600 },
];

const serviceDistribution = [
  { name: 'Cleaning', value: 45, color: '#3b82f6' },
  { name: 'Maintenance', value: 35, color: '#10b981' },
  { name: 'Landscaping', value: 15, color: '#f59e0b' },
  { name: 'Other', value: 5, color: '#8b5cf6' },
];

const recentReviews = [
  {
    id: 1,
    client: 'Sarah Johnson',
    service: 'Deep Cleaning',
    rating: 5,
    comment: 'Exceptional service! The team was professional and thorough.',
    date: '2024-01-15',
    property: 'Beacon Hill Penthouse',
  },
  {
    id: 2,
    client: 'Michael Chen',
    service: 'Plumbing Repair',
    rating: 5,
    comment: 'Quick response and excellent work. Highly recommended!',
    date: '2024-01-12',
    property: 'Back Bay Condo',
  },
  {
    id: 3,
    client: 'Emily Davis',
    service: 'Garden Maintenance',
    rating: 4,
    comment: 'Good work overall, garden looks much better now.',
    date: '2024-01-10',
    property: 'Newton Townhouse',
  },
];

export default function ProviderPortal() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showServiceModal, setShowServiceModal] = useState(false);
  const [editingService, setEditingService] = useState(null);

  const tabs = [
    { id: 'dashboard', name: 'Dashboard' },
    { id: 'bookings', name: 'Bookings' },
    { id: 'services', name: 'My Services' },
    { id: 'earnings', name: 'Earnings' },
    { id: 'reviews', name: 'Reviews' },
    { id: 'profile', name: 'Profile' },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case 'confirmed': return 'text-green-600 bg-green-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      case 'completed': return 'text-blue-600 bg-blue-100';
      case 'cancelled': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'confirmed': return CheckCircleIcon;
      case 'pending': return ExclamationTriangleIcon;
      case 'completed': return CheckCircleIcon;
      case 'cancelled': return XCircleIcon;
      default: return ClockIcon;
    }
  };

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <StarSolidIcon
        key={i}
        className={`w-4 h-4 ${
          i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
        }`}
      />
    ));
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Provider Portal
              </h1>
              <p className="text-xl text-gray-600">
                Manage your services, bookings, and grow your business
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <BellIcon className="w-6 h-6" />
              </button>
              <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
                <CogIcon className="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Dashboard Tab */}
        {activeTab === 'dashboard' && (
          <div className="space-y-8">
            {/* Stats Overview */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                    <p className="text-2xl font-bold text-gray-900">${providerStats.totalEarnings.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+12.5% this month</p>
                  </div>
                  <CurrencyDollarIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed Jobs</p>
                    <p className="text-2xl font-bold text-gray-900">{providerStats.completedJobs}</p>
                    <p className="text-sm text-blue-600">{providerStats.completionRate}% completion rate</p>
                  </div>
                  <CheckCircleIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-gray-900">{providerStats.rating}</p>
                      <div className="flex">
                        {renderStars(providerStats.rating)}
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">{providerStats.reviewCount} reviews</p>
                  </div>
                  <StarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Bookings</p>
                    <p className="text-2xl font-bold text-gray-900">{providerStats.activeBookings}</p>
                    <p className="text-sm text-purple-600">Avg response: {providerStats.responseTime}</p>
                  </div>
                  <CalendarIcon className="w-8 h-8 text-purple-500" />
                </div>
              </div>
            </div>

            {/* Earnings Chart */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Earnings Trend</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={earningsData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`$${value}`, 'Earnings']} />
                    <Line
                      type="monotone"
                      dataKey="earnings"
                      stroke="#3b82f6"
                      strokeWidth={3}
                      dot={{ fill: '#3b82f6', strokeWidth: 2, r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Recent Bookings */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-xl font-bold text-gray-900">Recent Bookings</h3>
                  <button
                    onClick={() => setActiveTab('bookings')}
                    className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View All
                  </button>
                </div>
                <div className="space-y-4">
                  {recentBookings.slice(0, 3).map((booking) => {
                    const StatusIcon = getStatusIcon(booking.status);
                    return (
                      <div key={booking.id} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                          booking.status === 'confirmed' ? 'bg-green-100' :
                          booking.status === 'pending' ? 'bg-yellow-100' :
                          booking.status === 'completed' ? 'bg-blue-100' :
                          'bg-red-100'
                        }`}>
                          <StatusIcon className={`w-5 h-5 ${
                            booking.status === 'confirmed' ? 'text-green-600' :
                            booking.status === 'pending' ? 'text-yellow-600' :
                            booking.status === 'completed' ? 'text-blue-600' :
                            'text-red-600'
                          }`} />
                        </div>
                        <div className="flex-1">
                          <p className="font-medium text-gray-900">{booking.service}</p>
                          <p className="text-sm text-gray-600">{booking.client} • {booking.property}</p>
                          <p className="text-sm text-gray-500">{booking.date} at {booking.time}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-900">${booking.amount}</p>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Service Performance */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-6">Service Distribution</h3>
                <div className="h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={serviceDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {serviceDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bookings Tab */}
        {activeTab === 'bookings' && (
          <div className="space-y-8">
            {/* Booking Filters */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <input
                    type="text"
                    placeholder="Search bookings..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex space-x-4">
                  <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>All Status</option>
                    <option>Pending</option>
                    <option>Confirmed</option>
                    <option>Completed</option>
                    <option>Cancelled</option>
                  </select>
                  <select className="px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option>All Services</option>
                    <option>Cleaning</option>
                    <option>Maintenance</option>
                    <option>Landscaping</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Bookings List */}
            <div className="bg-white rounded-xl shadow-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-900">All Bookings</h3>
              </div>
              <div className="divide-y divide-gray-200">
                {recentBookings.map((booking) => {
                  const StatusIcon = getStatusIcon(booking.status);
                  return (
                    <div key={booking.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                            booking.status === 'confirmed' ? 'bg-green-100' :
                            booking.status === 'pending' ? 'bg-yellow-100' :
                            booking.status === 'completed' ? 'bg-blue-100' :
                            'bg-red-100'
                          }`}>
                            <StatusIcon className={`w-6 h-6 ${
                              booking.status === 'confirmed' ? 'text-green-600' :
                              booking.status === 'pending' ? 'text-yellow-600' :
                              booking.status === 'completed' ? 'text-blue-600' :
                              'text-red-600'
                            }`} />
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-900">{booking.service}</h4>
                            <p className="text-gray-600">{booking.client} • {booking.property}</p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500 mt-1">
                              <span className="flex items-center space-x-1">
                                <CalendarIcon className="w-4 h-4" />
                                <span>{booking.date}</span>
                              </span>
                              <span className="flex items-center space-x-1">
                                <ClockIcon className="w-4 h-4" />
                                <span>{booking.time} ({booking.duration})</span>
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-gray-900">${booking.amount}</p>
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(booking.status)}`}>
                            {booking.status}
                          </span>
                          <div className="mt-2 space-x-2">
                            <button className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                              View Details
                            </button>
                            {booking.status === 'pending' && (
                              <button className="px-3 py-1 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                Accept
                              </button>
                            )}
                          </div>
                        </div>
                      </div>
                      {booking.notes && (
                        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">Notes:</span> {booking.notes}
                          </p>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Services Tab */}
        {activeTab === 'services' && (
          <div className="space-y-8">
            {/* Services Header */}
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold text-gray-900">My Services</h3>
              <button
                onClick={() => setShowServiceModal(true)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center space-x-2"
              >
                <PlusIcon className="w-5 h-5" />
                <span>Add Service</span>
              </button>
            </div>

            {/* Services Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {services.map((service) => (
                <motion.div
                  key={service.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
                >
                  <img
                    src={service.image}
                    alt={service.name}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-xl font-bold text-gray-900">{service.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        service.availability === 'Available' ? 'bg-green-100 text-green-800' :
                        service.availability === 'Seasonal' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {service.availability}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-4">{service.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                      <div>
                        <p className="text-gray-500">Price</p>
                        <p className="font-bold">${service.price}/{service.unit}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Duration</p>
                        <p className="font-bold">{service.duration}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Bookings</p>
                        <p className="font-bold">{service.bookings}</p>
                      </div>
                      <div>
                        <p className="text-gray-500">Rating</p>
                        <div className="flex items-center space-x-1">
                          <span className="font-bold">{service.rating}</span>
                          <div className="flex">
                            {renderStars(service.rating)}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-3">
                      <button
                        onClick={() => {
                          setEditingService(service);
                          setShowServiceModal(true);
                        }}
                        className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium flex items-center justify-center space-x-2"
                      >
                        <PencilIcon className="w-4 h-4" />
                        <span>Edit</span>
                      </button>
                      <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium flex items-center justify-center space-x-2">
                        <EyeIcon className="w-4 h-4" />
                        <span>View</span>
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        )}

        {/* Reviews Tab */}
        {activeTab === 'reviews' && (
          <div className="space-y-8">
            {/* Reviews Overview */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Average Rating</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-3xl font-bold text-gray-900">{providerStats.rating}</p>
                      <div className="flex">
                        {renderStars(providerStats.rating)}
                      </div>
                    </div>
                  </div>
                  <StarIcon className="w-8 h-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Reviews</p>
                    <p className="text-3xl font-bold text-gray-900">{providerStats.reviewCount}</p>
                    <p className="text-sm text-green-600">+8 this month</p>
                  </div>
                  <ChatBubbleLeftRightIcon className="w-8 h-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white rounded-xl p-6 shadow-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Recommendation Rate</p>
                    <p className="text-3xl font-bold text-gray-900">96%</p>
                    <p className="text-sm text-green-600">Excellent</p>
                  </div>
                  <HandThumbUpIcon className="w-8 h-8 text-green-500" />
                </div>
              </div>
            </div>

            {/* Recent Reviews */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Recent Reviews</h3>
              <div className="space-y-6">
                {recentReviews.map((review) => (
                  <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <UserIcon className="w-6 h-6 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div>
                            <h4 className="font-bold text-gray-900">{review.client}</h4>
                            <p className="text-sm text-gray-600">{review.service} • {review.property}</p>
                          </div>
                          <div className="text-right">
                            <div className="flex items-center space-x-1 mb-1">
                              {renderStars(review.rating)}
                            </div>
                            <p className="text-sm text-gray-500">{review.date}</p>
                          </div>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Service Modal */}
        {showServiceModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <h3 className="text-xl font-bold text-gray-900 mb-6">
                {editingService ? 'Edit Service' : 'Add New Service'}
              </h3>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Service Name
                    </label>
                    <input
                      type="text"
                      defaultValue={editingService?.name || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category
                    </label>
                    <select
                      defaultValue={editingService?.category || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option>Cleaning</option>
                      <option>Maintenance</option>
                      <option>Landscaping</option>
                      <option>Security</option>
                      <option>Other</option>
                    </select>
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    rows={3}
                    defaultValue={editingService?.description || ''}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price
                    </label>
                    <input
                      type="number"
                      defaultValue={editingService?.price || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Unit
                    </label>
                    <select
                      defaultValue={editingService?.unit || 'hour'}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="hour">per hour</option>
                      <option value="job">per job</option>
                      <option value="sqft">per sqft</option>
                      <option value="room">per room</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Duration
                    </label>
                    <input
                      type="text"
                      placeholder="e.g., 2-4 hours"
                      defaultValue={editingService?.duration || ''}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Service Image
                  </label>
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <PhotoIcon className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-600">Click to upload or drag and drop</p>
                    <p className="text-sm text-gray-500">PNG, JPG up to 10MB</p>
                  </div>
                </div>
                
                <div className="flex space-x-3">
                  <button
                    onClick={() => {
                      setShowServiceModal(false);
                      setEditingService(null);
                    }}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    {editingService ? 'Update Service' : 'Add Service'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}