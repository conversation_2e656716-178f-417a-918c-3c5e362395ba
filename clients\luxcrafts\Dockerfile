# Multi-stage build for production optimization
FROM node:20-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Copy package files
COPY package.json package-lock.json* ./
RUN npm ci --only=production && npm cache clean --force

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build arguments for environment variables
ARG VITE_WALLETCONNECT_PROJECT_ID
ARG VITE_ALCHEMY_API_KEY
ARG VITE_INFURA_API_KEY
ARG VITE_API_BASE_URL
ARG VITE_CHAIN_ID
ARG VITE_CONTRACT_ADDRESS
ARG VITE_LUX_TOKEN_ADDRESS

# Set environment variables
ENV VITE_WALLETCONNECT_PROJECT_ID=$VITE_WALLETCONNECT_PROJECT_ID
ENV VITE_ALCHEMY_API_KEY=$VITE_ALCHEMY_API_KEY
ENV VITE_INFURA_API_KEY=$VITE_INFURA_API_KEY
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_CHAIN_ID=$VITE_CHAIN_ID
ENV VITE_CONTRACT_ADDRESS=$VITE_CONTRACT_ADDRESS
ENV VITE_LUX_TOKEN_ADDRESS=$VITE_LUX_TOKEN_ADDRESS

# Build the application
RUN npm run build

# Production image, copy all the files and run nginx
FROM nginx:alpine AS runner

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Change ownership of nginx directories
RUN chown -R nextjs:nodejs /var/cache/nginx /var/run /var/log/nginx /usr/share/nginx/html

# Switch to non-root user
USER nextjs

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]