# ESTRATIX Payment Technology Matrix

---

## 1. Overview

This matrix catalogs all approved and evaluated payment processing technologies, including payment gateways and subscription management platforms. It governs the selection of financial technology to ensure security, compliance (e.g., PCI-DSS), and reliability for all ESTRATIX projects involving payments.

---

## 2. Technology Inventory

| Category | Technology | License | ESTRATIX Status | Primary Use Case | Integration Notes & Patterns | Traceability/Project Link |
|---|---|---|---|---|---|---|
| **Traditional Gateway** | Stripe | Proprietary | **Approved** | Comprehensive payment processing for online businesses. | Use `stripe-python` or `stripe-js`. Follow official docs for secure integration patterns. | `LIB-STRIPE` |
| **Traditional Gateway** | PayPal | Proprietary | **Approved** | Widely recognized payment gateway for online transactions. | Offers multiple integration products (Checkout, Subscriptions). | `LIB-PAYPAL` |
| **Merchant of Record** | Lemon Squeezy | Proprietary | **Evaluating** | Simplifies SaaS payments, subscriptions, and tax compliance by acting as MoR. | Attractive for projects wanting to offload tax and compliance overhead. | `R&D-PAYMENT-EVAL` |
| **Subscription Mgmt** | Chargebee | Proprietary | **Evaluating** | Subscription billing and revenue management platform. | For complex subscription models and recurring revenue management. | `R&D-PAYMENT-EVAL` |
| **Web3 / P2P** | BTCPay Server | MIT | **Evaluating** | Self-hosted, censorship-resistant Bitcoin and crypto payment processor. | Direct P2P payments, no intermediary. See `btcpayserver-btcpayserver-doc.txt`. | `R&D-PAYMENT-EVAL` |
| **Web3 / P2P** | Bitcart | MIT | **Evaluating** | Open-source, self-hosted crypto payment gateway. Alternative to BTCPay Server. | Similar to BTCPay, provides full control over funds. See `bitcart-bitcart-docs.txt`. | `R&D-PAYMENT-EVAL` |
| **Web3 / Gateway** | Coinbase Commerce | Proprietary | **Evaluating** | Managed service for accepting cryptocurrency payments from a major exchange. | Easier setup than self-hosting, but custodial. Use Coinbase Wallet SDK & X402.       (`coinbase-coinbase-wallet-sdk.txt`). | `R&D-PAYMENT-EVAL` |
| **Web3 / Wallet Connector** | WalletConnect | Apache-2.0 | **Evaluating** | Protocol to connect mobile crypto wallets to DApps. | Key component for enabling payments from a wide range of wallets. | `R&D-WEB3-EVAL` |

---

## 3. Maintenance

This matrix must be updated when new payment technologies are considered or when their status changes. Selections must be linked to the `project_matrix.md` to ensure clear traceability for client and internal projects.
