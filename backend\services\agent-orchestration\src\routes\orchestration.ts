import { FastifyInstance, FastifyRequest, FastifyReply } from 'fastify';
import { z } from 'zod';
import { authenticateToken, requirePermission } from '@/middleware/auth';
import { OrchestrationService, OrchestrationTask } from '@/services/orchestrationService';

// Request/Response Schemas
const createTaskSchema = z.object({
  type: z.enum(['workflow', 'agent_task', 'batch_process']).default('agent_task'),
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
  agentId: z.string().optional(),
  workflowId: z.string().optional(),
  payload: z.any().default({}),
  maxRetries: z.number().min(0).max(10).default(3)
});

const taskQuerySchema = z.object({
  status: z.enum(['queued', 'running', 'completed', 'failed', 'cancelled']).optional(),
  type: z.enum(['workflow', 'agent_task', 'batch_process']).optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']).optional(),
  agentId: z.string().optional(),
  workflowId: z.string().optional(),
  createdBy: z.string().optional(),
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0)
});

interface OrchestrationRouteContext {
  orchestrationService: OrchestrationService;
}

export async function orchestrationRoutes(fastify: FastifyInstance) {
  const { orchestrationService } = fastify as any as OrchestrationRouteContext;

  // Get all tasks
  fastify.get('/tasks', {
    preHandler: [authenticateToken, requirePermission('orchestration:read')],
    schema: {
      querystring: taskQuerySchema,
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  type: { type: 'string' },
                  name: { type: 'string' },
                  description: { type: 'string' },
                  priority: { type: 'string' },
                  status: { type: 'string' },
                  agentId: { type: 'string' },
                  workflowId: { type: 'string' },
                  payload: { type: 'object' },
                  result: { type: 'object' },
                  error: { type: 'string' },
                  createdBy: { type: 'string' },
                  organizationId: { type: 'string' },
                  createdAt: { type: 'string' },
                  startedAt: { type: 'string' },
                  completedAt: { type: 'string' },
                  retryCount: { type: 'number' },
                  maxRetries: { type: 'number' }
                }
              }
            },
            pagination: {
              type: 'object',
              properties: {
                total: { type: 'number' },
                limit: { type: 'number' },
                offset: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const query = taskQuerySchema.parse(request.query);
      const user = (request as any).user;
      
      const tasks = await orchestrationService.getTasks(user.organizationId);
      
      // Apply filters
      let filteredTasks = tasks;
      
      if (query.status) {
        filteredTasks = filteredTasks.filter(t => t.status === query.status);
      }
      
      if (query.type) {
        filteredTasks = filteredTasks.filter(t => t.type === query.type);
      }
      
      if (query.priority) {
        filteredTasks = filteredTasks.filter(t => t.priority === query.priority);
      }
      
      if (query.agentId) {
        filteredTasks = filteredTasks.filter(t => t.agentId === query.agentId);
      }
      
      if (query.workflowId) {
        filteredTasks = filteredTasks.filter(t => t.workflowId === query.workflowId);
      }
      
      if (query.createdBy) {
        filteredTasks = filteredTasks.filter(t => t.createdBy === query.createdBy);
      }
      
      // Apply pagination
      const total = filteredTasks.length;
      const paginatedTasks = filteredTasks.slice(query.offset, query.offset + query.limit);
      
      return reply.send({
        success: true,
        data: paginatedTasks,
        pagination: {
          total,
          limit: query.limit,
          offset: query.offset
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve tasks'
      });
    }
  });

  // Get task by ID
  fastify.get('/tasks/:id', {
    preHandler: [authenticateToken, requirePermission('orchestration:read')],
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                type: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                priority: { type: 'string' },
                status: { type: 'string' },
                agentId: { type: 'string' },
                workflowId: { type: 'string' },
                payload: { type: 'object' },
                result: { type: 'object' },
                error: { type: 'string' },
                createdBy: { type: 'string' },
                organizationId: { type: 'string' },
                createdAt: { type: 'string' },
                startedAt: { type: 'string' },
                completedAt: { type: 'string' },
                retryCount: { type: 'number' },
                maxRetries: { type: 'number' }
              }
            }
          }
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const user = (request as any).user;
      
      const task = await orchestrationService.getTask(id, user.organizationId);
      
      if (!task) {
        return reply.status(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      return reply.send({
        success: true,
        data: task
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve task'
      });
    }
  });

  // Create new task
  fastify.post('/tasks', {
    preHandler: [authenticateToken, requirePermission('orchestration:create')],
    schema: {
      body: createTaskSchema,
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                type: { type: 'string' },
                name: { type: 'string' },
                description: { type: 'string' },
                priority: { type: 'string' },
                status: { type: 'string' },
                agentId: { type: 'string' },
                workflowId: { type: 'string' },
                payload: { type: 'object' },
                createdBy: { type: 'string' },
                organizationId: { type: 'string' },
                createdAt: { type: 'string' },
                retryCount: { type: 'number' },
                maxRetries: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const taskData = createTaskSchema.parse(request.body);
      const user = (request as any).user;
      
      const task = await orchestrationService.createTask({
        ...taskData,
        createdBy: user.id,
        organizationId: user.organizationId
      });
      
      return reply.status(201).send({
        success: true,
        data: task
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to create task'
      });
    }
  });

  // Cancel task
  fastify.post('/tasks/:id/cancel', {
    preHandler: [authenticateToken, requirePermission('orchestration:manage')],
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const user = (request as any).user;
      
      // Check if task exists and user has access
      const task = await orchestrationService.getTask(id, user.organizationId);
      if (!task) {
        return reply.status(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      await orchestrationService.cancelTask(id);
      
      return reply.send({
        success: true,
        message: 'Task cancelled successfully'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel task'
      });
    }
  });

  // Retry task
  fastify.post('/tasks/:id/retry', {
    preHandler: [authenticateToken, requirePermission('orchestration:manage')],
    schema: {
      params: {
        type: 'object',
        properties: {
          id: { type: 'string' }
        },
        required: ['id']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        404: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            error: { type: 'string' }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const { id } = request.params as { id: string };
      const user = (request as any).user;
      
      // Check if task exists and user has access
      const task = await orchestrationService.getTask(id, user.organizationId);
      if (!task) {
        return reply.status(404).send({
          success: false,
          error: 'Task not found'
        });
      }
      
      await orchestrationService.retryTask(id);
      
      return reply.send({
        success: true,
        message: 'Task queued for retry'
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to retry task'
      });
    }
  });

  // Get orchestration metrics
  fastify.get('/metrics', {
    preHandler: [authenticateToken, requirePermission('orchestration:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                totalTasks: { type: 'number' },
                queuedTasks: { type: 'number' },
                runningTasks: { type: 'number' },
                completedTasks: { type: 'number' },
                failedTasks: { type: 'number' },
                averageExecutionTime: { type: 'number' },
                activeAgents: { type: 'number' },
                runningWorkflows: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const metrics = await orchestrationService.getMetrics();
      
      return reply.send({
        success: true,
        data: metrics
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve orchestration metrics'
      });
    }
  });

  // Get orchestration status
  fastify.get('/status', {
    preHandler: [authenticateToken, requirePermission('orchestration:read')],
    schema: {
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                status: { type: 'string' },
                timestamp: { type: 'string' }
              }
            }
          }
        }
      }
    }
  }, async (request: FastifyRequest, reply: FastifyReply) => {
    try {
      const status = await orchestrationService.getStatus();
      
      return reply.send({
        success: true,
        data: {
          status,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      fastify.log.error(error);
      return reply.status(500).send({
        success: false,
        error: 'Failed to retrieve orchestration status'
      });
    }
  });
}