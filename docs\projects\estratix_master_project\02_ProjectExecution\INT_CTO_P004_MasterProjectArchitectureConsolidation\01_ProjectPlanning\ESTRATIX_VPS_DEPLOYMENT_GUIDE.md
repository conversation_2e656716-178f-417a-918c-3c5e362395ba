# 🚀 ESTRATIX VPS Infrastructure Deployment Guide

**Version:** 1.0  
**Date:** 2025-07-21  
**Status:** Production Ready  
**Author:** ESTRATIX Infrastructure Team

## 📋 Executive Summary

This guide provides comprehensive instructions for deploying ESTRATIX's multi-VPS, multi-cluster infrastructure with automated CI/CD workflows, secure tunneling, and agentic framework support.

### **Your VPS Details**
- **Hostname:** v2202506272889356593.happysrv.de
- **IPv4:** **************/22
- **IPv6:** 2a0a:4cc0:2000:bfc9::/64

## 🏗️ Architecture Overview

### **Recommended Stack**
- **Primary Control Panel:** Dokploy (multi-server, Docker Swarm, monitoring)
- **Secondary Option:** Coolify (backup orchestrator)
- **Reverse Proxy:** <PERSON><PERSON><PERSON><PERSON> + <PERSON><PERSON><PERSON> (SSL/TLS termination)
- **Monitoring:** Grafana + Prometheus + Loki
- **Security:** Tailscale/WireGuard + SSH hardening
- **Storage:** MinIO object storage + automated backups
- **Orchestration:** Docker Swarm + K3s Kubernetes
- **CI/CD:** Gitea + Drone CI + Docker Registry

### **Security Features**
- Multi-layer authentication (SSH keys, VPN, mesh networking)
- Network segmentation and firewalls
- Automated security monitoring and alerting
- Encrypted communication channels
- Regular security updates and patches

## 🚀 Phase-by-Phase Deployment

### **Phase 1: Initial VPS Setup & Hardening**

```bash
# 1. Connect to your VPS
ssh root@**************

# 2. Download and run bootstrap script
curl -fsSL https://raw.githubusercontent.com/estratix/infrastructure/main/scripts/vps_bootstrap.sh | bash

# 3. Verify setup
cat /home/<USER>/vps_setup_complete.txt
```

**What this does:**
- ✅ System updates and essential packages
- ✅ Creates administrative user `estratix`
- ✅ SSH hardening (port 2222, key-only auth)
- ✅ UFW firewall configuration
- ✅ Fail2Ban intrusion prevention
- ✅ Docker installation and optimization
- ✅ Automatic security updates
- ✅ System performance tuning

### **Phase 2: Dokploy Multi-Server Deployment**

```bash
# 1. Switch to estratix user
ssh -p 2222 estratix@**************

# 2. Run Dokploy setup
sudo bash src/infrastructure/deployment/scripts/dokploy_setup.sh

# 3. Access Dokploy dashboard
# http://**************:3000
```

**Features Installed:**
- ✅ Dokploy control panel with multi-server support
- ✅ Traefik reverse proxy with SSL
- ✅ Docker Swarm clustering
- ✅ Monitoring stack (Prometheus, Grafana, Node Exporter)
- ✅ Automated backup system
- ✅ Security monitoring

### **Phase 3: Multi-Node Kubernetes Cluster**

```bash
# 1. Setup master node
sudo bash src/infrastructure/deployment/scripts/multi_node_setup.sh

# 2. For additional worker nodes (on other VPS):
sudo bash src/infrastructure/deployment/scripts/multi_node_setup.sh <master-ip> worker <token>

# 3. For edge nodes:
sudo bash src/infrastructure/deployment/scripts/multi_node_setup.sh <master-ip> edge <token>
```

**Kubernetes Components:**
- ✅ K3s lightweight Kubernetes
- ✅ MetalLB load balancer
- ✅ NGINX Ingress Controller
- ✅ cert-manager for SSL certificates
- ✅ KEDA for event-driven autoscaling
- ✅ MinIO object storage
- ✅ Network policies and security

### **Phase 4: CI/CD Pipeline Setup**

```bash
# 1. Setup CI/CD infrastructure
sudo bash src/infrastructure/deployment/scripts/setup_cicd_pipeline.sh

# 2. Configure services
# - Docker Registry: http://**************:5000
# - Gitea: http://**************:3001
# - Drone CI: http://**************:3002
```

**CI/CD Features:**
- ✅ Private Docker registry
- ✅ Gitea Git server with Actions
- ✅ Drone CI with Docker runners
- ✅ Automated pipeline templates
- ✅ Security scanning integration
- ✅ Multi-environment deployments

### **Phase 5: Secure Tunneling & Remote Access**

```bash
# 1. Setup secure tunneling
sudo bash src/infrastructure/deployment/scripts/setup_secure_tunneling.sh

# 2. Configure connection methods
# - WireGuard VPN: /opt/estratix/wireguard-client.conf
# - Tailscale: /opt/estratix/tailscale/setup.sh
# - SSH Tunnels: /opt/estratix/ssh-tunnels/tunnel-manager.sh
```

**Access Methods:**
- ✅ Direct SSH (port 2222)
- ✅ WireGuard VPN
- ✅ Tailscale mesh networking
- ✅ SSH tunnel management
- ✅ Ngrok temporary tunneling
- ✅ Caddy reverse proxy with SSL

## 🔧 Configuration & Management

### **Domain Configuration**

Update your DNS records to point to your VPS:

```dns
A    api.estratix.agency         **************
A    dashboard.estratix.agency   **************
A    git.estratix.agency         **************
A    drone.estratix.agency       **************
A    grafana.estratix.agency     **************
A    prometheus.estratix.agency  **************
A    registry.estratix.agency    **************
A    *.estratix.agency           **************
```

### **SSL Certificate Setup**

```bash
# Automatic SSL via cert-manager and Let's Encrypt
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: estratix-wildcard-cert
  namespace: estratix-system
spec:
  secretName: estratix-wildcard-tls
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - "*.estratix.agency"
  - "estratix.agency"
EOF
```

### **Access Dashboard**

Visit the management dashboard at:
- **URL:** http://**************:8080
- **Features:** Service status, resource monitoring, quick actions

## 🔒 Security Configuration

### **SSH Key Setup**

```bash
# 1. Generate SSH key pair (on your local machine)
ssh-keygen -t ed25519 -C "estratix-admin"

# 2. Copy public key to server
ssh-copy-id -p 2222 estratix@**************

# 3. Test key-based authentication
ssh -p 2222 estratix@**************
```

### **WireGuard VPN Setup**

```bash
# 1. Download client configuration
scp -P 2222 estratix@**************:/opt/estratix/wireguard-client.conf .

# 2. Install WireGuard client and import configuration
# 3. Connect to VPN for secure access
```

### **Tailscale Mesh Network**

```bash
# 1. Get Tailscale auth key from https://login.tailscale.com/admin/settings/keys
# 2. Run setup script on server
sudo /opt/estratix/tailscale/setup.sh

# 3. Install Tailscale on your devices and join the network
```

## 📊 Monitoring & Observability

### **Grafana Dashboards**
- **URL:** http://grafana.estratix.agency
- **Default Login:** admin / admin123 (change immediately)
- **Dashboards:** Node Exporter, Docker, Kubernetes, Application metrics

### **Prometheus Metrics**
- **URL:** http://prometheus.estratix.agency
- **Targets:** All infrastructure components
- **Alerts:** Configured for critical issues

### **Log Aggregation**
- **Loki:** Centralized log collection
- **Access:** Via Grafana Explore
- **Retention:** 30 days default

## 🔄 Backup & Recovery

### **Automated Backups**
- **Schedule:** Daily at 2 AM (system), 3 AM (CI/CD)
- **Retention:** 30 days
- **Location:** `/opt/estratix/backups/`
- **Includes:** Configurations, data, databases

### **Manual Backup**
```bash
# Run immediate backup
sudo /opt/estratix/backups/scripts/backup.sh
sudo /opt/estratix/backups/scripts/backup-cicd.sh
```

### **Recovery Procedures**
```bash
# Restore from backup
sudo /opt/estratix/backups/scripts/restore.sh <backup-date>
```

## 🚀 Deploying ESTRATIX Applications

### **Agent Deployment Example**

```yaml
# k8s/estratix-agent.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: estratix-agent
  namespace: estratix-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: estratix-agent
  template:
    metadata:
      labels:
        app: estratix-agent
    spec:
      containers:
      - name: agent
        image: localhost:5000/estratix/agent:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
```

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/estratix-agent.yaml
```

### **CI/CD Pipeline Example**

```yaml
# .drone.yml
kind: pipeline
type: docker
name: estratix-deployment

steps:
- name: test
  image: python:3.11-slim
  commands:
  - uv run pytest tests/ -v
  - uv run ruff check src/

- name: build
  image: plugins/docker
  settings:
    registry: localhost:5000
    repo: localhost:5000/estratix/agent
    tags: latest

- name: deploy
  image: bitnami/kubectl
  commands:
  - kubectl set image deployment/estratix-agent agent=localhost:5000/estratix/agent:latest -n estratix-system
```

## 🔧 Maintenance & Operations

### **Regular Maintenance Tasks**

```bash
# Weekly maintenance script
#!/bin/bash

# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean Docker resources
docker system prune -f

# Rotate logs
sudo logrotate -f /etc/logrotate.conf

# Check service health
docker ps --filter "status=exited"
kubectl get pods --all-namespaces | grep -v Running

# Backup verification
ls -la /opt/estratix/backups/data/ | tail -5
```

### **Scaling Operations**

```bash
# Scale Kubernetes deployments
kubectl scale deployment estratix-agent --replicas=5 -n estratix-system

# Add Docker Swarm nodes
docker swarm join-token worker

# Scale KEDA-managed workloads (automatic based on metrics)
```

### **Troubleshooting**

```bash
# Check service logs
docker logs estratix-dokploy
kubectl logs -f deployment/estratix-agent -n estratix-system

# Monitor resource usage
htop
kubectl top nodes
kubectl top pods --all-namespaces

# Network diagnostics
ss -tulpn
iptables -L
ufw status verbose
```

## 📈 Performance Optimization

### **Resource Allocation**
- **CPU:** Reserve 20% for system, 80% for applications
- **Memory:** Configure swap for burst capacity
- **Storage:** Use fast SSDs for databases, bulk storage for logs
- **Network:** Optimize TCP settings for high throughput

### **Auto-scaling Configuration**
```yaml
# KEDA ScaledObject example
apiVersion: keda.sh/v1alpha1
kind: ScaledObject
metadata:
  name: estratix-agent-scaler
spec:
  scaleTargetRef:
    name: estratix-agent
  minReplicaCount: 2
  maxReplicaCount: 10
  triggers:
  - type: prometheus
    metadata:
      serverAddress: http://prometheus:9090
      metricName: http_requests_per_second
      threshold: '100'
      query: sum(rate(http_requests_total[1m]))
```

## 🚨 Alerting & Incident Response

### **Alert Configuration**
- **Critical:** Service down, high error rates, resource exhaustion
- **Warning:** High resource usage, slow response times
- **Info:** Deployment events, scaling activities

### **Notification Channels**
- **Email:** <EMAIL>
- **Slack:** #infrastructure-alerts
- **PagerDuty:** For critical incidents

### **Incident Response Playbook**
1. **Acknowledge:** Confirm alert receipt
2. **Assess:** Determine impact and severity
3. **Mitigate:** Apply immediate fixes
4. **Communicate:** Update stakeholders
5. **Resolve:** Implement permanent solution
6. **Review:** Post-incident analysis

## 📚 Additional Resources

### **Documentation**
- [Dokploy Documentation](https://dokploy.com/docs)
- [K3s Documentation](https://docs.k3s.io/)
- [KEDA Documentation](https://keda.sh/docs/)
- [Tailscale Documentation](https://tailscale.com/kb/)

### **Support Contacts**
- **Infrastructure Team:** <EMAIL>
- **Emergency:** +1-XXX-XXX-XXXX
- **Documentation:** https://docs.estratix.agency

### **Quick Reference Commands**

```bash
# Service management
sudo systemctl status dokploy
docker ps --format "table {{.Names}}\t{{.Status}}"
kubectl get pods --all-namespaces

# Log access
sudo journalctl -u dokploy -f
docker logs -f estratix-gitea
kubectl logs -f deployment/estratix-agent -n estratix-system

# Backup operations
sudo /opt/estratix/backups/scripts/backup.sh
ls -la /opt/estratix/backups/data/

# Security checks
sudo fail2ban-client status
sudo ufw status verbose
tailscale status
```

## ✅ Deployment Checklist

### **Pre-Deployment**
- [ ] VPS provisioned and accessible
- [ ] DNS records configured
- [ ] SSH keys generated
- [ ] Domain ownership verified

### **Phase 1: Bootstrap**
- [ ] VPS hardening completed
- [ ] Administrative user created
- [ ] SSH configuration secured
- [ ] Firewall rules applied
- [ ] Docker installed and configured

### **Phase 2: Control Panel**
- [ ] Dokploy installed and running
- [ ] Traefik proxy configured
- [ ] Monitoring stack deployed
- [ ] Backup system configured
- [ ] Security monitoring active

### **Phase 3: Kubernetes**
- [ ] K3s cluster initialized
- [ ] Essential components installed
- [ ] Storage classes configured
- [ ] Network policies applied
- [ ] KEDA autoscaling enabled

### **Phase 4: CI/CD**
- [ ] Docker registry running
- [ ] Gitea Git server configured
- [ ] Drone CI operational
- [ ] Pipeline templates created
- [ ] Security scanning enabled

### **Phase 5: Security**
- [ ] WireGuard VPN configured
- [ ] Tailscale mesh network setup
- [ ] SSH tunnel management ready
- [ ] Reverse proxy with SSL
- [ ] Connection dashboard accessible

### **Post-Deployment**
- [ ] All services health-checked
- [ ] Monitoring dashboards configured
- [ ] Backup systems tested
- [ ] Security scans completed
- [ ] Documentation updated
- [ ] Team training completed

## 🎯 Success Metrics

### **Performance Targets**
- **Uptime:** 99.9%
- **Response Time:** < 200ms (95th percentile)
- **Deployment Time:** < 5 minutes
- **Recovery Time:** < 15 minutes
- **Security Incidents:** 0

### **Monitoring KPIs**
- Service availability
- Resource utilization
- Error rates
- Deployment frequency
- Mean time to recovery

---

**🚀 Congratulations!** You now have a production-ready, scalable, secure multi-VPS infrastructure for ESTRATIX agency operations with automated CI/CD workflows and comprehensive monitoring.

For questions or support, contact the ESTRATIX Infrastructure <NAME_EMAIL>.
