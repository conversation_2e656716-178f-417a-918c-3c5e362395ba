import { config } from 'dotenv';
import { z } from 'zod';

// Load environment variables
config();

// Environment validation schema
const envSchema = z.object({
  // Server Configuration
  NODE_ENV: z.enum(['development', 'production', 'test']).default('development'),
  PORT: z.string().transform(Number).default('3004'),
  HOST: z.string().default('0.0.0.0'),
  
  // Security
  JWT_SECRET: z.string().min(32),
  JWT_EXPIRES_IN: z.string().default('24h'),
  CORS_ORIGIN: z.string().default('*'),
  
  // Database
  DATABASE_URL: z.string().url(),
  REDIS_URL: z.string().url().default('redis://localhost:6379'),
  
  // AI Services
  OPENAI_API_KEY: z.string().min(1),
  OPENAI_MODEL: z.string().default('gpt-4'),
  OPENAI_TEMPERATURE: z.string().transform(Number).default('0.7'),
  LANGCHAIN_API_KEY: z.string().optional(),
  LANGCHAIN_TRACING_V2: z.string().transform(Boolean).default('false'),
  
  // External Services
  PROJECT_MANAGEMENT_SERVICE_URL: z.string().url().default('http://localhost:3002'),
  SMART_CONTRACTS_SERVICE_URL: z.string().url().default('http://localhost:3003'),
  CONTENT_STUDIO_SERVICE_URL: z.string().url().default('http://localhost:3001'),
  CLIENT_ONBOARDING_SERVICE_URL: z.string().url().default('http://localhost:3005'),
  
  // Agent Orchestration Specific
  MAX_CONCURRENT_AGENTS: z.string().transform(Number).default('10'),
  AGENT_TIMEOUT_MS: z.string().transform(Number).default('300000'), // 5 minutes
  WORKFLOW_RETRY_ATTEMPTS: z.string().transform(Number).default('3'),
  AGENT_MEMORY_TTL_HOURS: z.string().transform(Number).default('24'),
  
  // Queue Configuration
  QUEUE_REDIS_URL: z.string().url().optional(),
  QUEUE_CONCURRENCY: z.string().transform(Number).default('5'),
  QUEUE_RETRY_ATTEMPTS: z.string().transform(Number).default('3'),
  QUEUE_RETRY_DELAY_MS: z.string().transform(Number).default('5000'),
  
  // WebSocket Configuration
  WS_ENABLED: z.string().transform(Boolean).default('true'),
  WS_PORT: z.string().transform(Number).default('3014'),
  WS_HEARTBEAT_INTERVAL: z.string().transform(Number).default('30000'),
  
  // Monitoring
  LOG_LEVEL: z.enum(['fatal', 'error', 'warn', 'info', 'debug', 'trace']).default('info'),
  METRICS_ENABLED: z.string().transform(Boolean).default('true'),
  HEALTH_CHECK_INTERVAL: z.string().transform(Number).default('30000'),
  
  // Feature Flags
  ENABLE_AGENT_ANALYTICS: z.string().transform(Boolean).default('true'),
  ENABLE_WORKFLOW_CACHING: z.string().transform(Boolean).default('true'),
  ENABLE_DISTRIBUTED_EXECUTION: z.string().transform(Boolean).default('false'),
  ENABLE_AGENT_LEARNING: z.string().transform(Boolean).default('true')
});

// Validate and export environment
const env = envSchema.parse(process.env);

export const environment = {
  // Server
  nodeEnv: env.NODE_ENV,
  port: env.PORT,
  host: env.HOST,
  
  // Security
  jwt: {
    secret: env.JWT_SECRET,
    expiresIn: env.JWT_EXPIRES_IN
  },
  cors: {
    origin: env.CORS_ORIGIN
  },
  
  // Database
  database: {
    url: env.DATABASE_URL
  },
  redis: {
    url: env.REDIS_URL
  },
  
  // AI Services
  ai: {
    openai: {
      apiKey: env.OPENAI_API_KEY,
      model: env.OPENAI_MODEL,
      temperature: env.OPENAI_TEMPERATURE
    },
    langchain: {
      apiKey: env.LANGCHAIN_API_KEY,
      tracingEnabled: env.LANGCHAIN_TRACING_V2
    }
  },
  
  // External Services
  services: {
    projectManagement: env.PROJECT_MANAGEMENT_SERVICE_URL,
    smartContracts: env.SMART_CONTRACTS_SERVICE_URL,
    contentStudio: env.CONTENT_STUDIO_SERVICE_URL,
    clientOnboarding: env.CLIENT_ONBOARDING_SERVICE_URL
  },
  
  // Agent Orchestration
  orchestration: {
    maxConcurrentAgents: env.MAX_CONCURRENT_AGENTS,
    agentTimeoutMs: env.AGENT_TIMEOUT_MS,
    workflowRetryAttempts: env.WORKFLOW_RETRY_ATTEMPTS,
    agentMemoryTtlHours: env.AGENT_MEMORY_TTL_HOURS
  },
  
  // Queue
  queue: {
    redisUrl: env.QUEUE_REDIS_URL || env.REDIS_URL,
    concurrency: env.QUEUE_CONCURRENCY,
    retryAttempts: env.QUEUE_RETRY_ATTEMPTS,
    retryDelayMs: env.QUEUE_RETRY_DELAY_MS
  },
  
  // WebSocket
  websocket: {
    enabled: env.WS_ENABLED,
    port: env.WS_PORT,
    heartbeatInterval: env.WS_HEARTBEAT_INTERVAL
  },
  
  // Monitoring
  monitoring: {
    logLevel: env.LOG_LEVEL,
    metricsEnabled: env.METRICS_ENABLED,
    healthCheckInterval: env.HEALTH_CHECK_INTERVAL
  },
  
  // Feature Flags
  features: {
    agentAnalytics: env.ENABLE_AGENT_ANALYTICS,
    workflowCaching: env.ENABLE_WORKFLOW_CACHING,
    distributedExecution: env.ENABLE_DISTRIBUTED_EXECUTION,
    agentLearning: env.ENABLE_AGENT_LEARNING
  }
};

export type Environment = typeof environment;
export default environment;