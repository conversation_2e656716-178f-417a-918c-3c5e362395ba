import { OpenAI } from 'openai';
import Anthropic from '@anthropic-ai/sdk';
import { ChatOpenAI } from 'langchain/chat_models/openai';
import { PromptTemplate } from 'langchain/prompts';
import { LLMChain } from 'langchain/chains';
import { VectorStoreService } from './vectorStoreService';
import { ContentAnalyzer } from '../utils/contentAnalyzer';
import { SEOOptimizer } from '../utils/seoOptimizer';
import { BrandComplianceChecker } from '../utils/brandComplianceChecker';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';

export interface ContentGenerationParams {
  type: string;
  topic: string;
  tone: string;
  length: string;
  targetAudience?: string;
  keywords?: string[];
  brandGuidelines?: {
    brandName: string;
    brandVoice: string;
    brandValues: string[];
    doNotUse?: string[];
  };
  customInstructions?: string;
  includeImages?: boolean;
  includeCallToAction?: boolean;
  seoOptimized?: boolean;
  userId?: string;
}

export interface CampaignGenerationParams {
  campaignType: string;
  duration: number;
  frequency: string;
  platforms: string[];
  objectives: string[];
  targetAudience: {
    demographics: string;
    interests: string[];
    painPoints: string[];
  };
  brandInfo: {
    name: string;
    industry: string;
    uniqueSellingProposition: string;
    competitorAnalysis?: string;
  };
  budget?: number;
  kpis: string[];
  userId?: string;
  jobId?: string;
}

export interface ContentOptimizationParams {
  content: string;
  optimizationType: string;
  targetKeywords?: string[];
  platform?: string;
  currentMetrics?: {
    views?: number;
    engagement?: number;
    conversions?: number;
  };
  userId?: string;
}

export interface ContentAnalysisParams {
  content: string;
  analysisType: string;
  compareWith?: string;
  userId?: string;
}

export class AIContentService {
  private openai: OpenAI;
  private anthropic: Anthropic;
  private vectorStore: VectorStoreService;
  private contentAnalyzer: ContentAnalyzer;
  private seoOptimizer: SEOOptimizer;
  private brandChecker: BrandComplianceChecker;

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    this.vectorStore = new VectorStoreService();
    this.contentAnalyzer = new ContentAnalyzer();
    this.seoOptimizer = new SEOOptimizer();
    this.brandChecker = new BrandComplianceChecker();
  }

  async generateContent(params: ContentGenerationParams) {
    try {
      const contentId = uuidv4();
      logger.info(`Generating content ${contentId} of type ${params.type}`);

      // Get relevant context from vector store
      const context = await this.getRelevantContext(params.topic, params.type);

      // Build the prompt based on content type
      const prompt = await this.buildContentPrompt(params, context);

      // Generate content using the most suitable AI model
      const model = this.selectOptimalModel(params.type, params.length);
      const generatedContent = await this.generateWithModel(model, prompt, params);

      // Post-process the content
      const processedContent = await this.postProcessContent(generatedContent, params);

      // Generate metadata and analytics
      const metadata = await this.generateContentMetadata(processedContent);

      // Generate images if requested
      const images = params.includeImages ? await this.generateImages(params.topic, params.type) : [];

      // Generate suggestions for improvement
      const suggestions = await this.generateSuggestions(processedContent, params);

      // Store content in vector database for future reference
      await this.vectorStore.storeContent({
        id: contentId,
        content: processedContent,
        type: params.type,
        topic: params.topic,
        metadata,
        userId: params.userId,
      });

      return {
        id: contentId,
        content: processedContent,
        metadata,
        suggestions,
        images,
      };
    } catch (error) {
      logger.error('Error in generateContent:', error);
      throw new Error(`Content generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async generateCampaign(params: CampaignGenerationParams) {
    try {
      const campaignId = uuidv4();
      logger.info(`Generating campaign ${campaignId} of type ${params.campaignType}`);

      // Calculate content pieces needed
      const contentPieces = this.calculateContentPieces(params.duration, params.frequency, params.platforms.length);

      // Generate campaign strategy
      const strategy = await this.generateCampaignStrategy(params);

      // Create content calendar
      const timeline = await this.generateContentCalendar(params, contentPieces);

      // Generate initial content pieces
      const initialContent = await this.generateInitialCampaignContent(params, timeline.slice(0, 5));

      return {
        campaignId,
        status: 'generating',
        estimatedCompletion: this.calculateEstimatedCompletion(contentPieces),
        contentPieces,
        timeline,
        strategy,
        initialContent,
      };
    } catch (error) {
      logger.error('Error in generateCampaign:', error);
      throw new Error(`Campaign generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async optimizeContent(params: ContentOptimizationParams) {
    try {
      logger.info(`Optimizing content for ${params.optimizationType}`);

      let optimizedContent = params.content;
      const improvements: string[] = [];

      switch (params.optimizationType) {
        case 'seo':
          const seoResult = await this.seoOptimizer.optimize(params.content, params.targetKeywords || []);
          optimizedContent = seoResult.optimizedContent;
          improvements.push(...seoResult.improvements);
          break;

        case 'engagement':
          optimizedContent = await this.optimizeForEngagement(params.content, params.platform);
          improvements.push('Enhanced emotional hooks', 'Improved call-to-action placement', 'Added interactive elements');
          break;

        case 'conversion':
          optimizedContent = await this.optimizeForConversion(params.content, params.currentMetrics);
          improvements.push('Strengthened value proposition', 'Optimized conversion funnel', 'Enhanced urgency triggers');
          break;

        case 'readability':
          optimizedContent = await this.optimizeForReadability(params.content);
          improvements.push('Simplified sentence structure', 'Improved paragraph flow', 'Enhanced clarity');
          break;

        case 'accessibility':
          optimizedContent = await this.optimizeForAccessibility(params.content);
          improvements.push('Added alt text suggestions', 'Improved heading structure', 'Enhanced screen reader compatibility');
          break;
      }

      // Calculate metrics for both versions
      const originalMetrics = await this.contentAnalyzer.analyzeMetrics(params.content);
      const optimizedMetrics = await this.contentAnalyzer.analyzeMetrics(optimizedContent);

      return {
        originalContent: params.content,
        optimizedContent,
        improvements,
        metrics: {
          readabilityScore: optimizedMetrics.readability,
          seoScore: optimizedMetrics.seo,
          engagementPrediction: optimizedMetrics.engagement,
        },
        comparison: {
          readabilityImprovement: optimizedMetrics.readability - originalMetrics.readability,
          seoImprovement: optimizedMetrics.seo - originalMetrics.seo,
          engagementImprovement: optimizedMetrics.engagement - originalMetrics.engagement,
        },
      };
    } catch (error) {
      logger.error('Error in optimizeContent:', error);
      throw new Error(`Content optimization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async analyzeContent(params: ContentAnalysisParams) {
    try {
      logger.info(`Analyzing content for ${params.analysisType}`);

      const analysis: any = {};
      const recommendations: string[] = [];

      switch (params.analysisType) {
        case 'sentiment':
          analysis.sentiment = await this.contentAnalyzer.analyzeSentiment(params.content);
          break;

        case 'readability':
          analysis.readability = await this.contentAnalyzer.analyzeReadability(params.content);
          break;

        case 'seo':
          analysis.seo = await this.contentAnalyzer.analyzeSEO(params.content);
          break;

        case 'brand_compliance':
          analysis.brandCompliance = await this.brandChecker.checkCompliance(params.content);
          break;

        case 'plagiarism':
          analysis.plagiarism = await this.contentAnalyzer.checkPlagiarism(params.content);
          break;

        case 'performance_prediction':
          analysis.performancePrediction = await this.contentAnalyzer.predictPerformance(params.content);
          break;
      }

      // Generate recommendations based on analysis
      recommendations.push(...await this.generateAnalysisRecommendations(analysis, params.analysisType));

      // Predict performance metrics
      const performancePrediction = await this.contentAnalyzer.predictPerformance(params.content);

      return {
        analysis,
        recommendations,
        performancePrediction,
      };
    } catch (error) {
      logger.error('Error in analyzeContent:', error);
      throw new Error(`Content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async getAvailableModels() {
    return {
      models: [
        {
          id: 'gpt-4-turbo',
          name: 'GPT-4 Turbo',
          provider: 'OpenAI',
          capabilities: ['text-generation', 'analysis', 'creative-writing', 'technical-writing'],
          maxTokens: 128000,
          costPerToken: 0.00001,
          speed: 'fast',
          quality: 'excellent',
        },
        {
          id: 'claude-3-opus',
          name: 'Claude 3 Opus',
          provider: 'Anthropic',
          capabilities: ['text-generation', 'analysis', 'creative-writing', 'reasoning'],
          maxTokens: 200000,
          costPerToken: 0.000015,
          speed: 'medium',
          quality: 'excellent',
        },
        {
          id: 'gpt-3.5-turbo',
          name: 'GPT-3.5 Turbo',
          provider: 'OpenAI',
          capabilities: ['text-generation', 'analysis', 'summarization'],
          maxTokens: 16385,
          costPerToken: 0.000001,
          speed: 'very-fast',
          quality: 'good',
        },
      ],
      recommendations: {
        creative: 'claude-3-opus',
        analytical: 'gpt-4-turbo',
        technical: 'gpt-4-turbo',
        marketing: 'claude-3-opus',
      },
    };
  }

  private async getRelevantContext(topic: string, contentType: string): Promise<string> {
    try {
      const similarContent = await this.vectorStore.searchSimilar(topic, contentType, 5);
      return similarContent.map(item => item.content).join('\n\n');
    } catch (error) {
      logger.warn('Could not retrieve context from vector store:', error);
      return '';
    }
  }

  private async buildContentPrompt(params: ContentGenerationParams, context: string): Promise<string> {
    const brandGuidelines = params.brandGuidelines ? `
Brand Guidelines:
- Brand Name: ${params.brandGuidelines.brandName}
- Brand Voice: ${params.brandGuidelines.brandVoice}
- Brand Values: ${params.brandGuidelines.brandValues.join(', ')}
- Avoid: ${params.brandGuidelines.doNotUse?.join(', ') || 'None specified'}` : '';

    const keywords = params.keywords ? `\nTarget Keywords: ${params.keywords.join(', ')}` : '';
    const audience = params.targetAudience ? `\nTarget Audience: ${params.targetAudience}` : '';
    const instructions = params.customInstructions ? `\nCustom Instructions: ${params.customInstructions}` : '';
    const contextSection = context ? `\nRelevant Context:\n${context}` : '';

    return `Create a ${params.type} about "${params.topic}" with the following specifications:

Tone: ${params.tone}
Length: ${params.length}
${audience}${keywords}${brandGuidelines}${instructions}${contextSection}

Requirements:
- ${params.seoOptimized ? 'Optimize for SEO' : 'Focus on readability'}
- ${params.includeCallToAction ? 'Include a compelling call-to-action' : 'No call-to-action needed'}
- Ensure the content is engaging, valuable, and well-structured
- Use appropriate formatting (headings, bullet points, etc.)

Generate high-quality, original content that meets these specifications.`;
  }

  private selectOptimalModel(contentType: string, length: string): string {
    // Creative content benefits from Claude's creativity
    if (['blog_post', 'video_script', 'podcast_script', 'creative'].includes(contentType)) {
      return 'claude-3-opus';
    }

    // Technical and analytical content works well with GPT-4
    if (['technical', 'analysis', 'research'].includes(contentType)) {
      return 'gpt-4-turbo';
    }

    // For short content, use faster model
    if (length === 'short') {
      return 'gpt-3.5-turbo';
    }

    // Default to GPT-4 for balanced performance
    return 'gpt-4-turbo';
  }

  private async generateWithModel(model: string, prompt: string, params: ContentGenerationParams): Promise<string> {
    try {
      if (model.startsWith('claude')) {
        const response = await this.anthropic.messages.create({
          model: 'claude-3-opus-20240229',
          max_tokens: this.getMaxTokensForLength(params.length),
          messages: [{ role: 'user', content: prompt }],
        });
        return response.content[0].type === 'text' ? response.content[0].text : '';
      } else {
        const response = await this.openai.chat.completions.create({
          model,
          messages: [{ role: 'user', content: prompt }],
          max_tokens: this.getMaxTokensForLength(params.length),
          temperature: this.getTemperatureForTone(params.tone),
        });
        return response.choices[0]?.message?.content || '';
      }
    } catch (error) {
      logger.error(`Error generating with model ${model}:`, error);
      throw error;
    }
  }

  private getMaxTokensForLength(length: string): number {
    switch (length) {
      case 'short': return 500;
      case 'medium': return 1500;
      case 'long': return 3000;
      default: return 1500;
    }
  }

  private getTemperatureForTone(tone: string): number {
    switch (tone) {
      case 'creative': return 0.9;
      case 'casual': return 0.7;
      case 'professional': return 0.3;
      case 'authoritative': return 0.2;
      default: return 0.5;
    }
  }

  private async postProcessContent(content: string, params: ContentGenerationParams): Promise<string> {
    let processed = content;

    // Apply SEO optimization if requested
    if (params.seoOptimized && params.keywords) {
      processed = await this.seoOptimizer.optimize(processed, params.keywords).then(result => result.optimizedContent);
    }

    // Apply brand compliance checks
    if (params.brandGuidelines) {
      processed = await this.brandChecker.ensureCompliance(processed, params.brandGuidelines);
    }

    return processed;
  }

  private async generateContentMetadata(content: string) {
    const analysis = await this.contentAnalyzer.analyzeMetrics(content);
    
    return {
      wordCount: content.split(' ').length,
      readingTime: Math.ceil(content.split(' ').length / 200), // Average reading speed
      seoScore: analysis.seo,
      sentimentScore: analysis.sentiment,
      keywords: await this.contentAnalyzer.extractKeywords(content),
    };
  }

  private async generateImages(topic: string, contentType: string): Promise<any[]> {
    try {
      const imagePrompts = await this.generateImagePrompts(topic, contentType);
      const images = [];

      for (const prompt of imagePrompts.slice(0, 3)) { // Limit to 3 images
        const response = await this.openai.images.generate({
          model: 'dall-e-3',
          prompt,
          size: '1024x1024',
          quality: 'standard',
          n: 1,
        });

        if (response.data[0]?.url) {
          images.push({
            url: response.data[0].url,
            alt: `Generated image for ${topic}`,
            caption: prompt,
          });
        }
      }

      return images;
    } catch (error) {
      logger.error('Error generating images:', error);
      return [];
    }
  }

  private async generateImagePrompts(topic: string, contentType: string): Promise<string[]> {
    const prompt = `Generate 3 detailed image prompts for a ${contentType} about "${topic}". Each prompt should be descriptive, professional, and suitable for business use.`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 300,
    });

    const content = response.choices[0]?.message?.content || '';
    return content.split('\n').filter(line => line.trim().length > 0).slice(0, 3);
  }

  private async generateSuggestions(content: string, params: ContentGenerationParams): Promise<string[]> {
    const prompt = `Analyze this ${params.type} content and provide 3-5 specific suggestions for improvement:\n\n${content}`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 200,
    });

    const suggestions = response.choices[0]?.message?.content || '';
    return suggestions.split('\n').filter(line => line.trim().length > 0);
  }

  private calculateContentPieces(duration: number, frequency: string, platformCount: number): number {
    const frequencyMultiplier = {
      'daily': duration,
      'weekly': Math.ceil(duration / 7),
      'bi-weekly': Math.ceil(duration / 14),
      'monthly': Math.ceil(duration / 30),
    }[frequency] || 1;

    return frequencyMultiplier * platformCount;
  }

  private async generateCampaignStrategy(params: CampaignGenerationParams): Promise<any> {
    const prompt = `Create a comprehensive ${params.campaignType} campaign strategy for ${params.brandInfo.name} in the ${params.brandInfo.industry} industry. 

Campaign Details:
- Duration: ${params.duration} days
- Frequency: ${params.frequency}
- Platforms: ${params.platforms.join(', ')}
- Objectives: ${params.objectives.join(', ')}
- Target Audience: ${params.targetAudience.demographics}
- USP: ${params.brandInfo.uniqueSellingProposition}
- KPIs: ${params.kpis.join(', ')}

Provide a strategic overview including key themes, messaging pillars, and success metrics.`;

    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || '';
  }

  private async generateContentCalendar(params: CampaignGenerationParams, contentPieces: number): Promise<any[]> {
    // Generate a content calendar based on campaign parameters
    const calendar = [];
    const startDate = new Date();
    
    for (let i = 0; i < Math.min(contentPieces, 20); i++) { // Limit initial calendar to 20 pieces
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      calendar.push({
        date: date.toISOString().split('T')[0],
        content: `Content piece ${i + 1}`,
        platform: params.platforms[i % params.platforms.length],
        type: this.getContentTypeForPlatform(params.platforms[i % params.platforms.length]),
      });
    }

    return calendar;
  }

  private getContentTypeForPlatform(platform: string): string {
    const platformTypes = {
      'facebook': 'social_post',
      'instagram': 'visual_post',
      'twitter': 'tweet',
      'linkedin': 'professional_post',
      'tiktok': 'short_video',
      'youtube': 'video_content',
      'email': 'email_content',
      'blog': 'blog_post',
    };
    return platformTypes[platform as keyof typeof platformTypes] || 'social_post';
  }

  private async generateInitialCampaignContent(params: CampaignGenerationParams, timeline: any[]): Promise<any[]> {
    const content = [];
    
    for (const item of timeline) {
      try {
        const generatedContent = await this.generateContent({
          type: item.type,
          topic: `${params.brandInfo.name} ${params.objectives[0]}`,
          tone: 'professional',
          length: 'short',
          targetAudience: params.targetAudience.demographics,
          brandGuidelines: {
            brandName: params.brandInfo.name,
            brandVoice: 'professional',
            brandValues: ['quality', 'innovation'],
          },
          userId: params.userId,
        });
        
        content.push({
          ...item,
          generatedContent: generatedContent.content,
        });
      } catch (error) {
        logger.error(`Error generating content for ${item.type}:`, error);
      }
    }

    return content;
  }

  private calculateEstimatedCompletion(contentPieces: number): string {
    const estimatedMinutes = contentPieces * 2; // 2 minutes per piece estimate
    const completionDate = new Date();
    completionDate.setMinutes(completionDate.getMinutes() + estimatedMinutes);
    return completionDate.toISOString();
  }

  private async optimizeForEngagement(content: string, platform?: string): Promise<string> {
    const prompt = `Optimize this content for maximum engagement${platform ? ` on ${platform}` : ''}:\n\n${content}\n\nFocus on emotional hooks, storytelling, and interactive elements.`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || content;
  }

  private async optimizeForConversion(content: string, metrics?: any): Promise<string> {
    const metricsContext = metrics ? `\nCurrent metrics: Views: ${metrics.views}, Engagement: ${metrics.engagement}, Conversions: ${metrics.conversions}` : '';
    const prompt = `Optimize this content for higher conversion rates:${metricsContext}\n\n${content}\n\nFocus on value proposition, urgency, and clear calls-to-action.`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || content;
  }

  private async optimizeForReadability(content: string): Promise<string> {
    const prompt = `Improve the readability of this content by simplifying language, shortening sentences, and improving flow:\n\n${content}`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || content;
  }

  private async optimizeForAccessibility(content: string): Promise<string> {
    const prompt = `Optimize this content for accessibility by improving heading structure, adding descriptive text, and ensuring screen reader compatibility:\n\n${content}`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-4-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 1000,
    });

    return response.choices[0]?.message?.content || content;
  }

  private async generateAnalysisRecommendations(analysis: any, analysisType: string): Promise<string[]> {
    const prompt = `Based on this ${analysisType} analysis, provide 3-5 specific recommendations for improvement:\n\n${JSON.stringify(analysis, null, 2)}`;
    
    const response = await this.openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [{ role: 'user', content: prompt }],
      max_tokens: 300,
    });

    const recommendations = response.choices[0]?.message?.content || '';
    return recommendations.split('\n').filter(line => line.trim().length > 0);
  }
}