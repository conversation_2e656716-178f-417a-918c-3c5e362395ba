# ESTRATIX Process Definition: MKT002 - Agile Content Generation & Production

**Process ID:** MKT002
**Process Name:** Agile Content Generation & Production
**Version:** 1.0
**Status:** Draft
**Responsible Team / Focus Area:** Marketing Content Production Crew
**Last Reviewed:** 2025-05-14

---

## 1. Purpose

To efficiently generate and produce high-quality, engaging, and brand-aligned foundational content assets (e.g., articles, blog posts, social media updates, basic graphics, video scripts, whitepaper drafts) as outlined by `MKT001`, directly supporting the marketing and sales objectives for ESTRATIX's productized services. This process emphasizes an agile, iterative approach to content creation, leveraging specialized agents and open-source tools for speed and foundational quality.

## 2. Goal

*   Produce 100% of planned foundational content items from the `MKT001` content calendar on schedule.
*   Ensure >98% of produced content passes `MKT001` brand guideline and quality checks on the first review for foundational assets.
*   Reduce average foundational content production time (from brief to final asset) by X% within 6 months through agent automation and optimized workflows.
*   Maintain a content repository with all assets correctly tagged, versioned, and accessible.
*   Achieve a target content engagement score (defined by `MKT001` metrics) for Y% of produced foundational assets.

## 3. Scope

*   **In Scope:** Interpreting content briefs from `MKT001`; Textual Content Generation (articles, blog posts, website copy, social media posts, email copy, ad copy drafts, video scripts, podcast outlines, case study drafts, whitepaper drafts); Basic Visual Content Generation (simple graphics, social media visuals using templates); Video/Audio Pre-production (scripting, basic storyboarding); Content editing, proofreading, and revision cycles for foundational content; Ensuring SEO best practices; Formatting content for distribution channels; Managing content versions in a central repository; Basic metadata tagging; AI-assisted social media post package generation.
*   **Out of Scope:** Content strategy and planning (`MKT001`); Advanced graphic design, professional video production/editing, complex animation (`MKT003`); Content distribution and promotion (channel-specific MKT processes); Direct audience interaction.

## 4. Triggers

*   Approved content brief for foundational content received from `MKT001`.
*   Scheduled tasks for foundational content from the `MKT001` content calendar.
*   Requests for ad-hoc foundational content updates (subject to `MKT001` approval).

## 5. Inputs

*   Content Briefs (from `MKT001`).
*   Content Calendar & Deadlines (from `MKT001`).
*   Brand Guidelines Document (from `MKT001`).
*   Target Audience Persona Profiles (from `MKT001`).
*   SEO Keyword Lists & Guidelines (from `MKT001`).
*   Productized Service Information & USPs (from `MKT001`/Product Management).
*   Style Guides.
*   Templates for content types.
*   Source materials or research provided.

## 6. Outputs

*   Finalized Foundational Content Assets (Blog posts, articles, basic infographics, video scripts).
*   Metadata for each content asset (keywords, descriptions, categories).
*   Social Media Post Packages (Drafts for multiple platforms, including adapted titles, descriptions, hashtags, AI prompt guidance for visuals if not provided).
*   Confirmation of readiness for `MKT003 (Advanced Production)`, `MKT004 (Ad Creative)`, or direct publishing.
*   Editorial Calendar Updates (status changes).
*   Feedback on content briefs.

## 7. High-Level Steps

1.  **Brief Intake & Clarification (`ContentCreationCoordinatorAgent`):** Receive and validate content briefs derived from `MKT001`'s Content Calendar and Data Structure.
2.  **Textual Content Generation (`TextContentWriterAgent`):** Draft articles, blog posts, scripts using LLMs and human expertise.
3.  **Basic Visual Content Creation (`BasicVisualCreatorAgent`):** Generate simple infographics, quote cards, supporting visuals.
4.  **Review & Iteration (Internal):** `ContentEditorProofreaderAgent` reviews for quality, accuracy, brand voice.
5.  **Generate AI-Assisted Social Media Post Packages (`SocialMediaAdaptationAgent`):** Based on finalized content and the 'Content Topic/Objective Data Structure' from `MKT001`, use LLMs to generate platform-specific post drafts (text, visual prompts/briefs for images/videos if needed), including titles, descriptions, hashtags, and call-to-actions adapted for various social networks.
6.  **Stakeholder Review & Approval (Optional, per brief):** `MktExec_CoordinatorAgent` facilitates review.
7.  **Finalization & Handoff:** Prepare assets for next stage (`MKT003`, `MKT004`) or direct publishing channels.

## 8. Tools, Libraries & MCPs

*   **Text Generation & Editing:** LLMs (OpenAI API, Hugging Face models via `MCP_LLM_TextGeneration_Service`), Markdown Editors (Obsidian, VS Code), Grammarly, Hemingway Editor.
*   **Basic Visual Generation:** Canva, Simplified AI, Visme; Open-Source: GIMP, Inkscape (for template use).
*   **Prompt Engineering Tools/Frameworks.**
*   **Social Media Management Platforms (for inspiration/reference, and future publishing):** Buffer, Hootsuite, Sprout Social.
*   **Project Management & Collaboration:** Notion, Asana, Trello.
*   **MCPs (Conceptual/Internal):** `MCP_LLM_TextGeneration_Service`, `MCP_BasicImageGen_Service`, `MCP_ContentBrief_Reader`, `MCP_SocialMedia_Publishing_API` (for future direct publishing).

## 9. Roles & Responsibilities

*   **`ContentCreationCoordinatorAgent`:** Manages workflow, assigns tasks, ensures quality.
*   **`TextContentWriterAgent`:** Creates textual content, leveraging LLMs.
*   **`BasicVisualCreatorAgent`:** Creates simple visuals, potentially using AI tools.
*   **`ContentEditorProofreaderAgent`:** Reviews and refines all content.
*   **`SocialMediaAdaptationAgent`:** Generates platform-specific social media post drafts using LLMs based on core content and strategic inputs from `MKT001`.

## 10. Metrics & KPIs

*   **Content Throughput** (foundational assets).
*   **On-Time Delivery Rate.**
*   **First-Pass Acceptance Rate.**
*   **Revision Cycles per foundational content piece.**
*   **Adherence to Brief.**
*   **Asset Repository Integrity.**
*   **Social Media Engagement Metrics (e.g., likes, shares, comments) for published content.**

## 11. Dependencies

*   **Relies On:** `MKT001` (briefs, calendar, guidelines), `CIO_P002` (best practices), `DES001` (visual templates if used).
*   **Feeds Into:** Channel-specific MKT processes (`MKT00X`), `SAL00X` (sales collateral drafts), `MKT003` (scripts/storyboards for advanced production), `MKT001` (feedback).

## 12. Exception Handling

*   Creative Blocks/Agent Underperformance: Re-assign, refine prompts, escalate to human.
*   Brief Ambiguity: Clarification from `MKT001`.
*   Tool/API Failures: Manual fallback, alternative tools.
*   Scope Creep: Escalation to `MKT001`.

## 13. PDCA (Continuous Improvement for `MKT002`)

*   **Plan:** Analyze production metrics, review feedback, research new AI tools for foundational content.
*   **Do:** Experiment with new prompts, agent workflows for foundational content.
*   **Check:** Monitor KPIs, A/B test agent-generated styles.
*   **Act:** Standardize effective prompts/workflows, update agent training.

## 14. Agentic Framework Mapping

*   **Pydantic-AI Models:** `ContentPiece` (blog, article, script), `BasicVisualAsset`, `EditorialReviewFeedback`, `SocialMediaPostDraft`, `PlatformAdaptationSpec`.
*   **Windsurf Workflows:** `/wf_mkt_draft_blog_post <brief_id>`, `/wf_mkt_create_basic_infographic <topic>`, `/wf_mkt_generate_social_posts <content_asset_id>`.
*   **A2A/ACP Protocols:** Standardized JSON for briefs and content handoff.
*   **Aider Integration:** For code snippets (e.g., HTML for blog posts), script generation based on outlines, refining text with specific instructions.
