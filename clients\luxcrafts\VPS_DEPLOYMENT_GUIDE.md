# Luxcrafts VPS Deployment Guide

This document provides detailed instructions for deploying the Luxcrafts application to the configured Virtual Private Server (VPS) using the automated `agentic-cicd.yml` GitHub Actions workflow.

## 1. Overview

The CI/CD pipeline is designed to automate the entire lifecycle of the application, from code analysis and testing to containerization, deployment, and post-deployment validation. It leverages GitHub Actions for orchestration and a shell script to deploy the application on a VPS where <PERSON><PERSON><PERSON><PERSON> is expected to be running.

## 2. Prerequisites

Before initiating a deployment, ensure the following prerequisites are met.

### 2.1. GitHub Repository Secrets

The following secrets must be configured in the GitHub repository settings (`Settings > Secrets and variables > Actions`):

-   `VPS_HOST`: The IP address or domain name of the VPS.
-   `VPS_USER`: The username for SSH access to the VPS (e.g., `root` or a dedicated user).
-   `VPS_SSH_KEY`: The private SSH key for passwordless login to the VPS.
-   `DOMAIN_NAME`: The domain name for the application (e.g., `luxcrafts.com`).
-   `GITHUB_TOKEN`: A Personal Access Token (PAT) with `read:packages` and `write:packages` scopes to interact with GitHub Container Registry. The default `secrets.GITHUB_TOKEN` usually suffices.
-   `PROD_APP_URL` / `STAGING_APP_URL`: The full URLs for the production and staging environments for health checks and validation.
-   `SLACK_WEBHOOK_URL`: (Optional) Webhook URL for Slack notifications.
-   `DISCORD_WEBHOOK`: (Optional) Webhook URL for Discord notifications.
-   `PROMETHEUS_PUSHGATEWAY`: (Optional) URL for the Prometheus Pushgateway instance for metrics.
-   All `VITE_*` and other build-time secrets required by the application.

### 2.2. VPS Environment Setup

The target VPS must be configured with the following:

-   **Docker and Docker Compose:** The Docker engine and Docker Compose plugin must be installed and running.
-   **Dokploy Network:** Dokploy typically creates a dedicated Docker network (e.g., `dokploy-network`). The `docker-compose.prod.yml` file is configured to use this network. Verify its existence with `docker network ls`. If it doesn't exist, you may need to run Dokploy's setup or create it manually: `docker network create dokploy-network`.
-   **SSH Access:** The public key corresponding to the `VPS_SSH_KEY` secret must be in the `~/.ssh/authorized_keys` file for the `VPS_USER` on the server.

## 3. Deployment Process

The deployment can be triggered automatically on a push to `main` or `develop` branches, or it can be initiated manually.

### 3.1. Triggering the Workflow Manually

1.  Navigate to the **Actions** tab in the GitHub repository.
2.  Select the **Agentic CI/CD Pipeline** workflow from the list on the left.
3.  Click the **Run workflow** dropdown on the right.
4.  Choose the branch and other options (deployment target, agent framework) as needed.
5.  Click the **Run workflow** button.

### 3.2. Automated Workflow Steps

The `agentic-cicd.yml` workflow will execute the following jobs in sequence:

1.  **`code-analysis`**:
    *   Checks out the code.
    *   Installs dependencies.
    *   Runs ESLint for static analysis and `npm audit` for security vulnerability scanning.
    *   Enforces a quality gate based on the security score.

2.  **`agentic-testing`**:
    *   Runs unit, integration, and e2e tests for the agentic frameworks (AutoGen, CrewAI, LangChain).
    *   This job runs only if the `quality-gate` in the previous job passes.

3.  **`build-and-push`**:
    *   Builds the application's Docker image using the `Dockerfile`.
    *   Pushes the built image to the GitHub Container Registry (GHCR).
    *   Tags the image with the branch name, commit SHA, and 'latest' (for the main branch).

4.  **`agentic-deployment`**:
    *   This is the core deployment job. It runs only for `main`, `develop`, or manual dispatches.
    *   It securely connects to the VPS using the configured SSH secrets.
    *   It copies the `deploy_from_registry.sh` script and the `docker-compose.prod.yml` file to a `~/luxcrafts-deployment` directory on the VPS.
    *   It then executes the `deploy_from_registry.sh` script on the VPS. This script:
        *   Logs into GHCR using the provided token.
        *   Pulls the new Docker image.
        *   Runs `docker-compose -f docker-compose.prod.yml up -d` to start the new container.
        *   Removes old, unused Docker images to free up space.

5.  **`post-deployment-validation`**:
    *   Performs health checks on the application's public and API endpoints.
    *   Runs performance tests using Artillery.
    *   Conducts a Lighthouse audit for performance metrics.
    *   Runs a security scan using OWASP ZAP.

6.  **`notification`**:
    *   Sends a summary of the deployment (status, branch, commit, security score) to Slack and/or Discord if webhooks are configured.

7.  **`cleanup`**:
    *   Pushes deployment metrics to Prometheus Pushgateway using the `push_metrics.sh` script.

## 4. Verifying the Deployment

1.  **Check the Workflow Run:** Monitor the progress in the GitHub Actions tab. A green checkmark on all jobs indicates a successful run.
2.  **Access the Application:** Open the `PROD_APP_URL` or `STAGING_APP_URL` in your browser to ensure the website is live and functioning correctly.
3.  **Inspect VPS Logs (if needed):** If there's an issue, you can SSH into the VPS and check the Docker container logs:
    ```bash
    # Find the container ID or name
    docker ps

    # View logs
    docker logs <container_id_or_name>
    ```

## 5. Troubleshooting

-   **SSH Connection Fails:**
    *   Verify `VPS_HOST`, `VPS_USER`, and `VPS_SSH_KEY` secrets are correct.
    *   Ensure the public key is in `~/.ssh/authorized_keys` on the VPS.
    *   Check firewall rules on the VPS to ensure port 22 is open.
-   **Docker Login Fails on VPS:**
    *   Ensure the `GITHUB_TOKEN` has the correct `read:packages` permission.
-   **Docker Compose Fails:**
    *   Check that the `dokploy-network` exists on the VPS.
    *   Examine the `docker-compose.prod.yml` file for syntax errors.
    *   Check the Docker daemon logs on the VPS for more detailed errors.

## 6. Future Enhancements

-   **Deeper Dokploy Integration:** Explore using Dokploy's API or CLI (if available) for more integrated deployments instead of a generic script.
-   **Automated Rollbacks:** Enhance the workflow to automatically trigger a rollback to the previous version if the `post-deployment-validation` job fails.
-   **Infrastructure as Code:** Use Terraform or another IaC tool to provision the VPS and its dependencies as part of the pipeline.
